/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya;

import com.yijiupi.himalaya.distributedlock.annotation.EnableDistributedLock;
import com.yijiupi.himalaya.distributedlock.enums.DistributedLockType;
import com.yijiupi.himalaya.postcommit.TaskPoolConfiguration;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.SpringBootListener;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @since 2017年10月16日 下午3:34:07
 */
@EnableAsync
@EnableScheduling
@EnableDistributedLock(lockType = DistributedLockType.Redis)
@SpringBootApplication
@MapperScan(basePackages = {"com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao","com.yijiupi.himalaya.supplychain.productsync.domain.dao"})
public class WarehouseProductApp {

    public static void main(String[] args) throws Exception {
        SpringApplication app = new SpringApplication(WarehouseProductApp.class);
        // app.setWebEnvironment(false);// 不启动WEB 环境
        app.addListeners(new SpringBootListener());
        app.run(args);
        CountDownLatch latch = new CountDownLatch(1);
        latch.await();
    }

    @Bean
    RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean(name = "warehouseProductTaskExecutor")
    public Executor warehouseProductTaskExecutor() {
        int coreSize = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(coreSize + 80);
        executor.setQueueCapacity(1000);

        executor.setTaskDecorator(TaskPoolConfiguration::decorateTask);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("WarehouseProductTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        return executor;
    }

    @Bean(name = "productSyncTaskExecutor")
    public Executor productSyncTaskExecutor() {
        int coreSize = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize * 2 + 1);
        executor.setMaxPoolSize(coreSize * 3);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("ProductSyncTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.setTaskDecorator(TaskPoolConfiguration::decorateTask);
        return executor;
    }
}
