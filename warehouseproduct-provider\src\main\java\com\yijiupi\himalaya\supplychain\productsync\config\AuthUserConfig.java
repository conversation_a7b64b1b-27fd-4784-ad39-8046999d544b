package com.yijiupi.himalaya.supplychain.productsync.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description 授权用户配置
 * <AUTHOR>
 * @version
 * @date 2020年8月19日下午5:09:45
 */
@Component
@ConfigurationProperties(prefix = "authuser")
public class AuthUserConfig {
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
