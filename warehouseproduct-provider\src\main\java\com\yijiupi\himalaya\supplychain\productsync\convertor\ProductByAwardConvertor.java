package com.yijiupi.himalaya.supplychain.productsync.convertor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardItemMessage;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardMessage;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 兑奖产品管理化转换类
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
public class ProductByAwardConvertor {

    /**
     * 普通奖券类目名称
     */
    public static final String NORMAL_CATEGORY_NAME = "兑奖-奖券";

    /**
     * 虚拟奖券类目名称
     */
    public static final String VIRTUAL_CATEGORY_NAME = "兑奖-虚拟奖券";

    /**
     * 政策奖券类目名称
     */
    public static final String POLICY_CATEGORY_NAME = "兑奖-政策奖券";

    /**
     * 虚拟奖券默认规格名称
     */
    public static final String VIRTUAL_SPECIFICATION_NAME = "张奖票";

    /**
     * 奖券一级类目名称
     */
    public static final String CATEGORY_NAME = "兑奖";

    /**
     * ProductInfoDTO 转换为 ProductInfoPO
     * 
     * @return
     */
    public static ProductInfoPO convertToProductInfoPO(ProductInfoDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();
        BeanUtils.copyProperties(productInfoDTO, productInfoPO);
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        if (StringUtils.isEmpty(productInfoPO.getGeneralName())) {
            productInfoPO.setGeneralName(productInfoPO.getProductName());
        }
        productInfoPO.setDefaultImageFile_Id(productInfoDTO.getDefaultImageFileId());
        productInfoPO.setCreateUser_Id(productInfoDTO.getCreateUserId());
        productInfoPO.setLastUpdateUser_Id(productInfoDTO.getLastUpdateUserId());
        productInfoPO.setCreateTime(new Date());
        productInfoPO.setLastUpdateTime(new Date());
        return productInfoPO;
    }

    /**
     * ProductInfoDTO 转换为 ProductInfoPO
     * 
     * @return
     */
    public static ProductInfoPO convertToProductInfoPO(ProductInfoAndSkuDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();
        BeanUtils.copyProperties(productInfoDTO, productInfoPO);
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        if (StringUtils.isEmpty(productInfoPO.getGeneralName())) {
            productInfoPO.setGeneralName(productInfoPO.getProductName());
        }
        productInfoPO.setDefaultImageFile_Id(productInfoDTO.getDefaultImageFileId());
        productInfoPO.setCreateUser_Id(productInfoDTO.getCreateUserId());
        productInfoPO.setLastUpdateUser_Id(productInfoDTO.getLastUpdateUserId());
        productInfoPO.setCreateTime(new Date());
        productInfoPO.setLastUpdateTime(new Date());
        return productInfoPO;
    }

    /**
     * ProductInfoPO 转换为 ProductInfoDTO
     * 
     * @return
     */
    public static ProductInfoDTO convertToProductInfoDTO(ProductInfoPO productInfoPO) {
        if (productInfoPO == null) {
            return null;
        }
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        BeanUtils.copyProperties(productInfoPO, productInfoDTO);
        productInfoDTO.setDefaultImageFileId(productInfoPO.getDefaultImageFile_Id());
        productInfoDTO.setCreateUserId(productInfoPO.getCreateUser_Id());
        productInfoDTO.setLastUpdateUserId(productInfoPO.getLastUpdateUser_Id());
        return productInfoDTO;
    }

    /**
     * ProductInfoSpecificationDTO 转换为 ProductInfoSpecificationPO
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductInfoSpecificationDTO specificationDTO) {
        if (specificationDTO == null) {
            return null;
        }
        ProductInfoSpecificationPO specificationPO = new ProductInfoSpecificationPO();
        BeanUtils.copyProperties(specificationDTO, specificationPO);
        if (specificationPO.getId() == null) {
            specificationPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO_SPEC));
        }
        specificationPO.setProductInfo_Id(specificationDTO.getProductInfoId());
        specificationPO.setCreateUser_Id(specificationDTO.getCreateUserId() != null ? specificationDTO.getCreateUserId()
            : specificationDTO.getLastUpdateUserId());
        specificationPO.setCreateTime(new Date());
        specificationPO.setLastUpdateUser_Id(specificationDTO.getLastUpdateUserId());
        specificationPO.setLastUpdateTime(new Date());
        return specificationPO;
    }

    /**
     * ProductInfoSpecificationPO 转换为 ProductInfoSpecificationDTO
     * 
     * @return
     */
    public static ProductInfoSpecificationDTO convertToProductInfoSpecDTO(ProductInfoSpecificationPO specificationPO) {
        if (specificationPO == null) {
            return null;
        }
        ProductInfoSpecificationDTO specificationDTO = new ProductInfoSpecificationDTO();
        BeanUtils.copyProperties(specificationPO, specificationDTO);
        specificationDTO.setProductInfoId(specificationPO.getProductInfo_Id());
        specificationDTO.setCreateUserId(specificationPO.getCreateUser_Id());
        specificationDTO.setLastUpdateUserId(specificationPO.getLastUpdateUser_Id());
        return specificationDTO;
    }

    /**
     * 转换为ProductSkuPO
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuDTO productSkuDTO, ProductInfoPO productInfoPO,
        ProductInfoSpecificationPO specificationPO, Long ownerId, String ownerName) {
        if (productSkuDTO == null || productInfoPO == null || specificationPO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setProductSkuId(productSkuDTO.getProductSkuId());
        productSkuPO.setCityId(productSkuDTO.getCityId());
        productSkuPO.setProductSpecificationId(specificationPO.getId());
        productSkuPO.setName(productInfoPO.getProductName());
        productSkuPO.setCreateTime(new Date());
        productSkuPO.setCreateUserId(productSkuDTO.getCreateUserId());
        productSkuPO.setCompanyId(ownerId);
        productSkuPO.setOwnerName(ownerName);
        productSkuPO.setSpecificationName(specificationPO.getName());
        productSkuPO.setPackageName(specificationPO.getPackageName());
        productSkuPO.setUnitName(specificationPO.getUnitName());
        productSkuPO.setPackageQuantity(specificationPO.getPackageQuantity());
        productSkuPO.setSource(ProductSourceType.易酒批);
        productSkuPO.setProductInfoId(productInfoPO.getId());
        productSkuPO.setProductBrand(productInfoPO.getBrand());
        productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
        productSkuPO.setMonthOfShelfLife(productInfoPO.getMonthOfShelfLife());
        productSkuPO.setShelfLifeUnit(
            productInfoPO.getShelfLifeUnit() != null ? productInfoPO.getShelfLifeUnit().intValue() : null);
        productSkuPO.setRefProductSkuId(
            productSkuDTO.getRefProductSkuId() != null ? String.valueOf(productSkuDTO.getRefProductSkuId()) : "");
        productSkuPO.setProductType(productSkuDTO.getProductType());

        // productSkuPO.setUnpackage(productInfoPO.getPackageType() != null ? productInfoPO.getPackageType().intValue()
        // : null);
        // productSkuPO.setStorageType(productInfoPO.getStorageType());
        // productSkuPO.setCostPrice(productInfoPO.getDefaultCostPrice());

        productSkuPO.setWarehouseCustodyFee(productSkuDTO.getWarehouseCustodyFee());
        productSkuPO.setDeliveryFee(productSkuDTO.getDeliveryFee());
        productSkuPO.setDeliveryPayType(productSkuDTO.getDeliveryPayType());
        productSkuPO.setSortingFee(productSkuDTO.getSortingFee());
        productSkuPO.setUnpackage(productSkuDTO.getUnpackage());
        productSkuPO.setProductFeature(productSkuDTO.getProductFeature());
        productSkuPO.setMaxInventory(productSkuDTO.getMaxInventory());
        productSkuPO.setMinInventory(productSkuDTO.getMinInventory());
        productSkuPO.setMaxReplenishment(productSkuDTO.getMaxReplenishment());
        productSkuPO.setMinReplenishment(productSkuDTO.getMinReplenishment());
        productSkuPO.setIsComplete(productSkuDTO.getIsComplete());
        productSkuPO.setStorageType(productSkuDTO.getStorageType());
        productSkuPO.setPick(productSkuDTO.getPick());
        productSkuPO.setSow(productSkuDTO.getSow());
        productSkuPO.setInventoryRatio(productSkuDTO.getInventoryRatio());
        productSkuPO.setUnique(productSkuDTO.getUnique());
        productSkuPO.setFleeGoods(productSkuDTO.getFleeGoods());
        productSkuPO.setProductRelevantState(productSkuDTO.getProductRelevantState());
        productSkuPO.setProductGrade(productSkuDTO.getProductGrade());
        productSkuPO.setCostPrice(productSkuDTO.getCostPrice());
        productSkuPO.setSellingPrice(productSkuDTO.getSellingPrice());
        productSkuPO.setSellingPriceUnit(productSkuDTO.getSellingPriceUnit());
        return productSkuPO;
    }

    /**
     * productSkuDTO 转换为 ProductSkuPO
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuDTO productSkuDTO) {
        if (productSkuDTO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        BeanUtils.copyProperties(productSkuDTO, productSkuPO);
        return productSkuPO;
    }

    public static List<ProductSkuListDTO> convertToProductSkuDTO(List<ProductSkuPO> skuPOs) {
        List<ProductSkuListDTO> skuDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuPOs)) {
            return skuDTOS;
        }
        skuPOs.forEach(skuPO -> {
            ProductSkuListDTO productSkuDTO = new ProductSkuListDTO();
            productSkuDTO.setCityId(skuPO.getCityId());
            productSkuDTO.setProductSkuId(skuPO.getProductSkuId());
            productSkuDTO.setProductSpecificationId(skuPO.getId());
            productSkuDTO.setName(skuPO.getName());
            productSkuDTO.setCompanyId(skuPO.getCompanyId());
            productSkuDTO.setOwnerName(skuPO.getOwnerName());
            productSkuDTO.setProductSpecificationId(skuPO.getProductSpecificationId());
            productSkuDTO.setSpecificationName(skuPO.getName());
            productSkuDTO.setPackageName(skuPO.getPackageName());
            productSkuDTO.setUnitName(skuPO.getUnitName());
            productSkuDTO.setPackageQuantity(skuPO.getPackageQuantity());
            productSkuDTO.setSource(skuPO.getSource());
            productSkuDTO.setProductInfoId(skuPO.getId());
            productSkuDTO.setProductBrand(skuPO.getProductBrand());
            productSkuDTO.setProductState(ProductSkuStateEnum.上架.getType());
            productSkuDTO.setMonthOfShelfLife(skuPO.getMonthOfShelfLife());
            productSkuDTO
                .setShelfLifeUnit(skuPO.getShelfLifeUnit() != null ? skuPO.getShelfLifeUnit().intValue() : null);
            productSkuDTO.setStorageType(skuPO.getStorageType());
            productSkuDTO.setCostPrice(skuPO.getCostPrice());
            skuDTOS.add(productSkuDTO);
        });
        return skuDTOS;
    }

    public static List<ProductInfoDTO> convertToProductInfoDTO(ProductAwardMessage productAwardMessage) {
        List<ProductInfoDTO> productInfoDTOList = new ArrayList<>();
        if (productAwardMessage == null || CollectionUtils.isEmpty(productAwardMessage.getItems())) {
            return productInfoDTOList;
        }

        List<ProductAwardItemMessage> productAwardItemMessageList = productAwardMessage.getItems().stream()
            .filter(p -> p != null && p.getId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productAwardItemMessageList)) {
            return productInfoDTOList;
        }

        String statisticsCategoryName = NORMAL_CATEGORY_NAME;
        // 是否虚拟奖券
        boolean isVirtualAward = false;
        if (Objects.equals(productAwardMessage.getTicketType(), AwardTicketTypeEnums.虚拟凭证.getType())) {
            statisticsCategoryName = VIRTUAL_CATEGORY_NAME;
            isVirtualAward = true;
        }

        for (ProductAwardItemMessage message : productAwardItemMessageList) {
            ProductInfoDTO productInfoDTO = new ProductInfoDTO();
            productInfoDTO.setId(message.getId());
            productInfoDTO.setShopId(productAwardMessage.getShopId());
            productInfoDTO.setStatus(ProductInfoStateEnum.上架.getType());
            productInfoDTO.setCreateUserId(message.getCreateUserId());
            if (Objects.equals(message.getSettlementType(), SettlementTypeEnums.供应链.getType().intValue())) {
                statisticsCategoryName = POLICY_CATEGORY_NAME;
            }
            productInfoDTO.setStatisticsCategoryName(statisticsCategoryName);
            // 检查名称长度并截取
            checkAndSubstringNameLength(productInfoDTO, productAwardMessage, message);
            // 记录兑奖政策id
            productInfoDTO.setPolicyId(message.getScmPromotionId());
            List<ProductInfoSpecificationDTO> specificationList = new ArrayList<>();
            ProductInfoSpecificationDTO specification = new ProductInfoSpecificationDTO();
            specification.setId(message.getId());
            specification.setProductInfoId(productInfoDTO.getId());
            // 虚拟奖券使用默认奖券规格名称
            String baseUnit = isVirtualAward ? VIRTUAL_SPECIFICATION_NAME : message.getBaseUnit();
            specification.setName(baseUnit);
            specification.setPackageName(!StringUtils.isEmpty(baseUnit) ? baseUnit.substring(0, 1) : baseUnit);
            specification.setUnitName(!StringUtils.isEmpty(baseUnit) ? baseUnit.substring(0, 1) : baseUnit);
            specification.setPackageQuantity(new BigDecimal(1));
            specification.setState(StateEnum.启用.getType());
            specification.setCreateUserId(message.getCreateUserId());
            specificationList.add(specification);
            productInfoDTO.setSpecificationList(specificationList);
            productInfoDTOList.add(productInfoDTO);
        }

        return productInfoDTOList;
    }

    /**
     * 检查名称长度并截取
     *
     * @return
     */
    private static void checkAndSubstringNameLength(ProductInfoDTO productInfoDTO,
        ProductAwardMessage productAwardMessage, ProductAwardItemMessage message) {
        final int maxProductNameLength = 100;
        final int maxGeneralNameLength = 50;

        String awardName = message.getAwardName();
        if (awardName.indexOf(" ") != -1) {
            awardName = message.getAwardName().substring(0, awardName.indexOf(" "));
        }
        String awardTypeName =
            !StringUtils.isEmpty(message.getBaseUnit()) ? message.getBaseUnit().substring(1) : message.getBaseUnit();
        String productName = productAwardMessage.getProductName() + awardName + awardTypeName;
        String generalName = productName;
        if (productName.length() > maxProductNameLength) {
            int subLength = maxProductNameLength - awardName.length() - awardTypeName.length() - 2;
            productName = subLength >= 0
                ? productAwardMessage.getProductName().substring(0, subLength) + awardName + awardTypeName
                : productName.substring(productName.length() - maxProductNameLength + 2);
        }
        if (generalName.length() > maxGeneralNameLength) {
            int subLength = maxGeneralNameLength - awardName.length() - awardTypeName.length() - 2;
            generalName = subLength >= 0
                ? productAwardMessage.getProductName().substring(0, subLength) + awardName + awardTypeName
                : generalName.substring(generalName.length() - maxGeneralNameLength + 2);
        }
        productInfoDTO.setProductName(productName);
        productInfoDTO.setGeneralName(generalName);
    }
}
