package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.PojoUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 产品管理SAAS化转换类
 *
 * <AUTHOR>
 * @date 2019-08-28 10:26
 */
public class ProductBySaasConvertor {

    /**
     * ProductInfoDTO 转换为 ProductInfoPO
     * 
     * @return
     */
    public static ProductInfoPO convertToProductInfoPO(ProductInfoDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();
        BeanUtils.copyProperties(productInfoDTO, productInfoPO);
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        if (StringUtils.isEmpty(productInfoPO.getGeneralName())) {
            productInfoPO.setGeneralName(productInfoPO.getProductName());
        }
        productInfoPO.setDefaultImageFile_Id(productInfoDTO.getDefaultImageFileId());
        productInfoPO.setCreateUser_Id(productInfoDTO.getCreateUserId());
        productInfoPO.setLastUpdateUser_Id(productInfoDTO.getLastUpdateUserId());
        productInfoPO.setCreateTime(new Date());
        productInfoPO.setLastUpdateTime(new Date());
        return productInfoPO;
    }

    /**
     * ProductInfoDTO 转换为 ProductInfoPO
     * 
     * @return
     */
    public static ProductInfoPO convertToProductInfoPO(ProductInfoAndSkuDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();
        BeanUtils.copyProperties(productInfoDTO, productInfoPO);
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        if (StringUtils.isEmpty(productInfoPO.getGeneralName())) {
            productInfoPO.setGeneralName(productInfoPO.getProductName());
        }
        productInfoPO.setDefaultImageFile_Id(productInfoDTO.getDefaultImageFileId());
        productInfoPO.setCreateUser_Id(productInfoDTO.getCreateUserId());
        productInfoPO.setLastUpdateUser_Id(productInfoDTO.getLastUpdateUserId());
        productInfoPO.setCreateTime(new Date());
        productInfoPO.setLastUpdateTime(new Date());
        return productInfoPO;
    }

    /**
     * ProductInfoPO 转换为 ProductInfoDTO
     * 
     * @return
     */
    public static ProductInfoDTO convertToProductInfoDTO(ProductInfoPO productInfoPO) {
        if (productInfoPO == null) {
            return null;
        }
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        BeanUtils.copyProperties(productInfoPO, productInfoDTO);
        productInfoDTO.setDefaultImageFileId(productInfoPO.getDefaultImageFile_Id());
        productInfoDTO.setCreateUserId(productInfoPO.getCreateUser_Id());
        productInfoDTO.setLastUpdateUserId(productInfoPO.getLastUpdateUser_Id());
        return productInfoDTO;
    }

    /**
     * ProductInfoSpecificationDTO 转换为 ProductInfoSpecificationPO
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductInfoSpecificationDTO specificationDTO) {
        if (specificationDTO == null) {
            return null;
        }
        ProductInfoSpecificationPO specificationPO = new ProductInfoSpecificationPO();
        BeanUtils.copyProperties(specificationDTO, specificationPO);
        if (specificationPO.getId() == null) {
            specificationPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO_SPEC));
        }
        specificationPO.setProductInfo_Id(specificationDTO.getProductInfoId());
        specificationPO.setCreateUser_Id(specificationDTO.getCreateUserId() != null ? specificationDTO.getCreateUserId()
            : specificationDTO.getLastUpdateUserId());
        specificationPO.setCreateTime(new Date());
        specificationPO.setLastUpdateUser_Id(specificationDTO.getLastUpdateUserId());
        specificationPO.setLastUpdateTime(new Date());
        return specificationPO;
    }

    /**
     * ProductInfoSpecificationPO 转换为 ProductInfoSpecificationDTO
     * 
     * @return
     */
    public static ProductInfoSpecificationDTO convertToProductInfoSpecDTO(ProductInfoSpecificationPO specificationPO) {
        if (specificationPO == null) {
            return null;
        }
        ProductInfoSpecificationDTO specificationDTO = new ProductInfoSpecificationDTO();
        BeanUtils.copyProperties(specificationPO, specificationDTO);
        specificationDTO.setProductInfoId(specificationPO.getProductInfo_Id());
        specificationDTO.setCreateUserId(specificationPO.getCreateUser_Id());
        specificationDTO.setLastUpdateUserId(specificationPO.getLastUpdateUser_Id());
        return specificationDTO;
    }

    /**
     * 转换为ProductSkuPO
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuDTO productSkuDTO, ProductInfoPO productInfoPO,
        ProductInfoSpecificationPO specificationPO, Long ownerId, String ownerName) {
        if (productSkuDTO == null || productInfoPO == null || specificationPO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setCityId(productSkuDTO.getCityId());
        productSkuPO.setProductSpecificationId(specificationPO.getId());
        productSkuPO.setName(productInfoPO.getProductName());
        productSkuPO.setCreateTime(new Date());
        productSkuPO.setCreateUserId(productSkuDTO.getCreateUserId());
        productSkuPO.setCompanyId(ownerId);
        productSkuPO.setOwnerName(ownerName);
        productSkuPO.setSpecificationName(specificationPO.getName());
        productSkuPO.setPackageName(specificationPO.getPackageName());
        productSkuPO.setUnitName(specificationPO.getUnitName());
        productSkuPO.setPackageQuantity(specificationPO.getPackageQuantity());
        productSkuPO.setSource(ProductSourceType.易酒批);
        productSkuPO.setProductInfoId(productInfoPO.getId());
        productSkuPO.setProductBrand(productInfoPO.getBrand());
        productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
        productSkuPO.setMonthOfShelfLife(productInfoPO.getMonthOfShelfLife());
        productSkuPO.setShelfLifeUnit(
            productInfoPO.getShelfLifeUnit() != null ? productInfoPO.getShelfLifeUnit().intValue() : null);

        // productSkuPO.setUnpackage(productInfoPO.getPackageType() != null ? productInfoPO.getPackageType().intValue()
        // : null);
        // productSkuPO.setStorageType(productInfoPO.getStorageType());
        // productSkuPO.setCostPrice(productInfoPO.getDefaultCostPrice());

        productSkuPO.setWarehouseCustodyFee(productSkuDTO.getWarehouseCustodyFee());
        productSkuPO.setDeliveryFee(productSkuDTO.getDeliveryFee());
        productSkuPO.setDeliveryPayType(productSkuDTO.getDeliveryPayType());
        productSkuPO.setSortingFee(productSkuDTO.getSortingFee());
        productSkuPO.setUnpackage(productSkuDTO.getUnpackage());
        productSkuPO.setProductFeature(productSkuDTO.getProductFeature());
        productSkuPO.setMaxInventory(productSkuDTO.getMaxInventory());
        productSkuPO.setMinInventory(productSkuDTO.getMinInventory());
        productSkuPO.setMaxReplenishment(productSkuDTO.getMaxReplenishment());
        productSkuPO.setMinReplenishment(productSkuDTO.getMinReplenishment());
        productSkuPO.setIsComplete(productSkuDTO.getIsComplete());
        productSkuPO.setStorageType(productSkuDTO.getStorageType());
        productSkuPO.setPick(productSkuDTO.getPick());
        productSkuPO.setSow(productSkuDTO.getSow());
        productSkuPO.setInventoryRatio(productSkuDTO.getInventoryRatio());
        productSkuPO.setUnique(productSkuDTO.getUnique());
        productSkuPO.setFleeGoods(productSkuDTO.getFleeGoods());
        productSkuPO.setProductRelevantState(productSkuDTO.getProductRelevantState());
        productSkuPO.setProductGrade(productSkuDTO.getProductGrade());
        productSkuPO.setCostPrice(productSkuDTO.getCostPrice());
        productSkuPO.setSellingPrice(productSkuDTO.getSellingPrice());
        productSkuPO.setSellingPriceUnit(productSkuDTO.getSellingPriceUnit());
        return productSkuPO;
    }

    /**
     * productSkuDTO 转换为 ProductSkuPO
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuDTO productSkuDTO) {
        if (productSkuDTO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        BeanUtils.copyProperties(productSkuDTO, productSkuPO);
        return productSkuPO;
    }

    public static List<ProductSkuListDTO> convertToProductSkuDTO(List<ProductSkuPO> skuPOs) {
        List<ProductSkuListDTO> skuDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuPOs)) {
            return skuDTOS;
        }
        skuPOs.forEach(skuPO -> {
            ProductSkuListDTO productSkuDTO = new ProductSkuListDTO();
            productSkuDTO.setCityId(skuPO.getCityId());
            productSkuDTO.setProductSkuId(skuPO.getProductSkuId());
            productSkuDTO.setProductSpecificationId(skuPO.getId());
            productSkuDTO.setName(skuPO.getName());
            productSkuDTO.setCompanyId(skuPO.getCompanyId());
            productSkuDTO.setOwnerName(skuPO.getOwnerName());
            productSkuDTO.setProductSpecificationId(skuPO.getProductSpecificationId());
            productSkuDTO.setSpecificationName(skuPO.getName());
            productSkuDTO.setPackageName(skuPO.getPackageName());
            productSkuDTO.setUnitName(skuPO.getUnitName());
            productSkuDTO.setPackageQuantity(skuPO.getPackageQuantity());
            productSkuDTO.setSource(skuPO.getSource());
            productSkuDTO.setProductInfoId(skuPO.getId());
            productSkuDTO.setProductBrand(skuPO.getProductBrand());
            productSkuDTO.setProductState(ProductSkuStateEnum.上架.getType());
            productSkuDTO.setMonthOfShelfLife(skuPO.getMonthOfShelfLife());
            productSkuDTO
                .setShelfLifeUnit(skuPO.getShelfLifeUnit() != null ? skuPO.getShelfLifeUnit().intValue() : null);
            productSkuDTO.setStorageType(skuPO.getStorageType());
            productSkuDTO.setCostPrice(skuPO.getCostPrice());
            skuDTOS.add(productSkuDTO);
        });
        return skuDTOS;
    }

}
