package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ssc.CategoryFullPathDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ParentOrgIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zskx.ProductSkuByZskxSyncDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 产品类目转换类
 *
 * <AUTHOR>
 * @date 2019/3/27 16:50
 */
public class ProductCategoryConvertor {

    public static ProductCategoryPO convertorTOProductCategoryPO(ProductCategoryDTO productCategoryDTO) {
        if (null == productCategoryDTO) {
            return null;
        }
        ProductCategoryPO productCategoryPO = new ProductCategoryPO();
        BeanUtils.copyProperties(productCategoryDTO, productCategoryPO);
        return productCategoryPO;
    }

    public static ProductCategoryDTO convertorTOProductCategoryDTO(ProductInfo productInfo) {
        if (null == productInfo || null == productInfo.getId()) {
            return null;
        }
        ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
        productCategoryDTO.setId(productInfo.getId() != null ? productInfo.getId().longValue() : null);
        productCategoryDTO.setStatisticsClass(productInfo.getProductStatisticsClass() != null
            ? productInfo.getProductStatisticsClass().longValue() : null);
        productCategoryDTO.setSecondStatisticsClass(
            productInfo.getSecondStatisticsClass() != null ? productInfo.getSecondStatisticsClass().longValue() : null);
        // 所属统计类目名称=格式:白酒-茅台,白酒-剑南春
        String statisticsCategoryName = productInfo.getStatisticsCategoryName();
        if (StringUtils.isNotEmpty(statisticsCategoryName)) {
            String[] str = statisticsCategoryName.split("-");
            if (str.length > 0) {
                productCategoryDTO.setStatisticsClassName(str[0]);
                if (str.length > 1) {
                    productCategoryDTO.setSecondStatisticsClassName(str[1]);
                }
            }
        }
        // ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
        // productCategoryDTO.setId(productInfo.getId().longValue());
        // // 展示类目id
        // List<ProductInfoDisplayCategory> displayCategoryList = productInfo.getProductInfoDisplayCategoryList();
        // if (CollectionUtils.isNotEmpty(displayCategoryList)) {
        // ProductInfoDisplayCategory displayCategory = displayCategoryList.get(0);
        // if (null != displayCategory) {
        // productCategoryDTO.setStatisticsClass(displayCategory.getFirstCategoryId() != null ?
        // displayCategory.getFirstCategoryId().longValue() : null);
        // productCategoryDTO.setSecondStatisticsClass(displayCategory.getSecondCategoryId() != null ?
        // displayCategory.getSecondCategoryId().longValue() : null);
        // }
        // }
        // // 展示类目名称
        // // 格式:白酒-茅台,白酒-剑南春
        // String statisticsCategoryName = productInfo.getDisplayCategoryName();
        // if (StringUtils.isNotEmpty(statisticsCategoryName)) {
        // String[] str = statisticsCategoryName.split("-");
        // if (str.length > 0) {
        // productCategoryDTO.setStatisticsClassName(str[0]);
        // if (str.length > 1) {
        // productCategoryDTO.setSecondStatisticsClassName(str[1]);
        // }
        // }
        // }
        return productCategoryDTO;
    }

    public static ProductCategoryPO convertorTOProductCategoryPO(ProductInfo productInfo) {
        ProductCategoryDTO productCategoryDTO = convertorTOProductCategoryDTO(productInfo);
        return convertorTOProductCategoryPO(productCategoryDTO);
    }

    public static ProductCategoryPO convertorTOProductCategoryPO(SyncProductInfoDTO syncProductInfoDTO) {
        if (null == syncProductInfoDTO) {
            return null;
        }
        ProductCategoryPO productCategoryPO = new ProductCategoryPO();
        productCategoryPO.setId(syncProductInfoDTO.getId());
        productCategoryPO.setStatisticsClass(syncProductInfoDTO.getFirstCategoryId());
        productCategoryPO.setStatisticsClassName(syncProductInfoDTO.getFirstCategoryName());
        return productCategoryPO;
    }

    public static ProductCategoryPO convertorTOProductCategoryPO(ProductInfoDTO productInfoDTO) {
        if (null == productInfoDTO) {
            return null;
        }
        ProductCategoryPO productCategoryPO = new ProductCategoryPO();
        productCategoryPO.setId(productInfoDTO.getId());
        productCategoryPO.setStatisticsClass(productInfoDTO.getFirstCategoryId());
        productCategoryPO.setStatisticsClassName(productInfoDTO.getFirstCategoryName());
        productCategoryPO.setSecondStatisticsClassName("");
        return productCategoryPO;
    }

    public static CategorySync SyncProductInfoDTO2SyncCategoryTree(SyncProductInfoDTO syncProductInfoDTO) {
        if (null == syncProductInfoDTO || null == syncProductInfoDTO.getId()
            || syncProductInfoDTO.getFirstCategoryId() == null) {
            return null;
        }
        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(ParentOrgIdConstant.ZHZG);
        categorySync.setProductInfoId(syncProductInfoDTO.getId());
        categorySync.setCategoryId(syncProductInfoDTO.getFirstCategoryId().toString());
        categorySync.setCategoryName(syncProductInfoDTO.getFirstCategoryName());
        categorySync.setSequence(99999);
        return categorySync;
    }

    public static CategorySync ProductInfoDTO2SyncCategoryTree(ProductInfoDTO productInfoDTO) {
        if (null == productInfoDTO || null == productInfoDTO.getId() || productInfoDTO.getFirstCategoryId() == null) {
            return null;
        }
        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(ParentOrgIdConstant.ZHZG);
        categorySync.setProductInfoId(productInfoDTO.getId());
        categorySync.setCategoryId(productInfoDTO.getFirstCategoryId().toString());
        categorySync.setCategoryName(productInfoDTO.getFirstCategoryName());
        categorySync.setSequence(99999);
        return categorySync;
    }

    public static CategorySync ProductInfo2SyncCategoryTree(ProductInfo productInfo, Integer defaultOrgId) {
        if (null == productInfo || null == productInfo.getId()) {
            return null;
        }
        // 展示类目id
        List<ProductInfoDisplayCategory> displayCategoryList = productInfo.getProductInfoDisplayCategoryList();
        if (CollectionUtils.isEmpty(displayCategoryList) || displayCategoryList.get(0) == null) {
            return null;
        }
        CategorySync categorySync = getCategorySync(productInfo.getId(), displayCategoryList, defaultOrgId);
        return categorySync;
    }

    private static CategorySync getCategorySync(Integer productInfoId,
        List<ProductInfoDisplayCategory> displayCategoryList, Integer defaultOrgId) {
        if (displayCategoryList.get(0).getFirstCategoryId() == null
            || displayCategoryList.get(0).getFirstCategoryName() == null) {
            return null;
        }
        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(defaultOrgId == null ? ParentOrgIdConstant.YIJIUPI : defaultOrgId);
        categorySync.setProductInfoId(productInfoId.longValue());
        categorySync.setCategoryId(displayCategoryList.get(0).getFirstCategoryId().toString());
        categorySync.setCategoryName(displayCategoryList.get(0).getFirstCategoryName());
        categorySync.setSequence(99999);

        // 子类目
        if (displayCategoryList.get(0).getSecondCategoryId() != null) {
            CategorySync leafCategorySync = new CategorySync();
            leafCategorySync.setOrgId(categorySync.getOrgId());
            leafCategorySync.setProductInfoId(categorySync.getProductInfoId());
            leafCategorySync.setCategoryId(displayCategoryList.get(0).getSecondCategoryId().toString());
            leafCategorySync.setCategoryName(displayCategoryList.get(0).getSecondCategoryName());
            leafCategorySync.setSequence(99999);
            List<CategorySync> leafCategories = new ArrayList<>();
            leafCategories.add(leafCategorySync);

            categorySync.setLeafCategories(leafCategories);
        }
        return categorySync;
    }

    public static CategorySync ProductInfoMain2SyncCategoryTree(ProductInfoMain productInfoMain, Integer defaultOrgId) {
        if (null == productInfoMain || null == productInfoMain.getId()) {
            return null;
        }
        // 展示类目id
        List<ProductInfoDisplayCategory> displayCategoryList = productInfoMain.getProductInfoDisplayCategoryList();
        if (CollectionUtils.isEmpty(displayCategoryList) || displayCategoryList.get(0) == null) {
            return null;
        }
        CategorySync categorySync = getCategorySync(productInfoMain.getId(), displayCategoryList, defaultOrgId);
        return categorySync;
    }

    public static CategorySync ProductSkuByZskxSyncDTO2SyncCategoryTree(ProductSkuByZskxSyncDTO syncDTO,
        Map<String, Long> categoryMap) {
        if (null == syncDTO || syncDTO.getProductInfoId() == null || categoryMap == null) {
            return null;
        }
        if (StringUtils.isEmpty(syncDTO.getCategoryName()) || categoryMap.get(syncDTO.getCategoryName()) == null) {
            return null;
        }

        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(ParentOrgIdConstant.YIJIUPI);
        categorySync.setProductInfoId(syncDTO.getProductInfoId());
        categorySync.setCategoryId(categoryMap.get(syncDTO.getCategoryName()).toString());
        categorySync.setCategoryName(syncDTO.getCategoryName());
        categorySync.setSequence(99999);

        // 子类目
        if (StringUtils.isNotEmpty(syncDTO.getSecondCategoryName())
            && categoryMap.get(syncDTO.getSecondCategoryName()) != null) {
            CategorySync leafCategorySync = new CategorySync();
            leafCategorySync.setProductInfoId(categorySync.getProductInfoId());
            leafCategorySync.setCategoryId(categoryMap.get(syncDTO.getSecondCategoryName()).toString());
            leafCategorySync.setCategoryName(syncDTO.getSecondCategoryName());
            leafCategorySync.setSequence(99999);
            List<CategorySync> leafCategories = new ArrayList<>();
            leafCategories.add(leafCategorySync);
            categorySync.setLeafCategories(leafCategories);
        }
        return categorySync;
    }

    public static CategorySync ProductInfoPO2SyncCategoryTree(ProductInfoPO productInfoPO, Integer orgId) {
        if (null == productInfoPO || null == orgId || null == productInfoPO.getId()
            || null == productInfoPO.getProductStatisticsClass()) {
            return null;
        }
        // 格式:白酒-茅台,白酒-剑南春
        String statisticsCategoryName = productInfoPO.getStatisticsCategoryName();
        if (StringUtils.isEmpty(statisticsCategoryName)) {
            return null;
        }
        String[] str = statisticsCategoryName.split("-");
        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(orgId);
        categorySync.setProductInfoId(productInfoPO.getId());
        categorySync.setCategoryId(productInfoPO.getProductStatisticsClass().toString());
        if (str.length > 0) {
            categorySync.setCategoryName(str[0]);
        }
        categorySync.setSequence(99999);
        // 子类目
        if (productInfoPO.getSecondStatisticsClass() != null) {
            CategorySync leafCategorySync = new CategorySync();
            leafCategorySync.setOrgId(categorySync.getOrgId());
            leafCategorySync.setProductInfoId(categorySync.getProductInfoId());
            leafCategorySync.setCategoryId(productInfoPO.getSecondStatisticsClass().toString());
            if (str.length > 1) {
                leafCategorySync.setCategoryName(str[1]);
            }
            leafCategorySync.setSequence(99999);
            List<CategorySync> leafCategories = new ArrayList<>();
            leafCategories.add(leafCategorySync);

            categorySync.setLeafCategories(leafCategories);
        }
        return categorySync;
    }

    public static List<CategorySync> ProductInfo2SyncCategoryTree(Long productInfoId,
        CategoryFullPathDTO categoryFullPathDTO) {
        if (null == productInfoId || categoryFullPathDTO == null) {
            return null;
        }
        if (categoryFullPathDTO.getFirstCategoryId() == null
            || StringUtils.isEmpty(categoryFullPathDTO.getFirstCategoryName())) {
            return null;
        }
        List<CategorySync> resultList = new ArrayList<>();
        // 主类目
        CategorySync categorySync = new CategorySync();
        categorySync.setOrgId(ParentOrgIdConstant.YIJIUPI);
        categorySync.setProductInfoId(productInfoId);
        categorySync.setCategoryId(categoryFullPathDTO.getFirstCategoryId().toString());
        categorySync.setCategoryName(categoryFullPathDTO.getFirstCategoryName());
        categorySync.setSequence(99999);

        // 子类目
        if (categoryFullPathDTO.getSecondCategoryId() != null
            && !StringUtils.isEmpty(categoryFullPathDTO.getSecondCategoryName())) {
            CategorySync leafCategorySync = new CategorySync();
            leafCategorySync.setOrgId(categorySync.getOrgId());
            leafCategorySync.setProductInfoId(categorySync.getProductInfoId());
            leafCategorySync.setCategoryId(categoryFullPathDTO.getSecondCategoryId().toString());
            leafCategorySync.setCategoryName(categoryFullPathDTO.getSecondCategoryName());
            leafCategorySync.setSequence(99999);
            List<CategorySync> leafCategories = new ArrayList<>();
            leafCategories.add(leafCategorySync);

            categorySync.setLeafCategories(leafCategories);
        }
        resultList.add(categorySync);
        return resultList;
    }
}
