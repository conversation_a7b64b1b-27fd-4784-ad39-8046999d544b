package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategorySyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ProductCategoryGroupConverter {

    public static List<ProductCategoryGroupDTO> toDTO(List<ProductCategoryGroupPO> productCategoryGroupPOS) {
        if (CollectionUtils.isEmpty(productCategoryGroupPOS)) {
            return null;
        }
        List<ProductCategoryGroupDTO> productCategoryGroupDTOS = new ArrayList<>();
        productCategoryGroupPOS.forEach(po -> {
            ProductCategoryGroupDTO dto = new ProductCategoryGroupDTO();
            BeanUtils.copyProperties(po, dto);
            dto.setChildProductCategoryGroupDTOS(toDTO(po.getChildProductCategoryGroupPOS()));
            productCategoryGroupDTOS.add(dto);
        });
        productCategoryGroupDTOS.sort(Comparator.comparing(ProductCategoryGroupDTO::getSequence).thenComparing(ProductCategoryGroupDTO::getName));
        return productCategoryGroupDTOS;
    }

    public static void syncCategoryTree2ProductCategoryGroupPOS(List<CategorySync> categoryTrees,
        List<ProductCategoryGroupPO> productCategoryGroupPOS, Long categoryGroupId) {
        if (CollectionUtils.isEmpty(categoryTrees) || categoryGroupId == null) {
            return;
        }
        List<ProductCategoryGroupPO> productCategoryGroups =
            categorySyncs2ProductCategoryGroupPOS(categoryTrees, categoryGroupId);
        if (CollectionUtils.isNotEmpty(productCategoryGroups)) {
            productCategoryGroupPOS.addAll(productCategoryGroups);
        }

        List<CategorySync> leafCategories =
            categoryTrees.stream().filter(tree -> CollectionUtils.isNotEmpty(tree.getLeafCategories()))
                .flatMap(tree -> tree.getLeafCategories().stream()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leafCategories)) {
            syncCategoryTree2ProductCategoryGroupPOS(leafCategories, productCategoryGroupPOS, categoryGroupId);
        }
    }

    public static ProductCategoryGroupPO
        productCategoryGroupDTO2ProductCategoryGroupPO(ProductCategoryGroupDTO productCategoryGroupDTO) {
        if (productCategoryGroupDTO == null) {
            return null;
        }
        ProductCategoryGroupPO productCategoryGroupPO = new ProductCategoryGroupPO();

        if (productCategoryGroupDTO.getId() == null) {
            productCategoryGroupDTO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CATEGORY_GROUP));
        }
        BeanUtils.copyProperties(productCategoryGroupDTO, productCategoryGroupPO);
        return productCategoryGroupPO;
    }

    public static List<ProductCategoryGroupPO>
        productCategoryGroupDTOS2ProductCategoryGroupPOS(List<ProductCategoryGroupDTO> productCategoryGroupDTOS) {
        if (CollectionUtils.isEmpty(productCategoryGroupDTOS)) {
            return null;
        }

        List<ProductCategoryGroupPO> productCategoryGroupPOS = new ArrayList<>();
        productCategoryGroupDTOS.forEach(dto -> {
            productCategoryGroupPOS.add(productCategoryGroupDTO2ProductCategoryGroupPO(dto));
        });
        return productCategoryGroupPOS;
    }

    public static List<ProductCategoryGroupPO> categorySyncs2ProductCategoryGroupPOS(List<CategorySync> categorySyncs,
        Long categoryGroupId) {
        if (CollectionUtils.isEmpty(categorySyncs) || categoryGroupId == null) {
            return null;
        }

        List<ProductCategoryGroupPO> productCategoryGroupPOS = new ArrayList<>();
        categorySyncs.forEach(category -> {
            ProductCategoryGroupPO productCategoryGroupPO = new ProductCategoryGroupPO();

            productCategoryGroupPO.setRefCategoryId(category.getCategoryId());
            productCategoryGroupPO.setId(category.getWmsCategoryId());
            productCategoryGroupPO.setParentId(category.getWmsParentCategoryId());
            productCategoryGroupPO.setIsRelated(WhetherEnum.YES.getType());
            productCategoryGroupPO.setCategoryGroupId(categoryGroupId);
            productCategoryGroupPO.setSequence(category.getSequence());
            productCategoryGroupPO.setName(category.getWmsCategoryName());
            productCategoryGroupPO.setType(ProductCategoryGroupTypeEnum.类目.getType());
            productCategoryGroupPO.setCreateTime(new Date());
            productCategoryGroupPO.setMandatoryEntryBoxCode(category.getMandatoryEntryBoxCode());

            productCategoryGroupPOS.add(productCategoryGroupPO);

        });
        return productCategoryGroupPOS;
    }

    public static void productCategoryGroupTree2ProductCategoryGroupPOS(
        List<ProductCategoryGroupPO> productCategoryGroupTree, List<ProductCategoryGroupPO> allProductCategoryGroupPOS,
        Long categoryGroupId) {
        if (CollectionUtils.isEmpty(productCategoryGroupTree) || categoryGroupId == null) {
            return;
        }
        allProductCategoryGroupPOS.addAll(productCategoryGroupTree);

        List<ProductCategoryGroupPO> leafCategories = productCategoryGroupTree.stream()
            .filter(tree -> CollectionUtils.isNotEmpty(tree.getChildProductCategoryGroupPOS()))
            .flatMap(tree -> tree.getChildProductCategoryGroupPOS().stream()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leafCategories)) {
            productCategoryGroupTree2ProductCategoryGroupPOS(leafCategories, allProductCategoryGroupPOS,
                categoryGroupId);
        }
    }

    public static ProductCategoryGroupConfigPO ProductCategoryGroupConfigDTO2PO(ProductCategoryGroupConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductCategoryGroupConfigPO configPO = new ProductCategoryGroupConfigPO();
        BeanUtils.copyProperties(dto, configPO);
        return configPO;
    }

    public static CategorySync ProductCategorySyncDTO2CategorySync(ProductCategorySyncDTO syncData) {
        if (syncData == null) {
            return null;
        }

        CategorySync categorySync = new CategorySync();
        if (syncData.getParentId() != null && syncData.getParentId() != 0) {
            categorySync.setCategoryId(syncData.getParentId().toString());
            // 默认酒批分组
            categorySync.setCategoryGroupId(1L);

            CategorySync leafCategorySync = new CategorySync();
            leafCategorySync.setCategoryId(syncData.getId().toString());
            leafCategorySync.setParentCategoryId(categorySync.getCategoryId());
            leafCategorySync.setParentCategoryName(categorySync.getCategoryName());
            leafCategorySync.setCategoryName(syncData.getName());
            leafCategorySync.setSequence(syncData.getSequence());
            leafCategorySync.setCategoryGroupId(categorySync.getCategoryGroupId());
            if (leafCategorySync.getMandatoryEntryBoxCode() != null) {
                leafCategorySync.setMandatoryEntryBoxCode(syncData.getMandatoryEntryBoxCode() ? (byte)1 : (byte)0);
            }

            categorySync.setLeafCategories(Collections.singletonList(leafCategorySync));
        } else {
            categorySync.setCategoryId(syncData.getId().toString());
            categorySync.setCategoryName(syncData.getName());
            categorySync.setSequence(syncData.getSequence());
            if (categorySync.getMandatoryEntryBoxCode() != null) {
                categorySync.setMandatoryEntryBoxCode(syncData.getMandatoryEntryBoxCode() ? (byte)1 : (byte)0);
            }
            categorySync.setCategoryGroupId(1L);
        }
        return categorySync;
    }
}
