package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoAuditPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeOwnerTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditHandleDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditHandleItemDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UuidUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ProductCodeInfoAuditConvertor extends Convertor<ProductCodeInfoAuditDTO, ProductCodeInfoAuditPO> {

    @Override
    public ProductCodeInfoAuditPO convert(ProductCodeInfoAuditDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.getState() == null) {
            // 设置默认状态-待审核状态
            dto.setState(ProductCodeInfoStateEnum.TO_AUDIT.getType());
        }
        ProductCodeInfoAuditPO po = new ProductCodeInfoAuditPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    @Override
    public ProductCodeInfoAuditDTO reverseConvert(ProductCodeInfoAuditPO po) {
        if (po == null) {
            return null;
        }
        ProductCodeInfoAuditDTO dto = new ProductCodeInfoAuditDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public ProductCodeInfoAuditDTO convertAuditHandle(ProductCodeInfoAuditHandleDTO handleDTO) {
        if (handleDTO == null) {
            return null;
        }
        ProductCodeInfoAuditDTO auditDTO = new ProductCodeInfoAuditDTO();
        BeanUtils.copyProperties(handleDTO, auditDTO);
        if (auditDTO.getId() == null) {
            auditDTO.setId(UuidUtil.getUUid());
        }
        auditDTO.setCreateUser(handleDTO.getOperators());
        auditDTO.setCreateTime(new Date());
        auditDTO.setLastUpdateUser(handleDTO.getOperators());
        auditDTO.setLastUpdateTime(auditDTO.getCreateTime());
        List<ProductCodeInfoDTO> barCodes =
            createCodeInfo(handleDTO.getAuditBarItems(), auditDTO, ProductCodeTypeEnum.BAR_CODE.getType());
        List<ProductCodeInfoDTO> boxCodes =
            createCodeInfo(handleDTO.getAuditBoxItems(), auditDTO, ProductCodeTypeEnum.BOX_CODE.getType());
        List<ProductCodeInfoDTO> allCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(barCodes)) {
            allCodes.addAll(barCodes);
        }
        if (CollectionUtils.isNotEmpty(boxCodes)) {
            allCodes.addAll(boxCodes);
        }
        if (CollectionUtils.isNotEmpty(allCodes)) {
            auditDTO.setCodeInfoDTOList(allCodes);
        }
        return auditDTO;
    }

    public List<ProductCodeInfoAuditDTO> convertAuditHandle(List<ProductCodeInfoAuditHandleDTO> handleDTOList) {
        if (CollectionUtils.isEmpty(handleDTOList)) {
            return null;
        }
        List<ProductCodeInfoAuditDTO> auditDTOList = new ArrayList<ProductCodeInfoAuditDTO>(handleDTOList.size());
        for (ProductCodeInfoAuditHandleDTO handleDTO : handleDTOList) {
            if (handleDTO == null) {
                continue;
            }
            auditDTOList.add(convertAuditHandle(handleDTO));
        }
        return auditDTOList;
    }

    private List<ProductCodeInfoDTO> createCodeInfo(List<ProductCodeInfoAuditHandleItemDTO> auditHandleItemDTOS,
        ProductCodeInfoAuditDTO auditDTO, byte codeType) {
        if (CollectionUtils.isEmpty(auditHandleItemDTOS)) {
            return null;
        }
        List<ProductCodeInfoDTO> codeDTOS = new ArrayList<ProductCodeInfoDTO>(auditHandleItemDTOS.size());
        auditHandleItemDTOS.stream().filter(e -> e != null).forEach(e -> {
            ProductCodeInfoDTO codeInfoDTO = new ProductCodeInfoDTO();
            BeanUtils.copyProperties(e, codeInfoDTO);
            codeInfoDTO.setId(UuidUtil.getUUid());
            // 设置codeType
            codeInfoDTO.setCodeType(codeType);
            if (ProductCodeTypeEnum.BAR_CODE.getType().equals(codeType)) {
                // 条码
                codeInfoDTO.setProductInfoId(auditDTO.getProductInfoId());
            } else {
                // 箱码
                codeInfoDTO.setProductSpecificationId(auditDTO.getProductSpecificationId());
            }
            if (StringUtils.isBlank(codeInfoDTO.getCode())) {
                codeInfoDTO.setIsNoCode(WhetherEnum.YES.getType());
            }
            if (codeInfoDTO.getOwnerType() == null) {
                if (auditDTO.getCityId() == null) {
                    // 总部码
                    codeInfoDTO.setOwnerType(ProductCodeOwnerTypeEnum.HQ.getType());
                    codeInfoDTO.setOwnerId(null);
                } else {
                    // 区域码
                    codeInfoDTO.setOwnerType(ProductCodeOwnerTypeEnum.REGION.getType());
                    codeInfoDTO.setOwnerId(String.valueOf(auditDTO.getCityId()));
                }
            }
            codeInfoDTO.setProductCodeInfoAuditId(auditDTO.getId());
            codeInfoDTO.setCreateUser(auditDTO.getCreateUser());
            codeInfoDTO.setCreateTime(auditDTO.getCreateTime());
            codeInfoDTO.setLastUpdateUser(auditDTO.getLastUpdateUser());
            codeInfoDTO.setLastUpdateTime(auditDTO.getLastUpdateTime());
            if (codeInfoDTO.getState() == null) {
                // 新增审核数据如果单据状态为null，默认给待审核状态
                codeInfoDTO.setState(ProductCodeInfoStateEnum.TO_AUDIT.getType());
            }
            if (codeInfoDTO.getIsDelete() == null) {
                codeInfoDTO.setIsDelete(WhetherEnum.NO.getType());
            }
            codeDTOS.add(codeInfoDTO);
        });
        return codeDTOS;
    }

}
