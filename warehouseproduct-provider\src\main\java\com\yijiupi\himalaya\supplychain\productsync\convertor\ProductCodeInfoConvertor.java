package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.dubbop.dto.Code;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductBoxCode;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeOwnerTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

@Component
public class ProductCodeInfoConvertor extends Convertor<ProductCodeInfoDTO, ProductCodeInfoPO> {

    @Override
    public ProductCodeInfoPO convert(ProductCodeInfoDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.getIsCustom() == null) {
            dto.setIsCustom(WhetherEnum.NO.getType());
        }
        if (dto.getIsDelete() == null) {
            dto.setIsDelete(WhetherEnum.NO.getType());
        }
        if (dto.getState() == null) {
            // 默认待审核
            dto.setState(ProductCodeInfoStateEnum.TO_AUDIT.getType());
        }
        if (dto.getIsNoCode() == null) {
            // 默认是否有码赋值
            dto.setIsNoCode(StringUtils.isBlank(dto.getCode()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
        }
        ProductCodeInfoPO po = new ProductCodeInfoPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    @Override
    public ProductCodeInfoDTO reverseConvert(ProductCodeInfoPO po) {
        if (po == null) {
            return null;
        }
        if (po.getIsCustom() == null) {
            po.setIsCustom(WhetherEnum.NO.getType());
        }
        if (po.getIsDelete() == null) {
            po.setIsDelete(WhetherEnum.NO.getType());
        }
        if (po.getIsNoCode() == null) {
            // 默认是否有码赋值
            po.setIsNoCode(StringUtils.isBlank(po.getCode()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
        }
        ProductCodeInfoDTO dto = new ProductCodeInfoDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 转化交易箱码
     *
     * @param boxCode
     * @return
     */
    public SyncProductCodeInfoItemDTO convertBoxCodeTOProductCodeInfoDTO(ProductBoxCode boxCode) {
        if (boxCode == null) {
            return null;
        }
        SyncProductCodeInfoItemDTO infoDTO = new SyncProductCodeInfoItemDTO();
        infoDTO.setCode(boxCode.getBoxCode());
        infoDTO.setIsCustom((boxCode.getIsCustom() != null && boxCode.getIsCustom()) ? WhetherEnum.YES.getType()
            : WhetherEnum.NO.getType());
        infoDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
        infoDTO.setCodeType(ProductCodeTypeEnum.BOX_CODE.getType());
        infoDTO.setProductSpecificationId(boxCode.getProductInfoSpecificationId() == null ? null
            : boxCode.getProductInfoSpecificationId().longValue());
        infoDTO.setOwnerType(transferTrdOwnerType(boxCode.getOwnerType()));
        infoDTO.setOwnerId(boxCode.getOwnerId());
        infoDTO.setIsDelete(WhetherEnum.NO.getType());
        infoDTO
            .setIsNoCode(StringUtils.isBlank(infoDTO.getCode()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
        return infoDTO;
    }

    public List<SyncProductCodeInfoItemDTO> convertBoxCodeTOProductCodeInfoDTO(List<ProductBoxCode> boxCodes) {
        if (CollectionUtils.isEmpty(boxCodes)) {
            return new ArrayList<>();
        }
        List<SyncProductCodeInfoItemDTO> rs = new ArrayList<>();
        boxCodes.stream().filter(e -> e != null).forEach(e -> rs.add(convertBoxCodeTOProductCodeInfoDTO(e)));
        return rs;
    }

    /**
     * 转化交易条码
     *
     * @return
     */
    public SyncProductCodeInfoItemDTO convertBottleCodeTOProductCodeInfoDTO(Code code, Long productInfoId) {
        if (code == null) {
            return null;
        }
        SyncProductCodeInfoItemDTO infoDTO = new SyncProductCodeInfoItemDTO();
        infoDTO.setCode(code.getCode());
        infoDTO.setProductInfoId(productInfoId);
        infoDTO.setIsCustom(
            (code.getIsCustom() != null && code.getIsCustom()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
        infoDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
        infoDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
        infoDTO.setOwnerType(ProductCodeOwnerTypeEnum.HQ.getType());
        infoDTO.setIsDelete(WhetherEnum.NO.getType());
        infoDTO
            .setIsNoCode(StringUtils.isBlank(infoDTO.getCode()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
        return infoDTO;
    }

    public List<SyncProductCodeInfoItemDTO> convertBottleCodeTOProductCodeInfoDTO(List<Code> codes,
        Long productInfoId) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<SyncProductCodeInfoItemDTO> rs = new ArrayList<>();
        codes.stream().filter(e -> e != null)
            .forEach(e -> rs.add(convertBottleCodeTOProductCodeInfoDTO(e, productInfoId)));
        return rs;
    }

    /**
     * 转化交易ownerType
     *
     * @param ownerType
     * @return
     */
    private Byte transferTrdOwnerType(Integer ownerType) {
        if (ownerType == null) {
            return ProductCodeOwnerTypeEnum.HQ.getType();
        }
        switch (ownerType) {
            case 0:
                return ProductCodeOwnerTypeEnum.HQ.getType();
            case 1:
                return ProductCodeOwnerTypeEnum.REGION.getType();
            case 2:
                return ProductCodeOwnerTypeEnum.AGENCY.getType();
            default:
                return ProductCodeOwnerTypeEnum.HQ.getType();
        }
    }

    private String sortImgIdList(String imgIdsStr) {
        if (StringUtils.isBlank(imgIdsStr)) {
            return null;
        }
        // 不为空则转化为list排序
        try {
            List<String> imgIdList = JSON.parseArray(imgIdsStr, String.class);
            // 先排序-默认由小到大
            imgIdList.sort(Comparator.comparing(Function.identity()));
            // 放入排序好的 imgId
            return JSON.toJSONString(imgIdList);
        } catch (Exception e) {
            // ignore 异常
            return imgIdsStr;
        }
    }

    private List<String> convertImgId(String imgIdsStr) {
        if (StringUtils.isBlank(imgIdsStr)) {
            return null;
        }
        // 不为空则转化为list排序
        try {
            List<String> imgIdList = JSON.parseArray(imgIdsStr, String.class);
            // 先排序-默认由小到大
            imgIdList.sort(Comparator.comparing(Function.identity()));
            // 放入排序好的 imgId
            return imgIdList;
        } catch (Exception e) {
            // ignore 异常
            return Lists.newArrayList(imgIdsStr);
        }
    }
}
