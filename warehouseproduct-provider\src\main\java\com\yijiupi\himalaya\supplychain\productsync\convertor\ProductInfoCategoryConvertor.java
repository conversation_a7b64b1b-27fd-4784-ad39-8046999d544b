package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductInfoCategoryConvertor {

    /**
     * 树状类目对象转换成类目信息关联对象
     * 
     * @param productInfoCategoryTrees
     * @param categoryGroupId
     * @return
     */
    public static List<ProductInfoCategoryPO>
        categoryTrees2ProductInfoCategoryPOS(List<CategorySync> productInfoCategoryTrees, Long categoryGroupId) {
        if (CollectionUtils.isEmpty(productInfoCategoryTrees)) {
            return new ArrayList<>();
        }

        List<ProductInfoCategoryPO> productInfoCategoryPOS = new ArrayList<>();
        productInfoCategoryTrees.forEach(tree -> {
            ProductInfoCategoryPO productInfoCategoryPO = new ProductInfoCategoryPO();

            productInfoCategoryPO.setProductInfoId(tree.getProductInfoId());
            productInfoCategoryPO.setStatisticsClass(tree.getWmsCategoryId());
            productInfoCategoryPO.setStatisticsClassName(tree.getWmsCategoryName());
            productInfoCategoryPO.setCategoryGroupId(categoryGroupId);
            productInfoCategoryPO.setCreateTime(new Date());

            if (CollectionUtils.isNotEmpty(tree.getLeafCategories())) {
                productInfoCategoryPO.setSecondStatisticsClass(tree.getLeafCategories().get(0).getWmsCategoryId());
                productInfoCategoryPO
                    .setSecondStatisticsClassName(tree.getLeafCategories().get(0).getWmsCategoryName());
            }

            productInfoCategoryPOS.add(productInfoCategoryPO);
        });

        return productInfoCategoryPOS;
    }

    /**
     * 双层类目对象转换成类目信息关联对象
     * 
     * @param productInfoCategorySyncs
     * @param categoryGroupId
     * @return
     */
    public static List<ProductInfoCategoryPO>
        categorySyncs2ProductInfoCategoryPOS(List<CategorySync> productInfoCategorySyncs, Long categoryGroupId) {
        if (CollectionUtils.isEmpty(productInfoCategorySyncs)) {
            return null;
        }

        List<ProductInfoCategoryPO> productInfoCategoryPOS = new ArrayList<>();
        productInfoCategorySyncs.forEach(category -> {
            ProductInfoCategoryPO productInfoCategoryPO = new ProductInfoCategoryPO();

            productInfoCategoryPO.setProductInfoId(category.getProductInfoId());
            productInfoCategoryPO.setStatisticsClass(category.getWmsParentCategoryId());
            productInfoCategoryPO.setStatisticsClassName(category.getWmsParentCategoryName());
            productInfoCategoryPO.setSecondStatisticsClass(category.getWmsCategoryId());
            productInfoCategoryPO.setSecondStatisticsClassName(category.getWmsCategoryName());
            productInfoCategoryPO.setCategoryGroupId(categoryGroupId);
            productInfoCategoryPO.setCreateTime(new Date());

            productInfoCategoryPOS.add(productInfoCategoryPO);
        });

        return productInfoCategoryPOS;
    }

    /**
     * dto转po
     * 
     * @param productInfoCategoryPOS
     * @return
     */
    public static List<ProductInfoCategoryDTO>
        productInfoCategoryPOS2productInfoCategoryDTOS(List<ProductInfoCategoryPO> productInfoCategoryPOS) {
        if (CollectionUtils.isEmpty(productInfoCategoryPOS)) {
            return null;
        }

        List<ProductInfoCategoryDTO> productInfoCategoryDTOS = new ArrayList<>();
        productInfoCategoryPOS.forEach(po -> {
            ProductInfoCategoryDTO dto = new ProductInfoCategoryDTO();
            BeanUtils.copyProperties(po, dto);

            productInfoCategoryDTOS.add(dto);
        });
        return productInfoCategoryDTOS;
    }
}
