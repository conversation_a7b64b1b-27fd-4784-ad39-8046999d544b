/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfo;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author: yanpin
 * @date: 2018年11月13日 下午4:38:48
 */
@Component
public class ProductInfoConvertor extends Convertor<ProductInfo, ProductInfoPO> {

    @Autowired
    private ProductCodeInfoConvertor productCodeInfoConvertor;

    @Autowired
    private ProductInfoSpecificationConvertor productInfoSpecificationConvertor;

    @Override
    public ProductInfoPO convert(ProductInfo m) {
        ProductInfoPO po = new ProductInfoPO();
        po.setBottleCode(m.getBottleCode() == null ? null : m.getBottleCode().getCode());
        po.setBrand((StringUtils.isNotEmpty(m.getProductBrand()) && m.getProductBrand().length() > 20)
            ? m.getProductBrand().substring(0, 20) : m.getProductBrand());
        po.setDefaultImageFile_Id(m.getDefaultImageId() != null ? m.getDefaultImageId().toString() : null);
        po.setGeneralName(m.getProductGeneralName());
        // 长度为50，太长了截断
        final int nMaxNameLength = 50;
        if (!StringUtils.isEmpty(po.getGeneralName()) && po.getGeneralName().length() > nMaxNameLength) {
            po.setGeneralName(po.getGeneralName().substring(0, 50));
        }
        po.setHasBottleCode(m.getBottleCode() == null ? (byte)0 : (byte)1);
        po.setHasBoxCode(m.getPackagingCode() == null ? (byte)0 : (byte)1);
        po.setId(m.getId().longValue());
        po.setOriginalPlace(m.getOriginalPlace());
        po.setPackagingCode(m.getPackagingCode() == null ? null : m.getPackagingCode().getCode());
        po.setProductCode(m.getProductCode());
        po.setProductInfoType(m.getProductInfoType() == null ? null : m.getProductInfoType().value.byteValue());
        po.setProductName(m.getProductName());
        po.setProductStatisticsClass(
            m.getProductStatisticsClass() != null ? m.getProductStatisticsClass().longValue() : null);
        po.setSecondStatisticsClass(
            m.getSecondStatisticsClass() != null ? m.getSecondStatisticsClass().longValue() : null);
        // po.setSeriesName(m.getStatisticsCategoryName());
        po.setMonthOfShelfLife(m.getMonthOfShelfLife() == null ? 0 : m.getMonthOfShelfLife());
        // 转换保质期单位 0:长期，1:年，2:月，3:日
        po.setShelfLifeUnit(m.getShelfLifeUnit() == null ? null : m.getShelfLifeUnit().byteValue());
        // 保质期单位为0，或者保质期时长未设置，默认为长期
        po.setShelfLifeLongTime(m.getMonthOfShelfLife() == null || Objects.equals(m.getShelfLifeUnit(), 0) ? (byte) 1 : (byte) 0);
        po.setStatisticsCategoryName(m.getStatisticsCategoryName());
        po.setStatus(m.getState() == null ? null : m.getState().value.byteValue());
        po.setStorageMethod(m.getStorageMethod());
        po.setPackageType(m.getIsUnpackSale() == true ? (byte)1 : 0);
        po.setLastUpdateTime(new Date());
        return po;
    }

    @Override
    public ProductInfo reverseConvert(ProductInfoPO n) {
        return null;
    }

    public ProductInfoDTO convertTOProductInfoDTO(ProductInfoPO po) {
        if (po == null) {
            return null;
        }
        ProductInfoDTO dto = new ProductInfoDTO();
        BeanUtils.copyProperties(po, dto);
        dto.setDefaultImageFileId(po.getDefaultImageFile_Id());
        dto.setCreateUserId(po.getCreateUser_Id());
        dto.setLastUpdateUserId(po.getLastUpdateUser_Id());
        if (CollectionUtils.isNotEmpty(po.getProductInfoSpecificationPOList())) {
            dto.setProductInfoSpecificationDTOList(productInfoSpecificationConvertor
                .convertTOProductInfoSpecificationDTO(po.getProductInfoSpecificationPOList()));
        }
        if (CollectionUtils.isNotEmpty(po.getProductCodeInfoPOList())) {
            dto.setProductCodeInfoDTOList(productCodeInfoConvertor.reverseConvert(po.getProductCodeInfoPOList()));
        }
        return dto;
    }

    public List<ProductInfoDTO> convertTOProductInfoDTO(List<ProductInfoPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<ProductInfoDTO> dtoList = new ArrayList<>(poList.size());
        for (ProductInfoPO po : poList) {
            if (po == null) {
                continue;
            }
            dtoList.add(convertTOProductInfoDTO(po));
        }
        return dtoList;
    }

    public ProductInfoPO convertTOProductInfoPO(ProductInfoDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductInfoPO po = new ProductInfoPO();
        BeanUtils.copyProperties(dto, po);
        po.setDefaultImageFile_Id(dto.getDefaultImageFileId());
        po.setCreateUser_Id(dto.getCreateUserId());
        po.setLastUpdateUser_Id(dto.getLastUpdateUserId());
        if (CollectionUtils.isNotEmpty(dto.getProductInfoSpecificationDTOList())) {
            po.setProductInfoSpecificationPOList(productInfoSpecificationConvertor
                .convertTOProductInfoSpecificationPO(dto.getProductInfoSpecificationDTOList()));
        }
        if (CollectionUtils.isNotEmpty(dto.getProductCodeInfoDTOList())) {
            po.setProductCodeInfoPOList(productCodeInfoConvertor.convert(dto.getProductCodeInfoDTOList()));
        }
        return po;
    }

    public List<ProductInfoPO> convertTOProductInfoPO(List<ProductInfoDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<ProductInfoPO> poList = new ArrayList<>(dtoList.size());
        for (ProductInfoDTO dto : dtoList) {
            if (dto == null) {
                continue;
            }
            poList.add(convertTOProductInfoPO(dto));
        }
        return poList;
    }
}
