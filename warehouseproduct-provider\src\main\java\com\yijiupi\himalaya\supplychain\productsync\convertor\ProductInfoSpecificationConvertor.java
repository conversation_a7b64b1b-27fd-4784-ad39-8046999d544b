/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecification;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2018年11月13日 下午5:49:22
 */
@Component
public class ProductInfoSpecificationConvertor extends Convertor<ProductInfoSpecification, ProductInfoSpecificationPO> {

    @Override
    public ProductInfoSpecificationPO convert(ProductInfoSpecification m) {
        ProductInfoSpecificationPO po = new ProductInfoSpecificationPO();
        po.setId(m.getId().longValue());
        po.setName(m.getName());
        po.setHeight(m.getHeight());
        po.setLength(m.getLength());
        po.setWeight(m.getWeight());
        po.setWidth(m.getWidth());
        po.setProductInfo_Id(m.getProductInfoId().longValue());
        po.setState(m.getState() == null ? null : m.getState().value.byteValue());
        if (m.getLength() != null && m.getWidth() != null && m.getHeight() != null) {
            String volume = m.getLength() + "*" + m.getWidth() + "*" + m.getHeight();
            po.setVolume(volume);
        }
        if (m.getSpecificationsInfo() != null) {
            po.setPackageName(m.getSpecificationsInfo().getPackageName());
            po.setUnitName(m.getSpecificationsInfo().getUnitName());
            Integer quantity = m.getSpecificationsInfo().getQuantity();
            po.setPackageQuantity(quantity != null ? new BigDecimal(quantity) : null);
        }
        //同步产品信息外部编码
        po.setOuterCode(m.getOuterCode());
        return po;
    }

    @Override
    public ProductInfoSpecification reverseConvert(ProductInfoSpecificationPO n) {
        return null;
    }

    public ProductInfoSpecificationPO convertTOProductInfoSpecificationPO(ProductInfoSpecificationDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductInfoSpecificationPO po = new ProductInfoSpecificationPO();
        BeanUtils.copyProperties(dto, po);
        po.setProductInfo_Id(dto.getProductInfoId());
        po.setCreateUser_Id(dto.getCreateUserId());
        po.setLastUpdateUser_Id(dto.getLastUpdateUserId());
        return po;
    }

    public List<ProductInfoSpecificationPO>
    convertTOProductInfoSpecificationPO(List<ProductInfoSpecificationDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<ProductInfoSpecificationPO> poList = new ArrayList<ProductInfoSpecificationPO>();
        for (ProductInfoSpecificationDTO dto : dtoList) {
            if (dto == null) {
                continue;
            }
            poList.add(convertTOProductInfoSpecificationPO(dto));
        }
        return poList;
    }

    public ProductInfoSpecificationDTO convertTOProductInfoSpecificationDTO(ProductInfoSpecificationPO po) {
        if (po == null) {
            return null;
        }
        ProductInfoSpecificationDTO dto = new ProductInfoSpecificationDTO();
        BeanUtils.copyProperties(po, dto);
        dto.setProductInfoId(po.getProductInfo_Id());
        dto.setCreateUserId(po.getCreateUser_Id());
        dto.setLastUpdateUserId(po.getLastUpdateUser_Id());
        return dto;
    }

    public List<ProductInfoSpecificationDTO>
    convertTOProductInfoSpecificationDTO(List<ProductInfoSpecificationPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<ProductInfoSpecificationDTO> dtoList = new ArrayList<>();
        for (ProductInfoSpecificationPO po : poList) {
            if (po == null) {
                continue;
            }
            dtoList.add(convertTOProductInfoSpecificationDTO(po));
        }
        return dtoList;
    }

}
