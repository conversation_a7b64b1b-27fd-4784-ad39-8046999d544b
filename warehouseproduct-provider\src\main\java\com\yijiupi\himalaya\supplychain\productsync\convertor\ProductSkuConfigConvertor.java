package com.yijiupi.himalaya.supplychain.productsync.convertor;

import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO;

/**
 * 产品sku配置转化类
 *
 * <AUTHOR>
 * @date 2020-01-09 15:47
 */
public class ProductSkuConfigConvertor {

    public static ProductSkuConfigDTO convertorToProductSkuConfigDTO(ProductSkuPO productSkuPO) {
        if (productSkuPO == null) {
            return null;
        }
        ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
        configDTO.setProductSkuId(productSkuPO.getProductSkuId());
        configDTO.setWarehouseCustodyFee(productSkuPO.getWarehouseCustodyFee());
        configDTO.setDeliveryFee(productSkuPO.getDeliveryFee());
        configDTO.setDeliveryPayType(productSkuPO.getDeliveryPayType());
        configDTO.setSortingFee(productSkuPO.getSortingFee());
        configDTO.setUnpackage(productSkuPO.getUnpackage());
        configDTO.setProductFeature(productSkuPO.getProductFeature());
        configDTO.setMaxInventory(productSkuPO.getMaxInventory());
        configDTO.setMinInventory(productSkuPO.getMinInventory());
        configDTO.setMaxReplenishment(productSkuPO.getMaxReplenishment());
        configDTO.setMinReplenishment(productSkuPO.getMinReplenishment());
        configDTO.setIsComplete(productSkuPO.getIsComplete());
        configDTO.setStorageType(productSkuPO.getStorageType());
        configDTO.setPick(productSkuPO.getPick());
        configDTO.setSow(productSkuPO.getSow());
        configDTO.setInventoryRatio(productSkuPO.getInventoryRatio());
        configDTO.setUnique(productSkuPO.getUnique());
        configDTO.setFleeGoods(productSkuPO.getFleeGoods());
        configDTO.setProductRelevantState(productSkuPO.getProductRelevantState());
        configDTO.setProductGrade(productSkuPO.getProductGrade());
        configDTO.setCostPrice(productSkuPO.getCostPrice());
        configDTO.setSellingPrice(productSkuPO.getSellingPrice());
        configDTO.setSellingPriceUnit(productSkuPO.getSellingPriceUnit());
        configDTO.setUnifySkuId(productSkuPO.getUnifySkuId());
        return configDTO;
    }

    public static ProductSkuConfigDTO convertorToProductSkuConfigDTO(ProductSkuDTO productSkuDTO) {
        if (productSkuDTO == null) {
            return null;
        }
        ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
        configDTO.setProductSkuId(productSkuDTO.getProductSkuId());
        configDTO.setWarehouseCustodyFee(productSkuDTO.getWarehouseCustodyFee());
        configDTO.setDeliveryFee(productSkuDTO.getDeliveryFee());
        configDTO.setDeliveryPayType(productSkuDTO.getDeliveryPayType());
        configDTO.setSortingFee(productSkuDTO.getSortingFee());
        configDTO.setUnpackage(productSkuDTO.getUnpackage());
        configDTO.setProductFeature(productSkuDTO.getProductFeature());
        configDTO.setMaxInventory(productSkuDTO.getMaxInventory());
        configDTO.setMinInventory(productSkuDTO.getMinInventory());
        configDTO.setMaxReplenishment(productSkuDTO.getMaxReplenishment());
        configDTO.setMinReplenishment(productSkuDTO.getMinReplenishment());
        configDTO.setIsComplete(productSkuDTO.getIsComplete());
        configDTO.setStorageType(productSkuDTO.getStorageType());
        configDTO.setPick(productSkuDTO.getPick());
        configDTO.setSow(productSkuDTO.getSow());
        configDTO.setInventoryRatio(productSkuDTO.getInventoryRatio());
        configDTO.setUnique(productSkuDTO.getUnique());
        configDTO.setFleeGoods(productSkuDTO.getFleeGoods());
        configDTO.setProductRelevantState(productSkuDTO.getProductRelevantState());
        configDTO.setProductGrade(productSkuDTO.getProductGrade());
        configDTO.setCostPrice(productSkuDTO.getCostPrice());
        configDTO.setSellingPrice(productSkuDTO.getSellingPrice());
        configDTO.setSellingPriceUnit(productSkuDTO.getSellingPriceUnit());
        return configDTO;
    }

    public static List<ProductSkuConfigPO> convertToPOS(List<ProductSkuConfigDTO> dtoList) {
        return dtoList.stream().map(ProductSkuConfigConvertor::convertToPO).collect(Collectors.toList());
    }

    public static ProductSkuConfigPO convertToPO(ProductSkuConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductSkuConfigPO po = new ProductSkuConfigPO();
        po.setId(dto.getId());
        po.setProductSkuId(dto.getProductSkuId());
        po.setWarehouseCustodyFee(dto.getWarehouseCustodyFee());
        po.setDeliveryFee(dto.getDeliveryFee());
        po.setDeliveryPayType(dto.getDeliveryPayType());
        po.setSortingFee(dto.getSortingFee());
        po.setUnpackage(dto.getUnpackage());
        po.setProductFeature(dto.getProductFeature());
        po.setMaxInventory(dto.getMaxInventory());
        po.setMinInventory(dto.getMinInventory());
        po.setMaxReplenishment(dto.getMaxReplenishment());
        po.setMinReplenishment(dto.getMinReplenishment());
        po.setIsComplete(dto.getIsComplete());
        po.setStorageType(dto.getStorageType());
        po.setPick(dto.getPick());
        po.setSow(dto.getSow());
        po.setInventoryRatio(dto.getInventoryRatio());
        po.setUnique(dto.getUnique());
        po.setFleeGoods(dto.getFleeGoods());
        po.setProductRelevantState(dto.getProductRelevantState());
        po.setProductGrade(dto.getProductGrade());
        po.setCostPrice(dto.getCostPrice());
        po.setSellingPrice(dto.getSellingPrice());
        po.setSellingPriceUnit(dto.getSellingPriceUnit());
        return po;
    }
}
