package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ScmProductSkuSyncMessage;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddItemDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 产品sku实体转换类
 *
 * <AUTHOR>
 * @date 2019/12/26 9:14
 */
public class ProductSkuConvertor {

    /**
     * 产品skuPO转换
     */
    public static ProductSkuPO convertorToProductSkuPO(ScmProductSkuSyncMessage productSkuMessage) {
        if (productSkuMessage == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setRefProductSkuId(
            productSkuMessage.getProductSkuId() != null ? productSkuMessage.getProductSkuId().toString() : null);
        productSkuPO.setProductInfoId(productSkuMessage.getProductInfoId());
        productSkuPO.setProductSpecificationId(productSkuMessage.getProductInfoSpecId());
        productSkuPO.setName(productSkuMessage.getProductName());
        productSkuPO.setCompanyId(productSkuMessage.getCompanyId());
        productSkuPO.setProductState(productSkuMessage.getState());
        productSkuPO.setSaleModel(productSkuMessage.getSaleMode());
        productSkuPO.setSpecificationName(productSkuMessage.getSpecName());
        productSkuPO.setPackageName(productSkuMessage.getSpecPackageName());
        productSkuPO.setUnitName(productSkuMessage.getSpecUnitName());
        productSkuPO.setPackageQuantity(productSkuMessage.getSpecQuantity());
        productSkuPO.setSource(ProductSourceType.易酒批);
        productSkuPO.setUnpackage(-1);
        productSkuPO.setCreateTime(new Date());
        productSkuPO.setLastUpdateTime(new Date());
        productSkuPO.setSellingPrice(productSkuMessage.getSellingPrice());
        productSkuPO.setSellingPriceUnit(productSkuMessage.getSellingPriceUnit());
        productSkuPO.setUnifySkuId(productSkuMessage.getRelSscProductSkuId());
        productSkuPO.setProductInfoCategoryId(productSkuMessage.getProductInfoCategoryId());
        productSkuPO.setSecOwnerId(productSkuMessage.getSecOwnerId());
        productSkuPO.setProductBrand(productSkuMessage.getProductBrand());
        productSkuPO.setIsDelete(ConditionStateEnum.否.getType());
        return productSkuPO;
    }

    public static ProductRelationGroupAddDTO getProductRelationGroupAddDTO(Integer warehouseId,
        ProductSkuPO productSkuPO, List<Long> refSkuId) {
        ProductRelationGroupAddDTO groupAddDTO = new ProductRelationGroupAddDTO();
        groupAddDTO.setCityId(productSkuPO.getCityId());
        groupAddDTO.setWarehouseId(warehouseId);
        // 需要建立关联关系的sku
        List<ProductRelationGroupAddItemDTO> groupRelations = new ArrayList<>();
        ProductRelationGroupAddItemDTO itemDTO = new ProductRelationGroupAddItemDTO();
        List<Long> groupSkuIds = new ArrayList<>();
        groupSkuIds.add(productSkuPO.getProductSkuId());
        groupSkuIds.addAll(refSkuId);
        itemDTO.setGroupSkuIds(groupSkuIds);
        groupRelations.add(itemDTO);
        groupAddDTO.setGroupRelations(groupRelations);
        return groupAddDTO;
    }

}
