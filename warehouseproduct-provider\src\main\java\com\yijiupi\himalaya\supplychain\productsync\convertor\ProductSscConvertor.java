package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductInfoSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductPackageSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSkuSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSpecificationSscDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 中台产品转换工具类
 *
 * <AUTHOR>
 * @date 11/24/20 11:39 AM
 */
public class ProductSscConvertor {

    /**
     * 产品info转换
     */
    public static UnifyProductInfoPO convertToProductInfoPO(ProductInfoSscDTO productInfoSscDTO) {
        if (productInfoSscDTO == null) {
            return null;
        }
        UnifyProductInfoPO productInfoPO = new UnifyProductInfoPO();
        BeanUtils.copyProperties(productInfoSscDTO, productInfoPO);
        return productInfoPO;
    }

    /**
     * 产品规格转换
     */
    public static List<UnifyProductSpecificationPO>
        convertToProductSpecificationPO(List<ProductSpecificationSscDTO> productSpecificationSscDTOList) {
        if (CollectionUtils.isEmpty(productSpecificationSscDTOList)) {
            return null;
        }
        List<UnifyProductSpecificationPO> poList = new ArrayList<>();
        productSpecificationSscDTOList.forEach(dto -> {
            UnifyProductSpecificationPO po = convertToProductSpecificationPO(dto);
            if (po != null) {
                poList.add(po);
            }
        });
        return poList;
    }

    /**
     * 产品规格转换
     */
    private static UnifyProductSpecificationPO
        convertToProductSpecificationPO(ProductSpecificationSscDTO productSpecificationSscDTO) {
        if (productSpecificationSscDTO == null) {
            return null;
        }
        UnifyProductSpecificationPO productSpecificationPO = new UnifyProductSpecificationPO();
        BeanUtils.copyProperties(productSpecificationSscDTO, productSpecificationPO);
        return productSpecificationPO;
    }

    /**
     * 产品包装单位转换
     */
    public static List<UnifyProductPackagePO>
        convertToProductPackagePO(List<ProductPackageSscDTO> productPackageSscDTOList) {
        if (CollectionUtils.isEmpty(productPackageSscDTOList)) {
            return null;
        }
        List<UnifyProductPackagePO> poList = new ArrayList<>();
        productPackageSscDTOList.forEach(dto -> {
            UnifyProductPackagePO po = convertToProductPackagePO(dto);
            if (po != null) {
                poList.add(po);
            }
        });
        return poList;
    }

    /**
     * 产品包装单位转换
     */
    private static UnifyProductPackagePO convertToProductPackagePO(ProductPackageSscDTO productPackageSscDTO) {
        if (productPackageSscDTO == null) {
            return null;
        }
        UnifyProductPackagePO productPackagePO = new UnifyProductPackagePO();
        BeanUtils.copyProperties(productPackageSscDTO, productPackagePO);
        return productPackagePO;
    }

    /**
     * 产品SKU转换
     */
    public static UnifyProductSkuPO convertToProductSkuPO(ProductSkuSscDTO productSkuSscDTO) {
        if (productSkuSscDTO == null) {
            return null;
        }
        UnifyProductSkuPO productSkuPO = new UnifyProductSkuPO();
        BeanUtils.copyProperties(productSkuSscDTO, productSkuPO);
        return productSkuPO;
    }
}
