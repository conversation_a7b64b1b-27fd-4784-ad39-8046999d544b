package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.StateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByEasyBusinessDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * 易经商转换工具类
 *
 * <AUTHOR>
 * @date 2019/7/4 16:14
 */
public class ProductSyncByEasyBusinessConvertor {

    /**
     * 操作人id
     */
    private static final Integer DEFAULT_USER_ID = 0;

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuByEasyBusinessDTO easyBusinessDTO) {
        ProductSkuPO productSkuPO = new ProductSkuPO();
        if (null != easyBusinessDTO) {
            BeanUtils.copyProperties(easyBusinessDTO, productSkuPO);
            // 产品信息id
            productSkuPO.setProductInfoId(UUIDGenerator.getUUID("ProductInfo"));
            // 产品来源
            productSkuPO.setSource(ProductSourceType.易经商);
            // 默认
            Date now = new Date();
            productSkuPO.setCreateTime(now);
            productSkuPO.setCreateUserId(DEFAULT_USER_ID);
            productSkuPO.setLastUpdateTime(now);
            productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
        }
        return productSkuPO;
    }

    /**
     * 产品info转换
     * 
     * @return
     */
    public static ProductInfoPO convertToProductInfoPO(ProductSkuPO productSkuPO) {
        ProductInfoPO productInfoPO = new ProductInfoPO();
        if (null != productSkuPO) {
            productInfoPO.setId(productSkuPO.getProductInfoId());
            productInfoPO.setBrand(productSkuPO.getProductBrand());
            productInfoPO.setProductName(productSkuPO.getName());
            productInfoPO.setGeneralName("");
            productInfoPO.setStatus(ProductInfoStateEnum.上架.getType());
            // 默认
            productInfoPO.setHasBottleCode((byte)1);
            productInfoPO.setHasBoxCode((byte)1);
            productInfoPO.setShelfLifeLongTime((byte)0);
            productInfoPO.setProcess((byte)0);
            Date now = new Date();
            productInfoPO.setCreateTime(now);
            productInfoPO.setCreateUser_Id(DEFAULT_USER_ID);
            productInfoPO.setLastUpdateTime(now);
            productInfoPO.setLastUpdateUser_Id(DEFAULT_USER_ID);

            /** cityId 设置为parentOrgId */
            productInfoPO.setParentOrgId(productSkuPO.getCityId());

        }
        return productInfoPO;
    }

    /**
     * 产品规格转换
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductSkuPO productSkuPO) {
        ProductInfoSpecificationPO specificationPO = new ProductInfoSpecificationPO();
        if (null != productSkuPO) {
            specificationPO.setId(productSkuPO.getProductSpecificationId());
            specificationPO.setProductInfo_Id(productSkuPO.getProductInfoId());
            specificationPO.setName(productSkuPO.getSpecificationName());
            specificationPO.setState(StateEnum.启用.getType());
            // 默认
            Date now = new Date();
            specificationPO.setCreateTime(now);
            specificationPO.setCreateUser_Id(DEFAULT_USER_ID);
            specificationPO.setLastUpdateTime(now);
            specificationPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        }
        return specificationPO;
    }
}
