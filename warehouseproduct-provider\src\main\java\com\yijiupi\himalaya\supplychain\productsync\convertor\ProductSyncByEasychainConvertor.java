package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.easychain.ProductSkuMessageDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 易款连锁转换工具类
 *
 * <AUTHOR>
 * @date 2019/5/7 21:14
 */
public class ProductSyncByEasychainConvertor {

    /**
     * 操作人id
     */
    private static final Integer DEFAULT_USER_ID = 0;

    /**
     * 货主名称
     */
    private static final String OWNER_NAME = "易款连锁";

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuMessageDTO productSkuMessageDTO) {
        ProductSkuPO productSkuPO = new ProductSkuPO();
        if (null != productSkuMessageDTO) {
            // 大区id
            productSkuPO.setCityId(productSkuMessageDTO.getWmsRegionId() != null
                ? productSkuMessageDTO.getWmsRegionId().intValue() : null);
            // 产品skuId
            productSkuPO.setProductSkuId(productSkuMessageDTO.getId());
            // 产品信息id
            productSkuPO.setProductInfoId(productSkuMessageDTO.getYjpProductInfoId());
            // 产品信息规格id
            productSkuPO.setProductSpecificationId(productSkuMessageDTO.getYjpProductInfoSpecId());
            // 产品名称
            productSkuPO.setName(productSkuMessageDTO.getProductName());
            // 包装规格名称
            productSkuPO.setSpecificationName(productSkuMessageDTO.getYjpSpecName());
            // 包装规格大单位
            productSkuPO.setPackageName(productSkuMessageDTO.getYjpSpecPackageName());
            // 包装规格小单位
            productSkuPO.setUnitName(productSkuMessageDTO.getYjpSpecUnitName());
            // 包装规格大小单位转换系数
            productSkuPO.setPackageQuantity(new BigDecimal(productSkuMessageDTO.getYjpSpecQuantity()));
            // 产品状态
            productSkuPO.setProductState(convertProductState(productSkuMessageDTO.getState()));
            // 产品来源
            productSkuPO.setSource(productSkuMessageDTO.getSource());
            // 货主id
            productSkuPO.setCompanyId(productSkuMessageDTO.getOwnerId());
            // 货主名称
            productSkuPO.setOwnerName(Objects.equals(null, productSkuMessageDTO.getSource()) ? OWNER_NAME
                : ProductSourceType.getSourceTypeName(productSkuMessageDTO.getSource()));
            // 默认
            Date now = new Date();
            productSkuPO.setCreateTime(now);
            productSkuPO.setCreateUserId(DEFAULT_USER_ID);
            productSkuPO.setLastUpdateTime(now);
            productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
            // 配送系数
            productSkuPO.setDistributionPercentForAmount(productSkuMessageDTO.getDeliveryCoefficient());
            // 中台skuId
            productSkuPO.setUnifySkuId(productSkuMessageDTO.getYjpProductSkuId());
        }
        return productSkuPO;
    }

    private static Integer convertProductState(Integer state) {
        Integer productState = ProductSkuStateEnum.上架.getType();
        if (state != null) {
            if (state == 0) {
                productState = ProductSkuStateEnum.上架.getType();
            } else if (state == 1) {
                productState = ProductSkuStateEnum.下架.getType();
            } else if (state == 2) {
                productState = ProductSkuStateEnum.作废.getType();
            }
        }
        return productState;
    }
}
