package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.dto.enums.CommonConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ParentOrgIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductInfoSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductPackageSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSkuSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSpecificationSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductPackageDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSpecificationDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步中台产品转换工具类
 *
 * <AUTHOR>
 * @date 11/24/20 11:39 AM
 */
public class ProductSyncBySscConvertor {

    /**
     * 产品info转换
     */
    public static ProductInfoSscDTO convertToProductInfoSscDTO(SyncSscProductInfoDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        // 产品信息
        ProductInfoSscDTO productInfoSscDTO = new ProductInfoSscDTO();
        BeanUtils.copyProperties(productInfoDTO, productInfoSscDTO);
        productInfoSscDTO.setId(productInfoDTO.getId() != null ? Long.valueOf(productInfoDTO.getId()) : null);
        productInfoSscDTO.setProductName(productInfoDTO.getName());
        productInfoSscDTO.setShelfLifeType(convertByte(productInfoDTO.getShelfLifeType()));
        productInfoSscDTO.setProductSaleType(convertByte(productInfoDTO.getProductSaleType()));
        productInfoSscDTO.setState(convertByte(productInfoDTO.getState()));
        productInfoSscDTO.setParentOrgId(ParentOrgIdConstant.YIJIUPI);
        productInfoSscDTO.setCreateUserId(CommonConstant.DEFAULT_USER_ID);
        productInfoSscDTO.setLastUpdateUserId(CommonConstant.DEFAULT_USER_ID);

        // 产品规格
        if (!CollectionUtils.isEmpty(productInfoDTO.getProductSpecifications())) {
            List<ProductSpecificationSscDTO> specificationSscDTOList = new ArrayList<>();
            productInfoDTO.getProductSpecifications().forEach(specDTO -> {
                // 产品规格转换
                ProductSpecificationSscDTO specificationSscDTO =
                    convertToProductSpecificationSscDTO(specDTO, productInfoSscDTO.getId());
                if (specificationSscDTO != null) {
                    specificationSscDTOList.add(specificationSscDTO);
                }
            });
            productInfoSscDTO.setProductSpecificationList(specificationSscDTOList);
        }

        // 产品包装单位
        if (!CollectionUtils.isEmpty(productInfoDTO.getProductPackages())) {
            List<ProductPackageSscDTO> packageSscDTOList = new ArrayList<>();
            productInfoDTO.getProductPackages().forEach(packageDTO -> {
                // 产品包装单位转换
                ProductPackageSscDTO packageSscDTO =
                    convertToProductPackageSscDTO(packageDTO, productInfoSscDTO.getId());
                if (packageSscDTO != null) {
                    packageSscDTOList.add(packageSscDTO);
                }
            });
            productInfoSscDTO.setProductPackageList(packageSscDTOList);
        }
        return productInfoSscDTO;
    }

    /**
     * 产品规格转换
     */
    private static ProductSpecificationSscDTO convertToProductSpecificationSscDTO(
        SyncSscProductSpecificationDTO productSpecificationDTO, Long productInfoId) {
        if (productSpecificationDTO == null) {
            return null;
        }
        ProductSpecificationSscDTO productSpecificationSscDTO = new ProductSpecificationSscDTO();
        BeanUtils.copyProperties(productSpecificationDTO, productSpecificationSscDTO);
        productSpecificationSscDTO.setId(convertLong(productSpecificationDTO.getId()));
        productSpecificationSscDTO.setProductInfoId(convertLong(productSpecificationDTO.getProductInfoId()));
        if (productSpecificationSscDTO.getProductInfoId() == null) {
            productSpecificationSscDTO.setProductInfoId(productInfoId);
        }
        productSpecificationSscDTO.setState(convertByte(productSpecificationDTO.getState()));
        productSpecificationSscDTO.setCreateUserId(CommonConstant.DEFAULT_USER_ID);
        productSpecificationSscDTO.setLastUpdateUserId(CommonConstant.DEFAULT_USER_ID);
        return productSpecificationSscDTO;
    }

    /**
     * 产品包装单位转换
     */
    private static ProductPackageSscDTO convertToProductPackageSscDTO(SyncSscProductPackageDTO productPackageDTO,
        Long productInfoId) {
        if (productPackageDTO == null) {
            return null;
        }
        ProductPackageSscDTO productPackageSscDTO = new ProductPackageSscDTO();
        BeanUtils.copyProperties(productPackageDTO, productPackageSscDTO);
        productPackageSscDTO.setChildPackageQuantity(productPackageDTO.getChildPackageQuantity() != null
            ? new BigDecimal(productPackageDTO.getChildPackageQuantity()) : null);
        productPackageSscDTO.setScattered(productPackageDTO.getScattered() != null
            ? (productPackageDTO.getScattered() ? ConditionStateEnum.是.getType() : ConditionStateEnum.否.getType())
            : null);
        productPackageSscDTO.setState(convertByte(productPackageDTO.getState()));
        productPackageSscDTO.setInfoId(convertLong(productPackageDTO.getProductInfoId()));
        if (productPackageSscDTO.getInfoId() == null) {
            productPackageSscDTO.setInfoId(productInfoId);
        }
        productPackageSscDTO.setVolumeHeight(convertBigDecimal(productPackageDTO.getHeight()));
        productPackageSscDTO.setVolumeWidth(convertBigDecimal(productPackageDTO.getWidth()));
        productPackageSscDTO.setVolumeLength(convertBigDecimal(productPackageDTO.getLength()));
        productPackageSscDTO.setWeight(convertBigDecimal(productPackageDTO.getWeight()));
        productPackageSscDTO.setCreateUserId(CommonConstant.DEFAULT_USER_ID);
        productPackageSscDTO.setLastUpdateUserId(CommonConstant.DEFAULT_USER_ID);
        return productPackageSscDTO;
    }

    /**
     * 产品SKU转换
     */
    public static ProductSkuSscDTO convertToProductSkuSscDTO(SyncSscProductSkuDTO productSkuDTO) {
        if (productSkuDTO == null) {
            return null;
        }
        ProductSkuSscDTO productSkuSscDTO = new ProductSkuSscDTO();
        productSkuSscDTO.setSkuId(productSkuDTO.getId());
        productSkuSscDTO.setSourceSkuId(productSkuDTO.getId().toString());
        productSkuSscDTO.setInfoId(productSkuDTO.getProductInfoId());
        productSkuSscDTO.setSpecificationId(productSkuDTO.getProductSpecificationId());
        productSkuSscDTO.setPackageId(productSkuDTO.getProductPackageId());
        productSkuSscDTO.setPackageQuantity(productSkuDTO.getSpecificationQuantity() != null
            ? BigDecimal.valueOf(productSkuDTO.getSpecificationQuantity()) : null);
        productSkuSscDTO.setOwnerId(productSkuDTO.getCompanyId());
        productSkuSscDTO.setState(convertByte(productSkuDTO.getState()));
        productSkuSscDTO.setSource((byte)ProductSourceType.易酒批);
        productSkuSscDTO.setCreateUserId(CommonConstant.DEFAULT_USER_ID);
        productSkuSscDTO.setLastUpdateUserId(CommonConstant.DEFAULT_USER_ID);
        // 手动指定中台规格name
        productSkuSscDTO.setSpecificationName(productSkuDTO.getNewSpecificationName());
        return productSkuSscDTO;
    }

    private static Long convertLong(Integer i) {
        return i != null ? Long.valueOf(i) : null;
    }

    private static Byte convertByte(Integer i) {
        return i != null ? i.byteValue() : null;
    }

    private static BigDecimal convertBigDecimal(Double d) {
        return d != null ? BigDecimal.valueOf(d) : null;
    }
}
