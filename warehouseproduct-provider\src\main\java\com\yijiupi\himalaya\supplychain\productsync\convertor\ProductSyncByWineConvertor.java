package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.StateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoByWineSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByWineSyncDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 微酒实体转换类
 *
 * <AUTHOR>
 * @date 2020-2-3 15:23
 */
public class ProductSyncByWineConvertor {

    /**
     * 操作人id
     */
    private static final Integer DEFAULT_USER_ID = 0;

    /**
     * 产品信息PO转换
     * 
     * @return
     */
    public static ProductInfoPO convertorToProductInfoPO(ProductInfoByWineSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();

        productInfoPO.setId(syncDTO.getId());
        productInfoPO.setProductName(syncDTO.getProductName());
        productInfoPO.setGeneralName(productInfoPO.getProductName());
        if (syncDTO.getState() != null) {
            productInfoPO.setStatus(Objects.equals(syncDTO.getState(), StateEnum.停用.getType())
                ? ProductInfoStateEnum.下架.getType() : ProductInfoStateEnum.上架.getType());
        }
        Date now = new Date();
        productInfoPO.setCreateTime(now);
        productInfoPO.setCreateUser_Id(DEFAULT_USER_ID);
        productInfoPO.setLastUpdateTime(now);
        productInfoPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        return productInfoPO;
    }

    /**
     * 产品信息规格转换
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductInfoByWineSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductInfoSpecificationPO specificationPO = new ProductInfoSpecificationPO();
        specificationPO.setId(syncDTO.getId());
        specificationPO.setProductInfo_Id(syncDTO.getId());
        specificationPO.setName(syncDTO.getSpecName());
        specificationPO.setState(syncDTO.getState());
        specificationPO.setPackageName(syncDTO.getPackageName());
        specificationPO.setUnitName(syncDTO.getUnitName());
        specificationPO.setPackageQuantity(syncDTO.getPackageQuantity());
        Date now = new Date();
        specificationPO.setCreateTime(now);
        specificationPO.setCreateUser_Id(DEFAULT_USER_ID);
        specificationPO.setLastUpdateTime(now);
        specificationPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        return specificationPO;
    }

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuByWineSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        BeanUtils.copyProperties(syncDTO, productSkuPO);
        // 产品规格ID
        productSkuPO.setProductSpecificationId(syncDTO.getProductInfoId());
        // 产品来源，0:酒批，1:微酒（贷款/抵押）
        productSkuPO.setSource(ProductSourceType.微酒);
        // 货主
        productSkuPO.setCompanyId(syncDTO.getCityId() != null ? syncDTO.getCityId().longValue() : null);
        productSkuPO.setSecOwnerId(syncDTO.getCompanyId());
        // 默认
        Date now = new Date();
        productSkuPO.setCreateTime(now);
        productSkuPO.setCreateUserId(DEFAULT_USER_ID);
        productSkuPO.setLastUpdateTime(now);
        productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
        return productSkuPO;
    }
}
