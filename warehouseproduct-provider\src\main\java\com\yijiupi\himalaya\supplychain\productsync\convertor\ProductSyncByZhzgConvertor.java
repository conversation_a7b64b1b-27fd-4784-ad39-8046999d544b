package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.ProductInfoSaleSpecDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoSpecDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductSkuDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 知花知果实体转换类
 *
 * <AUTHOR>
 * @date 2019/3/19 9:14
 */
public class ProductSyncByZhzgConvertor {

    /**
     * 操作人id
     */
    private static final Integer DEFAULT_USER_ID = 0;

    /**
     * 货主名称
     */
    private static final String OWNER_NAME = "知花知果";

    /**
     * 产品信息PO转换
     * 
     * @return
     */
    public static ProductInfoPO convertorToProductInfoPO(SyncProductInfoDTO syncProductInfoDTO) {
        ProductInfoPO productInfoPO = new ProductInfoPO();
        if (null != syncProductInfoDTO) {
            // 产品信息id
            productInfoPO.setId(syncProductInfoDTO.getId());
            // 产品名称
            productInfoPO.setProductName(syncProductInfoDTO.getProductInfoName());
            // 自然名称
            productInfoPO.setGeneralName("");
            // 统计类目
            productInfoPO.setStatisticsCategoryName(syncProductInfoDTO.getFirstCategoryName());
            // 统计类目id
            productInfoPO.setProductStatisticsClass(syncProductInfoDTO.getFirstCategoryId());
            // 产品信息状态
            productInfoPO.setStatus(convertProductInfoStatus(syncProductInfoDTO.getState()));
            // 保质期
            productInfoPO.setMonthOfShelfLife(syncProductInfoDTO.getShelfLife());
            // 保质期单位, 0:年,1:月,2:日
            productInfoPO.setShelfLifeUnit(convertShelfLifeUnit(syncProductInfoDTO.getShelfLifeUnit()));
            // 是否可以加工
            productInfoPO.setProcess(syncProductInfoDTO.getProcess());
            // 默认
            productInfoPO.setHasBottleCode((byte)0);
            productInfoPO.setHasBoxCode((byte)0);
            productInfoPO.setShelfLifeLongTime((byte)0);
            Date now = new Date();
            productInfoPO.setCreateTime(now);
            productInfoPO.setCreateUser_Id(DEFAULT_USER_ID);
            productInfoPO.setLastUpdateTime(now);
            productInfoPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        }
        return productInfoPO;
    }

    /**
     * 产品信息PO转换
     * 
     * @return
     */
    public static ProductInfoPO convertorToProductInfoPO(ProductInfoDTO productInfoDTO) {
        ProductInfoPO productInfoPO = new ProductInfoPO();
        if (null != productInfoDTO) {
            // 产品信息id
            productInfoPO.setId(productInfoDTO.getId());
            // 产品名称
            productInfoPO.setProductName(productInfoDTO.getProductInfoName());
            // 自然名称
            productInfoPO.setGeneralName("");
            // 统计类目
            productInfoPO.setStatisticsCategoryName(productInfoDTO.getFirstCategoryName());
            // 统计类目id
            productInfoPO.setProductStatisticsClass(productInfoDTO.getFirstCategoryId());
            // 产品信息状态
            productInfoPO.setStatus(convertProductInfoStatus(productInfoDTO.getState()));
            // 保质期
            productInfoPO.setMonthOfShelfLife(productInfoDTO.getShelfLife());
            // 保质期单位, 0:年,1:月,2:日
            productInfoPO.setShelfLifeUnit(convertShelfLifeUnit(productInfoDTO.getShelfLifeUnit()));
            // 是否可以加工
            productInfoPO.setProcess(convertProcess(productInfoDTO.getProcess()));
            // 默认
            productInfoPO.setHasBottleCode((byte)0);
            productInfoPO.setHasBoxCode((byte)0);
            productInfoPO.setShelfLifeLongTime((byte)0);
            Date now = new Date();
            productInfoPO.setCreateTime(now);
            productInfoPO.setCreateUser_Id(DEFAULT_USER_ID);
            productInfoPO.setLastUpdateTime(now);
            productInfoPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        }
        return productInfoPO;
    }

    private static Byte convertProcess(Boolean process) {
        if (null == process) {
            return null;
        } else if (process) {
            return (byte)1;
        } else {
            return (byte)0;
        }
    }

    private static Byte convertProductInfoStatus(Integer state) {
        if (null == state) {
            return null;
        } else if (0 == state) {
            return ProductInfoStateEnum.上架.getType();
        } else if (1 == state) {
            return ProductInfoStateEnum.下架.getType();
        }
        return null;
    }

    /**
     * 保质期单位转换
     * 
     * @return
     */
    private static Byte convertShelfLifeUnit(Integer shelfLifeUnit) {
        if (null == shelfLifeUnit) {
            return null;
        } else if (0 == shelfLifeUnit) {
            return 1;
        } else if (1 == shelfLifeUnit) {
            return 2;
        } else if (2 == shelfLifeUnit) {
            return 3;
        }
        return null;
    }

    /**
     * 产品信息规格转换
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(SyncProductInfoSpecDTO syncProductInfoSpecDTO) {
        ProductInfoSpecificationPO productInfoSpecificationPO = new ProductInfoSpecificationPO();
        if (null != syncProductInfoSpecDTO) {
            // 规格id
            productInfoSpecificationPO.setId(syncProductInfoSpecDTO.getId());
            // 产品信息id
            productInfoSpecificationPO.setProductInfo_Id(syncProductInfoSpecDTO.getProductInfoId());
            // 规格名称
            productInfoSpecificationPO.setName(syncProductInfoSpecDTO.getSpecName());
            // 状态
            productInfoSpecificationPO.setState(
                null != syncProductInfoSpecDTO.getState() ? syncProductInfoSpecDTO.getState().byteValue() : null);
            // 对应转换的产品信息规格id
            productInfoSpecificationPO
                .setConvertProductInfoSpecId(syncProductInfoSpecDTO.getConvertProductInfoSpecId());
            // 对应转换的规格转换系数
            productInfoSpecificationPO.setConvertSpecQuantity(syncProductInfoSpecDTO.getSpecQuantity());
            // 对应转换的产品信息规格单位
            productInfoSpecificationPO.setConvertSpecUnitName(syncProductInfoSpecDTO.getConvertSpecUnitName());
            // 产品规格单位
            productInfoSpecificationPO.setPackageName(syncProductInfoSpecDTO.getSpecPackageName());
            // 默认
            Date now = new Date();
            productInfoSpecificationPO.setCreateTime(now);
            productInfoSpecificationPO.setCreateUser_Id(DEFAULT_USER_ID);
            productInfoSpecificationPO.setLastUpdateTime(now);
            productInfoSpecificationPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        }
        return productInfoSpecificationPO;
    }

    /**
     * 产品信息规格转换
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductInfoSaleSpecDTO productInfoSaleSpecDTO) {
        ProductInfoSpecificationPO productInfoSpecificationPO = new ProductInfoSpecificationPO();
        if (null != productInfoSaleSpecDTO) {
            // 规格id
            productInfoSpecificationPO.setId(productInfoSaleSpecDTO.getId());
            // 产品信息id
            productInfoSpecificationPO.setProductInfo_Id(productInfoSaleSpecDTO.getProductInfoId());
            // 规格名称
            productInfoSpecificationPO.setName(productInfoSaleSpecDTO.getSpecName());
            // 状态
            productInfoSpecificationPO.setState(
                null != productInfoSaleSpecDTO.getState() ? productInfoSaleSpecDTO.getState().byteValue() : null);
            // 对应转换的产品信息规格id
            productInfoSpecificationPO.setConvertProductInfoSpecId(productInfoSaleSpecDTO.getConvertSpecId());
            // 对应转换的规格转换系数
            productInfoSpecificationPO.setConvertSpecQuantity(productInfoSaleSpecDTO.getSpecificationQuantity());
            // 对应转换的产品信息规格单位
            productInfoSpecificationPO.setConvertSpecUnitName(productInfoSaleSpecDTO.getSpecUnitName());
            // 产品规格单位
            productInfoSpecificationPO.setPackageName(productInfoSaleSpecDTO.getSpecPackageName());
            // 默认
            Date now = new Date();
            productInfoSpecificationPO.setCreateTime(now);
            productInfoSpecificationPO.setCreateUser_Id(DEFAULT_USER_ID);
            productInfoSpecificationPO.setLastUpdateTime(now);
            productInfoSpecificationPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        }
        return productInfoSpecificationPO;
    }

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(SyncProductSkuDTO syncProductSkuDTO) {
        ProductSkuPO productSkuPO = new ProductSkuPO();
        if (null != syncProductSkuDTO) {
            // 产品信息id
            productSkuPO.setProductInfoId(syncProductSkuDTO.getProductInfoId());
            // 产品信息规格id
            productSkuPO.setProductSpecificationId(syncProductSkuDTO.getProductInfoSpecId());
            // 产品名称
            productSkuPO.setName(syncProductSkuDTO.getSkuName());
            // 包装规格名称
            productSkuPO.setSpecificationName(syncProductSkuDTO.getSpecName());
            // 包装规格大单位
            productSkuPO.setPackageName(syncProductSkuDTO.getSpecPackageName());
            // 包装规格小单位
            productSkuPO.setUnitName(syncProductSkuDTO.getSpecUnitName());
            // 包装规格大小单位转换系数
            productSkuPO.setPackageQuantity(syncProductSkuDTO.getSpecificationQuantity());
            // 产品状态
            productSkuPO.setProductState(convertProductSkuState(syncProductSkuDTO.getState()));
            // 产品来源
            productSkuPO.setSource(ProductSourceType.知花知果);
            // 货主名称
            productSkuPO.setOwnerName(OWNER_NAME);
            // 默认
            Date now = new Date();
            productSkuPO.setCreateTime(now);
            productSkuPO.setCreateUserId(DEFAULT_USER_ID);
            productSkuPO.setLastUpdateTime(now);
            productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
        }
        return productSkuPO;
    }

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuDTO productSkuDTO) {
        ProductSkuPO productSkuPO = new ProductSkuPO();
        if (null != productSkuDTO) {
            // 产品信息id
            productSkuPO.setProductInfoId(productSkuDTO.getProductInfoId());
            // 产品信息规格id
            productSkuPO.setProductSpecificationId(productSkuDTO.getProductInfoSpecId());
            // 产品名称
            productSkuPO.setName(productSkuDTO.getSkuName());
            // 产品状态
            productSkuPO.setProductState(convertProductSkuState(productSkuDTO.getState()));
            // 产品来源
            productSkuPO.setSource(ProductSourceType.知花知果);
            // 货主名称
            productSkuPO.setOwnerName(OWNER_NAME);
            // 默认
            Date now = new Date();
            productSkuPO.setCreateTime(now);
            productSkuPO.setCreateUserId(DEFAULT_USER_ID);
            productSkuPO.setLastUpdateTime(now);
            productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
        }
        return productSkuPO;
    }

    private static Integer convertProductSkuState(Integer state) {
        if (null == state) {
            return null;
        } else if (0 == state) {
            return ProductSkuStateEnum.下架.getType();
        } else if (1 == state) {
            return ProductSkuStateEnum.上架.getType();
        }
        return null;
    }

    public static ProductSkuPO convertToProductSkuPO(SyncProductInfoSpecDTO infoSpecDTO) {
        if (null == infoSpecDTO) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setSource(ProductSourceType.知花知果);
        productSkuPO.setProductInfoId(infoSpecDTO.getProductInfoId());
        productSkuPO.setProductSpecificationId(infoSpecDTO.getId());
        productSkuPO.setSpecificationName(infoSpecDTO.getSpecName());
        productSkuPO.setPackageName(infoSpecDTO.getSpecPackageName());
        productSkuPO.setUnitName(infoSpecDTO.getConvertSpecUnitName());
        return productSkuPO;
    }

    public static ProductSkuPO convertToProductSkuPO(ProductInfoPO productInfoPO) {
        if (null == productInfoPO) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setSource(ProductSourceType.知花知果);
        productSkuPO.setProductInfoId(productInfoPO.getId());
        productSkuPO.setName(productInfoPO.getProductName());
        return productSkuPO;
    }

    /**
     * 拆分list
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }
        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }
}
