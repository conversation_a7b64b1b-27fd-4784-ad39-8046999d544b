package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.StateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zskx.ProductSkuByZskxSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

import java.util.Date;

/**
 * 掌上快销实体转换类
 *
 * <AUTHOR>
 * @date 2019-12-10 15:23
 */
public class ProductSyncByZskxConvertor {

    /**
     * 操作人id
     */
    private static final Integer DEFAULT_USER_ID = 0;

    /**
     * 货主名称
     */
    private static final String OWNER_NAME = "掌上快销";

    /**
     * 产品信息PO转换
     * 
     * @return
     */
    public static ProductInfoPO convertorToProductInfoPO(ProductSkuByZskxSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductInfoPO productInfoPO = new ProductInfoPO();
        // 产品信息id
        productInfoPO.setId(syncDTO.getProductInfoId());
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        // 产品品牌
        productInfoPO.setBrand(syncDTO.getBrand());
        // 产品名称
        productInfoPO.setProductName(syncDTO.getProductName());
        // 自然名称
        productInfoPO.setGeneralName("");
        // 产品信息状态
        productInfoPO.setStatus(ProductInfoStateEnum.上架.getType());
        Date now = new Date();
        productInfoPO.setCreateTime(now);
        productInfoPO.setCreateUser_Id(DEFAULT_USER_ID);
        productInfoPO.setLastUpdateTime(now);
        productInfoPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        return productInfoPO;
    }

    /**
     * 产品信息规格转换
     * 
     * @return
     */
    public static ProductInfoSpecificationPO convertToProductInfoSpecPO(ProductSkuByZskxSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductInfoSpecificationPO productInfoSpecificationPO = new ProductInfoSpecificationPO();

        // 规格id
        productInfoSpecificationPO.setId(syncDTO.getProductSpecificationId());
        if (productInfoSpecificationPO.getId() == null) {
            productInfoSpecificationPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO_SPEC));
        }
        // 产品信息id
        productInfoSpecificationPO.setProductInfo_Id(syncDTO.getProductInfoId());
        // 规格名称
        productInfoSpecificationPO.setName(syncDTO.getSpecificationName());
        // 状态
        productInfoSpecificationPO.setState(StateEnum.启用.getType());
        // 包装规格大单位
        productInfoSpecificationPO.setPackageName(syncDTO.getPackageName());
        // 包装规格小单位
        productInfoSpecificationPO.setUnitName(syncDTO.getUnitName());
        // 包装规格大小单位转换系数
        productInfoSpecificationPO.setPackageQuantity(syncDTO.getPackageQuantity());
        // 默认
        Date now = new Date();
        productInfoSpecificationPO.setCreateTime(now);
        productInfoSpecificationPO.setCreateUser_Id(DEFAULT_USER_ID);
        productInfoSpecificationPO.setLastUpdateTime(now);
        productInfoSpecificationPO.setLastUpdateUser_Id(DEFAULT_USER_ID);
        return productInfoSpecificationPO;
    }

    /**
     * 产品sku转换
     * 
     * @return
     */
    public static ProductSkuPO convertToProductSkuPO(ProductSkuByZskxSyncDTO syncDTO) {
        if (syncDTO == null) {
            return null;
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        // id
        productSkuPO.setId(syncDTO.getId());
        if (productSkuPO.getId() == null) {
            productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
        }
        // 城市id
        productSkuPO.setCityId(syncDTO.getOrgId());
        // 产品skuId
        productSkuPO.setProductSkuId(syncDTO.getProductSkuId());
        // 产品信息id
        productSkuPO.setProductInfoId(syncDTO.getProductInfoId());
        // 产品信息规格id
        productSkuPO.setProductSpecificationId(syncDTO.getProductSpecificationId());
        // 产品名称
        productSkuPO.setName(syncDTO.getProductName());
        // 包装规格名称
        productSkuPO.setSpecificationName(syncDTO.getSpecificationName());
        // 包装规格大单位
        productSkuPO.setPackageName(syncDTO.getPackageName());
        // 包装规格小单位
        productSkuPO.setUnitName(syncDTO.getUnitName());
        // 包装规格大小单位转换系数
        productSkuPO.setPackageQuantity(syncDTO.getPackageQuantity());
        // 产品状态
        productSkuPO.setProductState(syncDTO.getProductState());
        // 品牌
        productSkuPO.setProductBrand(syncDTO.getBrand());
        // 保质期（天）
        if (syncDTO.getMonthOfShelfLife() != null) {
            productSkuPO.setMonthOfShelfLife(syncDTO.getMonthOfShelfLife());
            productSkuPO.setShelfLifeUnit(3);
        }
        // 产品来源
        productSkuPO.setSource(ProductSourceType.易酒批);
        // 货主名称
        productSkuPO.setOwnerName(OWNER_NAME);
        // 默认
        Date now = new Date();
        productSkuPO.setCreateTime(now);
        productSkuPO.setCreateUserId(DEFAULT_USER_ID);
        productSkuPO.setLastUpdateTime(now);
        productSkuPO.setLastUpdateUserId(DEFAULT_USER_ID);
        return productSkuPO;
    }
}
