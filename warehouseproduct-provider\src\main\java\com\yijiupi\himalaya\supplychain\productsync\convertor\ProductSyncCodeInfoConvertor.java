package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ProductSyncCodeInfoConvertor {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncCodeInfoConvertor.class);

    /**
     * 同步产品条码/箱码信息
     * 
     * @param syncDTOList
     * @return
     */
    public Map<Long, List<ProductCodeInfoDTO>> convertToProductCodeInfoDTO(List<SyncProductCodeInfoDTO> syncDTOList) {
        if (CollectionUtils.isEmpty(syncDTOList)) {
            return null;
        }
        Map<Long, List<ProductCodeInfoDTO>> result = new HashMap<>(16);
        for (int i = 0; i < syncDTOList.size(); i++) {
            SyncProductCodeInfoDTO syncDTO = syncDTOList.get(i);
            if (syncDTO == null) {
                continue;
            }
            List<SyncProductCodeInfoItemDTO> codeItemDTOList = syncDTO.getCodeItemDTOList();
            if (CollectionUtils.isEmpty(codeItemDTOList)) {
                LOG.info("产品[条码/箱码]同步转化，产品[{}]无明细信息！", syncDTO.getProductInfoId());
                continue;
            }
            List<ProductCodeInfoDTO> itemDTOList = new ArrayList<ProductCodeInfoDTO>(codeItemDTOList.size());
            for (int j = 0; j < codeItemDTOList.size(); j++) {
                SyncProductCodeInfoItemDTO syncItemDTO = codeItemDTOList.get(j);
                if (syncItemDTO == null) {
                    continue;
                }
                ProductCodeInfoDTO itemDTO = new ProductCodeInfoDTO();
                BeanUtils.copyProperties(syncItemDTO, itemDTO);
                itemDTO.setCreateUser(syncDTO.getOperatorUser());
                itemDTO.setCreateTime(syncDTO.getBusinessTime() == null ? new Date() : syncDTO.getBusinessTime());
                itemDTO.setLastUpdateUser(syncDTO.getOperatorUser());
                itemDTO.setLastUpdateTime(itemDTO.getCreateTime());
                itemDTO.setIsNoCode(
                    StringUtils.isBlank(itemDTO.getCode()) ? WhetherEnum.YES.getType() : WhetherEnum.NO.getType());
                itemDTOList.add(itemDTO);
            }
            // 存放结果
            result.put(syncDTO.getProductInfoId(), itemDTOList);
        }
        return result;
    }
}
