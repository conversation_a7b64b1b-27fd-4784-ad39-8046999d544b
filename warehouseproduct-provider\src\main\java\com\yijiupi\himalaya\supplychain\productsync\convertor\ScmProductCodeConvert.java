package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.dubbop.dto.Code;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductBoxCode;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductCodeDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ScmProductCodeConvert {
    public static List<ScmProductCodeDTO> convertJiuPiBottleCodeList(List<Code> bottleCodeList) {
        List<ScmProductCodeDTO> jiuPiBottleCodeList = new ArrayList<>(bottleCodeList.size());
        if (CollectionUtils.isEmpty(bottleCodeList)) {
            return jiuPiBottleCodeList;
        }
        for (Code code : bottleCodeList) {
            ScmProductCodeDTO scmProductCodeDTO = new ScmProductCodeDTO();
            scmProductCodeDTO.setCode(code.getCode());
            scmProductCodeDTO.setCustom(code.getIsCustom());
            scmProductCodeDTO.setState(1);
            jiuPiBottleCodeList.add(scmProductCodeDTO);
        }
        return jiuPiBottleCodeList;
    }

    public static List<ScmProductCodeDTO> convertBoxCode(List<List<ProductBoxCode>> productBoxCodeList) {
        List<ScmProductCodeDTO> boxCodeList = new ArrayList<>(productBoxCodeList.size());
        if (CollectionUtils.isEmpty(productBoxCodeList)) {
            return boxCodeList;
        }
        for (List<ProductBoxCode> productBoxCodes : productBoxCodeList) {
            if (CollectionUtils.isEmpty(productBoxCodes)) {
                continue;
            }
            for (ProductBoxCode productBoxCode : productBoxCodes) {
                ScmProductCodeDTO scmProductCodeDTO = new ScmProductCodeDTO();
                scmProductCodeDTO.setCustom(productBoxCode.getIsCustom());
                scmProductCodeDTO.setCode(productBoxCode.getBoxCode());
                scmProductCodeDTO.setOwnerId(productBoxCode.getOwnerId());
                scmProductCodeDTO.setOwnerType(productBoxCode.getOwnerType());
                scmProductCodeDTO.setState(1);
                boxCodeList.add(scmProductCodeDTO);
            }
        }
        return boxCodeList;
    }
}
