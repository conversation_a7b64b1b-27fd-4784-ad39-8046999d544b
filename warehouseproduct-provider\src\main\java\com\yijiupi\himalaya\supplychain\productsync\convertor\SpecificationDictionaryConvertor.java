package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionaryDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import org.springframework.beans.BeanUtils;

/**
 * 产品管理SAAS化转换类
 */
public class SpecificationDictionaryConvertor {

    /**
     * SpecificationDictionaryDTO 转换为 SpecificationDictionaryPO
     * 
     * @return
     */
    public static SpecificationDictionaryPO
        convertToSpecificationDictionaryPO(SpecificationDictionaryDTO specificationDictionaryDTO) {
        if (specificationDictionaryDTO == null) {
            return null;
        }
        SpecificationDictionaryPO specificationDictionaryPO = new SpecificationDictionaryPO();
        BeanUtils.copyProperties(specificationDictionaryDTO, specificationDictionaryPO);
        if (specificationDictionaryPO.getId() == null) {
            specificationDictionaryPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.SPEC_DICTIONARY));
        }
        if (specificationDictionaryPO.getPackageQuantity() != null && specificationDictionaryPO.getPackageName() != null
            && specificationDictionaryPO.getUnitName() != null) {
            specificationDictionaryPO.setSpecificationName(String.format("%s%s/%s",
                specificationDictionaryPO.getPackageQuantity().stripTrailingZeros().toPlainString(),
                specificationDictionaryPO.getUnitName(), specificationDictionaryPO.getPackageName()));
        }
        return specificationDictionaryPO;
    }

    /**
     * SpecificationDictionaryPO 转换为 SpecificationDictionaryDTO
     * 
     * @return
     */
    public static SpecificationDictionaryDTO
        convertToSpecificationDictionaryDTO(SpecificationDictionaryPO specificationDictionaryPO) {
        if (specificationDictionaryPO == null) {
            return null;
        }
        SpecificationDictionaryDTO specificationDictionaryDTO = new SpecificationDictionaryDTO();
        BeanUtils.copyProperties(specificationDictionaryPO, specificationDictionaryDTO);
        return specificationDictionaryDTO;
    }

}
