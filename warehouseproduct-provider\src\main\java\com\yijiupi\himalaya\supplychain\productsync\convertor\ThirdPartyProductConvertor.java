package com.yijiupi.himalaya.supplychain.productsync.convertor;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsRelevancePO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsRelevanceDTO;

/**
 * 
 *
 * <AUTHOR>
 * @date 2019/3/27 16:50
 */
/**
 * 第三方产品信息转换类
 * 
 * <AUTHOR>
 * @date: 2019年12月17日 下午1:38:53
 */
public class ThirdPartyProductConvertor {

    public static ThirdPartyProductsRelevancePO
        convertorTOThirdPartyProductRelevancePO(ThirdPartyProductsRelevanceDTO thirdPartyProductsRelevanceDTO) {
        if (null == thirdPartyProductsRelevanceDTO) {
            return new ThirdPartyProductsRelevancePO();
        }
        ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO = new ThirdPartyProductsRelevancePO();
        BeanUtils.copyProperties(thirdPartyProductsRelevanceDTO, thirdPartyProductsRelevancePO);
        return thirdPartyProductsRelevancePO;
    }

    public static ThirdPartyProductsRelevanceDTO
        convertorTOThirdPartyProductRelevanceDTO(ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO) {
        if (null == thirdPartyProductsRelevancePO) {
            return new ThirdPartyProductsRelevanceDTO();
        }
        ThirdPartyProductsRelevanceDTO thirdPartyProductsRelevanceDTO = new ThirdPartyProductsRelevanceDTO();
        BeanUtils.copyProperties(thirdPartyProductsRelevancePO, thirdPartyProductsRelevanceDTO);
        return thirdPartyProductsRelevanceDTO;
    }

    public static List<ThirdPartyProductsRelevanceDTO> convertorTOThirdPartyProductRelevanceDTOList(
        List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevancePOs) {
        if (CollectionUtils.isEmpty(thirdPartyProductsRelevancePOs)) {
            return null;
        }
        List<ThirdPartyProductsRelevanceDTO> thirdPartyProductsRelevanceDTOs =
            new ArrayList<ThirdPartyProductsRelevanceDTO>();
        thirdPartyProductsRelevancePOs.forEach(thirdPartyProductsRelevancePO -> {
            ThirdPartyProductsRelevanceDTO thirdPartyProductsRelevanceDTO = new ThirdPartyProductsRelevanceDTO();
            BeanUtils.copyProperties(thirdPartyProductsRelevancePO, thirdPartyProductsRelevanceDTO);
            thirdPartyProductsRelevanceDTOs.add(thirdPartyProductsRelevanceDTO);
        });
        return thirdPartyProductsRelevanceDTOs;
    }

    public static List<ThirdPartyProductsRelevancePO> convertorTOThirdPartyProductRelevancePOList(
        List<ThirdPartyProductsRelevanceDTO> thirdPartyProductsRelevanceDTOs) {
        if (CollectionUtils.isEmpty(thirdPartyProductsRelevanceDTOs)) {
            return null;
        }
        List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevancePOs =
            new ArrayList<ThirdPartyProductsRelevancePO>();
        thirdPartyProductsRelevanceDTOs.forEach(thirdPartyProductsRelevanceDTO -> {
            ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO = new ThirdPartyProductsRelevancePO();
            BeanUtils.copyProperties(thirdPartyProductsRelevanceDTO, thirdPartyProductsRelevancePO);
            thirdPartyProductsRelevancePOs.add(thirdPartyProductsRelevancePO);
        });
        return thirdPartyProductsRelevancePOs;
    }

    public static List<ThirdPartyProductsPO>
        convertorTOThirdPartyProductPOList(List<ThirdPartyProductsDTO> thirdPartyProductsDTOs) {
        if (CollectionUtils.isEmpty(thirdPartyProductsDTOs)) {
            return null;
        }
        List<ThirdPartyProductsPO> thirdPartyProductsRelevancePOs = new ArrayList<ThirdPartyProductsPO>();
        thirdPartyProductsDTOs.forEach(thirdPartyProductsDTO -> {
            ThirdPartyProductsPO thirdPartyProductsPO = new ThirdPartyProductsPO();
            BeanUtils.copyProperties(thirdPartyProductsDTO, thirdPartyProductsPO);
            thirdPartyProductsRelevancePOs.add(thirdPartyProductsPO);
        });
        return thirdPartyProductsRelevancePOs;
    }

    public static List<ThirdPartyProductsDTO>
        convertorTOThirdPartyProductDTOList(List<ThirdPartyProductsPO> thirdPartyProductsPOs) {
        if (CollectionUtils.isEmpty(thirdPartyProductsPOs)) {
            return null;
        }
        List<ThirdPartyProductsDTO> thirdPartyProductsDTOs = new ArrayList<ThirdPartyProductsDTO>();
        thirdPartyProductsPOs.forEach(thirdPartyProductsPO -> {
            ThirdPartyProductsDTO thirdPartyProductsDTO = new ThirdPartyProductsDTO();
            BeanUtils.copyProperties(thirdPartyProductsPO, thirdPartyProductsDTO);
            thirdPartyProductsDTOs.add(thirdPartyProductsDTO);
        });
        return thirdPartyProductsDTOs;
    }
}
