package com.yijiupi.himalaya.supplychain.productsync.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.unify.UnifyProductSkuDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 中台产品sku的相互转化
 * 
 * <AUTHOR>
 * @Date 2021/8/17 17:23
 */
@Component
public class UnifyProductSkuConverter extends Convertor<UnifyProductSkuPO, UnifyProductSkuDTO> {
    @Override
    public UnifyProductSkuDTO convert(UnifyProductSkuPO unifyProductSkuPO) {
        if (unifyProductSkuPO == null) {
            return null;
        }
        UnifyProductSkuDTO dto = new UnifyProductSkuDTO();
        BeanUtils.copyProperties(unifyProductSkuPO, dto);
        return dto;
    }

    @Override
    public UnifyProductSkuPO reverseConvert(UnifyProductSkuDTO unifyProductSkuDTO) {
        if (unifyProductSkuDTO == null) {
            return null;
        }
        UnifyProductSkuPO dto = new UnifyProductSkuPO();
        BeanUtils.copyProperties(unifyProductSkuDTO, dto);
        return dto;
    }

    public List<UnifyProductSkuDTO> convertList(List<UnifyProductSkuPO> unifyProductSkuPOS) {
        if (CollectionUtils.isEmpty(unifyProductSkuPOS)) {
            return new ArrayList<>();
        }
        List<UnifyProductSkuDTO> UnifyProductSkuDTOS = new ArrayList<>();
        unifyProductSkuPOS.forEach(elem -> {
            UnifyProductSkuDTO unifyProductSkuDTO = new UnifyProductSkuDTO();
            BeanUtils.copyProperties(elem, unifyProductSkuDTO);
            UnifyProductSkuDTOS.add(unifyProductSkuDTO);
        });
        return UnifyProductSkuDTOS;
    }
}
