package com.yijiupi.himalaya.supplychain.productsync.domain.aspect;

import java.io.Serializable;

public class RequestErrorTrackingDTO implements Serializable {
    private static final long serialVersionUID = -6242597001562628868L;
    /**
     * 请求类型
     */
    private Byte productLine;
    /**
     * 消息类型
     */
    private String requestType;
    /**
     * 消息请求的ID
     */
    private String requestTypeKey;
    /**
     * 请求内容
     */
    private String requestContent;
    /**
     * 相应内容
     */
    private String responseContent;
    /**
     * 请求次数
     */
    private Integer requestCount;
    /**
     * 状态
     */
    private Short state;
    /**
     * 备注
     */
    private String remo;

    public Byte getProductLine() {
        return productLine;
    }

    public void setProductLine(Byte productLine) {
        this.productLine = productLine;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getRequestTypeKey() {
        return requestTypeKey;
    }

    public void setRequestTypeKey(String requestTypeKey) {
        this.requestTypeKey = requestTypeKey;
    }

    public String getRequestContent() {
        return requestContent;
    }

    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public String getResponseContent() {
        return responseContent;
    }

    public void setResponseContent(String responseContent) {
        this.responseContent = responseContent;
    }

    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    public Integer getRequestCount() {
        return requestCount;
    }

    public void setRequestCount(Integer requestCount) {
        this.requestCount = requestCount;
    }
}
