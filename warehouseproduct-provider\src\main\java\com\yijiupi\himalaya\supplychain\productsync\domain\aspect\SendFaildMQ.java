package com.yijiupi.himalaya.supplychain.productsync.domain.aspect;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/7/9 16:21
 */
@Component
public class SendFaildMQ {

    private static final Logger LOG = LoggerFactory.getLogger(SendFaildMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.mq.sendFaild}")
    private String sendFaildEx;

    /**
     * 发送失败消息
     * 
     * @param json （方法参数）
     * @param requestType
     * @param e
     */
    public void mqSendFaild(String json, String requestType, Throwable e) {
        try {
            RequestErrorTrackingDTO dto = new RequestErrorTrackingDTO();
            dto.setRequestType(requestType);
            dto.setRequestContent(json);
            dto.setResponseContent(e.toString());
            dto.setProductLine((byte)20);

            LOG.info("{}发送失败消息:{}", sendFaildEx, JSON.toJSONString(dto));
            rabbitTemplate.convertAndSend(sendFaildEx, null, JSON.toJSONString(dto));
        } catch (Exception ex) {
            LOG.info("{}发送失败消息异常:{}", sendFaildEx, ex.toString());
        }
    }
}
