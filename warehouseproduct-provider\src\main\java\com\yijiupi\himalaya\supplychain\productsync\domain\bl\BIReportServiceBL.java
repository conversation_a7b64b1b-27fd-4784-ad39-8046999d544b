package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.nio.charset.Charset;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yijiupi.himalaya.supplychain.baseutil.HttpClientUtils;
import com.yijiupi.himalaya.supplychain.productsync.config.AuthUserConfig;
import com.yijiupi.himalaya.supplychain.productsync.dto.bireport.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ResponseResult;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.TokenRequestDTO;
import com.yijiupi.himalaya.supplychain.productsync.model.dto.TokenDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.DateFormatUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;

/**
 * BI报表获取
 */
@Service
public class BIReportServiceBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(BIReportServiceBL.class);

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Value("${bi.token.url}")
    private String toKenUrl;

    @Value("${bi.base.url}")
    private String baseUrl;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private AuthUserConfig authUserConfig;

    private static final String TOKEN_KEY = "supp:productsycn:bi:Token:";

    private final Integer pageSize = 200;
    /**
     * 获取仓库逾期监控
     */
    private static final String REQUEST_WAREHOUSEOVERDUEMONITOR =
        "/one-vue/prod-api/interface/api/bi/open/scm/warehouseOverdueMonitor";
    /**
     * 获取订单积压报表
     */
    private static final String REQUEST_OVERSTOCKDETAIL = "/one-vue/prod-api/interface/api/bi/open/scm/overStockDetail";

    /**
     * 获取产品临期过期报表（带二级货主）
     */
    private static final String REQUEST_SHELFLIFEDATA =
        "/one-vue/prod-api/interface/api/bi/open/scm/shelflifeDataWithSecondOwener";

    private Integer queryPageSize = 10000;

    private static final String SUCCESS_CODE = "200";

    /**
     * 获取仓库逾期监控数据
     */
    public WarehouseOverdueMonitorResultDTO
        getWarehouseOverdueMonitorReport(WarehouseOverdueMonitorRequestDTO request) {
        WarehouseOverdueMonitorResultDTO resutlDTO = new WarehouseOverdueMonitorResultDTO();

        List<Warehouse> warehouseList = warehouseQueryService.listWarehouseByIds(request.getWarehouseId());
        if (CollectionUtils.isEmpty(warehouseList)) {
            return resutlDTO;
        }

        String token = getToken();
        request.setType(warehouseList.get(0).getWarehouseClass().compareTo(0) == 0 ? 1 : 0);
        String result;
        try {
            LOGGER.info("获取仓库逾期监控数据 request={}", JSON.toJSONString(request));
            result = postTokenJson(baseUrl + REQUEST_WAREHOUSEOVERDUEMONITOR, "Bearer " + token, request);
            LOGGER.info("获取仓库逾期监控数据 result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            LOGGER.error("获取仓库逾期监控异常", e);
            return resutlDTO;
        }
        if (StringUtils.isBlank(result)) {
            LOGGER.warn("未查询到仓库逾期监控数据");
            return resutlDTO;
        }
        BIReportBaseDTO biReportBaseDTO = JSONObject.parseObject(result, BIReportBaseDTO.class);
        LOGGER.info("获取仓库逾期监控数据 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
        if (!SUCCESS_CODE.equals(biReportBaseDTO.getCode())) {
            LOGGER.warn("获取仓库逾期监控数据请求失败 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
            return resutlDTO;
        }

        Object data = biReportBaseDTO.getData();
        if (ObjectUtils.isEmpty(data)) {
            LOGGER.warn("未查询到仓库逾期监控数结果 data={}", JSON.toJSONString(data));
            return resutlDTO;
        }

        resutlDTO = JSONObject.parseObject(String.valueOf(data), WarehouseOverdueMonitorResultDTO.class);
        LOGGER.info("获取仓库逾期监控数据 resutlDTO={}", JSON.toJSONString(resutlDTO));
        return resutlDTO;
    }

    /**
     * 获取订单积压报表数据
     */
    public OverStockDetailResultDTO getOverStockDetailReport(OverStockDetailRequestDTO request) {
        OverStockDetailResultDTO resutlDTO = new OverStockDetailResultDTO();

        String token = getToken();
        String result = StringUtils.EMPTY;
        try {
            LOGGER.info("获取订单积压报表数据 request={}", JSON.toJSONString(request));
            result = postTokenJson(baseUrl + REQUEST_OVERSTOCKDETAIL, "Bearer " + token, request);
            LOGGER.info("获取订单积压报表数据 result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            LOGGER.error("获取订单积压报表异常", e);
            return resutlDTO;
        }
        if (StringUtils.isBlank(result)) {
            LOGGER.warn("未查询到订单积压报表");
            return resutlDTO;
        }
        BIReportBaseDTO biReportBaseDTO = JSONObject.parseObject(result, BIReportBaseDTO.class);
        LOGGER.info("获取订单积压报表数据 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
        if (!SUCCESS_CODE.equals(biReportBaseDTO.getCode())) {
            LOGGER.warn("获取订单积压报表数据请求失败 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
            return resutlDTO;
        }

        Object data = biReportBaseDTO.getData();
        if (ObjectUtils.isEmpty(data)) {
            LOGGER.warn("未查询到订单积压报表数据结果 data={}", JSON.toJSONString(data));
            return resutlDTO;
        }

        resutlDTO = JSONObject.parseObject(String.valueOf(data), OverStockDetailResultDTO.class);
        LOGGER.info("获取订单积压报表数据 resutlDTO={}", JSON.toJSONString(resutlDTO));
        return resutlDTO;
    }

    /**
     * 获取产品临期过期报表数据
     */
    public ShelflifeDataResultDTO getShelflifeDataReport(ShelflifeDataRequestDTO request) {
        ShelflifeDataResultDTO resutlDTO = new ShelflifeDataResultDTO();

        String token = getToken();
        String result = StringUtils.EMPTY;
        try {
            LOGGER.info("获取产品临期过期报表数据 request={}", JSON.toJSONString(request));
            result = postTokenJson(baseUrl + REQUEST_SHELFLIFEDATA, "Bearer " + token, request);
            LOGGER.info("获取产品临期过期报表数据 result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            LOGGER.error("获取产品临期过期报表异常", e);
            return resutlDTO;
        }
        if (StringUtils.isBlank(result)) {
            LOGGER.warn("未查询到产品临期过期报表数据");
            return resutlDTO;
        }
        BIReportBaseDTO biReportBaseDTO = JSONObject.parseObject(result, BIReportBaseDTO.class);
        LOGGER.info("获取产品临期过期报表数据 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
        if (!SUCCESS_CODE.equals(biReportBaseDTO.getCode())) {
            LOGGER.warn("获取产品临期过期报表数据请求失败 biReportBaseDTO={}", JSON.toJSONString(biReportBaseDTO));
            return resutlDTO;
        }

        Object data = biReportBaseDTO.getData();
        if (ObjectUtils.isEmpty(data)) {
            LOGGER.warn("未查询到产品临期过期报表结果 data={}", JSON.toJSONString(data));
            return resutlDTO;
        }

        resutlDTO = JSONObject.parseObject(String.valueOf(data), ShelflifeDataResultDTO.class);
        if (null != resutlDTO) {
            List<ShelflifeDataDTO> dataList = resutlDTO.getData();
            if (!CollectionUtils.isEmpty(dataList)) {
                for (ShelflifeDataDTO datum : dataList) {
                    if (!StringUtils.isBlank(datum.getFirstbatchproductiondate())) {
                        String productiondate =
                            DateFormatUtil.formatDate(DateFormatUtil.genDateFromStr(datum.getFirstbatchproductiondate(),
                                DateFormatUtil.FORMAT_TIMESTAMP_TWO), DateFormatUtil.FORMAT_DATE);
                        datum.setFirstbatchproductiondate(productiondate);
                    }
                    if (!StringUtils.isBlank(datum.getFirstbatchexpiretime())) {
                        String expiretime =
                            DateFormatUtil.formatDate(DateFormatUtil.genDateFromStr(datum.getFirstbatchexpiretime(),
                                DateFormatUtil.FORMAT_TIMESTAMP_TWO), DateFormatUtil.FORMAT_DATE);
                        datum.setFirstbatchexpiretime(expiretime);
                    }
                }
            }
        }
        LOGGER.info("获取产品临期过期报表数据 resutlDTO={}", JSON.toJSONString(resutlDTO));

        return resutlDTO;
    }

    public String getToken() {
        String token = redisTemplate.opsForValue().get(TOKEN_KEY);
        // 如果为空 则重新请求
        if (StringUtils.isBlank(token)) {
            TokenRequestDTO tokenRequestDTO = new TokenRequestDTO();
            BeanUtils.copyProperties(authUserConfig, tokenRequestDTO);
            String resultJson = null;
            try {
                resultJson = HttpClientUtils.postJson(toKenUrl, tokenRequestDTO);
            } catch (Exception e) {
                LOGGER.error("获取token异常", e);
            }
            if (StringUtils.isNotBlank(resultJson)) {
                LOGGER.info("获取token resultJson={}", JSON.toJSONString(resultJson));
                ResponseResult responseResult = JSONObject.parseObject(resultJson.toLowerCase(), ResponseResult.class);
                if (responseResult.getCode().compareTo(Integer.valueOf(SUCCESS_CODE)) == 0) {
                    Object data = responseResult.getData();
                    TokenDTO tokenDTO = JSONObject.parseObject(String.valueOf(data), TokenDTO.class);
                    token = tokenDTO.getOne_service_token();
                    redisTemplate.opsForValue().set(TOKEN_KEY, token, 1, TimeUnit.HOURS);
                }
            }
        }
        return token;
    }

    public String postTokenJson(String url, String token, Object obj) throws Exception {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        ObjectMapper objectMapper = new ObjectMapper();
        httpPost.addHeader("content-type", "application/json;charset=utf-8");
        httpPost.addHeader("accept", "application/json;charset=utf-8");
        httpPost.addHeader("Authorization", token);
        httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(obj), Charset.forName("utf-8")));
        CloseableHttpResponse resposnse = httpClient.execute(httpPost);
        HttpEntity entity = resposnse.getEntity();
        return EntityUtils.toString(entity);
    }

}
