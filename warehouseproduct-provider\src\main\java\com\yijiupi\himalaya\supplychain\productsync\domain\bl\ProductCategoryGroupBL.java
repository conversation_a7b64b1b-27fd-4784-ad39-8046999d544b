package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryGroupConverter;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductInfoCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryGroupMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryRelatedMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoCategoryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupUpdateParam;
import com.yijiupi.himalaya.supplychain.productsync.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductCategoryGroupBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCategoryGroupBL.class);

    @Autowired
    private ProductCategoryGroupMapper productCategoryGroupMapper;

    @Autowired
    private ProductCategoryRelatedMapper productCategoryRelatedMapper;

    @Autowired
    private ProductInfoCategoryMapper productInfoCategoryMapper;

    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    /**
     * 条件查询类目树
     */
    public List<ProductCategoryGroupDTO> findProductCategoryGroupTree(ProductCategoryGroupQueryDTO dto) {
        if (dto.getCategoryGroupId() == null && dto.getOrgId() != null) {
            dto.setCategoryGroupId(productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(dto.getOrgId(), null));
        }
        List<ProductCategoryGroupPO> productCategoryGroupPOS = productCategoryGroupMapper.findProductCategoryGroupTree(dto);
        return ProductCategoryGroupConverter.toDTO(productCategoryGroupPOS);
    }

    /**
     * 类目同步(树状结构) 整体更新类目
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncCategoryTree(List<CategorySync> categoryTrees) {
        try {
            batchSyncCategoryTree(categoryTrees);
        } catch (Exception e) {
            LOG.error("类目同步失败:{}", JSON.toJSONString(categoryTrees), e);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchSyncCategoryTree(List<CategorySync> categoryTrees) {
        if (CollectionUtils.isEmpty(categoryTrees)) {
            return;
        }
        LOG.info("类目树同步数据:{}", JSON.toJSONString(categoryTrees));

        // 获取分组id
        Long categoryGroupId = categoryTrees.get(0).getCategoryGroupId();
        if (categoryGroupId == null) {
            categoryGroupId =
                productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(categoryTrees.get(0).getOrgId(), null);
        }

        // 组装wms数据
        buildCategoryBranch(categoryTrees, categoryGroupId);

        // 转换对象
        List<ProductCategoryGroupPO> productCategoryGroupPOS = new ArrayList<>();
        ProductCategoryGroupConverter.syncCategoryTree2ProductCategoryGroupPOS(categoryTrees, productCategoryGroupPOS,
            categoryGroupId);
        productCategoryGroupPOS = productCategoryGroupPOS.stream()
            .filter(StreamUtils.distinctByKey(ProductCategoryGroupPO::getId)).collect(Collectors.toList());

        // 过滤已存在的类目数据
        productCategoryGroupPOS = filterExistProductCategoryGroups(productCategoryGroupPOS);
        if (CollectionUtils.isNotEmpty(productCategoryGroupPOS)) {
            LOG.info("转换后的类目数据:{}", JSON.toJSONString(productCategoryGroupPOS));

            productCategoryGroupMapper.insertOrUpdateBatch(productCategoryGroupPOS);
        }

        // 更新产品与类目关系表
        List<CategorySync> productInfoCategoryTrees = categoryTrees.stream()
            .filter(categoryTree -> categoryTree.getProductInfoId() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productInfoCategoryTrees)) {
            // 转换对象
            List<ProductInfoCategoryPO> productInfoCategoryPOS = ProductInfoCategoryConvertor
                .categoryTrees2ProductInfoCategoryPOS(productInfoCategoryTrees, categoryGroupId);
            productInfoCategoryPOS = productInfoCategoryPOS.stream()
                .filter(StreamUtils.distinctByKey(ProductInfoCategoryPO::getProductInfoId))
                .collect(Collectors.toList());

            // 组装产品与类目关系数据
            buildProductInfoCategories(productInfoCategoryPOS, categoryGroupId);

            // 过滤已存在的产品与类目关系数据
            productInfoCategoryPOS = filterExistProductInfoCategories(productInfoCategoryPOS);
            if (CollectionUtils.isNotEmpty(productInfoCategoryPOS)) {
                LOG.info("组装后的产品与类目关系数据:{}", JSON.toJSONString(productInfoCategoryPOS));

                productInfoCategoryMapper.insertOrUpdateBatch(productInfoCategoryPOS);
            }
        } else if (CollectionUtils.isNotEmpty(productCategoryGroupPOS)) {
            // 如果只是类目同步，则将所有关联过类目id的数据全部更新一遍
            productInfoCategoryMapper.batchUpdateCategoryName(productCategoryGroupPOS, categoryGroupId);
        }
    }

    /**
     * 类目树树枝组装 递归 -> 查找是否存在 -> 存在 -> wmsCategoryId使用存在的id -> 不存在 -> wmsCategoryId使用新Id -> 更新传递对象
     */
    private void buildCategoryBranch(List<CategorySync> categoryTrees, Long categoryGroupId) {
        if (CollectionUtils.isEmpty(categoryTrees)) {
            return;
        }

        List<String> categoryIds =
            categoryTrees.stream().map(CategorySync::getCategoryId).distinct().collect(Collectors.toList());

        // 查询产品类目表
        List<ProductCategoryGroupPO> categoryGroupPOS =
            productCategoryGroupMapper.listByRefCategoryIds(categoryIds, categoryGroupId);
        Map<String, List<ProductCategoryGroupPO>> categoryGroupMap =
            categoryGroupPOS.stream().collect(Collectors.groupingBy(ProductCategoryGroupPO::getRefCategoryId));

        // 查询关联类目配置表
        List<ProductCategoryRelatedPO> productCategoryRelatedPOS =
            productCategoryRelatedMapper.listByRefCategoryIds(categoryIds, categoryGroupId);
        Map<String, List<ProductCategoryRelatedPO>> productCategoryRelatedMap = productCategoryRelatedPOS.stream()
            .collect(Collectors.groupingBy(ProductCategoryRelatedPO::getRefCategoryId));

        categoryTrees.forEach(tree -> {
            String relatedCategoryId = tree.getCategoryId();

            // 获取wms系统类目id
            Long categoryId = getWmsCategoryId(relatedCategoryId, categoryGroupMap, productCategoryRelatedMap);

            tree.setWmsCategoryId(categoryId);
            tree.setWmsCategoryName(tree.getCategoryName());

            // 给叶子节点设置其父类目信息
            if (CollectionUtils.isNotEmpty(tree.getLeafCategories())) {
                tree.getLeafCategories().forEach(leaf -> leaf.setWmsParentCategoryId(categoryId));
            }

        });

        List<CategorySync> leafCategories =
            categoryTrees.stream().filter(tree -> CollectionUtils.isNotEmpty(tree.getLeafCategories()))
                .flatMap(tree -> tree.getLeafCategories().stream()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leafCategories)) {
            buildCategoryBranch(leafCategories, categoryGroupId);
        }
    }

    /**
     * 产品类目信息关系数据组装
     */
    private void buildProductInfoCategories(List<ProductInfoCategoryPO> productInfoCategoryPOS, Long categoryGroupId) {
        if (categoryGroupId == null || CollectionUtils.isEmpty(productInfoCategoryPOS)) {
            return;
        }

        // 查找产品与类目关系表，将已存在的数据更新掉入参
        List<Long> productInfoIds = productInfoCategoryPOS.stream().map(ProductInfoCategoryPO::getProductInfoId)
            .distinct().collect(Collectors.toList());
        List<ProductInfoCategoryPO> existProductInfoCategoryPOS =
            productInfoCategoryMapper.listByProductInfoIds(productInfoIds, categoryGroupId);

        for (ProductInfoCategoryPO exist : existProductInfoCategoryPOS) {
            productInfoCategoryPOS.stream()
                .filter(infoCategory -> exist.getProductInfoId().equals(infoCategory.getProductInfoId()))
                .forEach(infoCategory -> infoCategory.setId(exist.getId()));
        }

        productInfoCategoryPOS.stream().filter(infoCategory -> infoCategory.getId() == null).forEach(
            infoCategory -> infoCategory.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO_CATEGORY)));
    }

    /**
     * 获取wms系统类目id
     */
    private Long getWmsCategoryId(String relatedCategoryId, Map<String, List<ProductCategoryGroupPO>> categoryGroupMap,
        Map<String, List<ProductCategoryRelatedPO>> productCategoryRelatedMap) {
        Long categoryId;
        List<ProductCategoryGroupPO> productCategoryGroups = categoryGroupMap.get(relatedCategoryId);
        List<ProductCategoryRelatedPO> productCategoryRelateds = productCategoryRelatedMap.get(relatedCategoryId);

        if (CollectionUtils.isNotEmpty(productCategoryGroups)) {
            categoryId = productCategoryGroups.get(0).getId();
        } else if (CollectionUtils.isNotEmpty(productCategoryRelateds)) {
            categoryId = productCategoryRelateds.get(0).getCategoryId();
        } else {
            categoryId = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CATEGORY_GROUP);

            ProductCategoryGroupPO productCategoryGroupPO = new ProductCategoryGroupPO();
            productCategoryGroupPO.setId(categoryId);

            categoryGroupMap.put(relatedCategoryId, Arrays.asList(productCategoryGroupPO));
        }
        return categoryId;
    }

    /**
     * 条件查询类目
     */
    public PageList<ProductCategoryGroupDTO> pageListProductCategoryGroup(ProductCategoryGroupQueryDTO dto) {
        return productCategoryGroupMapper.pageListProductCategoryGroup(dto, dto.getPageNum(), dto.getPageSize())
                .toPageList(ProductCategoryGroupConverter::toDTO);
    }

    /**
     * 新增类目
     */
    public ProductCategoryGroupDTO addProductCategoryGroup(ProductCategoryGroupDTO productCategoryGroupDTO) {
        // 查询类目名称相同的数据
        List<ProductCategoryGroupPO> productCategoryGroupPOS =
            productCategoryGroupMapper.findDuplicateNameCategory(Arrays.asList(productCategoryGroupDTO.getName()),
                productCategoryGroupDTO.getParentId(), productCategoryGroupDTO.getCategoryGroupId());

        if (CollectionUtils.isNotEmpty(productCategoryGroupPOS)) {
            throw new BusinessValidateException("同级类目下名称不允许相同:" + productCategoryGroupDTO.getName());
        }

        ProductCategoryGroupPO productCategoryGroupPO =
            ProductCategoryGroupConverter.productCategoryGroupDTO2ProductCategoryGroupPO(productCategoryGroupDTO);
        productCategoryGroupMapper.insertSelective(productCategoryGroupPO);
        return productCategoryGroupDTO;
    }

    /**
     * 编辑类目
     */
    public void editProductCategoryGroup(ProductCategoryGroupDTO dto) {
        ProductCategoryGroupPO productCategoryGroup = productCategoryGroupMapper.selectByPrimaryKey(dto.getId());
        if (productCategoryGroup == null) {
            throw new BusinessException("类目不存在");
        }
        ProductCategoryGroupPO productCategoryGroupPO = ProductCategoryGroupConverter.productCategoryGroupDTO2ProductCategoryGroupPO(dto);
        productCategoryGroupMapper.updateByPrimaryKeySelective(productCategoryGroupPO);
        // 如果修改了类目名称则更新类目信息关联表
        if (!productCategoryGroup.getName().equals(productCategoryGroupPO.getName())) {
            ProductInfoCategoryPO productInfoCategoryPO = new ProductInfoCategoryPO();
            productInfoCategoryPO.setCategoryGroupId(productCategoryGroup.getCategoryGroupId());
            if (productCategoryGroup.getParentId() == null) {
                productInfoCategoryPO.setStatisticsClassName(productCategoryGroupPO.getName());
                productInfoCategoryPO.setStatisticsClass(productCategoryGroup.getId());
            } else {
                productInfoCategoryPO.setSecondStatisticsClassName(productCategoryGroupPO.getName());
                productInfoCategoryPO.setSecondStatisticsClass(productCategoryGroup.getId());
            }
            productInfoCategoryMapper.updateCategoryName(productInfoCategoryPO);
        }

    }

    /**
     * 批量新增类目
     */
    public void addBatchProductCategoryGroup(List<ProductCategoryGroupDTO> productCategoryGroupDTOS) {
        // 查询类目名称相同的数据
        List<String> names =
            productCategoryGroupDTOS.stream().map(ProductCategoryGroupDTO::getName).collect(Collectors.toList());
        List<ProductCategoryGroupPO> duplicateNameCategories = productCategoryGroupMapper.findDuplicateNameCategory(
            names, productCategoryGroupDTOS.get(0).getParentId(), productCategoryGroupDTOS.get(0).getCategoryGroupId());

        // 过滤掉重名数据
        List<String> duplicateNames =
            duplicateNameCategories.stream().map(ProductCategoryGroupPO::getName).collect(Collectors.toList());
        productCategoryGroupDTOS = productCategoryGroupDTOS.stream()
            .filter(category -> !duplicateNames.contains(category.getName())).collect(Collectors.toList());

        List<ProductCategoryGroupPO> productCategoryGroupPOS =
            ProductCategoryGroupConverter.productCategoryGroupDTOS2ProductCategoryGroupPOS(productCategoryGroupDTOS);
        productCategoryGroupMapper.insertBatch(productCategoryGroupPOS);
    }

    /**
     * 批量删除类目
     */
    public void deleteBatchProductCategoryGroup(List<Long> categoryIds) {
        List<ProductCategoryGroupPO> productCategoryGroupPOS =
            productCategoryGroupMapper.listByCategoryIds(categoryIds);

        if (CollectionUtils.isEmpty(productCategoryGroupPOS)) {
            throw new BusinessException("类目不存在");
        }

        List<Long> ids =
            productCategoryGroupPOS.stream().map(ProductCategoryGroupPO::getId).collect(Collectors.toList());
        Long categoryGroupId = productCategoryGroupPOS.get(0).getCategoryGroupId();

        List<ProductInfoCategoryPO> productInfoCategoryPOS;
        Boolean isParent = productCategoryGroupPOS.get(0).getParentId() == null;
        if (isParent) {
            // 删除父类目，需要连带删除子类目
            productInfoCategoryPOS = productInfoCategoryMapper.listByCategoryIds(ids, categoryGroupId);

            List<ProductCategoryGroupPO> allProductCategoryGroupPOS = new ArrayList<>();
            ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO = new ProductCategoryGroupQueryDTO();
            productCategoryGroupQueryDTO.setCategoryGroupId(categoryGroupId);
            productCategoryGroupQueryDTO.setParentIds(ids);
            List<ProductCategoryGroupPO> productCategoryGroupTree =
                productCategoryGroupMapper.findProductCategoryGroupTree(productCategoryGroupQueryDTO);
            ProductCategoryGroupConverter.productCategoryGroupTree2ProductCategoryGroupPOS(productCategoryGroupTree,
                allProductCategoryGroupPOS, categoryGroupId);
            ids.clear();
            ids.addAll(
                allProductCategoryGroupPOS.stream().map(ProductCategoryGroupPO::getId).collect(Collectors.toList()));
        } else {
            productInfoCategoryPOS = productInfoCategoryMapper.listByParentCategoryIds(ids, categoryGroupId);
        }

        if (CollectionUtils.isNotEmpty(productInfoCategoryPOS)) {
            Map<Long, List<ProductInfoCategoryPO>> productInfoCategoryMap =
                productInfoCategoryPOS.stream().collect(Collectors.groupingBy(ProductInfoCategoryPO::getId));

            // 根据infoId查询产品 判断其下的产品是否作废
            List<ProductSkuPO> productSkuPOS = productSkuMapper
                .findEffectiveSkuByProductInfoCategoryIds(new ArrayList<>(productInfoCategoryMap.keySet()));

            if (CollectionUtils.isNotEmpty(productSkuPOS)) {
                List<Long> effectiveProductInfoCategoryIds =
                    productSkuPOS.stream().map(ProductSkuPO::getProductInfoCategoryId).collect(Collectors.toList());
                if (isParent) {
                    String categoryNames = productInfoCategoryPOS.stream()
                        .filter(infoCategory -> effectiveProductInfoCategoryIds.contains(infoCategory.getId()))
                        .map(ProductInfoCategoryPO::getStatisticsClassName).collect(Collectors.joining(","));
                    throw new BusinessValidateException("以下类目关联的产品未作废，不能删除:" + categoryNames);
                } else {
                    String categoryNames = productInfoCategoryPOS.stream()
                        .filter(infoCategory -> effectiveProductInfoCategoryIds.contains(infoCategory.getId()))
                        .map(ProductInfoCategoryPO::getSecondStatisticsClassName).collect(Collectors.joining(","));
                    throw new BusinessValidateException("以下类目关联的产品未作废，不能删除:" + categoryNames);
                }
            }
        }

        productCategoryGroupMapper.deleteBatchByIds(ids);
    }

    /**
     * 根据类目ID获取外部类目ID
     *
     * @return
     */
    public String getRefCategoryId(Long categoryId) {
        AssertUtils.notNull(categoryId, "类目ID不能为空");
        ProductCategoryGroupPO productCategoryGroup = productCategoryGroupMapper.selectByPrimaryKey(categoryId);
        if (productCategoryGroup == null) {
            return null;
        }
        return productCategoryGroup.getRefCategoryId();
    }

    /**
     * 过滤已经存在的类目数据
     */
    private List<ProductInfoCategoryPO> filterExistProductInfoCategories(List<ProductInfoCategoryPO> productInfoCategoryPOS) {
        if (CollectionUtils.isEmpty(productInfoCategoryPOS)) {
            return productInfoCategoryPOS;
        }
        List<Long> productInfoCategoryIds =
            productInfoCategoryPOS.stream().map(ProductInfoCategoryPO::getId).distinct().collect(Collectors.toList());
        List<ProductInfoCategoryPO> existProductInfoCategoryPOS =
            productInfoCategoryMapper.findProductCategoryByIds(productInfoCategoryIds);

        if (CollectionUtils.isEmpty(existProductInfoCategoryPOS)) {
            return productInfoCategoryPOS;
        }

        List<ProductInfoCategoryPO> newProductInfoCategoryPOS = new ArrayList<>(productInfoCategoryPOS);
        existProductInfoCategoryPOS.forEach(exist -> {
            productInfoCategoryPOS.forEach(infoCategory -> {
                if (infoCategory.getProductInfoCategoryPOInfo().equals(exist.getProductInfoCategoryPOInfo())) {
                    newProductInfoCategoryPOS.remove(infoCategory);
                }
            });
        });

        return newProductInfoCategoryPOS;
    }

    /**
     * 过滤已经存在的产品类目关联数据
     */
    private List<ProductCategoryGroupPO> filterExistProductCategoryGroups(List<ProductCategoryGroupPO> productCategoryGroupPOS) {
        if (CollectionUtils.isEmpty(productCategoryGroupPOS)) {
            return productCategoryGroupPOS;
        }
        List<Long> productCategoryGroupIds =
            productCategoryGroupPOS.stream().map(ProductCategoryGroupPO::getId).distinct().collect(Collectors.toList());
        List<ProductCategoryGroupPO> existProductCategoryGroupPOS =
            productCategoryGroupMapper.listByCategoryIds(productCategoryGroupIds);

        if (CollectionUtils.isEmpty(existProductCategoryGroupPOS)) {
            return productCategoryGroupPOS;
        }

        List<ProductCategoryGroupPO> newProductCategoryGroupPOS = new ArrayList<>(productCategoryGroupPOS);
        existProductCategoryGroupPOS.forEach(exist -> {
            productCategoryGroupPOS.forEach(productCategoryGroupPO -> {
                if (productCategoryGroupPO.getProductCategoryGroupPOInfo()
                    .equals(exist.getProductCategoryGroupPOInfo())) {
                    newProductCategoryGroupPOS.remove(productCategoryGroupPO);
                }
            });
        });

        return newProductCategoryGroupPOS;
    }

    public ProductCategoryGroupPO findCategoryByName(String categoryName, Long parentId, Long categoryGroupId,
        Integer orgId) {
        LOG.info("查询类目参数：categoryName:{},parentId:{},categoryGroupId:{},orgId:{}", categoryName, parentId,
            categoryGroupId, orgId);
        if (categoryGroupId == null) {
            categoryGroupId = productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(orgId, null);
        }
        // 查询类目名称相同的数据
        List<ProductCategoryGroupPO> productCategoryGroupPOS = productCategoryGroupMapper
            .findDuplicateNameCategory(Arrays.asList(categoryName), parentId, categoryGroupId);
        if (CollectionUtils.isNotEmpty(productCategoryGroupPOS)) {
            return productCategoryGroupPOS.get(0);
        }
        return null;
    }

    /**
     * 新增类目
     */
    public ProductCategoryGroupDTO addSaasProductCategoryGroup(ProductCategoryGroupDTO productCategoryGroupDTO) {
        // 查询类目名称相同的数据
        List<ProductCategoryGroupPO> productCategoryGroupPOS =
            productCategoryGroupMapper.findDuplicateNameCategory(Arrays.asList(productCategoryGroupDTO.getName()),
                productCategoryGroupDTO.getParentId(), productCategoryGroupDTO.getCategoryGroupId());

        if (CollectionUtils.isNotEmpty(productCategoryGroupPOS)) {
            throw new BusinessValidateException("同级类目下名称不允许相同:" + productCategoryGroupDTO.getName());
        }
        ProductCategoryGroupPO productCategoryGroupPO =
            ProductCategoryGroupConverter.productCategoryGroupDTO2ProductCategoryGroupPO(productCategoryGroupDTO);
        if (StringUtils.isEmpty(productCategoryGroupPO.getRefCategoryId())) {
            productCategoryGroupPO.setRefCategoryId(productCategoryGroupPO.getId().toString());
        }
        productCategoryGroupMapper.insertSelective(productCategoryGroupPO);
        return productCategoryGroupDTO;
    }

    /**
     * 批量新增类目
     */
    public void addSaasBatchProductCategoryGroup(List<ProductCategoryGroupDTO> productCategoryGroupDTOS) {
        // 查询类目名称相同的数据
        List<String> names =
            productCategoryGroupDTOS.stream().map(ProductCategoryGroupDTO::getName).collect(Collectors.toList());
        List<ProductCategoryGroupPO> duplicateNameCategories = productCategoryGroupMapper.findDuplicateNameCategory(
            names, productCategoryGroupDTOS.get(0).getParentId(), productCategoryGroupDTOS.get(0).getCategoryGroupId());

        // 过滤掉重名数据
        List<String> duplicateNames =
            duplicateNameCategories.stream().map(ProductCategoryGroupPO::getName).collect(Collectors.toList());
        productCategoryGroupDTOS = productCategoryGroupDTOS.stream()
            .filter(category -> !duplicateNames.contains(category.getName())).collect(Collectors.toList());

        List<ProductCategoryGroupPO> productCategoryGroupPOS =
            ProductCategoryGroupConverter.productCategoryGroupDTOS2ProductCategoryGroupPOS(productCategoryGroupDTOS);
        if (CollectionUtils.isEmpty(productCategoryGroupPOS)) {
            return;
        }
        productCategoryGroupPOS.forEach(it -> {
            if (StringUtils.isEmpty(it.getRefCategoryId())) {
                it.setRefCategoryId(it.getId().toString());
            }
        });
        productCategoryGroupMapper.insertBatch(productCategoryGroupPOS);
    }

    /**
     * 是否强制录入箱码（默认true：是 false：否）
     *
     * @return
     */
    public boolean isMandatoryEntryBoxCode(Long productInfoId) {
        AssertUtils.notNull(productInfoId, "产品infoId不能为空");
        Byte isMandatoryEntryBoxCode = productCategoryGroupMapper.listMandatoryEntryBoxCode(productInfoId);
        if (Objects.equals(isMandatoryEntryBoxCode, ConditionStateEnum.否.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 通过infoId判断是否同步类目信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncCategoryByInfoId(List<CategorySync> categorySyncs) {
        LOG.info("产品信息修改同步类目:{}", JSON.toJSONString(categorySyncs));
        if (CollectionUtils.isEmpty(categorySyncs)) {
            return;
        }
        List<Long> infoIds = categorySyncs.stream().filter(sync -> sync.getProductInfoId() != null)
            .map(CategorySync::getProductInfoId).distinct().collect(Collectors.toList());
        Long categoryGroupId = categorySyncs.get(0).getCategoryGroupId();
        if (categoryGroupId == null) {
            categoryGroupId =
                productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(categorySyncs.get(0).getOrgId(), null);
            Long finalCategoryGroupId = categoryGroupId;
            categorySyncs.forEach(sync -> sync.setCategoryGroupId(finalCategoryGroupId));
        }
        if (CollectionUtils.isEmpty(infoIds)) {
            return;
        }

        List<ProductInfoCategoryPO> productInfoCategoryPOS =
            productInfoCategoryMapper.listByProductInfoIds(infoIds, categoryGroupId);
        List<CategorySync> syncList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productInfoCategoryPOS)) {
            syncList = categorySyncs;
        } else {
            Map<Long, List<ProductInfoCategoryPO>> productInfoCategoryMap =
                productInfoCategoryPOS.stream().collect(Collectors.groupingBy(ProductInfoCategoryPO::getProductInfoId));
            for (CategorySync sync : categorySyncs) {
                List<ProductInfoCategoryPO> productInfoCategoryList =
                    productInfoCategoryMap.get(sync.getProductInfoId());
                if (CollectionUtils.isNotEmpty(productInfoCategoryList)) {
                    Long parentCategoryId =
                        sync.getParentCategoryId() == null ? null : Long.valueOf(sync.getParentCategoryId());
                    Long categoryId = sync.getCategoryId() == null ? null : Long.valueOf(sync.getCategoryId());
                    if (!Objects.equals(parentCategoryId, productInfoCategoryList.get(0).getStatisticsClass())
                        || !Objects.equals(categoryId, productInfoCategoryList.get(0).getSecondStatisticsClass())) {
                        syncList.add(sync);
                    }
                } else {
                    syncList.add(sync);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(syncList)) {
            syncCategoryTree(syncList);
        }
    }

    public void updateCategoryGroupBatch(ProductCategoryGroupUpdateParam dto) {
        ProductCategoryGroupPO po = new ProductCategoryGroupPO();
        po.setGroupType(dto.getGroupType());
        po.setAttentionPeriod(dto.getAttentionPeriod());
        po.setNearExpiryPeriod(dto.getNearExpiryPeriod());
        po.setForbidSalesPeriod(dto.getForbidSalesPeriod());
        po.setUnsalablePeriod(dto.getUnsalablePeriod());
        po.setLastUpdateUser(String.valueOf(dto.getUserId()));
        if (Boolean.TRUE.equals(dto.getForceUpdateAll())) {
            productCategoryGroupMapper.updateCategoryGroupBatch(po, dto.getIds());
        } else {
            productCategoryGroupMapper.batchUpdateCategoryGroupSelective(po, dto.getIds());
        }
    }

    public List<ProductCategoryGroupDTO> listByIds(Collection<Long> ids) {
        return ProductCategoryGroupConverter.toDTO(productCategoryGroupMapper.listByCategoryIds(ids));
    }
}