package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryGroupConverter;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryGroupConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupType;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service
public class ProductCategoryGroupConfigBL {

    @Autowired
    private ProductCategoryGroupConfigMapper productCategoryGroupConfigMapper;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private IWarehouseQueryService warehouseQueryService;

    private Integer getParentOrgIdByCityId(Integer cityId) {
        AssertUtils.notNull(cityId, "城市ID不能为空！");
        // OrgDTO org = iOrgService.getOrg(cityId);
        // if (org == null) {
        // throw new BusinessValidateException("城市信息不存在！Id:" + cityId);
        // }
        // return org.getParentOrgId() == null || org.getParentOrgId() == 0 ? org.getId() : org.getParentOrgId();
        Integer parentOrgBy = iOrgService.findParentOrgBy(cityId);
        return parentOrgBy;
    }

    /**
     * 根据城市Id查找分组类目Id
     *
     * @param orgId
     * @param ownerId
     * @return
     */
    public Long getCategoryGroupIdByOrgId(Integer orgId, Long ownerId) {
        Integer parentOrgId = getParentOrgIdByCityId(orgId);
        return getCategoryGroupIdByRefId(parentOrgId, orgId, null, ownerId);
    }

    /**
     * 根据组织机构Id查找分组类目Id
     *
     * @param parentOrgId
     * @param ownerId
     * @return
     */
    public Long getCategoryGroupIdByParentOrgId(Integer parentOrgId, Long ownerId) {
        parentOrgId = getParentOrgIdByCityId(parentOrgId);
        return getCategoryGroupIdByRefId(parentOrgId, null, null, ownerId);
    }

    /**
     * 根据城市+仓库Id查找分组类目Id
     *
     * @param orgId
     * @param warehouseId
     * @param ownerId
     * @return
     */
    public Long getCategoryGroupIdByOrgIdAndWarehouseId(Integer orgId, Integer warehouseId, Long ownerId) {
        Integer parentOrgId = getParentOrgIdByCityId(orgId);
        return getCategoryGroupIdByRefId(parentOrgId, orgId, warehouseId, ownerId);
    }

    /**
     * 根据仓库Id查找分组类目Id
     *
     * @param warehouseId
     * @param ownerId
     * @return
     */
    public Long getCategoryGroupIdByWarehouseId(Integer warehouseId, Long ownerId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        AssertUtils.notNull(warehouse, "仓库信息不存在！");
        Integer orgId = warehouse.getCityId();
        Integer parentOrgId = getParentOrgIdByCityId(orgId);
        return getCategoryGroupIdByRefId(parentOrgId, orgId, warehouseId, ownerId);
    }

    /**
     * 根据仓库Id查找分组类目Id
     *
     * @param parentOrgId
     * @param orgId
     * @param warehouseId
     * @param ownerId
     * @return
     */
    public Long getCategoryGroupIdByRefId(Integer parentOrgId, Integer orgId, Integer warehouseId, Long ownerId) {
        AssertUtils.notNull(parentOrgId, "组织机构类目ID不能为空！");
        ProductCategoryGroupConfigPO productCategoryGroupConfigPO =
            productCategoryGroupConfigMapper.selectParentOrgConfigById(parentOrgId);
        if (productCategoryGroupConfigPO == null) {
            // 如果不存在配置，报错
            throw new BusinessValidateException("组织机构类目配置信息不存在，请联系管理员！OrgID:" + parentOrgId);
        }
        Byte categoryType = productCategoryGroupConfigPO.getCategoryType();
        if (categoryType == null) {
            categoryType = ProductCategoryGroupType.全局类目.getType();
        }
        Long categoryGroupId = null;
        // 1、先找到组织机构的类目分组配置
        // 如果配置的全局类目
        // 货主ID为空，结束，直接返回当前的类目分组Id就可以
        // 货主不为空，继续查货主的类目分组
        // 如果配置的指定类目Id，直接返回当前的类目分组Id就可以
        // 2、如果配的是城市或者仓库类目
        // 根据城市/仓库ID+组织机构ID+货主Id，查找对应的类目分组，返回ID
        if (Objects.equals(categoryType, ProductCategoryGroupType.全局类目.getType())) {
            // 如果有货主，继续查货主配置分组信息
            if (ownerId != null) {
                categoryGroupId = getCategoryGroupByCityOrWarehouseId(parentOrgId, null, ownerId);
            } else {
                categoryGroupId = productCategoryGroupConfigPO.getId();
            }
        }
        // 如果指定为自定义类目，结束
        else if (Objects.equals(categoryType, ProductCategoryGroupType.自定义类目.getType())) {
            categoryGroupId = productCategoryGroupConfigPO.getCategoryGroupId();
        } else {
            Integer refId = null;
            if (Objects.equals(categoryType, ProductCategoryGroupType.城市类目.getType())) {
                refId = orgId;
            } else if (Objects.equals(categoryType, ProductCategoryGroupType.仓库类目.getType())) {
                refId = warehouseId;
            }
            AssertUtils.notNull(refId, "查询城市/仓库类目，ID不能为空！");
            // 如果指定为城市类目，根据城市ID和组织结构Id加货主，查询指定的类目分组Id
            // 如果指定为仓库类目，根据仓库ID和组织结构Id加货主，查询指定的类目分组Id
            categoryGroupId = getCategoryGroupByCityOrWarehouseId(parentOrgId, refId, ownerId);
        }

        return categoryGroupId;
    }

    private Long getCategoryGroupByCityOrWarehouseId(Integer parentOrgId, Integer refId, Long ownerId) {
        ProductCategoryGroupConfigPO productCategoryGroupConfigPO =
            productCategoryGroupConfigMapper.selectByParentOrgIdAndRefIdAndOwnerId(parentOrgId, refId, ownerId);
        if (productCategoryGroupConfigPO == null) {
            // 如果不存在配置，报错
            throw new BusinessException("类目配置信息不存在，请联系管理员！");
        }
        return productCategoryGroupConfigPO.getId();
    }

    public void insertCategoryGroupConfig(ProductCategoryGroupConfigDTO dto) {
        ProductCategoryGroupConfigPO po = ProductCategoryGroupConverter.ProductCategoryGroupConfigDTO2PO(dto);
        po.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CATEGORY_GROUP_CONFIG));
        po.setCreateTime(new Date());
        po.setLastUpdateTime(new Date());
        productCategoryGroupConfigMapper.insert(po);
    }
}
