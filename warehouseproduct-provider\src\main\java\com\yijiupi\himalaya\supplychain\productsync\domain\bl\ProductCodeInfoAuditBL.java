package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCodeInfoAuditConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCodeInfoConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.*;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeOwnerTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.*;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 条码、箱码审核BL
 */

@Service
public class ProductCodeInfoAuditBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCodeInfoAuditBL.class);

    @Autowired
    private ProductCodeInfoAuditConvertor productCodeInfoAuditConvertor;

    @Autowired
    private ProductCodeInfoConvertor productCodeInfoConvertor;

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductCodeInfoAuditPOMapper productCodeInfoAuditPOMapper;

    @Autowired
    private ProductCodeInfoPOMapper productCodeInfoPOMapper;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    /**
     * 新增审核数据
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertCodeInfoAudit(ProductCodeInfoAuditHandleDTO handleDTO) {
        LOG.info("[条码/箱码]新增审核信息：{}", JSON.toJSONString(handleDTO));
        validateAuditHandleData(handleDTO);
        ProductCodeInfoAuditDTO auditDTO = productCodeInfoAuditConvertor.convertAuditHandle(handleDTO);
        String repeatMsg = getRepeatMsg(auditDTO);
        if (StringUtils.isNotBlank(repeatMsg)) {
            throw new BusinessValidateException(repeatMsg);
        }
        List<String> newFlagList = auditDTO.getCodeInfoDTOList().stream().filter(e -> e != null)
            .map(e -> String.format("%s%s",
                e.getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : e.getOwnerType(), e.getOwnerId()))
            .distinct().collect(Collectors.toList());

        // 判断产品是否首次新增code,首次新增直接审核通过
        // 网格仓不需要审核直接新增
        boolean firstAddProductCode = isFirstAddProductCode(auditDTO.getProductInfoId(), newFlagList);
        boolean notNeedAudit = !handleDTO.getNeedAudit() ? true : firstAddProductCode;
        if (notNeedAudit) {
            // 首次提交审核直接通过
            auditDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
            auditDTO.setAuditTime(new Date());
            auditDTO.setAuditUser("系统审核");
            auditDTO.setReason(StringUtils.trimToEmpty(auditDTO.getReason()) + "首次添加[条码/箱码]直接审核通过");
            auditDTO.getCodeInfoDTOList().stream().filter(e -> e != null).forEach(e -> e.setState(auditDTO.getState()));
        }
        // 新增条码、箱码信息
        Map<Long, List<ProductCodeInfoDTO>> auditCodesMap = new HashMap<>(16);
        auditCodesMap.put(handleDTO.getProductInfoId(), auditDTO.getCodeInfoDTOList());
        productCodeInfoBL.saveOrUpdateCodeInfo(auditCodesMap, false);
        // 转化为审核po
        ProductCodeInfoAuditPO auditPO = productCodeInfoAuditConvertor.convert(auditDTO);
        // 新增审核信息
        auditPO.setId(UUIDGenerator.getUUID(auditPO.getClass().getName()));
        productCodeInfoAuditPOMapper.insertSelective(auditPO);
    }

    /**
     * 是否第一次添加条码、箱码
     */
    public boolean isFirstAddProductCode(Long productInfoId, List<String> newFlagList) {
        // 查询该产品是否有条码箱码数据
        ProductCodeQueryConditionDTO queryConditionDTO = new ProductCodeQueryConditionDTO();
        queryConditionDTO.setProductInfoIdList(Lists.newArrayList(productInfoId));
        queryConditionDTO.setIsDelete(WhetherEnum.NO.getType().intValue());
        Map<Long, List<ProductCodeInfoDTO>> originDataMap =
            productCodeInfoBL.findProductCodeInfoByCondition(queryConditionDTO);
        if (originDataMap == null || originDataMap.isEmpty()) {
            return true;
        }
        List<ProductCodeInfoDTO> existsCodes = originDataMap.get(productInfoId);
        // 按 ownerType + ownerId 维度校验
        List<String> existFlagList = existsCodes.stream().filter(e -> e != null)
            .map(e -> String.format("%s%s",
                e.getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : e.getOwnerType(), e.getOwnerId()))
            .distinct().collect(Collectors.toList());
        Optional<String> existOpt = newFlagList.stream().filter(e -> e != null && existFlagList.contains(e)).findAny();
        return !existOpt.isPresent();
    }

    /**
     * 审核【条码\箱码】数据
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void auditCodeInfo(AuditProductCodeDTO auditDTO) {
        AssertUtils.notNull(auditDTO, "产品[条码/箱码]审核参数不能为空");
        AssertUtils.notNull(auditDTO.getAuditId(), "产品[条码/箱码]审核ID不能为空");
        AssertUtils.notNull(auditDTO.getAuditState(), "产品[条码/箱码]审核状态不能为空");
        AssertUtils.notNull(auditDTO.getAuditUser(), "产品[条码/箱码]审核人不能为空");
        ProductCodeInfoAuditPO auditPO = productCodeInfoAuditPOMapper.selectByPrimaryKey(auditDTO.getAuditId());
        if (auditPO == null) {
            throw new BusinessException("产品[条码/箱码]信息不存在，请重新查询后再操作！");
        }
        if (ObjectUtils.compare(ProductCodeInfoStateEnum.TO_AUDIT.getType(), auditPO.getState()) != 0) {
            throw new BusinessValidateException("产品[条码/箱码]信息不是【待审核】状态，请重新查询后再操作！");
        }

        // 組裝更新数据
        ProductCodeInfoAuditPO updateAuditPO = genUpdateAuditInfoByAuditDTO(auditDTO);
        // 更新审核表信息
        productCodeInfoAuditPOMapper.updateByPrimaryKeySelective(updateAuditPO);
        // 更新[条码\箱码表]
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setProductCodeInfoAuditIdList(Lists.newArrayList(auditDTO.getAuditId()));
        List<ProductCodeInfoPO> poList = productCodeInfoPOMapper.selectProductCodeInfoNormal(conditionDTO);
        if (CollectionUtils.isEmpty(poList)) {
            throw new BusinessException("需要审核的【条码-箱码】信息不存在！");
        }
        // 更改条码-箱码审核状态
        poList.stream().filter(e -> e != null).forEach(e -> e.setState(auditDTO.getAuditState()));
        Map<Long, List<ProductCodeInfoDTO>> auditCodesMap = new HashMap<>(16);
        auditCodesMap.put(auditPO.getProductInfoId(), productCodeInfoConvertor.reverseConvert(poList));
        productCodeInfoBL.saveOrUpdateCodeInfo(auditCodesMap, false);
    }

    /**
     * 查询审核数据
     */
    public PageList<ProductCodeInfoAuditDTO> findCodeAuditInfoByPage(ProductCodeInfoAuditQueryDTO queryDTO) {
        LOG.info("查询审核数据参数：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        if (queryDTO.getState() == null && CollectionUtils.isEmpty(queryDTO.getStateList())) {
            queryDTO.setStateList(Lists.newArrayList(ProductCodeInfoStateEnum.TO_AUDIT.getType(),
                ProductCodeInfoStateEnum.AUDIT_PASS.getType(), ProductCodeInfoStateEnum.AUDIT_REJECTION.getType()));
        }
        PageResult<ProductCodeInfoAuditDTO> pageResult = productCodeInfoAuditPOMapper.findAuditInfoByPage(queryDTO);
        if (CollectionUtils.isNotEmpty(pageResult)) {
            // 查询 code 信息
            List<Long> auditIds =
                pageResult.stream().filter(e -> e != null).map(e -> e.getId()).collect(Collectors.toList());
            List<ProductCodeInfoPO> codePOList = productCodeInfoPOMapper
                .selectProductCodeInfoNormal(new ProductCodeQueryConditionDTO(auditIds, null, null));
            List<ProductCodeInfoDTO> codeDTOList = productCodeInfoConvertor.reverseConvert(codePOList);
            if (CollectionUtils.isNotEmpty(codeDTOList)) {
                Map<Long, List<ProductCodeInfoDTO>> codeMap = codeDTOList.stream().filter(e -> e != null)
                    .collect(Collectors.groupingBy(e -> e.getProductCodeInfoAuditId()));
                for (ProductCodeInfoAuditDTO auditDTO : pageResult) {
                    if (auditDTO == null) {
                        continue;
                    }
                    auditDTO.setCodeInfoDTOList(codeMap.get(auditDTO.getId()));
                }
            }
        }
        PageList<ProductCodeInfoAuditDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(pageResult.getPager());
        dtoPageList.setDataList(pageResult.getResult());
        return dtoPageList;
    }

    /**
     * 根据code + codeType 判断 code 是否存在， 如果存在给出提示信息
     *
     * @return String : null 则不存在，不为 null 则给出提示信息
     */
    public String validateCodeIsExists(ProductCodeInfoDTO codeInfoDTO) {
        AssertUtils.notNull(codeInfoDTO, "参数信息不能为空");
        AssertUtils.isTrue(StringUtils.isNotBlank(codeInfoDTO.getCode()), "【条码/箱码】不能为空");
        AssertUtils.notNull(codeInfoDTO.getCodeType(), "编码类型不能为空");
        // 查询 code 信息
        List<ProductCodeInfoPO> codePOList = productCodeInfoPOMapper.selectProductCodeInfoNormal(
            new ProductCodeQueryConditionDTO(Lists.newArrayList(codeInfoDTO.getCode()), codeInfoDTO.getCodeType()));
        if (CollectionUtils.isEmpty(codePOList)) {
            return null;
        }
        String msg = "";
        ProductCodeTypeEnum codeTypeEnum = ProductCodeTypeEnum.getCodeTypeEnum(codeInfoDTO.getCodeType());
        Optional<ProductCodeInfoPO> infoOpt =
            codePOList.stream().filter(e -> e != null && e.getProductInfoId() != null).findAny();
        if (infoOpt.isPresent()) {
            // 找到产品则返回绑定产品信息
            ProductInfoPO infoPO = productInfoPOMapper.selectByPrimaryKey(infoOpt.get().getProductInfoId());
            if (infoPO != null) {
                msg = String.format("[%s]已经使用%s[%s]", infoPO.getProductName(),
                    codeTypeEnum == null ? "" : codeTypeEnum.getDesc(), codeInfoDTO.getCode());
            }
        } else {
            Optional<ProductCodeInfoPO> specOpt =
                codePOList.stream().filter(e -> e != null && e.getProductSpecificationId() != null).findAny();
            if (specOpt.isPresent()) {
                ProductInfoSpecificationPO specificationPO =
                    productInfoSpecificationPOMapper.selectByPrimaryKey(specOpt.get().getProductSpecificationId());
                if (specificationPO != null) {
                    msg = String.format("[%s]已经使用%s[%s]", specificationPO.getName(),
                        codeTypeEnum == null ? "" : codeTypeEnum.getDesc(), codeInfoDTO.getCode());
                }
            }
        }
        if (StringUtils.isBlank(msg)) {
            msg = String.format("%s[%s]已经被使用！", codeTypeEnum == null ? "" : codeTypeEnum.getDesc(),
                codeInfoDTO.getCode());
        }
        return msg;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateAuditBatch(List<ProductCodeInfoAuditDTO> recordList) {
        AssertUtils.notEmpty(recordList, "条码-箱码批量审核参数不能为空！");
        // 转化为审核po
        List<ProductCodeInfoAuditPO> updatePOList = productCodeInfoAuditConvertor.convert(recordList);
        // 新增审核信息
        productCodeInfoAuditPOMapper.updateAuditBatch(updatePOList);
    }

    /**
     * 校验提交数据
     *
     * @param handleDTO
     */
    private void validateAuditHandleData(ProductCodeInfoAuditHandleDTO handleDTO) {
        AssertUtils.notNull(handleDTO, "条码/箱码审核信息不能为空");
        AssertUtils.notNull(handleDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(handleDTO.getOperators(), "操作人信息不能为空");
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(handleDTO.getAuditBarItems())
            || CollectionUtils.isNotEmpty(handleDTO.getAuditBoxItems()), "条码/箱码审核详情信息不能为空");
        if (handleDTO.getProductInfoId() == null || handleDTO.getProductSpecificationId() == null) {
            AssertUtils.notNull(handleDTO.getProductSkuId(), "产品信息不能为空");
            ProductSkuPO productSkuPO =
                productSkuMapper.selectByCityIdAndProductSkuId(handleDTO.getCityId(), handleDTO.getProductSkuId());
            AssertUtils.notNull(productSkuPO, "未找到产品SKU信息");
            handleDTO.setProductInfoId(
                handleDTO.getProductInfoId() == null ? productSkuPO.getProductInfoId() : handleDTO.getProductInfoId());
            handleDTO.setProductSpecificationId(handleDTO.getProductSpecificationId() == null
                ? productSkuPO.getProductSpecificationId() : handleDTO.getProductSpecificationId());
        }
        AssertUtils.notNull(handleDTO.getProductInfoId(), "产品信息ID不能为空");
        AssertUtils.notNull(handleDTO.getProductSpecificationId(), "产品规格ID不能为空");
        // 条码
        List<ProductCodeInfoAuditHandleItemDTO> auditBarItems = handleDTO.getAuditBarItems();
        // 箱码
        List<ProductCodeInfoAuditHandleItemDTO> auditBoxItems = handleDTO.getAuditBoxItems();
        // 条码 + 箱码
        List<ProductCodeInfoAuditHandleItemDTO> allCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(auditBarItems)) {
            allCodes.addAll(auditBarItems);
        }
        if (CollectionUtils.isNotEmpty(auditBoxItems)) {
            allCodes.addAll(auditBoxItems);
        }
        allCodes.stream().filter(e -> e != null).forEach(e -> {
            if (StringUtils.isBlank(e.getCode())) {
                // 条码/箱码为空，其图片必须不能为空
                AssertUtils.isTrue(StringUtils.isNotBlank(e.getCodeImgId()), "[条码/箱码]确认无码时图片信息不能为空！");
            }
        });
    }

    private String getRepeatMsg(ProductCodeInfoAuditDTO auditDTO) {

        List<ProductCodeInfoDTO> codeInfoDTOList = auditDTO.getCodeInfoDTOList();
        // 过滤出有[条码\箱码]集合并且去重
        List<ProductCodeInfoDTO> hasCodeList =
            codeInfoDTOList.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getCode()))
                .collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getHasCodeIdentity()))),
                    ArrayList::new));
        // 过滤出无[条码\箱码]集合并且去重
        List<ProductCodeInfoDTO> noCodeList =
            codeInfoDTOList.stream().filter(e -> e != null && StringUtils.isBlank(e.getCode()))
                .collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getNoCodeIdentity()))),
                    ArrayList::new));
        // 重新设置去重后的数据集合
        List<ProductCodeInfoDTO> newCodesList = new ArrayList<>();
        newCodesList.addAll(hasCodeList);
        newCodesList.addAll(noCodeList);
        auditDTO.setCodeInfoDTOList(newCodesList);
        if (CollectionUtils.isEmpty(hasCodeList)) {
            return null;
        }
        // 新提交的[条码\箱码]
        List<String> newCodes =
            hasCodeList.stream().filter(e -> e != null).map(e -> e.getCode()).distinct().collect(Collectors.toList());
        // 查询出已经存在的信息
        ProductCodeQueryConditionDTO queryConditionDTO = new ProductCodeQueryConditionDTO();
        queryConditionDTO.setCodes(newCodes);
        List<ProductCodeInfoDTO> originCodeInfoList = productCodeInfoBL.findCodesByCommonCondition(queryConditionDTO);
        if (CollectionUtils.isEmpty(originCodeInfoList)) {
            // 没有原来数据直接返回
            return null;
        }
        // 拒绝后的code允许再次添加审核
        originCodeInfoList
            .removeIf(e -> e != null && Objects.equals(e.getState(), ProductCodeInfoStateEnum.AUDIT_REJECTION));
        // 新增审核数据
        Map<String, ProductCodeInfoDTO> newCodesMap =
            hasCodeList.stream().collect(Collectors.toMap(e -> e.getHasCodeIdentity(), Function.identity()));
        // 已存在有码集合
        Map<String, ProductCodeInfoDTO> originHasCodesMap = productCodeInfoBL.getHasCodesMap(originCodeInfoList);
        // 重复code数据
        List<ProductCodeInfoDTO> repeatInfoList = new ArrayList<>();
        // 遍历集合查找重复数据
        for (Map.Entry<String, ProductCodeInfoDTO> entry : newCodesMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            ProductCodeInfoDTO oldCodeDTO = originHasCodesMap.get(entry.getKey());
            if (oldCodeDTO == null) {
                continue;
            }
            ProductCodeInfoDTO newCodeDTO = entry.getValue();
            if (newCodeDTO == null) {
                continue;
            }
            // 所属类别及类别ID完全相同则记录
            if (!Objects.equals(newCodeDTO.getOwnerId(), oldCodeDTO.getOwnerId()) || !Objects.equals(
                ObjectUtils.defaultIfNull(newCodeDTO.getOwnerType(), ProductCodeOwnerTypeEnum.HQ.getType()),
                ObjectUtils.defaultIfNull(oldCodeDTO.getOwnerType(), ProductCodeOwnerTypeEnum.HQ.getType()))) {
                continue;
            }
            repeatInfoList.add(entry.getValue());
        }
        if (repeatInfoList.isEmpty()) {
            return null;
        }
        // 组装重复提示消息
        return repeatInfoList.stream().filter(e -> e != null)
            .map(e -> String.format("%s[%s]%s",
                ProductCodeTypeEnum.getCodeTypeEnum(e.getCodeType()) == null ? ""
                    : ProductCodeTypeEnum.getCodeTypeEnum(e.getCodeType()).getDesc(),
                e.getCode(), ProductCodeInfoStateEnum.getStateEnum(e.getState()) == null ? ""
                    : ProductCodeInfoStateEnum.getStateEnum(e.getState()).getDesc())

            ).distinct().collect(Collectors.joining(",")) + "已经存在，不能重复添加！";
    }

    private ProductCodeInfoAuditPO genUpdateAuditInfoByAuditDTO(AuditProductCodeDTO auditDTO) {
        // 更新审核表数据
        ProductCodeInfoAuditPO updateAuditPO = new ProductCodeInfoAuditPO();
        updateAuditPO.setId(auditDTO.getAuditId());
        updateAuditPO.setState(auditDTO.getAuditState());
        updateAuditPO.setAuditUser(auditDTO.getAuditUser());
        updateAuditPO.setAuditTime(new Date());
        updateAuditPO.setReason(auditDTO.getReason());
        return updateAuditPO;
    }
}
