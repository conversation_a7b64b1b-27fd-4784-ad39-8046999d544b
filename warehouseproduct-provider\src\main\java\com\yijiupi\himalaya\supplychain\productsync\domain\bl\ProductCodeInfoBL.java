package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.dubbop.adapter.IProductInfoSpecificationQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.Code;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductBoxCode;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecification;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCodeInfoConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductInfoConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncCodeInfoConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCodeInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeQueryTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.*;
import com.yijiupi.himalaya.supplychain.productsync.util.UuidUtil;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

@Service
public class ProductCodeInfoBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCodeInfoBL.class);

    // list 拆分大小
    public static final int PARTITIONS_SIZE = 100;

    @Autowired
    private ProductCodeInfoAuditBL productCodeInfoAuditBL;

    @Autowired
    private ProductCodeInfoConvertor productCodeInfoConvertor;

    @Autowired
    private ProductSyncCodeInfoConvertor productSyncCodeInfoConvertor;

    @Autowired
    private ProductInfoConvertor productInfoConvertor;

    @Autowired
    private ProductCodeInfoPOMapper productCodeInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductInfoSpecificationQueryService iProductInfoSpecificationQueryService;

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchInsert(List<ProductCodeInfoDTO> codeInfoDTOList) {
        LOG.info("新增[条码/箱码]信息：{}", JSON.toJSONString(codeInfoDTOList));
        AssertUtils.notEmpty(codeInfoDTOList, "新增产品条码/箱码不能为空");
        validateBaseCodeInfo(codeInfoDTOList);
        List<ProductCodeInfoDTO> newInsertList = removeRepeatCodeInfo(codeInfoDTOList);
        List<ProductCodeInfoPO> poList = productCodeInfoConvertor.convert(newInsertList);
        poList.forEach(p -> p.setId(UUIDGenerator.getUUID(p.getClass().getName())));
        productCodeInfoPOMapper.insertList(poList);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdate(List<ProductCodeInfoDTO> codeInfoDTOList) {
        LOG.info("修改[条码/箱码]信息：{}", JSON.toJSONString(codeInfoDTOList));
        AssertUtils.notEmpty(codeInfoDTOList, "修改产品条码/箱码不能为空");
        validateBaseCodeInfo(codeInfoDTOList);
        List<ProductCodeInfoPO> poList = productCodeInfoConvertor.convert(codeInfoDTOList);
        productCodeInfoPOMapper.updateForEditList(poList);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchDeleteByIdList(ProductCodeDeleteConditionDTO deleteConditionDTO) {
        LOG.info("删除[条码/箱码]ID集合：{}", JSON.toJSONString(deleteConditionDTO));
        AssertUtils.notNull(deleteConditionDTO, "删除[条码/箱码]信息不能为空");
        AssertUtils.notEmpty(deleteConditionDTO.getIdList(), "删除[条码/箱码]ID不能为空");
        AssertUtils.notNull(deleteConditionDTO.getIsDelete(), "[条码/箱码]删除状态不能为空");
        productCodeInfoPOMapper.updateIsDeleteByIds(deleteConditionDTO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void syncProductCodeInfo(List<SyncProductCodeInfoDTO> syncDTOList) {
        LOG.info("syncProductCodeInfo - 同步产品条码/箱码数据：{}", JSON.toJSONString(syncDTOList));
        // 校验同步信息
        validateSyncCodeInfo(syncDTOList);
        // 转化成[条码/箱码]DTO
        Map<Long, List<ProductCodeInfoDTO>> syncDataMap =
            productSyncCodeInfoConvertor.convertToProductCodeInfoDTO(syncDTOList);
        saveOrUpdateCodeInfo(syncDataMap, true);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateCodeInfo(Map<Long, List<ProductCodeInfoDTO>> codeInfoMap, Boolean needUpdateAuditInfo) {
        LOG.info("saveOrUpdateCodeInfo - 保存或更新条码/箱码参数：{}", JSON.toJSONString(codeInfoMap));
        if (codeInfoMap == null || codeInfoMap.isEmpty()) {
            throw new DataValidateException("[条码-箱码]保存或者修改信息不能为空！");
        }
        // 是否需要更新条码-箱码审核数据：code 同步时需要更新审核数据
        if (needUpdateAuditInfo == null) {
            needUpdateAuditInfo = false;
        }
        List<Long> productInfoIdList = Lists.newArrayList(codeInfoMap.keySet());
        // 查询数据库中已有条码编码
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setProductInfoIdList(productInfoIdList);
        conditionDTO.setIsDelete(WhetherEnum.NO.getType().intValue());
        Map<Long, List<ProductCodeInfoDTO>> oldCodeInfoMap = findProductCodeInfoByCondition(conditionDTO);
        List<ProductCodeInfoDTO> insertList = new ArrayList<>();
        List<ProductCodeInfoDTO> updateList = new ArrayList<>();
        List<Long> delBoxIdList = new ArrayList<>();
        for (Long productInfoId : productInfoIdList) {
            if (productInfoId == null) {
                continue;
            }
            // 旧 code 集合
            List<ProductCodeInfoDTO> oldCodes = oldCodeInfoMap.get(productInfoId);
            // 旧[审核通过]箱码集合
            List<ProductCodeInfoDTO> oldPassBoxCodes = null;
            if (CollectionUtils.isNotEmpty(oldCodes)) {
                // 不为空，则过滤掉新建的条码、箱码
                oldCodes = oldCodes.stream().filter(
                    e -> e != null && ObjectUtils.compare(e.getState(), ProductCodeInfoStateEnum.INIT.getType()) != 0)
                    .collect(Collectors.toList());
                oldPassBoxCodes = oldCodes.stream()
                    .filter(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType())
                        && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState()))
                    .collect(Collectors.toList());
            }

            // 新 code 集合
            List<ProductCodeInfoDTO> newCodes = codeInfoMap.get(productInfoId);
            Pair<List<ProductCodeInfoDTO>, List<ProductCodeInfoDTO>> changeInfoPair =
                genChangeCodeInfo(newCodes, oldCodes);
            List<ProductCodeInfoDTO> insertCodes = changeInfoPair.getLeft();
            List<ProductCodeInfoDTO> updateCodes = changeInfoPair.getRight();

            // 处理箱码
            List<Long> delBoxCodeIds = processInsertOrUpdateBoxCode(insertCodes, updateCodes, oldPassBoxCodes);

            // 记录需要删除的箱码
            delBoxIdList.addAll(delBoxCodeIds);
            insertList.addAll(insertCodes);
            updateList.addAll(updateCodes);
        }
        if (CollectionUtils.isNotEmpty(delBoxIdList)) {
            ProductCodeDeleteConditionDTO deleteConditionDTO = new ProductCodeDeleteConditionDTO();
            deleteConditionDTO.setIdList(delBoxIdList);
            deleteConditionDTO.setIsDelete(WhetherEnum.YES.getType());
            batchDeleteByIdList(deleteConditionDTO);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            if (needUpdateAuditInfo) {
                updateAuditBatch(updateList);
            }
            batchUpdate(updateList);
        }
    }

    private void updateAuditBatch(List<ProductCodeInfoDTO> updateList) {
        List<ProductCodeInfoDTO> hasAuditList = updateList.stream()
            .filter(e -> e != null && e.getProductCodeInfoAuditId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasAuditList)) {
            return;
        }
        Map<Long, ProductCodeInfoDTO> auditMap = hasAuditList.stream().filter(e -> e != null).collect(Collectors
            .toMap(e -> e.getProductCodeInfoAuditId(), Function.identity(), (v1, v2) -> v1 != null ? v1 : v2));
        List<ProductCodeInfoAuditDTO> result = new ArrayList<>();
        for (Map.Entry<Long, ProductCodeInfoDTO> entry : auditMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            ProductCodeInfoDTO code = entry.getValue();
            ProductCodeInfoAuditDTO auditDTO = new ProductCodeInfoAuditDTO();
            auditDTO.setId(code.getProductCodeInfoAuditId());
            auditDTO.setState(code.getState());
            auditDTO.setRemark("同步更新");
            auditDTO.setAuditUser(code.getLastUpdateUser());
            auditDTO.setLastUpdateUser(code.getLastUpdateUser());
            result.add(auditDTO);
        }
        // 更新审核信息
        productCodeInfoAuditBL.updateAuditBatch(result);
    }

    private List<Long> processInsertOrUpdateBoxCode(List<ProductCodeInfoDTO> insertCodes,
        List<ProductCodeInfoDTO> updateCodes, List<ProductCodeInfoDTO> oldPassBoxCodes) {
        if (insertCodes == null) {
            insertCodes = Collections.EMPTY_LIST;
        }
        if (updateCodes == null) {
            updateCodes = Collections.EMPTY_LIST;
        }
        // 箱码：每个区域或者总部[ownerType + ownerId]同一个规格只允许有一个箱码生效
        // 新增[审核通过]箱码信息
        List<
            ProductCodeInfoDTO> insertPassBoxCodes =
                insertCodes.stream()
                    .filter(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType())
                        && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState()))
                    .collect(Collectors.toList());
        List<String> insertBoxOwners = insertPassBoxCodes.stream().filter(e -> e != null)
            .map(e -> e.getBoxOwnerIdentification()).collect(Collectors.toList());
        // 移除更新与新增冲突的箱码审核通过数据，保留新增[审核通过]的箱码
        updateCodes.removeIf(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType())
            && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState())
            && insertBoxOwners.contains(e.getBoxOwnerIdentification()));
        if (CollectionUtils.isEmpty(oldPassBoxCodes)) {
            // 没有旧箱码直接返回
            return Collections.EMPTY_LIST;
        }
        // 修改[审核通过]箱码信息
        List<
            ProductCodeInfoDTO> updatePassBoxCodes =
                updateCodes.stream()
                    .filter(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType())
                        && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState()))
                    .collect(Collectors.toList());
        List<Long> updateBoxIds =
            updatePassBoxCodes.stream().filter(e -> e != null).map(e -> e.getId()).collect(Collectors.toList());
        List<String> updateBoxOwners = updatePassBoxCodes.stream().filter(e -> e != null)
            .map(e -> e.getBoxOwnerIdentification()).collect(Collectors.toList());

        // 将旧箱码[审核通过]的数据删除
        List<Long> insertNeedDelBoxIds = oldPassBoxCodes.stream()
            .filter(e -> e != null && insertBoxOwners.contains(e.getBoxOwnerIdentification())
                && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState()))
            .map(e -> e.getId()).collect(Collectors.toList());
        // 将旧箱码[审核通过]的数据删除
        List<Long> updateNeedDelBoxIds = oldPassBoxCodes.stream()
            .filter(e -> e != null && updateBoxOwners.contains(e.getBoxOwnerIdentification())
                && ProductCodeInfoStateEnum.AUDIT_PASS.getType().equals(e.getState()))
            .map(e -> e.getId()).collect(Collectors.toList());
        updateNeedDelBoxIds.removeIf(e -> e != null && updateBoxIds.contains(e));

        List<Long> delIds = new ArrayList<>();
        delIds.addAll(insertNeedDelBoxIds);
        delIds.addAll(updateNeedDelBoxIds);
        return delIds;
    }

    /**
     * 根据产品信息ID查询其所有的条码、箱码
     *
     * @return
     */
    public Map<Long, List<ProductCodeInfoDTO>>
        findProductCodeInfoByCondition(ProductCodeQueryConditionDTO queryConditionDTO) {
        // 1、查询条码（产品ID）
        // 2、查询箱码(交易目前箱码全部挂在规格上，供应链会在规格、产品ID都会有)
        AssertUtils.notNull(queryConditionDTO, "查询条件不能为空");
        AssertUtils.notEmpty(queryConditionDTO.getProductInfoIdList(), "产品信息ID集合不能为空");
        // 去重
        List<Long> productInfoIds =
            queryConditionDTO.getProductInfoIdList().stream().distinct().collect(Collectors.toList());
        queryConditionDTO.setProductInfoIdList(productInfoIds);
        List<ProductCodeInfoPO> poList = productCodeInfoPOMapper.selectTotalProductCodeInfo(queryConditionDTO);
        if (CollectionUtils.isEmpty(poList)) {
            return new HashMap<>(16);
        }
        // 条码
        Map<Long, List<ProductCodeInfoPO>> barCodeMap =
            poList.stream().filter(e -> e != null && ProductCodeTypeEnum.BAR_CODE.getType().equals(e.getCodeType()))
                .collect(Collectors.groupingBy(e -> e.getProductInfoId()));
        // 箱码 - 总集合
        List<ProductCodeInfoPO> boxCodeList =
            poList.stream().filter(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType()))
                .collect(Collectors.toList());
        // 箱码 - 挂靠在 productInfoId
        Map<Long, List<ProductCodeInfoPO>> boxCodeByProdudctInfoIdMap =
            boxCodeList.stream().filter(e -> e != null && e.getProductInfoId() != null)
                .collect(Collectors.groupingBy(e -> e.getProductInfoId()));
        // 箱码 - 挂靠在 ProductSpecificationId
        Map<Long, List<ProductCodeInfoPO>> boxCodeBySpecIdMap =
            boxCodeList.stream().filter(e -> e != null && e.getProductSpecificationId() != null)
                .collect(Collectors.groupingBy(e -> e.getProductSpecificationId()));
        // 查询产品规格
        List<ProductInfoSpecificationPO> specPOList =
            productInfoSpecificationPOMapper.findByProductInfoIds(queryConditionDTO.getProductInfoIdList());
        Map<Long, List<ProductInfoSpecificationPO>> specPOMap =
            specPOList.stream().filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getProductInfo_Id()));
        Map<Long, List<ProductCodeInfoDTO>> result = new HashMap<>(16);
        for (Long infoId : productInfoIds) {
            List<ProductCodeInfoPO> codeList = new ArrayList<>();
            // 当前产品条码集合
            List<ProductCodeInfoPO> barList = barCodeMap.get(infoId);
            // 当前产品箱码[productInfoId]集合
            List<ProductCodeInfoPO> boxWithInfoList = boxCodeByProdudctInfoIdMap.get(infoId);
            // 当前产品箱码[SpecificationId]集合
            List<ProductCodeInfoPO> boxWithSpecList = new ArrayList<>();
            List<ProductInfoSpecificationPO> specifications = specPOMap.get(infoId);
            if (CollectionUtils.isNotEmpty(specifications)) {
                for (ProductInfoSpecificationPO specPO : specifications) {
                    List<ProductCodeInfoPO> boxSpecList = boxCodeBySpecIdMap.get(specPO.getId());
                    if (CollectionUtils.isNotEmpty(boxSpecList)) {
                        boxWithSpecList.addAll(boxSpecList);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(barList)) {
                codeList.addAll(barList);
            }
            if (CollectionUtils.isNotEmpty(boxWithInfoList)) {
                codeList.addAll(boxWithInfoList);
            }
            if (CollectionUtils.isNotEmpty(boxWithSpecList)) {
                codeList.addAll(boxWithSpecList);
            }
            // 添加产品条码、箱码结果
            result.put(infoId, productCodeInfoConvertor
                .reverseConvert(codeList.stream().filter(e -> e != null).collect(Collectors.toList())));
        }
        return result;
    }

    /**
     * 根据产品infoId查询其所有的[条码/箱码]
     *
     * @param queryConditionDTO
     * @return
     */
    public List<ProductInfoDTO> findTotalProductInfo(ProductCodeQueryConditionDTO queryConditionDTO) {
        // 1、查询条码（产品ID）
        // 2、查询箱码(交易目前箱码全部挂在规格上，供应链会在规格、产品ID都会有)
        AssertUtils.notNull(queryConditionDTO, "查询条件不能为空");
        AssertUtils.notEmpty(queryConditionDTO.getProductInfoIdList(), "产品信息ID集合不能为空");
        // 去重
        List<Long> productInfoIds =
            queryConditionDTO.getProductInfoIdList().stream().distinct().collect(Collectors.toList());
        queryConditionDTO.setProductInfoIdList(productInfoIds);
        // 查询产品信息
        List<ProductInfoPO> infoPOList =
            productInfoPOMapper.selectProductByIdList(queryConditionDTO.getProductInfoIdList());
        if (CollectionUtils.isEmpty(infoPOList)) {
            return new ArrayList<>();
        }
        // 查询[条码/箱码]信息
        List<ProductCodeInfoPO> codeInfoPOList = productCodeInfoPOMapper.selectTotalProductCodeInfo(queryConditionDTO);
        // 查询产品规格
        List<ProductInfoSpecificationPO> specPOList =
            productInfoSpecificationPOMapper.findByProductInfoIds(queryConditionDTO.getProductInfoIdList());
        Map<Long, List<ProductInfoSpecificationPO>> specPOMap =
            specPOList.stream().filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getProductInfo_Id()));
        // 条码
        Map<Long,
            List<ProductCodeInfoPO>> barCodeMap = codeInfoPOList.stream()
                .filter(e -> e != null && ProductCodeTypeEnum.BAR_CODE.getType().equals(e.getCodeType()))
                .collect(Collectors.groupingBy(e -> e.getProductInfoId()));
        // 箱码 - 总集合
        List<ProductCodeInfoPO> boxCodeList = codeInfoPOList.stream()
            .filter(e -> e != null && ProductCodeTypeEnum.BOX_CODE.getType().equals(e.getCodeType()))
            .collect(Collectors.toList());
        // 箱码 - 挂靠在 productInfoId
        Map<Long, List<ProductCodeInfoPO>> boxCodeByInfoIdMap =
            boxCodeList.stream().filter(e -> e != null && e.getProductInfoId() != null)
                .collect(Collectors.groupingBy(e -> e.getProductInfoId()));
        // 箱码 - 挂靠在 ProductSpecificationId
        Map<Long, List<ProductCodeInfoPO>> boxCodeBySpecIdMap =
            boxCodeList.stream().filter(e -> e != null && e.getProductSpecificationId() != null)
                .collect(Collectors.groupingBy(e -> e.getProductSpecificationId()));
        // 组装数据
        for (ProductInfoPO infoPO : infoPOList) {
            Long productInfoId = infoPO.getId();
            // 当然产品[条码/箱码]结果集合
            List<ProductCodeInfoPO> codeList = new ArrayList<>();
            // 当前产品条码集合
            List<ProductCodeInfoPO> barList = barCodeMap.get(productInfoId);
            // 当前产品箱码[productInfoId]集合
            List<ProductCodeInfoPO> boxWithInfoList = boxCodeByInfoIdMap.get(productInfoId);
            // 当前产品箱码[SpecificationId]集合
            List<ProductCodeInfoPO> boxWithSpecList = new ArrayList<>();
            List<ProductInfoSpecificationPO> specifications = specPOMap.get(productInfoId);
            if (CollectionUtils.isNotEmpty(specifications)) {
                // 设置规格
                infoPO.setProductInfoSpecificationPOList(specifications);
                // 查找[箱码]信息
                for (ProductInfoSpecificationPO specPO : specifications) {
                    List<ProductCodeInfoPO> boxSpecList = boxCodeBySpecIdMap.get(specPO.getId());
                    if (CollectionUtils.isNotEmpty(boxSpecList)) {
                        boxWithSpecList.addAll(boxSpecList);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(barList)) {
                codeList.addAll(barList);
            }
            if (CollectionUtils.isNotEmpty(boxWithInfoList)) {
                codeList.addAll(boxWithInfoList);
            }
            if (CollectionUtils.isNotEmpty(boxWithSpecList)) {
                codeList.addAll(boxWithSpecList);
            }
            // 设置产品[条码/箱码]信息
            infoPO.setProductCodeInfoPOList(codeList.stream().filter(e -> e != null).collect(Collectors.toList()));
        }
        return productInfoConvertor.convertTOProductInfoDTO(infoPOList);
    }

    /**
     * 根据产品SKU同步产品[条码/箱码]信息 - 酒批 （临时同步方法）
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncProductCodesBySkuId(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            LOG.info("产品SKU为空，不能同步产品[条码/箱码]信息");
            return;
        }
        // 去重
        skuIds = skuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 查询交易条码/箱码
        Map<Long, ProductInfoSpecification> productInfoSpecificationMap = getProductCodeMapFromOP(skuIds);
        if (productInfoSpecificationMap.isEmpty()) {
            LOG.info("SKU ：[{}] 没有查询到产品[条码/箱码]信息", JSON.toJSONString(skuIds));
            return;
        }
        List<SyncProductCodeInfoDTO> syncCodeInfoDTOList = Lists.newArrayList();
        for (Map.Entry<Long, ProductInfoSpecification> entry : productInfoSpecificationMap.entrySet()) {
            ProductInfoSpecification value = entry.getValue();
            if (value == null) {
                continue;
            }
            // 产品信息ID
            Integer productInfoId =
                value.getProductInfoId() != null ? value.getProductInfoId() : value.getProductInfo().getId();
            AssertUtils.notNull(productInfoId, "产品信息ID为空");
            // 箱码
            List<ProductBoxCode> boxCodeList = value.getProductBoxCodeList();
            // 条码
            List<Code> bottleCodeList = value.getProductInfo().getBottleCodeList();
            // 转化后的 codes 集合
            List<SyncProductCodeInfoItemDTO> syncItemDTOList = new ArrayList<>();
            // 组装参数
            if (CollectionUtils.isNotEmpty(boxCodeList)) {
                syncItemDTOList.addAll(productCodeInfoConvertor.convertBoxCodeTOProductCodeInfoDTO(boxCodeList));
            }
            if (CollectionUtils.isNotEmpty(bottleCodeList)) {
                syncItemDTOList.addAll(productCodeInfoConvertor.convertBottleCodeTOProductCodeInfoDTO(bottleCodeList,
                    productInfoId.longValue()));
            }
            if (syncItemDTOList.isEmpty()) {
                continue;
            }
            SyncProductCodeInfoDTO codeInfoDTO = new SyncProductCodeInfoDTO();
            codeInfoDTO.setProductInfoId(productInfoId.longValue());
            codeInfoDTO.setBusinessTime(new Date());
            codeInfoDTO.setCodeItemDTOList(syncItemDTOList);
            syncCodeInfoDTOList.add(codeInfoDTO);
        }
        if (CollectionUtils.isEmpty(syncCodeInfoDTOList)) {
            LOG.info("SKU ：[{}] 查询产品[条码/箱码]后转化数据为空！", JSON.toJSONString(skuIds));
            return;
        }
        // 按 produdctInfoId 合并; 此方法是根据sku查找产品，一个产品可能对应多个sku
        List<SyncProductCodeInfoDTO> newSyncCodeInfoList = mergeSameProductInfo(syncCodeInfoDTOList);
        // 分批次更新产品信息[条码/箱码]
        List<List<SyncProductCodeInfoDTO>> codePartition = Lists.partition(newSyncCodeInfoList, PARTITIONS_SIZE);
        for (int i = 0; i < codePartition.size(); i++) {
            syncProductCodeInfo(codePartition.get(i));
        }
    }

    /**
     * 通用根据条件查询[条码/箱码] 默认查询：有码 + 审核通过 + 没停用[条码/箱码]
     *
     * @param conditionDTO
     * @return
     */
    public List<ProductCodeInfoDTO> findCodesByCommonDefaultCondition(ProductCodeQueryConditionDTO conditionDTO) {
        AssertUtils.notNull(conditionDTO, "查询参数不能为空");
        if (conditionDTO.getIsNoCode() == null) {
            // 默认查询有码 code
            conditionDTO.setIsNoCode(WhetherEnum.NO.getType().intValue());
        }
        if (conditionDTO.getState() == null) {
            // 默认查询审核通过 code
            conditionDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType().intValue());
        }
        if (conditionDTO.getIsDelete() == null) {
            // 默认查询没有[删除/停用] code
            conditionDTO.setIsDelete(WhetherEnum.NO.getType().intValue());
        }
        return findCodesByCommonCondition(conditionDTO);
    }

    /**
     * 通用根据[条码/箱码]查询并返回Map key ： [条码/箱码] value : [条码/箱码]信息
     *
     * @param conditionDTO
     * @return
     */
    public Map<String, List<ProductCodeInfoDTO>> findCodeInfoByCodes(ProductCodeQueryConditionDTO conditionDTO) {
        AssertUtils.notNull(conditionDTO, "查询参数不能为空");
        AssertUtils.notEmpty(conditionDTO.getCodes(), "查询参数不能为空");
        List<String> codes =
            conditionDTO.getCodes().stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        List<ProductCodeInfoDTO> codesByCommonCondition = findCodesByCommonCondition(conditionDTO);
        Map<String, List<ProductCodeInfoDTO>> result = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(codesByCommonCondition)) {
            Map<String, List<ProductCodeInfoDTO>> codeMap =
                codesByCommonCondition.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getCode()))
                    .collect(Collectors.groupingBy(e -> e.getCode()));
            for (String c : codes) {
                List<ProductCodeInfoDTO> codeInfoDTOList = codeMap.get(c);
                if (CollectionUtils.isNotEmpty(codeInfoDTOList)) {
                    result.put(c, codeInfoDTOList);
                }
            }
        }
        return result;
    }

    /**
     * 通用根据条件查询[条码/箱码]
     *
     * @param conditionDTO
     * @return
     */
    public List<ProductCodeInfoDTO> findCodesByCommonCondition(ProductCodeQueryConditionDTO conditionDTO) {
        AssertUtils.notNull(conditionDTO, "查询参数不能为空");
        // 1、确定查询类型
        ProductCodeQueryTypeEnum queryType = getQueryType(conditionDTO);
        List<ProductCodeInfoPO> poList;
        if (ProductCodeQueryTypeEnum.BOTH_CODE.equals(queryType)) {
            poList = productCodeInfoPOMapper.selectTotalProductCodeInfo(conditionDTO);
        } else if (ProductCodeQueryTypeEnum.NORMAL.equals(queryType)) {
            poList = productCodeInfoPOMapper.selectProductCodeInfoNormal(conditionDTO);
        } else {
            poList = new ArrayList<>();
        }
        return productCodeInfoConvertor.reverseConvert(poList);
    }

    /**
     * [停用/启用]条码/箱码
     *
     * @param dto
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateCodeIsDeleteById(ProductCodeInfoDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        AssertUtils.notNull(dto.getId(), "ID不能为空");
        AssertUtils.notNull(dto.getIsDelete(), "是否停用参数不能为空");
        ProductCodeInfoPO updatePO = new ProductCodeInfoPO();
        BeanUtils.copyProperties(dto, updatePO);
        productCodeInfoPOMapper.updateByPrimaryKeySelective(updatePO);
    }

    /**
     * 删除产品条码[瓶码]
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBarCodeByCondition(DeleteProductInfoBarCodesDTO deleteBarCode) {
        AssertUtils.notNull(deleteBarCode, "删除条码参数不能为空！");
        AssertUtils.notNull(deleteBarCode.getProductInfoId(), "删除条码产品信息ID不能为空！");
        AssertUtils.notEmpty(deleteBarCode.getBarCodes(), "删除条码信息不能为空！");
        ProductCodeEnableStatusUpdateDTO enableStatusUpdateDTO = new ProductCodeEnableStatusUpdateDTO();
        enableStatusUpdateDTO.setProductInfoId(deleteBarCode.getProductInfoId().longValue());
        enableStatusUpdateDTO.setCodeList(deleteBarCode.getBarCodes());
        enableStatusUpdateDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
        enableStatusUpdateDTO.setIsDelete(WhetherEnum.YES.getType());
        enableStatusUpdateDTO.setLastUpdateUser(deleteBarCode.getOperateUser());
        LOG.info("删除条码[瓶码]转化后WMS删除参数信息：{}", JSON.toJSONString(enableStatusUpdateDTO));
        productCodeInfoPOMapper.updateProductInfoBarCodeEnableStatus(enableStatusUpdateDTO);
    }

    private ProductCodeQueryTypeEnum getQueryType(ProductCodeQueryConditionDTO conditionDTO) {
        if (CollectionUtils.isNotEmpty(conditionDTO.getProductSpecificationIdList())
            || StringUtils.isNotBlank(conditionDTO.getCode()) || CollectionUtils.isNotEmpty(conditionDTO.getCodes())
            || conditionDTO.getCodeType() != null) {
            return ProductCodeQueryTypeEnum.NORMAL;
        }
        // 如果只传递了 productInfoId 那么默认以产品维度查询【条码/箱码】,即查询出每个产品相关的条码和箱码
        if (CollectionUtils.isNotEmpty(conditionDTO.getProductInfoIdList())) {
            return ProductCodeQueryTypeEnum.BOTH_CODE;
        }
        throw new DataValidateException("查询条件不能为空");
    }

    /**
     * 同步前按 productInfoId 进行合并
     *
     * @param codeInfoDTOList
     * @return
     */
    private List<SyncProductCodeInfoDTO> mergeSameProductInfo(List<SyncProductCodeInfoDTO> codeInfoDTOList) {
        // 按 produdctInfoId 分组
        Map<Long, List<SyncProductCodeInfoDTO>> infoGroupMap =
            codeInfoDTOList.stream().filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getProductInfoId()));
        // 合并 code 结果集
        List<SyncProductCodeInfoDTO> rsCodes = Lists.newArrayList();
        for (Map.Entry<Long, List<SyncProductCodeInfoDTO>> entry : infoGroupMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<SyncProductCodeInfoDTO> value = entry.getValue();
            // 合并 item 结果集
            List<SyncProductCodeInfoItemDTO> rsItemDTOS = Lists.newArrayList();
            for (int i = 0; i < value.size(); i++) {
                SyncProductCodeInfoDTO codeInfoDTO = value.get(i);
                if (codeInfoDTO == null || CollectionUtils.isEmpty(codeInfoDTO.getCodeItemDTOList())) {
                    continue;
                }
                rsItemDTOS.addAll(
                    codeInfoDTO.getCodeItemDTOList().stream().filter(e -> e != null).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(rsItemDTOS)) {
                continue;
            }
            SyncProductCodeInfoDTO codeInfoDTO = new SyncProductCodeInfoDTO();
            codeInfoDTO.setProductInfoId(entry.getKey());
            codeInfoDTO.setBusinessTime(new Date());
            codeInfoDTO.setCodeItemDTOList(rsItemDTOS);
            rsCodes.add(codeInfoDTO);
        }
        return rsCodes;
    }

    // 分批次查询交易条码/箱码信息
    private Map<Long, ProductInfoSpecification> getProductCodeMapFromOP(List<Long> skuIds) {
        Map<Long, ProductInfoSpecification> mapAll = new HashMap<>(16);
        List<List<Long>> partition = Lists.partition(skuIds, PARTITIONS_SIZE);
        for (int i = 0; i < partition.size(); i++) {
            Map<Long, ProductInfoSpecification> dataMap =
                iProductInfoSpecificationQueryService.getProductInfoSpecificationMap(Sets.newHashSet(partition.get(i)));
            if (dataMap == null || dataMap.isEmpty()) {
                continue;
            }
            mapAll.putAll(dataMap);
        }
        return mapAll;
    }

    /**
     * 校验同步产品条码/箱码数据
     *
     * @param syncDTOList
     */
    private void validateSyncCodeInfo(List<SyncProductCodeInfoDTO> syncDTOList) {
        AssertUtils.notEmpty(syncDTOList, "同步产品信息[条码/箱码]不能为空");
        for (SyncProductCodeInfoDTO syncDTO : syncDTOList) {
            if (syncDTO == null) {
                continue;
            }
            AssertUtils.notNull(syncDTO.getProductInfoId(), "同步产品信息ID不能为空");
            AssertUtils.notEmpty(syncDTO.getCodeItemDTOList(), "同步产品[条码/箱码]集合不能为空");
            List<SyncProductCodeInfoItemDTO> codeItemDTOList = syncDTO.getCodeItemDTOList();
            for (int i = 0; i < codeItemDTOList.size(); i++) {
                SyncProductCodeInfoItemDTO infoDTO = codeItemDTOList.get(i);
                AssertUtils.isTrue(StringUtils.isNotBlank(infoDTO.getCode()), "产品信息[条码/箱码]Code不能为空");
                AssertUtils.notNull(infoDTO.getCodeType(), "产品信息[条码/箱码]类型不能为空");
                if (ProductCodeTypeEnum.BAR_CODE.getType().equals(infoDTO.getCodeType())) {
                    // 如果是条码产品id必须存在
                    AssertUtils.notNull(infoDTO.getProductInfoId(), "产品条码对应的产品信息ID不能为空");
                }
                if (ProductCodeTypeEnum.BOX_CODE.getType().equals(infoDTO.getCodeType())) {
                    // 如果是箱码产品id和规格ID必须有一个不为空
                    if (infoDTO.getProductInfoId() == null && infoDTO.getProductSpecificationId() == null) {
                        throw new DataValidateException("产品箱码对应的产品ID与产品规格ID不能都为空");
                    }
                }
            }
        }
    }

    /**
     * 基础校验
     *
     * @param codeInfoDTOList
     */
    private void validateBaseCodeInfo(List<ProductCodeInfoDTO> codeInfoDTOList) {
        for (int i = 0; i < codeInfoDTOList.size(); i++) {
            ProductCodeInfoDTO infoDTO = codeInfoDTOList.get(i);
            AssertUtils.notNull(infoDTO.getCodeType(), "产品信息[条码/箱码]类型不能为空");
            if (StringUtils.isBlank(infoDTO.getCode())) {
                // 条码/箱码为空，其图片必须不能为空
                AssertUtils.isTrue(StringUtils.isNotBlank(infoDTO.getCodeImgId()), "[条码/箱码]为空时图片信息不能为空");
            }
            if (ProductCodeTypeEnum.BAR_CODE.getType().equals(infoDTO.getCodeType())) {
                // 如果是条码产品id必须存在
                AssertUtils.notNull(infoDTO.getProductInfoId(), "产品条码对应的产品信息ID不能为空");
            }
            if (ProductCodeTypeEnum.BOX_CODE.getType().equals(infoDTO.getCodeType())) {
                // 如果是箱码产品id和规格ID必须有一个不为空
                if (infoDTO.getProductInfoId() == null && infoDTO.getProductSpecificationId() == null) {
                    throw new DataValidateException("产品ID与产品规格ID不能都为空");
                }
            }
        }
    }

    /**
     * 去重
     *
     * @param codeInfoDTOList
     * @return
     */
    private List<ProductCodeInfoDTO> removeRepeatCodeInfo(List<ProductCodeInfoDTO> codeInfoDTOList) {
        if (CollectionUtils.isEmpty(codeInfoDTOList)) {
            return codeInfoDTOList;
        }
        // 1、[条码/箱码]有码
        List<ProductCodeInfoDTO> hasCodeList = codeInfoDTOList.stream()
            .filter(e -> e != null && StringUtils.isNotBlank(e.getCode())).collect(Collectors.toList());
        List<ProductCodeInfoDTO> hasCodeUniqueList = removeRepeatCodeInfo(hasCodeList, true);
        // 2、[条码/箱码]无码
        List<ProductCodeInfoDTO> noCodeList = codeInfoDTOList.stream()
            .filter(e -> e != null && StringUtils.isBlank(e.getCode())).collect(Collectors.toList());
        List<ProductCodeInfoDTO> noCodeUniqueList = removeRepeatCodeInfo(noCodeList, false);
        // 最终结果
        hasCodeUniqueList.addAll(noCodeUniqueList);
        return hasCodeUniqueList;
    }

    private List<ProductCodeInfoDTO> removeRepeatCodeInfo(List<ProductCodeInfoDTO> codeInfoDTOList, boolean hasCode) {
        // 不重复数据
        List<ProductCodeInfoDTO> uniqueList = codeInfoDTOList.stream().filter(e -> e != null)
            .collect(Collectors.collectingAndThen(Collectors.groupingBy(e -> {
                if (hasCode) {
                    return e.getHasCodeIdentity();
                }
                return e.getNoCodeIdentity();
            }), map -> {
                // 删除重复数据
                map.values().removeIf(values -> values.size() > 1);
                List<ProductCodeInfoDTO> rs = new ArrayList<>();
                for (List<ProductCodeInfoDTO> p : map.values()) {
                    rs.addAll(p);
                }
                return rs;
            }));
        if (uniqueList.size() == codeInfoDTOList.size()) {
            // 去重后的数组与原数组元素一样多则认为没有重复数据直接返回
            return codeInfoDTOList;
        }
        // 重复数据
        Collection<List<ProductCodeInfoDTO>> repeatList = codeInfoDTOList.stream().filter(e -> e != null)
            .collect(Collectors.collectingAndThen(Collectors.groupingBy(e -> {
                if (hasCode) {
                    return e.getHasCodeIdentity();
                }
                return e.getNoCodeIdentity();
            }), map -> {
                // 删除不重复数据
                map.values().removeIf(values -> values.size() == 1);
                return map.values();
            }));
        for (List<ProductCodeInfoDTO> rp : repeatList) {
            if (CollectionUtils.isEmpty(rp)) {
                continue;
            }
            ProductCodeInfoDTO saveDTO = null;
            // 保留最近更新数据，没有则保留最新创建数据，再没有随机选取一条
            Optional<ProductCodeInfoDTO> maxLastOpt =
                rp.stream().filter(e -> e != null && e.getLastUpdateTime() != null)
                    .max(Comparator.comparing(e -> e.getLastUpdateTime()));
            Optional<ProductCodeInfoDTO> maxCreateOpt = rp.stream().filter(e -> e != null && e.getCreateTime() != null)
                .max(Comparator.comparing(e -> e.getCreateTime()));
            if (maxLastOpt.isPresent()) {
                saveDTO = maxLastOpt.get();
            } else if (maxCreateOpt.isPresent()) {
                saveDTO = maxCreateOpt.get();
            } else {
                saveDTO = rp.stream().filter(e -> e != null).findAny().orElse(null);
            }
            if (saveDTO == null) {
                continue;
            }
            // 添加保留数据
            uniqueList.add(saveDTO);
            // 从重复集合移除保留的数据
            rp.remove(saveDTO);
            LOG.info("产品[条码/箱码]存在重复数据！移除重复数据：{}", JSON.toJSONString(rp));
        }
        return uniqueList;
    }

    private Pair<List<ProductCodeInfoDTO>, List<ProductCodeInfoDTO>>
        genChangeCodeInfo(List<ProductCodeInfoDTO> newCodeList, List<ProductCodeInfoDTO> oldCodeList) {
        List<ProductCodeInfoDTO> updateList = new ArrayList<>();
        List<ProductCodeInfoDTO> insertList = new ArrayList<>();
        if (CollectionUtils.isEmpty(oldCodeList)) {
            insertList.addAll(newCodeList);
            return new ImmutablePair<List<ProductCodeInfoDTO>, List<ProductCodeInfoDTO>>(insertList, new ArrayList<>());
        }
        // 1、旧[条码/箱码]有码
        Map<String, ProductCodeInfoDTO> oldHasCodeMap = getHasCodesMap(oldCodeList);
        // 2、旧[条码/箱码]无码，必须要上传图片，图片地址不一样的视为新增
        Map<String, ProductCodeInfoDTO> oldNoCodeMap = getNoCodesMap(oldCodeList);
        for (int i = 0; i < newCodeList.size(); i++) {
            ProductCodeInfoDTO newCodeDTO = newCodeList.get(i);
            if (newCodeDTO == null) {
                continue;
            }
            // 旧[条码/箱码]信息
            ProductCodeInfoDTO oldCodeDTO = null;
            if (StringUtils.isNotBlank(newCodeDTO.getCode())) {
                // [条码/箱码]有码
                oldCodeDTO = oldHasCodeMap.get(newCodeDTO.getHasCodeIdentity());
            } else {
                // [条码/箱码]无码
                oldCodeDTO = oldNoCodeMap.get(newCodeDTO.getNoCodeIdentity());
            }
            if (oldCodeDTO == null) {
                // 新增
                newCodeDTO.setId(UuidUtil.getUUid());
                insertList.add(newCodeDTO);
            } else {
                // 修改
                newCodeDTO.setId(oldCodeDTO.getId());
                newCodeDTO.setProductCodeInfoAuditId(oldCodeDTO.getProductCodeInfoAuditId());
                updateList.add(newCodeDTO);
            }
        }
        return new ImmutablePair<List<ProductCodeInfoDTO>, List<ProductCodeInfoDTO>>(insertList, updateList);
    }

    public Map<String, ProductCodeInfoDTO> getNoCodesMap(List<ProductCodeInfoDTO> codeList) {
        return codeList.stream().filter(e -> e != null && StringUtils.isBlank(e.getCode()))
            .collect(Collectors.toMap(e -> e.getNoCodeIdentity(), Function.identity(), (k1, k2) -> {
                if (k1 != null && WhetherEnum.NO.getType().equals(k1.getIsDelete())) {
                    return k1;
                }
                if (k2 != null && WhetherEnum.NO.getType().equals(k1.getIsDelete())) {
                    return k2;
                }
                return k1 != null ? k1 : k2;
            }));
    }

    public Map<String, ProductCodeInfoDTO> getHasCodesMap(List<ProductCodeInfoDTO> codeList) {
        return codeList.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getCode()))
            .collect(Collectors.toMap(e -> e.getHasCodeIdentity(), Function.identity(), (k1, k2) -> {
                if (k1 != null && WhetherEnum.NO.getType().equals(k1.getIsDelete())) {
                    return k1;
                }
                if (k2 != null && WhetherEnum.NO.getType().equals(k1.getIsDelete())) {
                    return k2;
                }
                return k1 != null ? k1 : k2;
            }));
    }
}
