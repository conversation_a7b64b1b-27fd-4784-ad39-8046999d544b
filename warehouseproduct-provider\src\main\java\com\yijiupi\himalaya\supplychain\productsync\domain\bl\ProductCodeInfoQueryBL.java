package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeOwnerTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;

@Service
public class ProductCodeInfoQueryBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCodeInfoQueryBL.class);

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    public Map<Long, List<ProductCodeInfoDTO>> findProductCodeBySkuIds(List<Long> skuIdList) {
        AssertUtils.notEmpty(skuIdList, "产品SkuId不能为空");
        // 去重
        skuIdList = skuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        AssertUtils.notEmpty(skuIdList, "产品SkuId不能为空");
        List<ProductSkuPO> productSkuPOS = productSkuMapper.selectSkuInfoBySkuIds(null, skuIdList);
        Map<Long, List<ProductCodeInfoDTO>> result = new HashMap<>(16);
        if (CollectionUtils.isEmpty(productSkuPOS)) {
            return result;
        }
        Map<Long, Long> skuMap = productSkuPOS.stream().filter(e -> e != null && e.getProductInfoId() != null)
            .collect(Collectors.toMap(e -> e.getProductSkuId(), e -> e.getProductInfoId()));
        List<Long> productInfoIdList = Lists.newArrayList(skuMap.values());
        // 查询审核通过条码/箱码
        Map<Long, List<ProductCodeInfoDTO>> productCodesMap =
            productCodeInfoBL.findProductCodeInfoByCondition(new ProductCodeQueryConditionDTO(productInfoIdList,
                ProductCodeInfoStateEnum.AUDIT_PASS.getType().intValue()));
        if (productCodesMap == null || productCodesMap.isEmpty()) {
            return result;
        }
        for (Map.Entry<Long, Long> entry : skuMap.entrySet()) {
            List<ProductCodeInfoDTO> codes = productCodesMap.get(entry.getValue());
            if (CollectionUtils.isEmpty(codes)) {
                continue;
            }
            result.put(entry.getKey(), codes);
        }
        return result;
    }

    /**
     * 根据skuId查询条码\箱码
     *
     * @param skuIdList
     * @return
     */
    public Map<Long, ProductCodeDTO> findProductCodeDTOBySkuIds(Integer cityId, List<Long> skuIdList) {
        Map<Long, List<ProductCodeInfoDTO>> codes = findProductCodeBySkuIds(skuIdList);
        if (codes == null || codes.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        return filterProductCode(cityId, codes, true);
    }

    /**
     * 根据skuId查询启用条码\箱码
     *
     * @param skuIdList
     */
    public Map<Long, ProductCodeDTO> findEnableProductCodeBySkuIds(Integer cityId, List<Long> skuIdList) {
        Map<Long, List<ProductCodeInfoDTO>> codes = findProductCodeBySkuIds(skuIdList);
        if (codes == null || codes.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        return filterProductCode(cityId, codes, false);
    }

    private Map<Long, ProductCodeDTO> filterProductCode(Integer cityId, Map<Long, List<ProductCodeInfoDTO>> codes,
        boolean includeDisableCode) {
        Map<Long, ProductCodeDTO> result = new HashMap<>(codes.size());
        for (Map.Entry<Long, List<ProductCodeInfoDTO>> entry : codes.entrySet()) {
            if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<ProductCodeInfoDTO> productCodes = entry.getValue();
            if (!includeDisableCode) {
                // 不包含禁用 Code：即只获取启用的code
                productCodes = productCodes.stream()
                    .filter(c -> c != null && Objects.equals(WhetherEnum.NO.getType(), c.getIsDelete()))
                    .collect(Collectors.toList());
            }
            // 条码
            List<String> barCodes = productCodes.stream()
                .filter(e -> e != null && StringUtils.isNotBlank(e.getCode())
                    && Objects.equals(ProductCodeTypeEnum.BAR_CODE.getType(), e.getCodeType()))
                .map(e -> e.getCode()).distinct().collect(Collectors.toList());
            List<String> boxCodes = null;
            if (cityId == null) {
                // 箱码
                boxCodes = productCodes.stream()
                    .filter(e -> e != null && StringUtils.isNotBlank(e.getCode())
                        && Objects.equals(ProductCodeTypeEnum.BOX_CODE.getType(), e.getCodeType()))
                    .map(e -> e.getCode()).distinct().collect(Collectors.toList());
            } else {
                // 1、过滤当前城市下的箱码
                boxCodes = productCodes.stream()
                    .filter(e -> e != null && StringUtils.isNotBlank(e.getCode())
                        && Objects.equals(ProductCodeTypeEnum.BOX_CODE.getType(), e.getCodeType())
                        && Objects.equals(ProductCodeOwnerTypeEnum.REGION.getType(), e.getOwnerType())
                        && Objects.equals(String.valueOf(cityId), e.getOwnerId()))
                    .map(e -> e.getCode()).distinct().collect(Collectors.toList());
                // 2、如果当前城市没有箱码，则取总部下的箱码
                if (CollectionUtils.isEmpty(boxCodes)) {
                    boxCodes = productCodes.stream()
                        .filter(e -> e != null && StringUtils.isNotBlank(e.getCode())
                            && Objects.equals(ProductCodeTypeEnum.BOX_CODE.getType(), e.getCodeType())
                            && Objects.equals(ProductCodeOwnerTypeEnum.HQ.getType(), e.getOwnerType()))
                        .map(e -> e.getCode()).distinct().collect(Collectors.toList());
                }
            }
            ProductCodeDTO codeDTO = new ProductCodeDTO();
            // 设置条码
            codeDTO.setUnitCode(barCodes);
            // 设置箱码
            codeDTO.setPackageCode(boxCodes);
            // 设置产品条码/箱码信息
            result.put(entry.getKey(), codeDTO);
        }
        return result;
    }

    /**
     * 通过瓶码或者箱码查询sku列表
     *
     * @param code 条码
     * @param cityId
     * @return
     */
    public List<Long> getProductSkuIdByCode(String code, Integer cityId) {
        LOG.info("WMS - 通过瓶码或者箱码查询sku列表参数：城市ID - {}, 瓶码/箱码 - {}", cityId, code);
        AssertUtils.isTrue(StringUtils.isNotBlank(code), "条码/箱码不能为空！");
        AssertUtils.notNull(cityId, "城市ID不能为空！");
        ProductCodeQueryConditionDTO queryConditionDTO = new ProductCodeQueryConditionDTO();
        queryConditionDTO.setCodes(Lists.newArrayList(code));
        queryConditionDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType().intValue());
        List<ProductCodeInfoDTO> codes = productCodeInfoBL.findCodesByCommonCondition(queryConditionDTO);
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> productInfoIdList = codes.stream().filter(e -> e != null && e.getProductInfoId() != null)
            .map(e -> e.getProductInfoId()).distinct().collect(Collectors.toList());
        List<Long> productSpecIdList = codes.stream().filter(e -> e != null && e.getProductSpecificationId() != null)
            .map(e -> e.getProductSpecificationId()).distinct().collect(Collectors.toList());
        List<Long> skuIdList = new ArrayList<>();
        // 查询 sku 信息
        if (CollectionUtils.isNotEmpty(productInfoIdList)) {
            List<ProductSkuPO> productSkuPOS =
                productSkuMapper.selectSkuInfoByProductInfoIds(cityId, productInfoIdList);
            skuIdList.addAll(productSkuPOS.stream().filter(e -> e != null).map(e -> e.getProductSkuId())
                .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(productSpecIdList)) {
            List<ProductSkuPO> productSkuPOS =
                productSkuMapper.listProductSkuBySpecIds(cityId, productSpecIdList, null, null);
            skuIdList.addAll(productSkuPOS.stream().filter(e -> e != null).map(e -> e.getProductSkuId())
                .collect(Collectors.toList()));
        }
        return skuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
    }
}
