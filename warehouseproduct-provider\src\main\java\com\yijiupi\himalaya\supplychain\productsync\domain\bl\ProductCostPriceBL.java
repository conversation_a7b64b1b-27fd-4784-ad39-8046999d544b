package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCostPriceSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品成本价同步
 *
 * <AUTHOR>
 * @date 2020-04-22 10:54
 */
@Service
public class ProductCostPriceBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCostPriceBL.class);

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    /**
     * 产品成本价同步
     */
    public void sync(List<ProductCostPriceSyncDTO> syncDTOList) {
        AssertUtils.notEmpty(syncDTOList, "成本价同步对象不能为空");
        syncDTOList.forEach(syncDTO -> {
            try {
                AssertUtils.notNull(syncDTO.getCityId(), "成本价同步城市ID不能为空");
                AssertUtils.notNull(syncDTO.getStoreHouseId(), "成本价同步仓库ID不能为空");
                AssertUtils.notNull(syncDTO.getProductSpecId(), "成本价同步规格ID不能为空");
                AssertUtils.notNull(syncDTO.getCostPrice(), "成本价同步价不能为空");
                // 酒批货主id设为null
                if (syncDTO.getOwnerId() != null && syncDTO.getOwnerId() == 0) {
                    syncDTO.setOwnerId(null);
                }

                // 查询产品
                ProductSkuPO productSkuPO = productSkuMapper.getProductSkuBySpecId(syncDTO.getCityId(),
                    syncDTO.getProductSpecId(), syncDTO.getOwnerId(), null);
                if (productSkuPO == null) {
                    LOG.info("找不到产品，产品成本价同步跳过：{}", JSON.toJSONString(syncDTO));
                    return;
                }

                // 保存成本价
                ProductSkuConfigDTO productSkuConfigDTO = new ProductSkuConfigDTO();
                productSkuConfigDTO.setWarehouseId(syncDTO.getStoreHouseId());
                productSkuConfigDTO.setProductSkuId(productSkuPO.getProductSkuId());
                productSkuConfigDTO.setCostPrice(syncDTO.getCostPrice());
                productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, false);
                LOG.info("同步成本价：{}", JSON.toJSONString(productSkuConfigDTO));

            } catch (Exception e) {
                LOG.error("成本价同步失败", e);
            }
        });
    }
}
