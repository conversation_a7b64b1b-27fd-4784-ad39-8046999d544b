package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.FileQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.file.dto.basic.FileInfoDTO;
import com.yijiupi.himalaya.supplychain.file.service.IFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 产品图片
 *
 * <AUTHOR>
 * @date 2020-07-13 17:07
 */
@Service
public class ProductImageBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductImageBL.class);

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private FileQueryService fileQueryService;

    @Reference
    private IFileService iFileService;

    /**
     * 保存图片
     *
     * @param imageId
     * @param bussinessId
     */
    public void saveImage(Integer imageId, String bussinessId) {
        try {
            AssertUtils.notNull(imageId, "图片ID不能为空");
            AssertUtils.notNull(bussinessId, "业务ID不能为空");

            // 根据图片id获取图片
            Set<Integer> imageIds = new HashSet<>();
            imageIds.add(imageId);
            Map<Integer, String> fileInfoMap = fileQueryService.queryUrlByIds(imageIds);
            LOG.info("调交易根据图片ID获取图片：{}", JSON.toJSONString(fileInfoMap));
            if (fileInfoMap == null || fileInfoMap.isEmpty() || StringUtils.isEmpty(fileInfoMap.get(imageId))) {
                LOG.info("调交易根据图片ID获取图片为空：{}", imageId);
                return;
            }

            // 先删除
            iFileService.deleteOneImg(bussinessId);
            // 后新增
            FileInfoDTO dto = new FileInfoDTO();
            dto.setBussinessId(bussinessId);
            dto.setCloudSrc(fileInfoMap.get(imageId));
            dto.setFileName("产品同步");
            dto.setFileSize(Long.valueOf("0"));
            iFileService.saveFile(dto);
            LOG.info("产品同步图片：{}", JSON.toJSONString(dto));
        } catch (Exception e) {
            LOG.error(String.format("保存图片异常, 参数 : %s, %s", imageId, bussinessId), e);
        }
    }

}
