/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfo;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecification;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductInfoConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductInfoSpecificationConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.cache.ScmProductCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品信息同步
 * <AUTHOR>
 * @since 2018年11月13日 下午3:53:23
 */
@Service
public class ProductInfoBL {
    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;
    @Autowired
    private ProductSkuMapper productSkuMapper;
    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;
    @Autowired
    private ProductInfoConvertor productInfoConvertor;
    @Autowired
    private ProductInfoSpecificationConvertor productInfoSpecificationConvertor;
    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;
    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;
    @Autowired
    private ProductImageBL productImageBL;
    @Autowired
    private ScmProductCache scmProductCache;
    @Reference
    private IVariableValueService variableValueService;

    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoBL.class);

    /**
     * 获取灰度仓库的KEY
     */
    private static final String DEFAULT_ORG = "DefaultCategoryOrgId";

    /**
     * 新增productInfo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertProductInfo(ProductInfo dto, Byte productInfoType) {
        updateProductInfo(dto, productInfoType);
    }

    /**
     * 修改 productInfo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductInfo(ProductInfo dto, Byte productInfoType) {
        ProductInfoPO infoOld = productInfoPOMapper.selectByPrimaryKey(dto.getId().longValue());
        ProductInfoPO convert = productInfoConvertor.convert(dto);
        convert.setProductInfoType(productInfoType);
        if (infoOld != null) {
            // 时间
            Date now = new Date();
            convert.setLastUpdateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
            convert.setLastUpdateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
            productInfoPOMapper.updateByPrimaryKeySelective(convert);
            LOG.info("【酒批】修改产品信息：{}", JSON.toJSONString(convert));
            // 同步更新sku的产品名称
            if (StringUtils.isNotEmpty(convert.getProductName()) && convert.getId() != null) {
                int count = productSkuMapper.updateProductNameByInfoId(convert.getProductName(), convert.getId());
                LOG.info("【酒批】修改产品信息同时更新sku名称，infoId:{}, name:{}, 修改条数:{}", convert.getId(), convert.getProductName(),
                    count);
            }
        } else {
            // 时间
            Date now = new Date();
            convert.setCreateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
            convert.setLastUpdateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
            convert.setCreateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
            convert.setLastUpdateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
            productInfoPOMapper.insertSelective(convert);
            LOG.info("【酒批】新增产品信息：{}", JSON.toJSONString(convert));
        }
        List<ProductInfoSpecification> specificationList = dto.getSpecificationList();
        if (!CollectionUtils.isEmpty(specificationList)) {
            List<Long> specIds = specificationList.stream().map(ProductInfoSpecification::getId)
                    .map(Integer::longValue).distinct().collect(Collectors.toList());
            Map<Long, ProductInfoSpecificationPO> specMap = productInfoSpecificationPOMapper.listByIds(specIds)
                    .stream().collect(Collectors.toMap(ProductInfoSpecificationPO::getId, Function.identity()));
            for (ProductInfoSpecification spec : specificationList) {
                long specId = spec.getId().longValue();
                // 产品信息id
                spec.setProductInfoId(dto.getId());
                // 修改时,spec可能会新增可能会修改,,如果我们原表有体积字段了,以我们的字段为准,不插入op传的体积字段
                // 2023-11-13 调整: 如果我们原表有体积字段了, 且其不为 0, 以我们的字段为准, 否则更新
                // 2024-05-28 调整: 以交易数据为准; SCM-14541 【线上问题】长宽高数据以交易数据为准
                ProductInfoSpecificationPO specInDB = specMap.get(specId);
                ProductInfoSpecificationPO infoSpec = productInfoSpecificationConvertor.convert(spec);
                Date now = new Date();
                if (specInDB == null) {
                    // 时间
                    infoSpec.setCreateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
                    infoSpec.setLastUpdateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
                    infoSpec.setCreateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
                    infoSpec.setLastUpdateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
                    productInfoSpecificationPOMapper.insertSelective(infoSpec);
                    LOG.info("【酒批】新增产品规格：{}", JSON.toJSONString(infoSpec));
                } else {
                    infoSpec.setLastUpdateTime(dto.getCreateTime() == null ? now : dto.getCreateTime());
                    infoSpec.setLastUpdateUser_Id(dto.getCreateUserId() == null ? 0 : dto.getCreateUserId());
                    productInfoSpecificationPOMapper.updateByPrimaryKeySelective(infoSpec);
                    LOG.info("【酒批】修改产品规格：{}", JSON.toJSONString(infoSpec));
                }
            }
        }
        // 设置展示类目名称
        productInfoCategoryBL.setCategoryName(dto.getProductInfoDisplayCategoryList());
        // 更新缓存信息
        scmProductCache.set(dto);
        // 同步类目
        VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
        valueQuery.setVariableKey(DEFAULT_ORG);
        VariableDefAndValueDTO value = variableValueService.detailVariable(valueQuery);
        CategorySync categorySync = ProductCategoryConvertor.ProductInfo2SyncCategoryTree(dto,
            value == null || StringUtils.isEmpty(value.getVariableData()) ? null
                : Integer.parseInt(value.getVariableData()));
        if (categorySync == null) {
            LOG.info("获取不到展示类目：{}", JSON.toJSONString(dto));
            return;
        }
        List<CategorySync> categorySyncs = new ArrayList<>();
        categorySyncs.add(categorySync);
        productCategoryGroupBL.syncCategoryByInfoId(categorySyncs);

        // 产品图片保存
        if (StringUtils.isNotEmpty(convert.getDefaultImageFile_Id())) {
            productImageBL.saveImage(Integer.valueOf(convert.getDefaultImageFile_Id()), convert.getId().toString());
        }
    }

    /**
     * number 是否为有效数字 (number 不为 null 且大于 0)
     *
     * @param number 要判断的数字
     * @return number 是否为有效数字
     */
    private boolean isValidNumber(Double number) {
        return number != null && number > 0;
    }

}
