package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.file.dto.basic.FileInfoDTO;
import com.yijiupi.himalaya.supplychain.file.service.IFileService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductBySaasConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.WhetherEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoItemDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductSyncOpService;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 产品管理SAAS化
 *
 * <AUTHOR>
 * @date 2019-08-27 11:29
 */
@Service
public class ProductInfoBySaasBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductInfoBySaasBL.class);

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @Autowired
    private ProductSkuBL productSkuBL;

    @Autowired
    private ThirdproductsRelationBL thirdproductsRelationBL;
    @Reference
    private IFileService fileService;
    @Autowired
    private OwnerBL ownerBL;
    @Reference
    private IOrgService iOrgService;
    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;
    @Autowired
    private ProductSyncOpService productSyncOpService;

    /**
     * 产品信息列表
     * 
     * @return
     */
    public PageList<ProductInfoDTO> listProductInfo(ProductInfoSO productInfoSO) {
        AssertUtils.notNull(productInfoSO, "参数不能为空");
        AssertUtils.notNull(productInfoSO.getOrgId(), "城市ID不能为空");

        productInfoSO.setParentOrgId(getParentOrgId(productInfoSO.getOrgId()));
        // 按产品条码查
        setProductInfoIdByBottleCode(productInfoSO);

        // 1、获取产品信息列表
        LOGGER.info("产品info列表查询参数：{}", JSON.toJSONString(productInfoSO));
        PageResult<ProductInfoPO> pageResult = productInfoPOMapper.listProductInfo(productInfoSO);
        List<ProductInfoDTO> dtoList = pageResult.toPageList().getDataList().stream()
            .map(ProductBySaasConvertor::convertToProductInfoDTO).collect(Collectors.toList());
        LOGGER.info("产品info列表查询结果：{}", JSON.toJSONString(dtoList));
        // 2、查询产品信息下对应的所有规格
        if (CollectionUtils.isNotEmpty(dtoList)) {
            // 批量查询所有信息规格
            List<Long> productInfoIds = dtoList.stream().map(p -> p.getId()).collect(Collectors.toList());
            List<ProductInfoSpecificationPO> specificationList =
                productInfoSpecificationPOMapper.listByProductInfoIds(productInfoIds);

            // 查询产品信息的条码
            Map<Long, List<ProductCodeInfoDTO>> productCodeMap = getProductCodeMap(productInfoIds);

            dtoList.forEach(dto -> {
                // 获取条码
                List<ProductCodeInfoDTO> productCodeList = new ArrayList<>();
                if (productCodeMap != null && CollectionUtils.isNotEmpty(productCodeMap.get(dto.getId()))) {
                    productCodeList = productCodeMap.get(dto.getId());
                }

                // 找到指定产品信息下的规格
                if (CollectionUtils.isNotEmpty(specificationList)) {
                    List<ProductInfoSpecificationPO> specificationPOList = specificationList.stream()
                        .filter(p -> Objects.equals(p.getProductInfo_Id(), dto.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specificationPOList)) {
                        List<ProductInfoSpecificationDTO> specificationDTOList =
                            getProductInfoSpecificationDTOS(productCodeList, specificationPOList);
                        dto.setSpecificationList(specificationDTOList);
                    }
                }
                // 设置产品图片信息
                setProdcutInfoImg(dto);
                // 设置产品瓶码（最小单位条码）
                setBottleCode(dto, productCodeList);
            });
        }

        PageList<ProductInfoDTO> pageList = new PageList<>();
        pageList.setPager(pageResult.getPager());
        pageList.setDataList(dtoList);
        return pageList;
    }

    private List<ProductInfoSpecificationDTO> getProductInfoSpecificationDTOS(List<ProductCodeInfoDTO> productCodeList,
        List<ProductInfoSpecificationPO> specificationPOList) {
        List<ProductInfoSpecificationDTO> specificationDTOList = new ArrayList<>();
        for (ProductInfoSpecificationPO specPO : specificationPOList) {
            ProductInfoSpecificationDTO specificationDTO = ProductBySaasConvertor.convertToProductInfoSpecDTO(specPO);
            // 设置包装条码
            setBarCode(productCodeList, specificationDTO);
            specificationDTOList.add(specificationDTO);
        }
        return specificationDTOList;
    }

    /**
     * 获取产品条码和箱码
     * 
     * @return
     */
    private Map<Long, List<ProductCodeInfoDTO>> getProductCodeMap(List<Long> productInfoIds) {
        ProductCodeQueryConditionDTO queryConditionDTO = new ProductCodeQueryConditionDTO();
        queryConditionDTO.setProductInfoIdList(productInfoIds);
        queryConditionDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType().intValue());
        queryConditionDTO.setIsDelete(WhetherEnum.NO.getType().intValue());
        return productCodeInfoBL.findProductCodeInfoByCondition(queryConditionDTO);
    }

    /**
     * 设置产品条码
     */
    private void setBottleCode(ProductInfoDTO dto, List<ProductCodeInfoDTO> productCodeList) {
        List<String> bottleCodeList = productCodeList.stream()
            .filter(p -> p != null && Objects.equals(p.getCodeType(), ProductCodeTypeEnum.BAR_CODE.getType()))
            .map(p -> p.getCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bottleCodeList)) {
            dto.setBottleCodeList(bottleCodeList);
            // 用","拼接
            StringBuilder sb = new StringBuilder();
            bottleCodeList.forEach(code -> {
                sb.append(",").append(code);
            });
            dto.setBottleCode(sb.substring(1));
        }
    }

    /**
     * 设置包装条码
     */
    private void setBarCode(List<ProductCodeInfoDTO> productCodeList, ProductInfoSpecificationDTO specificationDTO) {
        // 获取包装条码
        List<String> boxCodeList = productCodeList.stream()
            .filter(p -> p != null && Objects.equals(p.getCodeType(), ProductCodeTypeEnum.BOX_CODE.getType())
                && Objects.equals(p.getProductSpecificationId(), specificationDTO.getId()))
            .map(p -> p.getCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(boxCodeList)) {
            // 用","拼接
            StringBuilder sb = new StringBuilder();
            boxCodeList.forEach(code -> {
                sb.append(",").append(code);
            });
            specificationDTO.setBarCode(sb.substring(1));
        }
    }

    /**
     * 根据产品条码，获取产品信息ID集合
     */
    private void setProductInfoIdByBottleCode(ProductInfoSO productInfoSO) {
        if (StringUtils.isEmpty(productInfoSO.getBottleCode())) {
            return;
        }
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setCode(productInfoSO.getBottleCode());
        conditionDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
        List<ProductCodeInfoDTO> productCodeInfoDTOS =
            productCodeInfoBL.findCodesByCommonDefaultCondition(conditionDTO);
        if (!CollectionUtils.isEmpty(productCodeInfoDTOS)) {
            List<Long> productInfoIds =
                productCodeInfoDTOS.stream().map(p -> p.getProductInfoId()).collect(Collectors.toList());
            // 条码对应的所有产品信息ID
            if (!CollectionUtils.isEmpty(productInfoIds)) {
                LOGGER.info("按产品条码查 productInfoIds：{}", JSON.toJSONString(productInfoIds));
                productInfoSO.setProductInfoIdList(productInfoIds);
            }
        }
    }

    /**
     * 获取城市id
     * 
     * @return
     */
    private Integer getParentOrgId(Integer orgId) {
        // 获取组织机构ID
        Integer parentOrgId = iOrgService.findParentOrgBy(orgId);
        LOGGER.info("parentOrgId：{}", parentOrgId);
        return parentOrgId;
    }

    /**
     * 获取产品信息详情
     * 
     * @return
     */
    public ProductInfoDTO getProductInfo(Long productInfoId) {
        AssertUtils.notNull(productInfoId, "产品信息ID不能为空");

        // 1、获取产品信息
        ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(productInfoId);
        ProductInfoDTO productInfoDTO = ProductBySaasConvertor.convertToProductInfoDTO(productInfoPO);
        if (productInfoDTO == null) {
            return null;
        }

        // 查询产品信息的条码
        List<Long> productInfoIds = new ArrayList<>();
        productInfoIds.add(productInfoId);
        Map<Long, List<ProductCodeInfoDTO>> productCodeMap = getProductCodeMap(productInfoIds);
        List<ProductCodeInfoDTO> productCodeList = new ArrayList<>();
        if (productCodeMap != null && CollectionUtils.isNotEmpty(productCodeMap.get(productInfoId))) {
            productCodeList = productCodeMap.get(productInfoId);
        }
        // 设置产品瓶码（最小单位条码）
        setBottleCode(productInfoDTO, productCodeList);
        // 设置产品图片信息
        setProdcutInfoImg(productInfoDTO);

        // 2、获取产品信息规格
        List<ProductInfoSpecificationPO> specificationPOList =
            productInfoSpecificationPOMapper.listByProductInfoIds(Arrays.asList(productInfoId));
        if (CollectionUtils.isNotEmpty(specificationPOList)) {
            List<ProductInfoSpecificationDTO> specificationDTOList =
                getProductInfoSpecificationDTOS(productCodeList, specificationPOList);
            productInfoDTO.setSpecificationList(specificationDTOList);
        }
        return productInfoDTO;
    }

    /**
     * 设置产品图片信息
     * 
     * @return
     */
    private void setProdcutInfoImg(ProductInfoDTO productInfoDTO) {
        if (productInfoDTO == null || StringUtils.isEmpty(productInfoDTO.getDefaultImageFileId())) {
            return;
        }
        try {
            List<FileInfoDTO> fileInfoDTOS = fileService.listImg(productInfoDTO.getDefaultImageFileId());
            if (CollectionUtils.isNotEmpty(fileInfoDTOS)) {
                List<ProductInfoImgDTO> productInfoImgDTOS = fileInfoDTOS.stream().map(p -> {
                    ProductInfoImgDTO productInfoImgDTO = new ProductInfoImgDTO();
                    productInfoImgDTO.setId(p.getId());
                    productInfoImgDTO.setCloudSrc(p.getCloudSrc());
                    productInfoImgDTO.setBussinessId(p.getBussinessId());
                    return productInfoImgDTO;
                }).collect(Collectors.toList());
                // 设置产品图片信息
                productInfoDTO.setImgList(productInfoImgDTOS);
            }
        } catch (Exception e) {
            LOGGER.error("获取图片信息异常", e);
        }
    }

    /**
     * 批量新增产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductInfoBatch(List<ProductInfoDTO> productInfoDTOList) {
        AssertUtils.notEmpty(productInfoDTOList, "产品信息不能为空");
        // 判断产品编码是否重复
        validateProductInfoRepeat(productInfoDTOList);

        LOGGER.info("【SAAS】批量新增产品信息参数：{}", JSON.toJSONString(productInfoDTOList));
        productInfoDTOList.forEach(p -> {
            // 新增产品信息
            saveProductInfo(p);
        });
    }

    /**
     * 判断产品编码是否重复
     */
    private void validateProductInfoRepeat(List<ProductInfoDTO> productInfoDTOList) {
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return;
        }
        List<String> productCodeList = new ArrayList<>();
        productInfoDTOList.forEach(p -> {
            // 判断产品编码是否重复
            if (StringUtils.isNotEmpty(p.getProductCode())) {
                if (productCodeList.contains(p.getProductCode())) {
                    throw new BusinessValidateException("产品编码（" + p.getProductCode() + "）不能重复，请重新输入！");
                }
                productCodeList.add(p.getProductCode());
            }
        });
    }

    /**
     * 新增产品信息
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Long saveProductInfo(ProductInfoDTO productInfoDTO) {
        // 校验参数
        validateProductInfo(productInfoDTO);

        if (productInfoDTO.getParentOrgId() == null) {
            productInfoDTO.setParentOrgId(getParentOrgId(productInfoDTO.getOrgId()));
        }

        LOGGER.info("【SAAS】新增产品信息参数：{}", JSON.toJSONString(productInfoDTO));

        // 1、新增产品信息
        ProductInfoPO productInfoPO = ProductBySaasConvertor.convertToProductInfoPO(productInfoDTO);
        if (productInfoPO == null) {
            LOGGER.warn("【SAAS】新增产品信息失败");
            return null;
        }
        if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
            throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
        }

        productInfoPOMapper.insertSelective(productInfoPO);
        LOGGER.info("【SAAS】新增产品信息：{}", JSON.toJSONString(productInfoPO));

        // 2、保存产品条码
        saveBottleCode(productInfoPO);

        // 3、新增产品信息规格
        saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoPO.getId());

        // 4、保存产品类目
        saveProductCategory(productInfoPO, productInfoDTO.getOrgId());

        return productInfoPO.getId();
    }

    /**
     * 保存产品类目
     * 
     * @return
     */
    public void saveProductCategory(ProductInfoPO productInfoPO, Integer orgId) {
        setCateGoryIdByName(productInfoPO, orgId);
        LOGGER.info("【SAAS】新增类目参数：{}", JSON.toJSONString(productInfoPO));
        CategorySync categorySync = ProductCategoryConvertor.ProductInfoPO2SyncCategoryTree(productInfoPO, orgId);
        if (categorySync == null) {
            return;
        }
        List<CategorySync> categorySyncs = new ArrayList<>();
        categorySyncs.add(categorySync);
        productCategoryGroupBL.syncCategoryTree(categorySyncs);
        LOGGER.info("【SAAS】保存产品类目：{}", JSON.toJSONString(categorySyncs));
    }

    /**
     * 校验是否类目存在，不存在则新建
     * 
     * @param productInfoPO
     * @param orgId
     */
    private void setCateGoryIdByName(ProductInfoPO productInfoPO, Integer orgId) {
        if (productInfoPO.getProductStatisticsClass() != null) {
            return;
        }
        // 格式:白酒-茅台,白酒-剑南春
        String statisticsCategoryName = productInfoPO.getStatisticsCategoryName();
        if (StringUtils.isEmpty(statisticsCategoryName)) {
            return;
        }
        Long categoryGroupId = productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(orgId, null);
        String[] str = statisticsCategoryName.split("-");
        if (str.length > 0) {
            ProductCategoryGroupPO productCategoryGroupPO =
                productCategoryGroupBL.findCategoryByName(str[0], null, categoryGroupId, orgId);
            if (productCategoryGroupPO != null) {
                productInfoPO.setProductStatisticsClass(productCategoryGroupPO.getId());
            } else {
                productInfoPO.setProductStatisticsClass(addCategoryGroup(str[0], categoryGroupId));
            }
        }
        if (str.length > 1) {
            ProductCategoryGroupPO itemPO = productCategoryGroupBL.findCategoryByName(str[1],
                productInfoPO.getProductStatisticsClass(), categoryGroupId, orgId);
            if (itemPO != null) {
                productInfoPO.setSecondStatisticsClass(itemPO.getId());
            } else {
                productInfoPO.setProductStatisticsClass(addCategoryGroup(str[1], categoryGroupId));
            }
        }
    }

    private Long addCategoryGroup(String name, Long categoryGroupId) {
        LOGGER.info("【SAAS】类目不存在，新增name:{},categoryGroupId：{}", name, categoryGroupId);
        Long id = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CATEGORY_GROUP);
        ProductCategoryGroupDTO productCategoryGroupDTO = new ProductCategoryGroupDTO();
        productCategoryGroupDTO.setName(name);
        productCategoryGroupDTO.setCategoryGroupId(categoryGroupId);
        productCategoryGroupDTO.setSequence(99999);
        productCategoryGroupDTO.setId(id);
        productCategoryGroupDTO.setRefCategoryId(id.toString());
        ProductCategoryGroupDTO categoryGroupDTO =
            productCategoryGroupBL.addProductCategoryGroup(productCategoryGroupDTO);
        return categoryGroupDTO.getId();
    }

    /**
     * 保存产品条码
     */
    public void saveBottleCode(ProductInfoPO productInfoPO) {
        if (StringUtils.isEmpty(productInfoPO.getBottleCode())) {
            return;
        }

        String[] arr = productInfoPO.getBottleCode().split(",");
        if (arr == null || arr.length == 0) {
            return;
        }
        SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
        syncProductCodeInfoDTO.setProductInfoId(productInfoPO.getId());
        List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
        for (String code : arr) {
            SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
            codeInfoItemDTO.setCode(code);
            codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
            codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
            codeInfoItemDTO.setProductInfoId(productInfoPO.getId());
            codeItemDTOList.add(codeInfoItemDTO);
        }
        syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);

        // 保存条码
        List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
        syncDTOList.add(syncProductCodeInfoDTO);
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
        LOGGER.info("【SAAS】保存产品条码：{}", JSON.toJSONString(syncDTOList));
    }

    /**
     * 校验参数
     */
    private void validateProductInfo(ProductInfoDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO, "参数不能为空");
        AssertUtils.notNull(productInfoDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(productInfoDTO.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productInfoDTO.getProductCode(), "产品编码不能为空");
        AssertUtils.notNull(productInfoDTO.getStatus(), "产品状态不能为空");
        AssertUtils.notNull(productInfoDTO.getCreateUserId(), "创建人ID不能为空");

        AssertUtils.notEmpty(productInfoDTO.getSpecificationList(), "产品信息规格不能为空");
        productInfoDTO.getSpecificationList().forEach(p -> {
            AssertUtils.notNull(p.getName(), "包装规格名称不能为空");
            AssertUtils.notNull(p.getState(), "包装规格状态不能为空");
            AssertUtils.notNull(p.getPackageName(), "包装规格大单位不能为空");
            AssertUtils.notNull(p.getUnitName(), "包装规格小单位不能为空");
            AssertUtils.notNull(p.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
            p.setCreateUserId(productInfoDTO.getCreateUserId());
        });
    }

    /**
     * 校验参数
     */
    private void validateProductInfo(ProductInfoAndSkuDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO, "参数不能为空");
        AssertUtils.notNull(productInfoDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(productInfoDTO.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productInfoDTO.getProductCode(), "产品编码不能为空");
        AssertUtils.notNull(productInfoDTO.getStatus(), "产品状态不能为空");
        AssertUtils.notNull(productInfoDTO.getCreateUserId(), "创建人ID不能为空");

        AssertUtils.notEmpty(productInfoDTO.getSpecificationList(), "产品信息规格不能为空");
        productInfoDTO.getSpecificationList().forEach(p -> {
            AssertUtils.notNull(p.getName(), "包装规格名称不能为空");
            AssertUtils.notNull(p.getState(), "包装规格状态不能为空");
            AssertUtils.notNull(p.getPackageName(), "包装规格大单位不能为空");
            AssertUtils.notNull(p.getUnitName(), "包装规格小单位不能为空");
            AssertUtils.notNull(p.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
            p.setCreateUserId(productInfoDTO.getCreateUserId());
        });
    }

    /**
     * 新增产品信息规格
     */
    public List<ProductInfoSpecificationPO>
        saveProductInfoSpecification(List<ProductInfoSpecificationDTO> specificationList, Long productInfoId) {
        if (CollectionUtils.isEmpty(specificationList)) {
            return new ArrayList<>();
        }
        // 判断规格名称是否重复
        validateProductInfoSpecRepeat(specificationList);

        // 1、新增或修改产品规格
        List<ProductInfoSpecificationPO> specificationPOList = specificationList.stream().map(p -> {
            ProductInfoSpecificationPO specificationPO = ProductBySaasConvertor.convertToProductInfoSpecPO(p);
            specificationPO.setProductInfo_Id(productInfoId);
            return specificationPO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(specificationPOList)) {
            return new ArrayList<>();
        }
        productInfoSpecificationPOMapper.insertOrUpdateBatch(specificationPOList);
        LOGGER.info("【SAAS】新增或修改产品规格：{}", JSON.toJSONString(specificationPOList));

        // 2、保存包装条码
        saveBarCode(productInfoId, specificationPOList);
        return specificationPOList;
    }

    /**
     * 保存包装条码
     */
    private void saveBarCode(Long productInfoId, List<ProductInfoSpecificationPO> specificationPOList) {
        SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
        syncProductCodeInfoDTO.setProductInfoId(productInfoId);
        List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
        specificationPOList.forEach(p -> {
            if (StringUtils.isNotEmpty(p.getBarCode())) {
                String[] arr = p.getBarCode().split(",");
                if (arr != null && arr.length > 0) {
                    for (String code : arr) {
                        SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
                        codeInfoItemDTO.setCode(code);
                        codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
                        codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BOX_CODE.getType());
                        codeInfoItemDTO.setProductSpecificationId(p.getId());
                        codeItemDTOList.add(codeInfoItemDTO);
                    }
                }
            }
        });
        if (CollectionUtils.isEmpty(codeItemDTOList)) {
            return;
        }
        syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);
        // 保存条码
        List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
        syncDTOList.add(syncProductCodeInfoDTO);
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
        LOGGER.info("【SAAS】保存包装条码：{}", JSON.toJSONString(syncDTOList));
    }

    /**
     * 判断规格名称是否重复
     */
    private void validateProductInfoSpecRepeat(List<ProductInfoSpecificationDTO> specificationList) {
        if (CollectionUtils.isEmpty(specificationList)) {
            return;
        }
        List<String> specName = new ArrayList<>();
        specificationList.forEach(p -> {
            // 判断规格名称是否重复
            if (specName.contains(p.getName())) {
                throw new BusinessValidateException("产品规格名称（" + p.getName() + "）不能重复，请重新输入！");
            }
            specName.add(p.getName());
        });
    }

    /**
     * 修改产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductInfo(ProductInfoDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO, "参数不能为空");
        AssertUtils.notNull(productInfoDTO.getId(), "产品信息ID不能为空");
        LOGGER.info("【SAAS】修改产品信息参数：{}", JSON.toJSONString(productInfoDTO));

        // 1、修改产品信息
        ProductInfoPO productInfoPO = ProductBySaasConvertor.convertToProductInfoPO(productInfoDTO);
        if (productInfoPO == null) {
            LOGGER.warn("【SAAS】修改产品信息失败");
            return;
        }
        productInfoPOMapper.updateByPrimaryKeySelective(productInfoPO);
        LOGGER.info("【SAAS】修改产品信息：{}", JSON.toJSONString(productInfoPO));

        // 2、修改产品名称时，同时更新sku的产品名称
        if (StringUtils.isNotEmpty(productInfoPO.getProductName())) {
            ProductSkuPO productSkuPO = new ProductSkuPO();
            productSkuPO.setProductInfoId(productInfoPO.getId());
            productSkuPO.setName(productInfoPO.getProductName());
            productSkuPO.setSource(ProductSourceType.易酒批);
            productSkuMapper.updateSelective(productSkuPO);
        }

        // 3、保存产品条码
        saveBottleCode(productInfoPO);

        // 4、保存产品类目
        saveProductCategory(productInfoPO, productInfoDTO.getOrgId());
        productSyncOpService.findProductInfoAndSyncOp(productInfoPO.getId(), productInfoDTO.getOrgId(),
            productInfoPO.getCreateUser_Id());
    }

    /**
     * 修改产品包装规格
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductInfoSpec(ProductInfoDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO, "参数不能为空");
        AssertUtils.notNull(productInfoDTO.getId(), "产品信息ID不能为空");
        AssertUtils.notEmpty(productInfoDTO.getSpecificationList(), "产品包装规格列表不能为空");
        LOGGER.info("【SAAS】修改产品规格参数：{}", JSON.toJSONString(productInfoDTO));

        // 修改产品信息规格
        saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoDTO.getId());
    }

    /**
     * 查询城市未同步图片的产品DefaultImageFileId
     * 
     * @param cityId
     * @return
     */
    public List<Integer> getDefaultImageFileId(String cityId) {
        return productInfoPOMapper.getDefaultImageFileId(cityId);
    }

    /**
     * 批量新增产品信息
     */
    public List<ProductSkuDTO> saveProductInfoAndThird(List<ProductUploadDTO> productInfoDTOList) {
        List<ProductSkuDTO> skuList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return skuList;
        }
        Integer orgId = productInfoDTOList.get(0).getOrgId();
        Integer warehouseId = productInfoDTOList.get(0).getWarehouseId();
        String sysCode = productInfoDTOList.get(0).getSysCode();
        Integer parentOrgId = iOrgService.findParentOrgBy(orgId);
        List<Integer> orgIdList = new ArrayList<>();
        PageList<OrgDTO> pageList = iOrgService.listSelfAndSubById(orgId);
        if (warehouseId != null) {
            orgIdList.add(orgId);
        } else {
            if (pageList != null && CollectionUtils.isNotEmpty(pageList.getDataList())) {
                pageList.getDataList().forEach(it -> orgIdList.add(it.getId()));
            }
        }
        System.out.println("orgList:" + JSON.toJSONString(orgIdList));
        List<String> thirdSkuIds = new ArrayList<>();
        productInfoDTOList.forEach(it -> thirdSkuIds.add(it.getThirdSkuId()));
        Map<String, ThirdproductsRelationDTO> thirdSkuMap =
            getThirdProudctsRelation(parentOrgId, orgId, thirdSkuIds, null, null, sysCode);
        LOGGER.info("【SAAS】批量新增产品信息参数：{}", JSON.toJSONString(productInfoDTOList));
        for (ProductUploadDTO productUploadDTO : productInfoDTOList) {
            /** 如果第三方sku已经存在则不重复导入 */
            String thirdSkuId = productUploadDTO.getThirdSkuId();
            if (StringUtils.isNotEmpty(thirdSkuId)) {
                ThirdproductsRelationDTO thirdproductsRelationDTO = thirdSkuMap.get(thirdSkuId);
                if (thirdproductsRelationDTO != null) {
                    LOGGER.info("【SAAS】第三方产品已经存在：{}", JSON.toJSONString(productInfoDTOList));
                    continue;
                }
            }
            List<ThirdproductsRelationDTO> thirdProducRelations = new ArrayList<>();
            List<ProductSkuDTO> productSkuList = new ArrayList<>();
            ProductInfoDTO productInfoDTO = new ProductInfoDTO();
            BeanUtils.copyProperties(productUploadDTO, productInfoDTO);
            productInfoDTO.setStatus((byte)1);
            // 1、新增产品信息
            ProductInfoPO productInfoPO = ProductBySaasConvertor.convertToProductInfoPO(productInfoDTO);
            if (productInfoPO == null) {
                LOGGER.warn("【SAAS】新增产品信息失败");
                continue;
            }
            List<ProductInfoSpecificationPO> specificationPOList = new ArrayList<>();
            // 校验产品info是否存在，存在则不新增
            List<ProductInfoPO> ExistInfoList = checkInfoExist(productInfoPO.getProductName(), parentOrgId);
            if (CollectionUtils.isEmpty(ExistInfoList)) {
                productInfoPOMapper.insertSelective(productInfoPO);
                LOGGER.info("【SAAS】新增产品信息：{}", JSON.toJSONString(productInfoPO));
                // 2、保存产品条码
                saveBottleCode(productInfoPO);
                // 3、保存产品类目
                saveProductCategory(productInfoPO, productInfoDTO.getOrgId());
                // 4、新增产品信息规格
                specificationPOList =
                    saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoPO.getId());
            } else {
                LOGGER.info("【SAAS】产品信息已经存在：{}", JSON.toJSONString(ExistInfoList));
                Long infoId = ExistInfoList.get(0).getId();
                productInfoPO.setId(infoId);
                specificationPOList = productInfoSpecificationPOMapper.findByProductInfoIds(Arrays.asList(infoId));
                LOGGER.info("【SAAS】产品已存在规格：{}", JSON.toJSONString(specificationPOList));
            }

            // 如果货主不存在，则不生成sku
            String ownerId = productUploadDTO.getOwnerId();
            String ownerName = productUploadDTO.getOwnerName();
            if (StringUtils.isNotEmpty(ownerId) && StringUtils.isNotEmpty(ownerName)) {
                specificationPOList.forEach(it -> {
                    ProductSkuDTO productSkuDTO = new ProductSkuDTO();
                    productSkuDTO.setProductSpecificationId(it.getId());
                    productSkuDTO.setProductInfoId(productInfoPO.getId());
                    productSkuList.add(productSkuDTO);
                });
                // 5、新增产品sku
                List<ProductSkuPO> skuPOList =
                    productSkuBL.saveProductSku(orgIdList, ownerId, ownerName, productSkuList);
                skuPOList.forEach(it -> {
                    ProductSkuDTO productSkuDTO = new ProductSkuDTO();
                    BeanUtils.copyProperties(it, productSkuDTO);
                    skuList.add(productSkuDTO);
                });
                LOGGER.info("【SAAS】产品SKU：{}", JSON.toJSONString(skuPOList));
                if (thirdSkuId != null) {
                    skuPOList.forEach(skuPO -> {
                        ThirdproductsRelationDTO relationDTO = new ThirdproductsRelationDTO();
                        relationDTO.setSysCode(productUploadDTO.getSysCode());
                        relationDTO.setSysName(productUploadDTO.getSysName());
                        relationDTO.setOrgId(parentOrgId);
                        relationDTO.setCityId(orgId);
                        relationDTO.setThirdskuId(thirdSkuId);
                        relationDTO.setProductskuId(skuPO.getProductSkuId());
                        relationDTO.setProductinfoId(skuPO.getProductInfoId());
                        relationDTO.setSpecificationId(skuPO.getProductSpecificationId());
                        thirdProducRelations.add(relationDTO);
                    });
                    // 6、新增产品第三方关联
                    saveThirdProductsRelation(thirdProducRelations, parentOrgId);
                }
            }
        }
        productSyncOpService.findProductInfoAndSyncOp(productInfoDTOList);
        return skuList;
    }

    private List<ProductInfoPO> checkInfoExist(String productName, Integer parentOrgId) {
        ProductInfoSO productInfoSO = new ProductInfoSO();
        productInfoSO.setProductFullName(productName);
        productInfoSO.setParentOrgId(parentOrgId);
        LOGGER.info("【SAAS】产品信息查询是否存在参数:：{}", JSON.toJSONString(productInfoSO));
        PageResult<ProductInfoPO> infoPageResult = productInfoPOMapper.listProductInfo(productInfoSO);
        if (infoPageResult != null && infoPageResult.toPageList() != null) {
            return infoPageResult.toPageList().getDataList();
        }
        return new ArrayList<>();
    }

    /**
     * 保存第三方产品关系
     *
     * @param thirdProducRelations
     */
    private void saveThirdProductsRelation(List<ThirdproductsRelationDTO> thirdProducRelations, Integer parentOrgId) {
        if (CollectionUtils.isEmpty(thirdProducRelations)) {
            return;
        }
        ThirdproductsRelationQueryDTO queryDTO = new ThirdproductsRelationQueryDTO();
        List<String> thirdskuIdList = new ArrayList<>();
        List<ThirdproductsRelationDTO> hasThirdSkuList =
            thirdProducRelations.stream().filter(it -> it.getThirdskuId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasThirdSkuList)) {
            return;
        }
        hasThirdSkuList.forEach(it -> {
            thirdskuIdList.add(it.getThirdskuId());
        });
        queryDTO.setOrgId(parentOrgId);
        queryDTO.setThirdskuIdList(thirdskuIdList);
        List<ThirdproductsRelationDTO> list = thirdproductsRelationBL.list(queryDTO);
        Map<String, List<ThirdproductsRelationDTO>> listMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(list)) {
            listMap = list.stream().collect(Collectors.groupingBy(ThirdproductsRelationDTO::getThirdskuId));
        }
        List<ThirdproductsRelationDTO> needSave = new ArrayList<>();
        for (ThirdproductsRelationDTO dto : hasThirdSkuList) {
            System.out.println(JSON.toJSONString(dto));
            List<ThirdproductsRelationDTO> relations = listMap.get(dto.getThirdskuId());
            if (CollectionUtils.isNotEmpty(relations)) {
                List<ThirdproductsRelationDTO> exists = relations.stream()
                    .filter(rel -> rel.getThirdskuId().equals(dto.getThirdskuId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(exists)) {
                    needSave.add(dto);
                }
            } else {
                needSave.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(needSave)) {
            thirdproductsRelationBL.insertBatch(needSave);
        }
        LOGGER.info("【SAAS】新增产品第三方关联：{}", JSON.toJSONString(needSave));
    }

    /**
     * 新增产品信息
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Long saveProductInfoAndSku(ProductInfoDTO productInfoDTO) {
        // 校验参数
        validateProductInfo(productInfoDTO);
        Integer orgId = productInfoDTO.getOrgId();

        if (productInfoDTO.getParentOrgId() == null) {
            productInfoDTO.setParentOrgId(getParentOrgId(orgId));
        }

        LOGGER.info("【SAAS】新增产品信息参数：{}", JSON.toJSONString(productInfoDTO));
        // 1、新增产品信息
        ProductInfoPO productInfoPO = ProductBySaasConvertor.convertToProductInfoPO(productInfoDTO);
        if (productInfoPO == null) {
            LOGGER.warn("【SAAS】新增产品信息失败");
            return null;
        }
        if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
            throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
        }
        productInfoPOMapper.insertSelective(productInfoPO);
        LOGGER.info("【SAAS】新增产品信息：{}", JSON.toJSONString(productInfoPO));
        // 2、保存产品条码
        saveBottleCode(productInfoPO);
        // 3、新增产品信息规格
        List<ProductInfoSpecificationPO> specificationPOS =
            saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoPO.getId());
        // 4、保存产品类目
        saveProductCategory(productInfoPO, orgId);

        List<ProductSkuDTO> productSkuList = new ArrayList<>();
        specificationPOS.forEach(it -> {
            ProductSkuDTO productSkuDTO = new ProductSkuDTO();
            productSkuDTO.setProductSpecificationId(it.getId());
            productSkuDTO.setProductInfoId(productInfoPO.getId());
            productSkuList.add(productSkuDTO);
        });

        // 5、新增产品sku
        OwnerDTO owner = getDefaultOwner(productInfoDTO.getParentOrgId());
        if (owner != null) {
            productSkuBL.saveProductSku(Arrays.asList(orgId), owner.getId().toString(), owner.getOwnerName(),
                productSkuList);
        }

        productSyncOpService.findProductInfoAndSyncOp(productInfoPO.getId(), orgId, productInfoPO.getCreateUser_Id());
        return productInfoPO.getId();
    }

    private OwnerDTO getDefaultOwner(Integer parentOrgId) {
        OwnerQueryDTO dto = new OwnerQueryDTO();
        dto.setCityId(parentOrgId);
        dto.setIsDefault((byte)1);
        PageList<OwnerDTO> pageList = ownerBL.findOwnerByCondition(dto);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return null;
        }
        return pageList.getDataList().get(0);
    }

    /**
     * 获取第三方产品关联
     *
     * @param parentOrgId
     * @param thirdskuIdList
     * @param thridInfoId
     * @param thridspecId
     * @return
     */
    private Map<String, ThirdproductsRelationDTO> getThirdProudctsRelation(Integer parentOrgId, Integer cityId,
        List<String> thirdskuIdList, String thridInfoId, String thridspecId, String sysCode) {
        Map<String, ThirdproductsRelationDTO> thirdSkuMap = new HashMap<>(16);
        if (CollectionUtils.isEmpty(thirdskuIdList)) {
            return thirdSkuMap;
        }
        ThirdproductsRelationQueryDTO queryDTO = new ThirdproductsRelationQueryDTO();
        queryDTO.setOrgId(parentOrgId);
        queryDTO.setThirdinfoId(thridInfoId);
        queryDTO.setThirdspecificationId(thridspecId);
        queryDTO.setThirdskuIdList(thirdskuIdList);
        queryDTO.setSysCode(sysCode);
        queryDTO.setCityId(cityId);
        List<ThirdproductsRelationDTO> list = thirdproductsRelationBL.list(queryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return thirdSkuMap;
        }
        for (ThirdproductsRelationDTO relationDTO : list) {
            String key = relationDTO.getThirdskuId();
            ThirdproductsRelationDTO dto = thirdSkuMap.get(key);
            if (dto == null) {
                thirdSkuMap.put(key, dto);
            }
        }
        return thirdSkuMap;
    }

    /**
     * 简单查询产品信息数据
     * 
     * @param productInfoSO
     * @return
     */
    public PageList<ProductInfoDTO> listSimpleProductInfo(ProductInfoSO productInfoSO) {
        PageList<ProductInfoDTO> result = new PageList<>();
        AssertUtils.notNull(productInfoSO, "参数不能为空");
        // 1、获取产品信息列表
        LOGGER.info("产品info列表查询参数：{}", JSON.toJSONString(productInfoSO));
        PageResult<ProductInfoPO> pageResult = productInfoPOMapper.listProductInfo(productInfoSO);
        LOGGER.info("产品info列表查询结果：{}", JSON.toJSONString(pageResult));
        PageList<ProductInfoPO> pageList = pageResult.toPageList();
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return result;
        }
        List<ProductInfoDTO> dtoList = pageList.getDataList().stream()
            .map(ProductBySaasConvertor::convertToProductInfoDTO).collect(Collectors.toList());
        result.setDataList(dtoList);
        result.setPager(pageList.getPager());
        return result;
    }

    /**
     * 新增产品信息
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Long saveProductInfoAndSkuWithWarehouseId(ProductInfoAndSkuDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(productInfoDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(productInfoDTO.getOwnerId(), "货主id不能为空");

        // 校验参数
        Integer warehouseId = productInfoDTO.getWarehouseId();
        validateProductInfo(productInfoDTO);
        Integer orgId = productInfoDTO.getOrgId();

        if (productInfoDTO.getParentOrgId() == null) {
            productInfoDTO.setParentOrgId(getParentOrgId(orgId));
        }

        LOGGER.info("【SAAS】新增产品信息参数：{}", JSON.toJSONString(productInfoDTO));
        // 1、新增产品信息
        ProductInfoPO productInfoPO = ProductBySaasConvertor.convertToProductInfoPO(productInfoDTO);
        if (productInfoPO == null) {
            LOGGER.warn("【SAAS】新增产品信息失败");
            return null;
        }
        if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
            throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
        }
        productInfoPOMapper.insertSelective(productInfoPO);
        LOGGER.info("【SAAS】新增产品信息：{}", JSON.toJSONString(productInfoPO));
        // 2、保存产品条码
        saveBottleCode(productInfoPO);
        // 3、新增产品信息规格
        List<ProductInfoSpecificationPO> specificationPOS =
            saveProductInfoSpecification(new ArrayList<>(productInfoDTO.getSpecificationList()), productInfoPO.getId());
        // 4、保存产品类目
        saveProductCategory(productInfoPO, orgId);
        Map<String, List<ProductInfoSpecificationPO>> specificationMap =
            specificationPOS.stream().filter(it -> StringUtils.isNotEmpty(it.getName()))
                .collect(Collectors.groupingBy(ProductInfoSpecificationPO::getName));
        List<ProductSkuDTO> productSkuList = new ArrayList<>();
        productInfoDTO.getSpecificationList().forEach(it -> {
            ProductSkuDTO productSkuDTO = new ProductSkuDTO();
            productSkuDTO.setProductInfoId(productInfoPO.getId());
            productSkuDTO.setStorageType(productInfoPO.getStorageType());
            List<ProductInfoSpecificationPO> pos = specificationMap.get(it.getName());
            if (CollectionUtils.isNotEmpty(pos)) {
                productSkuDTO.setProductSpecificationId(pos.get(0).getId());
            }
            productSkuDTO.setWarehouseCustodyFee(it.getWarehouseCustodyFee());
            productSkuDTO.setDeliveryFee(it.getDeliveryFee());
            productSkuDTO.setDeliveryPayType(it.getDeliveryPayType());
            productSkuDTO.setSortingFee(it.getSortingFee());
            productSkuDTO.setUnpackage(it.getUnpackage());
            productSkuDTO.setProductFeature(it.getProductFeature());
            productSkuDTO.setMaxInventory(it.getMaxInventory());
            productSkuDTO.setMinInventory(it.getMinInventory());
            productSkuDTO.setMaxReplenishment(it.getMaxReplenishment());
            productSkuDTO.setMinReplenishment(it.getMinReplenishment());
            productSkuDTO.setIsComplete(it.getIsComplete());
            productSkuDTO.setPick(it.getPick());
            productSkuDTO.setSow(it.getSow());
            productSkuDTO.setInventoryRatio(it.getInventoryRatio());
            productSkuDTO.setUnique(it.getUnique());
            productSkuDTO.setFleeGoods(it.getFleeGoods());
            productSkuDTO.setProductRelevantState(it.getProductRelevantState());
            productSkuDTO.setProductGrade(it.getProductGrade());
            productSkuDTO.setCostPrice(it.getCostPrice());
            productSkuDTO.setSellingPrice(it.getSellingPrice());
            productSkuDTO.setSellingPriceUnit(it.getSellingPriceUnit());
            productSkuList.add(productSkuDTO);
        });
        // 5、新增产品sku
        productSkuBL.saveProductSku(warehouseId, orgId, productInfoDTO.getOwnerId().toString(),
            productInfoDTO.getOwnerName(), productSkuList);
        productSyncOpService.findProductInfoAndSyncOp(productInfoPO.getId(), orgId, productInfoPO.getCreateUser_Id());
        return productInfoPO.getId();
    }

    /**
     * 删除产品信息及规格
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteProductInfo(ProductInfoDTO productInfoDTO) {
        AssertUtils.notNull(productInfoDTO, "参数不能为空");
        AssertUtils.notNull(productInfoDTO.getId(), "产品信息ID不能为空");
        LOGGER.info("删除产品信息及规格 入参：{}", JSON.toJSONString(productInfoDTO));

        Long productInfoId = productInfoDTO.getId();
        ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(productInfoId);
        if (productInfoPO == null) {
            throw new BusinessValidateException("产品信息id不存在！");
        }

        List<ProductInfoSpecificationPO> specificationPOS =
            productInfoSpecificationPOMapper.findByProductInfoIds(Arrays.asList(productInfoId));
        if (CollectionUtils.isEmpty(specificationPOS)) {
            throw new BusinessValidateException("产品信息id对应规格信息不存在！");
        }

        productInfoPOMapper.deleteByPrimaryKey(productInfoId);
        specificationPOS.stream().forEach(p -> {
            productInfoSpecificationPOMapper.deleteByPrimaryKey(p.getId());
        });
        LOGGER.info("删除产品信息及规格 结束");
    }

}
