package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IDisplayCategoryQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.product.ScmProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.zhzg.ProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoDisplayCategory;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoMain;
import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoCategoryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ParentOrgIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductInfoCategoryBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoCategoryBL.class);

    @Autowired
    private ProductInfoCategoryMapper productInfoCategoryMapper;

    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @Autowired
    private ProductSkuBL productSkuBL;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductInfoQueryService iProductInfoQueryService;

    @ReferGateway(path = ServerPath.ZHZG)
    private ProductInfoQueryService productInfoQueryService;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductInfoQueryService scmProductInfoQueryService;

    @Reference
    private IVariableValueService variableValueService;

    /**
     * 获取灰度仓库的KEY
     */
    private static final String DEFAULT_ORG = "DefaultCategoryOrgId";

    /**
     * 分页条件查询产品类目信息
     * 
     * @param productInfoCategoryQueryDTO
     * @return
     */
    public PageList<ProductInfoCategoryDTO>
        pageListProductInfoCategory(ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO) {
        setProductCategoryGroupId(productInfoCategoryQueryDTO);
        PageResult<ProductInfoCategoryDTO> productInfoCategoryDTOS =
            productInfoCategoryMapper.pageListProductInfoCategory(productInfoCategoryQueryDTO);
        PageList<ProductInfoCategoryDTO> result = new PageList<>();
        result.setDataList(productInfoCategoryDTOS.getResult());
        result.setPager(productInfoCategoryDTOS.getPager());
        return result;
    }

    /**
     * 根据skuid查询产品类目信息
     * 
     * @param productInfoCategoryQueryDTO
     * @return
     */
    public List<ProductInfoCategoryDTO>
        findProductCategoryBySkuIds(ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO) {
        return productInfoCategoryMapper.findProductCategoryBySkuIds(productInfoCategoryQueryDTO);
    }

    private void setProductCategoryGroupId(ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO) {
        Long categoryGroupId = null;
        if (productInfoCategoryQueryDTO.getCategoryGroupId() != null) {
            categoryGroupId = productInfoCategoryQueryDTO.getCategoryGroupId();
        } else if (productInfoCategoryQueryDTO.getOrgId() != null) {
            categoryGroupId =
                productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(productInfoCategoryQueryDTO.getOrgId(), null);
        } else if (productInfoCategoryQueryDTO.getWarehouseId() != null) {
            categoryGroupId = productCategoryGroupConfigBL
                .getCategoryGroupIdByWarehouseId(productInfoCategoryQueryDTO.getWarehouseId(), null);
        } else {
            AssertUtils.notNull(productInfoCategoryQueryDTO.getOrgId(), "城市id不能为空");
            AssertUtils.notNull(productInfoCategoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        }

        productInfoCategoryQueryDTO.setCategoryGroupId(categoryGroupId);
        if (productInfoCategoryQueryDTO.getCategoryGroupId() == null) {
            LOG.info("找不到对应的类目分组id,查询参数:{}", JSON.toJSONString(productInfoCategoryQueryDTO));
            throw new BusinessValidateException("找不到对应的类目分组id");
        }
    }

    /**
     * 回写productInfoCategory信息
     */
    public void setProductInfoCategoryIdBySku(ProductSkuPO productSkuPO, Long productCategoryGroupId) {
        if (productSkuPO == null || productSkuPO.getProductInfoId() == null) {
            return;
        }
        ProductInfoCategoryPO productInfoCategoryPO =
            productInfoCategoryMapper.getByInfoId(productSkuPO.getProductInfoId(), productCategoryGroupId);
        if (productInfoCategoryPO == null) {
            LOG.info("未找到产品信息关联类目:{}", JSON.toJSONString(productSkuPO));
            return;
        }
        productSkuPO.setProductInfoCategoryId(productInfoCategoryPO.getId());
    }

    private String getLogTypeName(Integer source) {
        if (Objects.equals(source, ProductSourceType.易酒批)) {
            return "易酒批";
        } else if (Objects.equals(source, ProductSourceType.知花知果)) {
            return "知花知果";
        }
        return null;
    }

    /**
     * 同步产品类目
     * 
     * @param source 0-易酒批 2-知花知果
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncProductInfoCategory(Integer source) {
        AssertUtils.notNull(source, "来源不能为空");
        String logName = getLogTypeName(source);
        LOG.info("【同步产品类目 - {}】开始", logName);
        long start = System.currentTimeMillis();

        // 1、获取产品InfoId
        List<Long> infoIdList = productSkuBL.listProductInfoIdBySource(source);
        if (CollectionUtils.isEmpty(infoIdList)) {
            LOG.info("【同步产品类目】找不到产品infoId");
        }
        long first = System.currentTimeMillis();
        LOG.info("【同步产品类目 - {}】第一步，查询产品infoId总条数：{}，耗时：{}ms", logName, infoIdList.size(), first - start);

        // 2、根据产品信息id去获取类目信息
        // 拆成50个一组去批量同步
        List<List<Long>> allList = splitList(infoIdList, 50);
        if (Objects.equals(source, ProductSourceType.易酒批)) {
            processCatetorySyncByYJP(allList);
        } else if (Objects.equals(source, ProductSourceType.知花知果)) {
            processCatetorySyncByZHZG(allList);
        }

        long second = System.currentTimeMillis();
        LOG.info("【同步产品类目 - {}】第二步，同步类目，耗时：{}ms", logName, second - first);

        LOG.info("【同步产品类目 - {}】结束，总耗时：{}ms", logName, second - start);
    }

    /**
     * 同步易酒批产品类目
     */
    private void processCatetorySyncByYJP(List<List<Long>> allList) {
        VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
        valueQuery.setVariableKey(DEFAULT_ORG);
        VariableDefAndValueDTO value = variableValueService.detailVariable(valueQuery);
        allList.forEach(infoIds -> {

            // （1）调交易接口获取类目信息
            Set<Integer> infoSets = infoIds.stream().filter(p -> p != null && p <= Integer.MAX_VALUE)
                .map(p -> p.intValue()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(infoSets)) {
                return;
            }
            Map<Integer, ProductInfoMain> productInfoMainMap = null;
            try {
                productInfoMainMap = iProductInfoQueryService.getProductInfoMainMap(infoSets);
            } catch (Exception e) {
                LOG.error("【同步产品类目】调交易接口异常", e);
                LOG.info("【同步产品类目】交易接口参数：{}", JSON.toJSONString(infoSets));
            }
            if (productInfoMainMap == null || productInfoMainMap.size() == 0) {
                return;
            }

            // （2）执行类目同步
            List<CategorySync> categoryTrees = new ArrayList<>();
            productInfoMainMap.forEach((infoId, infoMain) -> {
                // 设置展示类目名称
                setCategoryName(infoMain.getProductInfoDisplayCategoryList());
                CategorySync categorySync = ProductCategoryConvertor.ProductInfoMain2SyncCategoryTree(infoMain,
                    value == null || StringUtils.isEmpty(value.getVariableData()) ? null
                        : Integer.parseInt(value.getVariableData()));
                if (categorySync != null) {
                    categoryTrees.add(categorySync);
                }
            });
            if (CollectionUtils.isNotEmpty(categoryTrees)) {
                productCategoryGroupBL.syncCategoryTree(categoryTrees);
            }

        });
    }

    /**
     * 同步知花知果产品类目
     */
    private void processCatetorySyncByZHZG(List<List<Long>> allList) {
        allList.forEach(infoIds -> {

            // （1）调知花知果接口获取类目信息
            Set<Long> infoSets = infoIds.stream().filter(p -> p != null).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(infoSets)) {
                return;
            }
            Map<Long, ProductInfoDTO> productInfoDTOMap = null;
            try {
                productInfoDTOMap = productInfoQueryService.getProductInfoMap(infoSets);
            } catch (Exception e) {
                LOG.error("【同步产品类目】调知花知果接口异常", e);
                LOG.info("【同步产品类目】知花知果接口参数：{}", JSON.toJSONString(infoSets));
            }
            if (productInfoDTOMap == null || productInfoDTOMap.size() == 0) {
                return;
            }

            // （2）执行类目同步
            List<CategorySync> categoryTrees = new ArrayList<>();
            productInfoDTOMap.forEach((infoId, infoDTO) -> {
                CategorySync categorySync = ProductCategoryConvertor.ProductInfoDTO2SyncCategoryTree(infoDTO);
                if (categorySync != null) {
                    categoryTrees.add(categorySync);
                }
            });
            if (CollectionUtils.isNotEmpty(categoryTrees)) {
                productCategoryGroupBL.syncCategoryTree(categoryTrees);
            }

        });
    }

    /**
     * 同步产品SKU的类目关系ID
     * 
     * @param source 0-易酒批 2-知花知果
     */
    public void syncProductSkuCategoryId(Integer source) {
        AssertUtils.notNull(source, "来源不能为空");
        String logName = getLogTypeName(source);
        LOG.info("【同步产品SKU的类目关系ID - {}】开始", logName);
        long start = System.currentTimeMillis();

        Integer parentOrgId = null;
        if (Objects.equals(source, ProductSourceType.易酒批)) {
            parentOrgId = ParentOrgIdConstant.YIJIUPI;
        } else if (Objects.equals(source, ProductSourceType.知花知果)) {
            parentOrgId = ParentOrgIdConstant.ZHZG;
        }
        if (parentOrgId == null) {
            throw new BusinessException("组织机构ID获取失败");
        }
        Long categoryGroupId = productCategoryGroupConfigBL.getCategoryGroupIdByParentOrgId(parentOrgId, null);
        if (categoryGroupId == null) {
            throw new BusinessException("类目分组ID获取失败");
        }
        int count = productSkuBL.updateProductSkuOfCatetoryId(categoryGroupId);

        long end = System.currentTimeMillis();
        LOG.info("【同步产品SKU的类目关系ID - {}】结束，更新条数：{}, 总耗时：{}ms", logName, count, end - start);
    }

    /**
     * 设置展示类目名称
     * 
     * @return
     */
    public void setCategoryName(List<ProductInfoDisplayCategory> displayCategoryList) {
        try {
            if (CollectionUtils.isEmpty(displayCategoryList)) {
                return;
            }

            Set<Integer> categoryIds = new HashSet<>();
            displayCategoryList.forEach(displayCategory -> {
                // 一级类目
                if (displayCategory.getFirstCategoryId() != null) {
                    categoryIds.add(displayCategory.getFirstCategoryId());
                }
                // 一级类目
                if (displayCategory.getSecondCategoryId() != null) {
                    categoryIds.add(displayCategory.getSecondCategoryId());
                }
            });
            if (CollectionUtils.isEmpty(categoryIds)) {
                return;
            }
            // 通过类目ID获取展示类目名称
            Map<Integer, String> categoryNameMap =
                scmProductInfoQueryService.productDisplayCategoryIdNameTransfer(categoryIds);
            LOG.info("通过类目ID获取展示类目名称：{}", JSON.toJSONString(categoryNameMap));
            if (categoryNameMap != null) {
                displayCategoryList.forEach(displayCategory -> {
                    // 一级类目
                    if (categoryNameMap.containsKey(displayCategory.getFirstCategoryId())) {
                        displayCategory.setFirstCategoryName(categoryNameMap.get(displayCategory.getFirstCategoryId()));
                    }
                    // 二级类目
                    if (categoryNameMap.containsKey(displayCategory.getSecondCategoryId())) {
                        displayCategory
                            .setSecondCategoryName(categoryNameMap.get(displayCategory.getSecondCategoryId()));
                    }
                });
            }
        } catch (Exception e) {
            LOG.error("设置展示类目名称异常", e);
        }
    }

    /**
     * 拆分list
     * 
     * @return
     */
    public <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    public List<ProductInfoCategoryDTO>
        findProductCategoryBySpecId(List<ProductCategoryQueryDTO> productCategoryQueryDTOS) {
        if (CollectionUtils.isEmpty(productCategoryQueryDTOS)) {
            return null;
        }
        return productInfoCategoryMapper.findProductCategoryBySpec(productCategoryQueryDTOS);
    }

    /**
     * 获取产品信息关联类目Id
     * 
     * @return
     */
    public Long getProductInfoCategoryId(Long productInfoId, Long productCategoryGroupId) {
        if (productInfoId == null || productCategoryGroupId == null) {
            return null;
        }
        ProductInfoCategoryPO productInfoCategoryPO =
            productInfoCategoryMapper.getByInfoId(productInfoId, productCategoryGroupId);
        if (productInfoCategoryPO == null) {
            LOG.info("未找到产品信息关联类目:{}", productInfoId);
            return null;
        }
        return productInfoCategoryPO.getId();
    }
}
