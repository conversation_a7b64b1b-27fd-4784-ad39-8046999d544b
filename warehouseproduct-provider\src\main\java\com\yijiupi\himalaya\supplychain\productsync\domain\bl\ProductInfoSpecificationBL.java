package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductByAwardConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductInfoSpecificationConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductPackageMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO;

/**
 * <AUTHOR>
 * @date 2020/5/23 11:00
 */
@Service
public class ProductInfoSpecificationBL {
    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoSpecificationBL.class);

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;
    @Autowired
    private ProductInfoSpecificationConvertor productInfoSpecificationConvertor;
    @Autowired
    private UnifyProductPackageMapper unifyProductPackageMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    public ProductInfoSpecificationDTO selectByPrimaryKey(Long id) {
        ProductInfoSpecificationPO po = productInfoSpecificationPOMapper.selectByPrimaryKey(id);
        return productInfoSpecificationConvertor.convertTOProductInfoSpecificationDTO(po);
    }

    public List<ProductInfoSpecificationDTO> listBySpecIds(List<Long> ids) {
        List<ProductInfoSpecificationPO> pos = productInfoSpecificationPOMapper.listByIds(ids);
        List<ProductInfoSpecificationDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(pos)) {
            return result;
        }
        pos.forEach(po -> result.add(productInfoSpecificationConvertor.convertTOProductInfoSpecificationDTO(po)));
        return result;
    }

    public List<ProductInfoSpecificationDTO> findByProductInfoIds(List<Long> productInfoIds) {
        List<ProductInfoSpecificationDTO> result = new ArrayList<>();
        List<ProductInfoSpecificationPO> pos = productInfoSpecificationPOMapper.findByProductInfoIds(productInfoIds);
        if (CollectionUtils.isEmpty(pos)) {
            return result;
        }
        pos.forEach(po -> result.add(productInfoSpecificationConvertor.convertTOProductInfoSpecificationDTO(po)));
        return result;
    }

    public Map<Long, ProductSpecificationDTO> findByProductSkuIds(List<Long> productSkuIds, Integer warehouseId) {
        Map<Long, ProductSpecificationDTO> result = new HashMap<>(16);
        List<ProductSpecificationDTO> productSpecificationDTOS =
            productInfoSpecificationPOMapper.findByProductSkuIds(productSkuIds, warehouseId);
        for (ProductSpecificationDTO specification : productSpecificationDTOS) {
            Double length = specification.getLength();
            Double width = specification.getWidth();
            Double height = specification.getHeight();
            if (length != null && width != null && height != null) {
                BigDecimal volume =
                    new BigDecimal(length).multiply(new BigDecimal(width)).multiply(new BigDecimal(height));
                specification.setVolumeValue(volume);
            }
            result.put(specification.getProductSkuId(), specification);
        }
        return result;
    }

    /**
     * 拼接出大单位转小单位的sku规格
     */
    public ProductInfoSpecificationPO getProductSpecificationPO(Long specificationId, Long infoId,
        BigDecimal packageQuantity, String packageName, String unitName) {
        AssertUtils.notNull(specificationId, "产品规格ID不能为空");
        AssertUtils.notNull(infoId, "产品infoId不能为空");
        AssertUtils.notNull(packageQuantity, "子单位转换系数不能为空");
        AssertUtils.notNull(packageName, "大单位名称不能为空");
        AssertUtils.notNull(unitName, "小单位名称不能为空");

        ProductInfoSpecificationPO newSpec = new ProductInfoSpecificationPO();
        newSpec.setId(specificationId);
        newSpec.setProductInfo_Id(infoId);

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(6);
        numberFormat.setMinimumFractionDigits(0);
        numberFormat.setGroupingUsed(false);

        newSpec.setPackageQuantity(packageQuantity);
        newSpec.setPackageName(packageName);
        newSpec.setUnitName(unitName);
        newSpec.setName(
            String.format("%s%s/%s", numberFormat.format(packageQuantity.doubleValue()), unitName, packageName));

        newSpec.setState((byte)0);
        newSpec.setCreateTime(new Date());
        newSpec.setCreateUser_Id(1);
        return newSpec;
    }

    /**
     * 产品规格的插入
     */
    public void insertOrUpdate(ProductInfoSpecificationPO newSpec) {
        // 修改时,spec可能会新增可能会修改,,如果我们原表有体积字段了,以我们的字段为准,不插入op传的体积字段
        ProductInfoSpecificationPO oldProductSku = productInfoSpecificationPOMapper.selectByPrimaryKey(newSpec.getId());
        if (oldProductSku == null) {
            productInfoSpecificationPOMapper.insertSelective(newSpec);
            LOG.info("【酒批】新增产品规格：{}", JSON.toJSONString(newSpec));
        } else {
            if (oldProductSku.getLength() == null && newSpec.getLength() != null) {
                newSpec.setLength(oldProductSku.getLength());
            }
            if (oldProductSku.getWidth() == null && newSpec.getWidth() != null) {
                newSpec.setWidth(oldProductSku.getWidth());
            }
            if (oldProductSku.getHeight() == null && newSpec.getHeight() != null) {
                newSpec.setHeight(oldProductSku.getHeight());
            }
            if (oldProductSku.getWeight() == null && newSpec.getWeight() != null) {
                newSpec.setWeight(oldProductSku.getWeight());
            }
            if (newSpec.getLength() != null && newSpec.getWidth() != null && newSpec.getHeight() != null) {
                StringBuilder volumn = new StringBuilder();
                volumn.append(newSpec.getLength());
                volumn.append("*");
                volumn.append(newSpec.getWidth());
                volumn.append("*");
                volumn.append(newSpec.getHeight());
                newSpec.setVolume(volumn.toString());
            }
            productInfoSpecificationPOMapper.updateByPrimaryKeySelective(newSpec);
            LOG.info("【酒批】修改产品规格：{}", JSON.toJSONString(newSpec));
        }
    }

    /**
     * 根据产品名称获取规格信息
     *
     * @return
     */
    public PageList<ProductInfoSpecificationDTO> listSpecInfoByProductName(ProductInfoSO so) {
        AssertUtils.notNull(so, "参数不能为空");
        LOG.info("根据产品名称获取规格信息：{}", JSON.toJSONString(so));
        PageHelper.startPage(so.getPageNum(), so.getPageSize());
        if (so.getQueryAwardFlag() == null || !so.getQueryAwardFlag()) {
            so.setStatisticsClassName(ProductByAwardConvertor.CATEGORY_NAME);
        }

        if (CollectionUtils.isEmpty(so.getSpecIdList()) && so.getSpecificationId() == null
            && CollectionUtils.isEmpty(so.getProductInfoIdList())) {
            // 先获取产品信息id
            PageResult<ProductInfoPO> pageResult = productInfoPOMapper.listProductInfo(so);
            LOG.info("根据产品名称获取规格信息 产品信息结果：{}", JSON.toJSONString(pageResult));
            PageList<ProductInfoPO> pageList = pageResult.toPageList();
            if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
                return new PageList<>();
            }

            List<Long> productInfoIdList = pageList.getDataList().stream().filter(p -> p.getId() != null)
                .map(p -> p.getId()).distinct().collect(Collectors.toList());
            so.setProductInfoIdList(productInfoIdList);
        }

        PageResult<ProductInfoSpecificationDTO> pageResult = productInfoSpecificationPOMapper.findByProductName(so);
        PageList<ProductInfoSpecificationDTO> pageList = new PageList<>();
        pageList.setDataList(pageResult.getResult());
        pageList.setPager(pageResult.getPager());
        return pageList;
    }
}
