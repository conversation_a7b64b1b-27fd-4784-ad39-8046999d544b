package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.baseutil.HttpClientUtils;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.model.dto.TradProductInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品信息同步业务逻辑
 *
 * <AUTHOR>
 */
@Service
public class ProductInfoSyncBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoSyncBL.class);
    private static final int SPLIT_LENGTH = 50;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;
    private static final String PRODUCT_INFO_QUERY = "ProductInfoQueryService/getByIds";

    /**
     * 同步产品信息
     *
     * @param infoIds 产品信息ID集合
     */
    public void syncProductInfo(List<Long> infoIds) {
        if (CollectionUtils.isEmpty(infoIds)) {
            return;
        }
        Lists.partition(infoIds, SPLIT_LENGTH).forEach(this::syncProductInfoBatch);
    }

    private void syncProductInfoBatch(List<Long> infoIds) {
        // （1）调交易接口获取产品信息
        List<Long> infoSets = infoIds.stream().filter(p -> p != null && p <= Integer.MAX_VALUE).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(infoSets)) {
            return;
        }
        List<TradProductInfoDTO> productInfoMainMap = listTrdProductInfo(infoSets);
        if (productInfoMainMap == null || productInfoMainMap.isEmpty()) {
            return;
        }

        // （2）执行产品信息同步
        List<ProductInfoPO> updateInfoList = new ArrayList<>();
        productInfoMainMap.forEach((infoDTO) -> {
            // 从ProductInfoMain同步字段到ProductInfoPO
            ProductInfoPO productInfoPO = new ProductInfoPO();

            // 根据商城Info补充更新信息
            if (infoDTO.getId() != null) {
                productInfoPO.setId(infoDTO.getId().longValue());
            }

            // 基本信息
            productInfoPO.setProductName(infoDTO.getName());
            productInfoPO.setGeneralName(infoDTO.getGeneralName());
            productInfoPO.setSeriesName(infoDTO.getSeries());

            // 图片信息
            if (infoDTO.getDefaultImageId() != null) {
                productInfoPO.setDefaultImageFile_Id(infoDTO.getDefaultImageId().toString());
            }

            // 保存条件信息
            productInfoPO.setStorageMethod(infoDTO.getStorageMethod());
            productInfoPO.setOriginalPlace(infoDTO.getOriginalPlace());

            // 保质期信息
            productInfoPO.setMonthOfShelfLife(infoDTO.getShelfLife() == null ? 0 : infoDTO.getShelfLife());
            // 转换保质期单位 0:长期，1:年，2:月，3:日
            productInfoPO.setShelfLifeUnit(infoDTO.getShelfLifeType() == null ? null : infoDTO.getShelfLifeType().byteValue());
            // 保质期单位为0，或者保质期时长未设置，默认为长期
            productInfoPO.setShelfLifeLongTime(infoDTO.getShelfLife() == null || Objects.equals(infoDTO.getShelfLifeType(), 0) ? (byte) 1 : (byte) 0);

            // 产品类型信息
            if (infoDTO.getProductInfoType() != null) {
                productInfoPO.setProductInfoType(infoDTO.getProductInfoType().byteValue());
            }

            // 产品编码信息
            productInfoPO.setProductCode(infoDTO.getProductCode());

            // 状态信息
            if (infoDTO.getState() != null) {
                productInfoPO.setStatus(infoDTO.getState().byteValue());
            }

            productInfoPO.setHasBottleCode((byte) 0);
            productInfoPO.setHasBoxCode((byte) 0);
            productInfoPO.setPackageType((byte) 0);
            productInfoPO.setCreateTime(new Date());
            productInfoPO.setLastUpdateTime(new Date());
            productInfoPO.setCreateUser_Id(0);
            productInfoPO.setLastUpdateUser_Id(0);
            productInfoPO.setIsBOM((byte) 0);

            updateInfoList.add(productInfoPO);
        });

        // 批量更新ProductInfo字段
        if (!updateInfoList.isEmpty()) {
            // 使用批量更新方式，提高效率
            productInfoPOMapper.insertOrUpdateBatch(updateInfoList);
            LOG.info("【同步产品信息】批量更新产品信息完成，共更新{}个产品", updateInfoList.size());
        }
    }

    private List<TradProductInfoDTO> listTrdProductInfo(List<Long> infoIds) {
//        LOG.info("查询交易产品信息 入参: {}", JSON.toJSONString(infoIds));
        String result = null;
        try {
            result = HttpClientUtils.postJson(String.format(ServerPath.SERVER_URL, ServerPath.BASE_SERVICE) + PRODUCT_INFO_QUERY, Collections.singletonList(infoIds));
        } catch (Exception e) {
            throw new BusinessException("获取商城ProductInfo失败！", e);
        }
        if (result == null) {
            return Collections.emptyList();
        }
//        LOG.info("查询交易产品信息 结果: {}", JSON.toJSONString(result));
        return JSON.parseArray(result, TradProductInfoDTO.class);
    }
} 