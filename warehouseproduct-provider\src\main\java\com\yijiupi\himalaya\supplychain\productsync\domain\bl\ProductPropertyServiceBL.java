package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yijiupi.himalaya.supplychain.baseutil.HttpClientUtils;
import com.yijiupi.himalaya.supplychain.productsync.config.AuthUserConfig;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.model.dto.InventoryPropertyDTO;
import com.yijiupi.himalaya.supplychain.productsync.model.dto.InventoryPropertyPageResultDTO;
import com.yijiupi.himalaya.supplychain.productsync.model.dto.TokenDTO;
import com.yijiupi.himalaya.supplychain.util.DateUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.so.WarehouseSO;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 产品属性同步
 */
@Service
public class ProductPropertyServiceBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductPropertyServiceBL.class);

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Value("${bi.token.url}")
    private String toKenUrl;

    @Value("${bi.base.url}")
    private String baseUrl;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private AuthUserConfig authUserConfig;

    private static final String TOKEN_KEY = "supp:productsycn:bi:Token:";

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductSkuConfigMapper productSkuConfigMapper;

    private final Integer pageSize = 200;
    /**
     * 获取仓库库存ABC属性
     */
    private static final String REQUEST_PROPERTY = "/one-vue/prod-api/interface/api/bi/open/scm/updateWarehouseType";
    /**
     * 获取单个sku属性
     */
    private static final String SKU_PROPERTY = "/one-vue/prod-api/interface/api/bi/open/scm/warehouseTypeDetail";

    private Integer queryPageSize = 10000;

    private static final String SUCCESS_CODE = "200";

    /**
     * 产品库存ABC属性同步
     */
    public void productPropertySync() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        String format = DateUtil.format(d, "yyyy-MM-dd");
        // 获取所有启用仓库信息

        WarehouseSO warehouseSO = new WarehouseSO();
        warehouseSO.setState(1);
        List<Warehouse> warehouseList = warehouseQueryService.listWarehouse(warehouseSO);
        if (CollectionUtils.isEmpty(warehouseList)) {
            return;
        }
        String token = getToken();
        InventoryPropertyRequestDTO param = new InventoryPropertyRequestDTO();
        param.setDateKey(format);
        Integer pageIndex = 1;
        param.setPageSize(queryPageSize);
        // 获取商品库存ABC属性
        do {
            param.setPageStart(pageIndex);
            String result = StringUtils.EMPTY;
            try {
                result = postTokenJson(baseUrl + REQUEST_PROPERTY, "Bearer " + token, param);
            } catch (Exception e) {
                LOGGER.error("同步库存ABC属性异常", e);
                return;
            }
            if (StringUtils.isBlank(result)) {
                LOGGER.warn("未查询到库存ABC属性");
                return;
            }
            InventoryPropertyBaseDTO inventoryPropertyBaseDTO =
                    JSONObject.parseObject(result, InventoryPropertyBaseDTO.class);
            LOGGER.info("同步库存ABC inventoryPropertyBaseDTO={}", JSON.toJSONString(inventoryPropertyBaseDTO));
            if (!SUCCESS_CODE.equals(inventoryPropertyBaseDTO.getCode())) {
                LOGGER.warn("获取库存ABC属性请求失败 inventoryPropertyBaseDTO={}", JSON.toJSONString(inventoryPropertyBaseDTO));
                return;
            }
            Object data = inventoryPropertyBaseDTO.getData();
            if (ObjectUtils.isEmpty(data)) {
                LOGGER.warn("未查询到库存ABC结果 data={}", JSON.toJSONString(data));
                return;
            }
            InventoryPropertyPageResultDTO inventoryPropertyPageResultDTO =
                    JSONObject.parseObject(String.valueOf(data), InventoryPropertyPageResultDTO.class);
            List<InventoryPropertyDTO> inventoryPropertyDTOListData = inventoryPropertyPageResultDTO.getData();
            if (CollectionUtils.isEmpty(inventoryPropertyDTOListData)) {
                LOGGER.warn("未查询到库存ABC结果 inventoryPropertyDTOList={}", JSON.toJSONString(inventoryPropertyDTOListData));
                return;
            }

            for (Warehouse warehouse : warehouseList) {
                List<InventoryPropertyDTO> collect = inventoryPropertyDTOListData.stream()
                        .filter(i -> i.getWarehouseId().compareTo(warehouse.getId()) == 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    continue;
                }
                List<InventoryPropertyDTO> nullOwnerIdInventoryList =
                        collect.stream().filter(i -> ObjectUtils.isEmpty(i.getOwnerId()) || "0".equals(i.getOwnerId()))
                                .collect(Collectors.toList());
                List<InventoryPropertyDTO> ownerIdInventoryList =
                        collect.stream().filter(i -> !ObjectUtils.isEmpty(i.getOwnerId())).collect(Collectors.toList());
                List<Long> nullOwnerSpeIdList = nullOwnerIdInventoryList.stream()
                        .map(InventoryPropertyDTO::getProductSpeId).collect(Collectors.toList());
                List<Long> ownerSpeIdList = ownerIdInventoryList.stream().map(InventoryPropertyDTO::getProductSpeId)
                        .collect(Collectors.toList());
                List<String> ownerIdList =
                        ownerIdInventoryList.stream().map(InventoryPropertyDTO::getOwnerId).collect(Collectors.toList());
                // 查询null货主 城市 规格 查询sku信息
                if (!CollectionUtils.isEmpty(nullOwnerSpeIdList)) {
                    List<ProductSkuPO> nullOwnerProductSkuPOList =
                            productSkuMapper.listSkuByOwner(nullOwnerSpeIdList, null, warehouse.getCityId());
                    if (!CollectionUtils.isEmpty(nullOwnerProductSkuPOList)) {
                        changeInventoryAttribute(warehouse, nullOwnerIdInventoryList, nullOwnerProductSkuPOList);
                    }
                }

                if (!CollectionUtils.isEmpty(ownerSpeIdList) && !CollectionUtils.isEmpty(ownerIdList)) {
                    List<ProductSkuPO> ownerProductSkuPOList =
                            productSkuMapper.listSkuByOwner(ownerSpeIdList, ownerIdList, warehouse.getCityId());
                    if (!CollectionUtils.isEmpty(ownerProductSkuPOList)) {
                        // 修改sku config 中 abc属性字段
                        changeInventoryAttribute(warehouse, ownerIdInventoryList, ownerProductSkuPOList);
                    }
                }
            }
            pageIndex++;
        } while (true);

    }

    private void changeInventoryAttribute(Warehouse warehouse, List<InventoryPropertyDTO> nullOwnerIdInventoryList,
                                          List<ProductSkuPO> nullOwnerProductSkuPOList) {
        // 根据属性分组
        Map<String, List<InventoryPropertyDTO>> collect =
                nullOwnerIdInventoryList.stream().collect(Collectors.groupingBy(InventoryPropertyDTO::getAbcAttribute));
        Set<String> keySet = collect.keySet();
        for (String key : keySet) {
            List<InventoryPropertyDTO> inventoryPropertyGroupList = collect.get(key);
            List<Long> integerList = inventoryPropertyGroupList.stream().map(i -> Long.valueOf(i.getProductSpeId()))
                    .collect(Collectors.toList());
            // 每个属性对应的sku信息
            List<Long> skuList =
                    nullOwnerProductSkuPOList.stream().filter(p -> integerList.contains(p.getProductSpecificationId()))
                            .map(ProductSkuPO::getProductSkuId).collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(skuList, pageSize);
            for (List<Long> skuIdList : partition) {
                List<ProductSkuConfigPO> skuConfigBySkuIdsAndWarehouseId =
                        productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouse.getId(), skuIdList);
                // 修改属性
                List<Long> configIdList = skuConfigBySkuIdsAndWarehouseId.stream().map(ProductSkuConfigPO::getId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(configIdList)) {
                    productSkuConfigMapper.updateInventoryAttribute(configIdList, key);
                }
            }
        }
    }

    public String getToken() {
        String token = redisTemplate.opsForValue().get(TOKEN_KEY);
        // 如果为空 则重新请求
        if (StringUtils.isBlank(token)) {
            TokenRequestDTO tokenRequestDTO = new TokenRequestDTO();
            BeanUtils.copyProperties(authUserConfig, tokenRequestDTO);
            String resultJson = null;
            try {
                resultJson = HttpClientUtils.postJson(toKenUrl, tokenRequestDTO);
            } catch (Exception e) {
                LOGGER.error("获取token异常", e);
            }
            if (StringUtils.isNotBlank(resultJson)) {
                LOGGER.info("获取token resultJson={}", JSON.toJSONString(resultJson));
                ResponseResult responseResult = JSONObject.parseObject(resultJson.toLowerCase(), ResponseResult.class);
                if (responseResult.getCode().compareTo(Integer.valueOf(SUCCESS_CODE)) == 0) {
                    Object data = responseResult.getData();
                    TokenDTO tokenDTO = JSONObject.parseObject(String.valueOf(data), TokenDTO.class);
                    token = tokenDTO.getOne_service_token();
                    redisTemplate.opsForValue().set(TOKEN_KEY, token, 1, TimeUnit.HOURS);
                }
            }
        }
        return token;
    }

    public String postTokenJson(String url, String token, Object obj) throws Exception {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        ObjectMapper objectMapper = new ObjectMapper();
        httpPost.addHeader("content-type", "application/json;charset=utf-8");
        httpPost.addHeader("accept", "application/json;charset=utf-8");
        httpPost.addHeader("Authorization", token);
        httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(obj), Charset.forName("utf-8")));
        CloseableHttpResponse resposnse = httpClient.execute(httpPost);
        HttpEntity entity = resposnse.getEntity();
        return EntityUtils.toString(entity);
    }

    public ProductSkuInventoryProperDTO getProductSkuInventoryProperty(InventoryPropertyRequestDTO request) {
        LOGGER.info("请求库存ABC request={}", JSON.toJSONString(request));
        ProductSkuInventoryProperDTO productSkuInventoryProperDTO = new ProductSkuInventoryProperDTO();
        if(request == null || request.getProduct_spe_id() == null){
            return productSkuInventoryProperDTO;
        }

        String token = getToken();
        try {
            String s = postTokenJson(baseUrl + SKU_PROPERTY, "Bearer " + token, request);
            if (StringUtils.isBlank(s)) {
                return productSkuInventoryProperDTO;
            }
            InventoryPropertyBaseDTO inventoryPropertyBaseDTO =
                    JSONObject.parseObject(s, InventoryPropertyBaseDTO.class);
            Object data = inventoryPropertyBaseDTO.getData();
            if (ObjectUtils.isEmpty(data)) {
                return null;
            }
            productSkuInventoryProperDTO =
                    JSONObject.parseObject(String.valueOf(data), ProductSkuInventoryProperDTO.class);
            Map<String, String> rankingSectionMap = new HashMap<>(16);
            rankingSectionMap.put("A", "0-20%");
            rankingSectionMap.put("B", "20%-80%");
            rankingSectionMap.put("C", "80%-100%");
            productSkuInventoryProperDTO
                    .setRankingSection(rankingSectionMap.get(productSkuInventoryProperDTO.getAbcAttribute()));
            Warehouse warehouseById = warehouseQueryService.findWarehouseById(request.getWarehouse_id());
            List<Long> productSpeId = new ArrayList<>();
            productSpeId.add(Long.valueOf(request.getProduct_spe_id()));
            List<ProductSkuPO> productSkuPOList = new ArrayList<>();
            if (ObjectUtils.isEmpty(request.getOwner_id())) {
                productSkuPOList = productSkuMapper.listSkuByOwner(productSpeId, null, warehouseById.getCityId());
            } else {
                List<String> ownerList = new ArrayList<>();
                ownerList.add(String.valueOf(request.getOwner_id()));
                productSkuPOList = productSkuMapper.listSkuByOwner(productSpeId, ownerList, warehouseById.getCityId());
            }
            if (!CollectionUtils.isEmpty(productSkuPOList)) {
                ProductSkuPO productSkuPO = productSkuPOList.get(0);
                BigDecimal count = productSkuInventoryProperDTO.getCount();
                if (!ObjectUtils.isEmpty(count)) {
                    // 整数
                    BigDecimal bigDecimal = count.setScale(0, RoundingMode.DOWN);
                    // 小数
                    BigDecimal subtract = count.subtract(bigDecimal);
                    String unitCount = subtract.multiply(productSkuPO.getPackageQuantity())
                            .setScale(0, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
                    productSkuInventoryProperDTO.setLastPlaceStatCount(bigDecimal + "大件" + unitCount + "小件");

                }
            }
            LOGGER.info("请求库存ABC信息 productSkuInventoryProperDTO={}", JSON.toJSONString(productSkuInventoryProperDTO));
            return productSkuInventoryProperDTO;
        } catch (Exception e) {
            LOGGER.error("请求获取详情异常", e);
        }
        return productSkuInventoryProperDTO;
    }
}
