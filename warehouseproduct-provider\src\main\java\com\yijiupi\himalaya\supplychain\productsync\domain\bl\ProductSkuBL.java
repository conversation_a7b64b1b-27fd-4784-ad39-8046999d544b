package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IProductInfoService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IProductInfoSpecificationQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.ScmProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfo;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecificationMain;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ScmProductSkuDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.WmsProductSkuSearchDTO;
import com.yijiupi.himalaya.supplychain.inventory.constant.InventoryChangeTypes;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryListQueryService;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordManagerService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductFeatureConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.UnifySkuSimpleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IUnifySkuService;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 产品bl
 *
 * <AUTHOR> 2018/1/19
 */
@Service
public class ProductSkuBL {
    @Autowired
    private ProductSkuMapper productSkuMapper;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductInfoSpecificationQueryService iProductInfoSpecificationQueryService;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductInfoService iProductInfoService;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductSkuQueryService scmProductSkuQueryService;

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuBL.class);
    @Autowired
    private OwnerBL ownerBL;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Autowired
    private ProductSscBL productSscBL;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference
    private IUnifySkuService iUnifySkuService;

    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductInfoBySaasBL productInfoBySaasBL;

    @Autowired
    private ProductSkuBySaasBL productSkuBySaasBL;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Reference
    private IProductInventoryRecordManagerService productInventoryRecordManagerService;

    @Reference
    private IAdminUserService iAdminUserService;

    @Reference
    private IInventoryListQueryService inventoryListQueryService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    /**
     * 校验SkuID能否缺货
     *
     * @param warehouseId 仓库ID
     * @param skuIds      SKU ID列表
     * @return 返回能否缺货的映射，true表示可以缺货，false表示不可以缺货
     */
    public Map<Long, Boolean> checkSkuCanBeOutOfStock(Integer warehouseId, List<Long> skuIds) {
        if (warehouseId == null || CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        Map<Long, Boolean> result = new HashMap<>(skuIds.size());
        for (Long skuId : skuIds) {
            String key = warehouseId + "_" + skuId;
            boolean canBeOutOfStock = redisTemplate.hasKey(key);
            result.put(skuId, canBeOutOfStock);
        }

        return result;
    }

    /**
     * 批量标记SKU可以缺货
     *
     * @param warehouseId 仓库ID
     * @param skuIds      SKU ID列表
     */
    public void markSkuCanBeOutOfStock(Integer warehouseId, List<Long> skuIds) {
        if (warehouseId == null || CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        for (Long skuId : skuIds) {
            String key = warehouseId + "_" + skuId;
            redisTemplate.opsForValue().set(key, "1", 10, TimeUnit.HOURS);
        }
        LOG.info("批量标记SKU可以缺货: warehouseId={}, skuIds={}", warehouseId, skuIds);

        // 在库存变更明细中新增记录
        // xxx确认产品允许标记缺货
        createProductStoreChangeRecord(warehouseId, skuIds);
    }

    private void createProductStoreChangeRecord(Integer warehouseId, List<Long> skuIds) {
        Integer userId = ThreadLocalUtil.getUserId();
        AdminUser adminUser = userId == null ? null : iAdminUserService.getAdminUserWithoutAuthById(userId);
        String userName = adminUser != null ? adminUser.getUserName() : String.valueOf(userId);

        FindStoreDTO findStoreQuery = new FindStoreDTO();
        findStoreQuery.setWarehouseIds(Collections.singletonList(warehouseId));
        findStoreQuery.setProductSkuIds(skuIds);
        findStoreQuery.setPageSize(1000);
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        findStoreQuery.setCityId(warehouse.getCityId());

        PageList<FindStoreInfoDTO> lstAllProductStore = inventoryListQueryService.findStorePage(findStoreQuery);

        List<ProductInventoryChangeRecordDTO> lstTmp = new ArrayList<>();
        lstAllProductStore.getDataList().forEach(productStoreDTO -> {
            ProductInventoryChangeRecordDTO inventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
            inventoryChangeRecordDTO.setId(String.valueOf(UUIDGeneratorUtil.getUUID(ProductInventoryChangeRecordDTO.class.getName())));
            inventoryChangeRecordDTO.setProductStoreId(productStoreDTO.getProductStoreId());
            inventoryChangeRecordDTO.setCityId(0);
            inventoryChangeRecordDTO.setJiupiEventType(-1);
            inventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setTotalCount(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
            inventoryChangeRecordDTO.setDescription("确认产品允许标记缺货");
            inventoryChangeRecordDTO.setCreateTime(new Date());
            inventoryChangeRecordDTO.setCreateUser(userName);
            inventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);
            lstTmp.add(inventoryChangeRecordDTO);
        });
        productInventoryRecordManagerService.saveProductStoreChangeRecord(lstTmp);
    }

    /**
     * 产品同步（交易平台化去SKU）
     */
    @DistributeLock(conditions = "#productSkuMessage.lockKey", sleepMills = 30000, expireMills = 30000, key = "processSingleSku",
            lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void syncProductSku(ScmProductSkuSyncMessage productSkuMessage, List<ProductSkuSyncMsgDTO> msgDTOList,
                               List<ProductRelationGroupAddDTO> groupAddList, List<Long> syncEsProductSkuIds) {
        // 校验参数
        validateProductSku(productSkuMessage);

        if (CollectionUtils.isEmpty(productSkuMessage.getWarehouseIds())) {
            LOG.info("销售仓库为空，跳过");
            return;
        }
        //
        // if (!isNeedProcessSKU(productSkuMessage)) {
        // LOG.info(String.format("虚仓实配其他类型SKU不需要同步:%s。", JSON.toJSONString(productSkuMessage)));
        // return;
        // }

        // 根据仓库查询所属城市
        List<Warehouse> warehouseList =
                iWarehouseQueryService.listWarehouseByIds(new ArrayList<>(productSkuMessage.getWarehouseIds()));
        if (CollectionUtils.isEmpty(warehouseList)) {
            throw new BusinessException("找不到城市id");
        }
        List<Integer> cityIds =
                warehouseList.stream().map(Warehouse::getCityId).distinct().collect(Collectors.toList());
        LOG.info("销售城市：{}", JSON.toJSONString(cityIds));

        // 按城市ID新增或修改产品sku
        ProductSkuPO productSkuPO = ProductSkuConvertor.convertorToProductSkuPO(productSkuMessage);
        if (productSkuPO == null) {
            throw new BusinessException("productSku不能为空");
        }
        // 1、处理货主信息
        processOwner(productSkuPO);

        boolean isProcessOtherMsg = false;

        for (Integer cityId : cityIds) {
            productSkuPO.setCityId(cityId);

            // 当前城市下包含的仓库
            List<Integer> warehouseIds = warehouseList.stream().filter(p -> Objects.equals(p.getCityId(), cityId))
                    .map(Warehouse::getId).distinct().collect(Collectors.toList());

            // 1、判断该城市下sku是否存在，不存在则新增，存在则修改
            ProductSkuPO oldSkuPO = productSkuMapper.getUniqueProductSku(productSkuPO);
            // 新增
            if (oldSkuPO == null) {
                if (!isProcessOtherMsg) {
                    // 处理产品参数
                    processParam(productSkuPO);
                    isProcessOtherMsg = true;
                }
                productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
                productSkuPO.setProductSkuId(productSkuPO.getId());
                if (productSkuPO.getDistributionPercentForAmount() == null) {
                    setDistributionPercent(productSkuPO);
                }
                // 处理产品状态
                processProductState(productSkuPO, warehouseIds);
                if (productSkuPO.getId() == null) {
                    productSkuPO.setId(UUIDGeneratorUtil.getUUID(productSkuPO.getClass().getName()));
                }
                productSkuMapper.insertSelective(productSkuPO);
                LOG.info("【酒批】新增产品sku：{}", JSON.toJSONString(productSkuPO));
                // 修改
            } else {
                // 2023-07-05 产品更新时，重新设置产品与类目关系id
                setProductInfoCategoryId(productSkuPO);

                productSkuPO.setId(oldSkuPO.getId());
                productSkuPO.setProductSkuId(oldSkuPO.getProductSkuId());
                // 不修改产品特征大小件属性
                productSkuPO.setProductFeature(null);
                // 不修改产品配送系数
                productSkuPO.setDistributionPercentForAmount(null);
                // 处理产品状态
                processProductState(productSkuPO, warehouseIds);
                productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
                LOG.info("【酒批】修改产品sku：{}", JSON.toJSONString(productSkuPO));

                // 同步给ES
                syncEsProductSkuIds.add(productSkuPO.getProductSkuId());
            }

            // 2、保存产品sku配置
            ProductSkuConfigDTO productSkuConfigDTO =
                    ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
            if (productSkuConfigDTO != null) {
                if (CollectionUtils.isNotEmpty(warehouseIds)) {
                    productSkuConfigBL.saveProductSkuConfigByWarehouseIds(warehouseIds, productSkuConfigDTO, false, Objects.isNull(oldSkuPO) ? productSkuPO : null);
                }
            }

            // 3、同步sku给外部系统
            ProductSkuSyncMsgDTO syncMsgDTO = new ProductSkuSyncMsgDTO();
            BeanUtils.copyProperties(productSkuPO, syncMsgDTO);
            msgDTOList.add(syncMsgDTO);

            // 4、同步产品关联
            if (CollectionUtils.isNotEmpty(warehouseIds)) {
                warehouseIds.forEach(warehouseId -> {
                    // 店仓仓库才需要关联
                    Optional<Warehouse> optional =
                            warehouseList.stream()
                                    .filter(p -> Objects.equals(p.getId(), warehouseId)
                                            && Objects.equals(p.getWarehouseType(), (int) WarehouseTypeEnum.店仓合一.getType()))
                                    .findFirst();
                    if (optional.isPresent()) {
                        // 查询仓库下同规格其他货主的sku
                        List<Long> relateSkuIds = productSkuMapper.getSameSpecSkuIds(productSkuPO.getCityId(),
                                warehouseId, productSkuPO.getProductSpecificationId(), productSkuPO.getProductSkuId());
                        if (CollectionUtils.isEmpty(relateSkuIds)) {
                            return;
                        }
                        ProductRelationGroupAddDTO groupAddDTO =
                                ProductSkuConvertor.getProductRelationGroupAddDTO(warehouseId, productSkuPO, relateSkuIds);
                        groupAddList.add(groupAddDTO);
                    }
                });
            }

            // 处理中台sku关联
            setUnifyProductSkuRelate(productSkuPO);
        }
    }

    /**
     * 处理中台sku关联
     */
    private void setUnifyProductSkuRelate(ProductSkuPO productSkuPO) {
        // 查询中台skuId
        Long unifySkuId =
                productSscBL.getUnifySkuId(productSkuPO.getProductSpecificationId(), productSkuPO.getCompanyId());
        if (unifySkuId == null) {
            return;
        }
        List<UnifySkuSimpleDTO> unifySkuSimpleDTOS = new ArrayList<>();
        UnifySkuSimpleDTO unifySkuSimpleDTO = new UnifySkuSimpleDTO();
        unifySkuSimpleDTO.setUnifySkuId(unifySkuId);
        unifySkuSimpleDTO.setProductSkuId(productSkuPO.getProductSkuId());
        unifySkuSimpleDTO.setProductSpecId(productSkuPO.getProductSpecificationId());
        unifySkuSimpleDTO.setOwnerId(productSkuPO.getCompanyId());
        unifySkuSimpleDTOS.add(unifySkuSimpleDTO);
        LOG.info("处理中台sku关联：{}", JSON.toJSONString(unifySkuSimpleDTOS));
        iUnifySkuService.setUnifySkuToRedis(unifySkuSimpleDTOS);
    }

    /**
     * 处理产品状态（一个城市下只要有一个sku状态是上架，就是上架状态）
     */
    public void processProductState(ProductSkuPO productSkuPO, List<Integer> warehouseIds) {
        try {
            if (Objects.equals(productSkuPO.getProductState(), ProductSkuStateEnum.上架.getType())) {
                return;
            }
            // 仓库ID集合
            Set<Integer> warehouseIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(warehouseIds)) {
                warehouseIdSet.addAll(warehouseIds);
            }
            // 获取skuConfig表的仓库id
            if (productSkuPO.getProductSkuId() != null) {
                List<Integer> configWarehouseIds =
                        productSkuConfigBL.getWarehouseIdBySkuId(productSkuPO.getProductSkuId());
                if (CollectionUtils.isNotEmpty(configWarehouseIds)) {
                    warehouseIdSet.addAll(configWarehouseIds);
                }
            }
            // 查询交易产品状态
            List<ScmProductSkuDTO> yjpSkuList = getScmProductSkuDTOS(productSkuPO.getProductSpecificationId(),
                    warehouseIdSet, productSkuPO.getCompanyId());
            // 只要交易sku有一个是上架状态，则把供应链sku更新为上架状态
            if (yjpSkuList.stream().anyMatch(p -> Objects.equals(p.getState(), ProductSkuStateEnum.上架.getType()))) {
                productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
                LOG.info("【酒批】处理产品状态为上架");
            }
        } catch (Exception e) {
            LOG.error("调用交易【获取产品状态】接口异常", e);
        }
    }

    /**
     * 查询交易产品状态
     *
     * @return
     */
    public List<ScmProductSkuDTO> getScmProductSkuDTOS(Long SpecificationId, Set<Integer> warehouseIdSet,
                                                       Long ownerId) {
        try {
            List<ScmProductSkuDTO> yjpSkuList = new ArrayList<>();
            WmsProductSkuSearchDTO wmsProductSkuSearchDTO = new WmsProductSkuSearchDTO();
            if (ownerId != null) {
                // 自营(0), 批次入驻(1), 实仓入驻(2), 虚仓入驻(3), 仓储服务(4), 直配入驻(5), 虚仓实配(6)
                wmsProductSkuSearchDTO.setProcurementModeList(new HashSet<>(Arrays.asList(2, 3, 4, 5, 6)));
            }
            wmsProductSkuSearchDTO.setProductSpecInfoId(SpecificationId != null ? SpecificationId.intValue() : null);
            // 10个仓库一组
            List<List<Integer>> warehouseList = Lists.partition(new ArrayList<>(warehouseIdSet), 10);
            for (List<Integer> list : warehouseList) {
                wmsProductSkuSearchDTO.setWarehouseIds(new HashSet<>(list));
                LOG.info("【酒批】查询交易产品状态参数：{}", JSON.toJSONString(wmsProductSkuSearchDTO));
                List<ScmProductSkuDTO> refSkuList = scmProductSkuQueryService.listWmsProductSku(wmsProductSkuSearchDTO);
                LOG.info("【酒批】查询交易产品状态返回：{}", JSON.toJSONString(refSkuList));
                if (CollectionUtils.isNotEmpty(refSkuList)) {
                    yjpSkuList.addAll(refSkuList);
                }
            }
            return yjpSkuList;
        } catch (Exception e) {
            LOG.error("酒批】查询交易产品状态异常", e);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 处理产品参数
     *
     * @param productSkuPO
     */
    public void processParam(ProductSkuPO productSkuPO) {
        // 2、设置配送系数.
        setDistributionPercent(productSkuPO);
        // 3、设置产品特征
        setStatisticsClass(productSkuPO.getProductInfoId() != null ? productSkuPO.getProductInfoId().intValue() : null,
                productSkuPO);
        // 4、设置产品与类目关系id
        setProductInfoCategoryId(productSkuPO);
    }

    /**
     * 获取灰度仓库的KEY
     */
    private static final String DEFAULT_ORG = "DefaultCategoryOrgId";

    @Reference
    private IVariableValueService variableValueService;

    /**
     * 设置产品与类目关系id
     */
    private void setProductInfoCategoryId(ProductSkuPO productSkuPO) {
        if (productSkuPO == null || productSkuPO.getProductInfoId() == null) {
            return;
        }
        Long productCategoryGroupId = ProductCategoryGroupIdConstant.YIJIUPI;
        // 同步类目
        VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
        valueQuery.setVariableKey(DEFAULT_ORG);
        VariableDefAndValueDTO value = variableValueService.detailVariable(valueQuery);
        Integer defaultOrgId = value == null || StringUtils.isEmpty(value.getVariableData()) ? null
                : Integer.parseInt(value.getVariableData());
        // 非久批环境，根据SKU获取默认类目分组
        if (defaultOrgId != null) {
            productCategoryGroupId =
                    productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(productSkuPO.getCityId(), null);
        }
        productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, productCategoryGroupId);
    }

    /**
     * 处理货主信息
     */
    public void processOwner(ProductSkuPO productSkuPO) {
        // 代运营模式的companyId为null，修数据
        if (productSkuPO.getCompanyId() == null || productSkuPO.getCompanyId() == 0) {
            productSkuPO.setCompanyId(null);
            productSkuPO.setOwnerName(ownerBL.getDefaultOwnerName());
        } else {
            // 如果货主名称为空的，更新OwnerName
            if (StringUtils.isEmpty(productSkuPO.getOwnerName())) {
                OwnerDTO ownerDTO = ownerBL.getOwnerById(productSkuPO.getCompanyId());
                productSkuPO.setOwnerName(ownerDTO == null ? "" : ownerDTO.getOwnerName());
            }
        }
    }

    /**
     * 校验参数
     */
    private void validateProductSku(ScmProductSkuSyncMessage productSkuMessage) {
        AssertUtils.notNull(productSkuMessage, "产品同步参数不能为空");
        AssertUtils.notNull(productSkuMessage.getProductSkuId(), "产品skuID不能为空");
        AssertUtils.notNull(productSkuMessage.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productSkuMessage.getProductInfoId(), "产品信息id不能为空");
        AssertUtils.notNull(productSkuMessage.getProductInfoSpecId(), "产品规格id不能为空");
        AssertUtils.notNull(productSkuMessage.getState(), "产品状态不能为空");
        AssertUtils.notNull(productSkuMessage.getSaleMode(), "销售模式不能为空");
        AssertUtils.notNull(productSkuMessage.getSpecName(), "包装规格名称不能为空");
        AssertUtils.notNull(productSkuMessage.getSpecPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(productSkuMessage.getSpecUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(productSkuMessage.getSpecQuantity(), "包装规格大小单位转换系数不能为空");
    }

    /**
     * 设置产品特征
     */
    private void setStatisticsClass(Integer productInfoId, ProductSkuPO productSkuPO) {
        try {
            if (productInfoId == null) {
                throw new BusinessException("产品信息Id不能为空！");
            }
            ProductInfo productInfo = iProductInfoService.getProductInfoById(productInfoId);
            if (productInfo != null) {
                // 商品特征
                Integer productFeature = ProductFeatureConstant.大件;
                if (productInfo.getIsUnpackSale()) {
                    productFeature = ProductFeatureConstant.小件;
                }
                productSkuPO.setProductFeature(productFeature);

                // LOG.info("产品类目Id :" + productInfo.getProductStatisticsClass());
                // 从产品信息中设置产品品牌
                productSkuPO.setProductBrand(productInfo.getProductBrand());
                // 设置保质期数量和单位
                productSkuPO.setMonthOfShelfLife(productInfo.getMonthOfShelfLife());
                productSkuPO.setShelfLifeUnit(productInfo.getShelfLifeUnit());
            }
        } catch (Exception e) {
            LOG.error("设置产品特征异常", e);
        }
    }

    /**
     * 设置配送系数
     *
     * @param productSkuPO
     */
    private void setDistributionPercent(ProductSkuPO productSkuPO) {
        try {
            ProductInfoSpecificationMain productInfoSpecificationMain = iProductInfoSpecificationQueryService
                    .getProductInfoSpecificationMain(productSkuPO.getProductSpecificationId().intValue());
            LOG.info(String.format("配送系数：%s", JSON.toJSONString(productInfoSpecificationMain)));
            if (productInfoSpecificationMain != null) {
                if (productInfoSpecificationMain.getDistributionPercentForAmount() != null) {
                    productSkuPO
                            .setDistributionPercentForAmount(productInfoSpecificationMain.getDistributionPercent() != null
                                    ? BigDecimal.valueOf(productInfoSpecificationMain.getDistributionPercent())
                                    : BigDecimal.ONE);
                }
            }
        } catch (Exception e) {
            LOG.error("设置配送系数异常", e);
        }
        // 配送系数为空时设置默认值为1
        if (productSkuPO.getDistributionPercentForAmount() == null
                || productSkuPO.getDistributionPercentForAmount().compareTo(BigDecimal.ZERO) == 0) {
            productSkuPO.setDistributionPercentForAmount(BigDecimal.ONE);
        }
    }

    /**
     * 根据产品来源查询所有产品infoId
     */
    public List<Long> listProductInfoIdBySource(Integer source) {
        AssertUtils.notNull(source, "产品来源不能为空");
        return productSkuMapper.listProductInfoIdBySource(source);
    }

    /**
     * 更新sku的类目关系ID
     */
    public int updateProductSkuOfCatetoryId(Long categoryGroupId) {
        AssertUtils.notNull(categoryGroupId, "分组ID不能为空");
        return productSkuMapper.updateProductSkuOfCatetoryId(categoryGroupId);
    }

    /**
     * 更新产品状态
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductState(List<Long> skuIdList, Integer productState) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return;
        }
        List<List<Long>> upLists = Lists.partition(skuIdList, 200);
        upLists.forEach((skuIds) -> {
            productSkuMapper.updateProductStateBySkuIds(skuIds, productState);
            LOG.info("[更新产品sku状态]skuId:{}, state:{}", JSON.toJSONString(skuIds), productState);
        });
    }

    /**
     * 批量更新中台skuId
     */
    public void batchUpdateProductSkuUnifyId(List<ProductSkuPO> skuUpdateList) {
        if (CollectionUtils.isEmpty(skuUpdateList)) {
            throw new DataValidateException("中台skuId同步信息不能为空");
        }
        skuUpdateList.forEach(elem -> {
            AssertUtils.notNull(elem.getId(), "id不能为空");
            AssertUtils.notNull(elem.getUnifySkuId(), "中台skuId不能为空");
        });
        Map<Long, List<ProductSkuPO>> unifyIdSkuMap =
                skuUpdateList.stream().collect(Collectors.groupingBy(ProductSkuPO::getUnifySkuId));

        unifyIdSkuMap.forEach((newUnifyId, skuUpdateGroup) -> {
            updateProductSkuUnifyId(skuUpdateGroup, newUnifyId);
        });
    }

    /**
     * 产品中台skuId同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProductSkuUnifyId(List<ProductSkuPO> updateProductSkuPOS, Long newUnifyId) {
        List<UnifySkuSimpleDTO> unifySkuSimpleDTOS = new ArrayList<>();
        List<Long> updateIdList = new ArrayList<>();

        updateProductSkuPOS.forEach(oldSkuElem -> {
            // 更新中台skuId
            updateIdList.add(oldSkuElem.getId());

            // 处理中台sku关联
            UnifySkuSimpleDTO unifySkuSimpleDTO = new UnifySkuSimpleDTO();
            unifySkuSimpleDTO.setUnifySkuId(newUnifyId);
            unifySkuSimpleDTO.setProductSkuId(oldSkuElem.getProductSkuId());
            unifySkuSimpleDTO.setProductSpecId(oldSkuElem.getProductSpecificationId());
            unifySkuSimpleDTO.setOwnerId(oldSkuElem.getCompanyId());
            unifySkuSimpleDTOS.add(unifySkuSimpleDTO);
        });

        // 更新中台skuId
        Lists.partition(updateIdList, 200).forEach(idList -> {
            productSkuMapper.updateUnifyIdInPrimaryKey(idList, newUnifyId);
        });

        // 处理中台sku关联
        Lists.partition(unifySkuSimpleDTOS, 2000).forEach(unifySkuSimpleList -> {
            LOG.info("处理中台sku关联：{}", JSON.toJSONString(unifySkuSimpleList));
            iUnifySkuService.setUnifySkuToRedis(unifySkuSimpleList);
        });
    }

    /**
     * 通过原始sku、新中台sku、新规格生成新sku（skuId、refSkuId为中台skuId）
     */
    public ScmProductSkuSyncMessage getSkuSyncMsgByUnifyInfo(ProductSkuPO fromSku, Long newUnifySkuId,
                                                             ProductInfoSpecificationPO specificationPO, Set<Integer> warehouseIdSet) {
        AssertUtils.notNull(fromSku, "原始sku不能为空");
        AssertUtils.notNull(newUnifySkuId, "新skuId不能为空");
        AssertUtils.notEmpty(warehouseIdSet, "仓库id不能不为空");
        AssertUtils.notNull(specificationPO, "规格不能为空");

        ScmProductSkuSyncMessage toSkuSync = new ScmProductSkuSyncMessage();
        // to变更的字段
        toSkuSync.setProductInfoSpecId(specificationPO.getId());
        // UnifySkuId
        toSkuSync.setRelSscProductSkuId(newUnifySkuId);
        // Ref_ProductSku_Id
        toSkuSync.setProductSkuId(newUnifySkuId);
        toSkuSync.setSpecName(specificationPO.getName());
        toSkuSync.setSpecPackageName(specificationPO.getPackageName());
        toSkuSync.setSpecUnitName(specificationPO.getUnitName());
        toSkuSync.setSpecQuantity(specificationPO.getPackageQuantity());

        // 不变的字段
        toSkuSync.setProductName(fromSku.getName());
        toSkuSync.setCompanyId(fromSku.getCompanyId());
        toSkuSync.setState(fromSku.getProductState());
        toSkuSync.setSaleMode(fromSku.getSaleModel() == null ? 0 : fromSku.getSaleModel());
        toSkuSync.setSellingPrice(fromSku.getSellingPrice());
        toSkuSync.setSellingPriceUnit(fromSku.getSellingPriceUnit());
        toSkuSync.setProductInfoId(fromSku.getProductInfoId());
        toSkuSync.setProductInfoCategoryId(fromSku.getProductInfoCategoryId());
        toSkuSync.setSecOwnerId(fromSku.getSecOwnerId());
        toSkuSync.setProductBrand(fromSku.getProductBrand());

        toSkuSync.setWarehouseIds(warehouseIdSet);

        // 未赋值的字段：Sequence、Remo、warehouseCustodyFee、
        // DeliveryFee、DeliveryPayType、sortingFee、MonthOfShelfLife、ShelfLifeUnit
        // ProductFeature、MaxInventory、MinInventory、MaxReplenishment、MinReplenishment、StorageType、
        // IsPick、IsSow、InventoryRatio、IsFleeGoods、ProductRelevantState、IsUnique、productGrade
        return toSkuSync;
    }

    /**
     * 通过中台skuId、仓库id查出from，根据from、规格生成新的sku
     */
    public List<ScmProductSkuSyncMessage> listSkuSyncMsgByUnifyInfo(Long fromUnifySkuId, Long toUnifySkuId,
                                                                    ProductInfoSpecificationPO specificationPO, List<Integer> warehouseIdList) {
        AssertUtils.notEmpty(warehouseIdList, "仓库Id不能为空");
        List<Warehouse> warehouseCfgList = iWarehouseQueryService.listWarehouseByIds(warehouseIdList);
        if (CollectionUtils.isEmpty(warehouseCfgList)) {
            throw new BusinessException("查找不到仓库信息，仓库Id：{}" + JSON.toJSONString(warehouseIdList));
        }
        List<Integer> cityIdList =
                warehouseCfgList.stream().map(Warehouse::getCityId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cityIdList)) {
            throw new BusinessException("仓库无城市信息，仓库Id：{}" + JSON.toJSONString(warehouseIdList));
        }
        List<ProductSkuPO> fromSkuList = productSkuMapper.findSkuByUnifyCityList(fromUnifySkuId, cityIdList);

        if (CollectionUtils.isEmpty(fromSkuList)) {
            throw new BusinessException(
                    "from的中台skuId不存在，unifySkuId：" + fromUnifySkuId + ", cityId：" + JSON.toJSONString(cityIdList));
        }

        List<ScmProductSkuSyncMessage> toSkuSyncList = new ArrayList<>();
        fromSkuList.forEach(fromSku -> {
            Set<Integer> warehouseIdSet = warehouseCfgList.stream()
                    .filter(warehouseElem -> Objects.equals(warehouseElem.getCityId(), fromSku.getCityId()))
                    .map(Warehouse::getId).collect(Collectors.toSet());
            ScmProductSkuSyncMessage toSkuSync =
                    getSkuSyncMsgByUnifyInfo(fromSku, toUnifySkuId, specificationPO, warehouseIdSet);
            toSkuSyncList.add(toSkuSync);
        });

        return toSkuSyncList;
    }

    /**
     * 创建单个产品sku
     *
     * @return
     */
    @DistributeLock(conditions = "#createDTO.lockKey", sleepMills = 30000, expireMills = 30000, key = "createSingleProductSkuWithResult",
            lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = RuntimeException.class)
    public ProductSkuDTO createSingleProductSkuWithResult(ProductSkuCreateDTO createDTO, List<ProductSkuSyncMsgDTO> msgDTOList) {
        AssertUtils.notNull(createDTO, "参数不能为空");
        AssertUtils.notNull(createDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(createDTO.getProductSpecificationId(), "产品规格ID不能为空");
        AssertUtils.notNull(createDTO.getSaleModel(), "销售模式不能为空");
        LOG.info("创建单个产品sku参数：{}", JSON.toJSONString(createDTO));

        ProductSkuPO productSkuPO = new ProductSkuPO();
        // 优先根据关联的skuId去获取产品信息
        ProductSkuPO refProductSkuPO = productSkuMapper.getProductSkuByRefSkuId(createDTO.getRefProductSkuId(),
                createDTO.getProductSpecificationId(), createDTO.getOwnerId());
        if (refProductSkuPO != null) {
            BeanUtils.copyProperties(refProductSkuPO, productSkuPO);
            productSkuPO.setCityId(createDTO.getCityId());
            productSkuPO.setRefProductSkuId(createDTO.getRefProductSkuId());
            LOG.info("创建单个产品sku-根据关联的sku去获取产品信息：{}", JSON.toJSONString(refProductSkuPO));
        } else {
            // 其次自己组装产品信息
            productSkuPO = createProductSkuPO(createDTO);
            LOG.info("创建单个产品sku-自己组装产品信息：{}", createDTO.getRefProductSkuId());
        }

        // 2、创建sku
        // 处理货主信息
        processOwner(productSkuPO);
        // 判断该城市下sku是否存在，不存在则新增
        ProductSkuPO oldSkuPO = productSkuMapper.getUniqueProductSku(productSkuPO);
        if (Objects.nonNull(oldSkuPO)) {
            LOG.info("单个产品sku存在，不需要创建：{}", JSON.toJSONString(oldSkuPO));
            return null;
        }

        // 处理产品参数
        processParam(productSkuPO);
        // (1)新增sku
        productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
        productSkuPO.setProductSkuId(productSkuPO.getId());
        productSkuPO.setCreateTime(new Date());
        productSkuPO.setLastUpdateTime(new Date());
        productSkuMapper.insertSelective(productSkuPO);
        LOG.info("【酒批】创建单个产品sku：{}", JSON.toJSONString(productSkuPO));

        // (2)保存产品sku配置
        if (createDTO.getWarehouseId() != null) {
            ProductSkuConfigDTO productSkuConfigDTO =
                    ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
            if (productSkuConfigDTO != null) {
                productSkuConfigDTO.setWarehouseId(createDTO.getWarehouseId());
                productSkuConfigBL.fillStorageAttributeBySkuCreate(productSkuConfigDTO, productSkuPO);
                productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, true);
            }
        }

        // (3)同步sku给外部系统
        ProductSkuSyncMsgDTO syncMsgDTO = new ProductSkuSyncMsgDTO();
        BeanUtils.copyProperties(productSkuPO, syncMsgDTO);
        msgDTOList.add(syncMsgDTO);

        ProductSkuDTO dto = new ProductSkuDTO();
        BeanUtils.copyProperties(productSkuPO, dto);

        LOG.info("创建单个产品sku结果：{}", JSON.toJSONString(dto));
        return dto;
    }

    /**
     * 组装ProductSkuPO
     *
     * @return
     */
    private ProductSkuPO createProductSkuPO(ProductSkuCreateDTO productSkuCreateDTO) {
        ProductInfoSpecificationPO specificationPO =
                productInfoSpecificationPOMapper.selectByPrimaryKey(productSkuCreateDTO.getProductSpecificationId());
        if (specificationPO == null) {
            throw new BusinessException("规格id不存在：" + productSkuCreateDTO.getProductSpecificationId());
        }
        ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(specificationPO.getProductInfo_Id());
        if (productInfoPO == null) {
            throw new BusinessException("产品infoId不存在：" + specificationPO.getProductInfo_Id());
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setCityId(productSkuCreateDTO.getCityId());
        productSkuPO.setRefProductSkuId(productSkuCreateDTO.getRefProductSkuId());
        productSkuPO.setProductInfoId(productInfoPO.getId());
        productSkuPO.setProductSpecificationId(specificationPO.getId());
        productSkuPO.setName(productInfoPO.getProductName());
        productSkuPO.setCompanyId(productSkuCreateDTO.getOwnerId());
        productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
        productSkuPO.setSaleModel(
                productSkuCreateDTO.getSaleModel() != null ? productSkuCreateDTO.getSaleModel().intValue() : null);
        productSkuPO.setSpecificationName(specificationPO.getName());
        productSkuPO.setPackageName(specificationPO.getPackageName());
        productSkuPO.setUnitName(specificationPO.getUnitName());
        productSkuPO.setPackageQuantity(specificationPO.getPackageQuantity());
        productSkuPO.setSource(ProductSourceType.易酒批);
        productSkuPO.setUnpackage(-1);
        return productSkuPO;
    }

    /**
     * 批量新增产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO> saveUploadProductInfo(List<ProductUploadDTO> productInfoDTOList) {
        List<com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO> result = new ArrayList<>();
        Map<String, List<ProductUploadDTO>> productMap =
                productInfoDTOList.stream().filter(it -> org.apache.commons.lang3.StringUtils.isNotEmpty(it.getSysCode()))
                        .collect(Collectors.groupingBy(ProductUploadDTO::getSysCode));

        List<ProductUploadDTO> notSyscodeProductList =
                productInfoDTOList.stream().filter(it -> org.apache.commons.lang3.StringUtils.isEmpty(it.getSysCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notSyscodeProductList)) {
            result.addAll(productInfoBySaasBL.saveProductInfoAndThird(notSyscodeProductList));
        }
        for (List<ProductUploadDTO> values : productMap.values()) {
            result.addAll(productInfoBySaasBL.saveProductInfoAndThird(values));
        }
        return result;
    }

    /**
     * 保存产品sku
     *
     * @param orgList
     * @param ownerId
     * @param ownerName
     * @param productSkuList
     * @return
     */
    public List<ProductSkuPO> saveProductSku(List<Integer> orgList, String ownerId, String ownerName,
                                             List<com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO> productSkuList) {
        List<ProductSkuPO> list = new ArrayList<>();
        for (Integer orgId : orgList) {
            ProductSkuImportDTO productSkuImportDTO = new ProductSkuImportDTO();
            productSkuImportDTO.setCityId(orgId);
            ProductSkuOwnerImportDTO ownerImportDTO = new ProductSkuOwnerImportDTO();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(ownerId)) {
                ownerImportDTO.setOwnerId(Long.valueOf(ownerId));
            }
            ownerImportDTO.setOwnerName(ownerName);
            productSkuImportDTO.setOwnerList(Collections.singletonList(ownerImportDTO));
            productSkuImportDTO.setProductList(productSkuList);
            List<ProductSkuPO> productSkuPOS = productSkuBySaasBL.saveProductSku(productSkuImportDTO);
            list.addAll(productSkuPOS);
        }
        LOG.info("【SAAS】产品SKU：{}", JSON.toJSONString(list));
        return list;
    }

    /**
     * 保存产品sku
     *
     * @param warehoseId
     * @param ownerId
     * @param ownerName
     * @param productSkuList
     * @return
     */
    public List<ProductSkuPO> saveProductSku(Integer warehoseId, Integer orgId, String ownerId, String ownerName,
                                             List<com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO> productSkuList) {
        List<ProductSkuPO> list = new ArrayList<>();
        ProductSkuImportDTO productSkuImportDTO = new ProductSkuImportDTO();
        productSkuImportDTO.setCityId(orgId);
        productSkuImportDTO.setWarehouseId(warehoseId);
        ProductSkuOwnerImportDTO ownerImportDTO = new ProductSkuOwnerImportDTO();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ownerId)) {
            ownerImportDTO.setOwnerId(Long.valueOf(ownerId));
        }
        ownerImportDTO.setOwnerName(ownerName);
        productSkuImportDTO.setOwnerList(Arrays.asList(ownerImportDTO));
        productSkuImportDTO.setProductList(productSkuList);
        List<ProductSkuPO> productSkuPOS = productSkuBySaasBL.saveProductSku(productSkuImportDTO);
        list.addAll(productSkuPOS);

        LOG.info("【SAAS】产品SKU：{}", JSON.toJSONString(list));
        return list;
    }

}
