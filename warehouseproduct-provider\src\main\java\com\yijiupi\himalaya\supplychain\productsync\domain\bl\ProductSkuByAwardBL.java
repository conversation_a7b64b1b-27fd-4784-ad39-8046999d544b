package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductByAwardConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardItemMessage;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardMessage;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoItemDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/6/6
 * @description 兑奖产品sku管理
 */
@Service
public class ProductSkuByAwardBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuByAwardBL.class);

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    /**
     * 创建产品sku
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void createProductInfoAndSku(ProductAwardMessage productAwardMessage) {
        // 校验参数
        validateProductInfo(productAwardMessage);

        LOGGER.info("兑奖新增产品信息参数：{}", JSON.toJSONString(productAwardMessage));
        List<ProductAwardItemMessage> productAwardItemMessageList = productAwardMessage.getItems().stream()
            .filter(p -> p != null && p.getId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productAwardItemMessageList)) {
            return;
        }

        List<ProductSkuDTO> productSkuList = new ArrayList<>();
        List<ProductInfoDTO> productInfoDTOList = new ArrayList<>();
        for (ProductAwardItemMessage message : productAwardItemMessageList) {
            ProductInfoDTO productInfoDTO = new ProductInfoDTO();
            productInfoDTO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
            productInfoDTO.setProductName(message.getAwardName());
            productInfoDTO.setShopId(productAwardMessage.getShopId());
            productInfoDTO.setStatus(ProductInfoStateEnum.上架.getType());
            productInfoDTO.setCreateUserId(message.getCreateUserId());
            List<ProductInfoSpecificationDTO> specificationList = new ArrayList<>();
            ProductInfoSpecificationDTO specification = new ProductInfoSpecificationDTO();
            specification.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO_SPEC));
            specification.setProductInfoId(productInfoDTO.getId());
            specification.setName(message.getUnit());
            // specification.setPackageName();
            specification.setUnitName(message.getBaseUnit());
            specification.setPackageQuantity(new BigDecimal(1));
            specification.setState(StateEnum.启用.getType());
            specification.setCreateUserId(message.getCreateUserId());
            specificationList.add(specification);
            productInfoDTO.setSpecificationList(specificationList);
            productInfoDTOList.add(productInfoDTO);

            ProductSkuDTO productSkuDTO = new ProductSkuDTO();
            productSkuDTO.setCityId(null);
            productSkuDTO.setProductSpecificationId(specification.getId());
            productSkuDTO.setProductInfoId(productInfoDTO.getId());
            productSkuDTO.setProductSkuId(message.getId());
            productSkuDTO.setProductType(ProductTypeEnums.奖券.getType());
            productSkuDTO.setRefProductSkuId(message.getRelProductSkuId());
            productSkuList.add(productSkuDTO);
        }

        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return;
        }

        for (ProductInfoDTO productInfoDTO : productInfoDTOList) {
            // 1、新增产品信息
            ProductInfoPO productInfoPO = ProductByAwardConvertor.convertToProductInfoPO(productInfoDTO);
            if (productInfoPO == null) {
                LOGGER.warn("兑奖新增产品信息失败");
            }
            if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
                throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
            }
            productInfoPOMapper.insertSelective(productInfoPO);
            LOGGER.info("兑奖新增产品信息：{}", JSON.toJSONString(productInfoPO));
            // // 2、保存产品条码
            // saveBottleCode(productInfoPO);
            // 3、新增产品信息规格
            List<ProductInfoSpecificationPO> specificationPOS =
                saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoPO.getId());
            // 4、保存产品类目
            saveProductCategory(productInfoPO, null);
        }

        // 新增产品sku
        saveProductSku(productSkuList);
    }

    /**
     * 校验参数
     */
    private void validateProductInfo(ProductAwardMessage productAwardMessage) {
        AssertUtils.notNull(productAwardMessage, "参数不能为空");
        AssertUtils.notNull(productAwardMessage.getShopId(), "兑奖产品店铺id不能为空");
        AssertUtils.notNull(productAwardMessage.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productAwardMessage.getProductSkuId(), "兑奖产品Sku不能为空");
        AssertUtils.notEmpty(productAwardMessage.getItems(), "兑奖项不能为空");
        productAwardMessage.getItems().forEach(p -> {
            AssertUtils.notNull(p.getId(), "兑奖项id不能为空");
            // AssertUtils.notNull(p.getState(), "兑奖项状态不能为空");
            AssertUtils.notNull(p.getAwardType(), "兑奖项类别不能为空");
            AssertUtils.notNull(p.getAwardName(), "兑奖项奖项名称不能为空");
            // AssertUtils.notNull(p.getRelProductSkuId(), "关联SkuId不能为空");
            AssertUtils.notNull(p.getCreateUserId(), "创建人Id不能为空");
        });
    }

    /**
     * 新增产品信息规格
     */
    public List<ProductInfoSpecificationPO>
        saveProductInfoSpecification(List<ProductInfoSpecificationDTO> specificationList, Long productInfoId) {
        if (CollectionUtils.isEmpty(specificationList)) {
            return new ArrayList<>();
        }
        // 判断规格名称是否重复
        validateProductInfoSpecRepeat(specificationList);

        // 1、新增或修改产品规格
        List<ProductInfoSpecificationPO> specificationPOList = specificationList.stream().map(p -> {
            ProductInfoSpecificationPO specificationPO = ProductByAwardConvertor.convertToProductInfoSpecPO(p);
            specificationPO.setProductInfo_Id(productInfoId);
            return specificationPO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(specificationPOList)) {
            return new ArrayList<>();
        }
        productInfoSpecificationPOMapper.insertOrUpdateBatch(specificationPOList);
        LOGGER.info("兑奖产品新增或修改产品规格：{}", JSON.toJSONString(specificationPOList));

        // 2、保存包装条码
        saveBarCode(productInfoId, specificationPOList);
        return specificationPOList;
    }

    /**
     * 判断规格名称是否重复
     */
    private void validateProductInfoSpecRepeat(List<ProductInfoSpecificationDTO> specificationList) {
        if (CollectionUtils.isEmpty(specificationList)) {
            return;
        }
        List<String> specName = new ArrayList<>();
        specificationList.forEach(p -> {
            // 判断规格名称是否重复
            if (specName.contains(p.getName())) {
                throw new BusinessValidateException("产品规格名称（" + p.getName() + "）不能重复，请重新输入！");
            }
            specName.add(p.getName());
        });
    }

    /**
     * 保存包装条码
     */
    private void saveBarCode(Long productInfoId, List<ProductInfoSpecificationPO> specificationPOList) {
        SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
        syncProductCodeInfoDTO.setProductInfoId(productInfoId);
        List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
        specificationPOList.forEach(p -> {
            if (!StringUtils.isEmpty(p.getBarCode())) {
                String[] arr = p.getBarCode().split(",");
                if (arr != null && arr.length > 0) {
                    for (String code : arr) {
                        SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
                        codeInfoItemDTO.setCode(code);
                        codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
                        codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BOX_CODE.getType());
                        codeInfoItemDTO.setProductSpecificationId(p.getId());
                        codeItemDTOList.add(codeInfoItemDTO);
                    }
                }
            }
        });
        if (CollectionUtils.isEmpty(codeItemDTOList)) {
            return;
        }
        syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);
        // 保存条码
        List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
        syncDTOList.add(syncProductCodeInfoDTO);
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
        LOGGER.info("兑奖产品保存包装条码：{}", JSON.toJSONString(syncDTOList));
    }

    /**
     * 保存产品类目
     *
     * @return
     */
    public void saveProductCategory(ProductInfoPO productInfoPO, Integer orgId) {
        setCateGoryIdByName(productInfoPO, orgId);
        LOGGER.info("兑奖产品新增类目参数：{}", JSON.toJSONString(productInfoPO));
        CategorySync categorySync = ProductCategoryConvertor.ProductInfoPO2SyncCategoryTree(productInfoPO, orgId);
        if (categorySync == null) {
            return;
        }
        List<CategorySync> categorySyncs = new ArrayList<>();
        categorySyncs.add(categorySync);
        productCategoryGroupBL.syncCategoryTree(categorySyncs);
        LOGGER.info("兑奖产品保存产品类目：{}", JSON.toJSONString(categorySyncs));
    }

    /**
     * 校验是否类目存在，不存在则新建
     *
     * @param productInfoPO
     * @param orgId
     */
    private void setCateGoryIdByName(ProductInfoPO productInfoPO, Integer orgId) {
        if (productInfoPO.getProductStatisticsClass() != null) {
            return;
        }
        // 格式:白酒-茅台,白酒-剑南春
        String statisticsCategoryName = productInfoPO.getStatisticsCategoryName();
        if (StringUtils.isEmpty(statisticsCategoryName)) {
            return;
        }
        Long categoryGroupId = productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(orgId, null);
        String[] str = statisticsCategoryName.split("-");
        if (str.length > 0) {
            ProductCategoryGroupPO productCategoryGroupPO =
                productCategoryGroupBL.findCategoryByName(str[0], null, categoryGroupId, orgId);
            if (productCategoryGroupPO != null) {
                productInfoPO.setProductStatisticsClass(productCategoryGroupPO.getId());
            } else {
                productInfoPO.setProductStatisticsClass(addCategoryGroup(str[0], categoryGroupId));
            }
        }
        if (str.length > 1) {
            ProductCategoryGroupPO itemPO = productCategoryGroupBL.findCategoryByName(str[1],
                productInfoPO.getProductStatisticsClass(), categoryGroupId, orgId);
            if (itemPO != null) {
                productInfoPO.setSecondStatisticsClass(itemPO.getId());
            } else {
                productInfoPO.setSecondStatisticsClass(addCategoryGroup(str[1], categoryGroupId));
            }
        }
    }

    private Long addCategoryGroup(String name, Long categoryGroupId) {
        LOGGER.info("兑奖产品类目不存在，新增name:{},categoryGroupId：{}", name, categoryGroupId);
        Long id = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CATEGORY_GROUP);
        ProductCategoryGroupDTO productCategoryGroupDTO = new ProductCategoryGroupDTO();
        productCategoryGroupDTO.setName(name);
        productCategoryGroupDTO.setCategoryGroupId(categoryGroupId);
        productCategoryGroupDTO.setSequence(99999);
        productCategoryGroupDTO.setId(id);
        productCategoryGroupDTO.setRefCategoryId(id.toString());
        ProductCategoryGroupDTO categoryGroupDTO =
            productCategoryGroupBL.addProductCategoryGroup(productCategoryGroupDTO);
        return categoryGroupDTO.getId();
    }

    /**
     * 添加产品SKU
     */
    public List<ProductSkuDTO> saveProductSku(List<ProductSkuDTO> productSkuDTOList) {
        LOGGER.info("新增奖券产品SKU参数：{}", JSON.toJSONString(productSkuDTOList));
        List<ProductSkuDTO> skuDTOList = new ArrayList<>();

        for (ProductSkuDTO p : productSkuDTOList) {
            // 1、产品信息是否存在
            ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(p.getProductInfoId());
            if (productInfoPO == null) {

                throw new BusinessException("奖券产品信息不存在：" + p.getProductInfoId());
            }
            // 2、产品规格是否存在
            ProductInfoSpecificationPO specificationPO =
                productInfoSpecificationPOMapper.selectByPrimaryKey(p.getProductSpecificationId());
            if (specificationPO == null) {
                throw new BusinessException("奖券产品规格不存在：" + p.getProductSpecificationId());
            }
            // 3、产品sku是否存在
            ProductSkuPO productSkuPO =
                ProductByAwardConvertor.convertToProductSkuPO(p, productInfoPO, specificationPO, null, "");
            ProductSkuPO skuPO = productSkuMapper.getProductSkuIdDetail(productSkuPO);
            if (skuPO == null) {
                // 保存类目
                saveProductCategory(productInfoPO, productSkuPO.getCityId());
                // 保存sku
                Long skuId = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU);
                productSkuPO.setId(skuId);
                productSkuPO.setProductSkuId(skuId);
                // 设置产品与类目关系id
                setProductInfoCategoryId(productSkuPO);
                productSkuMapper.insertSelective(productSkuPO);
                LOGGER.info("新增奖券产品SKU：{}", JSON.toJSONString(productSkuPO));
                // 保存产品sku配置
                ProductSkuConfigDTO productSkuConfigDTO =
                    ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
                if (productSkuConfigDTO != null) {
                    productSkuConfigDTO.setWarehouseId(p.getWarehouseId());
                    productSkuConfigBL.fillStorageAttributeBySkuCreate(productSkuConfigDTO, productSkuPO);
                    productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, true);
                }
            } else {
                productSkuPO = skuPO;
                LOGGER.info("新增奖券产品失败，该产品sku已存在：{}", JSON.toJSONString(productSkuPO));
            }

            ProductSkuDTO skuDTO = new ProductSkuDTO();
            BeanUtils.copyProperties(productSkuPO, skuDTO);
            skuDTOList.add(skuDTO);
        }

        return skuDTOList;
    }

    /**
     * 设置产品与类目关系id
     */
    private void setProductInfoCategoryId(ProductSkuPO productSkuPO) {
        if (productSkuPO == null || productSkuPO.getCityId() == null) {
            return;
        }
        Long productCategoryGroupId =
            productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(productSkuPO.getCityId(), null);
        productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, productCategoryGroupId);
    }

    /**
     * 创建奖券产品信息
     *
     * @return
     */
    @DistributeLock(conditions = "#productAwardMessage.productSkuId", sleepMills = 30000, expireMills = 30000,
        key = "createProductInfoByAward", lockType = DistributeLock.LockType.WAITLOCK)
    @Transactional(rollbackFor = RuntimeException.class)
    public void createProductInfoByAward(ProductAwardMessage productAwardMessage) {
        // 校验参数
        validateProductInfo(productAwardMessage);

        LOGGER.info("新增奖券产品信息参数：{}", JSON.toJSONString(productAwardMessage));
        List<ProductAwardItemMessage> productAwardItemMessageList = productAwardMessage.getItems().stream()
            .filter(p -> p != null && p.getId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productAwardItemMessageList)) {
            return;
        }

        List<ProductInfoDTO> productInfoDTOList = ProductByAwardConvertor.convertToProductInfoDTO(productAwardMessage);
        LOGGER.info("新增奖券产品信息productInfoDTOList：{}", JSON.toJSONString(productInfoDTOList));
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return;
        }

        for (ProductInfoDTO productInfoDTO : productInfoDTOList) {
            // 1、新增产品信息
            ProductInfoPO productInfoPO = ProductByAwardConvertor.convertToProductInfoPO(productInfoDTO);
            if (productInfoPO == null) {
                LOGGER.warn("新增奖券产品信息失败");
            }

            ProductInfoPO infoOld = productInfoPOMapper.selectByPrimaryKey(productInfoDTO.getId());
            if (infoOld == null) {
                if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
                    throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
                }
                productInfoPOMapper.insertSelective(productInfoPO);
                LOGGER.info("新增奖券产品信息：{}", JSON.toJSONString(productInfoPO));
            }

            // // 2、保存产品条码
            // saveBottleCode(productInfoPO);
            // 3、新增产品信息规格
            List<ProductInfoSpecificationPO> specificationPOS =
                saveProductInfoSpecification(productInfoDTO.getSpecificationList(), productInfoPO.getId());
            // // 4、保存产品类目
            // saveProductCategory(productInfoPO, null);
        }
        LOGGER.info("新增奖券产品信息成功");
    }

    /**
     * 创建奖券产品sku
     *
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public ProductSkuDTO createProductSkuByAward(ProductSkuDTO productSkuDTO) {
        // 校验参数
        AssertUtils.notNull(productSkuDTO, "创建产品sku参数不能为空");
        AssertUtils.notNull(productSkuDTO.getCityId(), "创建奖券sku城市id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getWarehouseId(), "创建奖券sku仓库id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductInfoId(), "创建奖券sku产品信息id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductSpecificationId(), "创建奖券sku规格id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductType(), "创建奖券sku产品类型参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getCreateUserId(), "创建奖券sku创建人id参数不能为空！");
        return saveProductSku(Collections.singletonList(productSkuDTO)).get(0);
    }

    /**
     * 创建奖券产品信息规格及sku信息
     *
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public ProductSkuDTO createProductInfoAndSkuByAward(ProductSkuDTO productSkuDTO) {
        // 校验参数
        AssertUtils.notNull(productSkuDTO, "创建产品sku参数不能为空");
        AssertUtils.notNull(productSkuDTO.getCityId(), "创建奖券sku城市id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getWarehouseId(), "创建奖券sku仓库id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductInfoId(), "创建奖券sku产品信息id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductSpecificationId(), "创建奖券sku规格id参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getProductType(), "创建奖券sku产品类型参数不能为空！");
        AssertUtils.notNull(productSkuDTO.getCreateUserId(), "创建奖券sku创建人id参数不能为空！");
        return saveProductInfoAndSku(Collections.singletonList(productSkuDTO)).get(0);
    }

    /**
     * 添加产品信息规格及SKU信息
     */
    public List<ProductSkuDTO> saveProductInfoAndSku(List<ProductSkuDTO> productSkuDTOList) {
        LOGGER.info("新增奖券产品信息及SKU参数：{}", JSON.toJSONString(productSkuDTOList));
        List<ProductSkuDTO> skuDTOList = new ArrayList<>();

        for (ProductSkuDTO p : productSkuDTOList) {
            // 1、产品信息是否存在
            ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(p.getProductInfoId());
            if (productInfoPO == null) {
                AssertUtils.notNull(p.getName(), "创建奖券sku产品名称参数不能为空！");
                AssertUtils.notNull(p.getPackageName(), "创建奖券sku包装规格大单位参数不能为空！");
                AssertUtils.notNull(p.getUnitName(), "创建奖券sku包装规格小单位参数不能为空！");

                productInfoPO = new ProductInfoPO();
                productInfoPO.setId(p.getProductInfoId());
                productInfoPO.setProductName(p.getName());
                productInfoPO.setGeneralName(productInfoPO.getProductName());
                // 长度为50，太长了截断
                final int maxNameLength = 50;
                if (!StringUtils.isEmpty(productInfoPO.getGeneralName())
                    && productInfoPO.getGeneralName().length() > maxNameLength) {
                    productInfoPO.setGeneralName(productInfoPO.getGeneralName().substring(0, 50));
                }
                productInfoPO.setStatus(ProductInfoStateEnum.上架.getType());
                productInfoPO.setStatisticsCategoryName("兑奖-奖券");
                productInfoPO.setCreateUser_Id(p.getCreateUserId());
                productInfoPO.setLastUpdateUser_Id(p.getCreateUserId());
                productInfoPO.setCreateTime(new Date());
                productInfoPO.setLastUpdateTime(new Date());
                // 新增产品信息
                productInfoPOMapper.insertSelective(productInfoPO);
                LOGGER.info("saveProductInfoAndSku兑奖新增产品信息：{}", JSON.toJSONString(productInfoPO));
            }
            // 2、产品规格是否存在
            ProductInfoSpecificationPO specificationPO =
                productInfoSpecificationPOMapper.selectByPrimaryKey(p.getProductSpecificationId());
            if (specificationPO == null) {
                ProductInfoSpecificationDTO specification = new ProductInfoSpecificationDTO();
                specification.setId(p.getProductInfoId());
                specification.setProductInfoId(p.getProductInfoId());
                specification.setName(p.getUnitName() + "/" + p.getPackageName());
                specification.setPackageName(p.getPackageName());
                specification.setUnitName(p.getUnitName());
                specification.setPackageQuantity(new BigDecimal(1));
                specification.setState(StateEnum.启用.getType());
                specification.setCreateUserId(p.getCreateUserId());
                // 新增产品信息规格
                specificationPO =
                    saveProductInfoSpecification(Collections.singletonList(specification), productInfoPO.getId())
                        .get(0);
            }
            // 3、产品sku是否存在
            ProductSkuPO productSkuPO =
                ProductByAwardConvertor.convertToProductSkuPO(p, productInfoPO, specificationPO, null, "");
            ProductSkuPO skuPO = productSkuMapper.getProductSkuIdDetail(productSkuPO);
            if (skuPO == null) {
                // 保存类目
                saveProductCategory(productInfoPO, productSkuPO.getCityId());
                // 保存sku
                Long skuId = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU);
                productSkuPO.setId(skuId);
                productSkuPO.setProductSkuId(skuId);
                // 设置产品与类目关系id
                setProductInfoCategoryId(productSkuPO);
                productSkuMapper.insertSelective(productSkuPO);
                LOGGER.info("新增奖券产品SKU：{}", JSON.toJSONString(productSkuPO));
                // 保存产品sku配置
                ProductSkuConfigDTO productSkuConfigDTO =
                    ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
                if (productSkuConfigDTO != null) {
                    productSkuConfigDTO.setWarehouseId(p.getWarehouseId());
                    productSkuConfigBL.fillStorageAttributeBySkuCreate(productSkuConfigDTO, productSkuPO);
                    productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, true);
                }
            } else {
                productSkuPO = skuPO;
                LOGGER.info("新增奖券产品失败，该产品sku已存在：{}", JSON.toJSONString(productSkuPO));
            }

            ProductSkuDTO skuDTO = new ProductSkuDTO();
            BeanUtils.copyProperties(productSkuPO, skuDTO);
            skuDTOList.add(skuDTO);
        }

        return skuDTOList;
    }
}
