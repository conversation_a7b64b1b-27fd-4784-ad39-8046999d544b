package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.mq.ProductSkuSyncMQ;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.FinanceProductSkuCreateDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuSyncMsgDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/18
 * @description 融销产品sku管理
 */
@Service
public class ProductSkuByFinanceBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuByFinanceBL.class);

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuBL productSkuBL;

    @Autowired
    private ProductSkuSyncMQ productSkuSyncMQ;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    /**
     * 创建产品sku
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void createProductSkuByFinance(FinanceProductSkuCreateDTO createDTO) {
        LOG.info("ProductSkuByFinanceBL.createProductSku 入参:{}", JSON.toJSONString(createDTO));
        AssertUtils.notNull(createDTO, "创建产品sku参数不能为空");
        AssertUtils.notNull(createDTO.getDealerId(), "创建产品经销商id参数不能为空！");
        AssertUtils.notNull(createDTO.getWarehouseId(), "创建产品sku仓库id参数不能为空！");
        AssertUtils.notEmpty(createDTO.getSkuInfos(), "创建产品sku城市产品信息参数不能为空！");
        createDTO.getSkuInfos().forEach(info -> {
            AssertUtils.notNull(info, "创建产品sku产品信息参数不能为空！");
            AssertUtils.notNull(info.getProductSpecificationId(), "创建产品sku规格id参数不能为空！");
            AssertUtils.notNull(info.getMode(), "创建产品sku模式参数不能为空！");
        });

        // 1、组装产品信息
        List<ProductSkuPO> productSkuPOList = createProductSkuPOS(createDTO);
        LOG.info("ProductSkuByFinanceBL.createProductSkuWithResult 组装产品信息：{}", JSON.toJSONString(productSkuPOList));
        if (CollectionUtils.isEmpty(productSkuPOList)) {
            return;
        }

        List<ProductSkuSyncMsgDTO> msgDTOList = new ArrayList<>();
        for (ProductSkuPO productSkuPO : productSkuPOList) {
            try {
                // 2、创建sku
                // 处理货主信息
                productSkuBL.processOwner(productSkuPO);
                // 判断该城市下sku是否存在，不存在则新增
                ProductSkuPO oldSkuPO = productSkuMapper.getUniqueProductSkuWithSecOwnerId(productSkuPO);
                if (Objects.isNull(oldSkuPO)) {
                    // 处理产品参数
                    productSkuBL.processParam(productSkuPO);
                    // (1)新增sku
                    productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
                    productSkuPO.setProductSkuId(productSkuPO.getId());
                    productSkuPO.setCreateTime(new Date());
                    productSkuPO.setLastUpdateTime(new Date());
                    productSkuMapper.insertSelective(productSkuPO);
                    LOG.info("ProductSkuByFinanceBL.createProductSkuWithResult 创建产品sku：{}",
                        JSON.toJSONString(productSkuPO));

                    // (2)同步sku给外部系统
                    ProductSkuSyncMsgDTO syncMsgDTO = new ProductSkuSyncMsgDTO();
                    BeanUtils.copyProperties(productSkuPO, syncMsgDTO);
                    msgDTOList.add(syncMsgDTO);
                }

                // (3)保存产品sku配置
                ProductSkuConfigDTO productSkuConfigDTO = ProductSkuConfigConvertor
                    .convertorToProductSkuConfigDTO(oldSkuPO != null ? oldSkuPO : productSkuPO);
                if (productSkuConfigDTO != null) {
                    productSkuConfigDTO.setWarehouseId(createDTO.getWarehouseId());
                    if (Objects.isNull(oldSkuPO)) {
                        productSkuConfigBL.fillStorageAttributeBySkuCreate(productSkuConfigDTO, productSkuPO);
                    }
                    productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, true);
                }
            } catch (Exception e) {
                LOG.error("ProductSkuByFinanceBL.createProductSkuWithResult 创建产品sku异常", e);
            }
        }

        // 同步sku给外部系统
        productSkuSyncMQ.send(msgDTOList, true);
    }

    /**
     * 组装ProductSkuPOS
     * 
     * @return
     */
    private List<ProductSkuPO> createProductSkuPOS(FinanceProductSkuCreateDTO createDTO) {
        Warehouse warehouse = warehouseQueryService.findWarehouseById(createDTO.getWarehouseId());
        if (Objects.isNull(warehouse) || Objects.isNull(warehouse.getCityId())) {
            throw new BusinessValidateException("仓库不存在或者没有对应城市，请先同步仓库信息！");
        }
        createDTO.setCityId(warehouse.getCityId());

        List<ProductSkuPO> poList = new ArrayList<>();
        for (FinanceProductSkuCreateDTO.SkuInfoDTO info : createDTO.getSkuInfos()) {
            ProductInfoSpecificationPO specificationPO =
                productInfoSpecificationPOMapper.selectByPrimaryKey(info.getProductSpecificationId());
            if (specificationPO == null) {
                throw new BusinessException("规格id不存在：" + info.getProductSpecificationId());
            }
            ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(specificationPO.getProductInfo_Id());
            if (productInfoPO == null) {
                throw new BusinessException("产品infoId不存在：" + specificationPO.getProductInfo_Id());
            }

            ProductSkuPO productSkuPO = new ProductSkuPO();
            productSkuPO.setCityId(createDTO.getCityId());
            productSkuPO.setProductInfoId(productInfoPO.getId());
            productSkuPO.setProductSpecificationId(specificationPO.getId());
            productSkuPO.setName(productInfoPO.getProductName());
            productSkuPO.setCompanyId(info.getMode());
            productSkuPO.setSecOwnerId(
                !StringUtils.isEmpty(createDTO.getDealerId()) ? Long.valueOf(createDTO.getDealerId()) : null);
            productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
            // productSkuPO.setSaleModel();
            productSkuPO.setSpecificationName(specificationPO.getName());
            productSkuPO.setPackageName(specificationPO.getPackageName());
            productSkuPO.setUnitName(specificationPO.getUnitName());
            productSkuPO.setPackageQuantity(specificationPO.getPackageQuantity());
            productSkuPO.setSource(ProductSourceType.易酒批);
            productSkuPO.setUnpackage(-1);
            productSkuPO.setCreateUserId(createDTO.getUserId());
            productSkuPO.setLastUpdateUserId(createDTO.getUserId());
            poList.add(productSkuPO);
        }

        return poList;
    }

}
