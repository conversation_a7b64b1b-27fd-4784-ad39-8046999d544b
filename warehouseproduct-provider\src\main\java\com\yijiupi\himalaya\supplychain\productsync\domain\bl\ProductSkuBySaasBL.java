package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.product.ScmProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.file.dto.basic.FileInfoDTO;
import com.yijiupi.himalaya.supplychain.file.service.IFileService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductBySaasConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductSyncOpService;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.so.WarehouseSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.search.SpellSearchSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 产品SKU管理SAAS化
 *
 * <AUTHOR>
 * @date 2019-08-27 11:29
 */
@Service
public class ProductSkuBySaasBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuBySaasBL.class);

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;
    @Autowired
    private ProductSkuConfigMapper productSkuConfigMapper;
    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Reference
    private IFileService fileService;

    @Autowired
    private ProductInfoBySaasBL productInfoBySaasBL;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    @Autowired
    private ProductCategoryGroupConfigBL productCategoryGroupConfigBL;
    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Autowired
    private ProductSyncOpService productSyncOpService;

    @Reference
    private IOrgService iOrgService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Reference
    private IProductSkuService productSkuService;

    @Autowired
    private ProductSkuBySaasBL productSkuBySaasBL;

    /**
     * 产品SKU接口
     */
    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductInfoQueryService scmProductInfoQueryService;

    /**
     * 产品SKU管理列表
     */
    public PageList<ProductSkuListDTO> listProductSku(ProductSkuListSO productSkuListSO) {
        AssertUtils.notNull(productSkuListSO, "参数不能为空");
        if (productSkuListSO.getCityId() == null && CollectionUtils.isEmpty(productSkuListSO.getCityIdList())) {
            throw new DataValidateException("城市ID不能为空");
        }
        if (productSkuListSO.getIsDelete() == null) {
            productSkuListSO.setIsDelete(ConditionStateEnum.否.getType());
        }
        // 按产品条码查
        setProductInfoIdByBottleCode(productSkuListSO);
        // 按产品条码/包装条码查
        setProductInfoIdAndSpecIdByCode(productSkuListSO);
        // LOGGER.info("产品sku列表查询参数：{}", JSON.toJSONString(productSkuListSO));
        PageResult<ProductSkuListDTO> pageResult = productSkuMapper.listProductSku(productSkuListSO);
        if (!pageResult.isEmpty()) {
            Set<Long> skuIds = pageResult.stream().map(ProductSkuListDTO::getProductSkuId).collect(Collectors.toSet());
            // 查询产品条码时, cityId 允许传 null
            Integer cityId = Optional.ofNullable(productSkuListSO.getCityIdList())
                    .filter(it -> !CollectionUtils.isEmpty(it))
                    .map(it -> it.get(0)).orElseGet(productSkuListSO::getCityId);
            Map<Long, ProductCodeDTO> codeMap = productSkuService.getPackageAndUnitCode(skuIds, cityId);
            for (ProductSkuListDTO skuInfo : pageResult) {
                ProductCodeDTO codeInfo = codeMap.get(skuInfo.getProductSkuId());
                if (codeInfo != null) {
                    Optional.ofNullable(codeInfo.getUnitCode()).map(it -> String.join(",", it))
                            .ifPresent(skuInfo::setBottleCode);
                    Optional.ofNullable(codeInfo.getPackageCode()).map(it -> String.join(",", it))
                            .ifPresent(skuInfo::setProductCode);
                }
            }
        }
        return pageResult.toPageList();
    }

    public PageList<ProductSkuListDTO> listProductSkuNew(ProductSkuListSO productSkuListSO) {
        PageList<ProductSkuListDTO> pageList = listProductSku(productSkuListSO);
        setProductSkuConfig(pageList, productSkuListSO.getWarehouseId());
        return pageList;
    }

    public void setProductSkuConfig(PageList<ProductSkuListDTO> pageList, Integer warehouseId) {
        if (warehouseId == null || pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return;
        }
        List<Long> skuList =
            pageList.getDataList().stream().map(ProductSkuListDTO::getProductSkuId).collect(Collectors.toList());
        List<ProductSkuConfigPO> skuConfigs =
            productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouseId, skuList);
        if (CollectionUtils.isEmpty(skuConfigs)) {
            return;
        }
        Map<Long, List<ProductSkuConfigPO>> skuConfigMap =
            skuConfigs.stream().collect(Collectors.groupingBy(ProductSkuConfigPO::getProductSkuId));
        pageList.getDataList().forEach(it -> {
            List<ProductSkuConfigPO> configPOS = skuConfigMap.get(it.getProductSkuId());
            if (!CollectionUtils.isEmpty(configPOS)) {
                it.setWarehouseCustodyFee(configPOS.get(0).getWarehouseCustodyFee());
                it.setDeliveryFee(configPOS.get(0).getDeliveryFee());
                it.setDeliveryPayType(configPOS.get(0).getDeliveryPayType());
                it.setSortingFee(configPOS.get(0).getSortingFee());
                it.setUnpackage(configPOS.get(0).getUnpackage());
                it.setProductFeature(configPOS.get(0).getProductFeature());
                it.setMaxInventory(configPOS.get(0).getMaxInventory());
                it.setMinInventory(configPOS.get(0).getMinInventory());
                it.setMaxReplenishment(configPOS.get(0).getMaxReplenishment());
                it.setMinReplenishment(configPOS.get(0).getMinReplenishment());
                it.setIsComplete(configPOS.get(0).getIsComplete());
                it.setPick(configPOS.get(0).getPick());
                it.setSow(configPOS.get(0).getSow());
                it.setInventoryRatio(configPOS.get(0).getInventoryRatio());
                it.setUnique(configPOS.get(0).getUnique());
                it.setFleeGoods(configPOS.get(0).getFleeGoods());
                it.setProductRelevantState(configPOS.get(0).getProductRelevantState());
                it.setCostPrice(configPOS.get(0).getCostPrice());
            }
        });
    }

    /**
     * 根据产品条码，获取产品信息ID集合
     */
    private void setProductInfoIdByBottleCode(ProductSkuListSO productSkuListSO) {
        if (StringUtils.isEmpty(productSkuListSO.getBottleCode())) {
            return;
        }
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setCode(productSkuListSO.getBottleCode());
        conditionDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
        List<ProductCodeInfoDTO> productCodeInfoDTOS =
            productCodeInfoBL.findCodesByCommonDefaultCondition(conditionDTO);
        if (!CollectionUtils.isEmpty(productCodeInfoDTOS)) {
            List<Long> productInfoIds =
                productCodeInfoDTOS.stream().map(p -> p.getProductInfoId()).collect(Collectors.toList());
            // 条码对应的所有产品信息ID
            if (!CollectionUtils.isEmpty(productInfoIds)) {
                LOGGER.info("按产品条码查 productInfoIds：{}", JSON.toJSONString(productInfoIds));
                productSkuListSO.setProductInfoIdList(productInfoIds);
            }
        }
    }

    /**
     * 根据产品条码/包装条码，获取产品信息ID和产品规格ID集合
     */
    private void setProductInfoIdAndSpecIdByCode(ProductSkuListSO productSkuListSO) {
        if (StringUtils.isEmpty(productSkuListSO.getBarCodeOrBoxCode())) {
            return;
        }
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setCode(productSkuListSO.getBarCodeOrBoxCode());
        List<ProductCodeInfoDTO> productCodeInfoDTOS =
            productCodeInfoBL.findCodesByCommonDefaultCondition(conditionDTO);
        if (!CollectionUtils.isEmpty(productCodeInfoDTOS)) {
            List<Long> productInfoIds = new ArrayList<>();
            List<Long> productSpecIds = new ArrayList<>();
            productCodeInfoDTOS.forEach(p -> {
                if (p.getProductSpecificationId() != null) {
                    productSpecIds.add(p.getProductSpecificationId());
                } else if (p.getProductInfoId() != null) {
                    productInfoIds.add(p.getProductInfoId());
                }
            });
            // 条码对应的所有产品信息ID
            if (!CollectionUtils.isEmpty(productInfoIds)) {
                LOGGER.info("按产品条码/包装条码查, productInfoIds：{}", JSON.toJSONString(productInfoIds));
                productSkuListSO.setProductInfoIdList(productInfoIds);
            }
            // 条码对应的所有产品规格ID
            if (!CollectionUtils.isEmpty(productSpecIds)) {
                LOGGER.info("按产品条码/包装条码查, productSpecIds：{}", JSON.toJSONString(productSpecIds));
                productSkuListSO.setProductSpecIdList(productSpecIds);
            }
        }
    }

    /**
     * 获取产品SKU详情
     *
     * @return
     */
    public ProductSkuListDTO getProductSku(Long id) {
        AssertUtils.notNull(id, "产品id不能为空");
        ProductSkuListDTO productSkuListDTO = productSkuMapper.getProductSku(id);
        return productSkuListDTO;
    }

    private List<Integer> getWarehouseIdsByCityId(Integer cityId) {
        List<Integer> result = new ArrayList<>();
        if (cityId == null) {
            return result;
        }
        WarehouseSO warehouseSO = new WarehouseSO();
        warehouseSO.setCityId(cityId);
        warehouseSO.setCurrentPage(1);
        warehouseSO.setPageSize(Integer.MAX_VALUE);
        PageList<Warehouse> warehouseList = iWarehouseQueryService.findWarehouseList(warehouseSO);
        if (warehouseList != null && !CollectionUtils.isEmpty(warehouseList.getDataList())) {
            warehouseList.getDataList().forEach(it -> result.add(it.getId()));
        }
        return result;
    }

    /**
     * 添加产品SKU
     */
    public List<ProductSkuPO> saveProductSku(ProductSkuImportDTO productSkuImportDTO) {
        // 校验参数
        validateProductSku(productSkuImportDTO);
        LOGGER.info("【SAAS】新增产品SKU参数：{}", JSON.toJSONString(productSkuImportDTO));
        List<ProductSkuPO> productSkuPOList = new ArrayList<>();

        List<Integer> warehouseIds = new ArrayList<>();
        if (productSkuImportDTO.getWarehouseId() != null) {
            warehouseIds.add(productSkuImportDTO.getWarehouseId());
        }
        if (CollectionUtils.isEmpty(warehouseIds)) {
            warehouseIds = getWarehouseIdsByCityId(productSkuImportDTO.getCityId());
        }
        for (ProductSkuOwnerImportDTO owner : productSkuImportDTO.getOwnerList()) {
            for (ProductSkuDTO p : productSkuImportDTO.getProductList()) {
                // 1、产品信息是否存在
                ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(p.getProductInfoId());
                if (productInfoPO == null) {
                    throw new BusinessException("产品信息不存在：" + p.getProductInfoId());
                }
                // 2、产品规格是否存在
                ProductInfoSpecificationPO specificationPO =
                    productInfoSpecificationPOMapper.selectByPrimaryKey(p.getProductSpecificationId());
                if (specificationPO == null) {
                    throw new BusinessException("产品规格不存在：" + p.getProductSpecificationId());
                }
                p.setCityId(productSkuImportDTO.getCityId());
                p.setCreateUserId(productSkuImportDTO.getUserId());
                // 3、产品sku是否存在
                ProductSkuPO productSkuPO = ProductBySaasConvertor.convertToProductSkuPO(p, productInfoPO,
                    specificationPO, owner.getOwnerId(), owner.getOwnerName());
                ProductSkuPO skuPO = productSkuMapper.getProductSkuIdDetail(productSkuPO);
                if (skuPO == null) {
                    // 保存类目
                    productInfoBySaasBL.saveProductCategory(productInfoPO, productSkuPO.getCityId());
                    // 保存sku
                    Long skuId = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU);
                    productSkuPO.setId(skuId);
                    productSkuPO.setProductSkuId(skuId);
                    // 设置产品与类目关系id
                    setProductInfoCategoryId(productSkuPO);
                    productSkuMapper.insertSelective(productSkuPO);
                    LOGGER.info("【SAAS】新增产品SKU：{}", JSON.toJSONString(productSkuPO));
                    // 保存产品sku配置
                    ProductSkuConfigDTO productSkuConfigDTO =
                        ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
                    if (productSkuConfigDTO != null) {
                        if (!CollectionUtils.isEmpty(warehouseIds)) {
                            productSkuConfigBL.saveProductSkuConfigByWarehouseIds(warehouseIds, productSkuConfigDTO,
                                true);
                        }
                    }
                } else {
                    productSkuPO = skuPO;
                    LOGGER.info("新增产品失败，该产品sku已存在：{}", JSON.toJSONString(productSkuPO));
                }
                productSkuPOList.add(productSkuPO);
            }
        }
        return productSkuPOList;
    }

    /**
     * 设置产品与类目关系id
     */
    private void setProductInfoCategoryId(ProductSkuPO productSkuPO) {
        if (productSkuPO == null || productSkuPO.getCityId() == null) {
            return;
        }
        Long productCategoryGroupId =
            productCategoryGroupConfigBL.getCategoryGroupIdByOrgId(productSkuPO.getCityId(), null);
        productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, productCategoryGroupId);
    }

    /**
     * 检验添加产品参数
     */
    private void validateProductSku(ProductSkuImportDTO productSkuImportDTO) {
        AssertUtils.notNull(productSkuImportDTO, "参数不能为空");
        AssertUtils.notNull(productSkuImportDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notEmpty(productSkuImportDTO.getOwnerList(), "货主不能为空");
        productSkuImportDTO.getOwnerList().forEach(p -> {
            // AssertUtils.notNull(p.getOwnerId(), "货主ID不能为空");
            AssertUtils.notNull(p.getOwnerName(), "货主名称不能为空");
        });
        AssertUtils.notEmpty(productSkuImportDTO.getProductList(), "产品不能为空");
        productSkuImportDTO.getProductList().forEach(p -> {
            AssertUtils.notNull(p.getProductInfoId(), "产品信息ID不能为空");
            AssertUtils.notNull(p.getProductSpecificationId(), "产品规格ID不能为空");
        });
    }

    /**
     * 修改产品SKU
     */
    public void updateProductSku(ProductSkuDTO productSkuDTO) {
        AssertUtils.notNull(productSkuDTO, "参数不能为空");
        AssertUtils.notNull(productSkuDTO.getId(), "产品ID不能为空");
        LOGGER.info("【SAAS】修改产品SKU参数：{}", JSON.toJSONString(productSkuDTO));
        if (productSkuDTO.getUnpackage() != null && productSkuDTO.getUnpackage().equals(1)) {
            productSkuDTO.setProductFeature(1);
        } else {
            productSkuDTO.setProductFeature(2);
        }
        ProductSkuPO productSkuPO = ProductBySaasConvertor.convertToProductSkuPO(productSkuDTO);
        productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
        LOGGER.info("【SAAS】修改产品SKU：{}", JSON.toJSONString(productSkuPO));
        productSyncOpService.findProductSkuAndSyncOp(productSkuDTO.getId(), productSkuDTO.getLastUpdateUserId());
    }

    /**
     * 修改sku和skuconfig
     *
     * @param productSkuDTO
     */
    public void updateProductSkuNew(ProductSkuDTO productSkuDTO) {
        AssertUtils.notNull(productSkuDTO, "参数不能为空");
        AssertUtils.notNull(productSkuDTO.getId(), "产品主键ID不能为空");
        updateProductSku(productSkuDTO);
        ProductSkuPO oldSkuPO = productSkuMapper.selectByPrimaryKey(productSkuDTO.getId());
        ProductSkuPO productSkuPO = new ProductSkuPO();
        BeanUtils.copyProperties(productSkuDTO, productSkuPO);
        productSkuPO.setProductSkuId(oldSkuPO.getProductSkuId());
        saveProductSkuConfig(productSkuPO, productSkuDTO.getCityId(), productSkuDTO.getWarehouseId());
    }

    private void saveProductSkuConfig(ProductSkuPO productSkuPO, Integer cityId, Integer warehouseId) {
        // 保存产品sku配置
        List<Integer> warehouseIds;
        if (warehouseId == null) {
            warehouseIds = getWarehouseIdsByCityId(cityId);
        } else {
            warehouseIds = Arrays.asList(warehouseId);
        }
        ProductSkuConfigDTO productSkuConfigDTO =
            ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
        if (productSkuConfigDTO != null) {
            if (!CollectionUtils.isEmpty(warehouseIds)) {
                productSkuConfigBL.saveProductSkuConfigByWarehouseIds(warehouseIds, productSkuConfigDTO, true);
            }
        }
    }

    /**
     * 根据包装条码查询产品SKU
     */
    public Map<String, List<ProductSkuListDTO>> getProductSkuByBoxCode(ProductSkuListSO productSkuListSO) {
        AssertUtils.notNull(productSkuListSO, "参数不能为空");
        AssertUtils.notNull(productSkuListSO.getCityId(), "城市ID不能为空");
        AssertUtils.notEmpty(productSkuListSO.getBoxCodeList(), "包装条码不能为空");

        Map<String, List<ProductSkuListDTO>> resultMap = new HashMap<>(16);

        // 1、根据箱码获取产品规格ID
        ProductCodeQueryConditionDTO conditionDTO = new ProductCodeQueryConditionDTO();
        conditionDTO.setCodes(productSkuListSO.getBoxCodeList());
        conditionDTO.setCodeType(ProductCodeTypeEnum.BOX_CODE.getType());
        List<ProductCodeInfoDTO> productCodeInfoDTOS =
            productCodeInfoBL.findCodesByCommonDefaultCondition(conditionDTO);
        if (CollectionUtils.isEmpty(productCodeInfoDTOS)) {
            return null;
        }
        List<Long> productSpecIds = productCodeInfoDTOS.stream().filter(p -> p.getProductSpecificationId() != null)
            .map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productSpecIds)) {
            return null;
        }
        LOGGER.info("根据包装条码获取productSpecIds：{}", JSON.toJSONString(productSpecIds));
        productSkuListSO.setProductSpecIdList(productSpecIds);

        // 2、查询产品sku
        PageResult<ProductSkuListDTO> pageResult = productSkuMapper.listProductSku(productSkuListSO);
        if (pageResult == null || pageResult.toPageList() == null
            || CollectionUtils.isEmpty(pageResult.toPageList().getDataList())) {
            return null;
        }
        List<ProductSkuListDTO> productSkuListDTOS = pageResult.toPageList().getDataList();

        // 3、按条码分组
        productSkuListSO.getBoxCodeList().forEach(boxCode -> {
            // 当前条码对应的规格ID
            List<Long> filterProductSpecIds = productCodeInfoDTOS.stream()
                .filter(p -> Objects.equals(p.getCode(), boxCode) && p.getProductSpecificationId() != null)
                .map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterProductSpecIds)) {
                // 找到指定规格的产品sku
                List<ProductSkuListDTO> filterSkuList = productSkuListDTOS.stream()
                    .filter(p -> filterProductSpecIds.contains(p.getProductSpecificationId()))
                    .collect(Collectors.toList());
                resultMap.put(boxCode, filterSkuList);
            } else {
                resultMap.put(boxCode, null);
            }
        });
        return resultMap;
    }

    /**
     * 据条码或者skuid查询产品SKU
     *
     * @param dto
     * @return
     */
    public Map<String, Map<String, List<ProductSaasSkuDTO>>> getProductSkuBySkuIdAndCodes(ProductSkuQueryDTO dto) {
        Map<String, Map<String, List<ProductSaasSkuDTO>>> resultMap = new HashMap<>(16);
        getProductInfoByBarCode(dto, resultMap);
        getProductInfoBySkuId(dto, resultMap);
        LOGGER.info("获取对应产品信息，skuMap：{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 根据瓶码获取产品信息
     *
     * @param dto
     * @param resultMap
     */
    public void getProductInfoByBarCode(ProductSkuQueryDTO dto,
        Map<String, Map<String, List<ProductSaasSkuDTO>>> resultMap) {
        Map<String, List<ProductSaasSkuDTO>> codeSkuMap = new HashMap<>(16);
        if (CollectionUtils.isEmpty(dto.getBottleCodes())) {
            return;
        }

        // 2、根据规格id获取产品信息
        ProductSaasQueryDTO productSaasQueryDTO = new ProductSaasQueryDTO();
        productSaasQueryDTO.setBottleCodes(dto.getBottleCodes());
        productSaasQueryDTO.setCityId(dto.getCityId());
        List<String> bottleCodes = dto.getBottleCodes();

        List<Long> skuIdList = new ArrayList<>();
        Map<String, Long> bottleAndSkuIdMap = new HashMap<>(16);
        // 3、根据条码获取sku
        // for (String bottleCode : bottleCodes) {
        // List<Long> productSkuIds = getProductSkuIdByCode(bottleCode, dto.getCityId(), dto.getWarehouseId());
        // if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(productSkuIds)) {
        // skuIdList.addAll(productSkuIds);
        // bottleAndSkuIdMap.put(bottleCode, productSkuIds.get(0));
        // }
        // }
        getProductSkuIdByCodes(bottleCodes, dto.getCityId(), dto.getWarehouseId(), skuIdList, bottleAndSkuIdMap);
        if (CollectionUtils.isEmpty(skuIdList)) {
            return;
        }

        ProductSkuListSO so = new ProductSkuListSO();
        so.setCityId(dto.getCityId());
        so.setProductSkuIdList(skuIdList);
        so.setProductState(ProductSkuStateEnum.上架.getType());
        so.setPageNum(1);
        so.setPageSize(Integer.MAX_VALUE);
        PageResult<ProductSaasSkuDTO> productSkuListDTOS = productSkuMapper.listProductSimpleSkuSaas(so);
        if (CollectionUtils.isEmpty(productSkuListDTOS)) {
            return;
        }
        // 4、根据条码和skuid对应的关系，填充sku数据
        Map<Long, List<ProductSaasSkuDTO>> skuMap =
            productSkuListDTOS.stream().collect(Collectors.groupingBy(ProductSaasSkuDTO::getProductSkuId));
        for (Map.Entry<String, Long> entry : bottleAndSkuIdMap.entrySet()) {
            List<ProductSaasSkuDTO> productSaasSkuDTOS = skuMap.get(entry.getValue());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(productSaasSkuDTOS)) {
                getProdcutInfoImg(productSaasSkuDTOS);
                codeSkuMap.put(entry.getKey(), productSaasSkuDTOS);
            }
        }

        resultMap.put("codes", codeSkuMap);
    }

    /**
     * 通过瓶码或者箱码查询sku列表
     *
     * @param code 条码
     * @param cityId
     * @return
     */
    public List<Long> getProductSkuIdByCode(String code, Integer cityId, Integer warehousId) {
        if (cityId == null || StringUtils.isEmpty(code) || warehousId == null) {
            return Collections.EMPTY_LIST;
        }

        Warehouse warehouse = getWarehouseIdById(warehousId);
        if (warehouse == null || warehouse.getShopId() == null) {
            return Collections.EMPTY_LIST;
        }

        List<Long> skuIdList = new ArrayList<>();
        // 1、酒批
        try {
            List<Long> specIds = queryProductInfoSpecByCode(code);
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(specIds)) {
                LOGGER.info("【酒批】按条码查询规格：{}", JSON.toJSONString(specIds));
                // 根据规格id查询skuid
                List<Long> skuIds = productSkuMapper.getSkuIdByCityIdAndSpecId(cityId, specIds, warehouse.getShopId());
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(skuIds)) {
                    skuIdList.addAll(skuIds);
                    LOGGER.info("【酒批】按条码查询sku：{}", JSON.toJSONString(skuIds));
                }
            }
        } catch (Exception e) {
            LOGGER.error("【酒批】按条码查询sku异常", e);
        }
        return skuIdList;
    }

    /**
     * 多线程处理匹配
     *
     * @param bottleCodes
     * @param cityId
     * @param warehousId
     * @param skuIdList
     * @param bottleAndSkuIdMap
     */
    public void getProductSkuIdByCodes(List<String> bottleCodes, Integer cityId, Integer warehousId,
        List<Long> skuIdList, Map<String, Long> bottleAndSkuIdMap) {
        /** 定义线程池大小为5 */
        int thirdPoolSize = 20;
        ThreadFactory namedThreadFactory =
            new ThreadFactoryBuilder().setNameFormat("getProductSkuIdByCodes-pool-%d").build();
        ThreadPoolExecutor pool = new ThreadPoolExecutor(thirdPoolSize, thirdPoolSize, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(), namedThreadFactory);
        // 结束的倒数锁
        final CountDownLatch latch = new CountDownLatch(bottleCodes.size());
        for (String bottleCode : bottleCodes) {
            Runnable runnable = () -> {
                try {
                    List<Long> skuIds = getProductSkuIdByCode(bottleCode, cityId, warehousId);
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(skuIds)) {
                        skuIdList.addAll(skuIds);
                        bottleAndSkuIdMap.put(bottleCode, skuIds.get(0));
                    }
                } finally {
                    // 有一个线程进来就减1
                    latch.countDown();
                }
            };
            pool.submit(runnable);
        }
        /** 采用countlatch的递减方法，同时执行await()方法进行阻塞，每一个线程执行完就进行递减，直到为0才继续执行最终的统计输出代码 */
        try {
            // 阻塞，直到latch为0才执行下面的输出语句
            latch.await();
            LOGGER.info("所有线程执行完毕！");
        } catch (Exception e) {
            throw new BusinessValidateException("线程执行失败");
        }
        pool.shutdown();
    }

    public List<Long> queryProductInfoSpecByCode(String code) {
        List<Long> result = new ArrayList<>();
        Set<Integer> specIdSet = scmProductInfoQueryService.queryProductInfoSpecByCode(code);
        if (CollectionUtils.isEmpty(specIdSet)) {
            return result;
        }
        List<Long> specIds = specIdSet.stream().map(p -> p.longValue()).collect(Collectors.toList());
        result.addAll(specIds);
        return result;
    }

    /**
     * 根据skuId获取产品信息
     *
     * @param dto
     * @param resultMap
     */
    private void getProductInfoBySkuId(ProductSkuQueryDTO dto,
        Map<String, Map<String, List<ProductSaasSkuDTO>>> resultMap) {
        Map<String, List<ProductSaasSkuDTO>> skuMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(dto.getSkuIds())) {
            ProductSaasQueryDTO productSaasQueryDTO = new ProductSaasQueryDTO();
            productSaasQueryDTO.setSkuIds(dto.getSkuIds());
            productSaasQueryDTO.setCityId(dto.getCityId());
            productSaasQueryDTO.setProductState(dto.getProductState());
            List<ProductSaasSkuDTO> productSkuList = productSkuMapper.getProductSkuList(productSaasQueryDTO);
            getProdcutInfoImg(productSkuList);
            Map<Long, List<ProductSaasSkuDTO>> map =
                productSkuList.stream().collect(Collectors.groupingBy(ProductSaasSkuDTO::getProductSkuId));
            for (Long key : map.keySet()) {
                // keySet获取map集合key的集合 然后在遍历key即可
                skuMap.put(key.toString(), map.get(key));
            }
        }
        resultMap.put("sku", skuMap);
    }

    /**
     * 设置产品图片信息
     *
     * @return
     */
    private void getProdcutInfoImg(List<ProductSaasSkuDTO> productInfoDTOs) {
        if (CollectionUtils.isEmpty(productInfoDTOs)) {
            return;
        }
        try {
            for (ProductSaasSkuDTO productInfoDTO : productInfoDTOs) {
                if (StringUtils.isEmpty(productInfoDTO.getDefaultImageFileId())) {
                    continue;
                }
                List<FileInfoDTO> fileInfoDTOS = fileService.listImg(productInfoDTO.getDefaultImageFileId());
                if (!CollectionUtils.isEmpty(fileInfoDTOS)) {
                    List<ProductInfoImgDTO> productInfoImgDTOS = fileInfoDTOS.stream().map(p -> {
                        ProductInfoImgDTO productInfoImgDTO = new ProductInfoImgDTO();
                        productInfoImgDTO.setId(p.getId());
                        productInfoImgDTO.setCloudSrc(p.getCloudSrc());
                        productInfoImgDTO.setBussinessId(p.getBussinessId());
                        return productInfoImgDTO;
                    }).collect(Collectors.toList());
                    // 设置产品图片信息
                    productInfoDTO.setImgList(productInfoImgDTOS);
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取图片信息异常", e);
        }
    }

    /**
     * 产品SKU管理列表
     *
     * @return
     */
    public PageList<ProductSaasSkuDTO> listProductSkuSaas(ProductSkuListSO productSkuListSO) {
        AssertUtils.notNull(productSkuListSO, "参数不能为空");
        AssertUtils.notNull(productSkuListSO.getCityId(), "城市ID不能为空");
        LOGGER.info("产品sku列表查询参数：{}", JSON.toJSONString(productSkuListSO));
        PageResult<ProductSaasSkuDTO> pageResult = productSkuMapper.listProductSkuSaas(productSkuListSO);
        PageList<ProductSaasSkuDTO> productSaasSkuDTOPageList = pageResult.toPageList();
        getProdcutInfoImg(productSaasSkuDTOPageList.getDataList());
        return productSaasSkuDTOPageList;
    }

    /**
     * 产品SKU管理列表
     *
     * @return
     */
    public PageList<ProductSaasSkuDTO> listProductSkuSaasNoImage(ProductSkuListSO productSkuListSO) {
        AssertUtils.notNull(productSkuListSO, "参数不能为空");
        AssertUtils.notNull(productSkuListSO.getCityId(), "城市ID不能为空");
        // LOGGER.info("产品sku列表查询参数：{}", JSON.toJSONString(productSkuListSO));
        PageResult<ProductSaasSkuDTO> pageResult = productSkuMapper.listProductSimpleSkuSaas(productSkuListSO);
        PageList<ProductSaasSkuDTO> productSaasSkuDTOPageList = pageResult.toPageList();
        return productSaasSkuDTOPageList;
    }

    public List<ProductSkuListDTO> findSkuByInfos(Integer cityId, List<Long> productInfoIds) {
        return productSkuMapper.findSkuByInfos(cityId, productInfoIds);
    }

    private Warehouse getWarehouseIdById(Integer warehouseId) {
        WarehouseSO warehouseSO = new WarehouseSO();
        warehouseSO.setWarehouseId(warehouseId);
        warehouseSO.setCurrentPage(1);
        warehouseSO.setPageSize(Integer.MAX_VALUE);
        PageList<Warehouse> warehouseList = iWarehouseQueryService.findWarehouseList(warehouseSO);
        if (warehouseList == null
            || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(warehouseList.getDataList())) {
            return null;
        }
        return warehouseList.getDataList().get(0);
    }

    public PageList<ProductSkuListDTO> getProductSkuList(ProductSkuListSO productSkuListSO) {
        List<Integer> cityIdList = findSelfAndSubOrg(productSkuListSO.getCityId());
        if (CollectionUtils.isEmpty(cityIdList)) {
            throw new BusinessException("城市id不能为空");
        } else {
            productSkuListSO.setCityId(null);
        }
        productSkuListSO.setCityIdList(cityIdList);
        String productName = productSkuListSO.getProductName();
        // 判断是否全是字母
        if (!ObjectUtils.isEmpty(productName) && productName.matches("[a-zA-Z]+")) {
            SpellSearchSO spellSearchSO = new SpellSearchSO();
            spellSearchSO.setQuery(productName);
            List<String> spellSearch = iProductSkuQueryService.getSpellSearch(spellSearchSO);
            if (!ObjectUtils.isEmpty(spellSearch)) {
                productSkuListSO.setProductName(spellSearch.get(0));
            } else {
                return new PageList<>();
            }
        }
        PageList<ProductSkuListDTO> pageList = listProductSku(productSkuListSO);
        setProductSkuConfig(pageList, productSkuListSO.getWarehouseId());
        return pageList;
    }

    /**
     * 如果是顶级组织则查本身以及下级，如果是下级组织只查自己
     *
     * @param cityId
     * @return
     */
    public List<Integer> findSelfAndSubOrg(Integer cityId) {
        List<Integer> cityIdList = new ArrayList<>();
        PageList<OrgDTO> orgDTOPageList = iOrgService.listSelfAndSubById(cityId);
        LOGGER.info("下级以及本组织：" + JSON.toJSONString(orgDTOPageList));
        if (orgDTOPageList != null) {
            if (!CollectionUtils.isEmpty(orgDTOPageList.getDataList())) {
                orgDTOPageList.getDataList().stream().forEach(orgDTO -> {
                    cityIdList.add(orgDTO.getId());
                });
            }
        }
        return cityIdList;
    }

    public PageList<ProductSkuListDTO> saasListProductSku(ProductSkuListSO productSkuListSO) {
        LOGGER.info("查询产品参数:{}", JSON.toJSONString(productSkuListSO));
        List<Integer> cityIdList = findSelfAndSubOrg(productSkuListSO.getCityId());
        if (CollectionUtils.isEmpty(cityIdList)) {
            throw new BusinessException("城市id不能为空");
        } else {
            productSkuListSO.setCityId(null);
        }
        productSkuListSO.setCityIdList(cityIdList);
        String productName = productSkuListSO.getProductName();
        // 判断是否全是字母
        if (!ObjectUtils.isEmpty(productName) && productName.matches("[a-zA-Z]+")) {
            SpellSearchSO spellSearchSO = new SpellSearchSO();
            spellSearchSO.setQuery(productName);
            List<String> spellSearch = iProductSkuQueryService.getSpellSearch(spellSearchSO);
            if (!ObjectUtils.isEmpty(spellSearch)) {
                productSkuListSO.setProductName(spellSearch.get(0));
            } else {
                return new PageList<>();
            }
        }
        PageList<ProductSkuListDTO> productSkuListDTOPageList = productSkuBySaasBL.listProductSku(productSkuListSO);
        return productSkuListDTOPageList;
    }

    /**
     * 更新sku的 库存和补货上下限
     *
     * @param updateDTO
     */
    public void saasUpdateInventoryRange(ProductSkuDTO updateDTO) {
        ProductSkuPO productSkuPO = productSkuMapper.selectByCityIdAndProductSkuId(updateDTO.getCityId(), updateDTO.getId());
        if (Objects.isNull(productSkuPO)) {
            throw new BusinessValidateException("产品信息不存在！");
        }

        ProductSkuPO updateSkuPO = new ProductSkuPO();
        updateSkuPO.setId(productSkuPO.getId());
        updateSkuPO.setMaxInventory(updateDTO.getMaxInventory());
        updateSkuPO.setMinInventory(updateDTO.getMinInventory());
        updateSkuPO.setMaxReplenishment(updateDTO.getMaxReplenishment());
        updateSkuPO.setMinReplenishment(updateDTO.getMinReplenishment());
        productSkuMapper.updateByPrimaryKeySelective(updateSkuPO);
    }
}
