package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncByWineConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoByWineSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByWineSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 微酒产品同步
 *
 * <AUTHOR>
 * @date 2019/3/4 15:20
 */
@Service
public class ProductSkuByWineBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuByWineBL.class);

    private static final Gson GSON = new Gson();

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    /**
     * 新增微酒产品信息
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductInfoByWine(ProductInfoByWineSyncDTO productInfoByWineSyncDTO) {
        AssertUtils.notNull(productInfoByWineSyncDTO, "新增微酒产品信息参数不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getId(), "产品信息id不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getState(), "状态不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getSpecName(), "包装规格名称不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");

        // 1、新增产品info
        ProductInfoPO productInfoPO = ProductSyncByWineConvertor.convertorToProductInfoPO(productInfoByWineSyncDTO);
        productInfoPOMapper.insertSelective(productInfoPO);
        LOG.info("新增微酒产品信息：{}", GSON.toJson(productInfoPO));

        // 2、新增产品规格
        ProductInfoSpecificationPO specificationPO =
            ProductSyncByWineConvertor.convertToProductInfoSpecPO(productInfoByWineSyncDTO);
        productInfoSpecificationPOMapper.insertSelective(specificationPO);
        LOG.info("新增微酒产品规格：{}", GSON.toJson(specificationPO));
    }

    /**
     * 更新微酒产品信息
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductInfoByWine(ProductInfoByWineSyncDTO productInfoByWineSyncDTO) {
        AssertUtils.notNull(productInfoByWineSyncDTO, "更新微酒产品参数不能为空");
        AssertUtils.notNull(productInfoByWineSyncDTO.getId(), "产品信息id不能为空");

        // 1、更新产品info
        ProductInfoPO productInfoPO = ProductSyncByWineConvertor.convertorToProductInfoPO(productInfoByWineSyncDTO);
        productInfoPOMapper.updateByPrimaryKeySelective(productInfoPO);
        LOG.info("更新微酒产品规格：{}", GSON.toJson(productInfoPO));

        // 2、更新产品规格
        ProductInfoSpecificationPO specificationPO =
            ProductSyncByWineConvertor.convertToProductInfoSpecPO(productInfoByWineSyncDTO);
        productInfoSpecificationPOMapper.updateByPrimaryKeySelective(specificationPO);
        LOG.info("更新微酒产品规格：{}", GSON.toJson(specificationPO));
    }

    /**
     * 新增微酒产品sku
     * 
     * @return
     */
    public void saveProductSkuByWine(ProductSkuByWineSyncDTO productSkuByWineSyncDTO) {
        AssertUtils.notNull(productSkuByWineSyncDTO, "新增微酒产品sku参数不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getName(), "产品名称不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getSpecificationName(), "包装规格名称不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getProductState(), "产品状态不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getCompanyId(), "货主ID不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getOwnerName(), "货主名称不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getProductInfoId(), "产品信息id不能为空");

        if (null != productSkuByWineSyncDTO.getPackageQuantity()
            && productSkuByWineSyncDTO.getPackageQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("包装规格大小单位转换系数必须大于0");
        }
        ProductSkuPO productSkuPO = ProductSyncByWineConvertor.convertToProductSkuPO(productSkuByWineSyncDTO);
        productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
        productSkuMapper.insertSelective(productSkuPO);
        LOG.info("新增微酒产品sku：{}", GSON.toJson(productSkuPO));
    }

    /**
     * 更新微酒产品sku
     */
    public void updateProductSkuByWine(ProductSkuByWineSyncDTO productSkuByWineSyncDTO) {
        AssertUtils.notNull(productSkuByWineSyncDTO, "更新微酒产品sku参数不能为空");
        AssertUtils.notNull(productSkuByWineSyncDTO.getProductSkuId(), "产品skuId不能为空");
        if (null != productSkuByWineSyncDTO.getPackageQuantity()
            && productSkuByWineSyncDTO.getPackageQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("包装规格大小单位转换系数必须大于0");
        }
        ProductSkuPO productSkuPO = ProductSyncByWineConvertor.convertToProductSkuPO(productSkuByWineSyncDTO);
        Long id = productSkuMapper.getProductSkuIdByIdAndSource(productSkuByWineSyncDTO.getProductSkuId(),
            ProductSourceType.微酒);
        if (id == null) {
            LOG.info("更新微酒产品sku不存在时，则新增");
            saveProductSkuByWine(productSkuByWineSyncDTO);
        } else {
            productSkuPO.setId(id);
            productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
            LOG.info("更新微酒产品sku：{}", GSON.toJson(productSkuPO));
        }
    }

    @Deprecated
    public ProductSkuListDTO findSkuByInfo(Integer cityId, Long productInfoId, Long secOwnerId) {
        return productSkuMapper.findSkuByInfo(cityId, productInfoId, secOwnerId);
    }
}
