package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.distributedlock.annotation.BatchDistributeLock;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoCategoryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigStorageAttributeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentUpdateParam;
import com.yijiupi.himalaya.supplychain.productsync.util.PageHelperUtils;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductCategoryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StorageAttributeService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig.ProductSkuConfigHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSkuPOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductConfigStorageAttributeUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuStorageAgeModDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;

/**
 * 产品sku配置
 *
 * <AUTHOR>
 * @since 2020-01-08 10:30
 */
@Service
public class ProductSkuConfigBL {
    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;
    @Autowired
    private ProductSkuPOConvertor productSkuPOConvertor;
    @Resource
    private ProductSkuConfigHelper productSkuConfigHelper;
    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;
    @Reference
    protected IContentConfigurationService iContentConfigurationService;

    private final Integer pageSize = 1000;

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCategoryBL.class);

    @Autowired
    private ProductSkuConfigMapper productSkuConfigMapper;

    @Resource
    @Qualifier("productSyncTaskExecutor")
    private Executor executor;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference
    private IProductSkuQueryService productSkuQueryService;

    @Autowired
    private ProductInfoCategoryMapper productInfoCategoryMapper;

    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;

    @Autowired
    private StorageAttributeService storageAttributeService;

    private static final String LOCK_KEY = "supp:updateReplenishment:";

    /**
     * 保存产品sku配置
     */
    public void saveProductSkuConfig(ProductSkuConfigDTO configDTO, Boolean isRelateProduct) {
        try {
            AssertUtils.notNull(configDTO, "保存产品sku配置参数不能为空");
            AssertUtils.notNull(configDTO.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(configDTO.getProductSkuId(), "skuId不能为空");
            Integer warehouseId = configDTO.getWarehouseId();
            Long productSkuId = configDTO.getProductSkuId();
            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
            if (warehouse == null) {
                throw new DataValidateException("保存产品sku配置时找不到仓库：" + warehouseId);
            }

            // // 填充分仓属性
            // fillSkuConfigStorageAttribute(configDTO);

            ProductSkuConfigPO oldConfigPO =
                productSkuConfigMapper.getProductSkuConfigBySkuIdAndWarehouseId(warehouseId, productSkuId);
            if (Objects.isNull(oldConfigPO) || oldConfigPO.getStorageAttribute() == null
                || oldConfigPO.getStorageAttribute() == 0) {
                // 填充分仓属性
                fillSkuConfigStorageAttribute(configDTO);
            }
            ProductSkuConfigPO productSkuConfigPO = new ProductSkuConfigPO();
            BeanUtils.copyProperties(configDTO, productSkuConfigPO);
            // 新增产品sku配置
            if (oldConfigPO == null) {
                productSkuConfigPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU_CONFIG));
                productSkuConfigMapper.insertSelective(productSkuConfigPO);
                LOGGER.info("新增产品sku配置：{}", JSON.toJSONString(productSkuConfigPO));
                // 修改产品sku配置
            } else {
                productSkuConfigPO.setId(oldConfigPO.getId());
                productSkuConfigMapper.updateByPrimaryKeySelective(productSkuConfigPO);
                LOGGER.info("修改产品sku配置：{}", JSON.toJSONString(productSkuConfigPO));
            }
            // 关联产品
            // if (isRelateProduct == null || isRelateProduct) {
            // addRelationGroup(configDTO, warehouse);
            // }
        } catch (Exception e) {
            LOGGER.error("保存产品sku配置异常，请联系管理员！", e);
            throw new BusinessException("保存产品sku配置异常，请联系管理员！param: " + JSON.toJSONString(configDTO), e);
        }
    }

    /**
     * 保存多个仓库产品sku配置
     */
    public void saveProductSkuConfigByWarehouseIds(List<Integer> warehouseIds, ProductSkuConfigDTO configDTO,
        Boolean isRelateProduct) {
        if (org.springframework.util.CollectionUtils.isEmpty(warehouseIds) || configDTO == null) {
            return;
        }
        warehouseIds.forEach(warehouseId -> {
            configDTO.setWarehouseId(warehouseId);
            saveProductSkuConfig(configDTO, isRelateProduct);
        });
    }

    /**
     * 批量保存产品sku配置
     */
    public void saveProductSkuConfigBatch(List<ProductSkuConfigDTO> configDTOS) {
        if (org.springframework.util.CollectionUtils.isEmpty(configDTOS)) {
            return;
        }
        configDTOS.forEach(p -> saveProductSkuConfig(p, true));
    }

    /**
     * 获取skuId所属的仓库
     */
    public Map<Long, Set<Integer>> getProductSkuBelongWarehouseId(Set<Long> skuIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_MAP;
        }
        // 根据skuId查询产品sku配置
        List<ProductSkuConfigPO> productSkuConfigPOS = productSkuConfigMapper.getProductSkuConfigBySkuIds(skuIds);
        if (org.springframework.util.CollectionUtils.isEmpty(productSkuConfigPOS)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, Set<Integer>> resultMap = new HashMap<>(16);
        Map<Long, List<ProductSkuConfigPO>> skuConfigMap =
            productSkuConfigPOS.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
        skuConfigMap.forEach((skuId, list) -> {
            resultMap.put(skuId, list.stream().map(p -> p.getWarehouseId()).collect(Collectors.toSet()));
        });
        return resultMap;
    }

    /**
     * 获取skuId所属的仓库
     */
    public List<Long> getSkuConfigBySkuIdsAndWarehouseId(List<Long> skuIds, Integer warehouesId) {
        if (org.springframework.util.CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        // 根据skuId查询产品sku配置
        List<ProductSkuConfigPO> productSkuConfigPOS =
            productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouesId, skuIds);
        List<Long> resultMap =
            productSkuConfigPOS.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        return resultMap;
    }

    /**
     * 根据sku查询对应的所有仓库
     */
    public List<Integer> getWarehouseIdBySkuId(Long productSkuId) {
        return productSkuConfigMapper.getWarehouseIdBySkuId(productSkuId);
    }

    /**
     * 根据SKUID+仓库ID，查询仓库是否关联产品
     *
     * @param skuIds
     * @param warehouseId
     */
    public List<ProductSkuConfigDTO> findSkuConfigBySkuIdsAndWarehouseId(List<Long> skuIds, Integer warehouseId) {
        if (org.springframework.util.CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        // 根据skuId查询产品sku配置
        List<ProductSkuConfigPO> productSkuConfigPOS =
            productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouseId, skuIds);
        if (org.springframework.util.CollectionUtils.isEmpty(productSkuConfigPOS)) {
            return Collections.emptyList();
        }
        return productSkuConfigPOS.stream().map(m -> {
            ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
            BeanUtils.copyProperties(m, configDTO);
            return configDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 批量更新补货上下限
     *
     * @param param 更新参数
     */
    public void updateReplenishmentInfo(ReplenishmentUpdateParam param) {
        LOGGER.info("批量更新补货上下限信息: {}", JSON.toJSONString(param, SerializerFeature.WriteMapNullValue));
        if (param.getProductSkuQueryDTO() == null) {
            StopWatch stopWatch = new StopWatch("批量更新补货上下限信息");
            stopWatch.start("更新当前页面产品补货上下限信息");
            productSkuConfigHelper.batchUpdateReplenishmentInfo(param);
            stopWatch.stop();
            LOGGER.info("更新操作执行完毕: \n{}", stopWatch.prettyPrint());
        } else {
            String message = "任务正在执行中, 请稍后再试";
            String key = String.format("%s%s", LOCK_KEY, param.getWarehouseId());
            AssertUtils.isTrue(!StringUtils.hasText(stringRedisTemplate.opsForValue().get(key)), message);
            executor.execute(() -> batchAsyncUpdateReplenishmentInfo(param, key));
        }
    }

    private void batchAsyncUpdateReplenishmentInfo(ReplenishmentUpdateParam param, String key) {
        stringRedisTemplate.opsForValue().set(key, "1");
        try {
            StopWatch stopWatch = new StopWatch("批量更新补货上下限信息");
            stopWatch.start("更新所有页面产品补货上下限信息");
            PageHelperUtils.pageConsumer(param::copyQuery, executor, productSkuQueryService::pageList,
                it -> productSkuConfigHelper.batchUpdate(param, it)).get();
            stopWatch.stop();
            LOGGER.info("异步更新操作执行完毕: \n{}", stopWatch.prettyPrint());
        } catch (Throwable t) {
            LOGGER.warn("批量更新补货任务上下限失败: ", t);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    private void fillSkuConfigStorageAttribute(ProductSkuConfigDTO configDTO) {
        if (configDTO.getStorageAttribute() != null) {
            return;
        }

        Integer warehouseId = configDTO.getWarehouseId();
        Long productSkuId = configDTO.getProductSkuId();
        try {
            if (!iWarehouseAllocationConfigService.isEnabledWarehouseSplit(warehouseId)) {
                return;
            }
            // List<WarehouseAllocationConfigDTO> configs =
            // iWarehouseAllocationConfigService.getConfigByWarehouseId(warehouseId);
            // if (configs.isEmpty()) {
            // return;
            // }

            ProductConfigStorageAttributeUpdateDTO queryDTO = new ProductConfigStorageAttributeUpdateDTO();
            queryDTO.setWarehouseId(warehouseId);
            queryDTO.setProductSkuIdList(Arrays.asList(productSkuId));
            Map<Long, Integer> taskPropertyMap = storageAttributeService.getSkuStorageAttributeMap(queryDTO);
            if (taskPropertyMap.isEmpty() || taskPropertyMap.get(productSkuId) == null) {
                return;
            }

            configDTO.setStorageAttribute(taskPropertyMap.get(productSkuId).byteValue());
        } catch (Exception e) {
            LOGGER.error("填充产品sku配置分仓属性异常", e);
        }
    }

    public void fillStorageAttributeBySkuCreate(ProductSkuConfigDTO configDTO, ProductSkuPO newPproductSkuPO) {
        if (Objects.isNull(newPproductSkuPO) || Objects.isNull(newPproductSkuPO.getProductInfoCategoryId())
            || configDTO.getStorageAttribute() != null) {
            return;
        }

        try {
            if (!iWarehouseAllocationConfigService.isEnabledWarehouseSplit(configDTO.getWarehouseId())) {
                return;
            }
            // 获取产品信息类目
            ProductInfoCategoryPO infoCategoryPO =
                productInfoCategoryMapper.selectByPrimaryKey(newPproductSkuPO.getProductInfoCategoryId());
            if (infoCategoryPO == null) {
                LOGGER.error("产品信息类目不存在，categoryId: {}", newPproductSkuPO.getProductInfoCategoryId());
                return;
            }
            LOGGER.info("获取产品信息类目查询结果:{}", JSON.toJSONString(infoCategoryPO));
            // 休食特征类目配置
            String restCategoryValue =
                iContentConfigurationService.getContentValue("FeatureProductDisplayCategory", null, "");
            LOGGER.info("休食特征类目配置查询结果:{}", restCategoryValue);
            // 0:默认,1:酒饮,2:休食
            Byte storageAttribute = null;
            String skuStatisticsClassId = String.valueOf(infoCategoryPO.getStatisticsClass());
            if (restCategoryValue.contains(skuStatisticsClassId)) {
                storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
            } else {
                storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
            }

            configDTO.setStorageAttribute(storageAttribute);
        } catch (Exception e) {
            LOGGER.error("创建sku时填充分仓属性异常", e);
        }
    }

    /**
     * 保存多个仓库产品sku配置
     */
    public void saveProductSkuConfigByWarehouseIds(List<Integer> warehouseIds, ProductSkuConfigDTO configDTO,
        Boolean isRelateProduct, ProductSkuPO newProductSkuPO) {
        if (CollectionUtils.isEmpty(warehouseIds) || configDTO == null) {
            return;
        }
        warehouseIds.forEach(warehouseId -> {
            configDTO.setWarehouseId(warehouseId);
            fillStorageAttributeBySkuCreate(configDTO, newProductSkuPO);
            saveProductSkuConfig(configDTO, isRelateProduct);
        });
    }

    public void deleteByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "入参不能为空");
        productSkuConfigMapper.deleteByPrimaryKeyIn(ids);
    }

    /**
     * 批量更新是否绝对滞销
     */
    @BatchDistributeLock(conditions = "#configDTOS", property = "productSkuId", expireMills = 60000, sleepMills = 60000,
        key = RedisConstant.SUP_F + "updateSkuConfigIsUnsalable", lockType = BatchDistributeLock.LockType.WAITLOCK)
    public void updateSkuConfigIsUnsalable(List<ProductSkuConfigDTO> configDTOS) {
        AssertUtils.notEmpty(configDTOS, "更新sku配置参数不能为空");
        configDTOS.stream().forEach(configDTO -> {
            AssertUtils.notNull(configDTO, "更新参数不能为空");
            AssertUtils.notNull(configDTO.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(configDTO.getProductSkuId(), "skuId不能为空");
        });

        LOGGER.info("批量更新是否绝对滞销入参: {}", JSON.toJSONString(configDTOS));
        configDTOS.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId())).forEach((warehouseId, dtoList) -> {
            List<Long> skuIds = dtoList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            // 更新绝对滞销
            updateIsUnsalable(warehouseId, skuIds);
        });
    }

    public void updateIsUnsalable(Integer warehouseId, List<Long> skuIds) {
        // 绝对滞销标志先清除
        List<Long> unsalableSkuConfigIds = productSkuConfigMapper.listUnsalableSkuConfig(warehouseId, null).stream()
            .filter(p -> p != null).map(p -> p.getId()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unsalableSkuConfigIds)) {
            LOGGER.info("批量清除产品配置为绝对滞销: {}", JSON.toJSONString(unsalableSkuConfigIds));
            productSkuConfigMapper.updateIsUnsalableByConfigIds(YesOrNoEnum.NO.getValue().byteValue(),
                unsalableSkuConfigIds);
        }

        // 按入参更新绝对滞销
        List<Long> updateSkuConfigIds = productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouseId, skuIds)
            .stream().filter(p -> p != null).map(p -> p.getId()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateSkuConfigIds)) {
            LOGGER.info("批量更新产品配置为绝对滞销: {}", JSON.toJSONString(updateSkuConfigIds));
            productSkuConfigMapper.updateIsUnsalableByConfigIds(YesOrNoEnum.YES.getValue().byteValue(),
                updateSkuConfigIds);
        }
    }

    /**
     * 查询所有绝对滞销的skuId
     */
    public List<Long> listUnsalableSkuIds(Integer warehouseId, List<Long> productSkuIds) {
        return productSkuConfigMapper.listUnsalableSkuConfig(warehouseId, productSkuIds).stream().filter(p -> p != null)
            .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
    }

    /**
     * 更批量新产品sku配置
     */
    @BatchDistributeLock(conditions = "#configDTOS", property = "productSkuId", expireMills = 60000, sleepMills = 60000,
        key = RedisConstant.SUP_F + "updateSkuConfigBatch", lockType = BatchDistributeLock.LockType.WAITLOCK)
    public void updateSkuConfigBatch(List<ProductSkuConfigDTO> configDTOS) {
        AssertUtils.notEmpty(configDTOS, "更新sku配置参数不能为空");
        configDTOS.stream().forEach(configDTO -> {
            AssertUtils.notNull(configDTO, "更新参数不能为空");
            AssertUtils.notNull(configDTO.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(configDTO.getProductSkuId(), "skuId不能为空");
        });

        // 查询已存在产品配置
        List<ProductSkuConfigPO> existConfigs = listExistConfigPOS(configDTOS);
        // 组装待更新产品配置数据
        List<ProductSkuConfigPO> updatePOS = getConfigUpdatePOS(configDTOS, existConfigs);
        if (CollectionUtils.isEmpty(updatePOS)) {
            return;
        }
        LOGGER.info("批量更新产品配置数据: {}", JSON.toJSONString(updatePOS));
        productSkuConfigMapper.updateBatchSelective(updatePOS);
    }

    private List<ProductSkuConfigPO> listExistConfigPOS(List<ProductSkuConfigDTO> configDTOS) {
        if (configDTOS.isEmpty()) {
            return Collections.emptyList();
        }

        List<ProductSkuConfigPO> existingConfigs = new ArrayList<>();
        configDTOS.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId())).forEach((warehouseId, dtoList) -> {
            List<Long> skuIds = dtoList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            List<ProductSkuConfigPO> skuConfigPOS =
                productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouseId, skuIds);
            if (CollectionUtils.isEmpty(skuConfigPOS)) {
                return;
            }
            existingConfigs.addAll(skuConfigPOS);
        });

        return existingConfigs;
    }

    private List<ProductSkuConfigPO> getConfigUpdatePOS(List<ProductSkuConfigDTO> configDTOS,
        List<ProductSkuConfigPO> existConfigs) {
        if (existConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, ProductSkuConfigPO> existConfigMap = existConfigs.stream().collect(
            Collectors.toMap(p -> p.getWarehouseId() + "-" + p.getProductSkuId(), Function.identity(), (v1, v2) -> v2));
        List<ProductSkuConfigPO> updatePOS = configDTOS.stream()
            .filter(
                configDTO -> existConfigMap.containsKey(configDTO.getWarehouseId() + "-" + configDTO.getProductSkuId()))
            .map(configDTO -> {
                ProductSkuConfigPO existingConfig =
                    existConfigMap.get(configDTO.getWarehouseId() + "-" + configDTO.getProductSkuId());
                ProductSkuConfigPO productSkuConfigPO = new ProductSkuConfigPO();
                productSkuConfigPO.setId(existingConfig.getId());
                productSkuConfigPO.setState(configDTO.getState());
                productSkuConfigPO.setDeliveryFee(configDTO.getDeliveryFee());
                productSkuConfigPO.setDeliveryPayType(configDTO.getDeliveryPayType());
                productSkuConfigPO.setSortingFee(configDTO.getSortingFee());
                productSkuConfigPO.setUnpackage(configDTO.getUnpackage());
                productSkuConfigPO.setProductFeature(configDTO.getProductFeature());
                productSkuConfigPO.setMaxInventory(configDTO.getMaxInventory());
                productSkuConfigPO.setMinInventory(configDTO.getMinInventory());
                productSkuConfigPO.setMaxReplenishment(configDTO.getMaxReplenishment());
                productSkuConfigPO.setMinReplenishment(configDTO.getMinReplenishment());
                productSkuConfigPO.setIsComplete(configDTO.getIsComplete());
                productSkuConfigPO.setStorageType(configDTO.getStorageType());
                productSkuConfigPO.setPick(configDTO.getPick());
                productSkuConfigPO.setSow(configDTO.getSow());
                productSkuConfigPO.setInventoryRatio(configDTO.getInventoryRatio());
                productSkuConfigPO.setUnique(configDTO.getUnique());
                productSkuConfigPO.setFleeGoods(configDTO.getFleeGoods());
                productSkuConfigPO.setProductRelevantState(configDTO.getProductRelevantState());
                productSkuConfigPO.setProductGrade(configDTO.getProductGrade());
                productSkuConfigPO.setCostPrice(configDTO.getCostPrice());
                productSkuConfigPO.setSellingPrice(configDTO.getSellingPrice());
                productSkuConfigPO.setSellingPriceUnit(configDTO.getSellingPriceUnit());
                productSkuConfigPO.setPalletQuantity(configDTO.getPalletQuantity());
                productSkuConfigPO.setBusinessTag(configDTO.getBusinessTag());
                productSkuConfigPO.setStorageAttribute(configDTO.getStorageAttribute());
                if (!Objects.equals(configDTO.getIsUnsalable(), existingConfig.getIsUnsalable())) {
                    productSkuConfigPO.setIsUnsalable(configDTO.getIsUnsalable());
                }
                return productSkuConfigPO;
            }).collect(Collectors.toList());

        return updatePOS;
    }

    /**
     * 修改库龄
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void modProductSkuStorageAge(ProductSkuStorageAgeModDTO dto) {
        List<ProductSkuConfigPO> productSkuConfigPOList = productSkuConfigMapper
            .getSkuConfigBySkuIdsAndWarehouseId(dto.getWarehouseId(), Collections.singletonList(dto.getProductSkuId()));
        if (CollectionUtils.isEmpty(productSkuConfigPOList)) {
            LOGGER.info("产品sku不存在，入参：{}", JSON.toJSONString(dto));
            return;
        }

        ProductSkuConfigPO productSkuConfigPO = productSkuConfigPOList.stream()
            .collect(Collectors.toMap(ProductSkuConfigPO::getProductSkuId, v -> v)).get(dto.getProductSkuId());

        ProductSkuConfigPO updateProductSkuConfigPO = new ProductSkuConfigPO();
        updateProductSkuConfigPO.setId(productSkuConfigPO.getId());
        updateProductSkuConfigPO.setProductStorageMaxAge(dto.getStorageAge());
        updateProductSkuConfigPO.setLastUpdateUserId(dto.getOptUserId());
        productSkuConfigMapper.updateByPrimaryKeySelective(updateProductSkuConfigPO);
    }
}