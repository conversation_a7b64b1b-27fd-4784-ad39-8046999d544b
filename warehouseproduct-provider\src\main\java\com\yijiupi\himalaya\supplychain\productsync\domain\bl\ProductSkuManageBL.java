package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ScmProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.mq.ProductSkuSyncMQ;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuCreateDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncSO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuSyncMsgDTO;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.EnableState;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.so.WarehouseSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品sku管理
 *
 * <AUTHOR>
 * @date 2019-12-30 15:46
 */
@Service
public class ProductSkuManageBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuManageBL.class);

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuBL productSkuBL;

    @Autowired
    private ProductSkuSyncMQ productSkuSyncMQ;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    /**
     * 创建产品sku
     *
     * @return
     */
    public Map<Long, ProductSkuDTO> createProductSkuWithResult(List<ProductSkuCreateDTO> createDTOList) {
        AssertUtils.notEmpty(createDTOList, "创建产品sku参数不能为空");
        createDTOList.forEach(p -> {
            AssertUtils.notNull(p.getCityId(), "城市ID不能为空");
            AssertUtils.notNull(p.getProductSpecificationId(), "产品规格ID不能为空");
            AssertUtils.notNull(p.getSaleModel(), "销售模式不能为空");
        });
        LOG.info("创建产品sku参数：{}", JSON.toJSONString(createDTOList));

        Map<Long, ProductSkuDTO> resultMap = new HashMap<>(16);
        List<ProductSkuSyncMsgDTO> msgDTOList = new ArrayList<>();
        for (ProductSkuCreateDTO createDto : createDTOList) {
            try {
                ProductSkuDTO createSkuDTO = productSkuBL.createSingleProductSkuWithResult(createDto, msgDTOList);
                if(Objects.nonNull(createSkuDTO)){
                    resultMap.put(Long.valueOf(createDto.getRefProductSkuId()), createSkuDTO);
                }
            } catch (Exception e) {
                LOG.error("【酒批】创建产品sku异常", e);
            }
        }
        // 同步sku给外部系统
        productSkuSyncMQ.send(msgDTOList, true);

        LOG.info("创建产品sku结果：{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 创建产品sku
     * 
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, Long> createProductSku(List<ProductSkuCreateDTO> createDTOList) {
        Map<String, Long> resultMap = new HashMap<>(16);
        Map<Long, ProductSkuDTO> productSkuWithResult = createProductSkuWithResult(createDTOList);
        productSkuWithResult.forEach((sku, po) -> {
            if (po != null) {
                resultMap.put(sku.toString(), po.getProductSkuId());
            }
        });
        return resultMap;
    }

    /**
     * 组装ProductSkuPO
     * 
     * @return
     */
    private ProductSkuPO createProductSkuPO(ProductSkuCreateDTO productSkuCreateDTO) {
        ProductInfoSpecificationPO specificationPO =
            productInfoSpecificationPOMapper.selectByPrimaryKey(productSkuCreateDTO.getProductSpecificationId());
        if (specificationPO == null) {
            throw new BusinessException("规格id不存在：" + productSkuCreateDTO.getProductSpecificationId());
        }
        ProductInfoPO productInfoPO = productInfoPOMapper.selectByPrimaryKey(specificationPO.getProductInfo_Id());
        if (productInfoPO == null) {
            throw new BusinessException("产品infoId不存在：" + specificationPO.getProductInfo_Id());
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setCityId(productSkuCreateDTO.getCityId());
        productSkuPO.setRefProductSkuId(productSkuCreateDTO.getRefProductSkuId());
        productSkuPO.setProductInfoId(productInfoPO.getId());
        productSkuPO.setProductSpecificationId(specificationPO.getId());
        productSkuPO.setName(productInfoPO.getProductName());
        productSkuPO.setCompanyId(productSkuCreateDTO.getOwnerId());
        productSkuPO.setProductState(ProductSkuStateEnum.上架.getType());
        productSkuPO.setSaleModel(
            productSkuCreateDTO.getSaleModel() != null ? productSkuCreateDTO.getSaleModel().intValue() : null);
        productSkuPO.setSpecificationName(specificationPO.getName());
        productSkuPO.setPackageName(specificationPO.getPackageName());
        productSkuPO.setUnitName(specificationPO.getUnitName());
        productSkuPO.setPackageQuantity(specificationPO.getPackageQuantity());
        productSkuPO.setSource(ProductSourceType.易酒批);
        productSkuPO.setUnpackage(-1);
        return productSkuPO;
    }

    /**
     * 手动发送sku同步消息给外部系统
     */
    public void sendProductSkuSyncMQ(Long skuId) {
        AssertUtils.notNull(skuId, "skuId不能为空");
        ProductSkuPO productSkuPO = productSkuMapper.selectByCityIdAndProductSkuId(null, skuId);
        if (productSkuPO == null) {
            throw new BusinessException("sku不存在！");
        }
        if (!Objects.equals(productSkuPO.getSource(), ProductSourceType.易酒批)) {
            throw new BusinessValidateException("只能处理易久批产品！");
        }
        // 同步sku给外部系统
        List<ProductSkuSyncMsgDTO> msgDTOList = new ArrayList<>();
        ProductSkuSyncMsgDTO syncMsgDTO = new ProductSkuSyncMsgDTO();
        BeanUtils.copyProperties(productSkuPO, syncMsgDTO);
        msgDTOList.add(syncMsgDTO);
        productSkuSyncMQ.send(msgDTOList, false);
    }

    /**
     * 手动修改产品sku状态
     */
    public void updateProductState(Long skuId, Integer productState) {
        AssertUtils.notNull(skuId, "skuId不能为空");
        AssertUtils.notNull(productState, "状态不能为空");

        // 1、更新sku
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setProductSkuId(skuId);
        productSkuPO.setProductState(productState);
        productSkuMapper.updateBySkuIdSelective(productSkuPO);
        LOG.info("手动修改产品sku状态:{}", JSON.toJSONString(productSkuPO));

        // 2、发送sku同步消息给外部系统
        sendProductSkuSyncMQ(skuId);
    }

    /**
     * 同步产品sku状态
     */
    public void syncProductSkuState(ProductSkuStateSyncSO syncSO) {
        long start = System.currentTimeMillis();
        LOG.info("warehouseSO={}", JSON.toJSONString(syncSO));
        LOG.info("开始同步产品sku状态");
        // 校正指定城市的sku状态
        if (syncSO != null && syncSO.getCityId() != null) {
            checkProductSkuState(syncSO);
        } else {
            // 不选城市的话，同步所有城市
            WarehouseSO warehouseSO = new WarehouseSO();
            warehouseSO.setState(EnableState.启用.value);
            warehouseSO.setPageSize(Integer.MAX_VALUE);
            List<Warehouse> warehouseList = warehouseQueryService.findWarehouseList(warehouseSO).getDataList();
            if (CollectionUtils.isEmpty(warehouseList)) {
                throw new BusinessException("找不到仓库");
            }
            warehouseList.stream().collect(Collectors.groupingBy(p -> p.getCityId())).forEach((cityId, warehouses) -> {
                try {
                    // 校正产品sku状态
                    syncSO.setCityId(cityId);
                    checkProductSkuState(syncSO);
                    LOG.info("校正产品sku状态成功：{}", cityId);
                } catch (Exception e) {
                    LOG.error("校正产品sku状态异常：{}", cityId);
                }
            });
        }
        long end = System.currentTimeMillis();
        LOG.info("结束同步产品sku状态，耗时：{}ms", end - start);
    }

    /**
     * 校正产品sku状态
     */
    private void checkProductSkuState(ProductSkuStateSyncSO syncSO) {
        AssertUtils.notNull(syncSO, "校正产品sku状态参数不能为空");
        AssertUtils.notNull(syncSO.getCityId(), "城市ID不能为空");
        LOG.info("[校正产品sku状态]参数：{}", JSON.toJSONString(syncSO));

        List<ProductSkuStateSyncDTO> productSkuStateSyncDTOList = productSkuMapper.listProductSkuStateSync(syncSO);
        if (CollectionUtils.isEmpty(productSkuStateSyncDTOList)) {
            return;
        }
        // 上架状态skuId集合
        List<Long> upBySkuIdList = new ArrayList<>();
        // 下架状态skuId集合
        List<Long> downBySkuIdList = new ArrayList<>();

        // 按sku分组
        Map<Long, List<ProductSkuStateSyncDTO>> skuMap =
            productSkuStateSyncDTOList.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
        skuMap.forEach((skuId, skuList) -> {
            if (CollectionUtils.isEmpty(skuList)) {
                return;
            }
            // 查询交易产品状态
            Set<Integer> warehouseIdSet = skuList.stream().map(p -> p.getWarehouseId()).collect(Collectors.toSet());
            List<ScmProductSkuDTO> yjpSkuList = productSkuBL.getScmProductSkuDTOS(skuList.get(0).getSpecId(),
                warehouseIdSet, skuList.get(0).getOwnerId());
            if (CollectionUtils.isEmpty(yjpSkuList)) {
                LOG.info("查询交易产品为空：{}", skuId);
                return;
            }

            // 交易sku只要有一个是上架状态 && 供应链sku状态不是上架状态，则把供应链sku更新为上架状态
            if (yjpSkuList.stream().anyMatch(p -> Objects.equals(p.getState(), ProductSkuStateEnum.上架.getType()))
                && !Objects.equals(skuList.get(0).getProductState(), ProductSkuStateEnum.上架.getType())) {
                upBySkuIdList.add(skuId);
                // 交易sku全都不是上架状态 && 供应链sku状态是上架状态，则供应链sku更新为下架状态
            } else if (yjpSkuList.stream()
                .noneMatch(p -> Objects.equals(p.getState(), ProductSkuStateEnum.上架.getType()))
                && Objects.equals(skuList.get(0).getProductState(), ProductSkuStateEnum.上架.getType())) {
                downBySkuIdList.add(skuId);
            }
        });

        if (!CollectionUtils.isEmpty(upBySkuIdList)) {
            productSkuBL.updateProductState(upBySkuIdList, ProductSkuStateEnum.上架.getType());
        }
        if (!CollectionUtils.isEmpty(downBySkuIdList)) {
            productSkuBL.updateProductState(downBySkuIdList, ProductSkuStateEnum.下架.getType());
        }
    }

    public void updateProductSkuState(ProductSkuStateSyncDTO stateSyncDTO){
        AssertUtils.notNull(stateSyncDTO, "参数不能为空");
        AssertUtils.notEmpty(stateSyncDTO.getSkuIdList(), "skuid不能为空");
        AssertUtils.notNull(stateSyncDTO.getWarehouseId(), "仓库id不能为空");
        LOG.info("更新sku上架状态入参：{}", JSON.toJSONString(stateSyncDTO));
        List<ProductSkuPO> productSkuPOS = productSkuMapper.selectSkuInfoBySkuIds(null, stateSyncDTO.getSkuIdList())
                .stream().filter(p -> Objects.equals(p.getProductState(), ProductSkuStateEnum.下架.getType())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(productSkuPOS)){
            return;
        }

        productSkuPOS.forEach(skuPO -> {
            productSkuBL.processProductState(skuPO, Arrays.asList(stateSyncDTO.getWarehouseId()));
        });

        List<Long> updateSkuIdList = productSkuPOS.stream().filter(p -> Objects.equals(p.getProductState(), ProductSkuStateEnum.上架.getType()))
                .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(updateSkuIdList)){
            return;
        }

        productSkuBL.updateProductState(updateSkuIdList, ProductSkuStateEnum.上架.getType());
    }
}
