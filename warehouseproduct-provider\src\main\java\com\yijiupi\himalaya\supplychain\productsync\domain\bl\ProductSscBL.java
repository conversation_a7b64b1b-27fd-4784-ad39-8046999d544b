package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSscConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductInfoMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductPackageMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductSpecificationMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductInfoSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductPackageSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSkuSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSpecificationSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;

/**
 * 产品中台通用服务
 *
 * <AUTHOR>
 * @date 11/24/20 11:39 AM
 */
@Service
public class ProductSscBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSscBL.class);

    @Autowired
    private UnifyProductInfoMapper unifyProductInfoMapper;

    @Autowired
    private UnifyProductSpecificationMapper unifyProductSpecificationMapper;

    @Autowired
    private UnifyProductPackageMapper unifyProductPackageMapper;

    @Autowired
    private UnifyProductSkuMapper unifyProductSkuMapper;

    @Autowired
    private ProductImageBL productImageBL;

    @Autowired
    private OwnerBL ownerBL;

    /**
     * 保存产品信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveProductInfoSsc(ProductInfoSscDTO productInfoSscDTO) {
        LOG.info("[中台]保存产品信息参数：{}", JSON.toJSONString(productInfoSscDTO));

        // 1、保存产品信息
        UnifyProductInfoPO productInfoPO = ProductSscConvertor.convertToProductInfoPO(productInfoSscDTO);
        AssertUtils.notNull(productInfoPO, "[中台]产品信息不能为空");
        AssertUtils.notNull(productInfoPO.getProductName(), "[中台]产品名称不能为空");
        AssertUtils.notNull(productInfoPO.getState(), "[中台]产品信息状态不能为空");
        // 判断是否存在
        UnifyProductInfoPO oldProductInfoPO = null;
        if (productInfoPO.getId() != null) {
            oldProductInfoPO = unifyProductInfoMapper.selectByPrimaryKey(productInfoPO.getId());
        }
        if (oldProductInfoPO == null) {
            if (productInfoPO.getId() == null) {
                productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UNIFY_PRODUCT_INFO));
            }
            unifyProductInfoMapper.insertSelective(productInfoPO);
            LOG.info("[中台]新增产品信息：{}", JSON.toJSONString(productInfoPO));
        } else {
            unifyProductInfoMapper.updateByPrimaryKeySelective(productInfoPO);
            LOG.info("[中台]修改产品信息：{}", JSON.toJSONString(productInfoPO));
        }
        // 保存产品图片
        if (productInfoPO.getDefaultImageId() != null) {
            productImageBL.saveImage(productInfoPO.getDefaultImageId(), productInfoPO.getId().toString());
        }

        // 2、保存产品信息规格
        if (!CollectionUtils.isEmpty(productInfoSscDTO.getProductSpecificationList())) {
            saveProductSpecificationSsc(productInfoSscDTO.getProductSpecificationList(), productInfoPO.getId());
        }

        // 3、保存产品包装单位
        if (!CollectionUtils.isEmpty(productInfoSscDTO.getProductPackageList())) {
            saveProductPackageSsc(productInfoSscDTO.getProductPackageList(), productInfoPO.getId());
        }
        return productInfoPO.getId();
    }

    /**
     * 保存产品信息规格
     */
    private void saveProductSpecificationSsc(List<ProductSpecificationSscDTO> productSpecificationSscDTOList,
        Long productInfoId) {
        LOG.info("[中台]保存产品信息规格参数：{}", JSON.toJSONString(productSpecificationSscDTOList));
        List<UnifyProductSpecificationPO> specificationPOList =
            ProductSscConvertor.convertToProductSpecificationPO(productSpecificationSscDTOList);
        if (CollectionUtils.isEmpty(specificationPOList)) {
            return;
        }
        specificationPOList.forEach(specificationPO -> {
            if (specificationPO == null) {
                return;
            }
            // infoId为空时
            if (specificationPO.getProductInfoId() == null) {
                specificationPO.setProductInfoId(productInfoId);
            }

            AssertUtils.notNull(specificationPO.getProductInfoId(), "[中台]产品信息规格infoId不能为空");
            AssertUtils.notNull(specificationPO.getName(), "[中台]产品信息规格名称不能为空");

            UnifyProductSpecificationPO oldSpecificationPO = null;
            if (specificationPO.getId() != null) {
                oldSpecificationPO = unifyProductSpecificationMapper.selectByPrimaryKey(specificationPO.getId());
            }

            if (oldSpecificationPO == null) {
                if (specificationPO.getId() == null) {
                    specificationPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UNIFY_PRODUCT_INFO_SPEC));
                }
                unifyProductSpecificationMapper.insertSelective(specificationPO);
                LOG.info("[中台]新增产品规格：{}", JSON.toJSONString(specificationPO));
            } else {
                unifyProductSpecificationMapper.updateByPrimaryKeySelective(specificationPO);
                LOG.info("[中台]修改产品规格：{}", JSON.toJSONString(specificationPO));
            }
        });
    }

    /**
     * 保存产品包装单位
     */
    private void saveProductPackageSsc(List<ProductPackageSscDTO> productPackageSscDTOList, Long productInfoId) {
        LOG.info("[中台]保存产品包装单位参数：{}", JSON.toJSONString(productPackageSscDTOList));
        List<UnifyProductPackagePO> packagePOList =
            ProductSscConvertor.convertToProductPackagePO(productPackageSscDTOList);
        if (CollectionUtils.isEmpty(packagePOList)) {
            return;
        }
        packagePOList.forEach(packagePO -> {
            if (packagePO == null) {
                return;
            }
            // infoId为空时
            if (packagePO.getInfoId() == null) {
                packagePO.setInfoId(productInfoId);
            }

            AssertUtils.notNull(packagePO.getInfoId(), "[中台]产品包装单位infoId不能为空");
            AssertUtils.notNull(packagePO.getName(), "[中台]产品包装单位名称不能为空");

            UnifyProductPackagePO oldPackagePO = null;
            if (packagePO.getId() != null) {
                oldPackagePO = unifyProductPackageMapper.selectByPrimaryKey(packagePO.getId());
            }

            if (oldPackagePO == null) {
                if (packagePO.getId() == null) {
                    packagePO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UNIFY_PRODUCT_PACKAGE));
                }
                unifyProductPackageMapper.insertSelective(packagePO);
                LOG.info("[中台]新增产品包装单位：{}", JSON.toJSONString(packagePO));
            } else {
                unifyProductPackageMapper.updateByPrimaryKeySelective(packagePO);
                LOG.info("[中台]修改产品包装单位：{}", JSON.toJSONString(packagePO));
            }
        });
    }

    /**
     * 保存产品SKU
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveProductSkuSsc(ProductSkuSscDTO productSkuSscDTO) {
        LOG.info("[中台]保存产品SKU参数：{}", JSON.toJSONString(productSkuSscDTO));
        // 参数校验
        UnifyProductSkuPO productSkuPO = ProductSscConvertor.convertToProductSkuPO(productSkuSscDTO);
        AssertUtils.notNull(productSkuPO, "[中台]产品SKU对象不能为空");
        AssertUtils.notNull(productSkuPO.getInfoId(), "[中台]产品SKU对象infoId不能为空");
        AssertUtils.notNull(productSkuPO.getSpecificationId(), "[中台]产品SKU对象规格Id不能为空");
        AssertUtils.notNull(productSkuPO.getPackageId(), "[中台]产品SKU对象包装单位Id不能为空");
        AssertUtils.notNull(productSkuPO.getName(), "[中台]产品SKU对象产品名称不能为空");
        AssertUtils.notNull(productSkuPO.getSpecificationName(), "[中台]产品SKU对象包装规格名称不能为空");
        AssertUtils.notNull(productSkuPO.getPackageName(), "[中台]产品SKU对象包装规格大单位不能为空");
        AssertUtils.notNull(productSkuPO.getUnitName(), "[中台]产品SKU对象包装规格小单位不能为空");
        AssertUtils.notNull(productSkuPO.getPackageQuantity(), "[中台]产品SKU对象包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(productSkuPO.getSource(), "[中台]产品SKU对象产品来源不能为空");

        // 设置货主信息
        setOwner(productSkuPO);

        UnifyProductSkuPO oldProductSkuPO = null;
        if (productSkuPO.getId() != null) {
            oldProductSkuPO = unifyProductSkuMapper.selectByPrimaryKey(productSkuPO.getId());
        }

        // 保存sku
        if (oldProductSkuPO == null) {
            if (productSkuPO.getId() == null) {
                productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UNIFY_PRODUCT_SKU));
            }
            if (productSkuPO.getSkuId() == null) {
                productSkuPO.setSkuId(productSkuPO.getId());
            }
            unifyProductSkuMapper.insertSelective(productSkuPO);
            LOG.info("[中台]新增产品SKU：{}", JSON.toJSONString(productSkuPO));
        } else {
            unifyProductSkuMapper.updateByPrimaryKeySelective(productSkuPO);
            LOG.info("[中台]修改产品SKU：{}", JSON.toJSONString(productSkuPO));
        }
    }

    /**
     * 设置货主信息
     */
    private void setOwner(UnifyProductSkuPO productSkuPO) {
        try {
            if (productSkuPO.getOwnerId() == null || productSkuPO.getOwnerId() == 0) {
                productSkuPO.setOwnerId(null);
                productSkuPO.setOwnerName(ownerBL.getDefaultOwnerName());
            } else {
                if (StringUtils.isEmpty(productSkuPO.getOwnerName())) {
                    OwnerDTO ownerDTO = ownerBL.getOwnerById(productSkuPO.getOwnerId());
                    productSkuPO.setOwnerName(ownerDTO == null ? "" : ownerDTO.getOwnerName());
                }
            }
        } catch (Exception e) {
            LOG.error("[中台]保存产品SKU设置货主信息异常", e);
        }
    }

    /**
     * 批量更新产品包装单位
     */
    public void updateProductPackage(List<ProductPackageSscDTO> productPackageSscDTOList) {
        LOG.info("批量更新产品包装单位参数：{}", JSON.toJSONString(productPackageSscDTOList));
        List<UnifyProductPackagePO> packagePOList =
            ProductSscConvertor.convertToProductPackagePO(productPackageSscDTOList);
        if (CollectionUtils.isEmpty(packagePOList)) {
            return;
        }
        packagePOList.forEach(p -> {
            AssertUtils.notNull(p, "产品包装单位对象不能为空");
            AssertUtils.notNull(p.getId(), "产品包装单位id不能为空");
        });
        unifyProductPackageMapper.updateBatch(packagePOList);
        LOG.info("批量更新产品包装单位：{}", JSON.toJSONString(packagePOList));
    }

    /**
     * 根据规格Id+货主Id查询中台skuId
     */
    public Long getUnifySkuId(Long specificationId, Long ownerId) {
        AssertUtils.notNull(specificationId, "产品规格id不能为空");
        return unifyProductSkuMapper.getUnifySkuId(specificationId, ownerId);
    }
}
