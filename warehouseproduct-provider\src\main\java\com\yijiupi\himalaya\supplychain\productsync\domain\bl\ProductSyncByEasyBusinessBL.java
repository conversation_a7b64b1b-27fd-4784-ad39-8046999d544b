package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncByEasyBusinessConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeInfoStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByEasyBusinessDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoItemDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;

/**
 * 易经商产品同步
 *
 * <AUTHOR>
 * @date 2019/7/4 16:02
 */
@Service
public class ProductSyncByEasyBusinessBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncByEasyBusinessBL.class);

    private static final Gson GSON = new Gson();

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Autowired
    private OwnerBL ownerBL;

    /**
     * 新增产品sku
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addProductSku(List<ProductSkuByEasyBusinessDTO> easyBusinessDTOList) {
        LOG.info("【易经商】新增产品参数：{}", JSON.toJSONString(easyBusinessDTOList));

        if (CollectionUtils.isEmpty(easyBusinessDTOList)) {
            throw new DataValidateException("新增产品SKU参数不能为空");
        }
        easyBusinessDTOList.forEach(p -> {
            validateProductSku(p);
        });
        easyBusinessDTOList = checkIsExist(easyBusinessDTOList);
        if (CollectionUtils.isEmpty(easyBusinessDTOList)) {
            LOG.info("【易经商】新增产品都已存在");
            return;
        }
        LOG.info("【易经商】新增产品总条数：{}", easyBusinessDTOList.size());
        List<ProductSkuPO> productSkuPOS = new ArrayList<>();
        easyBusinessDTOList.forEach(p -> {
            ProductSkuPO productSkuPO = ProductSyncByEasyBusinessConvertor.convertToProductSkuPO(p);
            // 货主名称
            OwnerDTO ownerDTO = ownerBL.getOwnerById(productSkuPO.getCompanyId());
            productSkuPO.setOwnerName(ownerDTO == null ? "" : ownerDTO.getOwnerName());
            // 补充同步对象
            p.setProductInfoId(productSkuPO.getProductInfoId());
            p.setOwnerName(productSkuPO.getOwnerName());
            productSkuPOS.add(productSkuPO);
        });
        productSkuPOS.stream().filter(p -> p.getId() == null)
            .forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU)));
        LOG.info("【易经商】新增产品参数:{}", JSON.toJSONString(easyBusinessDTOList));
        productSkuMapper.insertProductSkuBatchNew(productSkuPOS);
        LOG.info("【易经商】新增产品SKU：{}", GSON.toJson(productSkuPOS));
        Map<Long, List<ProductSkuByEasyBusinessDTO>> productSpecMap = easyBusinessDTOList.stream()
            .collect(Collectors.groupingBy(ProductSkuByEasyBusinessDTO::getProductSpecificationId));

        List<ProductInfoSpecificationPO> productInfoSpecificationPOS = productSkuPOS.stream()
            .map(ProductSyncByEasyBusinessConvertor::convertToProductInfoSpecPO).collect(Collectors.toList());

        productInfoSpecificationPOS.forEach(it -> {
            List<ProductSkuByEasyBusinessDTO> easyBusinessDTOS = productSpecMap.get(it.getId());
            if (CollectionUtils.isNotEmpty(easyBusinessDTOS)) {
                it.setBarCode(easyBusinessDTOS.get(0).getProductboxcode());
            }
        });

        productInfoSpecificationPOMapper.insertBatch(productInfoSpecificationPOS);
        LOG.info("【易经商】新增产品规格：{}", GSON.toJson(productInfoSpecificationPOS));

        List<ProductInfoPO> productInfoPOS = productSkuPOS.stream()
            .map(ProductSyncByEasyBusinessConvertor::convertToProductInfoPO).collect(Collectors.toList());
        Map<Long, List<ProductSkuByEasyBusinessDTO>> infoMap =
            easyBusinessDTOList.stream().collect(Collectors.groupingBy(ProductSkuByEasyBusinessDTO::getProductInfoId));

        productInfoPOS.forEach(it -> {
            List<ProductSkuByEasyBusinessDTO> easyBusinessDTOS = infoMap.get(it.getId());
            if (CollectionUtils.isNotEmpty(easyBusinessDTOS)) {
                it.setBottleCode(easyBusinessDTOS.get(0).getProductcode());
            }
            it.setIsBOM((byte)0);
        });
        productInfoPOMapper.insertBatch(productInfoPOS);

        LOG.info("【易经商】新增产品信息：{}", GSON.toJson(productInfoPOS));
        saveBarCodeAndBottleCode(productInfoPOS, productInfoSpecificationPOS);
    }

    private List<ProductSkuByEasyBusinessDTO> checkIsExist(List<ProductSkuByEasyBusinessDTO> easyBusinessDTOList) {
        List<ProductSkuByEasyBusinessDTO> result = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        easyBusinessDTOList.forEach(it -> skuIds.add(it.getProductSkuId()));
        if (CollectionUtils.isEmpty(skuIds)) {
            return result;
        }
        LOG.info("查询sku参数" + skuIds);
        List<ProductSkuPO> list = productSkuMapper.selectSkuInfoBySkuIds(null, skuIds);
        LOG.info("查询sku存在结果" + JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            return easyBusinessDTOList;
        }
        Map<Long, Long> map = new HashMap<>(16);
        list.forEach(it -> {
            if (it.getProductSkuId() != null) {
                map.put(it.getProductSkuId(), it.getProductSkuId());
            }
        });
        easyBusinessDTOList.forEach(it -> {
            Long skuId = map.get(it.getProductSkuId());
            if (skuId == null) {
                result.add(it);
            }
        });
        return result;
    }

    /**
     * 修改产品sku
     */
    public void updateProductSku(ProductSkuByEasyBusinessDTO easyBusinessDTO) {
        AssertUtils.notNull(easyBusinessDTO, "修改产品SKU参数不能为空");
        AssertUtils.notNull(easyBusinessDTO.getProductSkuId(), "skuId不能为空");

        ProductSkuPO productSkuPO = ProductSyncByEasyBusinessConvertor.convertToProductSkuPO(easyBusinessDTO);
        Long id = productSkuMapper.getProductSkuIdById(easyBusinessDTO.getProductSkuId());
        if (null == id) {
            throw new BusinessException("【易经商】修改产品SKU失败，skuId不存在：{}", easyBusinessDTO.getProductSkuId().toString());
        }
        productSkuPO.setId(id);
        // 产品信息ID不修改
        productSkuPO.setProductInfoId(null);
        productSkuPO.setSource(null);
        productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
        LOG.info("【易经商】修改产品SKU：{}", GSON.toJson(productSkuPO));

    }

    /**
     * 保存产品条码或者箱码
     */
    private void saveBarCodeAndBottleCode(List<ProductInfoPO> productInfoPOS,
        List<ProductInfoSpecificationPO> productInfoSpecificationPOS) {
        LOG.info("【SAAS】保存条码参数productInfoPOS：{}", JSON.toJSONString(productInfoPOS));
        LOG.info("【SAAS】保存条码参数productInfoSpecificationPOS：{}", JSON.toJSONString(productInfoSpecificationPOS));
        Map<Long, List<ProductInfoSpecificationPO>> specMap = productInfoSpecificationPOS.stream()
            .collect(Collectors.groupingBy(ProductInfoSpecificationPO::getProductInfo_Id));
        for (ProductInfoPO productInfoPO : productInfoPOS) {
            saveBottleCode(productInfoPO);
            // 2、保存包装条码
            List<ProductInfoSpecificationPO> specificationPOS = specMap.get(productInfoPO.getId());
            if (CollectionUtils.isNotEmpty(specificationPOS)) {
                saveBarCode(productInfoPO.getId(), specificationPOS);
            }
        }
    }

    /**
     * 保存产品箱码
     */
    private void saveBarCode(Long productInfoId, List<ProductInfoSpecificationPO> specificationPOList) {
        SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
        syncProductCodeInfoDTO.setProductInfoId(productInfoId);
        List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
        specificationPOList.forEach(p -> {
            if (StringUtils.isNotEmpty(p.getBarCode())) {
                String[] arr = p.getBarCode().split(",");
                if (arr != null && arr.length > 0) {
                    for (String code : arr) {
                        SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
                        codeInfoItemDTO.setCode(code);
                        codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
                        codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BOX_CODE.getType());
                        codeInfoItemDTO.setProductSpecificationId(p.getId());
                        codeItemDTOList.add(codeInfoItemDTO);
                    }
                }
            }
        });
        if (CollectionUtils.isEmpty(codeItemDTOList)) {
            return;
        }
        syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);
        // 保存条码
        List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
        syncDTOList.add(syncProductCodeInfoDTO);
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
        LOG.info("【SAAS】保存包装条码：{}", JSON.toJSONString(syncDTOList));
    }

    /**
     * 保存产品瓶码
     */
    private void saveBottleCode(ProductInfoPO productInfoPO) {
        if (StringUtils.isEmpty(productInfoPO.getBottleCode())) {
            return;
        }

        String[] arr = productInfoPO.getBottleCode().split(",");
        if (arr == null || arr.length == 0) {
            return;
        }
        SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
        syncProductCodeInfoDTO.setProductInfoId(productInfoPO.getId());
        List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
        for (String code : arr) {
            SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
            codeInfoItemDTO.setCode(code);
            codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
            codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
            codeInfoItemDTO.setProductInfoId(productInfoPO.getId());
            codeItemDTOList.add(codeInfoItemDTO);
        }
        syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);

        // 保存条码
        List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
        syncDTOList.add(syncProductCodeInfoDTO);
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
        LOG.info("【易经商】保存产品条码：{}", JSON.toJSONString(syncDTOList));
    }

    /**
     * 验证产品Sku参数
     */
    private void validateProductSku(ProductSkuByEasyBusinessDTO easyBusinessDTO) {
        AssertUtils.notNull(easyBusinessDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(easyBusinessDTO.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notNull(easyBusinessDTO.getProductSpecificationId(), "规格id不能为空");
        AssertUtils.notNull(easyBusinessDTO.getName(), "产品名称不能为空");
        AssertUtils.notNull(easyBusinessDTO.getSpecificationName(), "包装规格名称不能为空");
        AssertUtils.notNull(easyBusinessDTO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(easyBusinessDTO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(easyBusinessDTO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(easyBusinessDTO.getProductState(), "产品状态不能为空");
    }
}
