package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncByEasychainConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.easychain.ProductSkuAndWarehouseMessageDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.easychain.ProductSkuMessageDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 易款连锁产品同步
 *
 * <AUTHOR>
 * @date 2019/5/7 21:02
 */
@Service
public class ProductSyncByEasychainBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncByEasychainBL.class);

    private static final Gson GSON = new Gson();

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    /**
     * 同步产品sku
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductSku(ProductSkuMessageDTO productSkuMessageDTO) {
        AssertUtils.notNull(productSkuMessageDTO, "产品SKU参数不能为空");
        AssertUtils.notNull(productSkuMessageDTO.getId(), "skuId不能为空");

        if (productSkuMessageDTO.getSource() == null) {
            productSkuMessageDTO.setSource(ProductSourceType.易款连锁);
        }
        ProductSkuPO productSkuPO = ProductSyncByEasychainConvertor.convertToProductSkuPO(productSkuMessageDTO);
        Long id = productSkuMapper.getProductSkuIdByIdAndSource(productSkuMessageDTO.getId(),
            productSkuMessageDTO.getSource());

        // 仓库
        List<Warehouse> warehouseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productSkuMessageDTO.getWarehouseIdList())) {
            List<Integer> warehouseIds = productSkuMessageDTO.getWarehouseIdList().stream()
                .filter(p -> p != null && p <= Integer.MAX_VALUE).map(p -> p.intValue()).collect(Collectors.toList());
            // 根据仓库查询所属城市
            warehouseList = iWarehouseQueryService.listWarehouseByIds(warehouseIds);
        }

        if (null == id) {
            productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
            validateProductSku(productSkuPO);
            // 设置产品与类目关系id
            productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, ProductCategoryGroupIdConstant.YIJIUPI);
            productSkuMapper.insertSelective(productSkuPO);
            LOG.info("【易款连锁】新增产品SKU：{}", GSON.toJson(productSkuPO));
        } else {
            productSkuPO.setId(id);
            // 设置产品与类目关系id
            productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, ProductCategoryGroupIdConstant.YIJIUPI);
            productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
            LOG.info("【易款连锁】更新产品SKU：{}", GSON.toJson(productSkuPO));
        }

        // 保存产品sku配置
        if (!CollectionUtils.isEmpty(warehouseList)) {
            ProductSkuConfigDTO productSkuConfigDTO =
                ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
            if (productSkuConfigDTO != null) {
                List<Integer> warehouseIds =
                    warehouseList.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
                productSkuConfigBL.saveProductSkuConfigByWarehouseIds(warehouseIds, productSkuConfigDTO, true);
            }
        }
    }

    /**
     * 验证产品Sku参数
     */
    private void validateProductSku(ProductSkuPO productSkuPO) {
        AssertUtils.notNull(productSkuPO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productSkuPO.getProductInfoId(), "产品信息id不能为空");
        AssertUtils.notNull(productSkuPO.getName(), "产品名称不能为空");
        AssertUtils.notNull(productSkuPO.getSpecificationName(), "包装规格名称不能为空");
        AssertUtils.notNull(productSkuPO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(productSkuPO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(productSkuPO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(productSkuPO.getProductState(), "产品状态不能为空");
        AssertUtils.notNull(productSkuPO.getSource(), "产品来源不能为空");
    }

    /**
     * 新增仓库同步产品Sku
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addWarehouseProductSku(ProductSkuAndWarehouseMessageDTO messageDTO) {
        AssertUtils.notNull(messageDTO.getRegionProductInfoId(), "skuId不能为空");
        AssertUtils.notNull(messageDTO.getWarehouseId(), "仓库ID不能为空");
        if (messageDTO.getWarehouseId() > Integer.MAX_VALUE) {
            throw new BusinessException("仓库ID长度超长");
        }
        List<Integer> warehouseIds = new ArrayList<>();
        warehouseIds.add(messageDTO.getWarehouseId().intValue());
        List<Warehouse> warehouseList = iWarehouseQueryService.listWarehouseByIds(warehouseIds);
        if (CollectionUtils.isEmpty(warehouseList)) {
            throw new BusinessException("仓库不存在：" + messageDTO.getWarehouseId());
        }

        ProductSkuPO oldProductSku =
            productSkuMapper.selectByCityIdAndProductSkuId(null, messageDTO.getRegionProductInfoId());
        if (oldProductSku == null) {
            throw new BusinessException(String.format("产品sku不存在：%s", messageDTO.getRegionProductInfoId()));
        }
        ProductSkuPO productSkuPO = new ProductSkuPO();
        productSkuPO.setProductSkuId(oldProductSku.getProductSkuId());
        productSkuPO.setCityId(oldProductSku.getCityId());
        productSkuPO.setProductSpecificationId(oldProductSku.getProductSpecificationId());

        // 保存产品sku配置
        ProductSkuConfigDTO productSkuConfigDTO =
            ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
        if (productSkuConfigDTO != null) {
            productSkuConfigDTO.setWarehouseId(messageDTO.getWarehouseId().intValue());
            productSkuConfigBL.saveProductSkuConfig(productSkuConfigDTO, true);
        }
    }
}
