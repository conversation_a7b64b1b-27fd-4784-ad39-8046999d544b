package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.ssc.BrandQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.ssc.CategoryQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.ssc.ProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ssc.BrandDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ssc.CategoryFullPathDTO;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncBySscConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.aspect.SendFaildMQ;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.*;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.CommonStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductInfoSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSkuSscDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncRetailProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSkuDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLDataException;
import java.util.*;

/**
 * 产品同步（中台）
 *
 * <AUTHOR>
 * @date 11/24/20 11:39 AM
 */
@Service
public class ProductSyncBySscBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncBySscBL.class);

    @Autowired
    private UnifyProductInfoMapper unifyProductInfoMapper;

    @Autowired
    private UnifyProductSpecificationMapper unifyProductSpecificationMapper;

    @Autowired
    private UnifyProductPackageMapper unifyProductPackageMapper;

    @Autowired
    private UnifyProductSkuMapper unifyProductSkuMapper;

    @ReferGateway(path = ServerPath.SSC)
    private CategoryQueryService categoryQueryService;

    @ReferGateway(path = ServerPath.SSC)
    private BrandQueryService brandQueryService;

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    @Autowired
    private ProductSscBL productSscBL;

    @ReferGateway(path = ServerPath.SSC)
    private ProductInfoQueryService productSkuQueryService;

    @Autowired
    private SendFaildMQ sendFaildMQ;

    /**
     * 产品信息同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncProductInfo(SyncSscProductInfoDTO productInfoDTO) {
        LOG.info("[中台]产品信息同步：{}", JSON.toJSONString(productInfoDTO));
        AssertUtils.notNull(productInfoDTO, "产品信息对象不能为空");
        AssertUtils.notNull(productInfoDTO.getId(), "产品信息对象Id不能为空");
        AssertUtils.notNull(productInfoDTO.getName(), "产品信息对象产品名称不能为空");
        AssertUtils.notNull(productInfoDTO.getState(), "产品信息对象状态不能为空");
        AssertUtils.notEmpty(productInfoDTO.getProductSpecifications(), "产品规格对象集合不能为空");
        productInfoDTO.getProductSpecifications().forEach(specDTO -> {
            AssertUtils.notNull(specDTO, "产品规格对象不能为空");
            AssertUtils.notNull(specDTO.getId(), "产品规格对象id不能为空");
            AssertUtils.notNull(specDTO.getState(), "产品规格对象状态不能为空");
        });
        AssertUtils.notEmpty(productInfoDTO.getProductPackages(), "产品包装单位对象集合不能为空");
        productInfoDTO.getProductPackages().forEach(packageDTO -> {
            AssertUtils.notNull(packageDTO, "产品包装单位对象不能为空");
            AssertUtils.notNull(packageDTO.getId(), "产品包装单位对象id不能为空");
            AssertUtils.notNull(packageDTO.getState(), "产品包装单位对象状态不能为空");
            AssertUtils.notNull(packageDTO.getName(), "产品包装单位对象名称不能为空");
        });

        // 产品信息对象转换
        ProductInfoSscDTO productInfoSscDTO = ProductSyncBySscConvertor.convertToProductInfoSscDTO(productInfoDTO);
        // 处理类目
        setCategory(productInfoSscDTO, productInfoDTO.getCategoryId());
        // 处理品牌
        setBrand(productInfoSscDTO, productInfoDTO.getBrandId());
        // 保存产品信息
        productSscBL.saveProductInfoSsc(productInfoSscDTO);
    }

    /**
     * 产品SKU同步
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncProductSku(SyncSscProductSkuDTO productSkuDTO) {
        LOG.info("[中台]产品SKU同步：{}", JSON.toJSONString(productSkuDTO));
        AssertUtils.notNull(productSkuDTO, "产品SKU对象不能为空");
        AssertUtils.notNull(productSkuDTO.getId(), "产品SKU对象skuId不能为空");
        AssertUtils.notNull(productSkuDTO.getProductInfoId(), "产品SKU对象infoId不能为空");
        AssertUtils.notNull(productSkuDTO.getProductSpecificationId(), "产品SKU对象规格Id不能为空");
        AssertUtils.notNull(productSkuDTO.getProductPackageId(), "产品SKU对象包装单位Id不能为空");
        AssertUtils.notNull(productSkuDTO.getSpecificationQuantity(), "产品SKU对象大包装系数不能为空");
        AssertUtils.notNull(productSkuDTO.getState(), "产品SKU对象状态不能为空");
        // 产品sku对象转换
        ProductSkuSscDTO productSkuSscDTO = ProductSyncBySscConvertor.convertToProductSkuSscDTO(productSkuDTO);
        // 1、设置产品sku基本信息
        setBasicProductSku(productSkuSscDTO);
        // 2、设置产品与类目关系id
        setInfoCategoryId(productSkuSscDTO);
        // 3、保存产品sku
        productSscBL.saveProductSkuSsc(productSkuSscDTO);
    }

    /**
     * 处理类目
     */
    private void setCategory(ProductInfoSscDTO productInfoSscDTO, Integer categoryId) {
        if (categoryId == null) {
            return;
        }
        try {
            // 查询类目
            CategoryFullPathDTO categoryFullPathDTO = categoryQueryService.listFullCategoryPath(categoryId);
            if (categoryFullPathDTO == null) {
                throw new BusinessException("获取类目为空：" + categoryId);
            }
            LOG.info("[中台]获取类目：{}", JSON.toJSONString(categoryFullPathDTO));
            // 设置类目
            productInfoSscDTO.setStatisticsCategoryName(categoryFullPathDTO.getCategoryFullName());

            // 同步类目
            try {
                List<CategorySync> categorySync = ProductCategoryConvertor
                    .ProductInfo2SyncCategoryTree(productInfoSscDTO.getId(), categoryFullPathDTO);
                productCategoryGroupBL.syncCategoryByInfoId(categorySync);
            } catch (Exception e) {
                LOG.error("[中台]同步类目异常", e);
            }

        } catch (Exception e) {
            LOG.error("[中台]产品信息同步获取类目异常", e);
        }
    }

    /**
     * 处理品牌
     */
    private void setBrand(ProductInfoSscDTO productInfoSscDTO, Integer brandId) {
        if (brandId == null) {
            return;
        }
        try {
            // 查询品牌
            BrandDTO brandDTO = brandQueryService.findById(brandId);
            if (brandDTO == null) {
                throw new BusinessException("获取品牌为空：" + brandId);
            }
            LOG.info("[中台]获取品牌：{}", JSON.toJSONString(brandDTO));
            // 设置品牌
            productInfoSscDTO.setBrand(brandDTO.getName());
        } catch (Exception e) {
            LOG.error("[中台]产品信息同步获取品牌异常", e);
        }
    }

    /**
     * 设置产品Sku基本信息
     */
    private void setBasicProductSku(ProductSkuSscDTO productSkuSscDTO) {
        // 1、查询产品信息
        UnifyProductInfoPO productInfoPO = unifyProductInfoMapper.selectByPrimaryKey(productSkuSscDTO.getInfoId());
        if (productInfoPO == null) {
            throw new BusinessException("[中台]产品SKU同步失败，查询产品信息为空：" + productSkuSscDTO.getInfoId());
        }
        productSkuSscDTO.setName(productInfoPO.getProductName());
        productSkuSscDTO.setBrand(productInfoPO.getBrand());

        // 2、查询产品信息规格
        if (StringUtils.isEmpty(productSkuSscDTO.getSpecificationName())) {
            UnifyProductSpecificationPO productSpecificationPO =
                unifyProductSpecificationMapper.selectByPrimaryKey(productSkuSscDTO.getSpecificationId());
            if (productSpecificationPO == null) {
                throw new BusinessException("[中台]产品SKU同步失败，查询产品信息规格为空：" + productSkuSscDTO.getSpecificationId());
            }
            productSkuSscDTO.setSpecificationName(productSpecificationPO.getName());
        }

        // 3、查询产品包装单位
        UnifyProductPackagePO productPackagePO = unifyProductPackageMapper
            .getProductPackageIncludeChild(productSkuSscDTO.getPackageId(), productSkuSscDTO.getPackageQuantity());
        if (productPackagePO == null) {
            throw new BusinessException(String.format("[中台]产品SKU同步失败，查询产品包装单位为空，包装单位id：%s，转化系数：%s",
                productSkuSscDTO.getPackageId(), productSkuSscDTO.getPackageQuantity()));
        }
        productSkuSscDTO.setPackageName(productPackagePO.getName());
        productSkuSscDTO.setUnitName(productPackagePO.getChildPackageName());

        // 4、查询产品sku
        UnifyProductSkuPO productSkuPO = unifyProductSkuMapper.getBySkuId(productSkuSscDTO.getSkuId());
        if (productSkuPO != null) {
            productSkuSscDTO.setId(productSkuPO.getId());
        }
    }

    /**
     * 设置产品与类目关系id
     */
    private void setInfoCategoryId(ProductSkuSscDTO productSkuSscDTO) {
        try {
            Long infoCategoryId = productInfoCategoryBL.getProductInfoCategoryId(productSkuSscDTO.getInfoId(),
                ProductCategoryGroupIdConstant.YIJIUPI);
            if (infoCategoryId != null) {
                productSkuSscDTO.setInfoCategoryId(infoCategoryId);
            }
        } catch (Exception e) {
            LOG.error("[中台]产品SKU同步设置产品与类目关系id异常", e);
        }
    }

    /**
     * 中台sku拆零时同步中台sku
     */
    @Async
    public void syncProductSkuOnSplitPackage(SyncRetailProductSkuDTO fromSku, SyncRetailProductSkuDTO toSku,
        ProductInfoSpecificationPO specPO, Long infoId) {
        SyncSscProductSkuDTO unifySku = null;
        try {
            Thread.sleep(3000);

            unifySku = new SyncSscProductSkuDTO();
            unifySku.setId(toSku.getYjpProductSkuId());
            unifySku.setProductInfoId(infoId);
            unifySku.setProductSpecificationId(toSku.getYjpProductInfoSpecificationId());
            unifySku.setProductPackageId(toSku.getYjpProductPackageId());
            unifySku.setSpecificationQuantity(fromSku.getYjpProductSkuQuantity());
            unifySku.setState(Integer.valueOf(CommonStateEnum.启用.getType()));
            unifySku.setCompanyId(Objects.equals(toSku.getCompanyId(), 0L) ? null : toSku.getCompanyId());
            unifySku.setNewSpecificationName(specPO.getName());
            this.syncProductSku(unifySku);
        } catch (Exception ex) {
            LOG.info("[中台sku拆零]同步中台sku失败", ex);
            if (unifySku != null) {
                sendFaildMQ.mqSendFaild(JSON.toJSONString(unifySku), "mq.supplychain.ssc.productSkuSync", ex);
            }
        }
    }
}
