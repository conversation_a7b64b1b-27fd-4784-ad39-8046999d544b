package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSkuConfigConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncByZhzgConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCategoryGroupIdConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.StateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductFeatureConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoSpecDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知花知果产品同步
 *
 * <AUTHOR>
 * @date 2019/3/18 17:24
 */
@Service
public class ProductSyncByZhzgBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncByZhzgBL.class);

    private static final Gson GSON = new Gson();

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Reference
    private IOrgService iOrgService;

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    /**
     * 同步产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductInfo(SyncProductInfoDTO syncProductInfoDTO) {
        AssertUtils.notNull(syncProductInfoDTO, "产品信息参数不能为空");
        AssertUtils.notNull(syncProductInfoDTO.getId(), "产品信息Id不能为空");
        // 1、同步产品信息
        processProudctInfo(syncProductInfoDTO);
        // 2、同步产品信息规格
        processProductInfoSpec(syncProductInfoDTO);
        // 3、同步产品类目
        processProductCategory(syncProductInfoDTO);
    }

    /**
     * 处理产品信息
     */
    private void processProudctInfo(SyncProductInfoDTO syncProductInfoDTO) {
        ProductInfoPO productInfoPO = ProductSyncByZhzgConvertor.convertorToProductInfoPO(syncProductInfoDTO);
        ProductInfoPO oldProductInfoPO = productInfoPOMapper.selectByPrimaryKey(productInfoPO.getId());
        if (null == oldProductInfoPO) {
            // 校验参数
            validateProductInfo(productInfoPO);
            productInfoPOMapper.insertSelective(productInfoPO);
            LOG.info("【知花知果】新增产品信息：{}", GSON.toJson(productInfoPO));

        } else {
            productInfoPOMapper.updateByPrimaryKeySelective(productInfoPO);
            LOG.info("【知花知果】更新产品信息：{}", GSON.toJson(productInfoPO));

            // 当产品信息表的产品名称修改时，产品sku同时要更新
            if (!StringUtils.isEmpty(productInfoPO.getProductName())) {
                ProductSkuPO productSkuPO = ProductSyncByZhzgConvertor.convertToProductSkuPO(productInfoPO);
                productSkuMapper.updateSelective(productSkuPO);
                LOG.info("【知花知果】更新产品信息的产品名称，同时更新产品sku：{}", GSON.toJson(productSkuPO));
            }
        }
    }

    /**
     * 处理产品信息规格
     */
    private void processProductInfoSpec(SyncProductInfoDTO syncProductInfoDTO) {
        List<SyncProductInfoSpecDTO> specDTOList = syncProductInfoDTO.getSyncProductInfoSpecDTOList();
        if (!CollectionUtils.isEmpty(specDTOList)) {
            specDTOList.forEach(p -> {
                saveProductInfoSpec(p);
            });
        }
    }

    /**
     * 处理产品类目
     */
    private void processProductCategory(SyncProductInfoDTO syncProductInfoDTO) {
        CategorySync categorySync = ProductCategoryConvertor.SyncProductInfoDTO2SyncCategoryTree(syncProductInfoDTO);
        if (categorySync == null) {
            LOG.info("获取不到展示类目：{}", JSON.toJSONString(syncProductInfoDTO));
            return;
        }
        List<CategorySync> categorySyncs = new ArrayList<>();
        categorySyncs.add(categorySync);
        productCategoryGroupBL.syncCategoryTree(categorySyncs);

        // ProductCategoryPO productCategoryPO =
        // ProductCategoryConvertor.convertorTOProductCategoryPO(syncProductInfoDTO);
        // if (null != productCategoryPO) {
        // ProductCategoryPO oldProductCategoryPO = productCategoryMapper.selectByPrimaryKey(productCategoryPO.getId());
        // if (null == oldProductCategoryPO) {
        // productCategoryMapper.insertSelective(productCategoryPO);
        // LOG.info("【知花知果】新增产品类目：{}", GSON.toJson(productCategoryPO));
        // } else {
        // if (productCategoryPO.getStatisticsClass() != null ||
        // !StringUtils.isEmpty(productCategoryPO.getStatisticsClassName())) {
        // productCategoryMapper.updateByPrimaryKeySelective(productCategoryPO);
        // LOG.info("【知花知果】更新产品类目：{}", GSON.toJson(productCategoryPO));
        // }
        // }
        // }
    }

    /**
     * 校验产品信息参数
     */
    private void validateProductInfo(ProductInfoPO productInfoPO) {
        AssertUtils.notNull(productInfoPO.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(productInfoPO.getStatisticsCategoryName(), "统计类目不能为空");
        AssertUtils.notNull(productInfoPO.getProductStatisticsClass(), "统计类目Id不能为空");
        AssertUtils.notNull(productInfoPO.getStatus(), "产品信息状态不能为空");
        AssertUtils.notNull(productInfoPO.getProcess(), "是否可以加工不能为空");
    }

    /**
     * 同步产品信息规格
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductInfoSpec(SyncProductInfoSpecDTO syncProductInfoSpecDTO) {
        AssertUtils.notNull(syncProductInfoSpecDTO, "产品信息规格参数不能为空");
        AssertUtils.notNull(syncProductInfoSpecDTO.getId(), "产品规格Id不能为空");
        ProductInfoSpecificationPO specPO =
            ProductSyncByZhzgConvertor.convertToProductInfoSpecPO(syncProductInfoSpecDTO);
        ProductInfoSpecificationPO oldSpecPO = productInfoSpecificationPOMapper.selectByPrimaryKey(specPO.getId());
        if (null == oldSpecPO) {
            // 新增时默认状态为启用
            if (specPO.getState() == null) {
                specPO.setState(StateEnum.启用.getType());
            }
            validateProductInfoSpec(specPO);
            productInfoSpecificationPOMapper.insertSelective(specPO);
            LOG.info("【知花知果】新增产品信息规格：{}", GSON.toJson(specPO));
        } else {
            productInfoSpecificationPOMapper.updateByPrimaryKeySelective(specPO);
            LOG.info("【知花知果】更新产品信息规格：{}", GSON.toJson(specPO));

            // 产品规格单位修改时，同时更新产品sku的包装规格大小单位
            if (!StringUtils.isEmpty(syncProductInfoSpecDTO.getSpecPackageName())) {
                ProductSkuPO productSkuPO = ProductSyncByZhzgConvertor.convertToProductSkuPO(syncProductInfoSpecDTO);
                productSkuMapper.updateSelective(productSkuPO);
                LOG.info("【知花知果】更新产品信息规格的规格单位，同时更新产品sku：{}", GSON.toJson(productSkuPO));
            }
        }
        // 对应修改其可转换的最小规格单位
        processRelateProductInfoSpecList(syncProductInfoSpecDTO);
    }

    /**
     * 处理可转换的产品规格
     */
    private void processRelateProductInfoSpecList(SyncProductInfoSpecDTO syncProductInfoSpecDTO) {
        List<SyncProductInfoSpecDTO> syncProductInfoSpecDTOList =
            syncProductInfoSpecDTO.getSyncProductInfoSpecDTOList();
        if (!CollectionUtils.isEmpty(syncProductInfoSpecDTOList)) {
            syncProductInfoSpecDTOList.forEach(p -> {
                ProductInfoSpecificationPO specificationPO = ProductSyncByZhzgConvertor.convertToProductInfoSpecPO(p);
                productInfoSpecificationPOMapper.updateByPrimaryKeySelective(specificationPO);
                LOG.info("【知花知果】更新可转换的产品规格：{}", GSON.toJson(specificationPO));

                // 产品规格单位修改时，同时更新产品sku的包装规格大小单位
                if (!StringUtils.isEmpty(p.getConvertSpecUnitName())) {
                    ProductSkuPO productSkuPO = ProductSyncByZhzgConvertor.convertToProductSkuPO(p);
                    productSkuMapper.updateSelective(productSkuPO);
                    LOG.info("【知花知果】更新可转换的产品规格的规格单位，同时更新产品sku：{}", GSON.toJson(productSkuPO));
                }
            });
        }
    }

    /**
     * 验证产品信息规格参数
     */
    private void validateProductInfoSpec(ProductInfoSpecificationPO specPO) {
        AssertUtils.notNull(specPO.getProductInfo_Id(), "产品信息id不能为空");
        AssertUtils.notNull(specPO.getName(), "包装名称不能为空");
        AssertUtils.notNull(specPO.getState(), "产品规格状态不能为空");
        AssertUtils.notNull(specPO.getPackageName(), "包装规格单位不能为空");
    }

    /**
     * 同步产品SKU
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductSku(SyncProductSkuDTO syncProductSkuDTO) {
        AssertUtils.notNull(syncProductSkuDTO, "产品SKU参数不能为空");
        AssertUtils.notNull(syncProductSkuDTO.getRegionId(), "大区Id不能为空");
        AssertUtils.notNull(syncProductSkuDTO.getProductInfoSpecId(), "产品规格Id不能为空");

        ProductSkuPO productSkuPO = ProductSyncByZhzgConvertor.convertToProductSkuPO(syncProductSkuDTO);
        // 知花知果大区id转换成供应链区域id
        productSkuPO
            .setCityId(iOrgService.getOrgIdByFromInfo(syncProductSkuDTO.getRegionId(), OrgConstant.ORG_TYPE_EASYGO));

        // 根据大区ID+规格Id+OwnerId获取SKUID，知花知果一个大区会有多个sku，同步给WMS只能有一个
        ProductSkuPO oldProductSku = productSkuMapper.getUniqueProductSku(productSkuPO);
        if (null == oldProductSku) {
            // 校验参数
            validateProductSku(productSkuPO);
            productSkuPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU));
            productSkuPO.setProductSkuId(productSkuPO.getId());

            // 同步新产品时，默认"拣货"、"不播种"、"小件"
            productSkuPO.setPick(ConditionStateEnum.是.getType());
            productSkuPO.setSow(ConditionStateEnum.否.getType());
            productSkuPO.setProductFeature(ProductFeatureConstant.小件);

            // 设置产品与类目关系id
            productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, ProductCategoryGroupIdConstant.ZHZG);
            productSkuMapper.insertProductSkuByZhzg(productSkuPO);
            LOG.info("【知花知果】新增产品SKU：{}", GSON.toJson(productSkuPO));
        } else {
            productSkuPO.setId(oldProductSku.getId());
            productSkuPO.setProductSkuId(oldProductSku.getProductSkuId());
            // 设置产品与类目关系id
            productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, ProductCategoryGroupIdConstant.ZHZG);
            productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
            LOG.info("【知花知果】更新产品SKU：{}", GSON.toJson(productSkuPO));
        }

        // 保存产品sku配置
        if (!CollectionUtils.isEmpty(syncProductSkuDTO.getRealWarehouseIdList())) {
            ProductSkuConfigDTO productSkuConfigDTO =
                ProductSkuConfigConvertor.convertorToProductSkuConfigDTO(productSkuPO);
            if (productSkuConfigDTO != null) {
                productSkuConfigBL.saveProductSkuConfigByWarehouseIds(syncProductSkuDTO.getRealWarehouseIdList(),
                    productSkuConfigDTO, true);
            }
        }
    }

    /**
     * 验证产品Sku参数
     */
    private void validateProductSku(ProductSkuPO productSkuPO) {
        AssertUtils.notNull(productSkuPO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productSkuPO.getProductInfoId(), "产品信息id不能为空");
        AssertUtils.notNull(productSkuPO.getName(), "产品名称不能为空");
        AssertUtils.notNull(productSkuPO.getSpecificationName(), "包装规格名称不能为空");
        AssertUtils.notNull(productSkuPO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(productSkuPO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(productSkuPO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(productSkuPO.getProductState(), "产品状态不能为空");
        AssertUtils.notNull(productSkuPO.getSource(), "产品来源不能为空");
    }
}
