package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 掌上快销
 *
 * <AUTHOR>
 * @date 2019-12-10 15:03
 */
@Service
public class ProductSyncByZskxBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncByZhzgBL.class);
    //
    // @Autowired
    // private ProductInfoPOMapper productInfoPOMapper;
    //
    // @Autowired
    // private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;
    //
    // @Autowired
    // private ProductSkuMapper productSkuMapper;
    //
    // @Autowired
    // private ProductCodeInfoBL productCodeInfoBL;
    //
    // @Autowired
    // private ProductCategoryGroupBL productCategoryGroupBL;
    //
    // @Autowired
    // private ProductInfoCategoryBL productInfoCategoryBL;
    //
    // @Reference
    // private IFileService fileService;
    //
    // @Reference
    // private IContentConfigurationService contentConfigurationService;
    //
    // @Reference
    // private IOmsInventoryService iOmsInventoryService;
    //
    //// @Reference
    //// private IProductSkuIndexQueryService iProductSkuIndexQueryService;
    //
    // /**
    // * 产品查询
    // */
    // public PageList<ProductSkuByZskxDTO> productQuery(ProductSkuByZxkxQuery productSkuByZxkxQuery) {
    // if (StringUtils.isNotEmpty(productSkuByZxkxQuery.getAppCode())) {
    // productSkuByZxkxQuery.setAppCode(productSkuByZxkxQuery.getAppCode().toUpperCase());
    // }
    //// LOG.info("【掌上快销】产品查询参数：{}", JSON.toJSONString(productSkuByZxkxQuery));
    //
    // // 1、查询产品
    // ProductSkuIndexQueryDTO indexQueryDTO = new ProductSkuIndexQueryDTO();
    // BeanUtils.copyProperties(productSkuByZxkxQuery, indexQueryDTO);
    // indexQueryDTO.setSkuId(productSkuByZxkxQuery.getProductSkuId());
    // PageList<ProductSkuIndexDTO> pageList = iProductSkuIndexQueryService.pageListProductSkuIndex(indexQueryDTO);
    // if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
    // return null;
    // }
    // List<ProductSkuByZskxDTO> zskxDtoList = pageList.getDataList().stream().map(p-> {
    // ProductSkuByZskxDTO zskxDTO = new ProductSkuByZskxDTO();
    // BeanUtils.copyProperties(p, zskxDTO);
    // zskxDTO.setProductSkuId(p.getSkuId());
    // zskxDTO.setDefaultImageFile(p.getDefaultImage());
    // return zskxDTO;
    // }).collect(Collectors.toList());
    //
    // // 2、查询销售库存
    // if (CollectionUtils.isNotEmpty(zskxDtoList)) {
    // Map<Integer, List<ProductSkuByZskxDTO>> productSkuMap =
    // zskxDtoList.stream().collect(Collectors.groupingBy(ProductSkuByZskxDTO::getWarehouseId));
    // productSkuMap.forEach((warehouseId, productSkuList) -> {
    // //查询销售库存
    // List<String> internalKeyList = productSkuList.stream().map(p -> p.getInternalKey()).collect(Collectors.toList());
    // OmsInventoryQueryDTO omsInventoryQueryDTO = new OmsInventoryQueryDTO();
    // omsInventoryQueryDTO.setWarehouseId(warehouseId);
    // omsInventoryQueryDTO.setInternalKeys(internalKeyList);
    // List<OmsInventoryInfo> saleInventory = findSaleInventory(omsInventoryQueryDTO);
    // if (CollectionUtils.isEmpty(saleInventory)) {
    // return;
    // }
    // Map<String, List<OmsInventoryInfo>> saleInventoryMap =
    // saleInventory.stream().collect(Collectors.groupingBy(OmsInventoryInfo::getInternalKey));
    // productSkuList.forEach(sku -> {
    // if (CollectionUtils.isNotEmpty(saleInventoryMap.get(sku.getInternalKey()))) {
    // sku.setSaleStoreTotalCount(saleInventoryMap.get(sku.getInternalKey()).get(0).getSaleInventoryCount());
    // }
    // });
    // });
    // }
    //
    // PageList<ProductSkuByZskxDTO> result = new PageList<>();
    // result.setDataList(zskxDtoList);
    // result.setPager(pageList.getPager());
    // return result;
    // }
    //
    // /**
    // * 产品详情
    // */
    // public ProductSkuByZskxDTO productDetail(ProductSkuByZxkxQuery productSkuByZxkxQuery) {
    // AssertUtils.notNull(productSkuByZxkxQuery, "产品详情参数不能为空");
    // AssertUtils.notNull(productSkuByZxkxQuery.getProductSkuId(), "产品skuId不能为空");
    //
    // PageList<ProductSkuByZskxDTO> pageList = productQuery(productSkuByZxkxQuery);
    // if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
    // return null;
    // }
    // ProductSkuByZskxDTO zskxDTO = pageList.getDataList().get(0);
    // // 销售库存明细
    // List<SaleStoreDetail> saleStoreDetails = pageList.getDataList().stream().map(p -> {
    // SaleStoreDetail detail = new SaleStoreDetail();
    // detail.setWarehouseId(p.getWarehouseId());
    // detail.setTotalCount(p.getSaleStoreTotalCount());
    // return detail;
    // }).collect(Collectors.toList());
    // zskxDTO.setSaleStoreDetails(saleStoreDetails);
    // zskxDTO.setSaleStoreTotalCount(null);
    // return zskxDTO;
    // }
    //
    // /**
    // * 获取产品销售库存
    // */
    // private List<OmsInventoryInfo> findSaleInventory(OmsInventoryQueryDTO omsInventoryQueryDTO) {
    // List<OmsInventoryInfo> omsInventoryInfos = new ArrayList<>();
    // try {
    // LOG.info("查询产品销售库存,参数:{}", JSON.toJSONString(omsInventoryQueryDTO));
    // omsInventoryInfos = iOmsInventoryService.querySaleInventoryInternal(omsInventoryQueryDTO);
    // LOG.info("获取产品销售库存结果:{}", JSON.toJSONString(omsInventoryInfos));
    // } catch (Exception e) {
    // LOG.error("查询产品销售库存失败", e);
    // }
    // return omsInventoryInfos;
    // }
    //
    // /**
    // * 判断是否是掌上快销城市
    // * @return
    // */
    // public Boolean isZskcCity(Integer cityId) {
    // if (cityId == null) {
    // return false;
    // }
    // // 获取掌上快销的城市ID
    // String zskxCityCode = contentConfigurationService.getContentValue("ZskxCityCode", null, "");
    // if (StringUtils.isNotEmpty(zskxCityCode)
    // && ("、" + zskxCityCode + "、").contains("、" + cityId + "、")) {
    // return true;
    // }
    // return false;
    // }
    //
    // /**
    // * 在掌上快销城市新建产品sku
    // * @return
    // */
    // public Map<Long, Long> createProductSkuByCityId(Integer cityId, List<Long> productSkuIds) {
    // if (cityId == null || CollectionUtils.isEmpty(productSkuIds)) {
    // return Collections.EMPTY_MAP;
    // }
    // Map<Long, Long> resultMap = new HashMap<>(16);
    // productSkuIds.forEach(skuId -> {
    // // 复制一个sku
    // ProductSkuPO productSkuPO = productSkuMapper.selectByCityIdAndProductSkuId(null, skuId);
    // ProductSkuPO productSkuPONew = new ProductSkuPO();
    // BeanUtils.copyProperties(productSkuPO, productSkuPONew);
    // Long id = UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU);
    // productSkuPONew.setId(id);
    // productSkuPONew.setProductSkuId(id);
    // productSkuPONew.setCityId(cityId);
    // productSkuMapper.insertSelective(productSkuPONew);
    // resultMap.put(skuId, id);
    // });
    // return resultMap;
    // }
    //
    // /**
    // * 掌上快销产品同步
    // */
    // @Transactional(rollbackFor = RuntimeException.class)
    // public void productSync(ProductSkuByZskxSyncDTO syncDTO) {
    // LOG.info("【掌上快销】产品同步参数：{}", JSON.toJSONString(syncDTO));
    // // 校验参数
    // validateProduct(syncDTO);
    // // 判断产品sku是否存在
    // ProductSkuPO productSkuPO = productSkuMapper.selectByCityIdAndProductSkuId(syncDTO.getOrgId(),
    // syncDTO.getProductSkuId());
    // if (productSkuPO != null) {
    // syncDTO.setId(productSkuPO.getId());
    // syncDTO.setProductInfoId(productSkuPO.getProductInfoId());
    // syncDTO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
    // } else {
    // syncDTO.setId(null);
    // syncDTO.setProductInfoId(null);
    // syncDTO.setProductSpecificationId(null);
    // }
    // // 1、同步产品信息
    // saveProductInfo(syncDTO);
    // // 2、同步产品信息规格
    // saveProductInfoSpec(syncDTO);
    // // 3、同步产品SKU
    // saveProductSku(syncDTO, productSkuPO);
    // }
    //
    // /**
    // * 校验参数
    // */
    // private void validateProduct(ProductSkuByZskxSyncDTO syncDTO) {
    // AssertUtils.notNull(syncDTO, "参数不能为空");
    // AssertUtils.notNull(syncDTO.getProductSkuId(), "SkuId不能为空");
    // AssertUtils.notNull(syncDTO.getOrgId(), "组织机构ID不能为空");
    // AssertUtils.notNull(syncDTO.getProductName(), "产品名称不能为空");
    // AssertUtils.notNull(syncDTO.getProductState(), "产品状态不能为空");
    // AssertUtils.notNull(syncDTO.getSpecificationName(), "包装规格名称不能为空");
    // AssertUtils.notNull(syncDTO.getPackageName(), "包装规格大单位不能为空");
    // AssertUtils.notNull(syncDTO.getUnitName(), "包装规格小单位不能为空");
    // AssertUtils.notNull(syncDTO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
    // }
    //
    // /**
    // * 同步产品信息
    // */
    // private void saveProductInfo(ProductSkuByZskxSyncDTO syncDTO) {
    // // 保存产品图片
    // String imageFileId = saveImage(syncDTO.getImageFiles());
    //
    // // 保存产品信息
    // ProductInfoPO productInfoPO = ProductSyncByZskxConvertor.convertorToProductInfoPO(syncDTO);
    // if (productInfoPO == null) {
    // throw new BusinessValidateException("【掌上快销】同步产品信息为空");
    // }
    // productInfoPO.setDefaultImageFile_Id(imageFileId);
    // ProductInfoPO oldProductInfoPO = productInfoPOMapper.selectByPrimaryKey(productInfoPO.getId());
    // if (oldProductInfoPO == null) {
    // productInfoPOMapper.insertSelective(productInfoPO);
    // LOG.info("【掌上快销】新增产品信息：{}", JSON.toJSONString(productInfoPO));
    // } else {
    // productInfoPOMapper.updateByPrimaryKeySelective(productInfoPO);
    // LOG.info("【掌上快销】修改产品信息：{}", JSON.toJSONString(productInfoPO));
    // }
    //
    // // 回写产品信息ID
    // syncDTO.setProductInfoId(productInfoPO.getId());
    //
    // // 保存条码
    // saveBarCode(syncDTO);
    //
    // // 保存类目
    // saveProductCategory(syncDTO);
    // }
    //
    // /**
    // * 保存产品图片
    // * @return
    // */
    // private String saveImage(List<String> imageFiles) {
    // if (CollectionUtils.isEmpty(imageFiles)) {
    // return null;
    // }
    // // 图片ID
    // String businessId = String.valueOf(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UPLOAD_IMG));
    //
    // List<FileInfoDTO> uploadPicFile = imageFiles.stream().map(src-> {
    // FileInfoDTO fileInfo = new FileInfoDTO();
    // fileInfo.setBussinessCategory(CategoryConstant.CATEGORY_PRODUCT_INFO);
    // fileInfo.setBussinessId(businessId);
    // fileInfo.setCloudSrc(src);
    // fileInfo.setFileExtName(StringUtils.isNotEmpty(src) ? src.substring(src.lastIndexOf(".")+1) : "jpg");
    // fileInfo.setFileName(String.format("%s.%s", UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.UPLOAD_IMG),
    // fileInfo.getFileExtName()));
    // fileInfo.setFileSize(100000L);
    // fileInfo.setFileType(FileType.图片.value);
    // return fileInfo;
    // }).collect(Collectors.toList());
    // fileService.saveFiles(uploadPicFile);
    // LOG.info("【掌上快销】保存图片：{}", JSON.toJSONString(uploadPicFile));
    // return businessId;
    // }
    //
    // /**
    // * 保存条码
    // */
    // private void saveBarCode(ProductSkuByZskxSyncDTO syncDTO) {
    // if (CollectionUtils.isEmpty(syncDTO.getBarCode())) {
    // return;
    // }
    // List<SyncProductCodeInfoItemDTO> codeItemDTOList = new ArrayList<>();
    // syncDTO.getBarCode().forEach(code -> {
    // SyncProductCodeInfoItemDTO codeInfoItemDTO = new SyncProductCodeInfoItemDTO();
    // codeInfoItemDTO.setCode(code);
    // codeInfoItemDTO.setState(ProductCodeInfoStateEnum.AUDIT_PASS.getType());
    // codeInfoItemDTO.setCodeType(ProductCodeTypeEnum.BAR_CODE.getType());
    // codeInfoItemDTO.setProductInfoId(syncDTO.getProductInfoId());
    // codeItemDTOList.add(codeInfoItemDTO);
    // });
    // SyncProductCodeInfoDTO syncProductCodeInfoDTO = new SyncProductCodeInfoDTO();
    // syncProductCodeInfoDTO.setProductInfoId(syncDTO.getProductInfoId());
    // syncProductCodeInfoDTO.setCodeItemDTOList(codeItemDTOList);
    // // 保存条码
    // List<SyncProductCodeInfoDTO> syncDTOList = new ArrayList<>();
    // syncDTOList.add(syncProductCodeInfoDTO);
    // productCodeInfoBL.syncProductCodeInfo(syncDTOList);
    // LOG.info("【掌上快销】保存产品条码：{}", JSON.toJSONString(syncDTOList));
    // }
    //
    // /**
    // * 保存类目
    // */
    // private void saveProductCategory(ProductSkuByZskxSyncDTO syncDTO) {
    // List<String> categoryNameList = new ArrayList<>();
    // if (StringUtils.isNotEmpty(syncDTO.getCategoryName())) {
    // categoryNameList.add(syncDTO.getCategoryName());
    // }
    // if (StringUtils.isNotEmpty(syncDTO.getSecondCategoryName())) {
    // categoryNameList.add(syncDTO.getSecondCategoryName());
    // }
    // if (CollectionUtils.isEmpty(categoryNameList)) {
    // return;
    // }
    // // 根据类目名称查询类目ID
    // Map<String, Long> categoryMap = productCategoryGroupBL.getCategoryByNames(categoryNameList,
    // ProductCategoryGroupIdConstant.YIJIUPI);
    // LOG.info("获取类目map：{}", JSON.toJSONString(categoryMap));
    //
    // CategorySync categorySync = ProductCategoryConvertor.ProductSkuByZskxSyncDTO2SyncCategoryTree(syncDTO,
    // categoryMap);
    // if (categorySync == null) {
    // return;
    // }
    // List<CategorySync> categorySyncs = new ArrayList<>();
    // categorySyncs.add(categorySync);
    // productCategoryGroupBL.syncCategoryTree(categorySyncs);
    // }
    //
    // /**
    // * 同步产品信息规格
    // */
    // private void saveProductInfoSpec(ProductSkuByZskxSyncDTO syncDTO) {
    // ProductInfoSpecificationPO specificationPO = ProductSyncByZskxConvertor.convertToProductInfoSpecPO(syncDTO);
    // if (specificationPO == null) {
    // throw new BusinessValidateException("【掌上快销】同步产品规格为空");
    // }
    // ProductInfoSpecificationPO oldSpecificationPO =
    // productInfoSpecificationPOMapper.selectByPrimaryKey(specificationPO.getId());
    // if (oldSpecificationPO == null) {
    // productInfoSpecificationPOMapper.insertSelective(specificationPO);
    // LOG.info("【掌上快销】新增产品规格：{}", JSON.toJSONString(specificationPO));
    // } else {
    // productInfoSpecificationPOMapper.updateByPrimaryKeySelective(specificationPO);
    // LOG.info("【掌上快销】修改产品规格：{}", JSON.toJSONString(specificationPO));
    // }
    //
    // // 回写产品信息规格ID
    // syncDTO.setProductSpecificationId(specificationPO.getId());
    // }
    //
    // /**
    // * 同步产品SKU
    // */
    // private void saveProductSku(ProductSkuByZskxSyncDTO syncDTO, ProductSkuPO oldProductSkuPO) {
    // ProductSkuPO productSkuPO = ProductSyncByZskxConvertor.convertToProductSkuPO(syncDTO);
    // if (productSkuPO == null) {
    // throw new BusinessValidateException("【掌上快销】同步产品规格为空");
    // }
    // // 设置产品与类目关系id
    // productInfoCategoryBL.setProductInfoCategoryIdBySku(productSkuPO, ProductCategoryGroupIdConstant.YIJIUPI);
    //
    // if (oldProductSkuPO == null) {
    // productSkuMapper.insertSelective(productSkuPO);
    // LOG.info("【掌上快销】新增产品SKU：{}", JSON.toJSONString(productSkuPO));
    // } else {
    // productSkuMapper.updateByPrimaryKeySelective(productSkuPO);
    // LOG.info("【掌上快销】修改产品SKU：{}", JSON.toJSONString(productSkuPO));
    // }
    // }
    //
    // /**
    // * 同步产品给掌上快销
    // */
    //// public void pushProductSku(ProductSkuByZskxPushDTO pushDTO) {
    //// try{
    //// // 发送post请求同步
    //// DataResult dataResult = HttpUtil.httpPost(ConfigUtil.zskxAPIUrl + "/product/sync", JSON.toJSONString(pushDTO),
    // new TypeToken<DataResult>() {}.getType());
    //// LOG.info("【掌上快销】产品同步成功");
    //// } catch (Exception e) {
    //// LOG.error("【掌上快销】产品同步失败", e);
    //// }
    //// }
}
