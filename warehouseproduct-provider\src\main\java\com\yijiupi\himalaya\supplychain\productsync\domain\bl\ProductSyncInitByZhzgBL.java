package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.zhzg.ProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.zhzg.ProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.zhzg.*;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductSyncByZhzgConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知花知果产品同步初始化
 *
 * <AUTHOR>
 * @date 2019/3/18 17:24
 */
@Service
public class ProductSyncInitByZhzgBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncInitByZhzgBL.class);

    private static final Gson GSON = new Gson();

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Reference
    private IOrgService iOrgService;

    @ReferGateway(path = ServerPath.ZHZG)
    private ProductInfoQueryService productInfoQueryService;

    @ReferGateway(path = ServerPath.ZHZG)
    private ProductSkuQueryService productSkuQueryService;

    /**
     * 每次查询的条数
     */
    private Integer defaultPageSize = 1000;

    /**
     * 校验条数
     */
    private Integer validatePageSize(Integer pageSize) {
        if (null == pageSize || 0 >= pageSize || pageSize > defaultPageSize) {
            pageSize = defaultPageSize;
        }
        LOG.info("pageSize:{}", pageSize);
        return pageSize;
    }

    /**
     * 初始化产品信息、规格、类目
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void initProductInfo(Integer pageSize) {
        pageSize = validatePageSize(pageSize);
        List<ProductInfoPO> productInfoList = new ArrayList<>();
        List<ProductInfoSpecificationPO> productInfoSpecList = new ArrayList<>();
        List<ProductCategoryPO> productCategoryList = new ArrayList<>();

        LOG.info("【知花知果】产品信息初始化-开始");
        Long startTime = System.currentTimeMillis();
        // 1、查询数据，先查第一页，获取总页数
        PageList<ProductInfoDTO> firstPageList = getProductInfoByPageIndex(1, pageSize);
        processProductInfo(firstPageList.getDataList(), productInfoList, productInfoSpecList, productCategoryList);
        // 遍历剩余的页数
        Integer TotalPage = firstPageList.getPager().getTotalPage();
        for (int i = 2; i <= TotalPage; i++) {
            PageList<ProductInfoDTO> pageList = getProductInfoByPageIndex(i, pageSize);
            processProductInfo(pageList.getDataList(), productInfoList, productInfoSpecList, productCategoryList);
        }
        Long firstTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品信息初始化-第一步查询数据，耗时：{}ms, 产品信息：{}条，规格：{}条，类目：{}条", (firstTime - startTime),
            productInfoList.size(), productInfoSpecList.size(), productCategoryList.size());

        // 2、批量插入产品信息、产品规格、产品类目
        List<List<ProductInfoPO>> splitProductInfoList =
            ProductSyncByZhzgConvertor.splitList(productInfoList, pageSize);
        splitProductInfoList.forEach(list -> {
            productInfoPOMapper.insertOrUpdateBatch(list);
        });
        Long secondTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品信息初始化-第二步插入产品信息，耗时：{}ms", (secondTime - firstTime));

        // 3、批量插入产品规格、产品类目
        List<List<ProductInfoSpecificationPO>> splitProductInfoSpecList =
            ProductSyncByZhzgConvertor.splitList(productInfoSpecList, pageSize);
        splitProductInfoSpecList.forEach(list -> {
            productInfoSpecificationPOMapper.insertOrUpdateBatch(list);
        });
        Long thirdTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品信息初始化-第三步插入产品规格，耗时：{}ms", (thirdTime - secondTime));

        // 4、插入产品类目
        List<List<ProductCategoryPO>> splitProductCategoryList =
            ProductSyncByZhzgConvertor.splitList(productCategoryList, pageSize);
        splitProductCategoryList.forEach(list -> {
            productCategoryMapper.insertOrUpdateProductCategory(list);
        });
        Long fourTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品信息初始化-第四步插入产品类目，耗时：{}ms", (fourTime - thirdTime));

        LOG.info("【知花知果】产品信息初始化-结束，总耗时：{}ms", (fourTime - startTime));
    }

    /**
     * 分页查询产品信息
     */
    private PageList<ProductInfoDTO> getProductInfoByPageIndex(Integer pageIndex, Integer pageSize) {
        ProductInfoQuery productInfoQuery = new ProductInfoQuery();
        productInfoQuery.setPageIndex(pageIndex);
        productInfoQuery.setPageSize(pageSize);
        PageList<ProductInfoDTO> pageList = productInfoQueryService.listProductInfo(productInfoQuery);
        if (pageIndex == 1) {
            LOG.info("知花知果接口调用成功：{}", pageList.getPager().getRecordCount());
        }
        return pageList;
    }

    /**
     * 处理产品信息
     */
    private void processProductInfo(List<ProductInfoDTO> infoList, List<ProductInfoPO> productInfoList,
        List<ProductInfoSpecificationPO> productInfoSpecList, List<ProductCategoryPO> productCategoryList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return;
        }
        infoList.forEach(info -> {
            // 产品信息
            addProductInfo(info, productInfoList);

            // 产品信息规格
            if (!CollectionUtils.isEmpty(info.getProductInfoSaleSpecList())) {
                info.getProductInfoSaleSpecList().forEach(spec -> {
                    addProductInfoSpec(spec, productInfoSpecList);
                });
            }

            // 产品类目
            addProductCategory(info, productCategoryList);
        });
    }

    /**
     * 新增产品信息
     */
    private void addProductInfo(ProductInfoDTO info, List<ProductInfoPO> productInfoList) {
        // 产品信息
        ProductInfoPO productInfoPO = ProductSyncByZhzgConvertor.convertorToProductInfoPO(info);
        if (null == productInfoPO || null == productInfoPO.getId() || null == productInfoPO.getProductName()
            || null == productInfoPO.getStatisticsCategoryName() || null == productInfoPO.getProductStatisticsClass()
            || null == productInfoPO.getStatus() || null == productInfoPO.getProcess()) {
            LOG.info("产品信息新增失败：{}", GSON.toJson(info));
            return;
        }
        productInfoList.add(productInfoPO);
    }

    /**
     * 新增产品信息规格
     */
    private void addProductInfoSpec(ProductInfoSaleSpecDTO spec, List<ProductInfoSpecificationPO> productInfoSpecList) {
        // 产品信息规格
        ProductInfoSpecificationPO specPO = ProductSyncByZhzgConvertor.convertToProductInfoSpecPO(spec);
        if (null == specPO || null == specPO.getId() || null == specPO.getProductInfo_Id() || null == specPO.getName()
            || null == specPO.getState() || null == specPO.getConvertSpecQuantity()
            || null == specPO.getConvertSpecUnitName() || null == specPO.getPackageName()) {
            LOG.info("规格新增失败：{}", GSON.toJson(spec));
            return;
        }
        productInfoSpecList.add(specPO);
    }

    /**
     * 新增产品类目
     */
    private void addProductCategory(ProductInfoDTO info, List<ProductCategoryPO> productCategoryList) {
        // 产品类目
        ProductCategoryPO productCategoryPO = ProductCategoryConvertor.convertorTOProductCategoryPO(info);
        if (null == productCategoryPO || null == productCategoryPO.getId()
            || null == productCategoryPO.getStatisticsClass() || null == productCategoryPO.getStatisticsClassName()) {
            LOG.info("类目新增失败：{}", GSON.toJson(info));
            return;
        }
        productCategoryList.add(productCategoryPO);
    }

    /**
     * 初始化产品sku
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void initProductSku(Integer pageSize) {
        pageSize = validatePageSize(pageSize);
        List<ProductSkuPO> productSkuList = new ArrayList<>();

        LOG.info("【知花知果】产品sku初始化-开始");
        Long startTime = System.currentTimeMillis();
        // 1、查询数据，先查第一页，获取总页数
        PageList<ProductSkuDTO> firstPageList = getProductSkuByPageIndex(1, pageSize);
        processProductSku(firstPageList.getDataList(), productSkuList);
        // 遍历剩余的页数
        Integer totalPage = firstPageList.getPager().getTotalPage();
        for (int i = 2; i <= totalPage; i++) {
            PageList<ProductSkuDTO> pageList = getProductSkuByPageIndex(i, pageSize);
            processProductSku(pageList.getDataList(), productSkuList);
        }
        Long firstTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品sku初始化-第一步查询数据，耗时：{}ms, 产品sku：{}条", (firstTime - startTime),
            firstPageList.getPager().getRecordCount());

        // 2、合并产品sku，保证同一城市下同一规格只能有一个sku
        productSkuList = mergeProductSku(productSkuList);
        Long secondTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品sku初始化-第二步合并产品sku，耗时：{}ms，合并后的产品sku：{}条", (secondTime - firstTime), productSkuList.size());

        // 3、插入产品sku
        List<ProductSkuPO> insertAllList = new ArrayList<>();
        List<ProductSkuPO> updateAllList = new ArrayList<>();
        List<List<ProductSkuPO>> splitProductSkuList = ProductSyncByZhzgConvertor.splitList(productSkuList, pageSize);
        splitProductSkuList.forEach(list -> {
            // 判断sku是否存在，如果存在，则回显skuId
            Map<String, Long> skuIdMap = getProductSkuIdMap(list);
            list.forEach(p -> {
                Long skuId = skuIdMap.get(String.format("%s-%s", p.getCityId(), p.getProductSpecificationId()));
                if (null != skuId) {
                    p.setId(skuId);
                    p.setProductSkuId(skuId);
                }
            });
            // id为null的，批量插入
            List<ProductSkuPO> insertList = list.stream().filter(p -> null == p.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(insertList)) {
                insertList.forEach(p -> p.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SKU)));
                productSkuMapper.insertProductSkuBatch(insertList);
                insertAllList.addAll(insertList);
            }
            // id不为null的，批量更新
            List<ProductSkuPO> udpateList = list.stream().filter(p -> null != p.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(udpateList)) {
                productSkuMapper.insertOrUpdateProductSku(udpateList);
                updateAllList.addAll(udpateList);
            }
        });
        Long thirdTime = System.currentTimeMillis();
        LOG.info("【知花知果】产品sku初始化-第三步插入产品sku，耗时：{}ms，其中新增：{}条，更新：{}条", (thirdTime - secondTime), insertAllList.size(),
            updateAllList.size());

        LOG.info("【知花知果】产品sku初始化-结束，总耗时：{}ms", (thirdTime - startTime));
    }

    /**
     * 合并产品sku 根据大区ID+规格Id+OwnerId确定SKUID，知花知果一个大区会有多个sku，同步给WMS只能有一个
     */
    private List<ProductSkuPO> mergeProductSku(List<ProductSkuPO> productSkuList) {
        List<ProductSkuPO> mergeProductSkuList = new ArrayList<>();
        Map<String, List<ProductSkuPO>> skuMap = productSkuList.stream()
            .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getCityId(), p.getProductSpecificationId())));
        skuMap.forEach((k, list) -> {
            if (!CollectionUtils.isEmpty(list)) {
                // 新增
                addProductSku(list.get(0), mergeProductSkuList);
            }
        });
        return mergeProductSkuList;
    }

    /**
     * 查询产品skuId key -> ctiyId+规格id value -> skuId
     * 
     * @return
     */
    private Map<String, Long> getProductSkuIdMap(List<ProductSkuPO> list) {
        Map<String, Long> skuIdMap = new HashMap<>(16);
        // 按cityId分组
        Map<Integer, List<ProductSkuPO>> skuMap = list.stream().collect(Collectors.groupingBy(p -> p.getCityId()));
        if (null != skuMap) {
            skuMap.forEach((cityId, skuList) -> {
                // 批量查询产品sku
                List<Long> specIds =
                    skuList.stream().map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
                List<ProductSkuPO> poList =
                    productSkuMapper.listProductSkuBySpecIds(cityId, specIds, null, ProductSourceType.知花知果);
                if (!CollectionUtils.isEmpty(poList)) {
                    poList.forEach(p -> {
                        skuIdMap.put(String.format("%s-%s", cityId, p.getProductSpecificationId()), p.getId());
                    });
                }
            });
        }
        return skuIdMap;
    }

    /**
     * 分页查询产品sku
     */
    private PageList<ProductSkuDTO> getProductSkuByPageIndex(Integer pageIndex, Integer pageSize) {
        ProductSkuQuery productSkuQuery = new ProductSkuQuery();
        productSkuQuery.setPageIndex(pageIndex);
        productSkuQuery.setPageSize(pageSize);
        productSkuQuery.setSequence(false);
        PageList<ProductSkuDTO> pageList = productSkuQueryService.listProductSku(productSkuQuery);
        if (pageIndex == 1) {
            LOG.info("知花知果接口调用成功：{}", pageList.getPager().getRecordCount());
        }
        return pageList;
    }

    /**
     * 处理产品sku
     */
    private void processProductSku(List<ProductSkuDTO> skuList, List<ProductSkuPO> productSkuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        // 获取产品规格Map
        List<Long> specIds = skuList.stream().filter(p -> p.getProductInfoSpecId() != null)
            .map(p -> p.getProductInfoSpecId()).distinct().collect(Collectors.toList());
        Map<Long, ProductInfoSpecificationPO> specMap = getProductInfoSpecMap(specIds);

        // 获取城市idMap
        List<Long> regionIds = skuList.stream().filter(p -> p.getRegionId() != null).map(p -> p.getRegionId())
            .distinct().collect(Collectors.toList());
        Map<Long, Integer> cityMap = getCityIdMap(regionIds);

        // 填充sku
        skuList.forEach(sku -> {
            ProductSkuPO productSkuPO = ProductSyncByZhzgConvertor.convertToProductSkuPO(sku);
            ProductInfoSpecificationPO specPO = specMap.get(productSkuPO.getProductSpecificationId());
            if (null != specPO) {
                // 包装规格名称
                productSkuPO.setSpecificationName(specPO.getName());
                // 包装规格大单位
                productSkuPO.setPackageName(specPO.getPackageName());
                // 包装规格小单位
                productSkuPO.setUnitName(specPO.getConvertSpecUnitName());
                // 包装规格大小单位转换系数
                productSkuPO.setPackageQuantity(specPO.getConvertSpecQuantity());
            }
            // 城市id
            productSkuPO.setCityId(cityMap.get(sku.getRegionId()));
            productSkuList.add(productSkuPO);
        });
    }

    /**
     * 新增产品sku
     */
    private void addProductSku(ProductSkuPO productSkuPO, List<ProductSkuPO> productSkuList) {
        if (null == productSkuPO || null == productSkuPO.getProductInfoId()
            || null == productSkuPO.getProductSpecificationId() || null == productSkuPO.getName()
            || null == productSkuPO.getProductState() || null == productSkuPO.getCityId()
            || null == productSkuPO.getSpecificationName() || null == productSkuPO.getPackageName()
            || null == productSkuPO.getUnitName() || null == productSkuPO.getPackageQuantity()) {
            LOG.info("SKU新增失败：{}", GSON.toJson(productSkuPO));
            return;
        }
        productSkuList.add(productSkuPO);
    }

    /**
     * 获取产品规格Map
     * 
     * @return
     */
    private Map<Long, ProductInfoSpecificationPO> getProductInfoSpecMap(List<Long> specIds) {
        Map<Long, ProductInfoSpecificationPO> specMap = new HashMap<>(16);
        List<ProductInfoSpecificationPO> specList = productInfoSpecificationPOMapper.listByIds(specIds);
        if (!CollectionUtils.isEmpty(specList)) {
            specList.forEach(p -> {
                specMap.put(p.getId(), p);
            });
        }
        return specMap;
    }

    /**
     * 知花知果大区id转换成供应链区域id
     * 
     * @return
     */
    private Map<Long, Integer> getCityIdMap(List<Long> regionId) {
        Map<Long, Integer> cityMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(regionId)) {
            regionId.forEach(p -> {
                Integer cityId = iOrgService.getOrgIdByFromInfo(p, OrgConstant.ORG_TYPE_EASYGO);
                cityMap.put(p, cityId);
            });
        }
        return cityMap;
    }

}
