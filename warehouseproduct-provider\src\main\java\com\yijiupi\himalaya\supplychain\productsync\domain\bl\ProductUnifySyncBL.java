package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.dubbop.adapter.easychain.ProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.easychain.RegionProductInfoThirdService;
import com.yijiupi.himalaya.supplychain.dubbop.dto.unify.RetailUnifyProductSkuQueryDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.unify.TrdUnifyProductSkuDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.unify.TrdUnifyProductSkuQueryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.PartyNameConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListSO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ScmProductSkuSyncMessage;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncRetailProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSkuSplitPackageDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductSkuImpl;

/**
 * 中台sku同步
 *
 * <AUTHOR>
 * @Date 2021/8/18 16:16
 */
@Service
public class ProductUnifySyncBL {
    private static final Logger LOG = LoggerFactory.getLogger(ProductUnifySyncBL.class);

    // 新零售
    @ReferGateway(path = "retail-ssc-api", timeout = 10000)
    private RegionProductInfoThirdService retailUnifySkuService;

    // 交易
    @ReferGateway(path = "trd-scm-api", timeout = 10000)
    private ProductSkuQueryService trdUnifySkuService;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Autowired
    private ProductSkuBL productSkuBL;

    @Autowired
    private ProductInfoSpecificationBL productInfoSpecificationBL;

    @Autowired
    private ProductSkuImpl productSkuImpl;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private String lockKeySuffix = "supc:productSync:unifySkuSync:lock:";

    /**
     * 锁的有效时间
     */
    private int expireSec = 1000 * 60 * 60 * 4;

    /**
     * 读取productSku表，进行中台skuId同步
     */
    public void batchUnifySkuSync(Integer cityIdStart, Integer cityIdEnd, String action, Integer batchSize) {
        AssertUtils.notNull(cityIdStart, "起始cityId不能为空");
        AssertUtils.notNull(cityIdEnd, "截止cityId不能为空");

        // 相当于tryLock
        if (!lock(cityIdStart, cityIdEnd, action)) {
            throw new BusinessValidateException(
                String.format("[中台skuId同步]任务已经在运行...起始城市Id：%s，截止城市Id：%s，类型：%s", cityIdStart, cityIdEnd, action));
        }

        try {
            LOG.info("[中台skuId同步]开始...起始城市Id：{}，截止城市Id：{}，类型：{}", cityIdStart, cityIdEnd, action);

            long pageCount = 1;
            long rowCount = 0;
            boolean isGetPageCount = false;

            ProductSkuListSO cityRangeSO = new ProductSkuListSO();
            cityRangeSO.setCityIdStart(cityIdStart);
            cityRangeSO.setCityIdEnd(cityIdEnd);

            LocalDateTime startTime = LocalDateTime.now();
            Long startMSec = System.currentTimeMillis();
            Long onceMSec = 0L;

            Integer realSyncNum = 0;
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                cityRangeSO.setPageNum(pageNum);
                cityRangeSO.setPageSize(batchSize);

                Long onceStartMSec = System.currentTimeMillis();

                long remainMin = onceMSec * (pageCount - pageNum + 1) / 60000;
                LOG.info("[中台skuId同步]起始城市：{}，截止城市：{}，类型：{}，总记录数：{}，共{}个批次，正在同步第{}个批次，开始时间：{}，已耗时：{}分钟，预计剩余时间：{}分钟",
                    cityIdStart, cityIdEnd, action, rowCount, pageCount, pageNum, startTime,
                    (System.currentTimeMillis() - startMSec) / 60000, remainMin);

                // 1.查询出sku
                PageResult<ProductSkuPO> oldSkuList = productSkuMapper.listProductSkuPO(cityRangeSO);

                if (CollectionUtils.isEmpty(oldSkuList)) {
                    LOG.info("[中台skuId同步]查询sku列表为空");
                    continue;
                }

                if (!isGetPageCount) {
                    rowCount = oldSkuList.getTotal();
                    pageCount = oldSkuList.getPager().getTotalPage();
                    isGetPageCount = true;
                }

                // 2.为sku设置新中台skuId
                List<ProductSkuPO> updateSkuList = null;
                switch (action) {
                    case PartyNameConstant.RETAIL:
                        updateSkuList = syncRetailUnifySku(oldSkuList);
                        break;
                    case PartyNameConstant.TRD:
                        updateSkuList = syncTrdUnifySku(oldSkuList);
                        break;
                    default:
                        break;
                }
                if (CollectionUtils.isEmpty(updateSkuList)) {
                    LOG.info("[中台skuId同步]service响应中台skuId结果为空");
                    continue;
                }

                // 3.更新sku到数据库、redis
                try {
                    productSkuBL.batchUpdateProductSkuUnifyId(updateSkuList);
                    realSyncNum += updateSkuList.size();
                } catch (Exception ex) {
                    LOG.info("[中台skuId同步]产品SKU同步中台skuId异常", ex);
                }
                onceMSec = System.currentTimeMillis() - onceStartMSec;
            }
            LOG.info("[中台skuId同步]结束...起始城市Id：{}，截止城市Id：{}，类型：{},目标同步记录数：{}，实际同步记录数：{}，开始时间：{}，用时：{}分钟", cityIdStart,
                cityIdEnd, action, rowCount, realSyncNum, startTime, (System.currentTimeMillis() - startMSec) / 60000);
        } finally {
            if (unLock(cityIdStart, cityIdEnd, action)) {
                LOG.info("[中台skuId同步]锁已释放...起始城市Id：{}，截止城市Id：{}，类型：{}", cityIdStart, cityIdEnd, action);
            }
        }
    }

    /**
     * 新零售中台sku同步,调用零售service，查询中台skuId，并调用中台skuId同步方法，进行同步
     */
    private List<ProductSkuPO> syncRetailUnifySku(List<ProductSkuPO> oldProductSkuPOS) {
        AssertUtils.notEmpty(oldProductSkuPOS, "[新零售中台skuId同步]中台sku查询参数不能为空");
        List<ProductSkuPO> skuUpdateList = new ArrayList<>();

        // 拼接出查询参数
        List<RetailUnifyProductSkuQueryDTO> unifyQueryList = new ArrayList<>();
        oldProductSkuPOS.forEach(skuPO -> {
            if (skuPO.getProductSpecificationId() == null || skuPO.getPackageQuantity() == null) {
                return;
            }
            RetailUnifyProductSkuQueryDTO sync = new RetailUnifyProductSkuQueryDTO();
            // 新零售的易久批货主对应的区域ID为0
            sync.setCompanyId(skuPO.getCompanyId() == null ? 0L : skuPO.getCompanyId());
            sync.setProductSpecificationId(skuPO.getProductSpecificationId());
            sync.setSpecQuantity(skuPO.getPackageQuantity());
            unifyQueryList.add(sync);
        });

        Map<String, String> productUnifySkuMap = new HashMap<>(16);
        Lists.partition(unifyQueryList, 100).forEach(queryListElem -> {
            try {
                // 调用新零售接口，获取新的中台skuId
                // 格式：规格ID_货主ID_转化系数 -> 新中台skuId
                Map<String, String> productUnifySkuElem =
                    retailUnifySkuService.getSscProductSkuIdMap(new HashSet<>(queryListElem));
                productUnifySkuMap.putAll(productUnifySkuElem);
            } catch (Exception ex) {
                LOG.info("[新零售中台skuId同步]接口调用异常，查询参数：{}", JSON.toJSONString(queryListElem), ex);
            }
        });

        if (productUnifySkuMap == null || productUnifySkuMap.size() <= 0) {
            return skuUpdateList;
        }

        // 根据service返回结果，拼装出sku更新PO
        for (Map.Entry<String, String> productUnifySkuEntry : productUnifySkuMap.entrySet()) {
            String productKey = productUnifySkuEntry.getKey();
            String unifySkuId = productUnifySkuEntry.getValue();
            if (productKey == null || unifySkuId == null || !productKey.matches("([0-9.]+_){2}[0-9]+")
                || !unifySkuId.matches("\\d+")) {
                LOG.info("[新零售中台skuId同步]响应的中台sku信息不正确，key：{}，value：{}", productKey, unifySkuId);
                continue;
            }

            String[] productInfo = productKey.split("_");
            Long productSpecificationId = Long.valueOf(productInfo[0]);
            Long companyId = Long.valueOf(productInfo[1]) == 0L ? null : Long.valueOf(productInfo[1]);
            Double packageQuantity = Double.valueOf(productInfo[2]);
            Long newUnifySkuId = Long.valueOf(unifySkuId);

            // 匹配出查得unifySkuId的productSku
            List<ProductSkuPO> matchOldSkuListElem = oldProductSkuPOS.stream()
                .filter(oldSku -> Objects.equals(oldSku.getProductSpecificationId(), productSpecificationId)
                    && Objects.equals(oldSku.getPackageQuantity().doubleValue(), packageQuantity)
                    && Objects.equals(oldSku.getCompanyId(), companyId))
                .map(oldSku -> {
                    oldSku.setUnifySkuId(newUnifySkuId);
                    return oldSku;
                }).collect(Collectors.toList());

            skuUpdateList.addAll(matchOldSkuListElem);
        }

        return skuUpdateList;
    }

    /**
     * 交易中台sku同步,调用交易service，查询中台skuId，并调用中台skuId同步方法，进行同步
     */
    private List<ProductSkuPO> syncTrdUnifySku(List<ProductSkuPO> oldProductSkuPOS) {
        AssertUtils.notEmpty(oldProductSkuPOS, "[交易中台skuId同步]中台sku查询参数不能为空");

        // 调用交易接口，获取新的中台skuId
        List<ProductSkuPO> skuUpdateList = new ArrayList<>();
        oldProductSkuPOS.forEach(skuPO -> {
            if (skuPO.getProductSpecificationId() == null || skuPO.getProductInfoId() == null
                || skuPO.getProductSpecificationId() > Integer.MAX_VALUE
                || skuPO.getProductInfoId() > Integer.MAX_VALUE) {
                return;
            }
            // 拼接出查询参数
            TrdUnifyProductSkuQueryDTO syncRequest = new TrdUnifyProductSkuQueryDTO();
            // 交易的易久批货主ID为0
            syncRequest.setCompanyId(skuPO.getCompanyId() == null ? 0 : skuPO.getCompanyId());
            syncRequest.setProductSpecificationId(skuPO.getProductSpecificationId());
            syncRequest.setProductInfoId(skuPO.getProductInfoId());

            try {
                // 根据service返回中台skuId
                TrdUnifyProductSkuDTO newUnifySku = trdUnifySkuService.findOneForSCMLegacyInit(syncRequest);
                if (newUnifySku == null || newUnifySku.getId() == null) {
                    LOG.info("[交易中台skuId同步]service返回的中台skuId为空，查询参数：{}", JSON.toJSONString(syncRequest));
                    return;
                }
                skuPO.setUnifySkuId(newUnifySku.getId());
                skuUpdateList.add(skuPO);
            } catch (Exception ex) {
                LOG.info("[交易中台skuId同步]接口调用异常，查询参数：{}", JSON.toJSONString(syncRequest), ex);
            }
        });
        return skuUpdateList;
    }

    @Autowired
    private ProductSyncBySscBL productSyncBySscBL;

    /**
     * 根据fromSkuId（productSku表的unifySkuId）、拆包前后的package信息，更新规格、更新sku
     */
    public void syncSplitPackage(SyncSscProductSkuSplitPackageDTO splitPackageDTO) {
        SyncRetailProductSkuDTO fromSku = splitPackageDTO.getOldProductSkuDTO();
        SyncRetailProductSkuDTO toSku = splitPackageDTO.getNewProductSkuDTO();
        Long infoId =
            fromSku.getYjpProductInfoId() == null ? toSku.getYjpProductInfoId() : fromSku.getYjpProductInfoId();

        AssertUtils.notNull(fromSku, "原始的产品sku不能为空");
        AssertUtils.notNull(fromSku.getYjpProductSkuId(), "原始中台skuId不能为空");
        AssertUtils.notNull(fromSku.getYjpProductSkuQuantity(), "原始的中台子单位转换系数不能为空");
        AssertUtils.notNull(fromSku.getYjpProductSkuPackageName(), "原始的中台package名称不能为空");
        AssertUtils.notNull(toSku, "拆包后的产品sku不能为空");
        AssertUtils.notNull(toSku.getYjpProductSkuId(), "拆包后的中台skuId不能为空");
        AssertUtils.notNull(toSku.getYjpProductSkuPackageName(), "拆包后的中台package名称不能为空");
        AssertUtils.notNull(infoId, "拆包前、拆包后的中台产品infoId不能都为空");
        AssertUtils.notEmpty(splitPackageDTO.getWarehouseIdList(), "仓库Id不能为空");

        AssertUtils.notNull(toSku.getYjpProductInfoSpecificationId(), "拆包后的中台产品规格id不能为空");
        AssertUtils.notNull(toSku.getYjpProductPackageId(), "拆包后的中台产品包装id不能为空");

        // 1.新增产品规格
        ProductInfoSpecificationPO productInfoSpecificationPO = productInfoSpecificationBL.getProductSpecificationPO(
            toSku.getYjpProductSkuId(), infoId, new BigDecimal(fromSku.getYjpProductSkuQuantity()),
            fromSku.getYjpProductSkuPackageName(), toSku.getYjpProductSkuPackageName());
        productInfoSpecificationBL.insertOrUpdate(productInfoSpecificationPO);
        // 2.新增sku
        List<ScmProductSkuSyncMessage> skuSyncMsgList =
            productSkuBL.listSkuSyncMsgByUnifyInfo(fromSku.getYjpProductSkuId(), toSku.getYjpProductSkuId(),
                productInfoSpecificationPO, splitPackageDTO.getWarehouseIdList());
        skuSyncMsgList.forEach(skuSyncElem -> {
            productSkuImpl.sync(JSON.toJSONString(skuSyncElem));
        });

        // 3.同步中台sku
        productSyncBySscBL.syncProductSkuOnSplitPackage(fromSku, toSku, productInfoSpecificationPO, infoId);
    }

    private boolean lock(Integer cityIdStart, Integer cityIdEnd, String action) {
        String lockKey = lockKeySuffix + (action.equals(PartyNameConstant.RETAIL) ? "retail" : "trd");
        HashOperations<String, String, String> hashOpera = redisTemplate.opsForHash();
        Map<String, String> taskLockMap = hashOpera.entries(lockKey);

        Long curTime = System.currentTimeMillis();
        for (String taskLockElem : taskLockMap.keySet()) {
            Long expireTime = Long.valueOf(taskLockMap.get(taskLockElem));

            String[] cityRange = taskLockElem.split("_");
            int lockCityIdStart = Integer.valueOf(cityRange[0]);
            int lockCityIdEnd = Integer.valueOf(cityRange[1]);
            if (lockCityIdStart <= cityIdStart && cityIdEnd <= lockCityIdEnd) {
                if (curTime <= expireTime) {
                    return false;
                } else {
                    hashOpera.delete(lockKey, taskLockElem);
                    break;
                }
            }
        }
        Boolean isLock = hashOpera.putIfAbsent(lockKey, String.format("%d_%d", cityIdStart, cityIdEnd),
            String.valueOf(curTime + expireSec + 1));
        return isLock;
    }

    private boolean unLock(Integer cityIdStart, Integer cityIdEnd, String action) {
        String lockKey = lockKeySuffix + (action.equals(PartyNameConstant.RETAIL) ? "retail" : "trd");

        redisTemplate.opsForHash().delete(lockKey, String.format("%d_%d", cityIdStart, cityIdEnd));
        return true;
    }
}
