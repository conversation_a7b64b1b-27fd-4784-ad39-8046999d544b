package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductdbExtMapper;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
@Service
public class ProductdbExtBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductdbExtBL.class);
    @Autowired
    private ProductdbExtMapper productdbExtMapper;

    public ProductdbExtDTO detail(Long id) {
        return productdbExtMapper.detail(id);
    }

    public PageList<ProductdbExtDTO> pageList(ProductdbExtQueryDTO productdbExtQueryDTO) {
        PageResult<ProductdbExtDTO> pageResult = productdbExtMapper.pageList(productdbExtQueryDTO);
        return pageResult.toPageList();
    }

    public List<ProductdbExtDTO> list(ProductdbExtQueryDTO productdbExtDTO) {
        List<ProductdbExtDTO> list = productdbExtMapper.list(productdbExtDTO);
        return list;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void insert(ProductdbExtDTO productdbExtDTO) {
        LOGGER.info("插入产品拓展信息：{}", JSON.toJSON(productdbExtDTO));
        AssertUtils.notNull(productdbExtDTO.getDataId(), "数据ID不能为空");
        AssertUtils.notNull(productdbExtDTO.getCreateUserId(), "创建人不能为空");
        AssertUtils.notNull(productdbExtDTO.getLastUpdateUserId(), "更新人不能为空");
        AssertUtils.notNull(productdbExtDTO.getSourceTable(), "来源表不能为空");
        productdbExtDTO.setId(UUIDGenerator.getUUID(UUIDGeneratorUtil.PRODUCT_DB_EXT));
        productdbExtMapper.insert(productdbExtDTO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void insertBatch(List<ProductdbExtDTO> productdbExtDTOs) {
        LOGGER.info("批量插入产品拓展信息：{}", JSON.toJSON(productdbExtDTOs));
        if (CollectionUtils.isNotEmpty(productdbExtDTOs)) {
            productdbExtDTOs.forEach(dto -> {
                AssertUtils.notNull(dto.getDataId(), "数据ID不能为空");
                AssertUtils.notNull(dto.getCreateUserId(), "创建人不能为空");
                AssertUtils.notNull(dto.getLastUpdateUserId(), "更新人不能为空");
                AssertUtils.notNull(dto.getSourceTable(), "来源表不能为空");
                dto.setId(UUIDGenerator.getUUID(UUIDGeneratorUtil.PRODUCT_DB_EXT));
            });
        }
        productdbExtMapper.insertBatch(productdbExtDTOs);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void update(ProductdbExtDTO productdbExtDTO) {
        LOGGER.info("修改产品拓展信息：{}", JSON.toJSON(productdbExtDTO));
        AssertUtils.notNull(productdbExtDTO.getDataId(), "数据ID不能为空");
        AssertUtils.notNull(productdbExtDTO.getSourceTable(), "来源表不能为空");
        AssertUtils.notNull(productdbExtDTO.getLastUpdateUserId(), "更新人不能为空");
        productdbExtMapper.update(productdbExtDTO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(ProductdbExtDTO productdbExtDTO) {
        LOGGER.info("删除拓展信息：{}", JSON.toJSON(productdbExtDTO));
        productdbExtMapper.delete(productdbExtDTO);
    }

}
