package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.convertor.SpecificationDictionaryConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.SpecificationDictionaryMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionaryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionarySO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 包装规格字典管理
 */
@Service
public class SpecificationDictionaryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpecificationDictionaryBL.class);

    @Autowired
    private SpecificationDictionaryMapper specificationDictionaryMapper;

    /**
     * 包装规格管理列表
     * 
     * @return
     */
    public PageList<SpecificationDictionaryDTO>
        listSpecificationDictionary(SpecificationDictionarySO specificationDictionarySO) {
        PageResult<SpecificationDictionaryPO> pageResult =
            specificationDictionaryMapper.listSpecificationDictionary(specificationDictionarySO);
        List<SpecificationDictionaryDTO> specificationDictionaryDTOList = pageResult.toPageList().getDataList().stream()
            .map(SpecificationDictionaryConvertor::convertToSpecificationDictionaryDTO).collect(Collectors.toList());

        PageList<SpecificationDictionaryDTO> pageList = new PageList<>();
        pageList.setPager(pageResult.getPager());
        pageList.setDataList(specificationDictionaryDTOList);
        return pageList;
    }

    /**
     * 获取所有包装单位
     * 
     * @return
     */
    public List<String> listUnitName() {
        return specificationDictionaryMapper.listUnitName();
    }

    /**
     * 获取包装规格详情
     * 
     * @return
     */
    public SpecificationDictionaryDTO getSpecificationDictionary(Long id) {
        AssertUtils.notNull(id, "id不能为空");

        SpecificationDictionaryPO specificationDictionaryPO = specificationDictionaryMapper.selectByPrimaryKey(id);
        SpecificationDictionaryDTO specificationDictionaryDTO =
            SpecificationDictionaryConvertor.convertToSpecificationDictionaryDTO(specificationDictionaryPO);
        return specificationDictionaryDTO;
    }

    /**
     * 批量新增包装规格管理
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveSpecificationDictionaryBatch(List<SpecificationDictionaryDTO> specificationDictionaryDTOS) {
        AssertUtils.notEmpty(specificationDictionaryDTOS, "参数不能为空");
        // 判断包装规格名称是否重复
        validateSpecificationDictionaryRepeat(specificationDictionaryDTOS);

        LOGGER.info("【SAAS】批量新增包装规格管理参数：{}", JSON.toJSONString(specificationDictionaryDTOS));
        specificationDictionaryDTOS.forEach(p -> {
            // 新增包装规格管理
            saveSpecificationDictionary(p);
        });
    }

    /**
     * 判断包装规格名称是否重复
     */
    private void validateSpecificationDictionaryRepeat(List<SpecificationDictionaryDTO> specificationDictionaryDTOS) {
        if (CollectionUtils.isEmpty(specificationDictionaryDTOS)) {
            return;
        }
        List<String> specNameList = new ArrayList<>();
        specificationDictionaryDTOS.forEach(p -> {
            // 判断包装规格名称是否重复
            if (p.getPackageQuantity() != null && p.getPackageName() != null && p.getUnitName() != null) {
                String specName = String.format("%s%s/%s", p.getPackageQuantity().stripTrailingZeros().toPlainString(),
                    p.getUnitName(), p.getPackageName());
                if (specNameList.contains(specName)) {
                    throw new BusinessValidateException("规格名称（" + specName + "）不能重复，请重新输入！");
                }
                specNameList.add(specName);
            }
        });
    }

    /**
     * 新增包装规格管理
     * 
     * @return
     */
    public Long saveSpecificationDictionary(SpecificationDictionaryDTO specificationDictionaryDTO) {
        AssertUtils.notNull(specificationDictionaryDTO, "参数不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getPackageQuantity(), "包装数量不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getPackageName(), "外包装单位不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getUnitName(), "内包装单位不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getState(), "状态不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getCreateUserId(), "创建人id不能为空");
        if (specificationDictionaryDTO.getPackageQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessValidateException("规格转换系数不能小于0");
        }

        SpecificationDictionaryPO specificationDictionaryPO =
            SpecificationDictionaryConvertor.convertToSpecificationDictionaryPO(specificationDictionaryDTO);
        if (specificationDictionaryPO == null) {
            LOGGER.warn("【SAAS】新增包装规格管理失败");
            return null;
        }
        if (specificationDictionaryMapper.countSaveSpecificationDictionary(specificationDictionaryPO) > 0) {
            throw new BusinessValidateException(
                "规格名称（" + specificationDictionaryPO.getSpecificationName() + "）已存在，请重新输入！");
        }
        specificationDictionaryMapper.insertSelective(specificationDictionaryPO);
        LOGGER.info("【SAAS】新增包装规格管理：{}", JSON.toJSONString(specificationDictionaryPO));
        return specificationDictionaryPO.getId();
    }

    /**
     * 修改包装规格管理
     * 
     * @return
     */
    public void updateSpecificationDictionary(SpecificationDictionaryDTO specificationDictionaryDTO) {
        AssertUtils.notNull(specificationDictionaryDTO, "参数不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getId(), "id不能为空");
        AssertUtils.notNull(specificationDictionaryDTO.getLastUpdateUserId(), "修改人id不能为空");
        if (specificationDictionaryDTO.getPackageQuantity() != null
            && specificationDictionaryDTO.getPackageQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessValidateException("规格转换系数不能小于0");
        }

        SpecificationDictionaryPO specificationDictionaryPO =
            SpecificationDictionaryConvertor.convertToSpecificationDictionaryPO(specificationDictionaryDTO);
        if (specificationDictionaryPO == null) {
            LOGGER.warn("【SAAS】修改包装规格管理失败");
            return;
        }
        if (specificationDictionaryMapper.countSpecificationDictionary(specificationDictionaryPO) > 0) {
            throw new BusinessValidateException(
                "规格名称（" + specificationDictionaryPO.getSpecificationName() + "）已存在，请重新输入！");
        }
        specificationDictionaryMapper.updateByPrimaryKeySelective(specificationDictionaryPO);
        LOGGER.info("【SAAS】修改包装规格管理：{}", JSON.toJSONString(specificationDictionaryPO));
    }

}
