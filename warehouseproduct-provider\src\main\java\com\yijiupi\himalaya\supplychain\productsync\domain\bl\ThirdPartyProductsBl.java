package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdPartyProductsMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;

/**
 * <AUTHOR>
 * @date 2019-12-17
 */
@Service
public class ThirdPartyProductsBl {

    public static final String THIRDPARTY_PRODUCT = "thirdpartyproducts";

    @Autowired
    private ThirdPartyProductsMapper thirdPartyProductsMapper;

    public ThirdPartyProductsPO detail(Long id) {
        return thirdPartyProductsMapper.detail(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int insert(ThirdPartyProductsPO thirdPartyProduct) {
        thirdPartyProduct.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT));
        return thirdPartyProductsMapper.insert(thirdPartyProduct);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(ThirdPartyProductsDTO dto) {
        ThirdPartyProductsPO partyProductsPO = new ThirdPartyProductsPO();
        partyProductsPO.setId(dto.getId());
        thirdPartyProductsMapper.delete(partyProductsPO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int insertBatch(List<ThirdPartyProductsPO> thirdPartyProducts) {
        thirdPartyProducts.forEach(po -> {
            po.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT));
        });
        return thirdPartyProductsMapper.insertBatch(thirdPartyProducts);
    }

    public List<ThirdPartyProductsPO> listBySkuIds(List<String> skuIds, String cityId, String warehouseId) {
        return thirdPartyProductsMapper.listBySkuIds(skuIds, cityId, warehouseId);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteByIds(List<Long> ids) {
        thirdPartyProductsMapper.deleteByIds(ids);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteByTime(ThirdPartyProductsDTO dto) {
        thirdPartyProductsMapper.deleteByTime(dto);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void update(ThirdPartyProductsPO po) {
        thirdPartyProductsMapper.update(po);
    }
}
