package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ThirdPartyProductConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdPartyProductsMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdPartyProductsRelevanceMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsRelevancePO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.*;
import com.yijiupi.himalaya.supplychain.productsync.util.CommonUtils;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;

/**
 * <AUTHOR>
 * @date 2019-12-17
 */
@Service
public class ThirdPartyProductsRelevanceBl {

    public static final String THIRDPARTY_PRODUCT_RELEVANCE = "thirdpartyproductsrelevance";

    @Autowired
    private ThirdPartyProductsRelevanceMapper thirdPartyProductsRelevanceMapper;

    @Autowired
    private ThirdPartyProductsMapper thirdPartyProductsMapper;

    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    public ThirdPartyProductsRelevancePO detail(Long id) {
        return thirdPartyProductsRelevanceMapper.detail(id);
    }

    public PageList<ThirdProductsRelevanceWithSkuDTO> pageList(ThirdProductsRelevanceQueryDTO queryDTO) {
        PageList<ThirdProductsRelevanceWithSkuDTO> pageList =
            thirdPartyProductsRelevanceMapper.pageList(queryDTO).toPageList();
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            return pageList;
        }
        List<Long> skuIds = pageList.getDataList().stream().filter(it -> it.getProductsSkuId() != null)
            .map(it -> it.getProductsSkuId()).collect(Collectors.toList());
        List<ProductSkuPO> skuPOS = productSkuMapper.selectSkuInfoBySkuIds(null, skuIds);
        if (CollectionUtils.isNotEmpty(skuPOS)) {
            Map<Long, ProductSkuPO> skuMap =
                skuPOS.stream().collect(Collectors.toMap(it -> it.getProductSkuId(), it -> it, (v1, v2) -> v1));
            pageList.getDataList().stream().filter(it -> skuMap.get(it.getProductsSkuId()) != null).forEach(it -> {
                ProductSkuPO productSkuPO = skuMap.get(it.getProductsSkuId());
                it.setWmsProductName(productSkuPO.getName());
                it.setWmsSpecification(productSkuPO.getSpecificationName());
                it.setWmsSpecQuantity(productSkuPO.getPackageQuantity());
                it.setWmsOwnerName(productSkuPO.getOwnerName());
            });
        }
        return pageList;
    }

    public PageList<ThirdPartyProductsAndRelevanceDTO>
        listPage(ThirdPartyProductsRelevancePageDTO thirdPartyProductsRelevance) {
        return thirdPartyProductsRelevanceMapper.listPage(thirdPartyProductsRelevance).toPageList();
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int insert(ThirdPartyProductsRelevancePO thirdPartyProductsRelevance) {
        thirdPartyProductsRelevance.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELEVANCE));
        return thirdPartyProductsRelevanceMapper.insert(thirdPartyProductsRelevance);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int insertBatch(List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevances) {
        thirdPartyProductsRelevances.forEach(po -> {
            po.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELEVANCE));
        });
        return thirdPartyProductsRelevanceMapper.insertBatch(thirdPartyProductsRelevances);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateRelevance(ThirdPartyProductsRelevancePO partyProductsRelevancePO) {
        thirdPartyProductsRelevanceMapper.update(partyProductsRelevancePO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdate(List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevances) {
        for (ThirdPartyProductsRelevancePO thirdPartyProductsRelevance : thirdPartyProductsRelevances) {
            thirdPartyProductsRelevanceMapper.update(thirdPartyProductsRelevance);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int delete(ThirdPartyProductsRelevancePO thirdPartyProductsRelevance) {
        return thirdPartyProductsRelevanceMapper.delete(thirdPartyProductsRelevance);
    }

    public List<ThirdPartyProductsRelevancePO> listBySkuIds(List<String> skuIds) {
        return thirdPartyProductsRelevanceMapper.listBySkuIds(skuIds);
    }

    /**
     * 寻找已经绑定的sku并同步到当前城市
     *
     * @param cityId
     */
    public void findBindingProductSKU(Integer cityId, Integer warehouseId) {
        // 根据城市id找到当前城市的所有第三方产品
        List<ThirdPartyProductsPO> thirdPartyProductsPOS =
            thirdPartyProductsMapper.listThirdProductsByCityId(cityId, warehouseId);
        if (CollectionUtils.isEmpty(thirdPartyProductsPOS)) {
            return;
        }
        Set<String> skuSet =
            thirdPartyProductsPOS.stream().map(ThirdPartyProductsPO::getProductSkuId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuSet)) {
            return;
        }
        List<String> skuList = new ArrayList<>(skuSet);
        List<List<String>> skuLists = CommonUtils.splitList(skuList, 500);
        for (List<String> skus : skuLists) {
            setThirdpartyProductRelevance(cityId, warehouseId, skus);
        }
    }

    /**
     * 根据第三方skuid，找到对应的关联关系，并插入关联表
     *
     * @param cityId
     * @param skuList
     */
    private void setThirdpartyProductRelevance(Integer cityId, Integer warehouseId, List<String> skuList) {
        // 在关联表找到非当前城市的，且第三方sku关系存在的关联数据
        ThirdRelQueryDTO queryDTO = new ThirdRelQueryDTO();
        queryDTO.setThirdProductSkuIdList(skuList);
        List<ThirdPartyProductsRelevancePO> relevancePOS =
            thirdPartyProductsRelevanceMapper.listThirdRelevances(queryDTO);
        if (CollectionUtils.isEmpty(relevancePOS)) {
            return;
        }
        // 过滤已经绑定的数据
        List<ThirdPartyProductsRelevancePO> needBindRelevancePOS = findNeedBindData(relevancePOS, cityId, warehouseId);
        List<ThirdPartyProductsRelevanceDTO> thirdRelevanceDTOS =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevanceDTOList(needBindRelevancePOS);
        if (CollectionUtils.isEmpty(thirdRelevanceDTOS)) {
            return;
        }
        // 根据关联表的信息id找到供应链对应该城市的skuId
        // Map<String,Long> thirdMap = new HashMap<>(16);
        // for (ThirdPartyProductsRelevanceDTO thirdRelevanceDTO : thirdRelevanceDTOS) {
        // thirdMap.put( thirdRelevanceDTO.getProductSkuId(),thirdRelevanceDTO.getProductInfoId());
        //
        // }

        Set<Long> infoSet = thirdRelevanceDTOS.stream().map(ThirdPartyProductsRelevanceDTO::getProductInfoId)
            .collect(Collectors.toSet());
        List<Long> infoList = new ArrayList<>(infoSet);
        List<ProductSkuListDTO> skuByInfos = productSkuMapper.findSkuByInfos(cityId, infoList);
        Map<Long, List<ProductSkuListDTO>> skuMap =
            skuByInfos.stream().collect(Collectors.groupingBy(ProductSkuListDTO::getProductInfoId));

        if (CollectionUtils.isEmpty(skuByInfos)) {
            return;
        }
        // 把供应链的skuid插入到关联表
        List<ThirdPartyProductsRelevancePO> thirdRelevances = new ArrayList<>();
        thirdRelevanceDTOS.forEach(it -> {
            List<ProductSkuListDTO> dtos = skuMap.get(it.getProductInfoId());
            if (CollectionUtils.isNotEmpty(dtos)) {
                ThirdPartyProductsRelevancePO po = new ThirdPartyProductsRelevancePO();
                po.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELEVANCE));
                po.setSpecificationId(dtos.get(0).getProductSpecificationId());
                po.setProductsSkuId(dtos.get(0).getProductSkuId());
                po.setProductInfoId(dtos.get(0).getProductInfoId());
                po.setProductSkuId(it.getProductSkuId());
                po.setCityId(cityId);
                thirdRelevances.add(po);
            }
        });

        // skuByInfos.forEach(it -> {
        // String thirdSkuId = skuMap.get(it.get);
        // if (StringUtils.isNotEmpty(thirdSkuId)) {
        // ThirdPartyProductsRelevancePO po = new ThirdPartyProductsRelevancePO();
        // po.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELEVANCE));
        // po.setSpecificationId(it.getProductSpecificationId());
        // po.setProductsSkuId(it.getProductSkuId());
        // po.setProductInfoId(it.getProductInfoId());
        // po.setProductSkuId(thirdSkuId);
        // po.setCityId(cityId);
        // thirdRelevances.add(po);
        // }
        // });
        if (CollectionUtils.isNotEmpty(thirdRelevances)) {
            thirdPartyProductsRelevanceMapper.insertBatch(thirdRelevances);
        }

    }

    /**
     * 过滤已关联的数据
     * 
     * @param relevancePOS
     * @param cityId
     * @return
     */
    private List<ThirdPartyProductsRelevancePO> findNeedBindData(List<ThirdPartyProductsRelevancePO> relevancePOS,
        Integer cityId, Integer warehouseId) {
        List<ThirdPartyProductsRelevancePO> needBindRelevancePOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(relevancePOS)) {
            return needBindRelevancePOS;
        }
        Map<String, List<ThirdPartyProductsRelevancePO>> map =
            relevancePOS.stream().collect(Collectors.groupingBy(ThirdPartyProductsRelevancePO::getProductSkuId));
        for (String thirdSkuId : map.keySet()) {
            List<ThirdPartyProductsRelevancePO> pos = map.get(thirdSkuId);
            if (CollectionUtils.isNotEmpty(pos)) {
                List<ThirdPartyProductsRelevancePO> exists =
                    pos.stream().filter(it -> cityId.equals(it.getCityId())).collect(Collectors.toList());
                if (warehouseId != null) {
                    exists = exists.stream().filter(it -> warehouseId.equals(it.getWarehouseId()))
                        .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(exists)) {
                    needBindRelevancePOS.add(pos.get(0));
                }
            }
        }
        return needBindRelevancePOS;
    }

    /**
     * 批量修改或者新增
     * 
     * @param thirdPartyProductsRelevances
     */
    public void batchInsertOrUpdate(List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevances) {
        List<ThirdPartyProductsRelevancePO> needInsert = new ArrayList<>();
        List<ThirdPartyProductsRelevancePO> needUpdate = new ArrayList<>();
        thirdPartyProductsRelevances.stream().forEach(it -> {
            if (it.getId() != null) {
                needUpdate.add(it);
            } else {
                it.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELEVANCE));
                needInsert.add(it);
            }
        });
        if (CollectionUtils.isNotEmpty(needInsert)) {
            thirdPartyProductsRelevanceMapper.insertBatch(needInsert);
        }
        if (CollectionUtils.isNotEmpty(needUpdate)) {
            for (ThirdPartyProductsRelevancePO thirdPartyProductsRelevance : needUpdate) {
                thirdPartyProductsRelevanceMapper.update(thirdPartyProductsRelevance);
            }
        }
    }

    public void deleteByIds(List<Long> ids) {
        thirdPartyProductsRelevanceMapper.deleteByIds(ids);
    }

    public void deleteByTime(ThirdPartyProductsDTO dto) {
        thirdPartyProductsRelevanceMapper.deleteByTime(dto);
    }

    /**
     * 查询第三方产品和管理产品详细数据
     * 
     * @param queryDTO
     * @return
     */
    public PageList<ThirdPartyProductAndRefDetailDTO> listProdcutAndRef(ThridPartyQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getCityId(), "城市Id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        Warehouse warehouse = warehouseQueryService.findWarehouseById(queryDTO.getWarehouseId());
        PageResult<ThirdPartyProductAndRefDetailDTO> pageResult =
            thirdPartyProductsRelevanceMapper.listProdcutAndRef(queryDTO);
        PageList<ThirdPartyProductAndRefDetailDTO> pageList = pageResult.toPageList();
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            return pageList;
        }
        if (warehouse != null) {
            pageList.getDataList().forEach(it -> {
                it.setWarehouseName(warehouse.getName());
                it.setCity(warehouse.getCity());
                it.setCityId(queryDTO.getCityId().toString());
                it.setWarehouseId(queryDTO.getWarehouseId());
            });
        }
        return pageList;
    }

    public PageList<String> listThirdSkuIds(ThridPartyQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getCityId(), "城市Id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        PageResult<ThirdPartyProductAndRefDetailDTO> pageResult =
            thirdPartyProductsRelevanceMapper.listThirdSkuId(queryDTO);
        PageList<ThirdPartyProductAndRefDetailDTO> thisPartyDTOs = pageResult.toPageList();
        PageList<String> resultPage = new PageList<>();
        resultPage.setPager(thisPartyDTOs.getPager());
        if (CollectionUtils.isEmpty(thisPartyDTOs.getDataList())) {
            return resultPage;
        }

        List<String> thirdSkuIds = new ArrayList<>();
        thisPartyDTOs.getDataList().forEach(item -> thirdSkuIds.add(item.getThirdProductSkuId()));
        resultPage.setDataList(thirdSkuIds);
        return resultPage;
    }

    /**
     * 根据产品名称和规格名称查询团购产品
     * 
     * @param queryDTO
     * @return
     */
    public List<ProductSaasSkuDTO> listProductByProductNameAndSpecName(ThirdRelQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(queryDTO.getSysCode(), "平台编号不能为空");
        AssertUtils.notEmpty(queryDTO.getProductSpecAndNameList(), "产品名称和规格名称集合不能为空");
        queryDTO.getProductSpecAndNameList().forEach(it -> {
            AssertUtils.notNull(it.getProductName(), "产品名称不能为空");
            AssertUtils.notNull(it.getSpecName(), "规格名称不能为空");
        });

        /** 1、根据产品名和规格名查询第三方关系表是否存在关联数据 */
        List<ProductSaasSkuDTO> result = new ArrayList<>();
        List<String> productNameList = queryDTO.getProductSpecAndNameList().stream()
            .map(ThirdProductSpecAndNameDTO::getProductName).collect(Collectors.toList());
        List<ThirdPartyProductsRelevancePO> pos = thirdPartyProductsRelevanceMapper.selectByProductNames(
            productNameList, queryDTO.getCityId(), queryDTO.getWarehouseId(), queryDTO.getSysCode());
        if (CollectionUtils.isNotEmpty(pos)) {
            Map<String, ThirdPartyProductsRelevancePO> nameAndSpecMap = pos.stream().collect(
                Collectors.toMap(it -> it.getProductName() + "_" + it.getSpecification(), it -> it, (v1, v2) -> v1));
            pos =
                pos.stream().filter(it -> nameAndSpecMap.get(it.getProductName() + "_" + it.getSpecification()) != null)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(pos)) {
            return result;
        }
        Set<Long> skuIds = pos.stream().filter(it -> it.getProductsSkuId() != null)
            .map(ThirdPartyProductsRelevancePO::getProductsSkuId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuIds)) {
            return result;
        }

        /** 2、根据关联产品关系，查询sku数据 */
        ProductSaasQueryDTO saasQueryDTO = new ProductSaasQueryDTO();
        saasQueryDTO.setSkuIds(new ArrayList<>(skuIds));
        List<ProductSaasSkuDTO> productSkuList = productSkuMapper.getProductSkuList(saasQueryDTO);
        if (CollectionUtils.isEmpty(productSkuList)) {
            return result;
        }
        Map<Long, ProductSaasSkuDTO> skuMap = productSkuList.stream()
            .collect(Collectors.toMap(ProductSkuListDTO::getProductSkuId, it -> it, (v1, v2) -> v1));
        pos.stream().filter(it -> it.getProductsSkuId() != null).forEach(it -> {
            ProductSaasSkuDTO productSaasSkuDTO = skuMap.get(it.getProductsSkuId());
            if (productSaasSkuDTO != null) {
                ProductSaasSkuDTO skuDTO = new ProductSaasSkuDTO();
                BeanUtils.copyProperties(productSaasSkuDTO, skuDTO);
                skuDTO.setThirdProductName(it.getProductName());
                skuDTO.setThirdSpecName(it.getSpecification());
                skuDTO.setThirdSpecQuantity(it.getSpecificationQuantity());
                result.add(skuDTO);
            }
        });
        return result;
    }

}
