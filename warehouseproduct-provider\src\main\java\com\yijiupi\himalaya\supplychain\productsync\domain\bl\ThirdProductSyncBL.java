package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ThirdProductSkuSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/9 15:32
 */
@Service
public class ThirdProductSyncBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdProductSyncBL.class);
    @Reference
    private IOrgService iOrgService;
    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;
    @Autowired
    private ProductInfoBySaasBL productInfoBySaasBl;
    @Autowired
    private ThirdproductsRelationBL thirdproductsRelationBL;
    @Autowired
    private ProductSkuBySaasBL productSkuBySaasBL;
    @Autowired
    private ProductSkuBL productSkuBL;

    /**
     * 同步第三方产品数据
     *
     * @param thirdProductSkuSyncDTOS
     */
    public void syncThirdProducts(List<ThirdProductSkuSyncDTO> thirdProductSkuSyncDTOS) {
        for (ThirdProductSkuSyncDTO thirdProductSkuSyncDTO : thirdProductSkuSyncDTOS) {
            syncThirdProduct(thirdProductSkuSyncDTO);
        }
    }

    /**
     * 异步同步第三方产品
     *
     * @param thirdProductSkuSyncDTO
     */
    @Async
    public void syncThirdProduct(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO) {
        // 校验参数
        validateThirdProduct(thirdProductSkuSyncDTO);
        Integer orgId = thirdProductSkuSyncDTO.getOrgId();
        Integer parentOrgId = getParentOrgId(orgId);
        thirdProductSkuSyncDTO.setParentOrgId(parentOrgId);
        List<ThirdproductsRelationDTO> thirdProudctsRelation =
            getThirdProudctsRelation(parentOrgId, Arrays.asList(thirdProductSkuSyncDTO.getRefSkuId()), null, null,
                thirdProductSkuSyncDTO.getSysCode(), orgId);
        if (CollectionUtils.isNotEmpty(thirdProudctsRelation)) {
            updateThirdProductSku(thirdProductSkuSyncDTO, thirdProudctsRelation.get(0).getProductskuId());
            return;
        }
        saveThirdProductInfoAndSku(thirdProductSkuSyncDTO, orgId, parentOrgId);
    }

    /**
     * 修改第三方产品sku
     */
    public void updateThirdProductSku(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO, Long skuId) {
        LOGGER.info("【SAAS】修改第三方产品sku参数：{}", JSON.toJSONString(thirdProductSkuSyncDTO));
        ProductSkuListDTO productSku = productSkuBySaasBL.getProductSku(skuId);
        if (productSku == null) {
            LOGGER.info("产品不存在，skuId:{}", skuId);
            return;
        }
        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
        productSkuDTO.setId(productSku.getId());
        productSkuDTO.setProductState(thirdProductSkuSyncDTO.getStatus().intValue());
        productSkuBySaasBL.updateProductSku(productSkuDTO);
    }

    /**
     * 新增第三方产品
     *
     * @return
     */
    public Long saveThirdProductInfoAndSku(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO, Integer orgId,
        Integer parentOrgId) {
        LOGGER.info("【SAAS】同步第三方产品参数：{}", JSON.toJSONString(thirdProductSkuSyncDTO));
        String sysCode = thirdProductSkuSyncDTO.getSysCode();
        List<ThirdproductsRelationDTO> thirdProudctsRelation =
            getThirdProudctsRelation(parentOrgId, null, thirdProductSkuSyncDTO.getRefInfoId(), null, sysCode, orgId);
        Long infoId = null;
        if (CollectionUtils.isEmpty(thirdProudctsRelation)) {
            // 1、新增产品信息
            ProductInfoPO productInfoPO = convertThridProductToProductInfoPO(thirdProductSkuSyncDTO);
            if (productInfoPOMapper.countProductInfo(productInfoPO) > 0) {
                throw new BusinessValidateException("产品编码（" + productInfoPO.getProductCode() + "）已存在，请重新输入！");
            }
            productInfoPOMapper.insertSelective(productInfoPO);
            LOGGER.info("【SAAS】新增产品信息：{}", JSON.toJSONString(productInfoPO));
            // 2、保存产品条码
            productInfoBySaasBl.saveBottleCode(productInfoPO);
            // 3、保存产品类目
            productInfoBySaasBl.saveProductCategory(productInfoPO, orgId);
            infoId = productInfoPO.getId();
        } else {
            infoId = thirdProudctsRelation.get(0).getProductinfoId();
        }

        List<ProductSkuDTO> productSkuList = new ArrayList<>();
        List<ThirdproductsRelationDTO> thirdSpecRelation = getThirdProudctsRelation(parentOrgId, null, null,
            thirdProductSkuSyncDTO.getRefSpecificationId(), sysCode, orgId);
        if (CollectionUtils.isEmpty(thirdSpecRelation)) {
            // 4、新增产品信息规格
            ProductInfoSpecificationDTO specification = convertThridProductToSpecification(thirdProductSkuSyncDTO);
            List<ProductInfoSpecificationPO> specificationPOS =
                productInfoBySaasBl.saveProductInfoSpecification(Arrays.asList(specification), infoId);
            for (ProductInfoSpecificationPO specificationPO : specificationPOS) {
                ProductSkuDTO productSkuDTO = new ProductSkuDTO();
                productSkuDTO.setProductSpecificationId(specificationPO.getId());
                productSkuDTO.setProductInfoId(infoId);
                productSkuList.add(productSkuDTO);
            }
        } else {
            ProductSkuDTO productSkuDTO = new ProductSkuDTO();
            productSkuDTO.setProductSpecificationId(thirdSpecRelation.get(0).getSpecificationId());
            productSkuDTO.setProductInfoId(infoId);
            productSkuList.add(productSkuDTO);
        }
        // 5、新增产品sku
        List<ProductSkuPO> productSkuPOS = productSkuBL.saveProductSku(Arrays.asList(orgId),
            thirdProductSkuSyncDTO.getOwnerId().toString(), thirdProductSkuSyncDTO.getOwnerName(), productSkuList);
        List<ThirdproductsRelationDTO> thirdProducRelations = new ArrayList<>();
        productSkuPOS.forEach(skuPO -> {
            ThirdproductsRelationDTO relationDTO = new ThirdproductsRelationDTO();
            relationDTO.setSysCode(thirdProductSkuSyncDTO.getSysCode());
            relationDTO.setSysName(thirdProductSkuSyncDTO.getSysName());
            relationDTO.setOrgId(parentOrgId);
            relationDTO.setCityId(orgId);
            relationDTO.setThirdskuId(thirdProductSkuSyncDTO.getRefSkuId());
            relationDTO.setThirdinfoId(thirdProductSkuSyncDTO.getRefInfoId());
            relationDTO.setThirdspecificationId(thirdProductSkuSyncDTO.getRefSpecificationId());
            relationDTO.setProductskuId(skuPO.getProductSkuId());
            relationDTO.setProductinfoId(skuPO.getProductInfoId());
            relationDTO.setSpecificationId(skuPO.getProductSpecificationId());
            thirdProducRelations.add(relationDTO);
        });
        // 6、新增产品第三方关联
        saveThirdProductsRelation(thirdProducRelations);
        return infoId;
    }

    /**
     * 转换第三方产品规格数据
     *
     * @param thirdProductSkuSyncDTO
     * @return
     */
    public static ProductInfoSpecificationDTO
        convertThridProductToSpecification(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO) {
        ProductInfoSpecificationDTO specification = new ProductInfoSpecificationDTO();
        specification.setVolume(thirdProductSkuSyncDTO.getSpecVolume());
        specification.setLength(thirdProductSkuSyncDTO.getSpecLength());
        specification.setHeight(thirdProductSkuSyncDTO.getSpecHeight());
        specification.setWidth(thirdProductSkuSyncDTO.getSpecWidth());
        specification.setWeight(thirdProductSkuSyncDTO.getSpecWeight());
        specification.setName(thirdProductSkuSyncDTO.getSpecName());
        specification.setPackageQuantity(thirdProductSkuSyncDTO.getPackageQuantity());
        specification.setPackageName(thirdProductSkuSyncDTO.getPackageName());
        specification.setUnitName(thirdProductSkuSyncDTO.getUnitName());
        specification.setBarCode(thirdProductSkuSyncDTO.getBarCode());
        specification.setState(thirdProductSkuSyncDTO.getStatus());
        specification.setCreateUserId(thirdProductSkuSyncDTO.getCreateUserId());
        return specification;
    }

    /**
     * 转换第三方产品数据
     *
     * @param thirdProductSkuSyncDTO
     * @return
     */
    public static ProductInfoPO convertThridProductToProductInfoPO(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO) {
        ProductInfoPO productInfoPO = new ProductInfoPO();
        if (productInfoPO.getId() == null) {
            productInfoPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_INFO));
        }
        productInfoPO.setProductName(thirdProductSkuSyncDTO.getProductName());
        productInfoPO.setParentOrgId(thirdProductSkuSyncDTO.getParentOrgId());
        productInfoPO.setBrand(thirdProductSkuSyncDTO.getBrand());
        productInfoPO.setBottleCode(thirdProductSkuSyncDTO.getBottleCode());
        productInfoPO.setStatisticsCategoryName(thirdProductSkuSyncDTO.getStatisticsCategoryName());
        productInfoPO.setProductCode(thirdProductSkuSyncDTO.getProductCode());
        productInfoPO.setStatus(thirdProductSkuSyncDTO.getStatus());
        productInfoPO.setProductStatisticsClass(thirdProductSkuSyncDTO.getProductStatisticsClass());
        productInfoPO.setSecondStatisticsClass(thirdProductSkuSyncDTO.getSecondStatisticsClass());
        productInfoPO.setMonthOfShelfLife(thirdProductSkuSyncDTO.getMonthOfShelfLife());
        productInfoPO.setShelfLifeUnit(thirdProductSkuSyncDTO.getShelfLifeUnit());
        productInfoPO.setShelfLifeLongTime(thirdProductSkuSyncDTO.getShelfLifeLongTime());
        productInfoPO.setProcess(thirdProductSkuSyncDTO.getProcess());
        productInfoPO.setStorageType(thirdProductSkuSyncDTO.getStorageType());
        productInfoPO.setDefaultImageFile_Id(thirdProductSkuSyncDTO.getDefaultImageFileId());
        productInfoPO.setCreateUser_Id(thirdProductSkuSyncDTO.getCreateUserId());
        productInfoPO.setLastUpdateUser_Id(thirdProductSkuSyncDTO.getLastUpdateUserId());
        productInfoPO.setCreateTime(new Date());
        productInfoPO.setLastUpdateTime(new Date());
        if (StringUtils.isEmpty(productInfoPO.getGeneralName())) {
            productInfoPO.setGeneralName(productInfoPO.getProductName());
        }
        if (productInfoPO.getMonthOfShelfLife() == null) {
            productInfoPO.setMonthOfShelfLife(0);
            productInfoPO.setShelfLifeUnit(null);
        } else if (productInfoPO.getShelfLifeUnit() == null) {
            productInfoPO.setShelfLifeUnit((byte)3);
        }
        return productInfoPO;
    }

    /**
     * 校验第三方产品参数
     */
    private void validateThirdProduct(ThirdProductSkuSyncDTO thirdProductSkuSyncDTO) {
        AssertUtils.notNull(thirdProductSkuSyncDTO, "参数不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getProductCode(), "产品编码不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getStatus(), "产品状态不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getSpecName(), "包装规格名称不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getStatus(), "状态不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getPackageName(), "包装规格大单位不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getUnitName(), "包装规格小单位不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getPackageQuantity(), "包装规格大小单位转换系数不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getRefSkuId(), "第三方产品SKUID不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getRefInfoId(), "第三方产品信息ID不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getRefSpecificationId(), "第三方产品规格ID不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getSysCode(), "第三方系统编码不能为空");
        AssertUtils.notNull(thirdProductSkuSyncDTO.getCreateUserId(), "第三方产品创建人id不能为空");
    }

    /**
     * 获取城市id
     *
     * @return
     */
    private Integer getParentOrgId(Integer orgId) {
        // 获取组织机构ID
        Integer parentOrgId = iOrgService.findParentOrgBy(orgId);
        LOGGER.info("parentOrgId：{}", parentOrgId);
        return parentOrgId;
    }

    /**
     * 获取第三方产品关联
     *
     * @param parentOrgId
     * @param thirdskuIdList
     * @param thridInfoId
     * @param thridspecId
     * @return
     */
    private List<ThirdproductsRelationDTO> getThirdProudctsRelation(Integer parentOrgId, List<String> thirdskuIdList,
        String thridInfoId, String thridspecId, String sysCode, Integer cityId) {
        ThirdproductsRelationQueryDTO queryDTO = new ThirdproductsRelationQueryDTO();
        queryDTO.setOrgId(parentOrgId);
        queryDTO.setThirdinfoId(thridInfoId);
        queryDTO.setThirdspecificationId(thridspecId);
        queryDTO.setThirdskuIdList(thirdskuIdList);
        queryDTO.setCityId(cityId);
        queryDTO.setSysCode(sysCode);
        return thirdproductsRelationBL.list(queryDTO);
    }

    /**
     * 保存第三方产品关系
     *
     * @param thirdProducRelations
     */
    private void saveThirdProductsRelation(List<ThirdproductsRelationDTO> thirdProducRelations) {
        if (CollectionUtils.isEmpty(thirdProducRelations)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(thirdProducRelations)) {
            thirdproductsRelationBL.insertBatch(thirdProducRelations);
            LOGGER.info("【SAAS】新增产品第三方关联：{}", JSON.toJSONString(thirdProducRelations));
        }
    }
}
