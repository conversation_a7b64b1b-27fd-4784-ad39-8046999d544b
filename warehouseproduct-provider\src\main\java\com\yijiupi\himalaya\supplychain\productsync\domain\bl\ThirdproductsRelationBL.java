package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdproductsRelationMapper;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.UUIDGeneratorUtil;

/**
 * <AUTHOR>
 * @date 2020-03-13
 */
@Service
public class ThirdproductsRelationBL {

    public static final String THIRDPARTY_PRODUCT_RELATION = "thirdproductsrelation";

    @Autowired
    private ThirdproductsRelationMapper thirdproductsrelationMapper;

    public ThirdproductsRelationDTO detail(String id) {
        return thirdproductsrelationMapper.detail(id);
    }

    public List<ThirdproductsRelationDTO> list(ThirdproductsRelationQueryDTO thirdproductsrelation) {
        return thirdproductsrelationMapper.list(thirdproductsrelation);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void insert(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelation.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELATION));
        thirdproductsrelationMapper.insert(thirdproductsrelation);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void insertBatch(List<ThirdproductsRelationDTO> thirdproductsrelations) {
        if (CollectionUtils.isEmpty(thirdproductsrelations)) {
            return;
        }
        thirdproductsrelations.forEach(it -> {
            it.setId(UUIDGeneratorUtil.getUUID(THIRDPARTY_PRODUCT_RELATION));
        });
        thirdproductsrelationMapper.insertBatch(thirdproductsrelations);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void update(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelationMapper.update(thirdproductsrelation);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelationMapper.delete(thirdproductsrelation);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteByIds(List<Long> ids) {
        thirdproductsrelationMapper.deleteByIds(ids);
    }

}
