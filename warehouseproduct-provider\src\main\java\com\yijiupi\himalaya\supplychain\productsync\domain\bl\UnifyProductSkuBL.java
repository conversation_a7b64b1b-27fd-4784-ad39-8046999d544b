package com.yijiupi.himalaya.supplychain.productsync.domain.bl;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.convertor.UnifyProductSkuConverter;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.unify.UnifyProductSkuDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中台sku操作
 * 
 * <AUTHOR>
 * @Date 2021/8/17 16:30
 */
@Service
public class UnifyProductSkuBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(UnifyProductSkuBL.class);

    @Autowired
    private UnifyProductSkuMapper unifyProductSkuMapper;

    @Autowired
    private UnifyProductSkuConverter unifyProductSkuConverter;

    /**
     * 根据中台skuId查找出中台sku信息
     */
    public UnifyProductSkuDTO getUnifySkuBySkuId(Long skuId) {
        AssertUtils.notNull(skuId, "中台skuId不能为空");
        UnifyProductSkuPO unifyProductSkuPO = unifyProductSkuMapper.getBySkuId(skuId);
        return unifyProductSkuConverter.convert(unifyProductSkuPO);
    }

    /**
     * 根据中台infoId查找出中台sku信息
     */
    public List<UnifyProductSkuDTO> listUnifySkuByInfoId(Long infoId) {
        AssertUtils.notNull(infoId, "中台InfoId不能为空");
        List<UnifyProductSkuPO> unifyProductSkuPOS = unifyProductSkuMapper.listUnifySkuByInfoId(infoId);
        return unifyProductSkuConverter.convertList(unifyProductSkuPOS);
    }
}
