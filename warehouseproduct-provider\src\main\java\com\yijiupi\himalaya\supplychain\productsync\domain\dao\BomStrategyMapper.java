package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.dto.product.BomStrategyDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.BomStrategyQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-11
 */
public interface BomStrategyMapper {

    BomStrategyDTO detail(String id);

    List<BomStrategyDTO> list(BomStrategyQueryDTO bomStrategy);

    int insert(BomStrategyDTO bomStrategy);

    int insertBatch(List<BomStrategyDTO> bomStrategys);

    int update(BomStrategyDTO bomStrategy);

    int delete(BomStrategyDTO bomStrategy);

    int deleteByInfoId(Long productInfoId);

}