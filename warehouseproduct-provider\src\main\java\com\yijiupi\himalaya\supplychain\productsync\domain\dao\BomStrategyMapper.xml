<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.BomStrategyMapper">

    <resultMap id="bomStrategyResultMap" type="com.yijiupi.himalaya.supplychain.productsync.dto.product.BomStrategyDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="productInfoId" jdbcType="BIGINT" property="productInfoId"/>
        <result column="rawMaterialProductInfoId" jdbcType="BIGINT" property="rawMaterialProductInfoId"/>
        <result column="processNum" jdbcType="INTEGER" property="processNum"/>
        <result column="maxNum" jdbcType="INTEGER" property="maxNum"/>
        <result column="parentOrgId" jdbcType="INTEGER" property="parentOrgId"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="orgId" jdbcType="INTEGER" property="orgId"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>


    </resultMap>

    <sql id="bomStrategyColumns">
        bomstrategy.Id AS "id",
        bomstrategy.ProductInfo_Id AS "productInfoId",
        bomstrategy.RawMaterialProductInfo_Id AS "rawMaterialProductInfoId",
        bomstrategy.ProcessNum AS "processNum",
        bomstrategy.MaxNum AS "maxNum",
        bomstrategy.ParentOrg_Id AS "parentOrgId",
        bomstrategy.Warehouse_Id AS "warehouseId",
        bomstrategy.Org_Id AS "orgId",
        bomstrategy.CreateTime AS "createTime",
        bomstrategy.LastUpdateTime AS "lastUpdateTime"
    </sql>


    <select id="detail" resultMap="bomStrategyResultMap">
        SELECT
        <include refid="bomStrategyColumns"/>
        FROM bomstrategy
        <where>
            bomstrategy.Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="list" resultMap="bomStrategyResultMap">
        SELECT
        <include refid="bomStrategyColumns"/>
        FROM bomstrategy
        <where>
            <if test="productInfoId!= null">
                and ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
            </if>
            <if test="rawMaterialProductInfoId!= null">
                and RawMaterialProductInfo_Id = #{rawMaterialProductInfoId,jdbcType=BIGINT}
            </if>
            <if test="parentOrgId!= null">
                and ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId!= null">
                and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="orgId!= null">
                and Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <insert id="insert">
        INSERT INTO bomstrategy(
        Id,
        ProductInfo_Id,
        RawMaterialProductInfo_Id,
        ProcessNum,
        MaxNum,
        ParentOrg_Id,
        Warehouse_Id,
        Org_Id,
        CreateTime,
        LastUpdateTime
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{productInfoId,jdbcType=BIGINT},
        #{rawMaterialProductInfoId,jdbcType=BIGINT},
        #{processNum,jdbcType=INTEGER},
        #{maxNum,jdbcType=INTEGER},
        #{parentOrgId,jdbcType=INTEGER},
        #{warehouseId,jdbcType=INTEGER},
        #{orgId,jdbcType=INTEGER},
        now(),
        now()
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO bomstrategy(
        Id,
        ProductInfo_Id,
        RawMaterialProductInfo_Id,
        ProcessNum,
        MaxNum,
        ParentOrg_Id,
        Warehouse_Id,
        Org_Id,
        CreateTime,
        LastUpdateTime
        )
        VALUES
        <foreach collection="list" item="bomStrategy" separator=",">
            (
            #{bomStrategy.id,jdbcType=BIGINT},
            #{bomStrategy.productInfoId,jdbcType=BIGINT},
            #{bomStrategy.rawMaterialProductInfoId,jdbcType=BIGINT},
            #{bomStrategy.processNum,jdbcType=INTEGER},
            #{bomStrategy.maxNum,jdbcType=INTEGER},
            #{bomStrategy.parentOrgId,jdbcType=INTEGER},
            #{bomStrategy.warehouseId,jdbcType=INTEGER},
            #{bomStrategy.orgId,jdbcType=INTEGER},
            now(),
            now()
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE bomstrategy
        <set>
            <if test="productInfoId!= null">
                ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="rawMaterialProductInfoId!= null">
                RawMaterialProductInfo_Id = #{rawMaterialProductInfoId,jdbcType=BIGINT},
            </if>
            <if test="processNum!= null">
                ProcessNum = #{processNum,jdbcType=INTEGER},
            </if>
            <if test="maxNum!= null">
                MaxNum = #{maxNum,jdbcType=INTEGER},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="delete">
        DELETE FROM bomstrategy
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByInfoId">
        DELETE FROM bomstrategy
        WHERE ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
    </update>

</mapper>