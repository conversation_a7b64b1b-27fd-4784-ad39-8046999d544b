package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO;
import org.apache.ibatis.annotations.Param;

public interface ProductCategoryGroupConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductCategoryGroupConfigPO record);

    int insertSelective(ProductCategoryGroupConfigPO record);

    ProductCategoryGroupConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductCategoryGroupConfigPO record);

    int updateByPrimaryKey(ProductCategoryGroupConfigPO record);

    ProductCategoryGroupConfigPO selectParentOrgConfigById(@Param("parentOrgId") Integer parentOrgId);

    ProductCategoryGroupConfigPO selectByParentOrgIdAndRefIdAndOwnerId(@Param("parentOrgId") Integer parentOrgId,
        @Param("refId") Integer refId, @Param("ownerId") Long ownerId);
}