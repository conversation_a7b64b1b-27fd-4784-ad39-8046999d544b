<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryGroupConfigMapper">

    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ParentOrg_Id" jdbcType="INTEGER" property="parentOrgId"/>
        <result column="Ref_Id" jdbcType="INTEGER" property="refId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="CategoryType" jdbcType="TINYINT" property="categoryType"/>
        <result column="CategoryGroup_Id" jdbcType="INTEGER" property="categoryGroupId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, ParentOrg_Id, Ref_Id, Owner_Id, CategoryType, CategoryGroup_Id, Name, Remark,
        CreateUser, CreateTime, LastUpdateUser, LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroupconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectParentOrgConfigById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroupconfig
        where ParentOrg_Id = #{parentOrgId} and Owner_Id is null and Ref_Id is null
    </select>

    <select id="selectByParentOrgIdAndRefIdAndOwnerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroupconfig
        where ParentOrg_Id = #{parentOrgId}
        <if test="refId != null">
            and Ref_Id = #{refId}
        </if>
        <if test="refId == null">
            and Ref_Id is null
        </if>
        <if test="ownerId == null">
            and Owner_Id is null
        </if>
        <if test="ownerId != null">
            and Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcategorygroupconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO">
        insert into productcategorygroupconfig (Id, ParentOrg_Id, Ref_Id,
        Owner_Id, CategoryType, CategoryGroup_Id,
        Name, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime
        )
        values (#{id,jdbcType=BIGINT}, #{parentOrgId,jdbcType=INTEGER}, #{refId,jdbcType=INTEGER},
        #{ownerId,jdbcType=BIGINT}, #{categoryType,jdbcType=TINYINT}, #{categoryGroupId,jdbcType=INTEGER},
        #{name,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO">
        insert into productcategorygroupconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id,
            </if>
            <if test="refId != null">
                Ref_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="categoryType != null">
                CategoryType,
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="parentOrgId != null">
                #{parentOrg_Id,jdbcType=INTEGER},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=INTEGER},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="categoryType != null">
                #{categoryType,jdbcType=TINYINT},
            </if>
            <if test="categoryGroupId != null">
                #{categoryGroupId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO">
        update productcategorygroupconfig
        <set>
            <if test="parentOrgId != null">
                ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER},
            </if>
            <if test="refId != null">
                Ref_Id = #{refId,jdbcType=INTEGER},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="categoryType != null">
                CategoryType = #{categoryType,jdbcType=TINYINT},
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id = #{categoryGroupId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO">
        update productcategorygroupconfig
        set ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER},
        Ref_Id = #{refId,jdbcType=INTEGER},
        Owner_Id = #{ownerId,jdbcType=BIGINT},
        CategoryType = #{categoryType,jdbcType=TINYINT},
        CategoryGroup_Id = #{categoryGroupId,jdbcType=INTEGER},
        Name = #{name,jdbcType=VARCHAR},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP}
        where Id = #{id,jdbcType=BIGINT}
    </update>
</mapper>