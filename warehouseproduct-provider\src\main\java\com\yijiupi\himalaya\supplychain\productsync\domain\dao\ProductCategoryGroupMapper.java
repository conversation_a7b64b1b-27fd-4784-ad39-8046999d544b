package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface ProductCategoryGroupMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductCategoryGroupPO record);

    int insertSelective(ProductCategoryGroupPO record);

    ProductCategoryGroupPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductCategoryGroupPO record);

    int updateByPrimaryKey(ProductCategoryGroupPO record);

    List<ProductCategoryGroupPO>
        findProductCategoryGroupTree(@Param("query") ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO);

    List<ProductCategoryGroupPO> findProductCategoryGroupTreeByParentId(@Param("parentId") Long parentId);

    List<ProductCategoryGroupPO> listByRefCategoryIds(@Param("refCategoryIds") List<String> refCategoryIds,
        @Param("categoryGroupId") Long categoryGroupId);

    void insertBatch(@Param("list") List<ProductCategoryGroupPO> productCategoryGroupPOS);

    ProductCategoryGroupPO getByRefCategoryId(@Param("refCategoryId") String refCategoryId,
        @Param("categoryGroupId") Long categoryGroupId);

    void insertOrUpdateBatch(@Param("list") List<ProductCategoryGroupPO> productCategoryGroupPOS);

    PageResult<ProductCategoryGroupPO> pageListProductCategoryGroup(
        @Param("query") ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    List<ProductCategoryGroupPO> listByCategoryIds(@Param("categoryIds") Collection<Long> categoryId);

    void deleteBatchByIds(@Param("categoryIds") List<Long> categoryIds);

    List<ProductCategoryGroupPO> findDuplicateNameCategory(@Param("names") List<String> names,
        @Param("parentId") Long parentId, @Param("categoryGroupId") Long categoryGroupId);

    /**
     * 根据类目名称查询类目信息
     * 
     * @param names
     * @param categoryGroupId
     * @return
     */
    List<ProductCategoryGroupPO> listCategoryByNames(@Param("names") List<String> names,
        @Param("categoryGroupId") Long categoryGroupId);

    /**
     * 根据infoId查询类目是否强制录入箱码
     * 
     * @return
     */
    Byte listMandatoryEntryBoxCode(Long productInfoId);

    /**
     * 批量更新类目分组、类目名称、日期限制
     *
     * @param po  更新参数
     * @param ids 更新 id
     * @return 更新条数
     */
    int updateCategoryGroupBatch(ProductCategoryGroupPO po, Collection<Long> ids);

    /**
     * 批量更新类目分组、类目名称、日期限制, 可选更新
     *
     * @param po  更新参数
     * @param ids 更新 id
     * @return 更新条数
     */
    int batchUpdateCategoryGroupSelective(ProductCategoryGroupPO po, List<Long> ids);
}