<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryGroupMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="CategoryGroup_Id" jdbcType="BIGINT" property="categoryGroupId"/>
        <result column="Parent_Id" jdbcType="BIGINT" property="parentId"/>
        <result column="RefCategory_Id" jdbcType="VARCHAR" property="refCategoryId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Type" jdbcType="TINYINT" property="type"/>
        <result column="IsRelated" jdbcType="TINYINT" property="isRelated"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="GroupType" property="groupType"/>
        <result column="AttentionPeriod" property="attentionPeriod"/>
        <result column="NearExpiryPeriod" property="nearExpiryPeriod"/>
        <result column="ForbidSalesPeriod" property="forbidSalesPeriod"/>
    </resultMap>

    <resultMap id="ProductCategoryGroupTreeMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="CategoryGroup_Id" jdbcType="BIGINT" property="categoryGroupId"/>
        <result column="Parent_Id" jdbcType="BIGINT" property="parentId"/>
        <result column="RefCategory_Id" jdbcType="VARCHAR" property="refCategoryId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Type" jdbcType="TINYINT" property="type"/>
        <result column="IsRelated" jdbcType="TINYINT" property="isRelated"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="GroupType" property="groupType"/>
        <result column="AttentionPeriod" property="attentionPeriod"/>
        <result column="NearExpiryPeriod" property="nearExpiryPeriod"/>
        <result column="ForbidSalesPeriod" property="forbidSalesPeriod"/>
        <result column="UnsalablePeriod" property="unsalablePeriod"/>
        <collection property="childProductCategoryGroupPOS" column="Id"
                    ofType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO"
                    select="findProductCategoryGroupTreeByParentId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, CategoryGroup_Id, Parent_Id, RefCategory_Id, Name, Sequence, Type, IsRelated,
        Remark,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        GroupType,
        AttentionPeriod,
        NearExpiryPeriod,
        ForbidSalesPeriod,
        UnsalablePeriod
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcategorygroup
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        insert into productcategorygroup (Id, CategoryGroup_Id, Parent_Id,
        RefCategory_Id, Name, Sequence,
        Type, IsRelated, Remark,
        CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime)
        values (#{id,jdbcType=BIGINT}, #{categoryGroupId,jdbcType=BIGINT}, #{parentId,jdbcType=INTEGER},
        #{refCategoryId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sequence,jdbcType=INTEGER},
        #{type,jdbcType=TINYINT}, #{isRelated,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, now(), #{lastUpdateUser,jdbcType=VARCHAR},
        now())
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        insert into productcategorygroup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id,
            </if>
            <if test="parentId != null">
                Parent_Id,
            </if>
            <if test="refCategoryId != null">
                RefCategory_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sequence != null">
                Sequence,
            </if>
            <if test="type != null">
                Type,
            </if>
            <if test="isRelated != null">
                IsRelated,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            CreateTime,
            LastUpdateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="categoryGroupId != null">
                #{categoryGroupId,jdbcType=BIGINT},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="refCategoryId != null">
                #{refCategoryId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="isRelated != null">
                #{isRelated,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        update productcategorygroup
        <set>
            <if test="categoryGroupId != null">
                CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT},
            </if>
            <if test="parentId != null">
                Parent_Id = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="refCategoryId != null">
                RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                Sequence = #{sequence,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                Type = #{type,jdbcType=TINYINT},
            </if>
            <if test="isRelated != null">
                IsRelated = #{isRelated,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupType != null">
                GroupType = #{groupType,jdbcType=INTEGER},
            </if>
            <if test="attentionPeriod != null">
                AttentionPeriod = #{attentionPeriod,jdbcType=VARCHAR},
            </if>
            <if test="nearExpiryPeriod != null">
                NearExpiryPeriod = #{nearExpiryPeriod,jdbcType=VARCHAR},
            </if>
            <if test="unsalablePeriod != null">
                UnsalablePeriod = #{unsalablePeriod,jdbcType=VARCHAR}
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO">
        update productcategorygroup
        set CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT},
        Parent_Id = #{parentId,jdbcType=INTEGER},
        RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR},
        Name = #{name,jdbcType=VARCHAR},
        Sequence = #{sequence,jdbcType=INTEGER},
        Type = #{type,jdbcType=TINYINT},
        IsRelated = #{isRelated,jdbcType=TINYINT},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findProductCategoryGroupTree" resultMap="ProductCategoryGroupTreeMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where 1=1
        <if test="query.categoryGroupId != null ">
            and CategoryGroup_Id = #{query.categoryGroupId,jdbcType=BIGINT}
        </if>
        <if test="query.parentId == null ">
            and Parent_Id is null
        </if>
        <if test="query.parentId != null and query.parentIds == null ">
            and Id = #{query.parentId,jdbcType=BIGINT}
        </if>
        <if test="query.categoryName != null ">
            and Name like concat('%',#{query.categoryName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.parentIds != null and query.parentIds.size() > 0 ">
            and Id in
            <foreach collection="query.parentIds" item="id" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findProductCategoryGroupTreeByParentId" resultMap="ProductCategoryGroupTreeMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where Parent_Id = #{parentId,jdbcType=BIGINT}
    </select>

    <select id="listByRefCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and RefCategory_Id in
        <foreach collection="refCategoryIds" item="refCategoryId" open="(" separator="," close=")">
            #{refCategoryId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into productcategorygroup (Id, CategoryGroup_Id, Parent_Id,
        RefCategory_Id, Name, Sequence,
        Type, IsRelated, Remark,
        CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.categoryGroupId,jdbcType=BIGINT}, #{item.parentId,jdbcType=INTEGER},
            #{item.refCategoryId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.sequence,jdbcType=INTEGER},
            #{item.type,jdbcType=TINYINT}, #{item.isRelated,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, now(), #{item.lastUpdateUser,jdbcType=VARCHAR},
            now()
            )
        </foreach>
    </insert>


    <select id="getByRefCategoryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR}
    </select>

    <insert id="insertOrUpdateBatch">
        insert into productcategorygroup (Id, CategoryGroup_Id, Parent_Id,
        RefCategory_Id, Name, Sequence,
        Type, IsRelated, Remark,
        CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime, MandatoryEntryBoxCode)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.categoryGroupId,jdbcType=BIGINT}, #{item.parentId,jdbcType=INTEGER},
            #{item.refCategoryId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.sequence,jdbcType=INTEGER},
            #{item.type,jdbcType=TINYINT}, #{item.isRelated,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.mandatoryEntryBoxCode,jdbcType=TINYINT}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        Parent_Id = VALUES(Parent_Id),
        RefCategory_Id = VALUES(RefCategory_Id),
        Name = VALUES(Name),
        IsRelated = VALUES(IsRelated),
        MandatoryEntryBoxCode = VALUES(MandatoryEntryBoxCode),
        LastUpdateUser = VALUES(LastUpdateUser)
    </insert>

    <select id="pageListProductCategoryGroup" resultMap="ProductCategoryGroupTreeMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        <where>
            <if test="query.categoryGroupId != null ">
                and CategoryGroup_Id = #{query.categoryGroupId,jdbcType=BIGINT}
            </if>
            <if test="query.parentId != null">
                and Parent_Id = #{query.parentId,jdbcType=BIGINT}
            </if>
            <if test="query.parentId == null">
                and Parent_Id is null
            </if>
            <if test="query.categoryName != null and query.categoryName != '' ">
                and Name like concat('%',#{query.categoryName,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.ids != null and query.ids.size() > 0 ">
                and id in
                <foreach collection="query.ids" item="id" open="(" close=")" separator=",">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.groupType != null">
                and GroupType = #{query.groupType,jdbcType=INTEGER}
            </if>
        </where>
        order by Sequence, LastUpdateTime desc
    </select>

    <select id="listByCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where Id in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <delete id="deleteBatchByIds">
        delete
        from productcategorygroup
        where Id in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <select id="findDuplicateNameCategory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=VARCHAR}
        <if test="parentId != null ">
            and Parent_Id = #{parentId,jdbcType=BIGINT}
        </if>
        <if test="parentId == null ">
            and Parent_Id is null
        </if>
        and Name in
        <foreach collection="names" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listCategoryByNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategorygroup
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=VARCHAR}
        and Name in
        <foreach collection="names" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="listMandatoryEntryBoxCode" resultType="java.lang.Byte">
        SELECT pcg.MandatoryEntryBoxCode
        FROM productinfocategory pc inner join productcategorygroup pcg
        on pc.StatisticsClass = pcg.id
        and pc.ProductInfo_id = #{productInfoId,jdbcType=BIGINT}
    </select>

    <update id="updateCategoryGroupBatch">
        update productcategorygroup
        <set>
            GroupType = #{param1.groupType,jdbcType=INTEGER},
            AttentionPeriod = #{param1.attentionPeriod,jdbcType=VARCHAR},
            NearExpiryPeriod = #{param1.nearExpiryPeriod,jdbcType=VARCHAR},
            ForbidSalesPeriod = #{param1.forbidSalesPeriod,jdbcType=VARCHAR},
            UnsalablePeriod = #{param1.unsalablePeriod,jdbcType=VARCHAR},
            LastUpdateUser = #{param1.lastUpdateUser,jdbcType=VARCHAR}
        </set>
        <where>
            Id in
            <foreach collection="param2" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </where>
    </update>

    <update id="batchUpdateCategoryGroupSelective">
        update productcategorygroup
        <set>
            <if test="param1.groupType != null">
                GroupType = #{param1.groupType,jdbcType=INTEGER},
            </if>
            <if test="param1.attentionPeriod != null">
                AttentionPeriod = #{param1.attentionPeriod,jdbcType=VARCHAR},
            </if>
            <if test="param1.nearExpiryPeriod != null">
                NearExpiryPeriod = #{param1.nearExpiryPeriod,jdbcType=VARCHAR},
            </if>
            <if test="param1.forbidSalesPeriod != null">
                ForbidSalesPeriod = #{param1.forbidSalesPeriod,jdbcType=VARCHAR},
            </if>
            <if test="param1.unsalablePeriod != null">
                UnsalablePeriod = #{param1.unsalablePeriod,jdbcType=VARCHAR},
            </if>
            LastUpdateUser = #{param1.lastUpdateUser,jdbcType=VARCHAR}
        </set>
        <where>
            Id in
            <foreach collection="param2" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </where>
    </update>

</mapper>