package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品类目信息
 *
 * <AUTHOR>
 * @date 2018/9/10 17:04
 */
public interface ProductCategoryMapper {

    /**
     * 查询类目信息
     * 
     * @param id
     * @return
     */
    ProductCategoryPO selectByPrimaryKey(Long id);

    /**
     * 插入产品类目
     * 
     * @param productCategoryPO
     */
    int insertSelective(ProductCategoryPO productCategoryPO);

    /**
     * 更新产品类目
     * 
     * @param productCategoryPO
     */
    int updateByPrimaryKeySelective(ProductCategoryPO productCategoryPO);

    /**
     * 批量新增或更新产品类目信息
     * 
     * @param list
     */
    void insertOrUpdateProductCategory(@Param("list") List<ProductCategoryPO> list);


    /**
     * 根据id 查询类目信息
     *
     * @param id
     * @return
     */
    com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCategoryPO getProductCategoryById(@Param("id") Long id);

    /**
     * 根据规格id 批量查询类目信息
     *
     * @param lstSpecId
     * @return
     */
    List<ProductCategoryDTO> getProductCategoryBySpecId(@Param("list") List<Long> lstSpecId);


    List<ProductCategoryDTO> listCategoryPeriodBySkuIds(@Param("list") List<Long> skuIds);
}
