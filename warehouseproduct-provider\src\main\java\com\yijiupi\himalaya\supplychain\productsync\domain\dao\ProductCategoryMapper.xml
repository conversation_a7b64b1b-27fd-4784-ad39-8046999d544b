<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="StatisticsClass" jdbcType="BIGINT" property="statisticsClass"/>
        <result column="StatisticsClassName" jdbcType="VARCHAR" property="statisticsClassName"/>
        <result column="SecondStatisticsClass" jdbcType="BIGINT" property="secondStatisticsClass"/>
        <result column="SecondStatisticsClassName" jdbcType="VARCHAR" property="secondStatisticsClassName"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, StatisticsClass, StatisticsClassName, SecondStatisticsClass, SecondStatisticsClassName, Remark,
        CreateTime, CreateUserId, LastUpdateTime, LastUpdateUserId
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategory
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective">
        insert into productcategory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="statisticsClass != null">
                StatisticsClass,
            </if>
            <if test="statisticsClassName != null">
                StatisticsClassName,
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass,
            </if>
            <if test="secondStatisticsClassName != null">
                SecondStatisticsClassName,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            CreateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="statisticsClass != null">
                #{statisticsClass,jdbcType=BIGINT},
            </if>
            <if test="statisticsClassName != null">
                #{statisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="secondStatisticsClass != null">
                #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClassName != null">
                #{secondStatisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            NOW()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective">
        update productcategory
        <set>
            <if test="statisticsClass != null">
                StatisticsClass = #{statisticsClass,jdbcType=BIGINT},
            </if>
            <if test="statisticsClassName != null">
                StatisticsClassName = #{statisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClassName != null">
                SecondStatisticsClassName = #{secondStatisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            LastUpdateTime = NOW()
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <!--批量新增或更新产品类目信息-->
    <insert id="insertOrUpdateProductCategory">
        INSERT INTO productcategory(
        <include refid="Base_Column_List"/>
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.statisticsClass,jdbcType=BIGINT},
            #{item.statisticsClassName,jdbcType=VARCHAR},
            #{item.secondStatisticsClass,jdbcType=BIGINT},
            #{item.secondStatisticsClassName,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            NOW(),
            #{item.createUserId,jdbcType=INTEGER},
            NOW(),
            #{item.lastUpdateUserId,jdbcType=INTEGER}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        StatisticsClass = VALUES(StatisticsClass),
        StatisticsClassName = VALUES(StatisticsClassName),
        SecondStatisticsClass = VALUES(SecondStatisticsClass),
        SecondStatisticsClassName = VALUES(SecondStatisticsClassName),
        Remark = VALUES(Remark),
        LastUpdateTime = VALUES(LastUpdateTime),
        LastUpdateUserId = VALUES(LastUpdateUserId)
    </insert>

    <select id="getProductCategoryById"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCategoryPO">
        select id, RefCategory_Id as refCategoryId, Name
        from productcategorygroup
        where Id = #{id};
    </select>

    <select id="getProductCategoryBySpecId" parameterType="java.util.List"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO">
        select distinct c.ProductInfo_Id as id ,p.id as specId ,c.statisticsClass ,c.statisticsClassName ,c.secondStatisticsClass ,c.secondStatisticsClassName
        from productinfocategory c
        inner join productinfospecification p on p.ProductInfo_Id = c.ProductInfo_Id
        where p.id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listCategoryPeriodBySkuIds" parameterType="java.util.List"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO">
        select
        sku.ProductSku_Id as productSkuId,sku.Name,
        cg.Parent_Id as parentId, cg.AttentionPeriod ,cg.NearExpiryPeriod ,cg.ForbidSalesPeriod, cg.UnsalablePeriod
        from productsku sku
        inner join productinfocategory ic on sku.ProductInfoCategory_Id = ic.Id
        inner join productcategorygroup cg on cg.id = ic.StatisticsClass or cg.id = ic.SecondStatisticsClass
        where sku.ProductSku_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>