package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCategoryRelatedMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductCategoryRelatedPO record);

    int insertSelective(ProductCategoryRelatedPO record);

    ProductCategoryRelatedPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductCategoryRelatedPO record);

    int updateByPrimaryKey(ProductCategoryRelatedPO record);

    List<ProductCategoryRelatedPO> listByRefCategoryIds(@Param("categoryIds") List<String> categoryIds,
        @Param("categoryGroupId") Long categoryGroupId);

    void insertBatch(@Param("list") List<ProductCategoryRelatedPO> insertProductCategoryRelatedPOS);

    ProductCategoryRelatedPO getByRefCategoryId(@Param("refCategoryId") String refCategoryId,
        @Param("categoryGroupId") Long categoryGroupId);
}