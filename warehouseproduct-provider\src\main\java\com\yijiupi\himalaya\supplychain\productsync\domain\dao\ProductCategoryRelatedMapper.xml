<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryRelatedMapper">

    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Category_Id" jdbcType="BIGINT" property="categoryId"/>
        <result column="RefCategory_Id" jdbcType="VARCHAR" property="refCategoryId"/>
        <result column="RefCategoryName" jdbcType="VARCHAR" property="refCategoryName"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="CategoryGroup_Id" jdbcType="BIGINT" property="categoryGroupId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Category_Id, RefCategory_Id, RefCategoryName, Remark, CreateUser, CreateTime,
        LastUpdateUser, LastUpdateTime, CategoryGroup_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategoryrelated
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcategoryrelated
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO">
        insert into productcategoryrelated (Id, Category_Id, RefCategory_Id,
        RefCategoryName, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime, CategoryGroup_Id
        )
        values (#{id,jdbcType=BIGINT}, #{categoryId,jdbcType=BIGINT}, #{refCategoryId,jdbcType=VARCHAR},
        #{refCategoryName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
        now(), #{lastUpdateUser,jdbcType=VARCHAR}, now(), #{categoryGroupId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO">
        insert into productcategoryrelated
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="categoryId != null">
                Category_Id,
            </if>
            <if test="refCategoryId != null">
                RefCategory_Id,
            </if>
            <if test="refCategoryName != null">
                RefCategoryName,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="categoryId != null">
                #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="refCategoryId != null">
                #{refCategoryId,jdbcType=VARCHAR},
            </if>
            <if test="refCategoryName != null">
                #{refCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryGroupId != null">
                #{categoryGroupId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO">
        update productcategoryrelated
        <set>
            <if test="categoryId != null">
                Category_Id = #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="refCategoryId != null">
                RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR},
            </if>
            <if test="refCategoryName != null">
                RefCategoryName = #{refCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryRelatedPO">
        update productcategoryrelated
        set Category_Id = #{categoryId,jdbcType=BIGINT},
        RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR},
        RefCategoryName = #{refCategoryName,jdbcType=VARCHAR},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listByRefCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategoryrelated
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and RefCategory_Id in
        <foreach collection="categoryIds" item="refCategoryId" open="(" separator="," close=")">
            #{refCategoryId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into productcategoryrelated (Id, Category_Id, RefCategory_Id,
        RefCategoryName, Remark, CreateUser,
        CreateTime, LastUpdateUser, LastUpdateTime, CategoryGroup_Id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.categoryId,jdbcType=BIGINT}, #{item.refCategoryId,jdbcType=VARCHAR},
            #{item.refCategoryName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR},
            now(), #{item.lastUpdateUser,jdbcType=VARCHAR}, now(), #{categoryGroupId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <select id="getByRefCategoryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcategoryrelated
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and RefCategory_Id = #{refCategoryId,jdbcType=VARCHAR}
    </select>
</mapper>