/*
 * @ClassName ProductCodeInfoAuditPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-09-09 17:41:32
 */
package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoAuditPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditQueryDTO;

import java.util.List;

public interface ProductCodeInfoAuditPOMapper {
    /**
     * @Title deleteByPrimaryKey
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title insertSelective
     * @param record
     * @return int
     */
    int insertSelective(ProductCodeInfoAuditPO record);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return ProductCodeInfoAuditPO
     */
    ProductCodeInfoAuditPO selectByPrimaryKey(Long id);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(ProductCodeInfoAuditPO record);

    /**
     * 批量更新审核数据
     * 
     * @param record
     * @return
     */
    int updateAuditBatch(List<ProductCodeInfoAuditPO> recordList);

    /**
     * 分页查询审核信息
     * 
     * @param queryDTO
     * @return
     */
    PageResult<ProductCodeInfoAuditDTO> findAuditInfoByPage(ProductCodeInfoAuditQueryDTO queryDTO);

}