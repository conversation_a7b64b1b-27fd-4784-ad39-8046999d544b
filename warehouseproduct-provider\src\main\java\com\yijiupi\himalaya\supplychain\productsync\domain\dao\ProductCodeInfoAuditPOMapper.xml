<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCodeInfoAuditPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoAuditPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="ProductInfo_Id" property="productInfoId" jdbcType="BIGINT"/>
        <result column="ProductImg_Id" property="productImgId" jdbcType="VARCHAR"/>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="Reason" property="reason" jdbcType="VARCHAR"/>
        <result column="AuditUser" property="auditUser" jdbcType="VARCHAR"/>
        <result column="AuditTime" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id, ProductInfo_Id, ProductImg_Id, ProductSku_Id, ProductSpecification_Id,
        State, Remark, Reason, AuditUser, AuditTime, CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from productcodeinfoaudit
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcodeinfoaudit
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoAuditPO">
        insert into productcodeinfoaudit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productInfoId != null">
                ProductInfo_Id,
            </if>
            <if test="productImgId != null">
                ProductImg_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="reason != null">
                Reason,
            </if>
            <if test="auditUser != null">
                AuditUser,
            </if>
            <if test="auditTime != null">
                AuditTime,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productInfoId != null">
                #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="productImgId != null">
                #{productImgId,jdbcType=VARCHAR},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="auditUser != null">
                #{auditUser,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoAuditPO">
        update productcodeinfoaudit
        <set>
            <if test="cityId != null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productInfoId != null">
                ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="productImgId != null">
                ProductImg_Id = #{productImgId,jdbcType=VARCHAR},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                Reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="auditUser != null">
                AuditUser = #{auditUser,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                AuditTime = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateAuditBatch" parameterType="list">
        update productcodeinfoaudit
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="State =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.state != null ">
                        when id = #{po.id} then #{po.state}
                    </if>
                    <if test="po.state == null ">
                        when id = #{po.id} then productcodeinfoaudit.State
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.remark != null ">
                        when id = #{po.id} then #{po.remark}
                    </if>
                    <if test="po.remark == null ">
                        when id = #{po.id} then productcodeinfoaudit.Remark
                    </if>
                </foreach>
            </trim>
            <trim prefix="Reason =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.reason != null ">
                        when id = #{po.id} then #{po.reason}
                    </if>
                    <if test="po.reason == null ">
                        when id = #{po.id} then productcodeinfoaudit.Reason
                    </if>
                </foreach>
            </trim>
            <trim prefix="AuditUser =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.auditUser != null ">
                        when id = #{po.id} then #{po.auditUser}
                    </if>
                    <if test="po.auditUser == null ">
                        when id = #{po.id} then productcodeinfoaudit.AuditUser
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.lastUpdateUser != null ">
                        when id = #{po.id} then #{po.lastUpdateUser}
                    </if>
                    <if test="po.lastUpdateUser == null ">
                        when id = #{po.id} then productcodeinfoaudit.LastUpdateUser
                    </if>
                </foreach>
            </trim>
            AuditTime = now(),
            LastUpdateTime = now()
        </trim>
        where Id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id, jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findAuditInfoByPage"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditQueryDTO"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoAuditDTO">
        select
        pa.Id as id, pa.City_Id as cityId, pa.Warehouse_Id as warehouseId, pa.ProductInfo_Id as productInfoId,
        pa.ProductImg_Id as productImgId,
        pa.ProductSku_Id as productSkuId, pa.ProductSpecification_Id as productSpecificationId, pa.State as state,
        pa.Remark as remark, pa.Reason as reason,
        pa.AuditUser as auditUser, pa.AuditTime as auditTime, pa.CreateUser as createUser, pa.CreateTime as createTime,
        pa.LastUpdateUser as lastUpdateUser,
        pa.LastUpdateTime as lastUpdateTime, pi.ProductName as productName, pi.Brand as productBrand, spec.name as
        ProductSpecName, pi.StatisticsCategoryName as statisticsCategoryName,
        skuConfig.Unpackage as unPackage
        from productcodeinfoaudit pa
        INNER JOIN productinfo pi on pa.ProductInfo_Id = pi.Id
        left join productinfospecification spec on spec.ProductInfo_Id = pa.ProductInfo_Id and spec.id =
        pa.ProductSpecification_Id
        left join productskuconfig skuConfig on skuConfig.ProductSku_Id= pa.ProductSku_Id and skuConfig.Warehouse_Id =
        pa.Warehouse_Id
        <where>
            <if test="cityId != null">
                and pa.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and pa.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="state != null">
                and pa.State = #{state,jdbcType=TINYINT}
            </if>

            <if test="stateList != null and stateList.size() > 0">
                and pa.State in
                <foreach collection="stateList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>

            <if test="productName != null">
                and pi.ProductName Like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null">
                and pi.Brand Like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="statisticsCategoryName != null">
                and pi.StatisticsCategoryName Like concat(#{statisticsCategoryName,jdbcType=VARCHAR},'%')
            </if>
            AND pa.IsDelete = 0
        </where>
        order by pa.CreateTime desc
    </select>
</mapper>