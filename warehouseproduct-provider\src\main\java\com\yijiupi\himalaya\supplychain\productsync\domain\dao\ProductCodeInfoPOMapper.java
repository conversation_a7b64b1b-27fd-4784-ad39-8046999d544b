/*
 * @ClassName ProductCodeInfoPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-08-28 11:31:12
 */
package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeDeleteConditionDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeEnableStatusUpdateDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO;

import java.util.List;

public interface ProductCodeInfoPOMapper {
    /**
     * @Title deleteByPrimaryKey
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return ProductCodeInfoPO
     */
    ProductCodeInfoPO selectByPrimaryKey(Long id);

    /**
     * 根据产品信息ID查询产品所有条码和箱码
     * 
     * @param queryConditionDTO
     * @return
     */
    List<ProductCodeInfoPO> selectTotalProductCodeInfo(ProductCodeQueryConditionDTO queryConditionDTO);

    List<ProductCodeInfoPO> selectProductCodeInfoNormal(ProductCodeQueryConditionDTO queryConditionDTO);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(ProductCodeInfoPO record);

    int insertList(List<ProductCodeInfoPO> poList);

    int updateForEditList(List<ProductCodeInfoPO> poList);

    int updateIsDeleteByIds(ProductCodeDeleteConditionDTO deleteConditionDTO);

    /**
     * 根据审核ID更新code信息
     * 
     * @param deleteConditionDTO
     * @return
     */
    int updateByProductCodeInfoAuditId(ProductCodeInfoPO codeInfoPO);

    /**
     * @Title deleteByProductCodeInfoAuditId
     * @param productCodeInfoAuditId
     * @return int
     */
    int deleteByProductCodeInfoAuditId(Long productCodeInfoAuditId);

    /**
     * 更新产品Code是否启用状态
     */
    int updateProductInfoBarCodeEnableStatus(ProductCodeEnableStatusUpdateDTO enableStatusUpdateDTO);

}