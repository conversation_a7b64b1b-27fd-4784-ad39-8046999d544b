<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCodeInfoPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="ProductCodeInfoAudit_Id" property="productCodeInfoAuditId" jdbcType="BIGINT"/>
        <result column="Code" property="code" jdbcType="VARCHAR"/>
        <result column="IsCustom" property="isCustom" jdbcType="TINYINT"/>
        <result column="CodeImgId" property="codeImgId" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="CodeType" property="codeType" jdbcType="TINYINT"/>
        <result column="IsNoCode" property="isNoCode" jdbcType="TINYINT"/>
        <result column="ProductInfo_Id" property="productInfoId" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="TINYINT"/>
        <result column="Owner_Id" property="ownerId" jdbcType="VARCHAR"/>
        <result column="IsDelete" property="isDelete" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, ProductCodeInfoAudit_Id, Code, IsCustom, CodeImgId, State, CodeType, IsNoCode, ProductInfo_Id,
        ProductSpecification_Id,
        OwnerType, Owner_Id, IsDelete, CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from productcodeinfo
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectTotalProductCodeInfo" resultMap="BaseResultMap"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO">
        select
        <include refid="Base_Column_List"/>
        from productcodeinfo
        <where>
            and ProductInfo_Id in
            <foreach collection="productInfoIdList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
            and CodeType = 0
            <if test="isDelete != null">
                and IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
            <if test="ownerType != null">
                and OwnerType = #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="ownerId != null">
                and Owner_Id = #{ownerId,jdbcType=VARCHAR}
            </if>
            <if test="isNoCode != null">
                and IsNoCode = #{isNoCode,jdbcType=TINYINT}
            </if>
        </where>
        union all
        select
        <include refid="Base_Column_List"/>
        from productcodeinfo
        <where>
            and ProductInfo_Id in
            <foreach collection="productInfoIdList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
            and CodeType = 1
            <if test="isDelete != null">
                and IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
            <if test="ownerType != null">
                and OwnerType = #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="ownerId != null">
                and Owner_Id = #{ownerId,jdbcType=VARCHAR}
            </if>
            <if test="isNoCode != null">
                and IsNoCode = #{isNoCode,jdbcType=TINYINT}
            </if>
        </where>
        union all
        select
        <include refid="Base_Column_List"/>
        from productcodeinfo
        <where>
            and ProductSpecification_Id in (
            select id from productinfospecification where ProductInfo_Id in
            <foreach collection="productInfoIdList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
            )
            and CodeType = 1
            <if test="isDelete != null">
                and IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
            <if test="ownerType != null">
                and OwnerType = #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="ownerId != null">
                and Owner_Id = #{ownerId,jdbcType=VARCHAR}
            </if>
            <if test="isNoCode != null">
                and IsNoCode = #{isNoCode,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="selectProductCodeInfoNormal" resultMap="BaseResultMap"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeQueryConditionDTO">
        select
        <include refid="Base_Column_List"/>
        from productcodeinfo
        <where>
            <if test="productInfoIdList != null">
                and ProductInfo_Id in
                <foreach collection="productInfoIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productSpecificationIdList != null">
                and ProductSpecification_Id in
                <foreach collection="productSpecificationIdList" index="index" item="item" separator="," open="("
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productCodeInfoAuditIdList != null">
                and ProductCodeInfoAudit_Id in
                <foreach collection="productCodeInfoAuditIdList" index="index" item="item" separator="," open="("
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="code != null">
                and Code LIKE concat(#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="codes != null">
                and Code IN
                <foreach collection="codes" index="index" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="codeType != null">
                and CodeType = #{codeType,jdbcType=TINYINT}
            </if>
            <if test="isDelete != null">
                and IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
            <if test="ownerType != null">
                and OwnerType = #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="ownerId != null">
                and Owner_Id = #{ownerId,jdbcType=VARCHAR}
            </if>
            <if test="isNoCode != null">
                and IsNoCode = #{isNoCode,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcodeinfo
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCodeInfoPO">
        update productcodeinfo
        <set>
            <if test="productCodeInfoAuditId != null">
                ProductCodeInfoAudit_Id = #{productCodeInfoAuditId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                Code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="isCustom != null">
                IsCustom = #{isCustom,jdbcType=TINYINT},
            </if>
            <if test="codeImgId != null">
                CodeImgId = #{codeImgId,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="codeType != null">
                CodeType = #{codeType,jdbcType=TINYINT},
            </if>
            <if test="isNoCode != null">
                IsNoCode = #{isNoCode,jdbcType=TINYINT},
            </if>
            <if test="productInfoId != null">
                ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null">
                OwnerType = #{ownerType,jdbcType=TINYINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IsDelete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertList">
        insert into productcodeinfo (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="list" item="po" index="index" separator=",">
            (
            <if test="po.id != null">
                #{po.id,jdbcType=BIGINT},
            </if>
            #{po.productCodeInfoAuditId,jdbcType=BIGINT},
            #{po.code,jdbcType=VARCHAR},
            <if test="po.isCustom == null">
                0,
            </if>
            <if test="po.isCustom != null">
                #{po.isCustom,jdbcType=TINYINT},
            </if>
            #{po.codeImgId,jdbcType=VARCHAR},
            <if test="po.state == null">
                0,
            </if>
            <if test="po.state != null">
                #{po.state,jdbcType=TINYINT},
            </if>
            #{po.codeType,jdbcType=TINYINT},
            #{po.isNoCode,jdbcType=TINYINT},
            #{po.productInfoId,jdbcType=BIGINT},
            #{po.productSpecificationId,jdbcType=BIGINT},
            <if test="po.ownerType == null">
                0,
            </if>
            <if test="po.ownerType != null">
                #{po.ownerType,jdbcType=TINYINT},
            </if>
            #{po.ownerId,jdbcType=VARCHAR},
            <if test="po.isDelete == null">
                0,
            </if>
            <if test="po.isDelete != null">
                #{po.isDelete,jdbcType=TINYINT},
            </if>
            #{po.createUser,jdbcType=VARCHAR},
            <if test="po.createTime != null">
                #{po.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="po.createTime == null">
                now(),
            </if>
            #{po.lastUpdateUser,jdbcType=VARCHAR},
            <if test="po.lastUpdateTime == null">
                now()
            </if>
            <if test="po.lastUpdateTime != null">
                #{po.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            )
        </foreach>
    </insert>

    <update id="updateForEditList" parameterType="java.util.List">
        update productcodeinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="IsCustom =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isCustom != null ">
                        when id=#{item.id} then #{item.isCustom}
                    </if>
                    <if test="item.isCustom == null ">
                        when id=#{item.id} then productcodeinfo.IsCustom
                    </if>
                </foreach>
            </trim>
            <trim prefix="CodeImgId =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.codeImgId != null ">
                        when id=#{item.id} then #{item.codeImgId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="State =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.state != null ">
                        when id=#{item.id} then #{item.state}
                    </if>
                    <if test="item.state == null ">
                        when id=#{item.id} then productcodeinfo.State
                    </if>
                </foreach>
            </trim>
            <trim prefix="CodeType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.codeType != null ">
                        when id=#{item.id} then #{item.codeType}
                    </if>
                    <if test="item.codeType == null ">
                        when id=#{item.id} then productcodeinfo.CodeType
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsNoCode =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isNoCode != null ">
                        when id=#{item.id} then #{item.isNoCode}
                    </if>
                    <if test="item.isNoCode == null ">
                        when id=#{item.id} then productcodeinfo.IsNoCode
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductInfo_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.productInfoId != null ">
                        when id=#{item.id} then #{item.productInfoId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductSpecification_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.productSpecificationId != null ">
                        when id=#{item.id} then #{item.productSpecificationId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="OwnerType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.ownerType != null ">
                        when id=#{item.id} then #{item.ownerType}
                    </if>
                    <if test="item.ownerType == null ">
                        when id=#{item.id} then productcodeinfo.OwnerType
                    </if>
                </foreach>
            </trim>
            <trim prefix="Owner_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.ownerId != null ">
                        when id=#{item.id} then #{item.ownerId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsDelete =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isDelete != null ">
                        when id=#{item.id} then #{item.isDelete}
                    </if>
                    <if test="item.isDelete == null ">
                        when id=#{item.id} then productcodeinfo.IsDelete
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.lastUpdateUser != null ">
                        when id=#{item.id} then #{item.lastUpdateUser}
                    </if>
                    <if test="item.lastUpdateUser == null ">
                        when id=#{item.id} then productcodeinfo.LastUpdateUser
                    </if>
                </foreach>
            </trim>
            LastUpdateTime = now()
        </trim>
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateIsDeleteByIds"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeDeleteConditionDTO">
        update productcodeinfo
        <set>
            IsDelete = #{isDelete,jdbcType=TINYINT},
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            LastUpdateTime = now()
        </set>
        where id in
        <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateByProductCodeInfoAuditId">
        update productcodeinfo
        <set>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where ProductCodeInfoAudit_Id = #{productCodeInfoAuditId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByProductCodeInfoAuditId" parameterType="java.lang.Long">
        delete from productcodeinfo
        where ProductCodeInfoAudit_Id = #{productCodeInfoAuditId,jdbcType=BIGINT}
    </delete>

    <update id="updateProductInfoBarCodeEnableStatus"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeEnableStatusUpdateDTO">
        UPDATE productcodeinfo
        <set>
            IsDelete = #{isDelete,jdbcType=TINYINT},
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Code IN
        <foreach collection="codeList" item="code" open="(" close=")" separator=",">
            #{code,jdbcType=VARCHAR}
        </foreach>
        AND CodeType = #{codeType,jdbcType=TINYINT}
        <if test="productInfoId != null">
            AND ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        <if test="productSpecificationId != null">
            AND ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        </if>
    </update>

</mapper>