package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductInfoCategoryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductInfoCategoryPO record);

    int insertSelective(ProductInfoCategoryPO record);

    ProductInfoCategoryPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductInfoCategoryPO record);

    int updateByPrimaryKey(ProductInfoCategoryPO record);

    void insertOrUpdateBatch(@Param("list") List<ProductInfoCategoryPO> productInfoCategoryPOS);

    ProductInfoCategoryPO getByInfoId(@Param("productInfoId") Long productInfoId,
        @Param("categoryGroupId") Long categoryGroupId);

    List<ProductInfoCategoryPO> listByProductInfoIds(@Param("productInfoIds") List<Long> productInfoIds,
        @Param("categoryGroupId") Long categoryGroupId);

    void updateCategoryName(@Param("update") ProductInfoCategoryPO productInfoCategoryPO);

    List<ProductInfoCategoryPO> listByCategoryIds(@Param("categoryIds") List<Long> categoryIds,
        @Param("categoryGroupId") Long categoryGroupId);

    List<ProductInfoCategoryPO> listByParentCategoryIds(@Param("categoryIds") List<Long> categoryIds,
        @Param("categoryGroupId") Long categoryGroupId);

    PageResult<ProductInfoCategoryDTO>
        pageListProductInfoCategory(@Param("query") ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO);

    List<ProductInfoCategoryDTO>
        findProductCategoryBySkuIds(@Param("query") ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO);

    List<ProductInfoCategoryPO>
        findProductCategoryByIds(@Param("productInfoCategoryIds") List<Long> productInfoCategoryIds);

    List<ProductInfoCategoryDTO>
        findProductCategoryBySpec(@Param("queries") List<ProductCategoryQueryDTO> productCategoryQueryDTOS);

    void batchUpdateCategoryName(@Param("list") List<ProductCategoryGroupPO> productCategoryGroupPOS,
        @Param("categoryGroupId") Long categoryGroupId);
}