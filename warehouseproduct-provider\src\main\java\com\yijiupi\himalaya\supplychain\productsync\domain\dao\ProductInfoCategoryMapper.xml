<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoCategoryMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="StatisticsClass" jdbcType="BIGINT" property="statisticsClass"/>
        <result column="StatisticsClassName" jdbcType="VARCHAR" property="statisticsClassName"/>
        <result column="SecondStatisticsClass" jdbcType="BIGINT" property="secondStatisticsClass"/>
        <result column="SecondStatisticsClassName" jdbcType="VARCHAR" property="secondStatisticsClassName"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="CategoryGroup_Id" jdbcType="BIGINT" property="categoryGroupId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, ProductInfo_Id, StatisticsClass, StatisticsClassName, SecondStatisticsClass,
        SecondStatisticsClassName, CreateTime, LastUpdateTime, CategoryGroup_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productinfocategory
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO">
        insert into productinfocategory (Id, ProductInfo_Id, StatisticsClass,
        StatisticsClassName, SecondStatisticsClass,
        SecondStatisticsClassName, CreateTime,
        LastUpdateTime, CategoryGroup_Id)
        values (#{id,jdbcType=BIGINT}, #{productInfoId,jdbcType=BIGINT}, #{statisticsClass,jdbcType=BIGINT},
        #{statisticsClassName,jdbcType=VARCHAR}, #{secondStatisticsClass,jdbcType=BIGINT},
        #{secondStatisticsClassName,jdbcType=VARCHAR}, now(),
        now(), #{categoryGroupId,jdbcType=BIGINT})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO">
        insert into productinfocategory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="productInfoId != null">
                ProductInfo_Id,
            </if>
            <if test="statisticsClass != null">
                StatisticsClass,
            </if>
            <if test="statisticsClassName != null">
                StatisticsClassName,
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass,
            </if>
            <if test="secondStatisticsClassName != null">
                SecondStatisticsClassName,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="productInfoId != null">
                #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="statisticsClass != null">
                #{statisticsClass,jdbcType=BIGINT},
            </if>
            <if test="statisticsClassName != null">
                #{statisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="secondStatisticsClass != null">
                #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClassName != null">
                #{secondStatisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryGroupId != null">
                #{categoryGroupId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO">
        update productinfocategory
        <set>
            <if test="productInfoId != null">
                ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="statisticsClass != null">
                StatisticsClass = #{statisticsClass,jdbcType=BIGINT},
            </if>
            <if test="statisticsClassName != null">
                StatisticsClassName = #{statisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClassName != null">
                SecondStatisticsClassName = #{secondStatisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryGroupId != null">
                CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoCategoryPO">
        update productinfocategory
        set ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
        StatisticsClass = #{statisticsClass,jdbcType=BIGINT},
        StatisticsClassName = #{statisticsClassName,jdbcType=VARCHAR},
        SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT},
        SecondStatisticsClassName = #{secondStatisticsClassName,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        where Id = #{id,jdbcType=BIGINT}
    </update>


    <insert id="insertOrUpdateBatch">
        insert into productinfocategory (Id, ProductInfo_Id, StatisticsClass,
        StatisticsClassName, SecondStatisticsClass,
        SecondStatisticsClassName, CreateTime,
        LastUpdateTime, CategoryGroup_Id)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.productInfoId,jdbcType=BIGINT}, #{item.statisticsClass,jdbcType=BIGINT},
            #{item.statisticsClassName,jdbcType=VARCHAR}, #{item.secondStatisticsClass,jdbcType=BIGINT},
            #{item.secondStatisticsClassName,jdbcType=VARCHAR}, now(),
            now(), #{item.categoryGroupId,jdbcType=BIGINT}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        StatisticsClass=VALUES(StatisticsClass),
        StatisticsClassName=VALUES(StatisticsClassName),
        SecondStatisticsClass=VALUES(SecondStatisticsClass),
        SecondStatisticsClassName=VALUES(SecondStatisticsClassName)
    </insert>

    <select id="getByInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        and CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="listByProductInfoIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and ProductInfo_Id in
        <foreach collection="productInfoIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateCategoryName">
        update productinfocategory
        <set>
            <if test="update.statisticsClassName != null">
                StatisticsClassName = #{update.statisticsClassName,jdbcType=VARCHAR},
            </if>
            <if test="update.secondStatisticsClassName != null">
                SecondStatisticsClassName = #{update.secondStatisticsClassName,jdbcType=VARCHAR}
            </if>
        </set>
        where CategoryGroup_Id = #{update.categoryGroupId,jdbcType=BIGINT}
        <if test="update.statisticsClass != null">
            and StatisticsClass = #{update.statisticsClass,jdbcType=BIGINT}
        </if>
        <if test="update.secondStatisticsClass != null">
            and SecondStatisticsClass = #{update.secondStatisticsClass,jdbcType=BIGINT}
        </if>
    </update>

    <select id="listByCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and SecondStatisticsClass in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listByParentCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and StatisticsClass in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="pageListProductInfoCategory"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO">
        select
        Id, ProductInfo_Id as productInfoId, StatisticsClass, StatisticsClassName, SecondStatisticsClass,
        SecondStatisticsClassName, CreateTime, LastUpdateTime, CategoryGroup_Id as categoryGroupId
        from productinfocategory
        where CategoryGroup_Id = #{query.categoryGroupId,jdbcType=BIGINT}
        <if test="query.productInfoIds != null and query.productInfoIds.size() > 0">
            and ProductInfo_Id in
            <foreach collection="query.productInfoIds" item="productInfoId" open="(" separator="," close=")">
                #{productInfoId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findProductCategoryBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO">
        select
        pic.Id, pic.ProductInfo_Id as productInfoId, pic.StatisticsClass, pic.StatisticsClassName,
        pic.SecondStatisticsClass,
        pic.SecondStatisticsClassName, pic.CreateTime, pic.LastUpdateTime, pic.CategoryGroup_Id as categoryGroupId,
        ps.ProductSku_Id as productSkuId, ps.City_Id as orgId, ps.ProductSpecification_Id as productSpecificationId,
        ps.Company_Id as ownerId
        from productinfocategory pic
        inner join productsku ps on pic.Id = ps.ProductInfoCategory_Id
        where ps.ProductSku_Id in
        <foreach collection="query.skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findProductCategoryByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfocategory
        where
        Id in
        <foreach collection="productInfoCategoryIds" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findProductCategoryBySpec"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO">
        select
        pic.Id, pic.ProductInfo_Id as productInfoId, pic.StatisticsClass, pic.StatisticsClassName,
        pic.SecondStatisticsClass,
        pic.SecondStatisticsClassName, pic.CreateTime, pic.LastUpdateTime, pic.CategoryGroup_Id as categoryGroupId,
        ps.ProductSku_Id as productSkuId, ps.City_Id as orgId, ps.ProductSpecification_Id as productSpecificationId,
        ps.Company_Id as ownerId
        from productinfocategory pic
        inner join productsku ps on pic.Id = ps.ProductInfoCategory_Id
        where
        <foreach collection="queries" item="query" open="(" close=")" separator="or">
            (
            ps.ProductSpecification_Id = #{query.productSpecificationId,jdbcType=BIGINT}
            <if test="query.ownerId == null">and ps.Company_Id is null</if>
            <if test="query.ownerId != null">and ps.Company_Id = #{query.ownerId,jdbcType=BIGINT}</if>
            <if test="query.orgId != null">
                and ps.City_Id = #{query.orgId,jdbcType=INTEGER}
            </if>
            )
        </foreach>
    </select>

    <update id="batchUpdateCategoryName">
        update productinfocategory
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="StatisticsClassName = case" suffix="end,">
                <foreach collection="list" item="update" index="index">
                    when StatisticsClass = #{update.id,jdbcType=BIGINT} then #{update.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SecondStatisticsClassName = case" suffix="end,">
                <foreach collection="list" item="update" index="index">
                    when SecondStatisticsClass = #{update.id,jdbcType=BIGINT} then #{update.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
        and
        (
        StatisticsClass in
        <foreach collection="list" item="update" open="(" separator="," close=")">
            #{update.id,jdbcType=BIGINT}
        </foreach>
        or
        SecondStatisticsClass in
        <foreach collection="list" item="update" open="(" separator="," close=")">
            #{update.id,jdbcType=BIGINT}
        </foreach>
        )
    </update>
</mapper>