package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductInfoPOMapper {

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productinfo
     * 
     * @mbg.generated Thu Dec 06 16:13:17 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productinfo
     * 
     * @mbg.generated Thu Dec 06 16:13:17 CST 2018
     */
    int insert(ProductInfoPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productinfo
     * 
     * @mbg.generated Thu Dec 06 16:13:17 CST 2018
     */
    int insertSelective(ProductInfoPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productinfo
     * 
     * @mbg.generated Thu Dec 06 16:13:17 CST 2018
     */
    ProductInfoPO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productinfo
     * 
     * @mbg.generated Thu Dec 06 16:13:17 CST 2018
     */
    int updateByPrimaryKeySelective(ProductInfoPO record);

    /**
     * 批量新增或修改产品信息
     * 
     * @return
     */
    int insertOrUpdateBatch(@Param("list") List<ProductInfoPO> list);

    /**
     * 产品信息列表
     * 
     * @return
     */
    PageResult<ProductInfoPO> listProductInfo(ProductInfoSO productInfoSO);

    /**
     * 统计产品信息
     * 
     * @param record
     * @return
     */
    Integer countProductInfo(ProductInfoPO record);

    /**
     * 根据主键查询产品信息
     * 
     * @param idList
     * @return
     */
    List<ProductInfoPO> selectProductByIdList(List<Long> idList);

    /**
     * 根据SkuId查询产品信息
     *
     * @param lstSkuId
     * @return
     */
    List<ProductInfoPO> selectProductBySkuIdList(List<Long> lstSkuId);

    /**
     * 查询城市未同步图片的产品DefaultImageFileId
     * 
     * @param cityId
     * @return
     */
    List<Integer> getDefaultImageFileId(@Param("cityId") String cityId);

    /**
     * 批量新增产品信息
     * 
     * @return
     */
    int insertBatch(@Param("list") List<ProductInfoPO> list);

}