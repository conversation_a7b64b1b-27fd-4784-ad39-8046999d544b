<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="StatisticsCategoryName" jdbcType="VARCHAR" property="statisticsCategoryName"/>
        <result column="SeriesName" jdbcType="VARCHAR" property="seriesName"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="GeneralName" jdbcType="VARCHAR" property="generalName"/>
        <result column="DefaultImageFile_Id" jdbcType="VARCHAR" property="defaultImageFile_Id"/>
        <result column="StorageMethod" jdbcType="VARCHAR" property="storageMethod"/>
        <result column="OriginalPlace" jdbcType="VARCHAR" property="originalPlace"/>
        <result column="Status" jdbcType="TINYINT" property="status"/>
        <result column="BottleCode" jdbcType="VARCHAR" property="bottleCode"/>
        <result column="ProductCode" jdbcType="VARCHAR" property="productCode"/>
        <result column="packagingCode" jdbcType="VARCHAR" property="packagingCode"/>
        <result column="ProductInfoType" jdbcType="TINYINT" property="productInfoType"/>
        <result column="ProductStatisticsClass" jdbcType="BIGINT" property="productStatisticsClass"/>
        <result column="SecondStatisticsClass" jdbcType="BIGINT" property="secondStatisticsClass"/>
        <result column="MonthOfShelfLife" jdbcType="INTEGER" property="monthOfShelfLife"/>
        <result column="ShelfLifeUnit" jdbcType="TINYINT" property="shelfLifeUnit"/>
        <result column="hasBottleCode" jdbcType="TINYINT" property="hasBottleCode"/>
        <result column="hasBoxCode" jdbcType="TINYINT" property="hasBoxCode"/>
        <result column="ShopId" jdbcType="BIGINT" property="shopId"/>
        <result column="ShelfLifeLongTime" jdbcType="TINYINT" property="shelfLifeLongTime"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUser_Id"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUser_Id"/>
        <result column="PackageType" jdbcType="TINYINT" property="packageType"/>
        <result column="IsProcess" jdbcType="TINYINT" property="process"/>
        <result column="StorageType" jdbcType="TINYINT" property="storageType"/>
        <result column="ParentOrg_Id" jdbcType="INTEGER" property="parentOrgId"/>
        <result column="DefaultCostPrice" jdbcType="DECIMAL" property="defaultCostPrice"/>
        <result column="IsBOM" jdbcType="TINYINT" property="isBOM"/>
        <result column="PolicyId" jdbcType="BIGINT" property="policyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        Id, Brand, StatisticsCategoryName, SeriesName, ProductName, GeneralName, DefaultImageFile_Id,
        StorageMethod, OriginalPlace, Status, BottleCode, ProductCode, packagingCode, ProductInfoType,
        ProductStatisticsClass, SecondStatisticsClass, MonthOfShelfLife, ShelfLifeUnit, hasBottleCode,
        hasBoxCode, ShopId, ShelfLifeLongTime, CreateTime, CreateUser_Id, LastUpdateTime,
        LastUpdateUser_Id, PackageType, IsProcess, StorageType, ParentOrg_Id,DefaultCostPrice,IsBOM,PolicyId
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        select
        <include refid="Base_Column_List"/>
        from productinfo
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        delete from productinfo
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        insert into productinfo (Id, Brand, StatisticsCategoryName,
        SeriesName, ProductName, GeneralName,
        DefaultImageFile_Id, StorageMethod, OriginalPlace,
        Status, BottleCode, ProductCode,
        packagingCode, ProductInfoType, ProductStatisticsClass,
        SecondStatisticsClass, MonthOfShelfLife,
        ShelfLifeUnit, hasBottleCode, hasBoxCode,
        ShopId, ShelfLifeLongTime, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id,
        PackageType, IsProcess, StorageType, ParentOrg_Id,DefaultCostPrice,IsBOM)
        values (#{id,jdbcType=BIGINT}, #{brand,jdbcType=VARCHAR}, #{statisticsCategoryName,jdbcType=VARCHAR},
        #{seriesName,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{generalName,jdbcType=VARCHAR},
        #{defaultImageFile_Id,jdbcType=VARCHAR}, #{storageMethod,jdbcType=VARCHAR}, #{originalPlace,jdbcType=VARCHAR},
        #{status,jdbcType=TINYINT}, #{bottleCode,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR},
        #{packagingCode,jdbcType=VARCHAR}, #{productInfoType,jdbcType=TINYINT},
        #{productStatisticsClass,jdbcType=BIGINT},
        #{secondStatisticsClass,jdbcType=BIGINT}, #{monthOfShelfLife,jdbcType=INTEGER},
        #{shelfLifeUnit,jdbcType=TINYINT}, #{hasBottleCode,jdbcType=TINYINT}, #{hasBoxCode,jdbcType=TINYINT},
        #{shopId,jdbcType=BIGINT}, #{shelfLifeLongTime,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
        #{createUser_Id,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser_Id,jdbcType=INTEGER},
        #{packageType,jdbcType=TINYINT}, #{process,jdbcType=TINYINT}, #{storageType,jdbcType=TINYINT},
        #{parentOrgId,jdbcType=INTEGER},
        #{defaultCostPrice,jdbcType=DECIMAL},#{isBOM,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        insert into productinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="brand != null">
                Brand,
            </if>
            <if test="statisticsCategoryName != null">
                StatisticsCategoryName,
            </if>
            <if test="seriesName != null">
                SeriesName,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="generalName != null">
                GeneralName,
            </if>
            <if test="defaultImageFile_Id != null">
                DefaultImageFile_Id,
            </if>
            <if test="storageMethod != null">
                StorageMethod,
            </if>
            <if test="originalPlace != null">
                OriginalPlace,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="bottleCode != null">
                BottleCode,
            </if>
            <if test="productCode != null">
                ProductCode,
            </if>
            <if test="packagingCode != null">
                packagingCode,
            </if>
            <if test="productInfoType != null">
                ProductInfoType,
            </if>
            <if test="productStatisticsClass != null">
                ProductStatisticsClass,
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass,
            </if>
            <if test="monthOfShelfLife != null">
                MonthOfShelfLife,
            </if>
            <if test="shelfLifeUnit != null">
                ShelfLifeUnit,
            </if>
            <if test="hasBottleCode != null">
                hasBottleCode,
            </if>
            <if test="hasBoxCode != null">
                hasBoxCode,
            </if>
            <if test="shopId != null">
                ShopId,
            </if>
            <if test="shelfLifeLongTime != null">
                ShelfLifeLongTime,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser_Id != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser_Id != null">
                LastUpdateUser_Id,
            </if>
            <if test="packageType != null">
                PackageType,
            </if>
            <if test="process != null">
                IsProcess,
            </if>
            <if test="storageType != null">
                StorageType,
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id,
            </if>
            <if test="defaultCostPrice != null">
                DefaultCostPrice,
            </if>
            <if test="isBOM != null">
                IsBOM,
            </if>
            <if test="policyId != null">
                PolicyId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="statisticsCategoryName != null">
                #{statisticsCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="seriesName != null">
                #{seriesName,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="generalName != null">
                #{generalName,jdbcType=VARCHAR},
            </if>
            <if test="defaultImageFile_Id != null">
                #{defaultImageFile_Id,jdbcType=VARCHAR},
            </if>
            <if test="storageMethod != null">
                #{storageMethod,jdbcType=VARCHAR},
            </if>
            <if test="originalPlace != null">
                #{originalPlace,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="bottleCode != null">
                #{bottleCode,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="packagingCode != null">
                #{packagingCode,jdbcType=VARCHAR},
            </if>
            <if test="productInfoType != null">
                #{productInfoType,jdbcType=TINYINT},
            </if>
            <if test="productStatisticsClass != null">
                #{productStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClass != null">
                #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="monthOfShelfLife != null">
                #{monthOfShelfLife,jdbcType=INTEGER},
            </if>
            <if test="shelfLifeUnit != null">
                #{shelfLifeUnit,jdbcType=TINYINT},
            </if>
            <if test="hasBottleCode != null">
                #{hasBottleCode,jdbcType=TINYINT},
            </if>
            <if test="hasBoxCode != null">
                #{hasBoxCode,jdbcType=TINYINT},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="shelfLifeLongTime != null">
                #{shelfLifeLongTime,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser_Id != null">
                #{createUser_Id,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser_Id != null">
                #{lastUpdateUser_Id,jdbcType=INTEGER},
            </if>
            <if test="packageType != null">
                #{packageType,jdbcType=TINYINT},
            </if>
            <if test="process != null">
                #{process,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                #{storageType,jdbcType=TINYINT},
            </if>
            <if test="parentOrgId != null">
                #{parentOrgId,jdbcType=INTEGER},
            </if>
            <if test="defaultCostPrice != null">
                #{defaultCostPrice,jdbcType=DECIMAL},
            </if>
            <if test="isBOM != null">
                #{isBOM,jdbcType=DECIMAL},
            </if>
            <if test="policyId != null">
                #{policyId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Dec 06 16:13:17 CST 2018.
        -->
        update productinfo
        <set>
            <if test="brand != null">
                Brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="statisticsCategoryName != null">
                StatisticsCategoryName = #{statisticsCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="seriesName != null">
                SeriesName = #{seriesName,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="generalName != null">
                GeneralName = #{generalName,jdbcType=VARCHAR},
            </if>
            <if test="defaultImageFile_Id != null">
                DefaultImageFile_Id = #{defaultImageFile_Id,jdbcType=VARCHAR},
            </if>
            <if test="storageMethod != null">
                StorageMethod = #{storageMethod,jdbcType=VARCHAR},
            </if>
            <if test="originalPlace != null">
                OriginalPlace = #{originalPlace,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="bottleCode != null">
                BottleCode = #{bottleCode,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                ProductCode = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="packagingCode != null">
                packagingCode = #{packagingCode,jdbcType=VARCHAR},
            </if>
            <if test="productInfoType != null">
                ProductInfoType = #{productInfoType,jdbcType=TINYINT},
            </if>
            <if test="productStatisticsClass != null">
                ProductStatisticsClass = #{productStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="secondStatisticsClass != null">
                SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT},
            </if>
            <if test="monthOfShelfLife != null">
                MonthOfShelfLife = #{monthOfShelfLife,jdbcType=INTEGER},
            </if>
            <if test="shelfLifeUnit != null">
                ShelfLifeUnit = #{shelfLifeUnit,jdbcType=TINYINT},
            </if>
            <if test="hasBottleCode != null">
                hasBottleCode = #{hasBottleCode,jdbcType=TINYINT},
            </if>
            <if test="hasBoxCode != null">
                hasBoxCode = #{hasBoxCode,jdbcType=TINYINT},
            </if>
            <if test="shopId != null">
                ShopId = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="shelfLifeLongTime != null">
                ShelfLifeLongTime = #{shelfLifeLongTime,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser_Id != null">
                LastUpdateUser_Id = #{lastUpdateUser_Id,jdbcType=INTEGER},
            </if>
            <if test="packageType!=null">
                PackageType = #{packageType,jdbcType=TINYINT},
            </if>
            <if test="process != null">
                IsProcess = #{process,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                StorageType = #{storageType,jdbcType=TINYINT},
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER},
            </if>
            <if test="defaultCostPrice != null">
                DefaultCostPrice = #{defaultCostPrice,jdbcType=DECIMAL},
            </if>
            <if test="isBOM != null">
                IsBOM = #{isBOM,jdbcType=DECIMAL},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        insert into productinfo
        (
        Id,
        Brand,
        StatisticsCategoryName,
        SeriesName,
        ProductName,
        GeneralName,
        DefaultImageFile_Id,
        StorageMethod,
        OriginalPlace,
        Status,
        BottleCode,
        ProductCode,
        packagingCode,
        ProductInfoType,
        ProductStatisticsClass,
        SecondStatisticsClass,
        MonthOfShelfLife,
        ShelfLifeUnit,
        hasBottleCode,
        hasBoxCode,
        ShopId,
        ShelfLifeLongTime,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id,
        PackageType,
        IsProcess,
        DefaultCostPrice,
        IsBOM
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.brand,jdbcType=VARCHAR},
            #{item.statisticsCategoryName,jdbcType=VARCHAR},
            #{item.seriesName,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.generalName,jdbcType=VARCHAR},
            #{item.defaultImageFile_Id,jdbcType=VARCHAR},
            #{item.storageMethod,jdbcType=VARCHAR},
            #{item.originalPlace,jdbcType=VARCHAR},
            #{item.status,jdbcType=TINYINT},
            #{item.bottleCode,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.packagingCode,jdbcType=VARCHAR},
            #{item.productInfoType,jdbcType=TINYINT},
            #{item.productStatisticsClass,jdbcType=BIGINT},
            #{item.secondStatisticsClass,jdbcType=BIGINT},
            #{item.monthOfShelfLife,jdbcType=INTEGER},
            #{item.shelfLifeUnit,jdbcType=TINYINT},
            #{item.hasBottleCode,jdbcType=TINYINT},
            #{item.hasBoxCode,jdbcType=TINYINT},
            #{item.shopId,jdbcType=BIGINT},
            #{item.shelfLifeLongTime,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser_Id,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser_Id,jdbcType=INTEGER},
            #{item.packageType,jdbcType=TINYINT},
            #{item.process,jdbcType=TINYINT},
            #{item.defaultCostPrice,jdbcType=DECIMAL},
            #{item.isBOM,jdbcType=TINYINT}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        Brand = IF(VALUES(Brand) IS NOT NULL, VALUES(Brand), Brand),
        StatisticsCategoryName = IF(VALUES(StatisticsCategoryName) IS NOT NULL, VALUES(StatisticsCategoryName), StatisticsCategoryName),
        SeriesName = IF(VALUES(SeriesName) IS NOT NULL, VALUES(SeriesName), SeriesName),
        ProductName = IF(VALUES(ProductName) IS NOT NULL, VALUES(ProductName), ProductName),
        GeneralName = IF(VALUES(GeneralName) IS NOT NULL, VALUES(GeneralName), GeneralName),
        DefaultImageFile_Id = IF(VALUES(DefaultImageFile_Id) IS NOT NULL, VALUES(DefaultImageFile_Id), DefaultImageFile_Id),
        StorageMethod = IF(VALUES(StorageMethod) IS NOT NULL, VALUES(StorageMethod), StorageMethod),
        OriginalPlace = IF(VALUES(OriginalPlace) IS NOT NULL, VALUES(OriginalPlace), OriginalPlace),
        Status = IF(VALUES(Status) IS NOT NULL, VALUES(Status), Status),
        BottleCode = IF(VALUES(BottleCode) IS NOT NULL, VALUES(BottleCode), BottleCode),
        ProductCode = IF(VALUES(ProductCode) IS NOT NULL, VALUES(ProductCode), ProductCode),
        packagingCode = IF(VALUES(packagingCode) IS NOT NULL, VALUES(packagingCode), packagingCode),
        ProductInfoType = IF(VALUES(ProductInfoType) IS NOT NULL, VALUES(ProductInfoType), ProductInfoType),
        ProductStatisticsClass = IF(VALUES(ProductStatisticsClass) IS NOT NULL, VALUES(ProductStatisticsClass), ProductStatisticsClass),
        SecondStatisticsClass = IF(VALUES(SecondStatisticsClass) IS NOT NULL, VALUES(SecondStatisticsClass), SecondStatisticsClass),
        MonthOfShelfLife = IF(VALUES(MonthOfShelfLife) IS NOT NULL, VALUES(MonthOfShelfLife), MonthOfShelfLife),
        ShelfLifeUnit = IF(VALUES(ShelfLifeUnit) IS NOT NULL, VALUES(ShelfLifeUnit), ShelfLifeUnit),
        ShelfLifeLongTime = IF(VALUES(ShelfLifeLongTime) IS NOT NULL, VALUES(ShelfLifeLongTime), ShelfLifeLongTime),
        LastUpdateUser_Id = IF(VALUES(LastUpdateUser_Id) IS NOT NULL, VALUES(LastUpdateUser_Id), LastUpdateUser_Id),
        PackageType = IF(VALUES(PackageType) IS NOT NULL, VALUES(PackageType), PackageType)
    </insert>

    <select id="listProductInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfo
        <where>
            <if test="parentOrgId != null">
                ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER}
            </if>
            <if test="productCode != null">
                and ProductCode like concat('%',#{productCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="productName != null">
                and ProductName like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productFullName != null and productFullName != ''">
                and ProductName = #{productFullName,jdbcType=VARCHAR}
            </if>
            <if test="productStatisticsClass != null">
                and ProductStatisticsClass = #{productStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass != null">
                and SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="productInfoIdList != null and productInfoIdList.size() > 0">
                and Id in
                <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="process != null">
                and IsProcess = #{process,jdbcType=BIGINT}
            </if>
            <if test="isBOM != null">
                and IsBOM = #{isBOM,jdbcType=BIGINT}
            </if>
            <if test="statisticsClassName != null and statisticsClassName != '' and (queryAwardFlag == null or queryAwardFlag == false)">
                and PolicyId is null
                and (StatisticsCategoryName is null or StatisticsCategoryName not like
                concat('%',#{statisticsClassName,jdbcType=VARCHAR},'%'))
            </if>
        </where>
        order by CreateTime desc, Id
    </select>

    <select id="countProductInfo" resultType="java.lang.Integer"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO">
        select count(*)
        from productinfo
        where ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER}
          and ProductCode = #{productCode,jdbcType=VARCHAR}
    </select>

    <select id="selectProductByIdList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfo
        where Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectProductBySkuIdList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select distinct info.Id, info.Brand, ifnull(p.StatisticsClassName,info.StatisticsCategoryName) as StatisticsCategoryName, info.SeriesName, info.ProductName, info.GeneralName, info.DefaultImageFile_Id,
        info.StorageMethod, info.OriginalPlace, info.Status, info.BottleCode, info.ProductCode, info.packagingCode, info.ProductInfoType,
        ifnull(p.StatisticsClass,info.ProductStatisticsClass) as ProductStatisticsClass, ifnull(p.SecondStatisticsClass,info.SecondStatisticsClass) as SecondStatisticsClass
        , info.MonthOfShelfLife, info.ShelfLifeUnit, info.hasBottleCode,
        info.hasBoxCode, info.ShopId, info.ShelfLifeLongTime, info.CreateTime, info.CreateUser_Id, info.LastUpdateTime,
        info.LastUpdateUser_Id, info.PackageType, info.IsProcess, info.StorageType, info.ParentOrg_Id,info.DefaultCostPrice,info.IsBOM,info.PolicyId
        from productinfo info
        inner join productsku sku on sku.productinfo_id = info.id
        left join productinfocategory p on p.Id = sku.ProductInfoCategory_Id
        where sku.productsku_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="getDefaultImageFileId" resultType="java.lang.Integer">
        SELECT distinct DefaultImageFile_Id
        FROM productinfo pi
                     LEFT JOIN productsku ps on pi.id = ps.ProductInfo_Id
        WHERE DefaultImageFile_Id IS NOT NULL
          AND DefaultImageFile_Id != 'null'
        AND LENGTH(DefaultImageFile_Id) <![CDATA[<]]> 10
        and ps.City_Id=#{cityId,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into productinfo
        (
        Id,
        Brand,
        StatisticsCategoryName,
        SeriesName,
        ProductName,
        GeneralName,
        DefaultImageFile_Id,
        StorageMethod,
        OriginalPlace,
        Status,
        BottleCode,
        ProductCode,
        packagingCode,
        ProductInfoType,
        ProductStatisticsClass,
        SecondStatisticsClass,
        MonthOfShelfLife,
        ShelfLifeUnit,
        hasBottleCode,
        hasBoxCode,
        ShopId,
        ShelfLifeLongTime,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id,
        PackageType,
        IsProcess,
        ParentOrg_Id,
        DefaultCostPrice,
        IsBOM
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.brand,jdbcType=VARCHAR},
            #{item.statisticsCategoryName,jdbcType=VARCHAR},
            #{item.seriesName,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.generalName,jdbcType=VARCHAR},
            #{item.defaultImageFile_Id,jdbcType=INTEGER},
            #{item.storageMethod,jdbcType=VARCHAR},
            #{item.originalPlace,jdbcType=VARCHAR},
            #{item.status,jdbcType=TINYINT},
            #{item.bottleCode,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.packagingCode,jdbcType=VARCHAR},
            #{item.productInfoType,jdbcType=TINYINT},
            #{item.productStatisticsClass,jdbcType=BIGINT},
            #{item.secondStatisticsClass,jdbcType=BIGINT},
            #{item.monthOfShelfLife,jdbcType=INTEGER},
            #{item.shelfLifeUnit,jdbcType=TINYINT},
            #{item.hasBottleCode,jdbcType=TINYINT},
            #{item.hasBoxCode,jdbcType=TINYINT},
            #{item.shopId,jdbcType=BIGINT},
            #{item.shelfLifeLongTime,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser_Id,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser_Id,jdbcType=INTEGER},
            #{item.packageType,jdbcType=TINYINT},
            #{item.process,jdbcType=TINYINT},
            #{item.parentOrgId,jdbcType=INTEGER},
            #{item.defaultCostPrice,jdbcType=DECIMAL},
            #{item.isBOM,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
</mapper>