package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO;

public interface ProductInfoSpecificationPOMapper {
    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table
     * productinfospecification
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table
     * productinfospecification
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    int insert(ProductInfoSpecificationPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table
     * productinfospecification
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    int insertSelective(ProductInfoSpecificationPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table
     * productinfospecification
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    ProductInfoSpecificationPO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table
     * productinfospecification
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    int updateByPrimaryKeySelective(ProductInfoSpecificationPO record);

    /**
     * 批量新增或修改产品规格
     * 
     * @return
     */
    int insertOrUpdateBatch(@Param("list") List<ProductInfoSpecificationPO> list);

    /**
     * 根据规格id查询规格
     * 
     * @return
     */
    List<ProductInfoSpecificationPO> listByIds(@Param("list") List<Long> ids);

    /**
     * 根据产品信息id查询对应的所有规格
     * 
     * @return
     */
    List<ProductInfoSpecificationPO> listByProductInfoIds(@Param("list") List<Long> productInfoIds);

    List<ProductInfoSpecificationPO> findByProductInfoIds(@Param("list") List<Long> productInfoIds);

    /**
     * 批量新增产品规格
     * 
     * @return
     */
    int insertBatch(@Param("list") List<ProductInfoSpecificationPO> list);

    List<ProductSpecificationDTO> findByProductSkuIds(@Param("list") List<Long> productSkuIds,
        @Param("warehouseId") Integer warehouseId);

    PageResult<ProductInfoSpecificationDTO> findByProductName(ProductInfoSO so);

    /**
     * 批量更新
     *
     * @return
     */
    int updateBatch(@Param("list") List<ProductInfoSpecificationPO> record);
}