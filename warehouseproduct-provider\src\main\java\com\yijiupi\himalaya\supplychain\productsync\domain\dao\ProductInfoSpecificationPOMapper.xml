<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfo_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Length" jdbcType="DOUBLE" property="length"/>
        <result column="Width" jdbcType="DOUBLE" property="width"/>
        <result column="Height" jdbcType="DOUBLE" property="height"/>
        <result column="Volume" jdbcType="VARCHAR" property="volume"/>
        <result column="Weight" jdbcType="DOUBLE" property="weight"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUser_Id"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUser_Id"/>
        <result column="ConvertProductInfoSpec_Id" jdbcType="BIGINT" property="convertProductInfoSpecId"/>
        <result column="ConvertSpecQuantity" jdbcType="DECIMAL" property="convertSpecQuantity"/>
        <result column="ConvertSpecUnitName" jdbcType="VARCHAR" property="convertSpecUnitName"/>
        <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="PackageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        Id, ProductInfo_Id, Name, Length, Width, Height, Volume, Weight, State, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id, ConvertProductInfoSpec_Id, ConvertSpecQuantity,
        ConvertSpecUnitName, PackageName, UnitName, PackageQuantity,OuterCode
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        select
        <include refid="Base_Column_List"/>
        from productinfospecification
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        delete from productinfospecification
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        insert into productinfospecification (Id, ProductInfo_Id, Name,
        Length, Width, Height,
        Volume, Weight, State,
        CreateTime, CreateUser_Id, LastUpdateTime,
        LastUpdateUser_Id, ConvertProductInfoSpec_Id, ConvertSpecQuantity, ConvertSpecUnitName,
        PackageName, UnitName, PackageQuantity,OuterCode)
        values (#{id,jdbcType=BIGINT}, #{productInfo_Id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
        #{length,jdbcType=DOUBLE}, #{width,jdbcType=DOUBLE}, #{height,jdbcType=DOUBLE},
        #{volume,jdbcType=VARCHAR}, #{weight,jdbcType=DOUBLE}, #{state,jdbcType=TINYINT},
        #{createTime,jdbcType=TIMESTAMP}, #{createUser_Id,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{lastUpdateUser_Id,jdbcType=INTEGER}, #{convertProductInfoSpecId,jdbcType=BIGINT},
        #{convertSpecQuantity,jdbcType=DECIMAL}, #{convertSpecUnitName,jdbcType=VARCHAR},
        #{packageName,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, #{packageQuantity,jdbcType=DECIMAL},
        #{outerCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        insert into productinfospecification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="productInfo_Id != null">
                ProductInfo_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="length != null">
                Length,
            </if>
            <if test="width != null">
                Width,
            </if>
            <if test="height != null">
                Height,
            </if>
            <if test="volume != null">
                Volume,
            </if>
            <if test="weight != null">
                Weight,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser_Id != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser_Id != null">
                LastUpdateUser_Id,
            </if>
            <if test="convertProductInfoSpecId != null">
                ConvertProductInfoSpec_Id,
            </if>
            <if test="convertSpecQuantity != null">
                ConvertSpecQuantity,
            </if>
            <if test="convertSpecUnitName != null">
                ConvertSpecUnitName,
            </if>
            <if test="packageName != null">
                PackageName,
            </if>
            <if test="unitName != null">
                UnitName,
            </if>
            <if test="packageQuantity != null">
                PackageQuantity,
            </if>
            <if test="outerCode != null">
                OuterCode,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="productInfo_Id != null">
                #{productInfo_Id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                #{length,jdbcType=DOUBLE},
            </if>
            <if test="width != null">
                #{width,jdbcType=DOUBLE},
            </if>
            <if test="height != null">
                #{height,jdbcType=DOUBLE},
            </if>
            <if test="volume != null">
                #{volume,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=DOUBLE},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser_Id != null">
                #{createUser_Id,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser_Id != null">
                #{lastUpdateUser_Id,jdbcType=INTEGER},
            </if>
            <if test="convertProductInfoSpecId != null">
                #{convertProductInfoSpecId,jdbcType=BIGINT},
            </if>
            <if test="convertSpecQuantity != null">
                #{convertSpecQuantity,jdbcType=DECIMAL},
            </if>
            <if test="convertSpecUnitName != null">
                #{convertSpecUnitName,jdbcType=VARCHAR},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="outerCode != null">
                #{outerCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        insert into productinfospecification
        (
        Id,
        ProductInfo_Id,
        Name,
        Length,
        Width,
        Height,
        Volume,
        Weight,
        State,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id,
        ConvertProductInfoSpec_Id,
        ConvertSpecQuantity,
        ConvertSpecUnitName,
        PackageName,
        UnitName,
        PackageQuantity,
        OuterCode
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.productInfo_Id,jdbcType=BIGINT},
            #{item.name,jdbcType=VARCHAR},
            #{item.length,jdbcType=DOUBLE},
            #{item.width,jdbcType=DOUBLE},
            #{item.height,jdbcType=DOUBLE},
            #{item.volume,jdbcType=VARCHAR},
            #{item.weight,jdbcType=DOUBLE},
            #{item.state,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser_Id,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser_Id,jdbcType=INTEGER},
            #{item.convertProductInfoSpecId,jdbcType=BIGINT},
            #{item.convertSpecQuantity,jdbcType=DECIMAL},
            #{item.convertSpecUnitName,jdbcType=VARCHAR},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.outerCode,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        ProductInfo_Id = VALUES(ProductInfo_Id),
        Name = VALUES(Name),
        Length = VALUES(Length),
        Width = VALUES(Width),
        Height = VALUES(Height),
        Volume = VALUES(Volume),
        Weight = VALUES(Weight),
        State = VALUES(State),
        LastUpdateTime = VALUES(LastUpdateTime),
        LastUpdateUser_Id = VALUES(LastUpdateUser_Id),
        ConvertProductInfoSpec_Id = VALUES(ConvertProductInfoSpec_Id),
        ConvertSpecQuantity = VALUES(ConvertSpecQuantity),
        ConvertSpecUnitName = VALUES(ConvertSpecUnitName),
        PackageName = VALUES(PackageName),
        UnitName = VALUES(UnitName),
        PackageQuantity = VALUES(PackageQuantity),
        OuterCode = VALUES(OuterCode)
    </insert>

    <select id="listByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfospecification
        where Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listByProductInfoIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfospecification
        where ProductInfo_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        order by ProductInfo_Id, CreateTime
    </select>

    <select id="findByProductInfoIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productinfospecification
        where ProductInfo_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into productinfospecification
        (
        Id,
        ProductInfo_Id,
        Name,
        Length,
        Width,
        Height,
        Volume,
        Weight,
        State,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id,
        ConvertProductInfoSpec_Id,
        ConvertSpecQuantity,
        ConvertSpecUnitName,
        PackageName,
        UnitName,
        PackageQuantity,
        OuterCode
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.productInfo_Id,jdbcType=BIGINT},
            #{item.name,jdbcType=VARCHAR},
            #{item.length,jdbcType=DOUBLE},
            #{item.width,jdbcType=DOUBLE},
            #{item.height,jdbcType=DOUBLE},
            #{item.volume,jdbcType=VARCHAR},
            #{item.weight,jdbcType=DOUBLE},
            #{item.state,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser_Id,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser_Id,jdbcType=INTEGER},
            #{item.convertProductInfoSpecId,jdbcType=BIGINT},
            #{item.convertSpecQuantity,jdbcType=DECIMAL},
            #{item.convertSpecUnitName,jdbcType=VARCHAR},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.outerCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="findByProductSkuIds"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO">
        select
        sku.ProductSku_Id as productSkuId, pic.Id as productSpecificationId, pic.ProductInfo_Id as productInfoId,
        pic.Length, pic.Width, pic.Height, pic.Volume, pic.Weight, psc.ProductFeature,pic.OuterCode
        from productinfospecification pic
        inner join productsku sku on sku.ProductSpecification_Id = pic.Id
        inner join productskuconfig psc on psc.ProductSku_Id = sku.ProductSku_Id
        where sku.ProductSku_Id in
        <foreach collection="list" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and psc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="findByProductName"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO">
        select
        pic.Id, pic.Name, pic.ProductInfo_Id as productInfoId,pic.PackageName,pic.UnitName,pic.PackageQuantity,
        pic.Length, pic.Width, pic.Height, pic.Volume, pic.Weight,pic.CreateTime,
        info.productName
        from productinfospecification pic
        inner join productinfo info on info.Id = pic.ProductInfo_Id
        where pic.State = 0
        <if test="productName != null">
            and info.ProductName like concat('%',#{productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="specificationId != null">
            and pic.Id = #{specificationId,jdbcType=BIGINT}
        </if>
        <if test="specIdList != null and specIdList.size() > 0">
            and pic.Id in
            <foreach collection="specIdList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="statisticsClassName != null and statisticsClassName != '' and (queryAwardFlag == null or queryAwardFlag == false)">
            and info.PolicyId is null
            and (info.StatisticsCategoryName is null or info.StatisticsCategoryName not like
            concat('%',#{statisticsClassName,jdbcType=VARCHAR},'%'))
        </if>
        <if test="productInfoIdList != null and productInfoIdList.size() > 0">
            and info.Id in
            <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by pic.CreateTime desc
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        update productinfospecification
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Length =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.length,jdbcType=DOUBLE}
                </foreach>
            </trim>
            <trim prefix="Width =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.width,jdbcType=DOUBLE}
                </foreach>
            </trim>
            <trim prefix="Height =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.height,jdbcType=DOUBLE}
                </foreach>
            </trim>
            <trim prefix="Volume =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.volume,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="Weight =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.weight,jdbcType=DOUBLE}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 13 15:52:59 CST 2018.
        -->
        update productinfospecification
        <set>
            <if test="productInfo_Id != null">
                ProductInfo_Id = #{productInfo_Id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                Length = #{length,jdbcType=DOUBLE},
            </if>
            <if test="width != null">
                Width = #{width,jdbcType=DOUBLE},
            </if>
            <if test="height != null">
                Height = #{height,jdbcType=DOUBLE},
            </if>
            <if test="volume != null">
                Volume = #{volume,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                Weight = #{weight,jdbcType=DOUBLE},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser_Id != null">
                LastUpdateUser_Id = #{lastUpdateUser_Id,jdbcType=INTEGER},
            </if>
            <if test="convertProductInfoSpecId != null">
                ConvertProductInfoSpec_Id = #{convertProductInfoSpecId,jdbcType=BIGINT},
            </if>
            <if test="convertSpecQuantity != null">
                ConvertSpecQuantity = #{convertSpecQuantity,jdbcType=DECIMAL},
            </if>
            <if test="convertSpecUnitName != null">
                ConvertSpecUnitName = #{convertSpecUnitName,jdbcType=VARCHAR},
            </if>
            <if test="packageName != null">
                PackageName = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                UnitName = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                PackageQuantity = #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="outerCode != null">
                OuterCode = #{outerCode,jdbcType=DECIMAL},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
</mapper>