package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentUpdateParam;

/**
 * 产品sku配置表
 *
 * <AUTHOR>
 * @since 2020-01-08 10:34
 */
public interface ProductSkuConfigMapper {

    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(ProductSkuConfigPO record);

    int insertOrUpdate(ProductSkuConfigPO record);

    int insertOrUpdateSelective(ProductSkuConfigPO record);

    int insertSelective(ProductSkuConfigPO record);

    ProductSkuConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSkuConfigPO record);

    int updateByPrimaryKey(ProductSkuConfigPO record);

    int updateBatch(List<ProductSkuConfigPO> list);

    int updateBatchSelective(List<ProductSkuConfigPO> list);

    int batchInsert(@Param("list") List<ProductSkuConfigPO> list);

    /**
     * 根据skuId和仓库id查询产品sku配置
     */
    ProductSkuConfigPO getProductSkuConfigBySkuIdAndWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("productSkuId") Long productSkuId);

    /**
     * 根据skuId查询产品sku配置
     */
    List<ProductSkuConfigPO> getProductSkuConfigBySkuIds(@Param("list") Collection<Long> productSkuIds);

    /**
     * 根据skuId查询产品sku配置
     */
    List<ProductSkuConfigPO> getSkuConfigBySkuIdsAndWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("productSkuIds") List<Long> productSkuIds);

    /**
     * 根据sku查询对应的所有仓库
     */
    List<Integer> getWarehouseIdBySkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 修改属性
     */
    void updateInventoryAttribute(@Param("configIdList") List<Long> configIdList, @Param("key") String key);

    /**
     * 批量更新补货上下限
     *
     * @param param 更新参数
     */
    void updateReplenishmentInfo(@Param("param") ReplenishmentUpdateParam param);

    /**
     * 查询所有绝对滞销的产品配置
     */
    List<ProductSkuConfigPO> listUnsalableSkuConfig(@Param("warehouseId") Integer warehouseId,
        @Param("productSkuIds") List<Long> productSkuIds);

    /**
     * 更新是否绝对滞销
     */
    int updateIsUnsalableByConfigIds(@Param("isUnsalable") Byte isUnsalable, @Param("configIds") List<Long> configIds);
}
