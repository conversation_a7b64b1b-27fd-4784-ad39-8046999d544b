<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        <!--@Table productskuconfig-->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="TINYINT" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="Unpackage" jdbcType="TINYINT" property="unpackage"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="MaxInventory" jdbcType="DECIMAL" property="maxInventory"/>
        <result column="MinInventory" jdbcType="DECIMAL" property="minInventory"/>
        <result column="MaxReplenishment" jdbcType="DECIMAL" property="maxReplenishment"/>
        <result column="MinReplenishment" jdbcType="DECIMAL" property="minReplenishment"/>
        <result column="isComplete" jdbcType="TINYINT" property="isComplete"/>
        <result column="StorageType" jdbcType="TINYINT" property="storageType"/>
        <result column="IsPick" jdbcType="TINYINT" property="pick"/>
        <result column="IsSow" jdbcType="TINYINT" property="sow"/>
        <result column="InventoryRatio" jdbcType="VARCHAR" property="inventoryRatio"/>
        <result column="IsFleeGoods" jdbcType="TINYINT" property="fleeGoods"/>
        <result column="ProductRelevantState" jdbcType="TINYINT" property="productRelevantState"/>
        <result column="IsUnique" jdbcType="TINYINT" property="unique"/>
        <result column="productGrade" jdbcType="TINYINT" property="productGrade"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="SellingPrice" jdbcType="DECIMAL" property="sellingPrice"/>
        <result column="SellingPriceUnit" jdbcType="VARCHAR" property="sellingPriceUnit"/>
        <result column="DistributionPercent" jdbcType="DECIMAL" property="distributionPercent"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="StoreState" jdbcType="TINYINT" property="storeState"/>
        <result column="SaleState" jdbcType="TINYINT" property="saleState"/>
        <result column="UnifySkuId" jdbcType="BIGINT" property="unifySkuId"/>
        <result column="inventoryPinProperty" jdbcType="VARCHAR" property="inventoryPinProperty"/>
        <result column="palletQuantity" jdbcType="DECIMAL" property="palletQuantity"/>
        <result column="BusinessTag" jdbcType="TINYINT" property="businessTag"/>
        <result column="StorageAttribute" jdbcType="TINYINT" property="storageAttribute"/>
        <result column="IsUnsalable" jdbcType="TINYINT" property="isUnsalable"/>
        <result column="ProductStorageMaxAge" jdbcType="INTEGER" property="productStorageMaxAge"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        Id, Warehouse_Id, ProductSku_Id, warehouseCustodyFee, DeliveryFee, DeliveryPayType,
        sortingFee, Unpackage, ProductFeature, MaxInventory, MinInventory, MaxReplenishment,
        MinReplenishment, isComplete, StorageType, IsPick, IsSow, InventoryRatio, IsFleeGoods,
        ProductRelevantState, IsUnique, productGrade, Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id, CostPrice, SellingPrice, SellingPriceUnit, DistributionPercent,
        DistributionPercentForAmount, `State`, StoreState, SaleState, UnifySkuId, inventoryPinProperty,
        palletQuantity, BusinessTag, StorageAttribute, IsUnsalable, ProductStorageMaxAge
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from productskuconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        insert into productskuconfig (Id, Warehouse_Id, ProductSku_Id,
        warehouseCustodyFee, DeliveryFee, DeliveryPayType,
        sortingFee, Unpackage, ProductFeature,
        MaxInventory, MinInventory, MaxReplenishment,
        MinReplenishment, isComplete, StorageType,
        IsPick, IsSow, InventoryRatio,
        IsFleeGoods, ProductRelevantState, IsUnique,
        productGrade, Remark, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id,
        CostPrice, SellingPrice, SellingPriceUnit,
        DistributionPercent, DistributionPercentForAmount,
        `State`, StoreState, SaleState,
        UnifySkuId, inventoryPinProperty, palletQuantity,
        BusinessTag)
        values (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{productSkuId,jdbcType=BIGINT},
        #{warehouseCustodyFee,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, #{deliveryPayType,jdbcType=TINYINT},
        #{sortingFee,jdbcType=DECIMAL}, #{unpackage,jdbcType=TINYINT}, #{productFeature,jdbcType=TINYINT},
        #{maxInventory,jdbcType=DECIMAL}, #{minInventory,jdbcType=DECIMAL}, #{maxReplenishment,jdbcType=DECIMAL},
        #{minReplenishment,jdbcType=DECIMAL}, #{isComplete,jdbcType=TINYINT}, #{storageType,jdbcType=TINYINT},
        #{pick,jdbcType=TINYINT}, #{sow,jdbcType=TINYINT}, #{inventoryRatio,jdbcType=VARCHAR},
        #{fleeGoods,jdbcType=TINYINT}, #{productRelevantState,jdbcType=TINYINT}, #{unique,jdbcType=TINYINT},
        #{productGrade,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER},
        #{costPrice,jdbcType=DECIMAL}, #{sellingPrice,jdbcType=DECIMAL}, #{sellingPriceUnit,jdbcType=VARCHAR},
        #{distributionPercent,jdbcType=DECIMAL}, #{distributionPercentForAmount,jdbcType=DECIMAL},
        #{state,jdbcType=TINYINT}, #{storeState,jdbcType=TINYINT}, #{saleState,jdbcType=TINYINT},
        #{unifySkuId,jdbcType=BIGINT}, #{inventoryPinProperty,jdbcType=VARCHAR}, #{palletQuantity,jdbcType=DECIMAL},
        #{businessTag,jdbcType=TINYINT})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        insert into productskuconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="warehouseCustodyFee != null">
                warehouseCustodyFee,
            </if>
            <if test="deliveryFee != null">
                DeliveryFee,
            </if>
            <if test="deliveryPayType != null">
                DeliveryPayType,
            </if>
            <if test="sortingFee != null">
                sortingFee,
            </if>
            <if test="unpackage != null">
                Unpackage,
            </if>
            <if test="productFeature != null">
                ProductFeature,
            </if>
            <if test="maxInventory != null">
                MaxInventory,
            </if>
            <if test="minInventory != null">
                MinInventory,
            </if>
            <if test="maxReplenishment != null">
                MaxReplenishment,
            </if>
            <if test="minReplenishment != null">
                MinReplenishment,
            </if>
            <if test="isComplete != null">
                isComplete,
            </if>
            <if test="storageType != null">
                StorageType,
            </if>
            <if test="pick != null">
                IsPick,
            </if>
            <if test="sow != null">
                IsSow,
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                InventoryRatio,
            </if>
            <if test="fleeGoods != null">
                IsFleeGoods,
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState,
            </if>
            <if test="unique != null">
                IsUnique,
            </if>
            <if test="productGrade != null">
                productGrade,
            </if>
            <if test="remark != null and remark != ''">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
            <if test="costPrice != null">
                CostPrice,
            </if>
            <if test="sellingPrice != null">
                SellingPrice,
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                SellingPriceUnit,
            </if>
            <if test="distributionPercent != null">
                DistributionPercent,
            </if>
            <if test="distributionPercentForAmount != null">
                DistributionPercentForAmount,
            </if>
            <if test="state != null">
                `State`,
            </if>
            <if test="storeState != null">
                StoreState,
            </if>
            <if test="saleState != null">
                SaleState,
            </if>
            <if test="unifySkuId != null">
                UnifySkuId,
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                inventoryPinProperty,
            </if>
            <if test="palletQuantity != null">
                palletQuantity,
            </if>
            <if test="businessTag != null">
                BusinessTag,
            </if>
            <if test="storageAttribute != null">
                StorageAttribute,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="warehouseCustodyFee != null">
                #{warehouseCustodyFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryPayType != null">
                #{deliveryPayType,jdbcType=TINYINT},
            </if>
            <if test="sortingFee != null">
                #{sortingFee,jdbcType=DECIMAL},
            </if>
            <if test="unpackage != null">
                #{unpackage,jdbcType=TINYINT},
            </if>
            <if test="productFeature != null">
                #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null">
                #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="productRelevantState != null">
                #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null">
                #{unique,jdbcType=TINYINT},
            </if>
            <if test="productGrade != null">
                #{productGrade,jdbcType=TINYINT},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="costPrice != null">
                #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPrice != null">
                #{sellingPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                #{sellingPriceUnit,jdbcType=VARCHAR},
            </if>
            <if test="distributionPercent != null">
                #{distributionPercent,jdbcType=DECIMAL},
            </if>
            <if test="distributionPercentForAmount != null">
                #{distributionPercentForAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="storeState != null">
                #{storeState,jdbcType=TINYINT},
            </if>
            <if test="saleState != null">
                #{saleState,jdbcType=TINYINT},
            </if>
            <if test="unifySkuId != null">
                #{unifySkuId,jdbcType=BIGINT},
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                #{inventoryPinProperty,jdbcType=VARCHAR},
            </if>
            <if test="palletQuantity != null">
                #{palletQuantity,jdbcType=DECIMAL},
            </if>
            <if test="businessTag != null">
                #{businessTag,jdbcType=TINYINT},
            </if>
            <if test="storageAttribute != null">
                #{storageAttribute,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        update productskuconfig
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="warehouseCustodyFee != null">
                warehouseCustodyFee = #{warehouseCustodyFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                DeliveryFee = #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryPayType != null">
                DeliveryPayType = #{deliveryPayType,jdbcType=TINYINT},
            </if>
            <if test="sortingFee != null">
                sortingFee = #{sortingFee,jdbcType=DECIMAL},
            </if>
            <if test="unpackage != null">
                Unpackage = #{unpackage,jdbcType=TINYINT},
            </if>
            <if test="productFeature != null">
                ProductFeature = #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                MaxInventory = #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                MinInventory = #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                MaxReplenishment = #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                MinReplenishment = #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                isComplete = #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                StorageType = #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                IsPick = #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                IsSow = #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null">
                IsFleeGoods = #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState = #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null">
                IsUnique = #{unique,jdbcType=TINYINT},
            </if>
            <if test="productGrade != null">
                productGrade = #{productGrade,jdbcType=TINYINT},
            </if>
            <if test="remark != null and remark != ''">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="costPrice != null">
                CostPrice = #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPrice != null">
                SellingPrice = #{sellingPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                SellingPriceUnit = #{sellingPriceUnit,jdbcType=VARCHAR},
            </if>
            <if test="distributionPercent != null">
                DistributionPercent = #{distributionPercent,jdbcType=DECIMAL},
            </if>
            <if test="distributionPercentForAmount != null">
                DistributionPercentForAmount = #{distributionPercentForAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                `State` = #{state,jdbcType=TINYINT},
            </if>
            <if test="storeState != null">
                StoreState = #{storeState,jdbcType=TINYINT},
            </if>
            <if test="saleState != null">
                SaleState = #{saleState,jdbcType=TINYINT},
            </if>
            <if test="unifySkuId != null">
                UnifySkuId = #{unifySkuId,jdbcType=BIGINT},
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                inventoryPinProperty = #{inventoryPinProperty,jdbcType=VARCHAR},
            </if>
            <if test="palletQuantity != null">
                palletQuantity = #{palletQuantity,jdbcType=DECIMAL},
            </if>
            <if test="businessTag != null">
                BusinessTag = #{businessTag,jdbcType=TINYINT},
            </if>
            <if test="storageAttribute != null">
                StorageAttribute = #{storageAttribute,jdbcType=TINYINT},
            </if>
            <if test="isUnsalable != null">
                IsUnsalable = #{isUnsalable,jdbcType=TINYINT},
            </if>
            <if test="productStorageMaxAge != null">
                ProductStorageMaxAge = #{productStorageMaxAge,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        update productskuconfig
        set Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
        warehouseCustodyFee = #{warehouseCustodyFee,jdbcType=DECIMAL},
        DeliveryFee = #{deliveryFee,jdbcType=DECIMAL},
        DeliveryPayType = #{deliveryPayType,jdbcType=TINYINT},
        sortingFee = #{sortingFee,jdbcType=DECIMAL},
        Unpackage = #{unpackage,jdbcType=TINYINT},
        ProductFeature = #{productFeature,jdbcType=TINYINT},
        MaxInventory = #{maxInventory,jdbcType=DECIMAL},
        MinInventory = #{minInventory,jdbcType=DECIMAL},
        MaxReplenishment = #{maxReplenishment,jdbcType=DECIMAL},
        MinReplenishment = #{minReplenishment,jdbcType=DECIMAL},
        isComplete = #{isComplete,jdbcType=TINYINT},
        StorageType = #{storageType,jdbcType=TINYINT},
        IsPick = #{pick,jdbcType=TINYINT},
        IsSow = #{sow,jdbcType=TINYINT},
        InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR},
        IsFleeGoods = #{fleeGoods,jdbcType=TINYINT},
        ProductRelevantState = #{productRelevantState,jdbcType=TINYINT},
        IsUnique = #{unique,jdbcType=TINYINT},
        productGrade = #{productGrade,jdbcType=TINYINT},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
        CostPrice = #{costPrice,jdbcType=DECIMAL},
        SellingPrice = #{sellingPrice,jdbcType=DECIMAL},
        SellingPriceUnit = #{sellingPriceUnit,jdbcType=VARCHAR},
        DistributionPercent = #{distributionPercent,jdbcType=DECIMAL},
        DistributionPercentForAmount = #{distributionPercentForAmount,jdbcType=DECIMAL},
        `State` = #{state,jdbcType=TINYINT},
        StoreState = #{storeState,jdbcType=TINYINT},
        SaleState = #{saleState,jdbcType=TINYINT},
        UnifySkuId = #{unifySkuId,jdbcType=BIGINT},
        inventoryPinProperty = #{inventoryPinProperty,jdbcType=VARCHAR},
        palletQuantity = #{palletQuantity,jdbcType=DECIMAL},
        BusinessTag = #{businessTag,jdbcType=TINYINT}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productskuconfig
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Warehouse_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ProductSku_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productSkuId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="warehouseCustodyFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseCustodyFee,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="DeliveryFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.deliveryFee,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="DeliveryPayType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.deliveryPayType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="sortingFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.sortingFee,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="Unpackage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.unpackage,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="ProductFeature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productFeature,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="MaxInventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.maxInventory,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="MinInventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.minInventory,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="MaxReplenishment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.maxReplenishment,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="MinReplenishment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.minReplenishment,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="isComplete = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.isComplete,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="StorageType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.storageType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsPick = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.pick,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsSow = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.sow,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="InventoryRatio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.inventoryRatio,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="IsFleeGoods = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.fleeGoods,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="ProductRelevantState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productRelevantState,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsUnique = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.unique,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="productGrade = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productGrade,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="Remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CreateUser_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUserId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="CostPrice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.costPrice,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SellingPrice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.sellingPrice,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SellingPriceUnit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.sellingPriceUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="DistributionPercent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.distributionPercent,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="DistributionPercentForAmount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.distributionPercentForAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="`State` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="StoreState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.storeState,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="SaleState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.saleState,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="UnifySkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.unifySkuId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="inventoryPinProperty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.inventoryPinProperty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="palletQuantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.palletQuantity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="BusinessTag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.businessTag,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productskuconfig
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Warehouse_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductSku_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSkuId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productSkuId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="warehouseCustodyFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseCustodyFee != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseCustodyFee,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DeliveryFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliveryFee != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.deliveryFee,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DeliveryPayType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliveryPayType != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.deliveryPayType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sortingFee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sortingFee != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.sortingFee,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Unpackage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.unpackage != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.unpackage,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductFeature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productFeature != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productFeature,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MaxInventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.maxInventory != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.maxInventory,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MinInventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.minInventory != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.minInventory,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MaxReplenishment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.maxReplenishment != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.maxReplenishment,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MinReplenishment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.minReplenishment != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.minReplenishment,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="isComplete = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isComplete != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.isComplete,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="StorageType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.storageType != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.storageType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsPick = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pick != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.pick,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsSow = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sow != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.sow,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="InventoryRatio = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inventoryRatio != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.inventoryRatio,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsFleeGoods = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fleeGoods != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.fleeGoods,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductRelevantState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productRelevantState != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productRelevantState,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsUnique = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.unique != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.unique,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="productGrade = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productGrade != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productGrade,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateUser_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUserId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUserId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CostPrice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.costPrice != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.costPrice,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SellingPrice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sellingPrice != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.sellingPrice,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SellingPriceUnit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sellingPriceUnit != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.sellingPriceUnit,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DistributionPercent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.distributionPercent != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.distributionPercent,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DistributionPercentForAmount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.distributionPercentForAmount != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.distributionPercentForAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`State` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.state != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="StoreState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.storeState != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.storeState,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SaleState = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saleState != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.saleState,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnifySkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.unifySkuId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.unifySkuId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="inventoryPinProperty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inventoryPinProperty != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.inventoryPinProperty,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="palletQuantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.palletQuantity != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.palletQuantity,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BusinessTag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.businessTag != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.businessTag,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsUnsalable = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isUnsalable != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.isUnsalable,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into productskuconfig
        (Id, Warehouse_Id, ProductSku_Id, warehouseCustodyFee, DeliveryFee, DeliveryPayType,
        sortingFee, Unpackage, ProductFeature, MaxInventory, MinInventory, MaxReplenishment,
        MinReplenishment, isComplete, StorageType, IsPick, IsSow, InventoryRatio, IsFleeGoods,
        ProductRelevantState, IsUnique, productGrade, Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id, CostPrice, SellingPrice, SellingPriceUnit, DistributionPercent,
        DistributionPercentForAmount, `State`, StoreState, SaleState, UnifySkuId, inventoryPinProperty,
        palletQuantity, BusinessTag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.productSkuId,jdbcType=BIGINT},
            #{item.warehouseCustodyFee,jdbcType=DECIMAL}, #{item.deliveryFee,jdbcType=DECIMAL},
            #{item.deliveryPayType,jdbcType=TINYINT}, #{item.sortingFee,jdbcType=DECIMAL},
            #{item.unpackage,jdbcType=TINYINT}, #{item.productFeature,jdbcType=TINYINT},
            #{item.maxInventory,jdbcType=DECIMAL},
            #{item.minInventory,jdbcType=DECIMAL}, #{item.maxReplenishment,jdbcType=DECIMAL},
            #{item.minReplenishment,jdbcType=DECIMAL}, #{item.isComplete,jdbcType=TINYINT},
            #{item.storageType,jdbcType=TINYINT}, #{item.pick,jdbcType=TINYINT}, #{item.sow,jdbcType=TINYINT},
            #{item.inventoryRatio,jdbcType=VARCHAR}, #{item.fleeGoods,jdbcType=TINYINT},
            #{item.productRelevantState,jdbcType=TINYINT}, #{item.unique,jdbcType=TINYINT},
            #{item.productGrade,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUserId,jdbcType=INTEGER}, #{item.costPrice,jdbcType=DECIMAL},
            #{item.sellingPrice,jdbcType=DECIMAL}, #{item.sellingPriceUnit,jdbcType=VARCHAR},
            #{item.distributionPercent,jdbcType=DECIMAL}, #{item.distributionPercentForAmount,jdbcType=DECIMAL},
            #{item.state,jdbcType=TINYINT}, #{item.storeState,jdbcType=TINYINT}, #{item.saleState,jdbcType=TINYINT},
            #{item.unifySkuId,jdbcType=BIGINT}, #{item.inventoryPinProperty,jdbcType=VARCHAR},
            #{item.palletQuantity,jdbcType=DECIMAL}, #{item.businessTag,jdbcType=TINYINT})
        </foreach>
    </insert>

    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from productskuconfig where Id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <insert id="insertOrUpdate"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        insert into productskuconfig
        (Id, Warehouse_Id, ProductSku_Id, warehouseCustodyFee, DeliveryFee, DeliveryPayType,
        sortingFee, Unpackage, ProductFeature, MaxInventory, MinInventory, MaxReplenishment,
        MinReplenishment, isComplete, StorageType, IsPick, IsSow, InventoryRatio, IsFleeGoods,
        ProductRelevantState, IsUnique, productGrade, Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id, CostPrice, SellingPrice, SellingPriceUnit, DistributionPercent,
        DistributionPercentForAmount, `State`, StoreState, SaleState, UnifySkuId, inventoryPinProperty,
        palletQuantity, BusinessTag)
        values
        (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{productSkuId,jdbcType=BIGINT},
        #{warehouseCustodyFee,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, #{deliveryPayType,jdbcType=TINYINT},
        #{sortingFee,jdbcType=DECIMAL}, #{unpackage,jdbcType=TINYINT}, #{productFeature,jdbcType=TINYINT},
        #{maxInventory,jdbcType=DECIMAL}, #{minInventory,jdbcType=DECIMAL}, #{maxReplenishment,jdbcType=DECIMAL},
        #{minReplenishment,jdbcType=DECIMAL}, #{isComplete,jdbcType=TINYINT}, #{storageType,jdbcType=TINYINT},
        #{pick,jdbcType=TINYINT}, #{sow,jdbcType=TINYINT}, #{inventoryRatio,jdbcType=VARCHAR},
        #{fleeGoods,jdbcType=TINYINT}, #{productRelevantState,jdbcType=TINYINT}, #{unique,jdbcType=TINYINT},
        #{productGrade,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER},
        #{costPrice,jdbcType=DECIMAL}, #{sellingPrice,jdbcType=DECIMAL}, #{sellingPriceUnit,jdbcType=VARCHAR},
        #{distributionPercent,jdbcType=DECIMAL}, #{distributionPercentForAmount,jdbcType=DECIMAL},
        #{state,jdbcType=TINYINT}, #{storeState,jdbcType=TINYINT}, #{saleState,jdbcType=TINYINT},
        #{unifySkuId,jdbcType=BIGINT}, #{inventoryPinProperty,jdbcType=VARCHAR}, #{palletQuantity,jdbcType=DECIMAL},
        #{businessTag,jdbcType=TINYINT})
        on duplicate key update
        Id = #{id,jdbcType=BIGINT},
        Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
        warehouseCustodyFee = #{warehouseCustodyFee,jdbcType=DECIMAL},
        DeliveryFee = #{deliveryFee,jdbcType=DECIMAL},
        DeliveryPayType = #{deliveryPayType,jdbcType=TINYINT},
        sortingFee = #{sortingFee,jdbcType=DECIMAL},
        Unpackage = #{unpackage,jdbcType=TINYINT},
        ProductFeature = #{productFeature,jdbcType=TINYINT},
        MaxInventory = #{maxInventory,jdbcType=DECIMAL},
        MinInventory = #{minInventory,jdbcType=DECIMAL},
        MaxReplenishment = #{maxReplenishment,jdbcType=DECIMAL},
        MinReplenishment = #{minReplenishment,jdbcType=DECIMAL},
        isComplete = #{isComplete,jdbcType=TINYINT},
        StorageType = #{storageType,jdbcType=TINYINT},
        IsPick = #{pick,jdbcType=TINYINT},
        IsSow = #{sow,jdbcType=TINYINT},
        InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR},
        IsFleeGoods = #{fleeGoods,jdbcType=TINYINT},
        ProductRelevantState = #{productRelevantState,jdbcType=TINYINT},
        IsUnique = #{unique,jdbcType=TINYINT},
        productGrade = #{productGrade,jdbcType=TINYINT},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
        CostPrice = #{costPrice,jdbcType=DECIMAL},
        SellingPrice = #{sellingPrice,jdbcType=DECIMAL},
        SellingPriceUnit = #{sellingPriceUnit,jdbcType=VARCHAR},
        DistributionPercent = #{distributionPercent,jdbcType=DECIMAL},
        DistributionPercentForAmount = #{distributionPercentForAmount,jdbcType=DECIMAL},
        `State` = #{state,jdbcType=TINYINT},
        StoreState = #{storeState,jdbcType=TINYINT},
        SaleState = #{saleState,jdbcType=TINYINT},
        UnifySkuId = #{unifySkuId,jdbcType=BIGINT},
        inventoryPinProperty = #{inventoryPinProperty,jdbcType=VARCHAR},
        palletQuantity = #{palletQuantity,jdbcType=DECIMAL},
        BusinessTag = #{businessTag,jdbcType=TINYINT}
    </insert>

    <insert id="insertOrUpdateSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO">
        <!--@mbg.generated-->
        insert into productskuconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="warehouseCustodyFee != null">
                warehouseCustodyFee,
            </if>
            <if test="deliveryFee != null">
                DeliveryFee,
            </if>
            <if test="deliveryPayType != null">
                DeliveryPayType,
            </if>
            <if test="sortingFee != null">
                sortingFee,
            </if>
            <if test="unpackage != null">
                Unpackage,
            </if>
            <if test="productFeature != null">
                ProductFeature,
            </if>
            <if test="maxInventory != null">
                MaxInventory,
            </if>
            <if test="minInventory != null">
                MinInventory,
            </if>
            <if test="maxReplenishment != null">
                MaxReplenishment,
            </if>
            <if test="minReplenishment != null">
                MinReplenishment,
            </if>
            <if test="isComplete != null">
                isComplete,
            </if>
            <if test="storageType != null">
                StorageType,
            </if>
            <if test="pick != null">
                IsPick,
            </if>
            <if test="sow != null">
                IsSow,
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                InventoryRatio,
            </if>
            <if test="fleeGoods != null">
                IsFleeGoods,
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState,
            </if>
            <if test="unique != null">
                IsUnique,
            </if>
            <if test="productGrade != null">
                productGrade,
            </if>
            <if test="remark != null and remark != ''">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
            <if test="costPrice != null">
                CostPrice,
            </if>
            <if test="sellingPrice != null">
                SellingPrice,
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                SellingPriceUnit,
            </if>
            <if test="distributionPercent != null">
                DistributionPercent,
            </if>
            <if test="distributionPercentForAmount != null">
                DistributionPercentForAmount,
            </if>
            <if test="state != null">
                `State`,
            </if>
            <if test="storeState != null">
                StoreState,
            </if>
            <if test="saleState != null">
                SaleState,
            </if>
            <if test="unifySkuId != null">
                UnifySkuId,
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                inventoryPinProperty,
            </if>
            <if test="palletQuantity != null">
                palletQuantity,
            </if>
            <if test="businessTag != null">
                BusinessTag,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="warehouseCustodyFee != null">
                #{warehouseCustodyFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryPayType != null">
                #{deliveryPayType,jdbcType=TINYINT},
            </if>
            <if test="sortingFee != null">
                #{sortingFee,jdbcType=DECIMAL},
            </if>
            <if test="unpackage != null">
                #{unpackage,jdbcType=TINYINT},
            </if>
            <if test="productFeature != null">
                #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null">
                #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="productRelevantState != null">
                #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null">
                #{unique,jdbcType=TINYINT},
            </if>
            <if test="productGrade != null">
                #{productGrade,jdbcType=TINYINT},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="costPrice != null">
                #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPrice != null">
                #{sellingPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                #{sellingPriceUnit,jdbcType=VARCHAR},
            </if>
            <if test="distributionPercent != null">
                #{distributionPercent,jdbcType=DECIMAL},
            </if>
            <if test="distributionPercentForAmount != null">
                #{distributionPercentForAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="storeState != null">
                #{storeState,jdbcType=TINYINT},
            </if>
            <if test="saleState != null">
                #{saleState,jdbcType=TINYINT},
            </if>
            <if test="unifySkuId != null">
                #{unifySkuId,jdbcType=BIGINT},
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                #{inventoryPinProperty,jdbcType=VARCHAR},
            </if>
            <if test="palletQuantity != null">
                #{palletQuantity,jdbcType=DECIMAL},
            </if>
            <if test="businessTag != null">
                #{businessTag,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="warehouseCustodyFee != null">
                warehouseCustodyFee = #{warehouseCustodyFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                DeliveryFee = #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryPayType != null">
                DeliveryPayType = #{deliveryPayType,jdbcType=TINYINT},
            </if>
            <if test="sortingFee != null">
                sortingFee = #{sortingFee,jdbcType=DECIMAL},
            </if>
            <if test="unpackage != null">
                Unpackage = #{unpackage,jdbcType=TINYINT},
            </if>
            <if test="productFeature != null">
                ProductFeature = #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                MaxInventory = #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                MinInventory = #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                MaxReplenishment = #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                MinReplenishment = #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                isComplete = #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                StorageType = #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                IsPick = #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                IsSow = #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null and inventoryRatio != ''">
                InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null">
                IsFleeGoods = #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState = #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null">
                IsUnique = #{unique,jdbcType=TINYINT},
            </if>
            <if test="productGrade != null">
                productGrade = #{productGrade,jdbcType=TINYINT},
            </if>
            <if test="remark != null and remark != ''">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="costPrice != null">
                CostPrice = #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPrice != null">
                SellingPrice = #{sellingPrice,jdbcType=DECIMAL},
            </if>
            <if test="sellingPriceUnit != null and sellingPriceUnit != ''">
                SellingPriceUnit = #{sellingPriceUnit,jdbcType=VARCHAR},
            </if>
            <if test="distributionPercent != null">
                DistributionPercent = #{distributionPercent,jdbcType=DECIMAL},
            </if>
            <if test="distributionPercentForAmount != null">
                DistributionPercentForAmount = #{distributionPercentForAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                `State` = #{state,jdbcType=TINYINT},
            </if>
            <if test="storeState != null">
                StoreState = #{storeState,jdbcType=TINYINT},
            </if>
            <if test="saleState != null">
                SaleState = #{saleState,jdbcType=TINYINT},
            </if>
            <if test="unifySkuId != null">
                UnifySkuId = #{unifySkuId,jdbcType=BIGINT},
            </if>
            <if test="inventoryPinProperty != null and inventoryPinProperty != ''">
                inventoryPinProperty = #{inventoryPinProperty,jdbcType=VARCHAR},
            </if>
            <if test="palletQuantity != null">
                palletQuantity = #{palletQuantity,jdbcType=DECIMAL},
            </if>
            <if test="businessTag != null">
                BusinessTag = #{businessTag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateIsUnsalableByConfigIds">
        update productskuconfig
        set IsUnsalable = #{isUnsalable, jdbcType=TINYINT}
        where id in
        <foreach collection="configIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>