<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper">
    <update id="updateInventoryAttribute">
        update productskuconfig set inventoryPinProperty=#{key} where id in
        <foreach collection="configIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getProductSkuConfigBySkuIdAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="getProductSkuConfigBySkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where ProductSku_Id in
        <foreach collection="list" index="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="getSkuConfigBySkuIdsAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="productSkuIds != null and productSkuIds.size() > 0">
            and ProductSku_Id in
            <foreach collection="productSkuIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getWarehouseIdBySkuId" resultType="java.lang.Integer">
        select distinct Warehouse_Id
        from productskuconfig
        where ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
    </select>

    <update id="updateReplenishmentInfo">
        update productskuconfig
        <set>
            <if test="param.max != null">
                MaxReplenishment = #{param.max,jdbcType=DECIMAL},
            </if>
            <if test="param.min != null">
                MinReplenishment = #{param.min,jdbcType=DECIMAL},
            </if>
            LastUpdateUser_Id = #{param.userId,jdbcType=INTEGER}
        </set>
        where Warehouse_Id = #{param.warehouseId,jdbcType=INTEGER}
        and ProductSku_Id = #{param.skuId,jdbcType=BIGINT}
    </update>

    <select id="listUnsalableSkuConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and IsUnsalable = 1
        <if test="productSkuIds != null and productSkuIds.size() > 0">
            and ProductSku_Id in
            <foreach collection="productSkuIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
</mapper>