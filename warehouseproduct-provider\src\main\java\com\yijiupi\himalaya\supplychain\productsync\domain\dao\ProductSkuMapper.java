package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存产品表相关操作.
 * 
 * <AUTHOR> 2018/1/19
 */
public interface ProductSkuMapper {

    int insertSelective(ProductSkuPO productSkuPO);

    /**
     * 同步产品,productsku表新增一条记录
     */
    void insertProductSku(ProductSkuPO productSkuPO);

    /**
     * 同步微酒产品,productsku表新增一条记录
     */
    void insertProductSkuByWine(ProductSkuPO productSkuPO);

    /**
     * 同步知花知果产品,productsku表新增一条记录
     */
    void insertProductSkuByZhzg(ProductSkuPO productSkuPO);

    /**
     * 根据cityId和skuid查询productsku
     * 
     * @param cityId
     * @param productSkuId
     * @return
     */
    ProductSkuPO selectByCityIdAndProductSkuId(@Param("cityId") Integer cityId,
        @Param("productSkuId") Long productSkuId);

    /**
     * 修改productsku
     * 
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductSkuPO record);

    /**
     * 修改productsku<br/>
     * 这个方法只会更新 {@link ProductSkuPO#getLastUpdateTime()} 和 {@link ProductSkuPO#getProductState()}
     */
    int updateBySkuIdSelective(ProductSkuPO record);

    /**
     * 查询skuId
     * 
     * @return
     */
    Long getProductSkuId(ProductSkuPO record);

    /**
     * 查询skuId
     * 
     * @return
     */
    ProductSkuPO getProductSkuIdDetail(ProductSkuPO record);

    /**
     * 根据大区ID+规格Id+OwnerId确定唯一SKUID
     * 
     * @return
     */
    ProductSkuPO getUniqueProductSku(ProductSkuPO record);

    /**
     * 根据skuId+产品来源查找SKUID
     * 
     * @return
     */
    Long getProductSkuIdByIdAndSource(@Param("skuId") Long skuId, @Param("source") Integer productSource);

    /**
     * 根据大区ID+规格Id+OwnerId确定唯一SKUID
     * 
     * @return
     */
    List<ProductSkuPO> listProductSkuBySpecIds(@Param("cityId") Integer cityId,
        @Param("list") List<Long> productSpecIds, @Param("ownerId") Long ownerId,
        @Param("source") Integer productSource);

    /**
     * 根据大区ID+规格Id+OwnerId确定唯一SKUID
     * 
     * @return
     */
    ProductSkuPO getProductSkuBySpecId(@Param("cityId") Integer cityId, @Param("specId") Long productSpecId,
        @Param("ownerId") Long ownerId, @Param("source") Integer productSource);

    /**
     * 批量新增sku
     */
    void insertProductSkuBatch(@Param("list") List<ProductSkuPO> list);

    /**
     * 批量新增或修改产品sku
     */
    void insertOrUpdateProductSku(@Param("list") List<ProductSkuPO> list);

    /**
     * 修改productsku
     * 
     * @return
     */
    int updateSelective(ProductSkuPO record);

    /**
     * 产品sku管理列表
     * 
     * @return
     */
    PageResult<ProductSkuListDTO> listProductSku(ProductSkuListSO productSkuListSO);

    /**
     * 获取产品sku详情
     * 
     * @return
     */
    ProductSkuListDTO getProductSku(Long id);

    /**
     * 条件查询产品sku
     * 
     * @return
     */
    List<ProductSkuPO> listProductSkuByCondition(@Param("source") Integer source);

    /**
     * 根据产品来源查询所有产品infoId
     * 
     * @return
     */
    List<Long> listProductInfoIdBySource(@Param("source") Integer source);

    /**
     * 批量更新sku的类目关系ID
     */
    int updateProductSkuOfCatetoryId(Long categoryGroupId);

    /**
     * 根据产品信息id获取非作废的产品
     * 
     * @param productInfoCategoryIds
     * @return
     */
    List<ProductSkuPO>
        findEffectiveSkuByProductInfoCategoryIds(@Param("productInfoCategoryIds") List<Long> productInfoCategoryIds);

    /**
     * 根据城市、sku查询sku信息
     * 
     * @param cityId
     * @param skuIdList
     * @return
     */
    List<ProductSkuPO> selectSkuInfoBySkuIds(@Param("cityId") Integer cityId, @Param("skuIdList") List<Long> skuIdList);

    /**
     * saas批量查询产品信息
     * 
     * @param productSaasQueryDTO
     * @return
     */
    List<ProductSaasSkuDTO> getProductSkuList(ProductSaasQueryDTO productSaasQueryDTO);

    /**
     * sass产品sku管理列表
     * 
     * @return
     */
    PageResult<ProductSaasSkuDTO> listProductSkuSaas(ProductSkuListSO productSkuListSO);

    /**
     * 根据关联第三方产品skuID查询产品
     * 
     * @param refSkuIds
     * @return
     */
    List<ProductSkuPO> listProductSkuByRefSkuIds(@Param("list") List<String> refSkuIds);

    /**
     * 根据关联第三方产品skuID查询产品
     * 
     * @return
     */
    ProductSkuPO getProductSkuByRefSkuId(@Param("refSkuId") String refSkuId, @Param("specId") Long specId,
        @Param("ownerId") Long ownerId);

    /**
     * 根据信息id和城市获取对应的SkuIds
     * 
     * @param cityId
     * @param productInfoIds
     * @return
     */
    List<ProductSkuListDTO> findSkuByInfos(@Param("cityId") Integer cityId,
        @Param("productInfoIds") List<Long> productInfoIds);

    /**
     *
     * @param cityId 城市id
     * @param productInfoId 信息id
     * @param secOwnerId 二级货主id
     * @return
     */
    ProductSkuListDTO findSkuByInfo(@Param("cityId") Integer cityId, @Param("productInfoId") Long productInfoId,
        @Param("secOwnerId") Long secOwnerId);

    /**
     * sass产品简单sku管理列表
     * 
     * @return
     */
    PageResult<ProductSaasSkuDTO> listProductSimpleSkuSaas(ProductSkuListSO productSkuListSO);

    /**
     * 根据城市、产品信息ID查询sku信息
     * 
     * @param cityId
     * @param skuIdList
     * @return
     */
    List<ProductSkuPO> selectSkuInfoByProductInfoIds(@Param("cityId") Integer cityId,
        @Param("productInfoIdList") List<Long> productInfoIdList);

    /**
     * 查询仓库下同规格其他货主的sku
     * 
     * @return
     */
    List<Long> getSameSpecSkuIds(@Param("cityId") Integer cityId, @Param("warehouseId") Integer warehouseId,
        @Param("specId") Long specId, @Param("skuId") Long skuId);

    List<Long> getSkuIdByCityIdAndSpecId(@Param("cityId") Integer cityId, @Param("list") List<Long> specIds,
        @Param("ownerId") Long ownerId);

    /**
     * 根据skuId+产品来源查找SKUID
     * 
     * @return
     */
    Long getProductSkuIdById(@Param("skuId") Long skuId);

    /**
     * 批量新增sku
     */
    void insertProductSkuBatchNew(@Param("list") List<ProductSkuPO> list);

    ProductSkuPO selectByPrimaryKey(Long id);

    /**
     * 根据产品信息id更新sku名称
     */
    int updateProductNameByInfoId(@Param("productName") String productName, @Param("productInfoId") Long productInfoId);

    /**
     * 产品sku状态同步
     */
    List<ProductSkuStateSyncDTO> listProductSkuStateSync(ProductSkuStateSyncSO syncSO);

    /**
     * 根据skuId批量更新产品状态
     */
    int updateProductStateBySkuIds(@Param("skuIdList") List<Long> skuIdList,
        @Param("productState") Integer productState);

    /**
     * 通过ID更新中台skuId
     */
    void updateUnifyIdInPrimaryKey(@Param("idList") List<Long> idList, @Param("unifySkuId") Long unifySkuId);

    /**
     * productSku表
     */
    PageResult<ProductSkuPO> listProductSkuPO(ProductSkuListSO productSkuListSO);

    /**
     * 根据中台skuId、cityId查询唯一的sku记录
     */
    List<ProductSkuPO> findSkuByUnifyCityList(@Param("unifySkuId") Long unifySkuId,
        @Param("cityIdList") List<Integer> cityIdList);

    /**
     * @param perIdList
     * @param ownerIdList
     * @param cityId
     * @return
     */
    List<ProductSkuPO> listSkuByOwner(@Param("perIdList") List<Long> perIdList,
        @Param("ownerIdList") List<String> ownerIdList, @Param("cityId") Integer cityId);

    /**
     * 根据大区ID+规格Id+OwnerId+SecOwnerId确定唯一SKUID
     *
     * @return
     */
    ProductSkuPO getUniqueProductSkuWithSecOwnerId(ProductSkuPO record);
}
