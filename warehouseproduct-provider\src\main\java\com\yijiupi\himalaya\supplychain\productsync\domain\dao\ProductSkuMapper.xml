<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="Company_Id" jdbcType="BIGINT" property="companyId"/>
        <result column="SaleModel" jdbcType="TINYINT" property="saleModel"/>
        <result column="distributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="TINYINT" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="ProductBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="ProductState" jdbcType="TINYINT" property="productState"/>
        <result column="Unpackage" jdbcType="TINYINT" property="unpackage"/>
        <result column="MonthOfShelfLife" jdbcType="INTEGER" property="monthOfShelfLife"/>
        <result column="ShelfLifeUnit" jdbcType="TINYINT" property="shelfLifeUnit"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="MaxInventory" jdbcType="DECIMAL" property="maxInventory"/>
        <result column="MinInventory" jdbcType="DECIMAL" property="minInventory"/>
        <result column="MaxReplenishment" jdbcType="DECIMAL" property="maxReplenishment"/>
        <result column="MinReplenishment" jdbcType="DECIMAL" property="minReplenishment"/>
        <result column="isComplete" jdbcType="TINYINT" property="isComplete"/>
        <result column="StorageType" jdbcType="TINYINT" property="storageType"/>
        <result column="IsPick" jdbcType="TINYINT" property="pick"/>
        <result column="IsSow" jdbcType="TINYINT" property="sow"/>
        <result column="InventoryRatio" jdbcType="VARCHAR" property="inventoryRatio"/>
        <result column="IsUnique" jdbcType="TINYINT" property="unique"/>
        <result column="IsFleeGoods" jdbcType="TINYINT" property="fleeGoods"/>
        <result column="ProductRelevantState" jdbcType="TINYINT" property="productRelevantState"/>
        <result column="ProductGrade" jdbcType="TINYINT" property="productGrade"/>
        <result column="Ref_ProductSku_Id" jdbcType="VARCHAR" property="refProductSkuId"/>
        <result column="secOwner_Id" jdbcType="VARCHAR" property="secOwnerId"/>
        <result column="ProductInfoCategory_Id" jdbcType="BIGINT" property="productInfoCategoryId"/>
        <result column="IsDelete" jdbcType="BOOLEAN" property="isDelete"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id
        , City_Id, ProductSpecification_Id, ProductSku_Id, Name, Sequence, Remo,
        CreateTime, CreateUserId, LastUpdateTime, LastUpdateUserId, Company_Id, SaleModel,
        distributionPercentForAmount, specificationName, packageName, unitName, packageQuantity,
        Source, warehouseCustodyFee, DeliveryFee, DeliveryPayType, sortingFee,
        ProductInfo_Id, productBrand, OwnerName, ProductState, Unpackage, MonthOfShelfLife, ShelfLifeUnit,
        ProductFeature,
        productGrade,
        MaxInventory, MinInventory, MaxReplenishment, MinReplenishment, isComplete, StorageType, IsPick, IsSow,
        InventoryRatio, IsUnique, IsFleeGoods, ProductRelevantState, Ref_ProductSku_Id, secOwner_Id,
        ProductInfoCategory_Id,
        IsDelete
    </sql>

    <sql id="ProductSkuList_Sql">
        psku
            .
            Id
                                     as id,
        psku.City_Id                 as cityId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.ProductSku_Id           as productSkuId,
        psku.Name                    as name,
        psku.Company_Id              as companyId,
        psku.OwnerName               as ownerName,
        psku.specificationName       as specificationName,
        psku.packageName             as packageName,
        psku.unitName                as unitName,
        psku.packageQuantity         as packageQuantity,
        psku.Source                  as source,
        psku.ProductInfo_Id          as productInfoId,
        psku.productBrand            as productBrand,
        psku.ProductState            as productState,
        psku.Unpackage               as unpackage,
        psku.MonthOfShelfLife        as monthOfShelfLife,
        psku.ShelfLifeUnit           as shelfLifeUnit,
        psku.ProductFeature          as productFeature,
        psku.MaxInventory            as maxInventory,
        psku.MinInventory            as minInventory,
        psku.MaxReplenishment        as maxReplenishment,
        psku.MinReplenishment        as minReplenishment,
        psku.StorageType             as storageType,
        psku.IsPick                  as pick,
        psku.IsSow                   as sow,
        psku.lastupdatetime          as lastUpdateTime,
        psku.SaleModel               as saleModel,
        psku.secOwner_Id             as secOwnerId,
        psku.productType,
        info.StatisticsCategoryName  as statisticsCategoryName,
        info.ProductCode             as productCode,
        info.BottleCode              as bottleCode,
        cg.StatisticsClassName       as statisticsClassName,
        cg.SecondStatisticsClassName as secondStatisticsClassName,
        cg.StatisticsClass           as productStatisticsClass,
        cg.SecondStatisticsClass     as secondStatisticsClass
    </sql>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        insert into productsku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            Id,
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sequence != null">
                Sequence,
            </if>
            <if test="remo != null">
                Remo,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
            <if test="saleModel != null">
                SaleModel,
            </if>
            <if test="companyId != null">
                Company_Id,
            </if>
            <if test="distributionPercentForAmount != null">
                distributionPercentForAmount,
            </if>
            <if test="specificationName != null">
                specificationName,
            </if>
            <if test="packageName != null">
                packageName,
            </if>
            <if test="unitName != null">
                unitName,
            </if>
            <if test="packageQuantity != null">
                packageQuantity,
            </if>
            <if test="source != null">
                Source,
            </if>
            <if test="warehouseCustodyFee != null">
                warehouseCustodyFee,
            </if>
            <if test="deliveryFee != null">
                DeliveryFee,
            </if>
            <if test="deliveryPayType != null">
                DeliveryPayType,
            </if>
            <if test="sortingFee != null">
                sortingFee,
            </if>
            <if test="productInfoId != null">
                ProductInfo_Id,
            </if>
            <if test="productBrand != null">
                productBrand,
            </if>
            <if test="productState != null">
                ProductState,
            </if>
            <if test="ownerName != null">
                OwnerName,
            </if>
            <if test="unpackage != null">
                Unpackage,
            </if>
            <if test="shelfLifeUnit != null">
                ShelfLifeUnit,
            </if>
            <if test="monthOfShelfLife != null">
                MonthOfShelfLife,
            </if>
            <if test="productFeature != null">
                ProductFeature,
            </if>
            <if test="maxInventory != null">
                MaxInventory,
            </if>
            <if test="minInventory != null">
                MinInventory,
            </if>
            <if test="maxReplenishment != null">
                MaxReplenishment,
            </if>
            <if test="minReplenishment != null">
                MinReplenishment,
            </if>
            <if test="isComplete != null">
                isComplete,
            </if>
            <if test="storageType != null">
                StorageType,
            </if>
            <if test="pick != null">
                IsPick,
            </if>
            <if test="sow != null">
                IsSow,
            </if>
            <if test="inventoryRatio != null">
                InventoryRatio,
            </if>
            <if test="fleeGoods != null">
                IsFleeGoods,
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState,
            </if>
            <if test="unique != null">
                IsUnique,
            </if>
            <if test="productInfoCategoryId != null">
                ProductInfoCategory_Id,
            </if>
            <if test="refProductSkuId != null">
                Ref_ProductSku_Id,
            </if>
            <if test="secOwnerId != null">
                secOwner_Id,
            </if>
            <if test="unifySkuId != null">
                UnifySkuId,
            </if>
            <if test="productType != null">
                ProductType,
            </if>
            <if test="isDelete != null">
                IsDelete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=INTEGER},
            </if>
            <if test="remo != null">
                #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="saleModel != null">
                #{saleModel,jdbcType=TINYINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="distributionPercentForAmount != null">
                #{distributionPercentForAmount,jdbcType=DECIMAL},
            </if>
            <if test="specificationName != null">
                #{specificationName,jdbcType=VARCHAR},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="warehouseCustodyFee != null">
                #{warehouseCustodyFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="deliveryPayType != null">
                #{deliveryPayType,jdbcType=TINYINT},
            </if>
            <if test="sortingFee != null">
                #{sortingFee,jdbcType=DECIMAL},
            </if>
            <if test="productInfoId != null">
                #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="productBrand != null">
                #{productBrand,jdbcType=VARCHAR},
            </if>
            <if test="productState != null">
                #{productState,jdbcType=TINYINT},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="unpackage != null">
                #{unpackage,jdbcType=TINYINT},
            </if>
            <if test="shelfLifeUnit != null">
                #{shelfLifeUnit,jdbcType=INTEGER},
            </if>
            <if test="monthOfShelfLife != null">
                #{monthOfShelfLife,jdbcType=INTEGER},
            </if>
            <if test="productFeature != null">
                #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null">
                #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null">
                #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="productRelevantState != null">
                #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null">
                #{unique,jdbcType=TINYINT},
            </if>
            <if test="productInfoCategoryId != null">
                #{productInfoCategoryId,jdbcType=BIGINT},
            </if>
            <if test="refProductSkuId != null">
                #{refProductSkuId,jdbcType=VARCHAR},
            </if>
            <if test="secOwnerId != null">
                #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="unifySkuId != null">
                #{unifySkuId,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>

    <!--新增一条productsku-->
    <insert id="insertProductSku" parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        insert into productsku
        (Id, City_Id, ProductSpecification_Id, ProductSku_Id, Name, Sequence, Remo, CreateTime, CreateUserId,
         LastUpdateTime,
         LastUpdateUserId, distributionPercentForAmount, SaleModel, Company_Id, specificationName, packageName,
         unitName, packageQuantity, Source, warehouseCustodyFee, DeliveryFee, DeliveryPayType, sortingFee,
         ProductInfo_Id, productBrand, ProductState, OwnerName, Unpackage, ShelfLifeUnit, MonthOfShelfLife,
        ProductFeature, ProductInfoCategory_Id, IsDelete)
        VALUES (#{id,jdbcType=BIGINT}, #{cityId,jdbcType=INTEGER},
                #{productSpecificationId,jdbcType=BIGINT}, #{productSkuId,jdbcType=BIGINT},
                #{name,jdbcType=VARCHAR}, null, #{remo,jdbcType=VARCHAR},
                NOW(), #{createUserId,jdbcType=INTEGER}, NOW(),
                #{lastUpdateUserId,jdbcType=INTEGER},
                #{distributionPercentForAmount,jdbcType=DECIMAL},
                #{saleModel,jdbcType=TINYINT},
                #{companyId,jdbcType=BIGINT},
                #{specificationName,jdbcType=VARCHAR},
                #{packageName,jdbcType=VARCHAR},
                #{unitName,jdbcType=VARCHAR},
                #{packageQuantity,jdbcType=DECIMAL},
                #{source,jdbcType=INTEGER},
                #{warehouseCustodyFee,jdbcType=DECIMAL},
                #{deliveryFee,jdbcType=DECIMAL},
                #{deliveryPayType,jdbcType=INTEGER},
                #{sortingFee,jdbcType=DECIMAL},
                #{productInfoId,jdbcType=BIGINT},
                #{productBrand,jdbcType=VARCHAR},
                #{productState,jdbcType=TINYINT},
                #{ownerName,jdbcType=VARCHAR},
                #{unpackage,jdbcType=TINYINT},
                #{shelfLifeUnit,jdbcType=INTEGER},
                #{monthOfShelfLife,jdbcType=INTEGER},
                #{productFeature,jdbcType=INTEGER},
        #{productInfoCategoryId,jdbcType=BIGINT},
        #{isDelete,jdbcType=BOOLEAN})
    </insert>

    <insert id="insertProductSkuByWine"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        insert into productsku
        (Id, City_Id, ProductSpecification_Id, ProductSku_Id, Name, Sequence, Remo, CreateTime, CreateUserId,
         LastUpdateTime,
         LastUpdateUserId, Company_Id, specificationName, packageName, unitName, packageQuantity, Source,
        ProductInfo_Id, productBrand, ProductState, OwnerName, IsDelete)
        VALUES (#{id,jdbcType=BIGINT}, #{cityId,jdbcType=INTEGER},
                #{productSpecificationId,jdbcType=BIGINT}, Id,
                #{name,jdbcType=VARCHAR}, null, #{remo,jdbcType=VARCHAR},
                NOW(), #{createUserId,jdbcType=INTEGER}, NOW(),
                #{lastUpdateUserId,jdbcType=INTEGER},
                #{companyId,jdbcType=BIGINT},
                #{specificationName,jdbcType=VARCHAR},
                #{packageName,jdbcType=VARCHAR},
                #{unitName,jdbcType=VARCHAR},
                #{packageQuantity,jdbcType=DECIMAL},
                #{source,jdbcType=INTEGER},
                #{productInfoId,jdbcType=BIGINT},
                #{productBrand,jdbcType=VARCHAR},
                #{productState,jdbcType=TINYINT},
        #{ownerName,jdbcType=VARCHAR},
        #{isDelete,jdbcType=BOOLEAN})
    </insert>

    <insert id="insertProductSkuByZhzg"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        insert into productsku
        (Id, City_Id,ProductSpecification_Id, ProductSku_Id, Name,
        CreateTime, CreateUserId, LastUpdateTime,LastUpdateUserId,
        specificationName,packageName,unitName,packageQuantity,Source,
        ProductInfo_Id,ProductState,OwnerName,ProductInfoCategory_Id,
        ProductFeature,IsPick,IsSow
        )
        VALUES
        (
        <if test="id != null">
            #{id,jdbcType=BIGINT},
        </if>
        #{cityId,jdbcType=INTEGER},
        #{productSpecificationId,jdbcType=BIGINT},
        Id,
        #{name,jdbcType=VARCHAR},
        NOW(), #{createUserId,jdbcType=INTEGER},
        NOW(), #{lastUpdateUserId,jdbcType=INTEGER},
        #{specificationName,jdbcType=VARCHAR},
        #{packageName,jdbcType=VARCHAR},
        #{unitName,jdbcType=VARCHAR},
        #{packageQuantity,jdbcType=DECIMAL},
        #{source,jdbcType=INTEGER},
        #{productInfoId,jdbcType=BIGINT},
        #{productState,jdbcType=TINYINT},
        #{ownerName,jdbcType=VARCHAR},
        #{productInfoCategoryId,jdbcType=BIGINT},
        #{productFeature,jdbcType=INTEGER},
        #{pick,jdbcType=TINYINT},
        #{sow,jdbcType=TINYINT}
        )
    </insert>

    <select id="selectByCityIdAndProductSkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where productSku_Id=#{productSkuId,jdbcType=BIGINT}
        <if test="cityId != null">
            and City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        limit 1
    </select>

    <update id="updateByPrimaryKeySelective">
        update productsku SET
        LastUpdateTime = NOW()
        <if test="productSpecificationId != null">
            ,ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        </if>
        <if test="name != null">
            ,Name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="sequence != null">
            ,Sequence = #{sequence,jdbcType=INTEGER}
        </if>
        <if test="saleModel != null">
            ,SaleModel = #{saleModel,jdbcType=TINYINT}
        </if>
        <if test="companyId != null">
            ,Company_Id = #{companyId,jdbcType=BIGINT}
        </if>
        <if test="remo != null">
            ,Remo = #{remo,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateUserId != null">
            ,LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER}
        </if>
        <if test="distributionPercentForAmount != null">
            ,distributionPercentForAmount = #{distributionPercentForAmount,jdbcType=DECIMAL}
        </if>
        <if test="specificationName != null">
            ,specificationName = #{specificationName,jdbcType=VARCHAR}
        </if>
        <if test="packageName != null">
            ,packageName = #{packageName,jdbcType=VARCHAR}
        </if>
        <if test="unitName != null">
            ,unitName = #{unitName,jdbcType=VARCHAR}
        </if>
        <if test="packageQuantity != null">
            ,packageQuantity = #{packageQuantity,jdbcType=DECIMAL}
        </if>
        <if test="source != null">
            ,Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="warehouseCustodyFee != null">
            ,warehouseCustodyFee = #{warehouseCustodyFee,jdbcType=DECIMAL}
        </if>
        <if test="deliveryFee != null">
            ,DeliveryFee = #{deliveryFee,jdbcType=DECIMAL}
        </if>
        <if test="deliveryPayType != null">
            ,DeliveryPayType = #{deliveryPayType,jdbcType=INTEGER}
        </if>
        <if test="sortingFee != null">
            ,sortingFee = #{sortingFee,jdbcType=DECIMAL}
        </if>
        <if test="cityId != null">
            ,City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="productInfoId != null">
            ,ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        <if test="productBrand != null">
            ,productBrand = #{productBrand,jdbcType=VARCHAR}
        </if>
        <if test="productState != null">
            ,ProductState = #{productState,jdbcType=TINYINT}
        </if>
        <if test="ownerName != null">
            ,OwnerName = #{ownerName,jdbcType=VARCHAR}
        </if>
        <if test="unpackage != null">
            ,Unpackage = #{unpackage,jdbcType=VARCHAR}
        </if>
        <if test="shelfLifeUnit != null">
            ,ShelfLifeUnit = #{shelfLifeUnit,jdbcType=INTEGER}
        </if>
        <if test="monthOfShelfLife != null">
            ,MonthOfShelfLife = #{monthOfShelfLife,jdbcType=INTEGER}
        </if>
        <if test="productInfoCategoryId != null">
            ,ProductInfoCategory_Id = #{productInfoCategoryId,jdbcType=BIGINT}
        </if>
        <if test="refProductSkuId != null">
            ,Ref_ProductSku_Id = #{refProductSkuId,jdbcType=VARCHAR}
        </if>
        <if test="secOwnerId != null">
            ,secOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="productFeature != null">
            ,ProductFeature = #{productFeature,jdbcType=INTEGER}
        </if>
        <if test="unifySkuId != null">
            ,UnifySkuId = #{unifySkuId,jdbcType=BIGINT}
        </if>
        <if test="maxInventory != null">
            ,maxInventory = #{maxInventory,jdbcType=INTEGER},
        </if>
        <if test="minInventory != null">
            minInventory = #{minInventory,jdbcType=INTEGER},
        </if>
        <if test="maxReplenishment != null">
            maxReplenishment = #{maxReplenishment,jdbcType=INTEGER},
        </if>
        <if test="minReplenishment != null">
            minReplenishment = #{minReplenishment,jdbcType=INTEGER}
        </if>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBySkuIdSelective">
        update productsku SET
        LastUpdateTime = NOW()
        <if test="productState != null">
            ,ProductState = #{productState,jdbcType=TINYINT}
        </if>
        where ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
    </update>

    <select id="getProductSkuId" resultType="java.lang.Long">
        select Id
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="companyId == null">
            and Company_Id is null
        </if>
        <if test="companyId != null">
            and Company_Id = #{companyId,jdbcType=BIGINT}
        </if>
        <if test="productInfoId != null">
            and ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        limit 1
    </select>

    <select id="getProductSkuIdDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="companyId == null">
            and Company_Id is null
        </if>
        <if test="companyId != null">
            and Company_Id = #{companyId,jdbcType=BIGINT}
        </if>
        <if test="productInfoId != null">
            and ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        limit 1
    </select>

    <select id="getUniqueProductSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="companyId == null">
            and Company_Id is null
        </if>
        <if test="companyId != null">
            and Company_Id = #{companyId,jdbcType=BIGINT}
        </if>
        <if test="productInfoId != null">
            and ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        <if test="isDelete != null">
            and IsDelete = #{isDelete,jdbcType=TINYINT}
        </if>
        limit 1
    </select>

    <select id="getProductSkuIdByIdAndSource" resultType="java.lang.Long">
        select Id
        from productsku
        where ProductSku_Id = #{skuId,jdbcType=BIGINT}
          and Source = #{source,jdbcType=INTEGER}
    </select>

    <select id="listProductSkuBySpecIds" resultMap="BaseResultMap">
        select Id, City_Id, ProductSpecification_Id, Company_Id
        from productsku
        where (City_Id = #{cityId,jdbcType=INTEGER} or City_Id = 10000)
        and ProductSpecification_Id in
        <if test="list != null">
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="ownerId == null">
            and Company_Id is null
        </if>
        <if test="ownerId != null">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="getProductSkuBySpecId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="ownerId == null">
            and Company_Id is null
        </if>
        <if test="ownerId != null">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        and ProductState in (0, 2)
        order by ProductState desc
        limit 1
    </select>

    <insert id="insertProductSkuBatch">
        insert into productsku
        (
        Id,
        City_Id,
        ProductSpecification_Id,
        ProductSku_Id,
        Name,
        CreateTime,
        CreateUserId,
        LastUpdateTime,
        LastUpdateUserId,
        specificationName,
        packageName,
        unitName,
        packageQuantity,
        Source,
        ProductInfo_Id,
        ProductState,
        OwnerName
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.productSpecificationId,jdbcType=BIGINT},
            Id,
            #{item.name,jdbcType=VARCHAR},
            NOW(),
            #{item.createUserId,jdbcType=INTEGER},
            NOW(),
            #{item.lastUpdateUserId,jdbcType=INTEGER},
            #{item.specificationName,jdbcType=VARCHAR},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.source,jdbcType=INTEGER},
            #{item.productInfoId,jdbcType=BIGINT},
            #{item.productState,jdbcType=TINYINT},
            #{item.ownerName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertOrUpdateProductSku">
        insert into productsku
        (
        Id,
        City_Id,
        ProductSpecification_Id,
        ProductSku_Id,
        Name,
        CreateTime,
        CreateUserId,
        LastUpdateTime,
        LastUpdateUserId,
        specificationName,
        packageName,
        unitName,
        packageQuantity,
        Source,
        ProductInfo_Id,
        ProductState,
        OwnerName,
        IsDelete
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.productSpecificationId,jdbcType=BIGINT},
            Id,
            #{item.name,jdbcType=VARCHAR},
            NOW(),
            #{item.createUserId,jdbcType=INTEGER},
            NOW(),
            #{item.lastUpdateUserId,jdbcType=INTEGER},
            #{item.specificationName,jdbcType=VARCHAR},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.source,jdbcType=INTEGER},
            #{item.productInfoId,jdbcType=BIGINT},
            #{item.productState,jdbcType=TINYINT},
            #{item.ownerName,jdbcType=VARCHAR},
            <if test="item.isDelete == null">
                ${false}
            </if>
            <if test="item.isDelete != null">
                #{item.isDelete,jdbcType=BOOLEAN}
            </if>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        City_Id = VALUES(City_Id),
        ProductSpecification_Id = VALUES(ProductSpecification_Id),
        Name = VALUES(Name),
        LastUpdateTime = VALUES(LastUpdateTime),
        LastUpdateUserId = VALUES(LastUpdateUserId),
        specificationName = VALUES(specificationName),
        packageName = VALUES(packageName),
        unitName = VALUES(unitName),
        packageQuantity = VALUES(packageQuantity),
        Source = VALUES(Source),
        ProductInfo_Id = VALUES(ProductInfo_Id),
        ProductState = VALUES(ProductState),
        OwnerName = VALUES(OwnerName),
        IsDelete = VALUES(IsDelete)
    </insert>

    <update id="updateSelective">
        update productsku SET
        LastUpdateTime = NOW()
        <if test="name != null">
            ,Name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="specificationName != null">
            ,specificationName = #{specificationName,jdbcType=VARCHAR}
        </if>
        <if test="packageName != null">
            ,packageName = #{packageName,jdbcType=VARCHAR}
        </if>
        <if test="unitName != null">
            ,unitName = #{unitName,jdbcType=VARCHAR}
        </if>
        where ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="productSpecificationId != null">
            and ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        </if>
    </update>

    <select id="listProductSku" resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO">
        select
        <include refid="ProductSkuList_Sql"/>
        from productsku psku
        inner join productinfo info on psku.ProductInfo_Id = info.Id
        inner join productinfospecification spec on psku.ProductSpecification_Id = spec.Id
        left join productinfocategory cg on psku.ProductInfoCategory_Id = cg.Id
        <where>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="isDelete != null">
                and psku.IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="cityIdList != null and cityIdList.size() > 0">
                and psku.City_Id in
                <foreach collection="cityIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ownerId != null and ownerId == 0">
                and psku.Company_Id is null
            </if>
            <if test="ownerId != null and ownerId != 0">
                and psku.Company_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=BIGINT}
            </if>
            <if test="productName != null">
                and psku.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productState != null">
                and psku.ProductState = #{productState,jdbcType=BIGINT}
            </if>
            <if test="productStatisticsClass != null">
                and cg.StatisticsClass = #{productStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass != null">
                and cg.SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="productCode != null">
                and info.ProductCode like concat('%',#{productCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="bottleCode != null">
                and info.BottleCode like concat('%',#{bottleCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null and productBrand != ''">
                and psku.productBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                and psku.Id in
                <foreach collection="productSkuIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="(productInfoIdList != null and productInfoIdList.size() > 0) or (productSpecIdList != null and productSpecIdList.size() > 0)">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="productInfoIdList != null and productInfoIdList.size() > 0">
                        or psku.ProductInfo_Id in
                        <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                    <if test="productSpecIdList != null and productSpecIdList.size() > 0">
                        or psku.ProductSpecification_Id in
                        <foreach collection="productSpecIdList" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="lastUpdateTimeStart != null and lastUpdateTimeStart != ''">
                and psku.lastupdatetime &gt;= #{lastUpdateTimeStart}
            </if>
            <if test="lastUpdateTimeEnd != null and lastUpdateTimeEnd != ''">
                and psku.lastupdatetime &lt;= #{lastUpdateTimeEnd}
            </if>
            <if test="productTypeList != null and productTypeList.size() > 0">
                and psku.productType in
                <foreach collection="productTypeList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        order by psku.CreateTime desc, psku.Id
    </select>

    <select id="getProductSku" resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO">
        select
        <include refid="ProductSkuList_Sql"/>
        from productsku psku
        inner join productinfo info on psku.ProductInfo_Id = info.Id
        inner join productinfospecification spec on psku.ProductSpecification_Id = spec.Id
        left join productinfocategory cg on psku.ProductInfoCategory_Id = cg.Id
        where psku.Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listProductSkuByCondition" resultMap="BaseResultMap">
        select Id, City_Id, ProductInfo_Id
        from productsku
        <where>
            <if test="source != null">
                Source = #{source,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="listProductInfoIdBySource" resultType="java.lang.Long">
        select distinct ProductInfo_Id
        from productsku
        where Source = #{source,jdbcType=INTEGER}
    </select>

    <update id="updateProductSkuOfCatetoryId">
        update productsku sku, productinfocategory cate
        set sku.ProductInfoCategory_Id = cate.Id
        WHERE sku.ProductInfo_Id = cate.ProductInfo_Id
          and cate.CategoryGroup_Id = #{categoryGroupId,jdbcType=BIGINT}
    </update>

    <select id="findEffectiveSkuByProductInfoCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where ProductState != 1
        and ProductInfoCategory_Id in
        <foreach collection="productInfoCategoryIds" item="infoCategoryId" open="(" separator="," close=")">
            #{infoCategoryId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectSkuInfoBySkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where ProductSku_Id in
        <foreach collection="skuIdList" item="skuId" open="(" close=")" separator=",">
            #{skuId,jdbcType=BIGINT}
        </foreach>
        <if test="cityId != null">
            and City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listProductSkuSaas"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasSkuDTO">
        select
        info.DefaultImageFile_Id as defaultImageFileId,
        <include refid="ProductSkuList_Sql"/>
        <if test="warehouseId !=null">
            ,config.CostPrice as costPrice
        </if>
        from productsku psku
        inner join productinfo info on psku.ProductInfo_Id = info.Id
        inner join productinfospecification spec on psku.ProductSpecification_Id = spec.Id
        inner join productskuconfig config on config.productsku_id = psku.productsku_id
        left join productinfocategory cg on psku.ProductInfoCategory_Id = cg.Id
        <where>
            <if test="warehouseId != null">
                and config.warehouse_id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="cityIdList != null and cityIdList.size() > 0">
                and psku.City_Id in
                <foreach collection="cityIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ownerId != null and ownerId == 0">
                and psku.Company_Id is null
            </if>
            <if test="ownerId != null and ownerId != 0">
                and psku.Company_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=BIGINT}
            </if>
            <if test="productName != null">
                and psku.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productState != null">
                and psku.ProductState = #{productState,jdbcType=BIGINT}
            </if>
            <if test="productStatisticsClass != null">
                and cg.StatisticsClass = #{productStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass != null">
                and cg.SecondStatisticsClass = #{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="productCode != null">
                and info.ProductCode like concat('%',#{productCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="bottleCode != null">
                and info.BottleCode like concat('%',#{bottleCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null and productBrand != ''">
                and psku.productBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="productNameList != null and productNameList.size() > 0">
                and psku.Name in
                <foreach collection="productNameList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="(productInfoIdList != null and productInfoIdList.size() > 0) or (productSpecIdList != null and productSpecIdList.size() > 0)">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="productInfoIdList != null and productInfoIdList.size() > 0">
                        or psku.ProductInfo_Id in
                        <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                    <if test="productSpecIdList != null and productSpecIdList.size() > 0">
                        or psku.ProductSpecification_Id in
                        <foreach collection="productSpecIdList" item="item" open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                and psku.Id in
                <foreach collection="productSkuIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by psku.CreateTime desc, psku.Id
    </select>

    <select id="getProductSkuList"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasSkuDTO">
        select
        info.DefaultImageFile_Id as defaultImageFileId,
        <include refid="ProductSkuList_Sql"/>
        from productsku psku
        inner join productinfo info on psku.ProductInfo_Id = info.Id
        inner join productinfospecification spec on psku.ProductSpecification_Id = spec.Id
        left join productinfocategory cg on psku.ProductInfoCategory_Id = cg.Id
        <where>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="skuIds != null and skuIds.size() > 0">
                and psku.ProductSku_Id in
                <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productSpecIds != null and productSpecIds.size() > 0">
                and psku.ProductSpecification_Id in
                <foreach collection="productSpecIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productInfoIds != null and productInfoIds.size() > 0">
                and psku.ProductInfo_Id in
                <foreach collection="productInfoIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

            <if test="bottleCodes != null and bottleCodes.size() > 0">
                and info.BottleCode in
                <foreach collection="bottleCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productState != null">
                and psku.ProductState = #{productState,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="listProductSkuByRefSkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where Ref_ProductSku_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getProductSkuByRefSkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where ProductSku_Id = #{refSkuId,jdbcType=BIGINT}
        and ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        <if test="ownerId == null">
            and Company_Id is null
        </if>
        <if test="ownerId != null">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        union all
        select
        <include refid="Base_Column_List"/>
        from productsku
        where Ref_ProductSku_Id = #{refSkuId,jdbcType=VARCHAR}
        and ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        <if test="ownerId == null">
            and Company_Id is null
        </if>
        <if test="ownerId != null">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        limit 1
    </select>

    <select id="findSkuByInfos"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO">
        select
        psku.Id as id,
        psku.City_Id as cityId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.ProductSku_Id as productSkuId,
        psku.Name as name,
        psku.ProductInfo_Id as productInfoId
        from productsku psku
        <where>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productInfoIds != null and productInfoIds.size() > 0">
                and psku.ProductInfo_Id in
                <foreach collection="productInfoIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findSkuByInfo"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO">
        select
        psku.Id as id,
        psku.City_Id as cityId,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.ProductSku_Id as productSkuId,
        psku.Name as name,
        psku.ProductInfo_Id as productInfoId
        from productsku psku
        <where>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productInfoId != null and productInfoId != 0">
                and psku.ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
            </if>
            <if test="secOwnerId != null and secOwnerId != 0">
                and psku.secOwner_Id = #{secOwnerId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="listProductSimpleSkuSaas"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasSkuDTO">
        select
        <include refid="ProductSkuList_Sql"/>
        from productsku psku
        inner join productinfo info on psku.ProductInfo_Id = info.Id
        inner join productinfospecification spec on psku.ProductSpecification_Id = spec.Id
        left join productinfocategory cg on psku.ProductInfoCategory_Id = cg.Id
        <where>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="cityIdList != null and cityIdList.size() > 0">
                and psku.City_Id in
                <foreach collection="cityIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ownerId != null and ownerId == 0">
                and psku.Company_Id is null
            </if>
            <if test="ownerId != null and ownerId != 0">
                and psku.Company_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=BIGINT}
            </if>
            <if test="productName != null">
                and psku.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null and productBrand != ''">
                and psku.productBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="productState != null">
                and psku.ProductState = #{productState,jdbcType=BIGINT}
            </if>
            <if test="productNameList != null and productNameList.size() > 0">
                and psku.Name in
                <foreach collection="productNameList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                and psku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productSpecIdList != null and productSpecIdList.size() > 0">
                and psku.ProductSpecification_Id in
                <foreach collection="productSpecIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productInfoId != null and productInfoId != 0">
                and psku.ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
            </if>
            <if test="productInfoIdList != null and productInfoIdList.size() > 0">
                and psku.ProductInfo_Id in
                <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="process != null">
                and info.IsProcess = #{process}
            </if>
        </where>
    </select>

    <select id="selectSkuInfoByProductInfoIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where ProductInfo_Id in
        <foreach collection="productInfoIdList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="cityId != null">
            and (City_Id = #{cityId,jdbcType=INTEGER} or City_Id = 10000)
        </if>
    </select>

    <select id="getSameSpecSkuIds" resultType="java.lang.Long">
        select distinct sku.ProductSku_Id
        from productsku sku
                 inner join productskuconfig cfg on sku.ProductSku_Id = cfg.ProductSku_Id
        where sku.City_Id = #{cityId,jdbcType=INTEGER}
          and cfg.Warehouse_id = #{warehouseId,jdbcType=INTEGER}
          and sku.ProductSpecification_Id = #{specId,jdbcType=BIGINT}
          and sku.ProductSku_Id != #{skuId,jdbcType=BIGINT}
    </select>

    <select id="getSkuIdByCityIdAndSpecId" resultType="java.lang.Long">
        select ProductSku_Id
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and Company_Id = #{ownerId,jdbcType=BIGINT}
    </select>

    <select id="getProductSkuIdById" resultType="java.lang.Long">
        select Id
        from productsku
        where ProductSku_Id = #{skuId,jdbcType=BIGINT}
    </select>

    <insert id="insertProductSkuBatchNew">
        insert into productsku
        (
        Id,
        City_Id,
        ProductSpecification_Id,
        ProductSku_Id,
        Name,
        CreateTime,
        CreateUserId,
        LastUpdateTime,
        LastUpdateUserId,
        specificationName,
        packageName,
        unitName,
        packageQuantity,
        Source,
        ProductInfo_Id,
        ProductState,
        OwnerName,
        Company_Id,
        productBrand,
        Remo,
        IsDelete
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.productSkuId,jdbcType=BIGINT},
            #{item.name,jdbcType=VARCHAR},
            NOW(),
            #{item.createUserId,jdbcType=INTEGER},
            NOW(),
            #{item.lastUpdateUserId,jdbcType=INTEGER},
            #{item.specificationName,jdbcType=VARCHAR},
            #{item.packageName,jdbcType=VARCHAR},
            #{item.unitName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.source,jdbcType=INTEGER},
            #{item.productInfoId,jdbcType=BIGINT},
            #{item.productState,jdbcType=TINYINT},
            #{item.ownerName,jdbcType=VARCHAR},
            #{item.companyId,jdbcType=BIGINT},
            #{item.productBrand,jdbcType=VARCHAR},
            #{item.remo,jdbcType=VARCHAR},
            <if test="item.isDelete == null">
                ${false}
            </if>
            <if test="item.isDelete != null">
                #{item.isDelete,jdbcType=BOOLEAN}
            </if>
            )
        </foreach>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <update id="updateProductNameByInfoId">
        update productsku
        set Name = #{productName,jdbcType=VARCHAR}
        where ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
    </update>

    <select id="listProductSkuStateSync"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncDTO">
        SELECT
        sku.ProductSpecification_Id as specId,
        sku.ProductState as productState,
        sku.Company_Id as ownerId,
        pc.ProductSku_Id as productSkuId,
        pc.Warehouse_id as warehouseId
        FROM productskuconfig pc
        inner join productsku sku on pc.ProductSku_Id = sku.ProductSku_Id
        where sku.City_Id = #{cityId,jdbcType=INTEGER}
        and sku.Source = 0
        <if test="state != null">
            and sku.ProductState = #{state,jdbcType=INTEGER}
        </if>
        <if test="ownerType != null and ownerType == 0">
            and sku.Company_Id is null
        </if>
        <if test="ownerType != null and ownerType == 2">
            and sku.Company_Id is not null
        </if>
    </select>

    <update id="updateProductStateBySkuIds">
        update productsku
        set ProductState = #{productState,jdbcType=INTEGER}
        where ProductSku_Id in
        <foreach collection="skuIdList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateUnifyIdInPrimaryKey">
        update productsku
        set UnifySkuId = #{unifySkuId,jdbcType=BIGINT}
        where Id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listProductSkuPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        <where>
            <if test="cityIdStart != null">
                and City_Id >= #{cityIdStart,jdbcType=INTEGER}
            </if>
            <if test="cityIdEnd != null">
                and City_Id <![CDATA[ <= ]]> #{cityIdEnd,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="findSkuByUnifyCityList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where
        UnifySkuId = #{unifySkuId,jdbcType=BIGINT} and
        City_Id in
        <foreach collection="cityIdList" open="(" item="cityId" separator="," close=")">
            #{cityId,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="listSkuByOwner"
            resultType="com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO">
        select ProductSku_Id as productSkuId,
        City_Id as cityId,
        ProductSpecification_Id as productSpecificationId,
        Company_Id as companyId, packageQuantity
        from productsku
        where ProductSpecification_Id
        in
        <foreach collection="perIdList" open="(" item="perId" separator="," close=")">
            #{perId}
        </foreach>
        <if test="ownerIdList != null">
            and Company_Id in
            <foreach collection="ownerIdList" open="(" item="ownerId" separator="," close=")">
                #{ownerId}
            </foreach>
        </if>
        and City_Id =#{cityId};
    </select>
    <select id="getUniqueProductSkuWithSecOwnerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="companyId == null">
            and Company_Id is null
        </if>
        <if test="companyId != null">
            and Company_Id = #{companyId,jdbcType=BIGINT}
        </if>
        <if test="productInfoId != null">
            and ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
        </if>
        <if test="secOwnerId == null">
            and secOwner_Id is null
        </if>
        <if test="secOwnerId != null">
            and secOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
        limit 1
    </select>
</mapper>