package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import java.util.List;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtQueryDTO;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
public interface ProductdbExtMapper {

    ProductdbExtDTO detail(Long id);

    PageResult<ProductdbExtDTO> pageList(ProductdbExtQueryDTO productdbExtQueryDTO);

    List<ProductdbExtDTO> list(ProductdbExtQueryDTO productdbExtDTO);

    int insert(ProductdbExtDTO productdbExtDTO);

    int insertBatch(List<ProductdbExtDTO> productdbExtDTOs);

    int update(ProductdbExtDTO productdbExtDTO);

    int delete(ProductdbExtDTO productdbExtDTO);

}