<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductdbExtMapper">

    <resultMap id="productdbExtDTOResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dataId" jdbcType="BIGINT" property="dataId"/>
        <result column="sourceTable" jdbcType="VARCHAR" property="sourceTable"/>
        <result column="extType" jdbcType="TINYINT" property="extType"/>
        <result column="ext1" jdbcType="VARCHAR" property="ext1"/>
        <result column="ext2" jdbcType="VARCHAR" property="ext2"/>
        <result column="ext3" jdbcType="VARCHAR" property="ext3"/>
        <result column="ext4" jdbcType="VARCHAR" property="ext4"/>
        <result column="ext5" jdbcType="VARCHAR" property="ext5"/>
        <result column="ext6" jdbcType="VARCHAR" property="ext6"/>
        <result column="ext7" jdbcType="VARCHAR" property="ext7"/>
        <result column="ext8" jdbcType="VARCHAR" property="ext8"/>
        <result column="ext9" jdbcType="VARCHAR" property="ext9"/>
        <result column="ext10" jdbcType="VARCHAR" property="ext10"/>
        <result column="ext11" jdbcType="VARCHAR" property="ext11"/>
        <result column="ext12" jdbcType="VARCHAR" property="ext12"/>
        <result column="ext13" jdbcType="VARCHAR" property="ext13"/>
        <result column="ext14" jdbcType="VARCHAR" property="ext14"/>
        <result column="ext15" jdbcType="VARCHAR" property="ext15"/>
        <result column="ext16" jdbcType="VARCHAR" property="ext16"/>
        <result column="ext17" jdbcType="VARCHAR" property="ext17"/>
        <result column="ext18" jdbcType="VARCHAR" property="ext18"/>
        <result column="ext19" jdbcType="VARCHAR" property="ext19"/>
        <result column="ext20" jdbcType="VARCHAR" property="ext20"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="lastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>


    </resultMap>

    <sql id="productdbExtDTOColumns">
        productdbext.Id AS "id",
        productdbext.Data_Id AS "dataId",
        productdbext.SourceTable AS "sourceTable",
        productdbext.ExtType AS "extType",
        productdbext.Ext1 AS "ext1",
        productdbext.Ext2 AS "ext2",
        productdbext.Ext3 AS "ext3",
        productdbext.Ext4 AS "ext4",
        productdbext.Ext5 AS "ext5",
        productdbext.Ext6 AS "ext6",
        productdbext.Ext7 AS "ext7",
        productdbext.Ext8 AS "ext8",
        productdbext.Ext9 AS "ext9",
        productdbext.Ext10 AS "ext10",
        productdbext.Ext11 AS "ext11",
        productdbext.Ext12 AS "ext12",
        productdbext.Ext13 AS "ext13",
        productdbext.Ext14 AS "ext14",
        productdbext.Ext15 AS "ext15",
        productdbext.Ext16 AS "ext16",
        productdbext.Ext17 AS "ext17",
        productdbext.Ext18 AS "ext18",
        productdbext.Ext19 AS "ext19",
        productdbext.Ext20 AS "ext20",
        productdbext.CreateTime AS "createTime",
        productdbext.CreateUser_Id AS "createUserId",
        productdbext.LastUpdateTime AS "lastUpdateTime",
        productdbext.LastUpdateUser_Id AS "lastUpdateUserId"
    </sql>


    <select id="detail" resultMap="productdbExtDTOResultMap">
        SELECT
        <include refid="productdbExtDTOColumns"/>
        FROM productdbext
        <where>
            productdbext.Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="pageList" resultMap="productdbExtDTOResultMap">
        SELECT
        <include refid="productdbExtDTOColumns"/>
        FROM productdbext
        <where>
            <if test="dataId!= null">
                and Data_Id = #{dataId,jdbcType=BIGINT}
            </if>
            <if test="sourceTable!= null">
                and SourceTable = #{sourceTable,jdbcType=VARCHAR}
            </if>
            <if test="extType!= null">
                and ExtType = #{extType,jdbcType=TINYINT}
            </if>
            <if test="dataIdList != null and dataIdList.size() > 0">
                and Data_Id in
                <foreach collection="dataIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="list" resultMap="productdbExtDTOResultMap">
        SELECT
        <include refid="productdbExtDTOColumns"/>
        FROM productdbext
        <where>
            <if test="dataId!= null">
                and Data_Id = #{dataId,jdbcType=BIGINT}
            </if>

            <if test="dataIdList != null and dataIdList.size() > 0">
                and Data_Id in
                <foreach collection="dataIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>

            <if test="sourceTable!= null">
                and SourceTable = #{sourceTable,jdbcType=VARCHAR}
            </if>
            <if test="extType!= null">
                and ExtType = #{extType,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <insert id="insert">
        INSERT INTO productdbext(
        Id,
        Data_Id,
        SourceTable,
        ExtType,
        Ext1,
        Ext2,
        Ext3,
        Ext4,
        Ext5,
        Ext6,
        Ext7,
        Ext8,
        Ext9,
        Ext10,
        Ext11,
        Ext12,
        Ext13,
        Ext14,
        Ext15,
        Ext16,
        Ext17,
        Ext18,
        Ext19,
        Ext20,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{dataId,jdbcType=BIGINT},
        #{sourceTable,jdbcType=VARCHAR},
        #{extType,jdbcType=TINYINT},
        #{ext1,jdbcType=VARCHAR},
        #{ext2,jdbcType=VARCHAR},
        #{ext3,jdbcType=VARCHAR},
        #{ext4,jdbcType=VARCHAR},
        #{ext5,jdbcType=VARCHAR},
        #{ext6,jdbcType=VARCHAR},
        #{ext7,jdbcType=VARCHAR},
        #{ext8,jdbcType=VARCHAR},
        #{ext9,jdbcType=VARCHAR},
        #{ext10,jdbcType=VARCHAR},
        #{ext11,jdbcType=VARCHAR},
        #{ext12,jdbcType=VARCHAR},
        #{ext13,jdbcType=VARCHAR},
        #{ext14,jdbcType=VARCHAR},
        #{ext15,jdbcType=VARCHAR},
        #{ext16,jdbcType=VARCHAR},
        #{ext17,jdbcType=VARCHAR},
        #{ext18,jdbcType=VARCHAR},
        #{ext19,jdbcType=VARCHAR},
        #{ext20,jdbcType=VARCHAR},
        now(),
        #{createUserId,jdbcType=INTEGER},
        now(),
        #{lastUpdateUserId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO productdbext(
        Id,
        Data_Id,
        SourceTable,
        ExtType,
        Ext1,
        Ext2,
        Ext3,
        Ext4,
        Ext5,
        Ext6,
        Ext7,
        Ext8,
        Ext9,
        Ext10,
        Ext11,
        Ext12,
        Ext13,
        Ext14,
        Ext15,
        Ext16,
        Ext17,
        Ext18,
        Ext19,
        Ext20,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id
        )
        VALUES
        <foreach collection="list" item="productdbExtDTO" separator=",">
            (
            #{productdbExtDTO.id,jdbcType=BIGINT},
            #{productdbExtDTO.dataId,jdbcType=BIGINT},
            #{productdbExtDTO.sourceTable,jdbcType=VARCHAR},
            #{productdbExtDTO.extType,jdbcType=TINYINT},
            #{productdbExtDTO.ext1,jdbcType=VARCHAR},
            #{productdbExtDTO.ext2,jdbcType=VARCHAR},
            #{productdbExtDTO.ext3,jdbcType=VARCHAR},
            #{productdbExtDTO.ext4,jdbcType=VARCHAR},
            #{productdbExtDTO.ext5,jdbcType=VARCHAR},
            #{productdbExtDTO.ext6,jdbcType=VARCHAR},
            #{productdbExtDTO.ext7,jdbcType=VARCHAR},
            #{productdbExtDTO.ext8,jdbcType=VARCHAR},
            #{productdbExtDTO.ext9,jdbcType=VARCHAR},
            #{productdbExtDTO.ext10,jdbcType=VARCHAR},
            #{productdbExtDTO.ext11,jdbcType=VARCHAR},
            #{productdbExtDTO.ext12,jdbcType=VARCHAR},
            #{productdbExtDTO.ext13,jdbcType=VARCHAR},
            #{productdbExtDTO.ext14,jdbcType=VARCHAR},
            #{productdbExtDTO.ext15,jdbcType=VARCHAR},
            #{productdbExtDTO.ext16,jdbcType=VARCHAR},
            #{productdbExtDTO.ext17,jdbcType=VARCHAR},
            #{productdbExtDTO.ext18,jdbcType=VARCHAR},
            #{productdbExtDTO.ext19,jdbcType=VARCHAR},
            #{productdbExtDTO.ext20,jdbcType=VARCHAR},
            now(),
            #{productdbExtDTO.createUserId,jdbcType=INTEGER},
            now(),
            #{productdbExtDTO.lastUpdateUserId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE productdbext
        <set>
            <if test="id!= null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="dataId!= null">
                Data_Id = #{dataId,jdbcType=BIGINT},
            </if>
            <if test="sourceTable!= null">
                SourceTable = #{sourceTable,jdbcType=VARCHAR},
            </if>
            <if test="extType!= null">
                ExtType = #{extType,jdbcType=TINYINT},
            </if>
            <if test="ext1!= null">
                Ext1 = #{ext1,jdbcType=VARCHAR},
            </if>
            <if test="ext2!= null">
                Ext2 = #{ext2,jdbcType=VARCHAR},
            </if>
            <if test="ext3!= null">
                Ext3 = #{ext3,jdbcType=VARCHAR},
            </if>
            <if test="ext4!= null">
                Ext4 = #{ext4,jdbcType=VARCHAR},
            </if>
            <if test="ext5!= null">
                Ext5 = #{ext5,jdbcType=VARCHAR},
            </if>
            <if test="ext6!= null">
                Ext6 = #{ext6,jdbcType=VARCHAR},
            </if>
            <if test="ext7!= null">
                Ext7 = #{ext7,jdbcType=VARCHAR},
            </if>
            <if test="ext8!= null">
                Ext8 = #{ext8,jdbcType=VARCHAR},
            </if>
            <if test="ext9!= null">
                Ext9 = #{ext9,jdbcType=VARCHAR},
            </if>
            <if test="ext10!= null">
                Ext10 = #{ext10,jdbcType=VARCHAR},
            </if>
            <if test="ext11!= null">
                Ext11 = #{ext11,jdbcType=VARCHAR},
            </if>
            <if test="ext12!= null">
                Ext12 = #{ext12,jdbcType=VARCHAR},
            </if>
            <if test="ext13!= null">
                Ext13 = #{ext13,jdbcType=VARCHAR},
            </if>
            <if test="ext14!= null">
                Ext14 = #{ext14,jdbcType=VARCHAR},
            </if>
            <if test="ext15!= null">
                Ext15 = #{ext15,jdbcType=VARCHAR},
            </if>
            <if test="ext16!= null">
                Ext16 = #{ext16,jdbcType=VARCHAR},
            </if>
            <if test="ext17!= null">
                Ext17 = #{ext17,jdbcType=VARCHAR},
            </if>
            <if test="ext18!= null">
                Ext18 = #{ext18,jdbcType=VARCHAR},
            </if>
            <if test="ext19!= null">
                Ext19 = #{ext19,jdbcType=VARCHAR},
            </if>
            <if test="ext20!= null">
                Ext20 = #{ext20,jdbcType=VARCHAR},
            </if>
            <if test="createUserId!= null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime!= null">
                LastUpdateTime = now(),
            </if>
            <if test="lastUpdateUserId!= null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="delete">
        DELETE FROM productdbext
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>