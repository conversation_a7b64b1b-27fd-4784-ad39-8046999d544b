package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionarySO;

import java.util.List;

/**
 * 包装规格字典
 */
public interface SpecificationDictionaryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(SpecificationDictionaryPO record);

    int insertSelective(SpecificationDictionaryPO record);

    SpecificationDictionaryPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SpecificationDictionaryPO record);

    int updateByPrimaryKey(SpecificationDictionaryPO record);

    /**
     * 包装规格管理列表
     * 
     * @return
     */
    PageResult<SpecificationDictionaryPO>
        listSpecificationDictionary(SpecificationDictionarySO specificationDictionarySO);

    /**
     * 获取所有包装单位
     * 
     * @return
     */
    List<String> listUnitName();

    /**
     * 统计包装规格管理
     * 
     * @return
     */
    Integer countSpecificationDictionary(SpecificationDictionaryPO record);

    Integer countSaveSpecificationDictionary(SpecificationDictionaryPO record);

}