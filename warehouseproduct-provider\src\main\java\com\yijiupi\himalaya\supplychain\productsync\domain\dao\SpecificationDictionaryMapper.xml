<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.SpecificationDictionaryMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="SpecificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="PackageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="PackageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="UnitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="ParentOrg_Id" jdbcType="INTEGER" property="parentOrgId"/>

    </resultMap>

    <sql id="Base_Column_List">
        Id, SpecificationName, PackageQuantity, PackageName, UnitName, State, Remark, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id,ParentOrg_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from specificationdictionary
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from specificationdictionary
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        insert into specificationdictionary (Id, SpecificationName, PackageQuantity,
        PackageName, UnitName, State,
        Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id,ParentOrg_Id)
        values (#{id,jdbcType=BIGINT}, #{specificationName,jdbcType=VARCHAR}, #{packageQuantity,jdbcType=DECIMAL},
        #{packageName,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER}, #{parentOrgId,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        insert into specificationdictionary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="specificationName != null">
                SpecificationName,
            </if>
            <if test="packageQuantity != null">
                PackageQuantity,
            </if>
            <if test="packageName != null">
                PackageName,
            </if>
            <if test="unitName != null">
                UnitName,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="specificationName != null">
                #{specificationName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="parentOrgId != null">
                #{parentOrgId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        update specificationdictionary
        <set>
            <if test="specificationName != null">
                SpecificationName = #{specificationName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                PackageQuantity = #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="packageName != null">
                PackageName = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                UnitName = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        update specificationdictionary
        set SpecificationName = #{specificationName,jdbcType=VARCHAR},
        PackageQuantity = #{packageQuantity,jdbcType=DECIMAL},
        PackageName = #{packageName,jdbcType=VARCHAR},
        UnitName = #{unitName,jdbcType=VARCHAR},
        State = #{state,jdbcType=TINYINT},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
        ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>


    <select id="listSpecificationDictionary" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from specificationdictionary
        <where>
            <if test="specificationName != null">
                and SpecificationName like concat('%',#{specificationName,jdbcType=VARCHAR},'%')
            </if>

            <if test="parentOrgId != null">
                and ParentOrg_Id like #{parentOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        order by CreateTime desc, Id
    </select>

    <select id="listUnitName" resultType="java.lang.String">
        select distinct h.UnitName from
        (
        select PackageName as UnitName from specificationdictionary
        union all
        select UnitName from specificationdictionary
        ) h
    </select>

    <select id="countSpecificationDictionary" resultType="java.lang.Integer"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        select count(*) from specificationdictionary
        where SpecificationName = #{specificationName,jdbcType=VARCHAR}
        <if test="id != null">
            and Id != #{id,jdbcType=BIGINT}
        </if>
        <if test="parentOrgId != null">
            and ParentOrg_Id = #{parentOrgId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="countSaveSpecificationDictionary" resultType="java.lang.Integer"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.SpecificationDictionaryPO">
        select count(*) from specificationdictionary
        where SpecificationName = #{specificationName,jdbcType=VARCHAR}
        <if test="parentOrgId != null">
            and ParentOrg_Id = #{parentOrgId,jdbcType=BIGINT}
        </if>
    </select>

</mapper>