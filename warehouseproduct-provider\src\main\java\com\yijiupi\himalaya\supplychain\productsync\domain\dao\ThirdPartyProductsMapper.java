package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-17
 */
public interface ThirdPartyProductsMapper {

    ThirdPartyProductsPO detail(Long id);

    int insert(ThirdPartyProductsPO thirdPartyProducts);

    int insertBatch(@Param("list") List<ThirdPartyProductsPO> thirdPartyProducts);

    int update(ThirdPartyProductsPO thirdPartyProducts);

    int delete(ThirdPartyProductsPO thirdPartyProducts);

    List<ThirdPartyProductsPO> listBySkuIds(@Param("list") List<String> skuIds, @Param("cityId") String cityId,
        @Param("warehouseId") String warehouseId);

    List<ThirdPartyProductsPO> listThirdProductsByCityId(@Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId);

    int deleteByIds(@Param("ids") List<Long> ids);

    int deleteByTime(ThirdPartyProductsDTO dto);

}