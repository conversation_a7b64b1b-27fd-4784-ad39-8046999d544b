<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdPartyProductsMapper">

    <resultMap id="thirdPartyProductsResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="BarCode" jdbcType="VARCHAR" property="barCode"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="ThirdPartyProductSku_Id" jdbcType="VARCHAR" property="productSkuId"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="Specification" jdbcType="VARCHAR" property="specification"/>
        <result column="SpecificationQuantity" jdbcType="DECIMAL" property="specificationQuantity"/>
        <result column="SalesPrice" jdbcType="DECIMAL" property="salesPrice"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="TheBigUnit" jdbcType="VARCHAR" property="theBigUnit"/>
        <result column="TheSmallUnit" jdbcType="VARCHAR" property="theSmallUnit"/>
        <result column="CarefreeReturn" jdbcType="INTEGER" property="carefreeReturn"/>
        <result column="OneCategory" jdbcType="VARCHAR" property="oneCategory"/>
        <result column="TwoCategory" jdbcType="VARCHAR" property="twoCategory"/>
        <result column="ImgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SysCode" jdbcType="VARCHAR" property="sysCode"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="City_Id" jdbcType="VARCHAR" property="cityId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ThirdInfo_Id" jdbcType="VARCHAR" property="thirdinfoId"/>
        <result column="ThirdSpecification_Id" jdbcType="VARCHAR" property="thirdspecificationId"/>
        <result column="ProductNo" jdbcType="VARCHAR" property="productNo"/>
        <result column="ProductFeature" jdbcType="INTEGER" property="productFeature"/>

    </resultMap>

    <sql id="thirdPartyProductsColumns">
        Id, ProductName, BarCode, City_Id, state, ThirdPartyProductSku_Id, Brand, Specification,
        SpecificationQuantity, SalesPrice, CostPrice, TheBigUnit, TheSmallUnit, CarefreeReturn,
        OneCategory, TwoCategory, ImgUrl, Remark, SysCode, CreateUser, CreateTime,
        LastUpdateUser, LastUpdateTime,Warehouse_Id,ThirdInfo_Id,ThirdSpecification_Id,ProductNo,ProductFeature
    </sql>

    <select id="detail" resultMap="thirdPartyProductsResultMap">
        SELECT
        <include refid="thirdPartyProductsColumns"/>
        FROM ThirdPartyProducts
        <where>
            Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <insert id="insert">
        INSERT INTO ThirdPartyProducts(
        Id,
        ProductName,
        BarCode,
        City_Id,
        state,
        ThirdPartyProductSku_Id,
        Brand,
        Specification,
        SpecificationQuantity,
        SalesPrice,
        CostPrice,
        TheBigUnit,
        TheSmallUnit,
        CarefreeReturn,
        OneCategory,
        TwoCategory,
        ImgUrl,
        Remark,
        SysCode,
        Warehouse_Id,
        ThirdInfo_Id,
        ThirdSpecification_Id,
        ProductNo,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        ProductFeature
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{productName,jdbcType=VARCHAR},
        #{barCode,jdbcType=VARCHAR},
        #{cityId,jdbcType=VARCHAR},
        #{state,jdbcType=TINYINT},
        #{productSkuId,jdbcType=VARCHAR},
        #{brand,jdbcType=VARCHAR},
        #{specification,jdbcType=VARCHAR},
        #{specificationQuantity,jdbcType=DECIMAL},
        #{salesPrice,jdbcType=DECIMAL},
        #{costPrice,jdbcType=DECIMAL},
        #{theBigUnit,jdbcType=VARCHAR},
        #{theSmallUnit,jdbcType=VARCHAR},
        #{carefreeReturn,jdbcType=INTEGER},
        #{oneCategory,jdbcType=VARCHAR},
        #{twoCategory,jdbcType=VARCHAR},
        #{imgUrl,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{sysCode,jdbcType=VARCHAR},
        #{warehouseId,jdbcType=INTEGER},
        #{thirdinfoId,jdbcType=VARCHAR},
        #{thirdspecificationId,jdbcType=VARCHAR},
        #{productNo,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR},
        NOW(),
        #{lastUpdateUser,jdbcType=VARCHAR},
        NOW(),
        #{productFeature,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO ThirdPartyProducts(
        Id,
        ProductName,
        BarCode,
        City_Id,
        state,
        ThirdPartyProductSku_Id,
        Brand,
        Specification,
        SpecificationQuantity,
        SalesPrice,
        CostPrice,
        TheBigUnit,
        TheSmallUnit,
        CarefreeReturn,
        OneCategory,
        TwoCategory,
        ImgUrl,
        Remark,
        SysCode,
        Warehouse_Id,
        ThirdInfo_Id,
        ThirdSpecification_Id,
        ProductNo,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        ProductFeature
        )
        VALUES
        <foreach collection="list" item="thirdPartyProduct" separator=",">
            (
            #{thirdPartyProduct.id,jdbcType=BIGINT},
            #{thirdPartyProduct.productName,jdbcType=VARCHAR},
            #{thirdPartyProduct.barCode,jdbcType=VARCHAR},
            #{thirdPartyProduct.cityId,jdbcType=VARCHAR},
            #{thirdPartyProduct.state,jdbcType=TINYINT},
            #{thirdPartyProduct.productSkuId,jdbcType=VARCHAR},
            #{thirdPartyProduct.brand,jdbcType=VARCHAR},
            #{thirdPartyProduct.specification,jdbcType=VARCHAR},
            #{thirdPartyProduct.specificationQuantity,jdbcType=DECIMAL},
            #{thirdPartyProduct.salesPrice,jdbcType=DECIMAL},
            #{thirdPartyProduct.costPrice,jdbcType=DECIMAL},
            #{thirdPartyProduct.theBigUnit,jdbcType=VARCHAR},
            #{thirdPartyProduct.theSmallUnit,jdbcType=VARCHAR},
            #{thirdPartyProduct.carefreeReturn,jdbcType=INTEGER},
            #{thirdPartyProduct.oneCategory,jdbcType=VARCHAR},
            #{thirdPartyProduct.twoCategory,jdbcType=VARCHAR},
            #{thirdPartyProduct.imgUrl,jdbcType=VARCHAR},
            #{thirdPartyProduct.remark,jdbcType=VARCHAR},
            #{thirdPartyProduct.sysCode,jdbcType=VARCHAR},
            #{thirdPartyProduct.warehouseId,jdbcType=INTEGER},
            #{thirdPartyProduct.thirdinfoId,jdbcType=VARCHAR},
            #{thirdPartyProduct.thirdspecificationId,jdbcType=VARCHAR},
            #{thirdPartyProduct.productNo,jdbcType=VARCHAR},
            #{thirdPartyProduct.createUser,jdbcType=VARCHAR},
            NOW(),
            #{thirdPartyProduct.lastUpdateUser,jdbcType=VARCHAR},
            NOW(),
            #{thirdPartyProduct.productFeature,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE ThirdPartyProducts
        <set>
            <if test="productName!= null and productName!= ''">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="barCode!= null and barCode!= ''">
                BarCode = #{barCode,jdbcType=VARCHAR},
            </if>
            <if test="cityId!= null and cityId!= ''">
                City = #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="state!= null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="productSkuId!= null and productSkuId!= ''">
                ThirdPartyProductSku_Id = #{productSkuId,jdbcType=VARCHAR},
            </if>
            <if test="brand!= null and brand!= ''">
                Brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="specification!= null and specification!= ''">
                Specification = #{specification,jdbcType=VARCHAR},
            </if>
            <if test="specificationQuantity!= null">
                SpecificationQuantity = #{specificationQuantity,jdbcType=DECIMAL},
            </if>
            <if test="salesPrice!= null">
                SalesPrice = #{salesPrice,jdbcType=DECIMAL},
            </if>
            <if test="costPrice!= null">
                CostPrice = #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="theBigUnit!= null and theBigUnit!= ''">
                TheBigUnit = #{theBigUnit,jdbcType=VARCHAR},
            </if>
            <if test="theSmallUnit!= null and theSmallUnit!= ''">
                TheSmallUnit = #{theSmallUnit,jdbcType=VARCHAR},
            </if>
            <if test="carefreeReturn!= null">
                CarefreeReturn = #{carefreeReturn,jdbcType=INTEGER},
            </if>
            <if test="oneCategory!= null and oneCategory!= ''">
                OneCategory = #{oneCategory,jdbcType=VARCHAR},
            </if>
            <if test="twoCategory!= null and twoCategory!= ''">
                TwoCategory = #{twoCategory,jdbcType=VARCHAR},
            </if>
            <if test="imgUrl!= null and imgUrl!= ''">
                ImgUrl = #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="remark!= null and remark!= ''">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sysCode!= null and sysCode!= ''">
                SysCode = #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser!= null and lastUpdateUser!= ''">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId!= null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="thirdinfoId!= null and thirdinfoId!= ''">
                ThirdInfo_Id = #{thirdinfoId,jdbcType=VARCHAR},
            </if>
            <if test="thirdspecificationId!= null and thirdspecificationId!= ''">
                ThirdSpecification_Id = #{thirdspecificationId,jdbcType=VARCHAR},
            </if>
            <if test="productNo!= null and productNo!= ''">
                ProductNo = #{productNo,jdbcType=VARCHAR},
            </if>
            <if test="productFeature!= null">
                ProductFeature = #{productFeature,jdbcType=INTEGER},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="delete">
        DELETE FROM ThirdPartyProducts
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>


    <select id="listBySkuIds" resultMap="thirdPartyProductsResultMap">
        SELECT
        <include refid="thirdPartyProductsColumns"/>
        FROM ThirdPartyProducts
        <where>
            <if test="list != null and list.size() > 0">
                ThirdPartyProductSku_Id in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="cityId != null and cityId != ''">
                AND City_Id = #{cityId, jdbcType=VARCHAR}
            </if>
            <if test="warehouseId!= null">
                AND Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <select id="listThirdProductsByCityId" resultMap="thirdPartyProductsResultMap">
        SELECT
        <include refid="thirdPartyProductsColumns"/>
        FROM ThirdPartyProducts
        <where>
            <if test="cityId!= null">
                AND City_Id = #{cityId,jdbcType=VARCHAR}
            </if>
            <if test="warehouseId!= null">
                AND Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="deleteByIds">
        DELETE FROM ThirdPartyProducts
        WHERE Id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <delete id="deleteByTime">
        DELETE FROM ThirdPartyProducts
        WHERE 1=1
        <if test="createTime != null">
            and <![CDATA[ CreateTime >=  #{createTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="lastUpdateTime != null">
            and <![CDATA[ CreateTime <=  #{lastUpdateTime,jdbcType=TIMESTAMP} ]]>
        </if>
    </delete>
</mapper>