package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import java.util.List;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsRelevancePO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductAndRefDetailDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsAndRelevanceDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsRelevancePageDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdProductsRelevanceQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdProductsRelevanceWithSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdRelQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThridPartyQueryDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019-12-17
 */
public interface ThirdPartyProductsRelevanceMapper {

    ThirdPartyProductsRelevancePO detail(Long id);

    PageResult<ThirdPartyProductsAndRelevanceDTO>
        listPage(ThirdPartyProductsRelevancePageDTO thirdPartyProductsRelevance);

    PageResult<ThirdProductsRelevanceWithSkuDTO> pageList(ThirdProductsRelevanceQueryDTO thirdPartyProductsRelevance);

    int insert(ThirdPartyProductsRelevancePO thirdPartyProductsRelevance);

    int insertBatch(@Param("list") List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevances);

    int update(ThirdPartyProductsRelevancePO thirdPartyProductsRelevance);

    int delete(ThirdPartyProductsRelevancePO thirdPartyProductsRelevance);

    List<ThirdPartyProductsRelevancePO> listBySkuIds(List<String> skuIds);

    List<ThirdPartyProductsRelevancePO> listThirdRelevances(ThirdRelQueryDTO queryDTO);

    int deleteByIds(@Param("ids") List<Long> ids);

    int deleteByTime(ThirdPartyProductsDTO dto);

    PageResult<ThirdPartyProductAndRefDetailDTO> listProdcutAndRef(ThridPartyQueryDTO queryDTO);

    PageResult<ThirdPartyProductAndRefDetailDTO> listThirdSkuId(ThridPartyQueryDTO queryDTO);

    List<ThirdPartyProductsRelevancePO> selectByProductNames(
        @Param("thirdProductNameList") List<String> thirdProductNameList, @Param("cityId") Integer cityId,
        @Param("warehouseId") Integer warehouseId, @Param("sysCode") String sysCode);

}