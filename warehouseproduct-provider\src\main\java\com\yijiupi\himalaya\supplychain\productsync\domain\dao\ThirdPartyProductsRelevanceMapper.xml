<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdPartyProductsRelevanceMapper">

    <resultMap id="thirdPartyProductsRelevanceResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsRelevancePO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="BarCode" jdbcType="VARCHAR" property="barCode"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="ThirdPartyProductSku_Id" jdbcType="VARCHAR" property="productSkuId"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="Specification" jdbcType="VARCHAR" property="specification"/>
        <result column="SpecificationQuantity" jdbcType="DECIMAL" property="specificationQuantity"/>
        <result column="SalesPrice" jdbcType="DECIMAL" property="salesPrice"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="TheBigUnit" jdbcType="VARCHAR" property="theBigUnit"/>
        <result column="TheSmallUnit" jdbcType="VARCHAR" property="theSmallUnit"/>
        <result column="CarefreeReturn" jdbcType="INTEGER" property="carefreeReturn"/>
        <result column="OneCategory" jdbcType="VARCHAR" property="oneCategory"/>
        <result column="TwoCategory" jdbcType="VARCHAR" property="twoCategory"/>
        <result column="ImgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SysCode" jdbcType="VARCHAR" property="sysCode"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productsSkuId"/>
        <result column="Specification_Id" jdbcType="BIGINT" property="specificationId"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
    </resultMap>

    <resultMap id="thirdPartyProductsAndRelevanceResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsAndRelevanceDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="BarCode" jdbcType="VARCHAR" property="barCode"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="ThirdPartyProductSku_Id" jdbcType="VARCHAR" property="productSkuId"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="Specification" jdbcType="VARCHAR" property="specification"/>
        <result column="SpecificationQuantity" jdbcType="DECIMAL" property="specificationQuantity"/>
        <result column="SalesPrice" jdbcType="DECIMAL" property="salesPrice"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="TheBigUnit" jdbcType="VARCHAR" property="theBigUnit"/>
        <result column="TheSmallUnit" jdbcType="VARCHAR" property="theSmallUnit"/>
        <result column="CarefreeReturn" jdbcType="INTEGER" property="carefreeReturn"/>
        <result column="OneCategory" jdbcType="VARCHAR" property="oneCategory"/>
        <result column="TwoCategory" jdbcType="VARCHAR" property="twoCategory"/>
        <result column="ImgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SysCode" jdbcType="VARCHAR" property="sysCode"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productsSkuId"/>
        <result column="Specification_Id" jdbcType="BIGINT" property="specificationId"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="ProductFeature" jdbcType="INTEGER" property="productFeature"/>
        <result column="City_Id" jdbcType="VARCHAR" property="cityId"/>
        <result column="RelevanceId" jdbcType="BIGINT" property="relevanceId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ThirdInfo_Id" jdbcType="VARCHAR" property="thirdinfoId"/>
        <result column="ThirdSpecification_Id" jdbcType="VARCHAR" property="thirdspecificationId"/>
        <result column="ProductNo" jdbcType="VARCHAR" property="productNo"/>
    </resultMap>

    <sql id="thirdPartyProductsRelevanceColumns">
        Id, ProductName, BarCode, City, state, ThirdPartyProductSku_Id, Brand, Specification,
        SpecificationQuantity, SalesPrice, CostPrice, TheBigUnit, TheSmallUnit, CarefreeReturn,
        OneCategory, TwoCategory, ImgUrl, Remark, SysCode, ProductInfo_Id, ProductSku_Id,
        Specification_Id, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, City_Id
    </sql>

    <select id="detail" resultMap="thirdPartyProductsRelevanceResultMap">
        SELECT
        <include refid="thirdPartyProductsRelevanceColumns"/>
        FROM ThirdPartyProductsRelevance
        <where>
            Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="pageList"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdProductsRelevanceWithSkuDTO">
        SELECT
        Id, ProductName, BarCode, City, state, ThirdPartyProductSku_Id as productSkuId, Brand, Specification,
        SpecificationQuantity, SalesPrice, CostPrice, TheBigUnit, TheSmallUnit, CarefreeReturn,
        OneCategory, TwoCategory, ImgUrl, Remark, SysCode, ProductInfo_Id as productInfoId, ProductSku_Id as
        productsSkuId,
        Specification_Id as specificationId, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, City_Id as cityId,
        Warehouse_Id as warehouseId
        FROM ThirdPartyProductsRelevance
        <where>
            <if test="cityId!= null">
                and City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId!= null">
                and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="barCode!=null and barCode!=''">
                and BarCode = #{barCode,jdbcType=VARCHAR}
            </if>
            <if test="productName!=null and productName!=''">
                and ProductName = #{productName,jdbcType=VARCHAR}
            </if>
            <if test="specification!=null and specification!=''">
                and Specification = #{specification,jdbcType=VARCHAR}
            </if>
            <if test="sysCode != null and sysCode !=''">
                AND SysCode = #{sysCode,jdbcType=VARCHAR}
            </if>
            <if test="state!= null">
                AND State = #{state,jdbcType=TINYINT}
            </if>
            <if test="productsSkuId!= null">
                AND ProductSku_Id = #{productsSkuId,jdbcType=INTEGER}
            </if>
            <if test="productSkuId!= null and productSkuId !=''">
                AND ThirdPartyProductSku_Id = #{productSkuId,jdbcType=VARCHAR}
            </if>
            <if test="sysCodeList != null and sysCodeList.size() > 0 ">
                and SysCode in
                <foreach collection="sysCodeList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listPage" resultMap="thirdPartyProductsAndRelevanceResultMap">
        SELECT
        tpp.Id, tpp.ProductName, tpp.BarCode, tppr.City, tpp.state, tpp.ThirdPartyProductSku_Id, tpp.Brand,
        tpp.Specification,
        tpp.SpecificationQuantity, tpp.SalesPrice, tpp.CostPrice, tpp.TheBigUnit, tpp.TheSmallUnit, tpp.CarefreeReturn,
        tpp.OneCategory, tpp.TwoCategory, tpp.ImgUrl, tpp.Remark, tpp.SysCode, tppr.ProductInfo_Id, tppr.ProductSku_Id,
        tppr.Specification_Id, tpp.CreateUser, tpp.CreateTime, tpp.LastUpdateUser, tpp.LastUpdateTime, tpp.City_Id,
        tppr.Id RelevanceId,tpp.Warehouse_Id,tpp.ThirdInfo_Id,tpp.ThirdSpecification_Id,tpp.ProductNo,tpp.ProductFeature
        FROM
        thirdpartyproducts tpp LEFT JOIN
        ThirdPartyProductsRelevance tppr
        ON tpp.ThirdPartyProductSku_Id = tppr.ThirdPartyProductSku_Id
        <if test="cityId!= null and cityId !='' ">
            and tppr.City_Id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId!= null">
            and tppr.Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        </if>

        <where>
            <if test="id!= null">
                tpp.Id = #{id,jdbcType=BIGINT}
            </if>
            <if test="productName!= null and productName != ''">
                AND tpp.ProductName LIKE concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="barCode!= null and barCode !='' ">
                AND tpp.BarCode = #{barCode,jdbcType=VARCHAR}
            </if>
            <if test="cityId!= null and cityId !='' ">
                AND tpp.City_Id = #{cityId,jdbcType=VARCHAR}
            </if>
            <if test="warehouseId!= null">
                AND tpp.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="thirdinfoId!= null and thirdinfoId !='' ">
                AND tpp.ThirdInfo_Id = #{thirdinfoId,jdbcType=VARCHAR}
            </if>
            <if test="thirdspecificationId!= null and thirdspecificationId !='' ">
                AND tpp.ThirdSpecification_Id = #{thirdspecificationId,jdbcType=VARCHAR}
            </if>
            <if test="productNo!= null and productNo !='' ">
                AND tpp.ProductNo = #{productNo,jdbcType=VARCHAR}
            </if>
            <if test="state!= null">
                AND tpp.State = #{state,jdbcType=TINYINT}
            </if>
            <if test="productSkuId!= null and productSkuId !=''">
                AND tpp.ThirdPartyProductSku_Id = #{productSkuId,jdbcType=VARCHAR}
            </if>
            <if test="brand != null and brand != ''">
                AND tpp.Brand = #{brand,jdbcType=VARCHAR}
            </if>
            <if test="specification != null and specification != ''">
                AND tpp.Specification = #{specification,jdbcType=VARCHAR}
            </if>
            <if test="specificationQuantity != null">
                AND tpp.SpecificationQuantity = #{specificationQuantity,jdbcType=DECIMAL}
            </if>
            <if test="salesPrice != null">
                AND tpp.SalesPrice = #{salesPrice,jdbcType=DECIMAL}
            </if>
            <if test="costPrice != null">
                AND tpp.CostPrice = #{costPrice,jdbcType=DECIMAL}
            </if>
            <if test="theBigUnit != null and theBigUnit != ''">
                AND tpp.TheBigUnit = #{theBigUnit,jdbcType=VARCHAR}
            </if>
            <if test="theSmallUnit != null and theSmallUnit != '' ">
                AND tpp.TheSmallUnit = #{theSmallUnit,jdbcType=VARCHAR}
            </if>
            <if test="oneCategory != null and oneCategory != ''">
                AND tpp.OneCategory = #{oneCategory,jdbcType=VARCHAR}
            </if>
            <if test="twoCategory != null and twoCategory !='' ">
                AND tpp.TwoCategory = #{twoCategory,jdbcType=VARCHAR}
            </if>
            <if test="imgUrl != null and imgUrl != '' ">
                AND tpp.ImgUrl = #{imgUrl,jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != ''">
                AND tpp.Remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="sysCode != null and sysCode !='' ">
                AND tpp.SysCode = #{sysCode,jdbcType=VARCHAR}
            </if>
            <if test="onlyShowUnrefProducts">
                AND tppr.Id IS NULL
            </if>

        </where>
    </select>

    <select id="listProdcutAndRef"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductAndRefDetailDTO">
        SELECT
        tpp.Id,
        tpp.ProductName ThirdProductName,
        tpp.BarCode ThirdBarCode,
        tppr.City,
        tpp.state,
        tpp.ThirdPartyProductSku_Id as ThirdPartyProductSkuId,
        tpp.Brand ThirdBrand,
        tpp.Specification ThirdSpecification,
        tpp.SpecificationQuantity ThirdSpecificationQuantity,
        tpp.OneCategory ThirdOneCategory,
        tpp.TwoCategory ThirdTwoCategory,
        tpp.Remark,
        tpp.SysCode,
        tpp.City_Id as CityId,
        tpp.Warehouse_Id as WarehouseId,
        tpp.ThirdInfo_Id as ThirdInfoId,
        tpp.ThirdSpecification_Id as ThirdSpecificationId,
        tpp.ProductNo,
        tppr.Id RelevanceId,
        tppr.ProductInfo_Id as ProductInfoId,
        tppr.ProductSku_Id as ProductsSkuId,
        tpp.ProductFeature as productFeature,
        psku.NAME ProductName,
        psku.ProductSpecification_Id as specificationId,
        psku.SpecificationName,
        psku.ProductBrand,
        psku.PackageQuantity,
        psku.Company_Id as OwnerId,
        psku.OwnerName,
        psku.ProductState,
        info.StatisticsCategoryName,
        info.BottleCode
        FROM
        thirdpartyproducts tpp
        INNER JOIN ThirdPartyProductsRelevance tppr ON tpp.ThirdPartyProductSku_Id = tppr.ThirdPartyProductSku_Id
        <if test="cityId!= null">
            and tppr.City_Id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId!= null">
            and tppr.Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        </if>
        INNER JOIN productsku psku ON psku.ProductSku_Id = tppr.ProductSku_Id
        INNER JOIN productinfo info ON psku.ProductInfo_Id = info.Id
        LEFT JOIN productinfocategory cg ON psku.ProductInfoCategory_Id = cg.Id
        WHERE 1 = 1
        <if test="beginTime != null">
            and <![CDATA[ tppr.CreateTime >=  #{beginTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null">
            and <![CDATA[ tppr.CreateTime <=  #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="cityId!= null">
            and tpp.City_Id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId!= null">
            and tpp.Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="listThirdSkuId"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductAndRefDetailDTO">
        SELECT
        tpp.ThirdPartyProductSku_Id AS thirdProductSkuId
        FROM
        thirdpartyproducts tpp
        INNER JOIN ThirdPartyProductsRelevance tppr ON tpp.ThirdPartyProductSku_Id = tppr.ThirdPartyProductSku_Id
        <if test="cityId!= null">
            and tppr.City_Id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId!= null">
            and tppr.Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        </if>
        WHERE 1 = 1
        <if test="beginTime != null">
            and <![CDATA[ tppr.CreateTime >=  #{beginTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null">
            and <![CDATA[ tppr.CreateTime <=  #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="cityId!= null">
            and tpp.City_Id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId!= null">
            and tpp.Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insert">
        INSERT INTO ThirdPartyProductsRelevance(
        Id,
        ProductName,
        BarCode,
        City,
        state,
        ThirdPartyProductSku_Id,
        Brand,
        Specification,
        SpecificationQuantity,
        SalesPrice,
        CostPrice,
        TheBigUnit,
        TheSmallUnit,
        CarefreeReturn,
        OneCategory,
        TwoCategory,
        ImgUrl,
        Remark,
        SysCode,
        ProductInfo_Id,
        ProductSku_Id,
        Specification_Id,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        City_Id,
        Warehouse_Id
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{productName,jdbcType=VARCHAR},
        #{barCode,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR},
        #{state,jdbcType=TINYINT},
        #{productSkuId,jdbcType=VARCHAR},
        #{brand,jdbcType=VARCHAR},
        #{specification,jdbcType=VARCHAR},
        #{specificationQuantity,jdbcType=DECIMAL},
        #{salesPrice,jdbcType=DECIMAL},
        #{costPrice,jdbcType=DECIMAL},
        #{theBigUnit,jdbcType=VARCHAR},
        #{theSmallUnit,jdbcType=VARCHAR},
        #{carefreeReturn,jdbcType=INTEGER},
        #{oneCategory,jdbcType=VARCHAR},
        #{twoCategory,jdbcType=VARCHAR},
        #{imgUrl,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{sysCode,jdbcType=VARCHAR},
        #{productInfoId,jdbcType=BIGINT},
        #{productsSkuId,jdbcType=BIGINT},
        #{specificationId,jdbcType=BIGINT},
        #{createUser,jdbcType=VARCHAR},
        NOW(),
        #{lastUpdateUser,jdbcType=VARCHAR},
        NOW(),
        #{cityId,jdbcType=INTEGER},
        #{warehouseId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO ThirdPartyProductsRelevance(
        Id,
        ThirdPartyProductSku_Id,
        ProductInfo_Id,
        ProductSku_Id,
        Specification_Id,
        CreateTime,
        LastUpdateTime,
        City_Id,
        Warehouse_Id,
        ProductName,
        Specification,
        SysCode,
        SpecificationQuantity
        )
        VALUES
        <foreach collection="list" item="thirdPartyProductsRelevance" separator=",">
            (
            #{thirdPartyProductsRelevance.id,jdbcType=BIGINT},
            #{thirdPartyProductsRelevance.productSkuId,jdbcType=VARCHAR},
            #{thirdPartyProductsRelevance.productInfoId,jdbcType=BIGINT},
            #{thirdPartyProductsRelevance.productsSkuId,jdbcType=BIGINT},
            #{thirdPartyProductsRelevance.specificationId,jdbcType=BIGINT},
            NOW(),
            NOW(),
            #{thirdPartyProductsRelevance.cityId,jdbcType=INTEGER},
            #{thirdPartyProductsRelevance.warehouseId,jdbcType=INTEGER},
            #{thirdPartyProductsRelevance.productName,jdbcType=VARCHAR},
            #{thirdPartyProductsRelevance.specification,jdbcType=VARCHAR},
            #{thirdPartyProductsRelevance.sysCode,jdbcType=VARCHAR},
            #{thirdPartyProductsRelevance.specificationQuantity,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE ThirdPartyProductsRelevance
        <set>
            <if test="productName!= null and productName!= ''">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="barCode!= null and barCode!= ''">
                BarCode = #{barCode,jdbcType=VARCHAR},
            </if>
            <if test="city!= null and city!= ''">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="state!= null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="productSkuId!= null and productSkuId!= ''">
                ThirdPartyProductSku_Id = #{productSkuId,jdbcType=VARCHAR},
            </if>
            <if test="brand!= null and brand!= ''">
                Brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="specification!= null and specification!= ''">
                Specification = #{specification,jdbcType=VARCHAR},
            </if>
            <if test="specificationQuantity!= null">
                SpecificationQuantity = #{specificationQuantity,jdbcType=DECIMAL},
            </if>
            <if test="salesPrice!= null">
                SalesPrice = #{salesPrice,jdbcType=DECIMAL},
            </if>
            <if test="costPrice!= null">
                CostPrice = #{costPrice,jdbcType=DECIMAL},
            </if>
            <if test="theBigUnit!= null and theBigUnit!= ''">
                TheBigUnit = #{theBigUnit,jdbcType=VARCHAR},
            </if>
            <if test="theSmallUnit!= null and theSmallUnit!= ''">
                TheSmallUnit = #{theSmallUnit,jdbcType=VARCHAR},
            </if>
            <if test="carefreeReturn!= null">
                CarefreeReturn = #{carefreeReturn,jdbcType=INTEGER},
            </if>
            <if test="oneCategory!= null and oneCategory!= ''">
                OneCategory = #{oneCategory,jdbcType=VARCHAR},
            </if>
            <if test="twoCategory!= null and twoCategory!= ''">
                TwoCategory = #{twoCategory,jdbcType=VARCHAR},
            </if>
            <if test="imgUrl!= null and imgUrl!= ''">
                ImgUrl = #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="remark!= null and remark!= ''">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sysCode!= null and sysCode!= ''">
                SysCode = #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="productInfoId!= null">
                ProductInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="productsSkuId!= null">
                ProductSku_Id = #{productsSkuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId!= null">
                Specification_Id = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateUser!= null and lastUpdateUser!= ''">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="cityId!= null and cityId!= ''">
                City_Id = #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId!= null">
                Warehouse_Id = #{warehouseId,jdbcType=VARCHAR},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="delete">
        DELETE FROM ThirdPartyProductsRelevance
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="listBySkuIds" resultMap="thirdPartyProductsRelevanceResultMap">
        SELECT
        <include refid="thirdPartyProductsRelevanceColumns"/>
        FROM ThirdPartyProductsRelevance
        <where>
            <if test="list != null and list.size() > 0">
                ThirdPartyProductSku_Id in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>


    <select id="listThirdRelevances" resultMap="thirdPartyProductsRelevanceResultMap">
        SELECT
        <include refid="thirdPartyProductsRelevanceColumns"/>
        FROM ThirdPartyProductsRelevance
        <where>
            <if test="id!= null">
                Id = #{id,jdbcType=BIGINT}
            </if>
            <if test="cityId!= null and cityId !='' ">
                AND City_Id = #{cityId,jdbcType=VARCHAR}
            </if>
            <if test="warehouseId!= null">
                AND Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
            </if>
            <if test="thirdProductSkuId!= null and thirdProductSkuId !=''">
                AND ThirdPartyProductSku_Id = #{thirdProductSkuId,jdbcType=VARCHAR}
            </if>
            <if test="thirdProductSkuIdList != null and thirdProductSkuIdList.size() > 0">
                AND ThirdPartyProductSku_Id in
                <foreach collection="thirdProductSkuIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="productInfoId!= null">
                AND ProductInfo_Id = #{productInfoId,jdbcType=BIGINT}
            </if>
            <if test="productSkuId!= null">
                AND ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="specificationId!= null">
                AND Specification_Id = #{specificationId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <delete id="deleteByIds">
        DELETE FROM ThirdPartyProductsRelevance
        WHERE Id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <delete id="deleteByTime">
        DELETE FROM ThirdPartyProductsRelevance
        WHERE 1=1
        <if test="createTime != null">
            and <![CDATA[ CreateTime >=  #{createTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="lastUpdateTime != null">
            and <![CDATA[ CreateTime <=  #{lastUpdateTime,jdbcType=TIMESTAMP} ]]>
        </if>
    </delete>

    <select id="selectByProductNames" resultMap="thirdPartyProductsRelevanceResultMap">
        SELECT
        <include refid="thirdPartyProductsRelevanceColumns"/>
        FROM ThirdPartyProductsRelevance
        WHERE ProductName in
        <foreach collection="thirdProductNameList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND City_Id = #{cityId,jdbcType=VARCHAR}
        AND Warehouse_Id = #{warehouseId,jdbcType=VARCHAR}
        AND SysCode = #{sysCode,jdbcType=VARCHAR}

    </select>


</mapper>