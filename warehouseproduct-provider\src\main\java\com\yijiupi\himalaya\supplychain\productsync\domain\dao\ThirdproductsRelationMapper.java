package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-13
 */
public interface ThirdproductsRelationMapper {

    ThirdproductsRelationDTO detail(String id);

    List<ThirdproductsRelationDTO> list(ThirdproductsRelationQueryDTO thirdproductsrelation);

    int insert(ThirdproductsRelationDTO thirdproductsrelation);

    int insertBatch(List<ThirdproductsRelationDTO> thirdproductsrelations);

    int update(ThirdproductsRelationDTO thirdproductsrelation);

    int delete(ThirdproductsRelationDTO thirdproductsrelation);

    int deleteByIds(@Param("ids") List<Long> ids);

}