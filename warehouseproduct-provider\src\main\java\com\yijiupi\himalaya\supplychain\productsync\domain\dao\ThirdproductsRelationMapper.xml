<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.ThirdproductsRelationMapper">

    <resultMap id="thirdproductsrelationResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="thirdskuId" jdbcType="VARCHAR" property="thirdskuId"/>
        <result column="thirdinfoId" jdbcType="VARCHAR" property="thirdinfoId"/>
        <result column="thirdProductName" jdbcType="VARCHAR" property="thirdProductName"/>
        <result column="thirdspecificationId" jdbcType="VARCHAR" property="thirdspecificationId"/>
        <result column="productinfoId" jdbcType="BIGINT" property="productinfoId"/>
        <result column="productskuId" jdbcType="BIGINT" property="productskuId"/>
        <result column="specificationId" jdbcType="BIGINT" property="specificationId"/>
        <result column="orgId" jdbcType="INTEGER" property="orgId"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="sysCode" jdbcType="VARCHAR" property="sysCode"/>
        <result column="sysName" jdbcType="VARCHAR" property="sysName"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="ProductFeature" jdbcType="INTEGER" property="productFeature"/>
        <result column="ConvertSaleSpecQuantity" jdbcType="DECIMAL" property="convertSaleSpecQuantity"/>
        <result column="ConvertSaleUnit" jdbcType="VARCHAR" property="convertSaleUnit"/>

    </resultMap>

    <sql id="thirdproductsrelationColumns">
        Id AS "id",
        ThirdSku_Id AS "thirdskuId",
        ThirdInfo_Id AS "thirdinfoId",
        ThirdProductName AS "thirdProductName",
        ThirdSpecification_Id AS "thirdspecificationId",
        ProductInfo_Id AS "productinfoId",
        ProductSku_Id AS "productskuId",
        Specification_Id AS "specificationId",
        Org_Id AS "orgId",
        City_Id AS "cityId",
        Warehouse_Id AS "warehouseId",
        SysCode AS "sysCode",
        SysName AS "sysName",
        CreateTime AS "createTime",
        LastUpdateTime AS "lastUpdateTime",
        ProductFeature AS "productFeature",
        ConvertSaleSpecQuantity,
        ConvertSaleUnit
    </sql>


    <select id="detail" resultMap="thirdproductsrelationResultMap">
        SELECT
        <include refid="thirdproductsrelationColumns"/>
        FROM thirdproductsrelation
        <where>
            Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="list" resultMap="thirdproductsrelationResultMap">
        SELECT
        <include refid="thirdproductsrelationColumns"/>
        FROM thirdproductsrelation
        <where>
            <if test="thirdskuId!= null and thirdskuId!= ''">
                and ThirdSku_Id = #{thirdskuId,jdbcType=VARCHAR}
            </if>
            <if test="thirdinfoId!= null and thirdinfoId!= ''">
                and ThirdInfo_Id = #{thirdinfoId,jdbcType=VARCHAR}
            </if>
            <if test="thirdspecificationId!= null  and thirdspecificationId!= ''">
                and ThirdSpecification_Id = #{thirdspecificationId,jdbcType=VARCHAR}
            </if>
            <if test="productinfoId!= null">
                and ProductInfo_Id = #{productinfoId,jdbcType=BIGINT}
            </if>
            <if test="productskuId!= null">
                and ProductSku_Id = #{productskuId,jdbcType=BIGINT}
            </if>
            <if test="specificationId!= null">
                and Specification_Id = #{specificationId,jdbcType=BIGINT}
            </if>
            <if test="orgId!= null">
                and Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="cityId!= null">
                and City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId!= null">
                and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="sysCode!= null and sysCode!= ''">
                and SysCode = #{sysCode,jdbcType=VARCHAR}
            </if>
            <if test="sysName!= null and sysName!= ''">
                and SysName = #{sysName,jdbcType=VARCHAR}
            </if>
            <if test="thirdskuIdList != null and thirdskuIdList.size() > 0">
                and ThirdSku_Id in
                <foreach collection="thirdskuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="skuIdList != null and skuIdList.size() > 0">
                and ProductSku_Id in
                <foreach collection="skuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

        </where>
    </select>

    <insert id="insert">
        INSERT INTO thirdproductsrelation(
        Id,
        ThirdSku_Id,
        ThirdInfo_Id,
        ThirdProductName,
        ThirdSpecification_Id,
        ProductInfo_Id,
        ProductSku_Id,
        Specification_Id,
        Org_Id,
        City_Id,
        Warehouse_Id,
        SysCode,
        SysName,
        CreateTime,
        LastUpdateTime,
        ProductFeature,
        ConvertSaleSpecQuantity,
        ConvertSaleUnit
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{thirdskuId,jdbcType=VARCHAR},
        #{thirdinfoId,jdbcType=VARCHAR},
        #{thirdProductName,jdbcType=VARCHAR},
        #{thirdspecificationId,jdbcType=VARCHAR},
        #{productinfoId,jdbcType=BIGINT},
        #{productskuId,jdbcType=BIGINT},
        #{specificationId,jdbcType=BIGINT},
        #{orgId,jdbcType=INTEGER},
        #{cityId,jdbcType=INTEGER},
        #{warehouseId,jdbcType=INTEGER},
        #{sysCode,jdbcType=VARCHAR},
        #{sysName,jdbcType=VARCHAR},
        now(),
        now(),
        #{productFeature,jdbcType=INTEGER},
        #{convertSaleSpecQuantity,jdbcType=DECIMAL},
        #{convertSaleUnit,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO thirdproductsrelation(
        Id,
        ThirdSku_Id,
        ThirdInfo_Id,
        ThirdSpecification_Id,
        ProductInfo_Id,
        ThirdProductName,
        ProductSku_Id,
        Specification_Id,
        Org_Id,
        City_Id,
        Warehouse_Id,
        SysCode,
        SysName,
        CreateTime,
        LastUpdateTime,
        ProductFeature,
        ConvertSaleSpecQuantity,
        ConvertSaleUnit
        )
        VALUES
        <foreach collection="list" item="items" separator=",">
            (
            #{items.id,jdbcType=BIGINT},
            #{items.thirdskuId,jdbcType=VARCHAR},
            #{items.thirdinfoId,jdbcType=VARCHAR},
            #{items.thirdspecificationId,jdbcType=VARCHAR},
            #{items.productinfoId,jdbcType=BIGINT},
            #{items.thirdProductName,jdbcType=VARCHAR},
            #{items.productskuId,jdbcType=BIGINT},
            #{items.specificationId,jdbcType=BIGINT},
            #{items.orgId,jdbcType=INTEGER},
            #{items.cityId,jdbcType=INTEGER},
            #{items.warehouseId,jdbcType=INTEGER},
            #{items.sysCode,jdbcType=VARCHAR},
            #{items.sysName,jdbcType=VARCHAR},
            now(),
            now(),
            #{items.productFeature,jdbcType=INTEGER},
            #{items.convertSaleSpecQuantity,jdbcType=DECIMAL},
            #{items.convertSaleUnit,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE thirdproductsrelation
        <set>
            <if test="thirdskuId!= null and thirdskuId!= ''">
                ThirdSku_Id = #{thirdskuId,jdbcType=VARCHAR},
            </if>
            <if test="thirdinfoId!= null and thirdinfoId!= ''">
                ThirdInfo_Id = #{thirdinfoId,jdbcType=VARCHAR},
            </if>
            <if test="thirdProductName!= null and thirdinfoId!= ''">
                ThirdProductName = #{thirdProductName,jdbcType=VARCHAR},
            </if>
            <if test="thirdspecificationId!= null  and thirdspecificationId!= ''">
                ThirdSpecification_Id = #{thirdspecificationId,jdbcType=VARCHAR},
            </if>
            <if test="productinfoId!= null">
                ProductInfo_Id = #{productinfoId,jdbcType=BIGINT},
            </if>
            <if test="productskuId!= null">
                ProductSku_Id = #{productskuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId!= null">
                Specification_Id = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="orgId!= null">
                Org_Id = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="cityId!= null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId!= null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="sysCode!= null and sysCode!= ''">
                SysCode = #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="sysName!= null and sysName!= ''">
                SysName = #{sysName,jdbcType=VARCHAR},
            </if>
            <if test="productFeature!= null">
                ProductFeature = #{productFeature,jdbcType=INTEGER},
            </if>
            <if test="convertSaleSpecQuantity!= null">
                ConvertSaleSpecQuantity = #{convertSaleSpecQuantity,jdbcType=DECIMAL},
            </if>
            <if test="convertSaleUnit!= null">
                ConvertSaleUnit = #{convertSaleUnit,jdbcType=VARCHAR},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="delete">
        DELETE FROM thirdproductsrelation
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByIds">
        DELETE FROM thirdproductsrelation
        WHERE 1=1
        <if test="ids != null and ids.size() > 0">
            and Id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </update>

</mapper>