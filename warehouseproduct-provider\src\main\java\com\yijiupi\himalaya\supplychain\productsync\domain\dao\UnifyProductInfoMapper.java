package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductInfoSaasQueryDTO;

/**
 * 产品信息（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public interface UnifyProductInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UnifyProductInfoPO record);

    UnifyProductInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UnifyProductInfoPO record);

    PageResult<UnifyProductInfoPO> listProductInfo(ProductInfoSaasQueryDTO dto);

}