<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductInfoMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ParentOrg_Id" jdbcType="INTEGER" property="parentOrgId"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="StatisticsCategoryName" jdbcType="VARCHAR" property="statisticsCategoryName"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="shelfLife" jdbcType="INTEGER" property="shelfLife"/>
        <result column="shelfLife_Type" jdbcType="TINYINT" property="shelfLifeType"/>
        <result column="defaultImageId" jdbcType="INTEGER" property="defaultImageId"/>
        <result column="productSaleType" jdbcType="TINYINT" property="productSaleType"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="lastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, ParentOrg_Id, Brand, StatisticsCategoryName, ProductName, shelfLife, shelfLife_Type,
        defaultImageId, productSaleType, state, createTime, createUser_Id, lastUpdateTime,
        lastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductinfo
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from unifyproductinfo
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO">
        insert into unifyproductinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="parentOrgId != null">
                ParentOrg_Id,
            </if>
            <if test="brand != null">
                Brand,
            </if>
            <if test="statisticsCategoryName != null">
                StatisticsCategoryName,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="shelfLife != null">
                shelfLife,
            </if>
            <if test="shelfLifeType != null">
                shelfLife_Type,
            </if>
            <if test="defaultImageId != null">
                defaultImageId,
            </if>
            <if test="productSaleType != null">
                productSaleType,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
            <if test="createUserId != null">
                createUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="parentOrgId != null">
                #{parentOrgId,jdbcType=INTEGER},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="statisticsCategoryName != null">
                #{statisticsCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="shelfLife != null">
                #{shelfLife,jdbcType=INTEGER},
            </if>
            <if test="shelfLifeType != null">
                #{shelfLifeType,jdbcType=TINYINT},
            </if>
            <if test="defaultImageId != null">
                #{defaultImageId,jdbcType=INTEGER},
            </if>
            <if test="productSaleType != null">
                #{productSaleType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductInfoPO">
        update unifyproductinfo
        <set>
            <if test="parentOrgId != null">
                ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER},
            </if>
            <if test="brand != null">
                Brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="statisticsCategoryName != null">
                StatisticsCategoryName = #{statisticsCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="shelfLife != null">
                shelfLife = #{shelfLife,jdbcType=INTEGER},
            </if>
            <if test="shelfLifeType != null">
                shelfLife_Type = #{shelfLifeType,jdbcType=TINYINT},
            </if>
            <if test="defaultImageId != null">
                defaultImageId = #{defaultImageId,jdbcType=INTEGER},
            </if>
            <if test="productSaleType != null">
                productSaleType = #{productSaleType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                lastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listProductInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductinfo
        where ParentOrg_Id = #{parentOrgId,jdbcType=INTEGER}
        <if test="brand != null">
            Brand = #{brand,jdbcType=VARCHAR},
        </if>
        <if test="statisticsCategoryName != null">
            StatisticsCategoryName = #{statisticsCategoryName,jdbcType=VARCHAR}
        </if>
        <if test="productName != null">
            ProductName = #{productName,jdbcType=VARCHAR}
        </if>
        <if test="productSaleType != null">
            productSaleType = #{productSaleType,jdbcType=TINYINT}
        </if>
        <if test="state != null">
            state = #{state,jdbcType=TINYINT}
        </if>
        <if test="productInfoIdList != null and productInfoIdList.size() > 0">
            and Id in
            <foreach collection="productInfoIdList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

</mapper>