package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品包装单位（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public interface UnifyProductPackageMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UnifyProductPackagePO record);

    UnifyProductPackagePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UnifyProductPackagePO record);

    /**
     * 批量更新包装单位
     */
    int updateBatch(@Param("list") List<UnifyProductPackagePO> record);

    /**
     * 查询产品包装单位同时包含子包装单位
     * 
     * @return
     */
    UnifyProductPackagePO getProductPackageIncludeChild(@Param("id") Long id,
        @Param("packageQuantity") BigDecimal packageQuantity);

    List<UnifyProductPackagePO> listByProductInfoIds(@Param("productInfoIds") List<Long> productInfoIds);

}