<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductPackageMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="childPackage_Quantity" jdbcType="DECIMAL" property="childPackageQuantity"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="packageType" jdbcType="INTEGER" property="packageType"/>
        <result column="scattered" jdbcType="TINYINT" property="scattered"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="volume_height" jdbcType="DECIMAL" property="volumeHeight"/>
        <result column="volume_length" jdbcType="DECIMAL" property="volumeLength"/>
        <result column="volume_width" jdbcType="DECIMAL" property="volumeWidth"/>
        <result column="weight" jdbcType="DECIMAL" property="weight"/>
        <result column="childPackage_Id" jdbcType="BIGINT" property="childPackageId"/>
        <result column="Info_Id" jdbcType="BIGINT" property="infoId"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="lastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, childPackage_Quantity, name, packageType, scattered, state, volume_height, volume_length,
        volume_width, weight, childPackage_Id, Info_Id, createTime, createUser_Id, lastUpdateTime,
        lastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductpackage
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getProductPackageIncludeChild"
            resultType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO">
        select
        pp.childPackage_Quantity as childPackageQuantity,
        pp.name as name,
        p.name as childPackageName
        from unifyproductpackage p
        inner join unifyproductpackage pp on pp.childPackage_Id = p.id
        where p.id = #{id,jdbcType=BIGINT}
        and pp.childPackage_Quantity = #{packageQuantity,jdbcType=DECIMAL}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from unifyproductpackage
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO">
        insert into unifyproductpackage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="childPackageQuantity != null">
                childPackage_Quantity,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="packageType != null">
                packageType,
            </if>
            <if test="scattered != null">
                scattered,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="volumeHeight != null">
                volume_height,
            </if>
            <if test="volumeLength != null">
                volume_length,
            </if>
            <if test="volumeWidth != null">
                volume_width,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="childPackageId != null">
                childPackage_Id,
            </if>
            <if test="infoId != null">
                Info_Id,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
            <if test="createUserId != null">
                createUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="childPackageQuantity != null">
                #{childPackageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="packageType != null">
                #{packageType,jdbcType=INTEGER},
            </if>
            <if test="scattered != null">
                #{scattered,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="volumeHeight != null">
                #{volumeHeight,jdbcType=DECIMAL},
            </if>
            <if test="volumeLength != null">
                #{volumeLength,jdbcType=DECIMAL},
            </if>
            <if test="volumeWidth != null">
                #{volumeWidth,jdbcType=DECIMAL},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=DECIMAL},
            </if>
            <if test="childPackageId != null">
                #{childPackageId,jdbcType=BIGINT},
            </if>
            <if test="infoId != null">
                #{infoId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductPackagePO">
        update unifyproductpackage
        <set>
            <if test="childPackageQuantity != null">
                childPackage_Quantity = #{childPackageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="packageType != null">
                packageType = #{packageType,jdbcType=INTEGER},
            </if>
            <if test="scattered != null">
                scattered = #{scattered,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="volumeHeight != null">
                volume_height = #{volumeHeight,jdbcType=DECIMAL},
            </if>
            <if test="volumeLength != null">
                volume_length = #{volumeLength,jdbcType=DECIMAL},
            </if>
            <if test="volumeWidth != null">
                volume_width = #{volumeWidth,jdbcType=DECIMAL},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=DECIMAL},
            </if>
            <if test="childPackageId != null">
                childPackage_Id = #{childPackageId,jdbcType=BIGINT},
            </if>
            <if test="infoId != null">
                Info_Id = #{infoId,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                lastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="listByProductInfoIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductpackage
        where Info_Id in
        <foreach collection="productInfoIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>


    <update id="updateBatch">
        UPDATE unifyproductpackage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="volume_height =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.volumeHeight,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="volume_length =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.volumeLength,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="volume_width =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.volumeWidth,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="weight =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.weight,jdbcType=DECIMAL}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>

