package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.ProductSkuSscQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.unify.UnifyProductSkuDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品sku（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public interface UnifyProductSkuMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UnifyProductSkuPO record);

    UnifyProductSkuPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UnifyProductSkuPO record);

    /**
     * 通过skuid查找
     * 
     * @return
     */
    UnifyProductSkuPO getBySkuId(Long skuId);

    PageResult<UnifyProductSkuPO> listProductSku(ProductSkuSscQueryDTO queryDTO);

    /**
     * 根据规格Id+货主Id查询中台skuId
     */
    Long getUnifySkuId(@Param("specificationId") Long specificationId, @Param("ownerId") Long ownerId);

    /**
     * 根据产品infoId查询中台产品
     */
    List<UnifyProductSkuPO> listUnifySkuByInfoId(@Param("infoId") Long infoId);
}