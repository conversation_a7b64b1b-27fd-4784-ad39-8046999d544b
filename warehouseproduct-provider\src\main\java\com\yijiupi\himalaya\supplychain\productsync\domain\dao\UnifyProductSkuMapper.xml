<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductSkuMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Sku_Id" jdbcType="BIGINT" property="skuId"/>
        <result column="Info_Id" jdbcType="BIGINT" property="infoId"/>
        <result column="Specification_Id" jdbcType="BIGINT" property="specificationId"/>
        <result column="Package_Id" jdbcType="BIGINT" property="packageId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Brand" jdbcType="VARCHAR" property="brand"/>
        <result column="InfoCategory_Id" jdbcType="BIGINT" property="infoCategoryId"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="LastOwner_Id" jdbcType="BIGINT" property="lastOwnerId"/>
        <result column="LastOwnerName" jdbcType="VARCHAR" property="lastOwnerName"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="SourceSku_Id" jdbcType="VARCHAR" property="sourceSkuId"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Sku_Id, Info_Id, Specification_Id, Package_Id, Name, Brand, InfoCategory_Id,
        Owner_Id, OwnerName, LastOwner_Id, LastOwnerName, specificationName, packageName,
        unitName, packageQuantity, Source, SourceSku_Id, State, Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductsku
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getBySkuId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductsku
        where Sku_Id = #{skuId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from unifyproductsku
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO">
        insert into unifyproductsku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="skuId != null">
                Sku_Id,
            </if>
            <if test="infoId != null">
                Info_Id,
            </if>
            <if test="specificationId != null">
                Specification_Id,
            </if>
            <if test="packageId != null">
                Package_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="brand != null">
                Brand,
            </if>
            <if test="infoCategoryId != null">
                InfoCategory_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="ownerName != null">
                OwnerName,
            </if>
            <if test="lastOwnerId != null">
                LastOwner_Id,
            </if>
            <if test="lastOwnerName != null">
                LastOwnerName,
            </if>
            <if test="specificationName != null">
                specificationName,
            </if>
            <if test="packageName != null">
                packageName,
            </if>
            <if test="unitName != null">
                unitName,
            </if>
            <if test="packageQuantity != null">
                packageQuantity,
            </if>
            <if test="source != null">
                Source,
            </if>
            <if test="sourceSkuId != null">
                SourceSku_Id,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="infoId != null">
                #{infoId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="packageId != null">
                #{packageId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="infoCategoryId != null">
                #{infoCategoryId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="lastOwnerId != null">
                #{lastOwnerId,jdbcType=BIGINT},
            </if>
            <if test="lastOwnerName != null">
                #{lastOwnerName,jdbcType=VARCHAR},
            </if>
            <if test="specificationName != null">
                #{specificationName,jdbcType=VARCHAR},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="source != null">
                #{source,jdbcType=TINYINT},
            </if>
            <if test="sourceSkuId != null">
                #{sourceSkuId,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSkuPO">
        update unifyproductsku
        <set>
            <if test="skuId != null">
                Sku_Id = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="infoId != null">
                Info_Id = #{infoId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                Specification_Id = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="packageId != null">
                Package_Id = #{packageId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                Brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="infoCategoryId != null">
                InfoCategory_Id = #{infoCategoryId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="ownerName != null">
                OwnerName = #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="lastOwnerId != null">
                LastOwner_Id = #{lastOwnerId,jdbcType=BIGINT},
            </if>
            <if test="lastOwnerName != null">
                LastOwnerName = #{lastOwnerName,jdbcType=VARCHAR},
            </if>
            <if test="specificationName != null">
                specificationName = #{specificationName,jdbcType=VARCHAR},
            </if>
            <if test="packageName != null">
                packageName = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                unitName = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="packageQuantity != null">
                packageQuantity = #{packageQuantity,jdbcType=DECIMAL},
            </if>
            <if test="source != null">
                Source = #{source,jdbcType=TINYINT},
            </if>
            <if test="sourceSkuId != null">
                SourceSku_Id = #{sourceSkuId,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>


    <select id="listProductSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductsku
        <where>
            <if test="skuId != null">
                and Sku_Id = #{skuId,jdbcType=BIGINT}
            </if>
            <if test="infoId != null">
                and Info_Id = #{infoId,jdbcType=BIGINT}
            </if>
            <if test="specificationId != null">
                and Specification_Id = #{specificationId,jdbcType=BIGINT}
            </if>
            <if test="packageId != null">
                and Package_Id = #{packageId,jdbcType=BIGINT}
            </if>
            <if test="name != null">
                and Name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="brand != null">
                and Brand = #{brand,jdbcType=VARCHAR}
            </if>
            <if test="infoCategoryId != null">
                and InfoCategory_Id = #{infoCategoryId,jdbcType=BIGINT}
            </if>
            <if test="ownerId != null">
                and Owner_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="lastOwnerId != null">
                and LastOwner_Id = #{lastOwnerId,jdbcType=BIGINT}
            </if>
            <if test="sourceSkuId != null">
                and SourceSku_Id = #{sourceSkuId,jdbcType=VARCHAR}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="getUnifySkuId" resultType="java.lang.Long">
        select Sku_Id from unifyproductsku
        where Specification_Id = #{specificationId,jdbcType=BIGINT}
        <if test="ownerId == null">
            and Owner_Id is null
        </if>
        <if test="ownerId != null">
            and Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        limit 1
    </select>

    <select id="listUnifySkuByInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductsku
        where Info_Id = #{infoId,jdbcType=BIGINT}
    </select>
</mapper>