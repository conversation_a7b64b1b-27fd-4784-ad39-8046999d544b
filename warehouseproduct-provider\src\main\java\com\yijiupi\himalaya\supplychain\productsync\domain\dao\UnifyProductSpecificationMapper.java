package com.yijiupi.himalaya.supplychain.productsync.domain.dao;

import com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品信息规格（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public interface UnifyProductSpecificationMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UnifyProductSpecificationPO record);

    UnifyProductSpecificationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UnifyProductSpecificationPO record);

    List<UnifyProductSpecificationPO> listByProductInfoIds(@Param("productInfoIds") List<Long> productInfoIds);

}