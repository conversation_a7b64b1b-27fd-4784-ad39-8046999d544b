<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.productsync.domain.dao.UnifyProductSpecificationMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="productInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="defaultImageId" jdbcType="INTEGER" property="defaultImageId"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="lastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, state, productInfo_Id, defaultImageId, createTime, createUser_Id, lastUpdateTime,
        lastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductspecification
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from unifyproductspecification
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO">
        insert into unifyproductspecification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="productInfoId != null">
                productInfo_Id,
            </if>
            <if test="defaultImageId != null">
                defaultImageId,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
            <if test="createUserId != null">
                createUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="productInfoId != null">
                #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="defaultImageId != null">
                #{defaultImageId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.productsync.domain.po.UnifyProductSpecificationPO">
        update unifyproductspecification
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="productInfoId != null">
                productInfo_Id = #{productInfoId,jdbcType=BIGINT},
            </if>
            <if test="defaultImageId != null">
                defaultImageId = #{defaultImageId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                lastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="listByProductInfoIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unifyproductspecification
        where productInfo_Id in
        <foreach collection="productInfoIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        order by ProductInfo_Id, CreateTime
    </select>

</mapper>