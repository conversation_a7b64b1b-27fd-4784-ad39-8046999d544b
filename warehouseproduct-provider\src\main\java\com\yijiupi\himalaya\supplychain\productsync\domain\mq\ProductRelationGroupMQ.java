package com.yijiupi.himalaya.supplychain.productsync.domain.mq;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 产品关联
 *
 * <AUTHOR>
 * @date 2019-12-27 16:33
 */
@Component
public class ProductRelationGroupMQ {

    private static final Logger LOG = LoggerFactory.getLogger(ProductRelationGroupMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.productSku.relateGroupAdd}")
    private String productRelateExchange;

    /**
     * 发送消息
     */
    public void send(List<ProductRelationGroupAddDTO> groupAddList) {
        if (!CollectionUtils.isEmpty(groupAddList)) {
            String json = JSON.toJSONString(groupAddList);
            LOG.info("产品关联发送消息:{}", json);
            rabbitTemplate.convertAndSend(productRelateExchange, null, json);
        }
    }
}
