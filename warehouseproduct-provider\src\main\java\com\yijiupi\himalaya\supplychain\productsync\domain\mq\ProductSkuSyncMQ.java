package com.yijiupi.himalaya.supplychain.productsync.domain.mq;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuSyncMsgDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 产品sku同步给外部系统
 *
 * <AUTHOR>
 * @date 2019-12-27 16:33
 */
@Component
public class ProductSkuSyncMQ {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuSyncMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.productSku.syncProductSku}")
    private String productSkuSyncExchange;

    /**
     * 发送消息
     */
    public void send(List<ProductSkuSyncMsgDTO> syncMsgDTOS, Boolean isAuto) {
        if (!CollectionUtils.isEmpty(syncMsgDTOS)) {
            if (isAuto) {
                LOG.info("产品SKU同步发送消息:{}", JSON.toJSONString(syncMsgDTOS));
            } else {
                LOG.info("[手动]产品SKU同步发送消息:{}", JSON.toJSONString(syncMsgDTOS));
            }
            rabbitTemplate.convertAndSend(productSkuSyncExchange, null, syncMsgDTOS);
        }
    }
}
