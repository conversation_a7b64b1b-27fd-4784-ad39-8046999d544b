package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ProductCategoryGroupPO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 分组Id
     */
    private Long categoryGroupId;

    /**
     * 父类编号
     */
    private Long parentId;

    /**
     * 冗余外部类目Id
     */
    private String refCategoryId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 排序号
     */
    private Integer sequence;

    /**
     * 分类类型：类目(1)
     */
    private Byte type;

    /**
     * 是否关联外部类目 0:否 1:是
     */
    private Byte isRelated;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 子节点
     */
    private List<ProductCategoryGroupPO> childProductCategoryGroupPOS;

    /**
     * 是否强制录入箱码 0: 否 1: 是
     */
    private Byte mandatoryEntryBoxCode;

    /**
     * 分组类型, 0=未设置, 1=酒饮, 2=休食
     */
    private Integer groupType;

    /**
     * 关注期, 这里是分数, 类似 1/2
     */
    private String attentionPeriod;

    /**
     * 临期, 这里是分数, 类似 1/2
     */
    private String nearExpiryPeriod;

    /**
     * 禁止销售期, 距离过期日期的天数
     */
    private String forbidSalesPeriod;

    /**
     * 绝对滞销期, 距离过期日期的天数
     */
    private String unsalablePeriod;

    public Byte getMandatoryEntryBoxCode() {
        return mandatoryEntryBoxCode;
    }

    public void setMandatoryEntryBoxCode(Byte mandatoryEntryBoxCode) {
        this.mandatoryEntryBoxCode = mandatoryEntryBoxCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryGroupId() {
        return categoryGroupId;
    }

    public void setCategoryGroupId(Long categoryGroupId) {
        this.categoryGroupId = categoryGroupId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getRefCategoryId() {
        return refCategoryId;
    }

    public void setRefCategoryId(String refCategoryId) {
        this.refCategoryId = refCategoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Byte getIsRelated() {
        return isRelated;
    }

    public void setIsRelated(Byte isRelated) {
        this.isRelated = isRelated;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public List<ProductCategoryGroupPO> getChildProductCategoryGroupPOS() {
        return childProductCategoryGroupPOS;
    }

    public void setChildProductCategoryGroupPOS(List<ProductCategoryGroupPO> childProductCategoryGroupPOS) {
        this.childProductCategoryGroupPOS = childProductCategoryGroupPOS;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public String getAttentionPeriod() {
        return attentionPeriod;
    }

    public void setAttentionPeriod(String attentionPeriod) {
        this.attentionPeriod = attentionPeriod;
    }

    public String getNearExpiryPeriod() {
        return nearExpiryPeriod;
    }

    public void setNearExpiryPeriod(String nearExpiryPeriod) {
        this.nearExpiryPeriod = nearExpiryPeriod;
    }

    public String getForbidSalesPeriod() {
        return forbidSalesPeriod;
    }

    public void setForbidSalesPeriod(String forbidSalesPeriod) {
        this.forbidSalesPeriod = forbidSalesPeriod;
    }

    public String getUnsalablePeriod() {
        return unsalablePeriod;
    }

    public void setUnsalablePeriod(String unsalablePeriod) {
        this.unsalablePeriod = unsalablePeriod;
    }

    @Override
    public String toString() {
        return "ProductCategoryGroupPO{" + "id=" + id + ", categoryGroupId=" + categoryGroupId + ", parentId="
            + parentId + ", refCategoryId='" + refCategoryId + '\'' + ", name='" + name + '\'' + ", sequence="
            + sequence + ", type=" + type + ", isRelated=" + isRelated + ", remark='" + remark + '\'' + ", createUser='"
            + createUser + '\'' + ", createTime=" + createTime + ", lastUpdateUser='" + lastUpdateUser + '\''
            + ", lastUpdateTime=" + lastUpdateTime + ", childProductCategoryGroupPOS=" + childProductCategoryGroupPOS
            + ", mandatoryEntryBoxCode=" + mandatoryEntryBoxCode + ", groupType=" + groupType + ", attentionPeriod='"
            + attentionPeriod + '\'' + ", nearExpiryPeriod='" + nearExpiryPeriod + '\'' + ", forbidSalesPeriod='"
            + forbidSalesPeriod + '\'' + ", unsalablePeriod='" + unsalablePeriod + '\'' + '}';
    }

    public String getProductCategoryGroupPOInfo() {
        return "ProductCategoryGroupPO{" + "id=" + id + ", categoryGroupId=" + categoryGroupId + ", parentId="
            + parentId + ", refCategoryId='" + refCategoryId + '\'' + ", name='" + name + '\'' + ", sequence="
            + sequence + ", type=" + type + ", isRelated=" + isRelated + '}';
    }
}