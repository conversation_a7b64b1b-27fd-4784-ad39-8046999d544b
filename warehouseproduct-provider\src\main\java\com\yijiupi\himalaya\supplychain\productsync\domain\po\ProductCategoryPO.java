package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.util.Date;

/**
 * 产品类目信息
 *
 * <AUTHOR>
 * @date 2018/9/10 16:56
 */
public class ProductCategoryPO {

    /**
     * 产品信息id
     */
    private Long id;

    /**
     * 一级统计类目ID
     */
    private Long statisticsClass;

    /**
     * 一级统计类目名称
     */
    private String statisticsClassName;

    /**
     * 二级统计类目ID
     */
    private Long secondStatisticsClass;

    /**
     * 二级统计类目名称
     */
    private String secondStatisticsClassName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人id
     */
    private Integer lastUpdateUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public Long getSecondStatisticsClass() {
        return secondStatisticsClass;
    }

    public void setSecondStatisticsClass(Long secondStatisticsClass) {
        this.secondStatisticsClass = secondStatisticsClass;
    }

    public String getSecondStatisticsClassName() {
        return secondStatisticsClassName;
    }

    public void setSecondStatisticsClassName(String secondStatisticsClassName) {
        this.secondStatisticsClassName = secondStatisticsClassName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

}
