package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.util.Date;

public class ProductCategoryRelatedPO {
    /**
     * id
     */
    private Long id;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 外部分类id
     */
    private String refCategoryId;

    /**
     * 外部分类名称
     */
    private String refCategoryName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 分组Id
     */
    private Long categoryGroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getRefCategoryId() {
        return refCategoryId;
    }

    public void setRefCategoryId(String refCategoryId) {
        this.refCategoryId = refCategoryId;
    }

    public String getRefCategoryName() {
        return refCategoryName;
    }

    public void setRefCategoryName(String refCategoryName) {
        this.refCategoryName = refCategoryName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getCategoryGroupId() {
        return categoryGroupId;
    }

    public void setCategoryGroupId(Long categoryGroupId) {
        this.categoryGroupId = categoryGroupId;
    }
}