/*
 * @ClassName ProductCodeInfoAuditPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-09-09 17:41:32
 */
package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.util.Date;

public class ProductCodeInfoAuditPO implements Serializable {
    /**
     * @Fields id 主键
     */
    private Long id;
    /**
     * @Fields cityId 城市ID
     */
    private Integer cityId;
    /**
     * @Fields warehouseId 仓库ID
     */
    private Integer warehouseId;
    /**
     * @Fields productInfoId 产品信息ID
     */
    private Long productInfoId;

    /**
     * 产品照片ID
     */
    private String productImgId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * @Fields productSkuId 产品SKUID
     */
    private Long productSkuId;
    /**
     * @Fields productSpecificationId 产品规格ID
     */
    private Long productSpecificationId;
    /**
     * @Fields state 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    private Byte state;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields reason 审核通过或者不通过填写的原因
     */
    private String reason;
    /**
     * @Fields auditUser 审核人
     */
    private String auditUser;
    /**
     * @Fields auditTime 审核时间
     */
    private Date auditTime;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品信息ID
     */
    public Long getProductInfoId() {
        return productInfoId;
    }

    /**
     * 设置 产品信息ID
     */
    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    /**
     * 获取 产品SKUID
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品SKUID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品规格ID
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    public Byte getState() {
        return state;
    }

    /**
     * 设置 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取 审核通过或者不通过填写的原因
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置 审核通过或者不通过填写的原因
     */
    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    /**
     * 获取 审核人
     */
    public String getAuditUser() {
        return auditUser;
    }

    /**
     * 设置 审核人
     */
    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser == null ? null : auditUser.trim();
    }

    /**
     * 获取 审核时间
     */
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * 设置 审核时间
     */
    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductImgId() {
        return productImgId;
    }

    public void setProductImgId(String productImgId) {
        this.productImgId = productImgId;
    }
}