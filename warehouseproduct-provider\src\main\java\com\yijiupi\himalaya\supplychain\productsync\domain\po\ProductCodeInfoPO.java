/*
 * @ClassName ProductCodeInfoPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-08-28 11:31:12
 */
package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeOwnerTypeEnum;

import java.io.Serializable;
import java.util.Date;

public class ProductCodeInfoPO implements Serializable {
    /**
     * @Fields id 主键
     */
    private Long id;

    /**
     * @Fields productCodeInfoAuditId 审核表ID
     */
    private Long productCodeInfoAuditId;
    /**
     * @Fields code 条码/箱码
     */
    private String code;
    /**
     * @Fields isCustom 是否自定义,0-否,1-是
     */
    private Byte isCustom;
    /**
     * @Fields codeImgId 条码/箱码图片信息ID,
     */
    private String codeImgId;
    /**
     * @Fields state 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    private Byte state;
    /**
     * @Fields codeType 类型:0-条码,1-箱码
     */
    private Byte codeType;

    /**
     * @Fields isNoCode 是否无码,0-否,1-是
     */
    private Byte isNoCode;
    /**
     * @Fields productInfoId 产品信息ID
     */
    private Long productInfoId;
    /**
     * @Fields productSpecificationId 产品规格ID
     */
    private Long productSpecificationId;
    /**
     * @Fields ownerType 条码/箱码所属类别,0-总部,1-城市(区域),2-经销商
     */
    private Byte ownerType;
    /**
     * @Fields ownerId 条码/箱码所属类别ID
     */
    private String ownerId;
    /**
     * @Fields isDelete 是否删除(是否停用),0-否,1-是
     */
    private Byte isDelete;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 条码/箱码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置 条码/箱码
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * 获取 是否自定义,0-否,1-是
     */
    public Byte getIsCustom() {
        return isCustom;
    }

    /**
     * 设置 是否自定义,0-否,1-是
     */
    public void setIsCustom(Byte isCustom) {
        this.isCustom = isCustom;
    }

    /**
     * 获取 条码/箱码图片信息ID
     */
    public String getCodeImgId() {
        return codeImgId;
    }

    /**
     * 设置 条码/箱码图片信息ID
     */
    public void setCodeImgId(String codeImgId) {
        this.codeImgId = codeImgId == null ? null : codeImgId.trim();
    }

    /**
     * 获取 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    public Byte getState() {
        return state;
    }

    /**
     * 设置 状态,0-待审核,1-审核通过,2-审核拒绝
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 类型:0-条码,1-箱码
     */
    public Byte getCodeType() {
        return codeType;
    }

    /**
     * 设置 类型:0-条码,1-箱码
     */
    public void setCodeType(Byte codeType) {
        this.codeType = codeType;
    }

    /**
     * 获取 产品信息ID
     */
    public Long getProductInfoId() {
        return productInfoId;
    }

    /**
     * 设置 产品信息ID
     */
    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    /**
     * 获取 产品规格ID
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 条码/箱码所属类别,0-总部,1-城市(区域),2-经销商
     */
    public Byte getOwnerType() {
        return ownerType;
    }

    /**
     * 设置 条码/箱码所属类别,0-总部,1-城市(区域),2-经销商
     */
    public void setOwnerType(Byte ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 条码/箱码所属类别ID
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * 设置 条码/箱码所属类别ID
     */
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId == null ? null : ownerId.trim();
    }

    /**
     * 获取 是否删除,0-否,1-是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 设置 是否删除,0-否,1-是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getProductCodeInfoAuditId() {
        return productCodeInfoAuditId;
    }

    public void setProductCodeInfoAuditId(Long productCodeInfoAuditId) {
        this.productCodeInfoAuditId = productCodeInfoAuditId;
    }

    public Byte getIsNoCode() {
        return isNoCode;
    }

    public void setIsNoCode(Byte isNoCode) {
        this.isNoCode = isNoCode;
    }

    /**
     * 获取有码唯一标识
     *
     * @return
     */
    public String getHasCodeIdentity() {
        return String.format("%s-%s-%s-%s-%s-%s", getCode(), getCodeType(), getProductInfoId(),
            getProductSpecificationId(),
            getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : getOwnerType(), getOwnerId());
    }

    /**
     * 获取无码唯一标识
     *
     * @return
     */
    public String getNoCodeIdentity() {
        return String.format("%s-%s-%s-%s-%s-%s", getCodeType(), getProductInfoId(), getProductSpecificationId(),
            getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : getOwnerType(), getOwnerId(),
            getCodeImgId());
    }

    /**
     * 获取无码分组唯一标识
     *
     * @return
     */
    public String getNoCodeGroupIdentity() {
        return String.format("%s-%s-%s-%s-%s", getCodeType(), getProductInfoId(), getProductSpecificationId(),
            getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : getOwnerType(), getOwnerId());
    }

    /**
     * 获取 code 区域标识
     *
     * @return
     */
    public String getOwnerIdentification() {
        return String.format("%s%s", getOwnerType() == null ? ProductCodeOwnerTypeEnum.HQ.getType() : getOwnerType(),
            getOwnerId());
    }
}