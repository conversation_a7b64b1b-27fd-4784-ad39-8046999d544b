package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.util.Date;

public class ProductInfoCategoryPO {

    /**
     * id
     */
    private Long id;

    /**
     * 产品信息id
     */
    private Long productInfoId;

    /**
     * 分组Id
     */
    private Long categoryGroupId;

    /**
     * 一级类目id
     */
    private Long statisticsClass;

    /**
     * 一级类目名称
     */
    private String statisticsClassName;

    /**
     * 二级类目id
     */
    private Long secondStatisticsClass;

    /**
     * 二级类目名称
     */
    private String secondStatisticsClassName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public Long getCategoryGroupId() {
        return categoryGroupId;
    }

    public void setCategoryGroupId(Long categoryGroupId) {
        this.categoryGroupId = categoryGroupId;
    }

    public Long getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(Long statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public Long getSecondStatisticsClass() {
        return secondStatisticsClass;
    }

    public void setSecondStatisticsClass(Long secondStatisticsClass) {
        this.secondStatisticsClass = secondStatisticsClass;
    }

    public String getSecondStatisticsClassName() {
        return secondStatisticsClassName;
    }

    public void setSecondStatisticsClassName(String secondStatisticsClassName) {
        this.secondStatisticsClassName = secondStatisticsClassName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getProductInfoCategoryPOInfo() {
        return "ProductInfoCategoryPO{" + "id=" + id + ", productInfoId=" + productInfoId + ", categoryGroupId="
            + categoryGroupId + ", statisticsClass=" + statisticsClass + ", statisticsClassName='" + statisticsClassName
            + '\'' + ", secondStatisticsClass=" + secondStatisticsClass + ", secondStatisticsClassName='"
            + secondStatisticsClassName + '\'' + '}';
    }
}