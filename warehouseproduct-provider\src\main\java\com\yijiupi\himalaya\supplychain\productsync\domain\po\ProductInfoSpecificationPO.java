package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.math.BigDecimal;
import java.util.Date;

public class ProductInfoSpecificationPO {
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Id
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.ProductInfo_Id
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Long productInfo_Id;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Name
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Length
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Double length;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Width
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Double width;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Height
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Double height;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Volume
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private String volume;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.Weight
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Double weight;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.State
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Byte state;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.CreateTime
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.CreateUser_Id
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Integer createUser_Id;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.LastUpdateTime
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Date lastUpdateTime;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfospecification.LastUpdateUser_Id
     *
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    private Integer lastUpdateUser_Id;

    /**
     * 对应转换的产品信息规格id
     */
    private Long convertProductInfoSpecId;

    /**
     * 对应转换的规格转换系数
     */
    private BigDecimal convertSpecQuantity;

    /**
     * 对应转换的产品信息规格单位（份，斤，瓶，双等）
     */
    private String convertSpecUnitName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 条码(多个用","分隔)
     */
    private String barCode;

    /**
     * 外部编码
     */
    private String outerCode;

    public String getOuterCode() {
        return outerCode;
    }

    public void setOuterCode(String outerCode) {
        this.outerCode = outerCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getConvertSpecUnitName() {
        return convertSpecUnitName;
    }

    public void setConvertSpecUnitName(String convertSpecUnitName) {
        this.convertSpecUnitName = convertSpecUnitName;
    }

    public BigDecimal getConvertSpecQuantity() {
        return convertSpecQuantity;
    }

    public void setConvertSpecQuantity(BigDecimal convertSpecQuantity) {
        this.convertSpecQuantity = convertSpecQuantity;
    }

    public Long getConvertProductInfoSpecId() {
        return convertProductInfoSpecId;
    }

    public void setConvertProductInfoSpecId(Long convertProductInfoSpecId) {
        this.convertProductInfoSpecId = convertProductInfoSpecId;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Id
     *
     * @return the value of productinfospecification.Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Id
     *
     * @param id the value for productinfospecification.Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.ProductInfo_Id
     *
     * @return the value of productinfospecification.ProductInfo_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Long getProductInfo_Id() {
        return productInfo_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.ProductInfo_Id
     *
     * @param productInfo_Id the value for productinfospecification.ProductInfo_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setProductInfo_Id(Long productInfo_Id) {
        this.productInfo_Id = productInfo_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Name
     *
     * @return the value of productinfospecification.Name
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Name
     *
     * @param name the value for productinfospecification.Name
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Length
     *
     * @return the value of productinfospecification.Length
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Double getLength() {
        return length;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Length
     *
     * @param length the value for productinfospecification.Length
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setLength(Double length) {
        this.length = length;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Width
     *
     * @return the value of productinfospecification.Width
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Double getWidth() {
        return width;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Width
     *
     * @param width the value for productinfospecification.Width
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setWidth(Double width) {
        this.width = width;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Height
     *
     * @return the value of productinfospecification.Height
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Double getHeight() {
        return height;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Height
     *
     * @param height the value for productinfospecification.Height
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setHeight(Double height) {
        this.height = height;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Volume
     *
     * @return the value of productinfospecification.Volume
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public String getVolume() {
        return volume;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Volume
     *
     * @param volume the value for productinfospecification.Volume
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setVolume(String volume) {
        this.volume = volume == null ? null : volume.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.Weight
     *
     * @return the value of productinfospecification.Weight
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Double getWeight() {
        return weight;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.Weight
     *
     * @param weight the value for productinfospecification.Weight
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setWeight(Double weight) {
        this.weight = weight;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.State
     *
     * @return the value of productinfospecification.State
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Byte getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.State
     *
     * @param state the value for productinfospecification.State
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.CreateTime
     *
     * @return the value of productinfospecification.CreateTime
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.CreateTime
     *
     * @param createTime the value for productinfospecification.CreateTime
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.CreateUser_Id
     *
     * @return the value of productinfospecification.CreateUser_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Integer getCreateUser_Id() {
        return createUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.CreateUser_Id
     *
     * @param createUser_Id the value for productinfospecification.CreateUser_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setCreateUser_Id(Integer createUser_Id) {
        this.createUser_Id = createUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.LastUpdateTime
     *
     * @return the value of productinfospecification.LastUpdateTime
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.LastUpdateTime
     *
     * @param lastUpdateTime the value for productinfospecification.LastUpdateTime
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfospecification.LastUpdateUser_Id
     *
     * @return the value of productinfospecification.LastUpdateUser_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public Integer getLastUpdateUser_Id() {
        return lastUpdateUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfospecification.LastUpdateUser_Id
     *
     * @param lastUpdateUser_Id the value for productinfospecification.LastUpdateUser_Id
     * @mbg.generated Tue Nov 13 15:52:59 CST 2018
     */
    public void setLastUpdateUser_Id(Integer lastUpdateUser_Id) {
        this.lastUpdateUser_Id = lastUpdateUser_Id;
    }
}