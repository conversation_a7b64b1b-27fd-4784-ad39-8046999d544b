package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品sku配置表
 *
 * <AUTHOR>
 * @since 2020-01-07 17:44
 */
public class ProductSkuConfigPO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * skuID
     */
    private Long productSkuId;

    /**
     * 仓库托管费
     */
    private BigDecimal warehouseCustodyFee;

    /**
     * 配送费（单件配送费）
     */
    private BigDecimal deliveryFee;

    /**
     * 配送费支付方式（0：固定价格，1：百分比）
     */
    private Integer deliveryPayType;

    /**
     * 分拣费
     */
    private BigDecimal sortingFee;

    /**
     * 是否拆包,不拆包(0), 拆包(1)
     */
    private Integer unpackage;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Integer productFeature;

    /**
     * 库存上限
     */
    private BigDecimal maxInventory;

    /**
     * 库存下限
     */
    private BigDecimal minInventory;

    /**
     * 补货上限
     */
    private BigDecimal maxReplenishment;

    /**
     * 补货下限
     */
    private BigDecimal minReplenishment;

    /**
     * 是否补全
     */
    private Byte isComplete;

    /**
     * 保存条件 0:标品 1:冷藏 2:冷冻 3:常温食品 4:鲜花绿植 5:电子券
     */
    private Byte storageType;

    /**
     * 是否拣货，默认否 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种，默认是 0:否 1:是
     */
    private Byte sow;

    /**
     * 库存占比
     */
    private String inventoryRatio;

    /**
     * 是否独家产品 0:否 1:是
     */
    private Byte unique;

    /**
     * 能否窜货（是否允许货主混放） 0:不能 1:能
     */
    private Byte fleeGoods;

    /**
     * 产品关联状态，0:未关联 1:已关联
     */
    private Byte productRelevantState;

    /**
     * 产品分级属性:0：未设置，1:A,2:B,3:C
     */
    private Integer productGrade;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 修改人id
     */
    private Integer lastUpdateUserId;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 销售价
     */
    private BigDecimal sellingPrice;

    /**
     * 销售价单位
     */
    private String sellingPriceUnit;

    /**
     * 配送系数-工资
     */
    private BigDecimal distributionPercentForAmount;

    /**
     * 产品状态 0: 停用, 1: 启用
     */
    private Byte state;

    /**
     * 库存状态 0: 无库存, 1: 有库存
     */
    private Byte storeState;

    /**
     * 动销状态 0: 无动销, 1：有动销, 2：滞销, 3：热销
     */
    private Byte saleState;

    /**
     * 中台skuID
     */
    private Long unifySkuId;

    /**
     * 托盘规格系数
     */
    private BigDecimal palletQuantity;

    /**
     * 业务属性, 整件区=0, 拆零区=1
     */
    private Integer businessTag;

    private BigDecimal distributionPercent;

    private String inventoryPinProperty;

    /**
     * 分仓属性：0、默认；1、酒饮，2、休百
     */
    private Byte storageAttribute;

    /**
     * 是否绝对滞销 0:否,1:是
     */
    private Byte isUnsalable;

    /**
     * 产品库龄
     */
    private Integer productStorageMaxAge;

    public BigDecimal getDistributionPercentForAmount() {
        return distributionPercentForAmount;
    }

    public void setDistributionPercentForAmount(BigDecimal distributionPercentForAmount) {
        this.distributionPercentForAmount = distributionPercentForAmount;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getStoreState() {
        return storeState;
    }

    public void setStoreState(Byte storeState) {
        this.storeState = storeState;
    }

    public Byte getSaleState() {
        return saleState;
    }

    public void setSaleState(Byte saleState) {
        this.saleState = saleState;
    }

    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getSellingPriceUnit() {
        return sellingPriceUnit;
    }

    public void setSellingPriceUnit(String sellingPriceUnit) {
        this.sellingPriceUnit = sellingPriceUnit;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getWarehouseCustodyFee() {
        return warehouseCustodyFee;
    }

    public void setWarehouseCustodyFee(BigDecimal warehouseCustodyFee) {
        this.warehouseCustodyFee = warehouseCustodyFee;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public Integer getDeliveryPayType() {
        return deliveryPayType;
    }

    public void setDeliveryPayType(Integer deliveryPayType) {
        this.deliveryPayType = deliveryPayType;
    }

    public BigDecimal getSortingFee() {
        return sortingFee;
    }

    public void setSortingFee(BigDecimal sortingFee) {
        this.sortingFee = sortingFee;
    }

    public Integer getUnpackage() {
        return unpackage;
    }

    public void setUnpackage(Integer unpackage) {
        this.unpackage = unpackage;
    }

    public Integer getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Integer productFeature) {
        this.productFeature = productFeature;
    }

    public BigDecimal getMaxInventory() {
        return maxInventory;
    }

    public void setMaxInventory(BigDecimal maxInventory) {
        this.maxInventory = maxInventory;
    }

    public BigDecimal getMinInventory() {
        return minInventory;
    }

    public void setMinInventory(BigDecimal minInventory) {
        this.minInventory = minInventory;
    }

    public BigDecimal getMaxReplenishment() {
        return maxReplenishment;
    }

    public void setMaxReplenishment(BigDecimal maxReplenishment) {
        this.maxReplenishment = maxReplenishment;
    }

    public BigDecimal getMinReplenishment() {
        return minReplenishment;
    }

    public void setMinReplenishment(BigDecimal minReplenishment) {
        this.minReplenishment = minReplenishment;
    }

    public Byte getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Byte isComplete) {
        this.isComplete = isComplete;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }

    public String getInventoryRatio() {
        return inventoryRatio;
    }

    public void setInventoryRatio(String inventoryRatio) {
        this.inventoryRatio = inventoryRatio;
    }

    public Byte getUnique() {
        return unique;
    }

    public void setUnique(Byte unique) {
        this.unique = unique;
    }

    public Byte getFleeGoods() {
        return fleeGoods;
    }

    public void setFleeGoods(Byte fleeGoods) {
        this.fleeGoods = fleeGoods;
    }

    public Byte getProductRelevantState() {
        return productRelevantState;
    }

    public void setProductRelevantState(Byte productRelevantState) {
        this.productRelevantState = productRelevantState;
    }

    public Integer getProductGrade() {
        return productGrade;
    }

    public void setProductGrade(Integer productGrade) {
        this.productGrade = productGrade;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public Long getUnifySkuId() {
        return unifySkuId;
    }

    public void setUnifySkuId(Long unifySkuId) {
        this.unifySkuId = unifySkuId;
    }

    public BigDecimal getPalletQuantity() {
        return palletQuantity;
    }

    public void setPalletQuantity(BigDecimal palletQuantity) {
        this.palletQuantity = palletQuantity;
    }

    public Integer getBusinessTag() {
        return businessTag;
    }

    public void setBusinessTag(Integer businessTag) {
        this.businessTag = businessTag;
    }

    public BigDecimal getDistributionPercent() {
        return distributionPercent;
    }

    public void setDistributionPercent(BigDecimal distributionPercent) {
        this.distributionPercent = distributionPercent;
    }

    public String getInventoryPinProperty() {
        return inventoryPinProperty;
    }

    public void setInventoryPinProperty(String inventoryPinProperty) {
        this.inventoryPinProperty = inventoryPinProperty;
    }

    public Byte getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Byte storageAttribute) {
        this.storageAttribute = storageAttribute;
    }

    public Byte getIsUnsalable() {
        return isUnsalable;
    }

    public void setIsUnsalable(Byte isUnsalable) {
        this.isUnsalable = isUnsalable;
    }

    /**
     * 获取 产品库龄
     *
     * @return productStorageMaxAge 产品库龄
     */
    public Integer getProductStorageMaxAge() {
        return this.productStorageMaxAge;
    }

    /**
     * 设置 产品库龄
     *
     * @param productStorageMaxAge 产品库龄
     */
    public void setProductStorageMaxAge(Integer productStorageMaxAge) {
        this.productStorageMaxAge = productStorageMaxAge;
    }
}
