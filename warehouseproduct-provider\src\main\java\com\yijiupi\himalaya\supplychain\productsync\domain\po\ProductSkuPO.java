package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductSkuPO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 规格系数id
     */
    private Long productSpecificationId;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 序号
     */
    private Integer sequence;

    /**
     * 备注
     */
    private String remo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人id
     */
    private Integer lastUpdateUserId;

    /**
     * 经销商或者合作商id
     */
    private Long companyId;

    /**
     * 销售模式
     */
    private Integer saleModel;

    /**
     * 配送系数-工资
     */
    private BigDecimal distributionPercentForAmount;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 产品来源 0:酒批，1:微酒（贷款/抵押）, 2:知花知果
     */
    private Integer source;

    /**
     * 仓库托管费（每件每月，单位为元）
     */
    private BigDecimal warehouseCustodyFee;

    /**
     * 配送费（单件配送费）
     */
    private BigDecimal deliveryFee;

    /**
     * 配送费支付方式: 固定价格(0), 百分比(1)
     */
    private Integer deliveryPayType;

    /**
     * 分拣费
     */
    private BigDecimal sortingFee;

    /**
     * 产品信息Id
     */
    private Long productInfoId;

    /**
     * 产品品牌
     */
    private String productBrand;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    /**
     * 是否拆包,不拆包(0), 拆包(1)
     */
    private Integer unpackage;

    /**
     * 产品的保质期
     */
    private Integer monthOfShelfLife;

    /**
     * 保质期单位(1：年 2：月 3：日）
     */
    private Integer shelfLifeUnit;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Integer productFeature;

    /**
     * 库存上限
     */
    private BigDecimal maxInventory;

    /**
     * 库存下限
     */
    private BigDecimal minInventory;

    /**
     * 补货上限
     */
    private BigDecimal maxReplenishment;

    /**
     * 补货下限
     */
    private BigDecimal minReplenishment;

    /**
     * 是否补全
     */
    private Byte isComplete;

    /**
     * 保存条件 0:常温 1:冷藏 2:冷冻
     */
    private Byte storageType;

    /**
     * 是否拣货，默认否 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种，默认是 0:否 1:是
     */
    private Byte sow;

    /**
     * 库存占比
     */
    private String inventoryRatio;

    /**
     * 是否独家产品 0:否 1:是
     */
    private Byte unique;

    /**
     * 能否窜货（是否允许货主混放） 0:不能 1:能
     */
    private Byte fleeGoods;

    /**
     * 产品关联状态，0:未关联 1:已关联
     */
    private Byte productRelevantState;

    /**
     * 产品分级属性:0：未设置，1:A,2:B,3:C
     */
    private Integer productGrade;

    /**
     * 产品与类目关系id
     */
    private Long productInfoCategoryId;

    /**
     * 关联外部的产品SKUID
     */
    private String refProductSkuId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    /**
     * 成本价（最小单位）
     */
    private BigDecimal costPrice;

    /**
     * 销售价
     */
    private BigDecimal sellingPrice;

    /**
     * 销售价单位
     */
    private String sellingPriceUnit;

    /**
     * 中台skuID
     */
    private Long unifySkuId;

    /**
     * 产品类型：1.成品;2.半成品;3.包装材料;4.奖券;
     */
    private Byte productType;

    /**
     * 是否已删除
     */
    private Byte isDelete;

    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getSellingPriceUnit() {
        return sellingPriceUnit;
    }

    public void setSellingPriceUnit(String sellingPriceUnit) {
        this.sellingPriceUnit = sellingPriceUnit;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getRefProductSkuId() {
        return refProductSkuId;
    }

    public void setRefProductSkuId(String refProductSkuId) {
        this.refProductSkuId = refProductSkuId;
    }

    public Integer getProductGrade() {
        return productGrade;
    }

    public void setProductGrade(Integer productGrade) {
        this.productGrade = productGrade;
    }

    public Long getProductInfoCategoryId() {
        return productInfoCategoryId;
    }

    public void setProductInfoCategoryId(Long productInfoCategoryId) {
        this.productInfoCategoryId = productInfoCategoryId;
    }

    public Byte getUnique() {
        return unique;
    }

    public void setUnique(Byte unique) {
        this.unique = unique;
    }

    public Byte getFleeGoods() {
        return fleeGoods;
    }

    public void setFleeGoods(Byte fleeGoods) {
        this.fleeGoods = fleeGoods;
    }

    public Byte getProductRelevantState() {
        return productRelevantState;
    }

    public void setProductRelevantState(Byte productRelevantState) {
        this.productRelevantState = productRelevantState;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Integer saleModel) {
        this.saleModel = saleModel;
    }

    public BigDecimal getDistributionPercentForAmount() {
        return distributionPercentForAmount;
    }

    public void setDistributionPercentForAmount(BigDecimal distributionPercentForAmount) {
        this.distributionPercentForAmount = distributionPercentForAmount;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public BigDecimal getWarehouseCustodyFee() {
        return warehouseCustodyFee;
    }

    public void setWarehouseCustodyFee(BigDecimal warehouseCustodyFee) {
        this.warehouseCustodyFee = warehouseCustodyFee;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public Integer getDeliveryPayType() {
        return deliveryPayType;
    }

    public void setDeliveryPayType(Integer deliveryPayType) {
        this.deliveryPayType = deliveryPayType;
    }

    public BigDecimal getSortingFee() {
        return sortingFee;
    }

    public void setSortingFee(BigDecimal sortingFee) {
        this.sortingFee = sortingFee;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public Integer getUnpackage() {
        return unpackage;
    }

    public void setUnpackage(Integer unpackage) {
        this.unpackage = unpackage;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public Integer getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Integer productFeature) {
        this.productFeature = productFeature;
    }

    public BigDecimal getMaxInventory() {
        return maxInventory;
    }

    public void setMaxInventory(BigDecimal maxInventory) {
        this.maxInventory = maxInventory;
    }

    public BigDecimal getMinInventory() {
        return minInventory;
    }

    public void setMinInventory(BigDecimal minInventory) {
        this.minInventory = minInventory;
    }

    public BigDecimal getMaxReplenishment() {
        return maxReplenishment;
    }

    public void setMaxReplenishment(BigDecimal maxReplenishment) {
        this.maxReplenishment = maxReplenishment;
    }

    public BigDecimal getMinReplenishment() {
        return minReplenishment;
    }

    public void setMinReplenishment(BigDecimal minReplenishment) {
        this.minReplenishment = minReplenishment;
    }

    public Byte getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Byte isComplete) {
        this.isComplete = isComplete;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }

    public String getInventoryRatio() {
        return inventoryRatio;
    }

    public void setInventoryRatio(String inventoryRatio) {
        this.inventoryRatio = inventoryRatio;
    }

    public Long getUnifySkuId() {
        return unifySkuId;
    }

    public void setUnifySkuId(Long unifySkuId) {
        this.unifySkuId = unifySkuId;
    }

    public Byte getProductType() {
        return productType;
    }

    public void setProductType(Byte productType) {
        this.productType = productType;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}
