package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
public class ProductdbExtPO implements Serializable {

    private static final long serialVersionUID = 3487033659138094039L;
    /**
     * 
     */
    private Long id;
    /**
     * 数据ID
     */
    private Long dataId;
    /**
     * 来源表
     */
    private String sourceTable;
    /**
     * 扩展类型
     */
    private Byte extType;
    /**
     * 
     */
    private String ext1;
    /**
     * 
     */
    private String ext2;
    /**
     * 
     */
    private String ext3;
    /**
     * 
     */
    private String ext4;
    /**
     * 
     */
    private String ext5;
    /**
     * 
     */
    private String ext6;
    /**
     * 
     */
    private String ext7;
    /**
     * 
     */
    private String ext8;
    /**
     * 
     */
    private String ext9;
    /**
     * 
     */
    private String ext10;
    /**
     * 
     */
    private String ext11;
    /**
     * 
     */
    private String ext12;
    /**
     * 
     */
    private String ext13;
    /**
     * 
     */
    private String ext14;
    /**
     * 
     */
    private String ext15;
    /**
     * 
     */
    private String ext16;
    /**
     * 
     */
    private String ext17;
    /**
     * 
     */
    private String ext18;
    /**
     * 
     */
    private String ext19;
    /**
     * 
     */
    private String ext20;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 创建人
     */
    private Integer createUserId;
    /**
     * 最后修改时间
     */
    private Timestamp lastUpdateTime;
    /**
     * 最后修改人
     */
    private Integer lastUpdateUserId;

    /**
     * 获取
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取数据ID
     */
    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    /**
     * 设置数据ID
     */
    public Long getDataId() {
        return dataId;
    }

    /**
     * 获取来源表
     */
    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    /**
     * 设置来源表
     */
    public String getSourceTable() {
        return sourceTable;
    }

    /**
     * 获取扩展类型
     */
    public void setExtType(Byte extType) {
        this.extType = extType;
    }

    /**
     * 设置扩展类型
     */
    public Byte getExtType() {
        return extType;
    }

    /**
     * 获取
     */
    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * 设置
     */
    public String getExt1() {
        return ext1;
    }

    /**
     * 获取
     */
    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * 设置
     */
    public String getExt2() {
        return ext2;
    }

    /**
     * 获取
     */
    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * 设置
     */
    public String getExt3() {
        return ext3;
    }

    /**
     * 获取
     */
    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    /**
     * 设置
     */
    public String getExt4() {
        return ext4;
    }

    /**
     * 获取
     */
    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }

    /**
     * 设置
     */
    public String getExt5() {
        return ext5;
    }

    /**
     * 获取
     */
    public void setExt6(String ext6) {
        this.ext6 = ext6;
    }

    /**
     * 设置
     */
    public String getExt6() {
        return ext6;
    }

    /**
     * 获取
     */
    public void setExt7(String ext7) {
        this.ext7 = ext7;
    }

    /**
     * 设置
     */
    public String getExt7() {
        return ext7;
    }

    /**
     * 获取
     */
    public void setExt8(String ext8) {
        this.ext8 = ext8;
    }

    /**
     * 设置
     */
    public String getExt8() {
        return ext8;
    }

    /**
     * 获取
     */
    public void setExt9(String ext9) {
        this.ext9 = ext9;
    }

    /**
     * 设置
     */
    public String getExt9() {
        return ext9;
    }

    /**
     * 获取
     */
    public void setExt10(String ext10) {
        this.ext10 = ext10;
    }

    /**
     * 设置
     */
    public String getExt10() {
        return ext10;
    }

    /**
     * 获取
     */
    public void setExt11(String ext11) {
        this.ext11 = ext11;
    }

    /**
     * 设置
     */
    public String getExt11() {
        return ext11;
    }

    /**
     * 获取
     */
    public void setExt12(String ext12) {
        this.ext12 = ext12;
    }

    /**
     * 设置
     */
    public String getExt12() {
        return ext12;
    }

    /**
     * 获取
     */
    public void setExt13(String ext13) {
        this.ext13 = ext13;
    }

    /**
     * 设置
     */
    public String getExt13() {
        return ext13;
    }

    /**
     * 获取
     */
    public void setExt14(String ext14) {
        this.ext14 = ext14;
    }

    /**
     * 设置
     */
    public String getExt14() {
        return ext14;
    }

    /**
     * 获取
     */
    public void setExt15(String ext15) {
        this.ext15 = ext15;
    }

    /**
     * 设置
     */
    public String getExt15() {
        return ext15;
    }

    /**
     * 获取
     */
    public void setExt16(String ext16) {
        this.ext16 = ext16;
    }

    /**
     * 设置
     */
    public String getExt16() {
        return ext16;
    }

    /**
     * 获取
     */
    public void setExt17(String ext17) {
        this.ext17 = ext17;
    }

    /**
     * 设置
     */
    public String getExt17() {
        return ext17;
    }

    /**
     * 获取
     */
    public void setExt18(String ext18) {
        this.ext18 = ext18;
    }

    /**
     * 设置
     */
    public String getExt18() {
        return ext18;
    }

    /**
     * 获取
     */
    public void setExt19(String ext19) {
        this.ext19 = ext19;
    }

    /**
     * 设置
     */
    public String getExt19() {
        return ext19;
    }

    /**
     * 获取
     */
    public void setExt20(String ext20) {
        this.ext20 = ext20;
    }

    /**
     * 设置
     */
    public String getExt20() {
        return ext20;
    }

    /**
     * 获取创建时间
     */
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    /**
     * 设置创建时间
     */
    public Timestamp getCreateTime() {
        return createTime;
    }

    /**
     * 获取创建人
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 设置创建人
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * 获取最后修改时间
     */
    public void setLastUpdateTime(Timestamp lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 设置最后修改时间
     */
    public Timestamp getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 获取最后修改人
     */
    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    /**
     * 设置最后修改人
     */
    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

}