package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-17
 */
public class ThirdPartyProductsRelevancePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 城市
     */
    private String city;
    /**
     * 状态0=停用，1=启用
     */
    private Byte state;
    /**
     * 商品skuId
     */
    private String productSkuId;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 规格
     */
    private String specification;
    /**
     * 包装规格系数
     */
    private BigDecimal specificationQuantity;
    /**
     * 销售价
     */
    private BigDecimal salesPrice;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 大单位
     */
    private String theBigUnit;
    /**
     * 小单位
     */
    private String theSmallUnit;
    /**
     * 无忧退货(天)
     */
    private Integer carefreeReturn;
    /**
     * 一级类目
     */
    private String oneCategory;
    /**
     * 二级类目
     */
    private String twoCategory;
    /**
     * 图片路径
     */
    private String imgUrl;
    /**
     * 说明
     */
    private String remark;
    /**
     * 系统编码
     */
    private String sysCode;
    /**
     * 产品信息id
     */
    private Long productInfoId;
    /**
     * 供应链skuid
     */
    private Long productsSkuId;
    /**
     * 规格Id
     */
    private Long specificationId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String lastUpdateUser;
    /**
     * 修改时间
     */
    private Date lastUpdateTime;
    /**
     * 城市Id
     */
    private Integer cityId;

    private Integer warehouseId;

    /**
     * 商品特征:1:大件,2:小件
     */
    private Integer productFeature;

    public Integer getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Integer productFeature) {
        this.productFeature = productFeature;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 设置
     */
    public Long getId() {
        return id;
    }

    /**
     * 获取产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 设置产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 获取条码
     */
    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    /**
     * 设置条码
     */
    public String getBarCode() {
        return barCode;
    }

    /**
     * 获取城市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 设置城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 获取状态0=停用，1=启用
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 设置状态0=停用，1=启用
     */
    public Byte getState() {
        return state;
    }

    /**
     * 获取商品skuId
     */
    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 设置商品skuId
     */
    public String getProductSkuId() {
        return productSkuId;
    }

    /**
     * 获取品牌
     */
    public void setBrand(String brand) {
        this.brand = brand;
    }

    /**
     * 设置品牌
     */
    public String getBrand() {
        return brand;
    }

    /**
     * 获取规格
     */
    public void setSpecification(String specification) {
        this.specification = specification;
    }

    /**
     * 设置规格
     */
    public String getSpecification() {
        return specification;
    }

    /**
     * 获取包装规格系数
     */
    public void setSpecificationQuantity(BigDecimal specificationQuantity) {
        this.specificationQuantity = specificationQuantity;
    }

    /**
     * 设置包装规格系数
     */
    public BigDecimal getSpecificationQuantity() {
        return specificationQuantity;
    }

    /**
     * 获取销售价
     */
    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    /**
     * 设置销售价
     */
    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    /**
     * 获取成本价
     */
    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    /**
     * 设置成本价
     */
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    /**
     * 获取大单位
     */
    public void setTheBigUnit(String theBigUnit) {
        this.theBigUnit = theBigUnit;
    }

    /**
     * 设置大单位
     */
    public String getTheBigUnit() {
        return theBigUnit;
    }

    /**
     * 获取小单位
     */
    public void setTheSmallUnit(String theSmallUnit) {
        this.theSmallUnit = theSmallUnit;
    }

    /**
     * 设置小单位
     */
    public String getTheSmallUnit() {
        return theSmallUnit;
    }

    /**
     * 获取无忧退货(天)
     */
    public void setCarefreeReturn(Integer carefreeReturn) {
        this.carefreeReturn = carefreeReturn;
    }

    /**
     * 设置无忧退货(天)
     */
    public Integer getCarefreeReturn() {
        return carefreeReturn;
    }

    /**
     * 获取一级类目
     */
    public void setOneCategory(String oneCategory) {
        this.oneCategory = oneCategory;
    }

    /**
     * 设置一级类目
     */
    public String getOneCategory() {
        return oneCategory;
    }

    /**
     * 获取二级类目
     */
    public void setTwoCategory(String twoCategory) {
        this.twoCategory = twoCategory;
    }

    /**
     * 设置二级类目
     */
    public String getTwoCategory() {
        return twoCategory;
    }

    /**
     * 获取图片路径
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    /**
     * 设置图片路径
     */
    public String getImgUrl() {
        return imgUrl;
    }

    /**
     * 获取说明
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 设置说明
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 获取系统编码
     */
    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    /**
     * 设置系统编码
     */
    public String getSysCode() {
        return sysCode;
    }

    /**
     * 获取产品信息id
     */
    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    /**
     * 设置产品信息id
     */
    public Long getProductInfoId() {
        return productInfoId;
    }

    /**
     * 获取供应链skuid
     */
    public void setProductsSkuId(Long productsSkuId) {
        this.productsSkuId = productsSkuId;
    }

    /**
     * 设置供应链skuid
     */
    public Long getProductsSkuId() {
        return productsSkuId;
    }

    /**
     * 获取规格Id
     */
    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    /**
     * 设置规格Id
     */
    public Long getSpecificationId() {
        return specificationId;
    }

    /**
     * 获取创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 设置创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 获取创建时间
     */
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    /**
     * 设置创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 获取修改人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 设置修改人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 获取修改时间
     */
    public void setLastUpdateTime(Timestamp lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 设置修改时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }
}