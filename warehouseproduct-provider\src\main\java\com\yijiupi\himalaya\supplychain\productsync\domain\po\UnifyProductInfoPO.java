package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品信息（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public class UnifyProductInfoPO implements Serializable {

    private static final long serialVersionUID = 434562707939968617L;
    /**
     * 产品信息id
     */
    private Long id;

    /**
     * 组织机构ID
     */
    private Integer parentOrgId;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 类目
     */
    private String statisticsCategoryName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 保质期时间
     */
    private Integer shelfLife;

    /**
     * 保质期类别, 0: 长期, 1: 年, 2: 月, 3: 日
     */
    private Byte shelfLifeType;

    /**
     * 默认图片id
     */
    private Integer defaultImageId;

    /**
     * 产品销售方式, 0: 普通产品, 1: 组合产品, 2: 原装组合, 3: 预售, 4: 虚拟, 5: 特殊
     */
    private Byte productSaleType;

    /**
     * 状态, 0: 停用, 1: 启用
     */
    private Byte state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 修改人id
     */
    private Integer lastUpdateUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getParentOrgId() {
        return parentOrgId;
    }

    public void setParentOrgId(Integer parentOrgId) {
        this.parentOrgId = parentOrgId;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getStatisticsCategoryName() {
        return statisticsCategoryName;
    }

    public void setStatisticsCategoryName(String statisticsCategoryName) {
        this.statisticsCategoryName = statisticsCategoryName == null ? null : statisticsCategoryName.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public Integer getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(Integer shelfLife) {
        this.shelfLife = shelfLife;
    }

    public Byte getShelfLifeType() {
        return shelfLifeType;
    }

    public void setShelfLifeType(Byte shelfLifeType) {
        this.shelfLifeType = shelfLifeType;
    }

    public Integer getDefaultImageId() {
        return defaultImageId;
    }

    public void setDefaultImageId(Integer defaultImageId) {
        this.defaultImageId = defaultImageId;
    }

    public Byte getProductSaleType() {
        return productSaleType;
    }

    public void setProductSaleType(Byte productSaleType) {
        this.productSaleType = productSaleType;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }
}