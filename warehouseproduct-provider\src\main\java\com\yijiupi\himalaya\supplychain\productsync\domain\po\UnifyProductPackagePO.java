package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品包装单位（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public class UnifyProductPackagePO {

    /**
     * 包装单位id
     */
    private Long id;

    /**
     * 子单位转换系数
     */
    private BigDecimal childPackageQuantity;

    /**
     * 单位名称
     */
    private String name;

    /**
     * 包装类型
     */
    private Integer packageType;

    /**
     * 是否散称包装, 0: 非散装, 1: 散装
     */
    private Byte scattered;

    /**
     * 状态, 0: 停用, 1: 启用
     */
    private Byte state;

    /**
     * 高
     */
    private BigDecimal volumeHeight;

    /**
     * 长
     */
    private BigDecimal volumeLength;

    /**
     * 宽
     */
    private BigDecimal volumeWidth;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 子包装单位id
     */
    private Long childPackageId;

    /**
     * 产品信息id
     */
    private Long infoId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 修改人id
     */
    private Integer lastUpdateUserId;

    /**
     * 子包装单位名称
     */
    private String childPackageName;

    public String getChildPackageName() {
        return childPackageName;
    }

    public void setChildPackageName(String childPackageName) {
        this.childPackageName = childPackageName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getChildPackageQuantity() {
        return childPackageQuantity;
    }

    public void setChildPackageQuantity(BigDecimal childPackageQuantity) {
        this.childPackageQuantity = childPackageQuantity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getPackageType() {
        return packageType;
    }

    public void setPackageType(Integer packageType) {
        this.packageType = packageType;
    }

    public Byte getScattered() {
        return scattered;
    }

    public void setScattered(Byte scattered) {
        this.scattered = scattered;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public BigDecimal getVolumeHeight() {
        return volumeHeight;
    }

    public void setVolumeHeight(BigDecimal volumeHeight) {
        this.volumeHeight = volumeHeight;
    }

    public BigDecimal getVolumeLength() {
        return volumeLength;
    }

    public void setVolumeLength(BigDecimal volumeLength) {
        this.volumeLength = volumeLength;
    }

    public BigDecimal getVolumeWidth() {
        return volumeWidth;
    }

    public void setVolumeWidth(BigDecimal volumeWidth) {
        this.volumeWidth = volumeWidth;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public Long getChildPackageId() {
        return childPackageId;
    }

    public void setChildPackageId(Long childPackageId) {
        this.childPackageId = childPackageId;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    @Override
    public String toString() {
        return "UnifyProductPackagePO{" + "id=" + id + ", childPackageQuantity=" + childPackageQuantity + ", name='"
            + name + '\'' + ", packageType=" + packageType + ", scattered=" + scattered + ", state=" + state
            + ", childPackageId=" + childPackageId + ", infoId=" + infoId + ", childPackageName='" + childPackageName
            + '\'' + '}';
    }
}