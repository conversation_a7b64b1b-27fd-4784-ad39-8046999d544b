package com.yijiupi.himalaya.supplychain.productsync.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品sku（中台）
 *
 * <AUTHOR>
 * @date 2020-11-24 10:08
 */
public class UnifyProductSkuPO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 产品SkuId
     */
    private Long skuId;

    /**
     * 产品信息Id
     */
    private Long infoId;

    /**
     * 产品规格ID
     */
    private Long specificationId;

    /**
     * 产品包装单位id
     */
    private Long packageId;

    /**
     * 产品SKU名称
     */
    private String name;

    /**
     * 产品品牌
     */
    private String brand;

    /**
     * 产品与类目关系id
     */
    private Long infoCategoryId;

    /**
     * 一级货主id
     */
    private Long ownerId;

    /**
     * 一级货主名称
     */
    private String ownerName;

    /**
     * 末级货主Id
     */
    private Long lastOwnerId;

    /**
     * 末级货主名称
     */
    private String lastOwnerName;

    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 包装规格大单位
     */
    private String packageName;

    /**
     * 包装规格小单位
     */
    private String unitName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 产品来源，易酒批 = 0;微酒 = 1;知花知果 = 2;易款连锁 = 3;易经商 = 4;
     */
    private Byte source;

    /**
     * 关联外部的产品SKUID
     */
    private String sourceSkuId;

    /**
     * 状态, 0: 停用, 1: 启用
     */
    private Byte state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 修改人id
     */
    private Integer lastUpdateUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Long getSpecificationId() {
        return specificationId;
    }

    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public Long getInfoCategoryId() {
        return infoCategoryId;
    }

    public void setInfoCategoryId(Long infoCategoryId) {
        this.infoCategoryId = infoCategoryId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName == null ? null : ownerName.trim();
    }

    public Long getLastOwnerId() {
        return lastOwnerId;
    }

    public void setLastOwnerId(Long lastOwnerId) {
        this.lastOwnerId = lastOwnerId;
    }

    public String getLastOwnerName() {
        return lastOwnerName;
    }

    public void setLastOwnerName(String lastOwnerName) {
        this.lastOwnerName = lastOwnerName == null ? null : lastOwnerName.trim();
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName == null ? null : specificationName.trim();
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public String getSourceSkuId() {
        return sourceSkuId;
    }

    public void setSourceSkuId(String sourceSkuId) {
        this.sourceSkuId = sourceSkuId;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }
}