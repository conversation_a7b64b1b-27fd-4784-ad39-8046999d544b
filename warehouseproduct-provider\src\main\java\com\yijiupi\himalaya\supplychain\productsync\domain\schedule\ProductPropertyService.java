package com.yijiupi.himalaya.supplychain.productsync.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductPropertyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-08-19 16:17
 **/
@Service
public class ProductPropertyService {

    @Reference
    private IProductPropertyService productPropertyService;

    private static final Logger logger = LoggerFactory.getLogger(ProductPropertyService.class);

    @XxlJob("productPropertySync")
    public void productPropertySync() {
        logger.info("定时获取库存ABC");
        productPropertyService.productPropertySync();
    }

}
