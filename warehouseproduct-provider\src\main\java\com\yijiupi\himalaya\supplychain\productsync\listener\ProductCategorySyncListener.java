package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.product.ScmProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ProductCategoryGroupConverter;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCategoryGroupBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryGroupMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.CategorySync;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategorySyncDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ProductCategorySyncListener {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCategorySyncListener.class);

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductInfoQueryService scmProductInfoQueryService;

    @Autowired
    private ProductCategoryGroupMapper productCategoryGroupMapper;

    @RabbitListener(queues = "${mq.supplychain.productCategory.change}")
    public void syncProductCategory(Message message) {
        String json = null;
        try {
            json = new String(message.getBody(), "UTF-8");
            LOG.info("[类目]信息同步>>>【mq.supplychain.productCategory.change】:{}", json);
            ProductCategorySyncDTO syncData = JSON.parseObject(json, ProductCategorySyncDTO.class);
            CategorySync categorySync = ProductCategoryGroupConverter.ProductCategorySyncDTO2CategorySync(syncData);
            if (categorySync != null) {
                if (StringUtils.isEmpty(categorySync.getCategoryName())) {
                    // 先通过类目id查询数据库
                    ProductCategoryGroupPO refCategory = productCategoryGroupMapper
                        .getByRefCategoryId(categorySync.getCategoryId(), categorySync.getCategoryGroupId());
                    if (refCategory != null) {
                        categorySync.setCategoryName(refCategory.getName());
                    } else {
                        // 通过类目ID获取展示类目名称
                        Map<Integer, String> categoryNameMap = scmProductInfoQueryService
                            .productDisplayCategoryIdNameTransfer(new HashSet<>(syncData.getParentId()));
                        if (categoryNameMap != null && categoryNameMap.size() > 0) {
                            categorySync.setCategoryName(categoryNameMap.get(syncData.getId()));
                        } else {
                            LOG.warn("[类目]信息同步失败！未查到类目信息:{}", json);
                            return;
                        }
                    }
                }
                productCategoryGroupBL.syncCategoryTree(Arrays.asList(categorySync));
            }
        } catch (Exception e) {
            LOG.error("[类目]信息同步失败！参数:{}", json, e);
        }
    }
}
