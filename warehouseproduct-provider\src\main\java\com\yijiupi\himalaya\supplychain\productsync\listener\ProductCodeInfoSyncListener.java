package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCodeInfoBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.SyncProductCodeInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProductCodeInfoSyncListener {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCodeInfoSyncListener.class);

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    // @RabbitListener(queues = "${mq.supplychain.productCodeInfo.change}")
    public void syncProductCodeInfo(Message message) {
        String json = null;
        try {
            json = new String(message.getBody(), "UTF-8");
            LOG.info("产品[条码/箱码]信息同步>>>【mq.supplychain.productCodeInfo.change】" + json);
            List<SyncProductCodeInfoDTO> syncData = JSON.parseArray(json, SyncProductCodeInfoDTO.class);
            productCodeInfoBL.syncProductCodeInfo(syncData);
        } catch (Exception e) {
            LOG.error("产品[条码/箱码]信息同步失败！参数:{}", json, e);
        }
    }

}
