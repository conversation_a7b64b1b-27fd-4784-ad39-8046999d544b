package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCostPriceBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCostPriceSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.ConvertorException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品成本价同步
 */
@Service
public class ProductCostPriceSyncListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductCostPriceSyncListener.class);

    @Autowired
    private ProductCostPriceBL productCostPriceBL;

    /**
     * 酒批产品成本价同步
     */
    @RabbitListener(queues = "${mq.supplychain.costprice.yjpsync}")
    public void yjpSync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("【酒批】成本价同步>>>【mq.supplychain.costprice.yjpsync】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new ConvertorException("成本价同步为空！");
            }
            List<ProductCostPriceSyncDTO> costPriceSyncDTOList = JSON.parseArray(json, ProductCostPriceSyncDTO.class);
            productCostPriceBL.sync(costPriceSyncDTOList);
        } catch (Exception e) {
            LOG.error("【酒批】成本价同步失败", e);
        }
    }

    /**
     * 易款连锁产品成本价同步
     */
    @RabbitListener(queues = "${mq.supplychain.costprice.yksync}")
    public void ykSync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("【易款】成本价同步>>>【mq.supplychain.costprice.yksync】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new ConvertorException("成本价同步为空！");
            }
            List<ProductCostPriceSyncDTO> costPriceSyncDTOList = JSON.parseArray(json, ProductCostPriceSyncDTO.class);
            productCostPriceBL.sync(costPriceSyncDTOList);
        } catch (Exception e) {
            LOG.error("【易款】成本价同步失败", e);
        }
    }

}
