package com.yijiupi.himalaya.supplychain.productsync.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductInfoImpl;

/**
 * 产品信息同步
 */
@Service
public class ProductInfoSyncListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoSyncListener.class);
    @Autowired
    private ProductInfoImpl productInfoImpl;

    /**
     * 产品信息新增
     * 
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productInfo.add.name}")
    public void startAdd(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("产品信息新增>>>【mq.supplychain.productInfo.add】" + json);
            productInfoImpl.doAdd(json);
        } catch (Exception e) {
            LOG.error("产品信息新增失败", e);
        }
    }

    /**
     * 产品信息修改
     * 
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productInfo.modify.name}")
    public void startModify(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("产品信息修改>>>【mq.supplychain.productInfo.modify】" + json);
            productInfoImpl.doModify(json);
        } catch (Exception e) {
            LOG.error("产品信息修改失败", e);
        }
    }
}
