package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductUnifySyncBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSkuSplitPackageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 根据拆包消息，创建久批SKU及ProductSpecification（规格Id为中台SKUID）
 *
 * <AUTHOR>
 * @Date 2021/8/16 14:04
 */
@Service
public class ProductSkuSplitPackageSyncListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuSplitPackageSyncListener.class);

    @Autowired
    private ProductUnifySyncBL productUnifySyncBL;

    /**
     * 根据交易的拆包消息，创建/更新SKU、规格
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.splitPackageSync}")
    public void syncSplitPackage(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("根据零售的拆包消息，创建SKU和规格>>>【mq.supplychain.productSku.splitPackageSync】" + json);
            SyncSscProductSkuSplitPackageDTO splitPackageDTO =
                JSON.parseObject(json, SyncSscProductSkuSplitPackageDTO.class);
            productUnifySyncBL.syncSplitPackage(splitPackageDTO);
            LOG.info("根据零售的拆包消息，创建SKU和规格成功");
        } catch (Exception e) {
            LOG.error("根据零售的拆包消息，创建SKU和规格异常", e);
        }
    }
}
