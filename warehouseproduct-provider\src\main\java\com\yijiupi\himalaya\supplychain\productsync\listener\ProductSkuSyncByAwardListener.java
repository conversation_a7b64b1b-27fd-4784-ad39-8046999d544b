package com.yijiupi.himalaya.supplychain.productsync.listener;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuByAwardBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardMessage;
import com.yijiupi.himalaya.supplychain.productsync.util.ConvertorException;

/**
 * 奖券产品信息同步
 */
@Service
public class ProductSkuSyncByAwardListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuSyncByAwardListener.class);

    @Autowired
    private ProductSkuByAwardBL productSkuByAwardBL;

    /**
     * 兑奖产品批量同步
     *
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.syncawardproduct}")
    public void syncAwardProductSku(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("奖券产品信息批量同步>>>【mq.supplychain.productSku.syncawardproduct】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new ConvertorException("奖券产品新增信息为空！");
            }
            ProductAwardMessage messageDTO = JSON.parseObject(json, ProductAwardMessage.class);
            productSkuByAwardBL.createProductInfoByAward(messageDTO);
        } catch (Exception e) {
            LOG.error("奖券产品信息同步失败", e);
        }
    }

}
