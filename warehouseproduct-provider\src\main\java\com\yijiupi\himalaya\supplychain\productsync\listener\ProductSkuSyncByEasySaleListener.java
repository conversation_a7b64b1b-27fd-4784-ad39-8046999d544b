package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductSkuImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 产品同步
 */
@Service
public class ProductSkuSyncByEasySaleListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuSyncByEasySaleListener.class);
    @Autowired
    private ProductSkuImpl productSkuImpl;

    /**
     * 产品同步（易经销）
     *
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.easysalesync}")
    public void syncProductSku(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("产品SKU同步>>>【mq.supplychain.productSku.easysalesync】" + json);
            productSkuImpl.sync(json);
        } catch (Exception e) {
            LOG.error("产品SKU同步失败", e);
        }
    }

}
