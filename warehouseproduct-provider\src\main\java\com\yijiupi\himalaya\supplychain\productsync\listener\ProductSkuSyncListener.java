package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.yijiupi.himalaya.supplychain.productsync.service.impl.ProductSkuImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 产品同步
 */
@Service
public class ProductSkuSyncListener {
    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuSyncListener.class);
    @Autowired
    private ProductSkuImpl productSkuImpl;

    /**
     * 产品同步（交易平台化去SKU）
     *
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.allInfosync}")
    // @RabbitListener(queues = "${mq.supplychain.trdproductSku.sync}")
    public void syncProductSku(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("产品SKU同步>>>【mq.supplychain.trdproductSku.sync】" + json);
            productSkuImpl.sync(json);
        } catch (Exception e) {
            LOG.error("产品SKU同步失败", e);
        }
    }

    /**
     * 店铺产品批量同步（交易平台化去SKU）
     *
     * @param message
     * @throws Exception
     * @return: void
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.syncShop}")
    public void syncShopProductSku(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("店铺产品SKU批量同步>>>【mq.supplychain.productSku.syncShop】" + json);
            productSkuImpl.syncBatch(json);
        } catch (Exception e) {
            LOG.error("店铺产品SKU同步失败", e);
        }
    }

}
