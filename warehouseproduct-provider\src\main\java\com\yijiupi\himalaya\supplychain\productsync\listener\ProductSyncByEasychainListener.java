package com.yijiupi.himalaya.supplychain.productsync.listener;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncByEasychainBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.easychain.ProductSkuAndWarehouseMessageDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.easychain.ProductSkuMessageDTO;
import com.yijiupi.himalaya.supplychain.productsync.util.ConvertorException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 易款连锁产品同步
 *
 * <AUTHOR>
 * @date 2019/5/7 21:01
 */
@Service
public class ProductSyncByEasychainListener {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncByEasychainListener.class);

    @Autowired
    private ObjectMapper jsonObject;

    @Autowired
    private ProductSyncByEasychainBL productSyncByEasychainBL;

    /**
     * 产品Sku同步
     */
    @RabbitListener(queues = "${mq.supplychain.easychainproduct.addProductSku}")
    public void productSkuSync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            if (StringUtils.isEmpty(json)) {
                throw new ConvertorException("【易款连锁】产品SKU同步为空！");
            }
            LOG.info("【易款连锁】产品SKU同步>>>【mq.supplychain.easychainproduct.addProductSku】{}", json);
            ProductSkuMessageDTO dto = JSON.parseObject(json, ProductSkuMessageDTO.class);
            productSyncByEasychainBL.saveProductSku(dto);
        } catch (Exception e) {
            LOG.error("【易款连锁】产品SKU同步失败", e);
        }
    }

    /**
     * 新增仓库同步产品Sku
     */
    @RabbitListener(queues = "${mq.supplychain.easychainproduct.addWarehouseProductSku}")
    public void addWarehouseProductSkuSync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            if (StringUtils.isEmpty(json)) {
                throw new ConvertorException("【易款连锁】新增仓库同步产品Sku为空！");
            }
            LOG.info("【易款连锁】新增仓库同步产品Sku>>>【mq.supplychain.easychainproduct.addWarehouseProductSku】{}", json);
            ProductSkuAndWarehouseMessageDTO dto = JSON.parseObject(json, ProductSkuAndWarehouseMessageDTO.class);
            productSyncByEasychainBL.addWarehouseProductSku(dto);
        } catch (Exception e) {
            LOG.error("【易款连锁】新增仓库同步产品Sku失败", e);
        }
    }
}
