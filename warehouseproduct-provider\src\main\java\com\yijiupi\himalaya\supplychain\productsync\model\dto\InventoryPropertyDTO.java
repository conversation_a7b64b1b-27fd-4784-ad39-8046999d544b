package com.yijiupi.himalaya.supplychain.productsync.model.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class InventoryPropertyDTO implements Serializable {
    /**
     * 本期ABC属性
     */
    private String abcAttribute;
    /**
     * 货主id
     */
    private String ownerId;
    /**
     * 规格id
     */
    private Long productSpeId;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    public String getAbcAttribute() {
        return abcAttribute;
    }

    @JSONField(name = "abc_attribute")
    public void setAbcAttribute(String abcAttribute) {
        this.abcAttribute = abcAttribute;
    }

    public String getOwnerId() {
        return ownerId;
    }

    @JSONField(name = "owner_id")
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpeId() {
        return productSpeId;
    }

    @JSONField(name = "product_spe_id")
    public void setProductSpeId(Long productSpeId) {
        this.productSpeId = productSpeId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    @JSONField(name = "warehouse_id")
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
