package com.yijiupi.himalaya.supplychain.productsync.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 库存abc属性分页结果
 */
public class InventoryPropertyPageResultDTO implements Serializable {
    private static final long serialVersionUID = -3812650016407904347L;
    private Integer pageStart;
    private Integer pageSize;
    private Integer total;
    private List<InventoryPropertyDTO> data;

    public Integer getPageStart() {
        return pageStart;
    }

    public void setPageStart(Integer pageStart) {
        this.pageStart = pageStart;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<InventoryPropertyDTO> getData() {
        return data;
    }

    public void setData(List<InventoryPropertyDTO> data) {
        this.data = data;
    }
}
