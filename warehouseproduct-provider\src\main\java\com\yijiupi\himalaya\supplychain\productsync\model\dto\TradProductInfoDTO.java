package com.yijiupi.himalaya.supplychain.productsync.model.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/9/17 10:58
 */
public class TradProductInfoDTO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 产品名称.
     */
    private String name;

    /**
     * 类目ID.
     */
    private Integer categoryId;

    /**
     * 品牌ID.
     */
    private Integer brandId;

    /**
     * 公司ID.
     */
    private Long companyId;

    /**
     * 产品信息状态
     */
    private Integer state;

    /**
     * 默认图片Id
     */
    private Integer defaultImageId;

    /**
     * 易尔惠大图
     */
    private Integer bigImageId;

    /**
     * 产品销售方式
     */
    private Integer productSaleType;

    /**
     * 保质期单位
     * <pre>0: 长期, 1: 年, 2: 月, 3: 日</pre>
     */
    private Integer shelfLifeType;

    /**
     * 保质期时长
     */
    private Integer shelfLife;

    /**
     * 产品介绍
     */
    private String intro;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * 产品分类
     * <p>统采独家、统采非独家、非统采</p>
     */
    private Integer productInfoType;

    /**
     * 产品来源类别
     */
    private Integer productInfoSourceType;

    /**
     * 产品管理类别
     */
    private Integer productInfoManagementType;

    /**
     * 系列
     */
    private String series;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产品助记码， 如茅台飞天的产品助记码可能是： MTFT
     */
    private String productCode;

    /**
     * 产品通用名称
     */
    private String generalName;

    /**
     * 产地
     */
    private String originalPlace;

    /**
     * 储藏方式
     */
    private String storageMethod;

    /**
     * 是否可拆包销售
     */
    private Boolean splittable;

    /**
     * 外部id
     */
    private String outerId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer createUserId;

    /**
     * 用途
     */
    private Integer useType;

    /**
     * 获取 id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置 id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取 产品名称.
     */
    public String getName() {
        return name;
    }

    /**
     * 设置 产品名称.
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取 类目ID.
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * 设置 类目ID.
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * 获取 品牌ID.
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * 设置 品牌ID.
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * 获取 公司ID.
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * 设置 公司ID.
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取 产品信息状态
     */
    public Integer getState() {
        return state;
    }

    /**
     * 设置 产品信息状态
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取 默认图片Id
     */
    public Integer getDefaultImageId() {
        return defaultImageId;
    }

    /**
     * 设置 默认图片Id
     */
    public void setDefaultImageId(Integer defaultImageId) {
        this.defaultImageId = defaultImageId;
    }

    /**
     * 获取 易尔惠大图
     */
    public Integer getBigImageId() {
        return bigImageId;
    }

    /**
     * 设置 易尔惠大图
     */
    public void setBigImageId(Integer bigImageId) {
        this.bigImageId = bigImageId;
    }

    /**
     * 获取 产品销售方式
     */
    public Integer getProductSaleType() {
        return productSaleType;
    }

    /**
     * 设置 产品销售方式
     */
    public void setProductSaleType(Integer productSaleType) {
        this.productSaleType = productSaleType;
    }

    /**
     * 保质期单位
     * <pre>0: 长期, 1: 年, 2: 月, 3: 日</pre>
     */
    public Integer getShelfLifeType() {
        return shelfLifeType;
    }

    /**
     * 保质期单位
     * <pre>0: 长期, 1: 年, 2: 月, 3: 日</pre>
     */
    public void setShelfLifeType(Integer shelfLifeType) {
        this.shelfLifeType = shelfLifeType;
    }

    /**
     * 获取 保质期时长
     */
    public Integer getShelfLife() {
        return shelfLife;
    }

    /**
     * 设置 保质期时长
     */
    public void setShelfLife(Integer shelfLife) {
        this.shelfLife = shelfLife;
    }

    /**
     * 获取 产品介绍
     */
    public String getIntro() {
        return intro;
    }

    /**
     * 设置 产品介绍
     */
    public void setIntro(String intro) {
        this.intro = intro;
    }

    /**
     * 获取 产品描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置 产品描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 产品分类
     * <p>统采独家、统采非独家、非统采</p>
     */
    public Integer getProductInfoType() {
        return productInfoType;
    }

    /**
     * 产品分类
     * <p>统采独家、统采非独家、非统采</p>
     */
    public void setProductInfoType(Integer productInfoType) {
        this.productInfoType = productInfoType;
    }

    /**
     * 获取 产品来源类别
     */
    public Integer getProductInfoSourceType() {
        return productInfoSourceType;
    }

    /**
     * 设置 产品来源类别
     */
    public void setProductInfoSourceType(Integer productInfoSourceType) {
        this.productInfoSourceType = productInfoSourceType;
    }

    /**
     * 获取 产品管理类别
     */
    public Integer getProductInfoManagementType() {
        return productInfoManagementType;
    }

    /**
     * 设置 产品管理类别
     */
    public void setProductInfoManagementType(Integer productInfoManagementType) {
        this.productInfoManagementType = productInfoManagementType;
    }

    /**
     * 获取 系列
     */
    public String getSeries() {
        return series;
    }

    /**
     * 设置 系列
     */
    public void setSeries(String series) {
        this.series = series;
    }

    /**
     * 获取 副标题
     */
    public String getSubTitle() {
        return subTitle;
    }

    /**
     * 设置 副标题
     */
    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    /**
     * 获取 产品助记码， 如茅台飞天的产品助记码可能是： MTFT
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * 设置 产品助记码， 如茅台飞天的产品助记码可能是： MTFT
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     * 获取 产品通用名称
     */
    public String getGeneralName() {
        return generalName;
    }

    /**
     * 设置 产品通用名称
     */
    public void setGeneralName(String generalName) {
        this.generalName = generalName;
    }

    /**
     * 获取 产地
     */
    public String getOriginalPlace() {
        return originalPlace;
    }

    /**
     * 设置 产地
     */
    public void setOriginalPlace(String originalPlace) {
        this.originalPlace = originalPlace;
    }

    /**
     * 获取 储藏方式
     */
    public String getStorageMethod() {
        return storageMethod;
    }

    /**
     * 设置 储藏方式
     */
    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod;
    }

    /**
     * 获取 是否可拆包销售
     */
    public Boolean getSplittable() {
        return splittable;
    }

    /**
     * 设置 是否可拆包销售
     */
    public void setSplittable(Boolean splittable) {
        this.splittable = splittable;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 创建人
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取 卖点
     */
    public String getSellingPoint() {
        return sellingPoint;
    }

    /**
     * 设置 卖点
     */
    public void setSellingPoint(String sellingPoint) {
        this.sellingPoint = sellingPoint;
    }

    /**
     * 获取 外部id
     */
    public String getOuterId() {
        return outerId;
    }

    /**
     * 设置 外部id
     */
    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    /**
     * 获取 用途
     */
    public Integer getUseType() {
        return useType;
    }

    /**
     * 设置 用途
     */
    public void setUseType(Integer useType) {
        this.useType = useType;
    }
}
