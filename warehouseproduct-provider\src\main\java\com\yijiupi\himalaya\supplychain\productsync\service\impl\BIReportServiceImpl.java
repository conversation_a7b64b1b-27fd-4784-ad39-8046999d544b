package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.BIReportServiceBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.bireport.*;
import com.yijiupi.himalaya.supplychain.productsync.service.IBIReportService;
import org.springframework.beans.factory.annotation.Autowired;

@org.springframework.stereotype.Service
/**
 * BI报表查询
 */
@Service
public class BIReportServiceImpl implements IBIReportService {

    @Autowired
    private BIReportServiceBL biReportServiceBL;

    @Override
    public WarehouseOverdueMonitorResultDTO
        getWarehouseOverdueMonitorReport(WarehouseOverdueMonitorRequestDTO request) {
        return biReportServiceBL.getWarehouseOverdueMonitorReport(request);
    }

    @Override
    public OverStockDetailResultDTO getOverStockDetailReport(OverStockDetailRequestDTO request) {
        return biReportServiceBL.getOverStockDetailReport(request);
    }

    @Override
    public ShelflifeDataResultDTO getShelflifeDataReport(ShelflifeDataRequestDTO request) {
        return biReportServiceBL.getShelflifeDataReport(request);
    }
}
