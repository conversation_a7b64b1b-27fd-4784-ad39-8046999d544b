package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.BomStrategyDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.BomStrategyQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IBomStrategyService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-11
 */
@Service
public class BomStrategyServiceImpl implements IBomStrategyService {

    @Override
    public List<BomStrategyDTO> list(BomStrategyQueryDTO bomStrategy) {
        return null;
    }

    @Override
    public void insert(BomStrategyDTO bomStrategy) {

    }

    @Override
    public void insertBatch(List<BomStrategyDTO> bomStrategys) {

    }

    @Override
    public void update(BomStrategyDTO bomStrategy) {

    }

    @Override
    public void updateBatch(List<BomStrategyDTO> bomStrategys) {

    }

    @Override
    public void delete(BomStrategyDTO bomStrategy) {

    }

}
