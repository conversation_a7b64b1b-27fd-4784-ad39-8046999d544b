package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCategoryGroupConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductCategoryGroupConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupConfigService;
import org.springframework.beans.factory.annotation.Autowired;

@Service(timeout = 300000)
public class ProductCategoryGroupConfigServiceImpl implements IProductCategoryGroupConfigService {

    @Autowired
    private ProductCategoryGroupConfigBL configBL;

    @Override
    public Long getCategoryGroupIdByParentOrgId(Integer parentOrgId, Long ownerId) {
        return configBL.getCategoryGroupIdByParentOrgId(parentOrgId, ownerId);
    }

    @Override
    public Long getCategoryGroupIdByOrgId(Integer orgId, Long ownerId) {
        return configBL.getCategoryGroupIdByOrgId(orgId, ownerId);
    }

    @Override
    public Long getCategoryGroupIdByOrgIdAndWarehouseId(Integer cityId, Integer warehouseId, Long ownerId) {
        return configBL.getCategoryGroupIdByOrgIdAndWarehouseId(cityId, warehouseId, ownerId);
    }

    @Override
    public Long getCategoryGroupIdByWarehouseId(Integer warehouseId, Long ownerId) {
        return configBL.getCategoryGroupIdByWarehouseId(warehouseId, ownerId);
    }

    @Override
    public void insertCategoryGroupConfig(ProductCategoryGroupConfigDTO dto) {
        configBL.insertCategoryGroupConfig(dto);
    }
}
