package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCategoryGroupBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupUpdateParam;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service(timeout = 300000)
public class ProductCategoryGroupServiceImpl implements IProductCategoryGroupService {

    @Autowired
    private ProductCategoryGroupBL productCategoryGroupBL;

    /**
     * 条件查询类目树
     */
    @Override
    public List<ProductCategoryGroupDTO> findProductCategoryGroupTree(ProductCategoryGroupQueryDTO dto) {
        return productCategoryGroupBL.findProductCategoryGroupTree(dto);
    }

    /**
     * 条件查询类目
     */
    @Override
    public PageList<ProductCategoryGroupDTO> pageListProductCategoryGroup(ProductCategoryGroupQueryDTO dto) {
        return productCategoryGroupBL.pageListProductCategoryGroup(dto);
    }

    /**
     * 新增类目
     */
    @Override
    public ProductCategoryGroupDTO addProductCategoryGroup(ProductCategoryGroupDTO productCategoryGroupDTO) {
        AssertUtils.notNull(productCategoryGroupDTO.getCategoryGroupId(), "类目分组id不能为空");
        return productCategoryGroupBL.addProductCategoryGroup(productCategoryGroupDTO);
    }

    /**
     * 编辑类目
     */
    @Override
    public void editProductCategoryGroup(ProductCategoryGroupDTO productCategoryGroupDTO) {
        AssertUtils.notNull(productCategoryGroupDTO.getId(), "类目id不能为空");
        productCategoryGroupBL.editProductCategoryGroup(productCategoryGroupDTO);
    }

    /**
     * 批量新增类目
     */
    @Override
    public void addBatchProductCategoryGroup(List<ProductCategoryGroupDTO> productCategoryGroupDTOS) {
        AssertUtils.notEmpty(productCategoryGroupDTOS, "数据不能为空");
        AssertUtils.notNull(productCategoryGroupDTOS.get(0).getCategoryGroupId(), "类目分组id不能为空");
        productCategoryGroupBL.addBatchProductCategoryGroup(productCategoryGroupDTOS);
    }

    /**
     * 批量删除类目
     */
    @Override
    public void deleteBatchProductCategoryGroup(List<Long> categoryIds) {
        productCategoryGroupBL.deleteBatchProductCategoryGroup(categoryIds);
    }

    /**
     * 根据类目ID获取外部类目ID
     * 
     * @return
     */
    @Override
    public String getRefCategoryId(Long categoryId) {
        return productCategoryGroupBL.getRefCategoryId(categoryId);
    }

    /**
     * 新增类目
     */
    @Override
    public ProductCategoryGroupDTO addSaasProductCategoryGroup(ProductCategoryGroupDTO productCategoryGroupDTO) {
        AssertUtils.notNull(productCategoryGroupDTO.getCategoryGroupId(), "类目分组id不能为空");
        return productCategoryGroupBL.addSaasProductCategoryGroup(productCategoryGroupDTO);
    }

    /**
     * saas批量新增类目
     */
    @Override
    public void addSaasBatchProductCategoryGroup(List<ProductCategoryGroupDTO> productCategoryGroupDTOS) {
        AssertUtils.notEmpty(productCategoryGroupDTOS, "数据不能为空");
        AssertUtils.notNull(productCategoryGroupDTOS.get(0).getCategoryGroupId(), "类目分组id不能为空");
        productCategoryGroupBL.addSaasBatchProductCategoryGroup(productCategoryGroupDTOS);
    }

    /**
     * 是否强制录入箱码（true：是 false：否）
     */
    @Override
    public boolean isMandatoryEntryBoxCode(Long productInfoId) {
        return productCategoryGroupBL.isMandatoryEntryBoxCode(productInfoId);
    }

    /**
     * 批量更新类目分组, 日期限制
     *
     * @param dto 更新参数
     */
    @Override
    public void updateCategoryGroupBatch(ProductCategoryGroupUpdateParam dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        AssertUtils.notEmpty(dto.getIds(), "id 不能为空");
        productCategoryGroupBL.updateCategoryGroupBatch(dto);
    }

    /**
     * 通过 id 查找类目组信息
     *
     * @param ids 类目组 id
     * @return 查询结果
     */
    @Override
    public List<ProductCategoryGroupDTO> listByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return productCategoryGroupBL.listByIds(ids);
    }
}
