package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCodeInfoAuditBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.*;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoAuditService;

@Service
public class ProductCodeInfoAuditServiceImpl implements IProductCodeInfoAuditService {

    @Autowired
    private ProductCodeInfoAuditBL productCodeInfoAuditBL;

    /**
     * 新增审核数据
     */
    @Override
    public void insertCodeInfoAudit(ProductCodeInfoAuditHandleDTO handleDTO) {
        productCodeInfoAuditBL.insertCodeInfoAudit(handleDTO);
    }

    /**
     * 审核【条码\箱码】数据
     */
    @Override
    public void auditCodeInfo(AuditProductCodeDTO auditDTO) {
        productCodeInfoAuditBL.auditCodeInfo(auditDTO);
    }

    /**
     * 分页查询审核数据
     */
    @Override
    public PageList<ProductCodeInfoAuditDTO> findCodeAuditInfoByPage(ProductCodeInfoAuditQueryDTO queryDTO) {
        return productCodeInfoAuditBL.findCodeAuditInfoByPage(queryDTO);
    }

    /**
     * 根据code + codeType 判断 code 是否存在， 如果存在给出提示信息
     *
     * @return String : null 则不存在，不为 null 则给出提示信息
     */
    @Override
    public String validateCodeIsExists(ProductCodeInfoDTO codeInfoDTO) {
        return productCodeInfoAuditBL.validateCodeIsExists(codeInfoDTO);
    }

}
