package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCodeInfoQueryBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class ProductCodeInfoQueryServiceImpl implements IProductCodeInfoQueryService {

    @Autowired
    private ProductCodeInfoQueryBL productCodeInfoQueryBL;

    @Override
    public Map<Long, List<ProductCodeInfoDTO>> findProductCodeBySkuIds(List<Long> skuIdList) {
        return productCodeInfoQueryBL.findProductCodeBySkuIds(skuIdList);
    }

    /**
     * 根据skuId查询条码\箱码 - 禁用与启用的code都返回
     *
     * @param skuIdList
     * @return
     */
    @Override
    public Map<Long, ProductCodeDTO> findProductCodeDTOBySkuIds(Integer cityId, List<Long> skuIdList) {
        return productCodeInfoQueryBL.findProductCodeDTOBySkuIds(cityId, skuIdList);
    }

    /**
     * 根据skuId查询启用条码\箱码 - 只返回启用的code
     *
     * @param skuIdList
     * @return
     */
    @Override
    public Map<Long, ProductCodeDTO> findEnableProductCodeBySkuIds(Integer cityId, List<Long> skuIdList) {
        return productCodeInfoQueryBL.findEnableProductCodeBySkuIds(cityId, skuIdList);
    }

    /**
     *
     * @param code
     * @param cityId
     * @return
     */
    @Override
    public List<Long> getProductSkuIdByCode(String code, Integer cityId) {
        return productCodeInfoQueryBL.getProductSkuIdByCode(code, cityId);
    }
}
