package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCodeInfoBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.*;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class ProductCodeInfoServiceImpl implements IProductCodeInfoService {

    @Autowired
    private ProductCodeInfoBL productCodeInfoBL;

    @Override
    public void batchInsert(List<ProductCodeInfoDTO> codeInfoDTOList) {
        productCodeInfoBL.batchInsert(codeInfoDTOList);
    }

    @Override
    public void batchUpdate(List<ProductCodeInfoDTO> codeInfoDTOList) {
        productCodeInfoBL.batchUpdate(codeInfoDTOList);
    }

    @Override
    public void batchDeleteByIdList(ProductCodeDeleteConditionDTO deleteConditionDTO) {
        productCodeInfoBL.batchDeleteByIdList(deleteConditionDTO);
    }

    @Override
    public void syncProductCodeInfo(List<SyncProductCodeInfoDTO> syncDTOList) {
        productCodeInfoBL.syncProductCodeInfo(syncDTOList);
    }

    @Override
    public Map<Long, List<ProductCodeInfoDTO>>
        findProductCodeInfoByCondition(ProductCodeQueryConditionDTO queryConditionDTO) {
        return productCodeInfoBL.findProductCodeInfoByCondition(queryConditionDTO);
    }

    @Override
    public List<ProductInfoDTO> findTotalProductInfo(ProductCodeQueryConditionDTO queryConditionDTO) {
        return productCodeInfoBL.findTotalProductInfo(queryConditionDTO);
    }

    @Override
    public void syncProductCodesBySkuId(List<Long> skuIds) {
        AssertUtils.notEmpty(skuIds, "产品SKU不能为空");
        productCodeInfoBL.syncProductCodesBySkuId(skuIds);
    }

    @Override
    public List<ProductCodeInfoDTO> findCodesByCommonDefaultCondition(ProductCodeQueryConditionDTO conditionDTO) {
        return productCodeInfoBL.findCodesByCommonDefaultCondition(conditionDTO);
    }

    @Override
    public List<ProductCodeInfoDTO> findCodesByCommonCondition(ProductCodeQueryConditionDTO conditionDTO) {
        return productCodeInfoBL.findCodesByCommonCondition(conditionDTO);
    }

    @Override
    public Map<String, List<ProductCodeInfoDTO>> findCodeInfoByCodes(ProductCodeQueryConditionDTO conditionDTO) {
        return productCodeInfoBL.findCodeInfoByCodes(conditionDTO);
    }

    @Override
    public void updateCodeIsDeleteById(ProductCodeInfoDTO dto) {
        productCodeInfoBL.updateCodeIsDeleteById(dto);
    }

    /**
     * 删除产品条码[瓶码]
     */
    @Override
    public void deleteBarCodeByCondition(DeleteProductInfoBarCodesDTO deleteBarCode) {
        productCodeInfoBL.deleteBarCodeByCondition(deleteBarCode);
    }
}
