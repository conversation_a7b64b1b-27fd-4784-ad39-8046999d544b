package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import java.util.List;

import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBL;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductInfoBySaasBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoBySaasService;

/**
 * 产品管理SAAS化
 *
 * <AUTHOR>
 * @date 2019-08-27 11:26
 */
@Service
public class ProductInfoBySaasServiceImpl implements IProductInfoBySaasService {

    @Autowired
    private ProductInfoBySaasBL productInfoBySaasBL;
    @Autowired
    private ProductSkuBL productSkuBL;

    /**
     * 产品信息列表
     * 
     * @return
     */
    @Override
    public PageList<ProductInfoDTO> listProductInfo(ProductInfoSO productInfoSO) {
        return productInfoBySaasBL.listProductInfo(productInfoSO);
    }

    /**
     * 获取产品信息详情
     * 
     * @return
     */
    @Override
    public ProductInfoDTO getProductInfo(Long productInfoId) {
        return productInfoBySaasBL.getProductInfo(productInfoId);
    }

    /**
     * 批量新增产品信息
     */
    @Override
    public void saveProductInfoBatch(List<ProductInfoDTO> productInfoDTOList) {
        productInfoBySaasBL.saveProductInfoBatch(productInfoDTOList);
    }

    /**
     * 新增产品信息
     * 
     * @return
     */
    @Override
    public Long saveProductInfo(ProductInfoDTO productInfoDTO) {
        return productInfoBySaasBL.saveProductInfo(productInfoDTO);
    }

    /**
     * 新增产品信息
     * 
     * @return
     */
    @Override
    public Long saveProductInfoAndSku(ProductInfoDTO productInfoDTO) {
        return productInfoBySaasBL.saveProductInfoAndSku(productInfoDTO);
    }

    @Override
    public PageList<ProductInfoDTO> listSimpleProductInfo(ProductInfoSO productInfoSO) {
        return productInfoBySaasBL.listSimpleProductInfo(productInfoSO);
    }

    /**
     * 修改产品信息
     */
    @Override
    public void updateProductInfo(ProductInfoDTO productInfoDTO) {
        productInfoBySaasBL.updateProductInfo(productInfoDTO);
    }

    /**
     * 修改产品规格
     */
    @Override
    public void updateProductInfoSpec(ProductInfoDTO productInfoDTO) {
        productInfoBySaasBL.updateProductInfoSpec(productInfoDTO);
    }

    /**
     * 查询城市未同步图片的产品DefaultImageFileId
     * 
     * @param cityId
     * @return
     */
    @Override
    public List<Integer> getDefaultImageFileId(String cityId) {
        AssertUtils.notNull(cityId, "城市id不能为空！");
        return productInfoBySaasBL.getDefaultImageFileId(cityId);
    }

    @Override
    public List<ProductSkuDTO> saveUploadProductInfo(List<ProductUploadDTO> productInfoDTOList) {
        return productSkuBL.saveUploadProductInfo(productInfoDTOList);
    }

    /**
     * 新增产品信息(仓库级别)
     * 
     * @return
     */
    @Override
    public Long saveProductInfoAndSkuWithWarehouseId(ProductInfoAndSkuDTO productInfoDTO) {
        return productInfoBySaasBL.saveProductInfoAndSkuWithWarehouseId(productInfoDTO);
    }

    /**
     * 删除产品信息及规格（工具）
     */
    @Override
    public void deleteProductInfo(ProductInfoDTO productInfoDTO) {
        productInfoBySaasBL.deleteProductInfo(productInfoDTO);
    }
}
