package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductInfoCategoryBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoCategoryService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service(timeout = 300000)
public class ProductInfoCategoryServiceImpl implements IProductInfoCategoryService {

    @Autowired
    private ProductInfoCategoryBL productInfoCategoryBL;

    /**
     * 分页条件查询
     * 
     * @param productInfoCategoryQueryDTO
     * @return
     */
    @Override
    public PageList<ProductInfoCategoryDTO>
        pageListProductInfoCategory(ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO) {
        return productInfoCategoryBL.pageListProductInfoCategory(productInfoCategoryQueryDTO);
    }

    /**
     * 根据skuId查询类目信息
     */
    @Override
    public List<ProductInfoCategoryDTO>
        findProductCategoryBySkuIds(ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO) {
        AssertUtils.notEmpty(productInfoCategoryQueryDTO.getSkuIds(), "skuId不能为空");
        return productInfoCategoryBL.findProductCategoryBySkuIds(productInfoCategoryQueryDTO);
    }

    /**
     * 同步产品类目
     * 
     * @param source 0-易酒批 2-知花知果
     */
    @Override
    public void syncProductInfoCategory(Integer source) {
        new Thread(() -> productInfoCategoryBL.syncProductInfoCategory(source)).start();
    }

    /**
     * 同步产品SKU的类目关系ID
     * 
     * @param source 0-易酒批 2-知花知果
     */
    @Override
    public void syncProductSkuCategoryId(Integer source) {
        productInfoCategoryBL.syncProductSkuCategoryId(source);
    }

    /**
     * 根据规格查询类目信息
     */
    @Override
    public List<ProductInfoCategoryDTO>
        findProductCategoryBySpecId(List<ProductCategoryQueryDTO> productCategoryQueryDTOS) {
        productCategoryQueryDTOS.forEach(query -> {
            AssertUtils.notNull(query.getProductSpecificationId(), "规格id不能为空");
        });
        return productInfoCategoryBL.findProductCategoryBySpecId(productCategoryQueryDTOS);
    }
}
