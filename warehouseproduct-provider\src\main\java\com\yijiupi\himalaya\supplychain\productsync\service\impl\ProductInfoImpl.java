package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfo;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductInfoBL;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoService;
import com.yijiupi.himalaya.supplychain.productsync.util.ConvertorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@Service(timeout = 300000)
public class ProductInfoImpl implements IProductInfoService {
    public static final String PRODUCT_TYPE = "productType";

    @Autowired
    private ProductInfoBL productInfoBL;

    /**
     * info新增
     *
     * @param json
     * @throws Exception
     * @return: void
     */
    @Override
    public void doAdd(String json) throws Exception {
        if (StringUtils.isEmpty(json)) {
            throw new ConvertorException("产品新增信息为空！");
        }
        ProductInfo dto = JSON.parseObject(json, ProductInfo.class);
        productInfoBL.insertProductInfo(dto, getProductType(json));
    }

    /**
     * info修改
     *
     * @param json
     * @throws Exception
     * @return: void
     */
    @Override
    public void doModify(String json) throws Exception {
        if (StringUtils.isEmpty(json)) {
            throw new ConvertorException("产品修改信息为空！");
        }
        ProductInfo dto = JSON.parseObject(json, ProductInfo.class);
        productInfoBL.updateProductInfo(dto, getProductType(json));
    }

    private Byte getProductType(String json) {
        Byte productInfoType = null;
        String productType = JSON.parseObject(json).getString(PRODUCT_TYPE);
        if (StringUtils.isNotEmpty(productType)) {
            productInfoType = Byte.valueOf(productType);
        }
        return productInfoType;
    }
}
