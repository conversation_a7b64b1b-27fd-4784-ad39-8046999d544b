package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductInfoSyncBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBL;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 产品信息同步服务实现
 *
 * <AUTHOR>
 */
@Service
public class ProductInfoServiceImpl implements IProductInfoSyncService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductInfoServiceImpl.class);
    // 每批次处理的数据量
    private static final int PAGE_SIZE = 5000;

    @Autowired
    private ProductInfoSyncBL productInfoSyncBL;

    @Autowired
    private ProductSkuBL productSkuBL;

    /**
     * 同步产品信息 - 分页处理，每批次处理5000条
     */
    @Override
    public void syncTradProductInfo() {
        LOG.info("【同步产品信息】开始");
        long startTotal = System.currentTimeMillis();

        // 设置产品来源类型
        Integer source = 0;
        int pageNum = 1;
        int totalProcessed = 0;

        // 分页获取产品信息ID并处理
        while (true) {
            long startBatch = System.currentTimeMillis();

            // 设置分页参数 - PageHelper会拦截后续的SQL查询并添加分页语句
            PageHelper.startPage(pageNum, PAGE_SIZE);

            // 获取当前页的产品InfoId - 这个查询会被PageHelper拦截，添加分页条件
            List<Long> batchInfoIds = productSkuBL.listProductInfoIdBySource(source);

            // 如果当前页没有数据，说明已经处理完所有数据
            if (batchInfoIds == null || batchInfoIds.isEmpty()) {
                LOG.info("【同步产品信息】第{}页无数据，查询结束", pageNum);
                break;
            }

            LOG.info("【同步产品信息】第{}页，获取{}条产品信息ID", pageNum, batchInfoIds.size());

            // 同步产品信息
            productInfoSyncBL.syncProductInfo(batchInfoIds);

            // 更新计数器
            totalProcessed += batchInfoIds.size();

            long endBatch = System.currentTimeMillis();
            LOG.info("【同步产品信息】第{}批次处理完成，本批处理{}条，累计处理{}条，耗时：{}ms",
                    pageNum, batchInfoIds.size(), totalProcessed, endBatch - startBatch);

            // 如果当前页数据量小于页大小，说明已经是最后一页
            if (batchInfoIds.size() < PAGE_SIZE) {
                LOG.info("【同步产品信息】当前页数据量小于页大小，处理完成");
                break;
            }

            // 查询下一页
            pageNum++;
        }

        long endTotal = System.currentTimeMillis();
        LOG.info("【同步产品信息】全部处理完成，共处理{}条记录，总耗时：{}ms", totalProcessed, endTotal - startTotal);
    }
} 