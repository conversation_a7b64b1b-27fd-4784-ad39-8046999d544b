package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductInfoSpecificationBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSpecificationSerivce;

/**
 * <AUTHOR>
 * @date 2020/5/23 11:04
 */

@Service
public class ProductInfoSpecificationSerivceImpl implements IProductInfoSpecificationSerivce {
    @Autowired
    private ProductInfoSpecificationBL productInfoSpecificationBL;

    @Override
    public ProductInfoSpecificationDTO selectByPrimaryKey(Long id) {
        return productInfoSpecificationBL.selectByPrimaryKey(id);
    }

    @Override
    public List<ProductInfoSpecificationDTO> listBySpecIds(List<Long> ids) {
        return productInfoSpecificationBL.listBySpecIds(ids);
    }

    @Override
    public List<ProductInfoSpecificationDTO> findByProductInfoIds(List<Long> productInfoIds) {
        return productInfoSpecificationBL.findByProductInfoIds(productInfoIds);
    }

    @Override
    public Map<Long, ProductSpecificationDTO> findByProductSkuIds(List<Long> productSkuIds, Integer warehouseId) {
        return productInfoSpecificationBL.findByProductSkuIds(productSkuIds, warehouseId);
    }

    @Override
    public PageList<ProductInfoSpecificationDTO> listSpecInfoByProductName(ProductInfoSO so) {
        return productInfoSpecificationBL.listSpecInfoByProductName(so);
    }
}
