package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductPropertyServiceBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.InventoryPropertyRequestDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuInventoryProperDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductPropertyService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 产品属性同步
 */
@Service
public class ProductPropertyServiceImpl implements IProductPropertyService {

    @Autowired
    private ProductPropertyServiceBL productPropertyServiceBL;

    /**
     * 产品属性同步
     */
    @Override
    public void productPropertySync() {
        productPropertyServiceBL.productPropertySync();
    }

    @Override
    public ProductSkuInventoryProperDTO getProductSkuInventoryProperty(InventoryPropertyRequestDTO request) {
        return productPropertyServiceBL.getProductSkuInventoryProperty(request);
    }
}
