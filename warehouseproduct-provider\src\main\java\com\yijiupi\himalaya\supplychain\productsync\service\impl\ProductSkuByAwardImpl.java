package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuDTO;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuByAwardBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.award.ProductAwardMessage;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuByAwardService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/6/6
 * @description 兑奖产品sku管理
 */
@Service(timeout = 30000)
public class ProductSkuByAwardImpl implements IProductSkuByAwardService {

    @Autowired
    private ProductSkuByAwardBL productSkuByAwardBL;

    /**
     * 创建产品sku
     * 
     * @return
     */
    @Override
    public void createProductInfoAndSku(ProductAwardMessage message) {
        productSkuByAwardBL.createProductInfoAndSku(message);
    }

    /**
     * 创建奖券产品信息
     *
     * @return
     */
    @Override
    public void createProductInfoByAward(ProductAwardMessage message) {
        productSkuByAwardBL.createProductInfoByAward(message);
    }

    /**
     * 创建奖券产品sku
     *
     * @return
     */
    @Override
    public ProductSkuDTO createProductSkuByAward(ProductSkuDTO productSkuDTO) {
        return productSkuByAwardBL.createProductSkuByAward(productSkuDTO);
    }

    /**
     * 创建奖券产品信息规格及sku信息
     *
     * @return
     */
    @Override
    public ProductSkuDTO createProductInfoAndSkuByAward(ProductSkuDTO productSkuDTO) {
        return productSkuByAwardBL.createProductInfoAndSkuByAward(productSkuDTO);
    }
}
