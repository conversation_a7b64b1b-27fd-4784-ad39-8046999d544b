package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuByFinanceBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.FinanceProductSkuCreateDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuByFinanceService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/18
 * @description 融销产品sku管理
 */
@Service(timeout = 30000)
public class ProductSkuByFinanceImpl implements IProductSkuByFinanceService {

    @Autowired
    private ProductSkuByFinanceBL productSkuByFinanceBL;

    /**
     * 创建产品sku
     * 
     * @return
     */
    @Override
    public void createProductSkuByFinance(FinanceProductSkuCreateDTO createDTO) {
        productSkuByFinanceBL.createProductSkuByFinance(createDTO);
    }
}
