package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBySaasBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuBySaasService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 产品SKU管理SAAS化
 *
 * <AUTHOR>
 * @date 2019-08-27 11:26
 */
@Service(timeout = 100000)
public class ProductSkuBySaasServiceImpl implements IProductSkuBySaasService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuBySaasBL.class);

    @Autowired
    private ProductSyncOpService productSyncOpService;

    @Autowired
    private ProductSkuBySaasBL productSkuBySaasBL;

    @Reference
    private IOrgService iOrgService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 产品SKU管理列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSkuListDTO> listProductSku(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.listProductSku(productSkuListSO);
    }

    /**
     * 产品SKU管理列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSkuListDTO> listProductSkuNew(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.listProductSkuNew(productSkuListSO);
    }

    /**
     * 获取产品SKU详情
     * 
     * @return
     */
    @Override
    public ProductSkuListDTO getProductSku(Long id) {
        return productSkuBySaasBL.getProductSku(id);
    }

    /**
     * 添加产品SKU
     */
    @Override
    public void saveProductSku(ProductSkuImportDTO productSkuImportDTO) {
        List<ProductSkuPO> productSkuPOS = productSkuBySaasBL.saveProductSku(productSkuImportDTO);
        productSyncOpService.syncProductSku2Op(productSkuImportDTO.getCityId(), productSkuImportDTO.getUserId(),
            productSkuPOS);

    }

    /**
     * 修改产品SKU
     */
    @Override
    public void updateProductSku(ProductSkuDTO productSkuDTO) {
        productSkuBySaasBL.updateProductSku(productSkuDTO);
    }

    @Override
    public void updateProductSkuNew(ProductSkuDTO productSkuDTO) {
        productSkuBySaasBL.updateProductSkuNew(productSkuDTO);
    }

    /**
     * 根据包装条码查询产品SKU
     */
    @Override
    public Map<String, List<ProductSkuListDTO>> getProductSkuByBoxCode(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.getProductSkuByBoxCode(productSkuListSO);
    }

    /**
     * 根据条码或者skuid查询产品SKU
     */
    @Override
    public Map<String, Map<String, List<ProductSaasSkuDTO>>> getProductSkuBySkuIdAndCodes(ProductSkuQueryDTO dto) {
        LOGGER.info("根据条码或者skuid查询产品SKU，参数：{}", JSON.toJSONString(dto));
        return productSkuBySaasBL.getProductSkuBySkuIdAndCodes(dto);
    }

    /**
     * 产品SKU管理列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSaasSkuDTO> listProductSkuSaas(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.listProductSkuSaas(productSkuListSO);
    }

    /**
     * 产品SKU管理列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSaasSkuDTO> listProductSkuSaasNoImage(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.listProductSkuSaasNoImage(productSkuListSO);
    }

    /**
     * 产品SKU管理列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSkuListDTO> getProductSkuList(ProductSkuListSO productSkuListSO) {
        LOGGER.info("查询产品参数:{}", JSON.toJSONString(productSkuListSO));
        AssertUtils.notNull(productSkuListSO.getWarehouseId(), "仓库id不能为空");
        return productSkuBySaasBL.getProductSkuList(productSkuListSO);
    }

    @Override
    public PageList<ProductSkuListDTO> saasListProductSku(ProductSkuListSO productSkuListSO) {
        return productSkuBySaasBL.saasListProductSku(productSkuListSO);
    }

    /**
     * 更新sku的 库存和补货上下限
     *
     * @param updateDTO
     */
    @Override
    public void saasUpdateInventoryRange(ProductSkuDTO updateDTO) {
        productSkuBySaasBL.saasUpdateInventoryRange(updateDTO);
    }
}
