package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuByWineBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoByWineSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByWineSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuListDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuByWineService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 微酒产品同步
 *
 * <AUTHOR>
 * @date 2019/3/4 15:18
 */
@Service(timeout = 30000)
public class ProductSkuByWineServiceImpl implements IProductSkuByWineService {

    @Autowired
    private ProductSkuByWineBL productSkuByWineBL;

    @Override
    public void saveProductInfoByWine(ProductInfoByWineSyncDTO productInfoByWineSyncDTO) {
        productSkuByWineBL.saveProductInfoByWine(productInfoByWineSyncDTO);
    }

    @Override
    public void updateProductInfoByWine(ProductInfoByWineSyncDTO productInfoByWineSyncDTO) {
        productSkuByWineBL.updateProductInfoByWine(productInfoByWineSyncDTO);
    }

    @Override
    public void saveProductSkuByWine(ProductSkuByWineSyncDTO productSkuByWineSyncDTO) {
        productSkuByWineBL.saveProductSkuByWine(productSkuByWineSyncDTO);
    }

    @Override
    public void updateProductSkuByWine(ProductSkuByWineSyncDTO productSkuByWineSyncDTO) {
        productSkuByWineBL.updateProductSkuByWine(productSkuByWineSyncDTO);
    }

    @Override
    @Deprecated
    public ProductSkuListDTO findSkuByInfo(Integer cityId, Long productInfoId, Long secOwnerId) {
        return productSkuByWineBL.findSkuByInfo(cityId, productInfoId, secOwnerId);
    }
}
