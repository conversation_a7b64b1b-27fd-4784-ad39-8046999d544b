package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentUpdateParam;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuConfigService;

/**
 * 产品sku配置
 *
 * <AUTHOR>
 * @since 2020-01-09 14:41
 */
@Service(timeout = 30000)
public class ProductSkuConfigServiceImpl implements IProductSkuConfigService {

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    /**
     * 批量保存产品sku配置
     */
    @Override
    public void saveProductSkuConfigBatch(List<ProductSkuConfigDTO> configDTOS) {
        productSkuConfigBL.saveProductSkuConfigBatch(configDTOS);
    }

    /**
     * 获取skuId所属的仓库
     */
    @Override
    public Map<Long, Set<Integer>> getProductSkuBelongWarehouseId(Set<Long> skuIds) {
        return productSkuConfigBL.getProductSkuBelongWarehouseId(skuIds);
    }

    /**
     * 根据SKUID+仓库ID，查询仓库是否关联产品
     */
    @Override
    public List<Long> getSkuConfigBySkuIdsAndWarehouseId(List<Long> skuIds, Integer warehouseId) {
        return productSkuConfigBL.getSkuConfigBySkuIdsAndWarehouseId(skuIds, warehouseId);
    }

    /**
     * 批量更新补货上下限
     *
     * @param param 更新参数
     */
    @Override
    public void updateReplenishmentInfo(ReplenishmentUpdateParam param) {
        AssertUtils.notNull(param.getWarehouseId(), "仓库 id 不能为空");
        productSkuConfigBL.updateReplenishmentInfo(param);
    }

    /**
     * 根据SKUID+仓库ID，查询仓库是否关联产品
     *
     * @param skuIds
     * @param warehouseId
     */
    @Override
    public List<ProductSkuConfigDTO> findSkuConfigBySkuIdsAndWarehouseId(List<Long> skuIds, Integer warehouseId) {
        AssertUtils.notEmpty(skuIds, "产品信息不能为空！");
        AssertUtils.notNull(warehouseId, "仓库信息不能为空！");
        return productSkuConfigBL.findSkuConfigBySkuIdsAndWarehouseId(skuIds, warehouseId);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        productSkuConfigBL.deleteByIds(ids);
    }

    @Override
    public void updateSkuConfigIsUnsalable(List<ProductSkuConfigDTO> configDTOS) {
        productSkuConfigBL.updateSkuConfigIsUnsalable(configDTOS);
    }

    /**
     * 查询所有绝对滞销的skuId
     */
    @Override
    public List<Long> listUnsalableSkuIds(Integer warehouseId, List<Long> productSkuIds) {
        return productSkuConfigBL.listUnsalableSkuIds(warehouseId, productSkuIds);
    }

    @Override
    public void updateSkuConfigBatch(List<ProductSkuConfigDTO> configDTOS) {
        productSkuConfigBL.updateSkuConfigBatch(configDTOS);
    }
}
