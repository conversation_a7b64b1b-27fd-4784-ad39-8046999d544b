package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.mq.ProductRelationGroupMQ;
import com.yijiupi.himalaya.supplychain.productsync.domain.mq.ProductSkuSyncMQ;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.PickUpModeConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuSyncMsgDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ScmProductSkuSyncMessage;
import com.yijiupi.himalaya.supplychain.productsync.util.ConvertorException;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;

@Service
public class ProductSkuImpl {
    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuImpl.class);

    @Autowired
    private ProductSkuBL productSkuBL;

    @Autowired
    private ProductSkuSyncMQ productSkuSyncMQ;

    @Autowired
    private ProductRelationGroupMQ productRelationGroupMQ;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    /**
     * 产品同步（交易平台化去SKU）
     *
     * @param json
     * @throws Exception
     * @return: void
     */
    public void sync(String json) {
        if (StringUtils.isEmpty(json)) {
            throw new ConvertorException("产品同步信息为空！");
        }
        // 处理sku
        ScmProductSkuSyncMessage productSkuMessage = JSON.parseObject(json, ScmProductSkuSyncMessage.class);
        processProductSync(Arrays.asList(productSkuMessage));
    }

    /**
     * 店铺产品批量同步（交易平台化去SKU）
     *
     * @param json
     * @throws Exception
     * @return: void
     */
    public void syncBatch(String json) {
        if (StringUtils.isEmpty(json)) {
            throw new ConvertorException("产品同步信息为空！");
        }
        // 处理sku
        List<ScmProductSkuSyncMessage> productSkuMessageList = JSON.parseArray(json, ScmProductSkuSyncMessage.class);
        processProductSync(productSkuMessageList);
    }

    /**
     * 批量同步SKU
     * 
     * @param productSkuMessageList
     */
    private void processProductSync(List<ScmProductSkuSyncMessage> productSkuMessageList) {
        if (CollectionUtils.isEmpty(productSkuMessageList)) {
            throw new DataValidateException("店铺产品批量同步不能为空");
        }
        List<ProductSkuSyncMsgDTO> syncMsgDTOS = new ArrayList<>();
        List<ProductRelationGroupAddDTO> groupAddList = new ArrayList<>();
        List<Long> syncEsProductSkuIds = new ArrayList<>();
        productSkuMessageList.forEach(productSkuSyncMessage -> {
            processSingleSku(syncMsgDTOS, groupAddList, syncEsProductSkuIds, productSkuSyncMessage);
        });
        // 同步sku给外部系统
        productSkuSyncMQ.send(syncMsgDTOS, true);
        // 发送产品关联消息
        // productRelationGroupMQ.send(groupAddList);
    }

    public void processSingleSku(List<ProductSkuSyncMsgDTO> syncMsgDTOS, List<ProductRelationGroupAddDTO> groupAddList,
        List<Long> syncEsProductSkuIds, ScmProductSkuSyncMessage productSkuSyncMessage) {

        // 虚仓+实仓取货的，特殊处理
        boolean isPick = Objects.equals(productSkuSyncMessage.getPickUpMode(), PickUpModeConstant.YIJIUPI_AND_AGENCY);
        if (isPick) {
            List<Integer> lstWarehouseIds =
                productSkuSyncMessage.getWarehouseIds().stream().distinct().collect(Collectors.toList());
            List<Warehouse> listWarehouseByIds = warehouseQueryService.listWarehouseByIds(lstWarehouseIds);
            List<Integer> lstXuCangWarehouseId = listWarehouseByIds.stream()
                .filter(p -> Objects.equals(p.getWarehouseType(), (int)WarehouseTypeEnum.虚仓实配.getType()))
                .map(p -> p.getId()).collect(Collectors.toList());
            List<Integer> lstShiCangWarehouse =
                lstWarehouseIds.stream().filter(p -> !lstXuCangWarehouseId.contains(p)).collect(Collectors.toList());
            // 同步实仓SKU
            productSkuSyncMessage.setWarehouseIds(new HashSet(lstShiCangWarehouse));
            productSkuSyncMessage.setPickUpMode(PickUpModeConstant.YIJIUPI);
            LOG.info("虚仓实配，批次SKU处理：{}", JSON.toJSONString(productSkuSyncMessage));
            processSingleSku(syncMsgDTOS, groupAddList, syncEsProductSkuIds, productSkuSyncMessage);

            if (CollectionUtils.isEmpty(lstXuCangWarehouseId)) {
                LOG.info("只有实仓，没有虚仓SKU，不需要处理：{}", JSON.toJSONString(productSkuSyncMessage));
                return;
            }

            // 同步虚仓SKU
            // 2021-11-04 交易代码调整，经销商ID传的值为1，排除此类数据
            if (productSkuSyncMessage.getDealerId() != null && productSkuSyncMessage.getDealerId() > 1) {
                productSkuSyncMessage.setCompanyId(productSkuSyncMessage.getDealerId());
                productSkuSyncMessage.setWarehouseIds(new HashSet(lstXuCangWarehouseId));
                LOG.info("虚仓实配，虚仓SKU处理：{}", JSON.toJSONString(productSkuSyncMessage));
            }
        }
        try {
            // 产品同步
            productSkuBL.syncProductSku(productSkuSyncMessage, syncMsgDTOS, groupAddList, syncEsProductSkuIds);
        } catch (Exception e) {
            LOG.info("productSkuSyncMessage：{}", JSON.toJSONString(productSkuSyncMessage));
            LOG.error("产品同步异常", e);
        }
    }

}
