package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuManageBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.mq.ProductRelationGroupMQ;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuCreateDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncSO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuManageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 产品sku管理
 *
 * <AUTHOR>
 * @date 2019-12-30 15:45
 */
@Service(timeout = 30000)
public class ProductSkuManageImpl implements IProductSkuManageService {

    @Autowired
    private ProductSkuManageBL productSkuManageBL;

    @Autowired
    private ProductRelationGroupMQ productRelationGroupMQ;

    @Autowired
    private ProductSkuBL productSkuBL;

    /**
     * 创建产品sku
     *
     * @return
     */
    @Override
    public Map<String, Long> createProductSku(List<ProductSkuCreateDTO> createDTOList) {
        return productSkuManageBL.createProductSku(createDTOList);
    }

    /**
     * 创建产品sku
     *
     * @return
     */
    @Override
    public Map<Long, ProductSkuDTO> createProductSkuWithResult(List<ProductSkuCreateDTO> createDTOList) {
        return productSkuManageBL.createProductSkuWithResult(createDTOList);
    }

    /**
     * 手动发送sku同步消息给外部系统
     */
    @Override
    public void sendProductSkuSyncMQ(Long skuId) {
        productSkuManageBL.sendProductSkuSyncMQ(skuId);
    }

    @Override
    public void retryProductRelationGroupMsg(List<ProductRelationGroupAddDTO> groupAddList) {
        AssertUtils.notEmpty(groupAddList, "重试产品分组关系参数为空！");
        // 发送产品关联消息
        productRelationGroupMQ.send(groupAddList);
    }

    /**
     * 手动修改产品sku状态
     */
    @Override
    public void updateProductState(Long skuId, Integer productState) {
        productSkuManageBL.updateProductState(skuId, productState);
    }

    /**
     * 同步产品sku状态
     */
    @Override
    public void syncProductSkuState(ProductSkuStateSyncSO syncSO) {
        productSkuManageBL.syncProductSkuState(syncSO);
    }

    /**
     * 同步产品sku状态
     */
    @Override
    public void updateProductSkuState(ProductSkuStateSyncDTO stateSyncDTO) {
        productSkuManageBL.updateProductSkuState(stateSyncDTO);
    }

    @Override
    public void markSkuCanBeOutOfStock(Integer warehouseId, List<Long> skuIds) {
        productSkuBL.markSkuCanBeOutOfStock(warehouseId, skuIds);
    }

    @Override
    public Map<Long, Boolean> checkSkuCanBeOutOfStock(Integer warehouseId, List<Long> skuIds) {
        return productSkuBL.checkSkuCanBeOutOfStock(warehouseId, skuIds);
    }
}
