package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncByEasyBusinessBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuByEasyBusinessDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSyncByEasyBusinessService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 易经商产品同步
 *
 * <AUTHOR>
 * @date 2019/7/4 16:25
 */
@Service(timeout = 30000)
public class ProductSyncByEasyBusinessServiceImpl implements IProductSyncByEasyBusinessService {

    @Autowired
    private ProductSyncByEasyBusinessBL productSyncByEasyBusinessBL;

    @Override
    public void addProductSku(List<ProductSkuByEasyBusinessDTO> productSkuByEasyBusinessDTOS) {
        productSyncByEasyBusinessBL.addProductSku(productSkuByEasyBusinessDTOS);
    }

    @Override
    public void updateProductSku(ProductSkuByEasyBusinessDTO productSkuByEasyBusinessDTO) {
        productSyncByEasyBusinessBL.updateProductSku(productSkuByEasyBusinessDTO);
    }
}
