package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductUnifySyncBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncBySscBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.PartyNameConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ssc.sync.SyncSscProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSyncBySscService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 产品同步（中台）
 *
 * <AUTHOR>
 * @date 11/24/20 11:39 AM
 */
@Service(timeout = 60000)
public class ProductSyncBySscImpl implements IProductSyncBySscService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSyncBySscImpl.class);

    @Autowired
    private ProductSyncBySscBL productSyncBySscBL;

    @Autowired
    ProductUnifySyncBL productUnifySyncBL;

    /**
     * 产品信息同步
     */
    @Override
    public void syncProductInfo(String json) {
        LOG.info("[中台]产品信息同步消息JSON：{}", json);
        if (StringUtils.isEmpty(json)) {
            throw new DataValidateException("[中台]产品信息同步消息为空！");
        }
        SyncSscProductInfoDTO productInfoDTO = JSON.parseObject(json, SyncSscProductInfoDTO.class);
        productSyncBySscBL.syncProductInfo(productInfoDTO);
    }

    /**
     * 产品SKU同步
     */
    @Override
    public void syncProductSku(String json) {
        LOG.info("[中台]产品SKU同步消息JSON：{}", json);
        if (StringUtils.isEmpty(json)) {
            throw new DataValidateException("[中台]产品SKU同步消息为空！");
        }
        SyncSscProductSkuDTO productSkuDTO = JSON.parseObject(json, SyncSscProductSkuDTO.class);
        productSyncBySscBL.syncProductSku(productSkuDTO);
    }

    @Override
    public void syncRetailUnifySku(Integer cityIdStart, Integer cityIdEnd) {
        LOG.info("[中台]新零售中台sku同步开始，起始cityId：{}，终止cityId：{}", cityIdStart, cityIdEnd);
        productUnifySyncBL.batchUnifySkuSync(cityIdStart, cityIdEnd, PartyNameConstant.RETAIL, 500);
    }

    @Override
    public void syncTrdUnifySku(Integer cityIdStart, Integer cityIdEnd) {
        LOG.info("[中台]交易中台sku同步开始，起始cityId：{}，终止cityId：{}", cityIdStart, cityIdEnd);
        productUnifySyncBL.batchUnifySkuSync(cityIdStart, cityIdEnd, PartyNameConstant.TRD, 500);
    }
}
