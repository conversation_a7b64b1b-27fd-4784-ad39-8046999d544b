package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncByZhzgBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncInitByZhzgBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductInfoSpecDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zhzg.SyncProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSyncByZhzgService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 知花知果
 *
 * <AUTHOR>
 * @date 2019/4/19 17:47
 */
@Service(timeout = 300000)
public class ProductSyncByZhzgServiceImpl implements IProductSyncByZhzgService {

    @Autowired
    private ProductSyncInitByZhzgBL productSyncInitByZhzgBL;

    @Autowired
    private ProductSyncByZhzgBL productSyncByZhzgBL;

    /**
     * 初始化知花知果产品信息和规格
     */
    @Override
    public void initProductInfo(Integer pageSize) {
        productSyncInitByZhzgBL.initProductInfo(pageSize);
    }

    /**
     * 初始化知花知果产品sku
     */
    @Override
    public void initProductSku(Integer pageSize) {
        productSyncInitByZhzgBL.initProductSku(pageSize);
    }

    /**
     * 同步产品信息
     */
    @Override
    public void saveProductInfo(SyncProductInfoDTO syncProductInfoDTO) {
        productSyncByZhzgBL.saveProductInfo(syncProductInfoDTO);
    }

    /**
     * 同步产品信息规格
     */
    @Override
    public void saveProductInfoSpec(SyncProductInfoSpecDTO syncProductInfoSpecDTO) {
        productSyncByZhzgBL.saveProductInfoSpec(syncProductInfoSpecDTO);
    }

    /**
     * 同步产品SKU
     */
    @Override
    public void saveProductSku(SyncProductSkuDTO syncProductSkuDTO) {
        productSyncByZhzgBL.saveProductSku(syncProductSkuDTO);
    }
}
