package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSyncByZskxBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zskx.ProductSkuByZskxDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zskx.ProductSkuByZskxSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.zskx.ProductSkuByZxkxQuery;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSyncByZskxService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 掌上快销
 *
 * <AUTHOR>
 * @date 2019-12-10 15:02
 */
@Service(timeout = 300000)
public class ProductSyncByZskxServiceImpl implements IProductSyncByZskxService {

    @Override
    public Boolean isZskcCity(Integer cityId) {
        return null;
    }

    @Override
    public Map<Long, Long> createProductSkuByCityId(Integer cityId, List<Long> productSkuIds) {
        return null;
    }

    @Override
    public PageList<ProductSkuByZskxDTO> productQuery(ProductSkuByZxkxQuery productSkuByZxkxQuery) {
        return null;
    }

    @Override
    public ProductSkuByZskxDTO productDetail(ProductSkuByZxkxQuery productSkuByZxkxQuery) {
        return null;
    }
}
