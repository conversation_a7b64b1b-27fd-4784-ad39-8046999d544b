package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.IntefaceRelevanceDTO;
import com.yijiupi.himalaya.supplychain.dto.IntefaceRelevanceQueryDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductCodeInfoQueryBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductCodeTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.*;
import com.yijiupi.himalaya.supplychain.productsync.dto.productcodeinfo.ProductCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoQueryService;
import com.yijiupi.himalaya.supplychain.productsync.util.HttpClientUtils;
import com.yijiupi.himalaya.supplychain.service.IIntefaceRelevanceService;

/**
 * 商品同步op接口
 *
 * <AUTHOR>
 * @date 2020/5/22 14:43
 */
@Service
public class ProductSyncOpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSyncOpService.class);

    @Reference
    private IProductCodeInfoQueryService iProductCodeInfoQueryService;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private IIntefaceRelevanceService intefaceRelevanceService;
    @Autowired
    private ProductSkuMapper productSkuMapper;
    @Autowired
    private ProductCodeInfoQueryBL productCodeInfoQueryBL;

    public DealerProductAddDTO convert2DealerProductAddDTO(List<ProductSaasSkuDTO> productSkuDTOS, Integer userId) {
        List<Long> skuIds = new ArrayList<>();
        productSkuDTOS.forEach(it -> skuIds.add(it.getProductSkuId()));
        if (CollectionUtils.isEmpty(skuIds)) {
            throw new BusinessException("skuId不能为空");
        }
        Map<Long, List<ProductCodeInfoDTO>> codeMap = productCodeInfoQueryBL.findProductCodeBySkuIds(skuIds);
        DealerProductAddDTO dealerProductAddDTO = new DealerProductAddDTO();
        dealerProductAddDTO.setCreateUser(userId.toString());
        List<ProductAddDTO> productAddDTOS = new ArrayList<>();
        productSkuDTOS.forEach(skuDTO -> {
            ProductAddDTO productAddDTO = new ProductAddDTO();
            productAddDTO.setBrandName(skuDTO.getProductBrand());
            productAddDTO.setCreateUser(userId.toString());
            productAddDTO.setProductName(skuDTO.getName());
            productAddDTO.setPrice(BigDecimal.ZERO);
            productAddDTO.setPriceUnit(skuDTO.getPackageName());
            productAddDTO.setProductType((byte)1);
            productAddDTO.setSpecification(skuDTO.getSpecificationName());
            productAddDTO.setEffectiveDate(new Date());
            productAddDTO.setUnitName(skuDTO.getUnitName());
            productAddDTO.setPackageName(skuDTO.getPackageName());
            productAddDTO.setQuantity(skuDTO.getPackageQuantity().intValue());
            productAddDTO.setSaleQuantity(skuDTO.getPackageQuantity().intValue());
            productAddDTO.setSaleSpecification(skuDTO.getSpecificationName());
            if (skuDTO.getCompanyId() != null) {
                productAddDTO.setDealerId(skuDTO.getCompanyId().toString());
            }
            productAddDTO.setId(skuDTO.getProductSkuId().toString());
            productAddDTO.setProductSpecificationId(skuDTO.getProductSpecificationId().toString());
            List<ProductCodeInfoDTO> productCodeInfoDTOS = codeMap.get(skuDTO.getProductSkuId());
            if (CollectionUtils.isNotEmpty(productCodeInfoDTOS)) {
                productAddDTO.setProductcode(getBarCode(productCodeInfoDTOS));
                productAddDTO.setProductboxcode(getBoxCode(productCodeInfoDTOS, skuDTO.getProductSpecificationId()));
            }
            productAddDTO.setSaleSpecification(productAddDTO.getSpecification());
            productAddDTO.setSaleQuantity(productAddDTO.getQuantity());
            productAddDTOS.add(productAddDTO);
        });
        dealerProductAddDTO.setProductList(productAddDTOS);
        dealerProductAddDTO.setDealerId(productAddDTOS.get(0).getDealerId());
        return dealerProductAddDTO;
    }

    /**
     * 设置包装条码
     */
    private String getBoxCode(List<ProductCodeInfoDTO> productCodeList, Long specificationId) {
        String barCode = "";
        if (CollectionUtils.isEmpty(productCodeList)) {
            return barCode;
        }
        // 获取包装条码
        List<String> boxCodeList = productCodeList.stream()
            .filter(p -> p != null && Objects.equals(p.getCodeType(), ProductCodeTypeEnum.BOX_CODE.getType())
                && Objects.equals(p.getProductSpecificationId(), specificationId))
            .map(ProductCodeInfoDTO::getCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(boxCodeList)) {
            // 用","拼接
            StringBuilder sb = new StringBuilder();
            boxCodeList.forEach(code -> {
                sb.append(",").append(code);
            });
            barCode = sb.substring(1);
        }
        return barCode;
    }

    /**
     * 设置产品条码
     */
    private String getBarCode(List<ProductCodeInfoDTO> productCodeList) {
        String bottleCode = "";
        if (CollectionUtils.isEmpty(productCodeList)) {
            return bottleCode;
        }
        List<String> bottleCodeList = productCodeList.stream()
            .filter(p -> p != null && Objects.equals(p.getCodeType(), ProductCodeTypeEnum.BAR_CODE.getType()))
            .map(p -> p.getCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bottleCodeList)) {
            // 用","拼接
            StringBuilder sb = new StringBuilder();
            bottleCodeList.forEach(code -> sb.append(",").append(code));
            bottleCode = sb.substring(1);
        }
        return bottleCode;
    }

    private void syncProduct2Op(Integer cityId, Integer userId, List<ProductSaasSkuDTO> productSkuDTOS) {
        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            LOGGER.info("同步产品数据:{}", JSON.toJSONString(productSkuDTOS));
            return;
        }
        if (userId == null) {
            userId = -1;
        }
        try {
            PageList<IntefaceRelevanceDTO> pageList = getIntefaceRelevances(cityId);
            if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
                LOGGER.info("第三方方案未配置,cityId:{}", cityId);
                return;
            }
            LOGGER.info("第三方方案配置请求结果:{}", JSON.toJSONString(pageList));
            IntefaceRelevanceDTO intefaceRelevanceDTO = pageList.getDataList().get(0);
            DealerProductAddDTO dealerProductAddDTO = convert2DealerProductAddDTO(productSkuDTOS, userId);
            doPost(dealerProductAddDTO, intefaceRelevanceDTO.getUrl());
        } catch (Exception e) {
            LOGGER.info("产品同步op失败", e);
        }
    }

    private void findProductInfoAndSyncOp(List<Long> infoIds, Integer orgId, Integer userId) {
        ProductSkuListSO productSkuListSO = new ProductSkuListSO();
        productSkuListSO.setProductInfoIdList(infoIds);
        productSkuListSO.setCityId(orgId);
        LOGGER.info("查询产品sku请求参数:{}", JSON.toJSONString(productSkuListSO));
        PageResult<ProductSaasSkuDTO> productSaasSkuDTOS = productSkuMapper.listProductSkuSaas(productSkuListSO);
        LOGGER.info("查询产品sku结果:{}", JSON.toJSONString(productSaasSkuDTOS));
        if (CollectionUtils.isNotEmpty(productSaasSkuDTOS)) {
            syncProduct2Op(orgId, userId, productSaasSkuDTOS);
        }
    }

    @Async
    public void findProductInfoAndSyncOp(List<ProductUploadDTO> productInfoDTOList) {
        List<Long> infoIds = new ArrayList<>();
        productInfoDTOList.stream().filter(it -> it.getId() != null).forEach(it -> infoIds.add(it.getId()));
        if (CollectionUtils.isEmpty(infoIds)) {
            LOGGER.info("产品信息id不存在，无法同步：{}", JSON.toJSONString(infoIds));
        }
        findProductInfoAndSyncOp(infoIds, productInfoDTOList.get(0).getOrgId(), -1);
    }

    @Async
    public void findProductInfoAndSyncOp(Long infoId, Integer orgId, Integer userId) {
        findProductInfoAndSyncOp(Collections.singletonList(infoId), orgId, userId);
    }

    /**
     * 同步产品到Op
     *
     * @param skuId
     * @param userId
     * @throws Exception
     */
    @Async
    public void findProductSkuAndSyncOp(Long skuId, Integer userId) {
        ProductSkuListDTO productSku = productSkuMapper.getProductSku(skuId);
        LOGGER.info("查询产品sku结果:{}", JSON.toJSONString(productSku));
        if (productSku == null) {
            return;
        }
        ProductSaasSkuDTO productSaasSkuDTO = new ProductSaasSkuDTO();
        BeanUtils.copyProperties(productSku, productSaasSkuDTO);
        syncProduct2Op(productSaasSkuDTO.getCityId(), userId, Collections.singletonList(productSaasSkuDTO));
    }

    private DealerProductAddDTO convertSku2DealerProductAddDTO(List<ProductSkuPO> productSkuDTOS, Integer userId) {
        DealerProductAddDTO dealerProductAddDTO = new DealerProductAddDTO();
        dealerProductAddDTO.setCreateUser(userId.toString());
        List<ProductAddDTO> productAddDTOS = new ArrayList<>();
        productSkuDTOS.forEach(skuDTO -> {
            ProductAddDTO productAddDTO = new ProductAddDTO();
            productAddDTO.setBrandName(skuDTO.getProductBrand());
            productAddDTO.setCreateUser(userId.toString());
            productAddDTO.setProductName(skuDTO.getName());
            productAddDTO.setPrice(BigDecimal.ZERO);
            productAddDTO.setPriceUnit(skuDTO.getPackageName());
            productAddDTO.setProductType((byte)1);
            productAddDTO.setSpecification(skuDTO.getSpecificationName());
            productAddDTO.setEffectiveDate(new Date());
            productAddDTO.setUnitName(skuDTO.getUnitName());
            productAddDTO.setPackageName(skuDTO.getPackageName());
            productAddDTO.setQuantity(skuDTO.getPackageQuantity().intValue());
            productAddDTO.setSaleQuantity(skuDTO.getPackageQuantity().intValue());
            productAddDTO.setSaleSpecification(skuDTO.getSpecificationName());
            if (skuDTO.getCompanyId() != null) {
                productAddDTO.setDealerId(skuDTO.getCompanyId().toString());
            }
            productAddDTO.setId(skuDTO.getProductSkuId().toString());
            productAddDTO.setProductSpecificationId(skuDTO.getProductSpecificationId().toString());
            productAddDTOS.add(productAddDTO);
        });
        dealerProductAddDTO.setProductList(productAddDTOS);
        dealerProductAddDTO.setDealerId(productAddDTOS.get(0).getDealerId());
        return dealerProductAddDTO;
    }

    @Async
    public void syncProductSku2Op(Integer cityId, Integer userId, List<ProductSkuPO> productSkuDTOS) {
        PageList<IntefaceRelevanceDTO> pageList = getIntefaceRelevances(cityId);
        if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
            LOGGER.info("第三方方案未配置,cityId:{}", cityId);
            return;
        }
        if (userId == null) {
            userId = -1;
        }
        try {
            LOGGER.info("第三方方案配置请求结果:{}", JSON.toJSONString(pageList));
            IntefaceRelevanceDTO intefaceRelevanceDTO = pageList.getDataList().get(0);
            DealerProductAddDTO dealerProductAddDTO = convertSku2DealerProductAddDTO(productSkuDTOS, userId);
            doPost(dealerProductAddDTO, intefaceRelevanceDTO.getUrl());
        } catch (Exception e) {
            LOGGER.info("产品同步op失败", e);
        }
    }

    private PageList<IntefaceRelevanceDTO> getIntefaceRelevances(Integer cityId) {
        IntefaceRelevanceQueryDTO queryDTO = new IntefaceRelevanceQueryDTO();
        Integer parentOrgBy = iOrgService.findParentOrgBy(cityId);
        queryDTO.setDirection((byte)2);
        queryDTO.setType((byte)1);
        queryDTO.setSysCode("EASYSALE");
        queryDTO.setCompanyId(parentOrgBy.longValue());
        LOGGER.info("第三方方案配置请求参数:{}", JSON.toJSONString(queryDTO));
        return intefaceRelevanceService.listIntefaceRelevance(queryDTO);
    }

    private static final String SUCCESS_CODE = "success";

    private void doPost(DealerProductAddDTO dealerProductAddDTO, String url) {
        LOGGER.info("产品同步请求参数:{}", JSON.toJSONString(dealerProductAddDTO));
        String result = HttpClientUtils.doPost(url, JSON.toJSONString(dealerProductAddDTO));
        LOGGER.info("产品同步请求结果:{}", result);
        BaseReturnDTO baseReturnDTO = JSON.parseObject(result, BaseReturnDTO.class);
        if (!SUCCESS_CODE.equals(baseReturnDTO.getResult())) {
            throw new BusinessException("同步错误：" + baseReturnDTO.getDesc());
        }
    }

}
