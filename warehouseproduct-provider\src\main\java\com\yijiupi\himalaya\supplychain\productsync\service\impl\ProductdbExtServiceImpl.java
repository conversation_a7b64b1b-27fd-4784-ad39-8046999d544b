package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductdbExtBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductdbExtReturnDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductdbExtService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
@Service
public class ProductdbExtServiceImpl implements IProductdbExtService {

    private static final String PRODUCTTABLENAME = "productinfo";
    private static final Integer DEFAULTUSERID = 1;
    private static final String ISTURE = "true";
    private static final String ISFALSE = "false";
    @Autowired
    private ProductdbExtBL productdbExtDTOBL;

    @Override
    public ProductdbExtDTO detail(Long id) {
        return productdbExtDTOBL.detail(id);
    }

    @Override
    public PageList<ProductdbExtDTO> pageList(ProductdbExtQueryDTO productdbExtQueryDTO) {
        return productdbExtDTOBL.pageList(productdbExtQueryDTO);
    }

    @Override
    public List<ProductdbExtDTO> list(ProductdbExtQueryDTO productdbExtDTO) {
        return productdbExtDTOBL.list(productdbExtDTO);
    }

    @Override
    public void insert(ProductdbExtDTO productdbExtDTO) {
        productdbExtDTOBL.insert(productdbExtDTO);
    }

    @Override
    public void insertBatch(List<ProductdbExtDTO> productdbExtDTOs) {
        productdbExtDTOBL.insertBatch(productdbExtDTOs);
    }

    @Override
    public void update(ProductdbExtDTO productdbExtDTO) {
        productdbExtDTOBL.update(productdbExtDTO);
    }

    @Override
    public void delete(ProductdbExtDTO productdbExtDTO) {
        productdbExtDTOBL.delete(productdbExtDTO);
    }

    private ProductdbExtDTO detailByProductId(Long productInfoId) {
        ProductdbExtQueryDTO queryDTO = new ProductdbExtQueryDTO();
        queryDTO.setDataId(productInfoId);
        queryDTO.setSourceTable(PRODUCTTABLENAME);
        List<ProductdbExtDTO> list = list(queryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public ProductdbExtReturnDTO getByProductId(Long productInfoId) {
        AssertUtils.notNull(productInfoId, "产品信息不能为空");
        ProductdbExtDTO detail = detailByProductId(productInfoId);
        if (detail == null) {
            return new ProductdbExtReturnDTO();
        }
        ProductdbExtReturnDTO productdbExtVO = new ProductdbExtReturnDTO();
        productdbExtVO.setProductInfoId(detail.getDataId());
        productdbExtVO.setForceChargeback(getBoolean(detail.getExt1()));
        return productdbExtVO;
    }

    @Override
    public List<ProductdbExtReturnDTO> getByProductIdList(List<Long> productInfoIdList) {
        AssertUtils.notEmpty(productInfoIdList, "产品信息不能为空");
        ProductdbExtQueryDTO queryDTO = new ProductdbExtQueryDTO();
        queryDTO.setDataIdList(productInfoIdList);
        queryDTO.setSourceTable(PRODUCTTABLENAME);
        return convertProductdbExtDTO2VO(list(queryDTO));
    }

    private List<ProductdbExtReturnDTO> convertProductdbExtDTO2VO(List<ProductdbExtDTO> list) {
        List<ProductdbExtReturnDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        list.forEach(it -> {
            ProductdbExtReturnDTO productdbExtVO = new ProductdbExtReturnDTO();
            productdbExtVO.setProductInfoId(it.getDataId());
            productdbExtVO.setForceChargeback(getBoolean(it.getExt1()));
            result.add(productdbExtVO);
        });
        return result;
    }

    private String setBoolean(boolean forceChargeback) {
        if (forceChargeback) {
            return ISTURE;
        } else {
            return ISFALSE;
        }
    }

    private boolean getBoolean(String forceChargeback) {
        if (ISTURE.equals(forceChargeback)) {
            return true;
        } else {
            return false;
        }
    }

}
