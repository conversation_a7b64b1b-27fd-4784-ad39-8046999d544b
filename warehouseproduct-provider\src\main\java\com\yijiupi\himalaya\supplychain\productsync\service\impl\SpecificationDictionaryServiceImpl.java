package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.SpecificationDictionaryBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionaryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionarySO;
import com.yijiupi.himalaya.supplychain.productsync.service.ISpecificationDictionaryService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 包装规格管理
 */
@Service(timeout = 300000)
public class SpecificationDictionaryServiceImpl implements ISpecificationDictionaryService {

    @Autowired
    private SpecificationDictionaryBL specificationDictionaryBL;

    @Reference
    private IOrgService iOrgService;

    /**
     * 包装规格管理列表
     * 
     * @return
     */
    @Override
    public PageList<SpecificationDictionaryDTO>
        listSpecificationDictionary(SpecificationDictionarySO specificationDictionarySO) {
        return specificationDictionaryBL.listSpecificationDictionary(specificationDictionarySO);
    }

    /**
     * 获取所有包装单位
     * 
     * @return
     */
    @Override
    public List<String> listUnitName() {
        return specificationDictionaryBL.listUnitName();
    }

    /**
     * 获取包装规格详情
     * 
     * @return
     */
    @Override
    public SpecificationDictionaryDTO getSpecificationDictionary(Long id) {
        return specificationDictionaryBL.getSpecificationDictionary(id);
    }

    /**
     * 批量新增包装规格管理
     */
    @Override
    public void saveSpecificationDictionaryBatch(List<SpecificationDictionaryDTO> specificationDictionaryDTOS) {
        specificationDictionaryBL.saveSpecificationDictionaryBatch(specificationDictionaryDTOS);
    }

    /**
     * 新增包装规格管理
     * 
     * @return
     */
    @Override
    public Long saveSpecificationDictionary(SpecificationDictionaryDTO specificationDictionaryDTO) {
        return specificationDictionaryBL.saveSpecificationDictionary(specificationDictionaryDTO);
    }

    /**
     * 修改包装规格管理
     * 
     * @return
     */
    @Override
    public void updateSpecificationDictionary(SpecificationDictionaryDTO specificationDictionaryDTO) {
        specificationDictionaryBL.updateSpecificationDictionary(specificationDictionaryDTO);
    }

    /**
     * 包装规格管理列表
     * 
     * @return
     */
    @Override
    public PageList<SpecificationDictionaryDTO>
        getSpecificationDictionaryList(SpecificationDictionarySO specificationDictionarySO) {
        specificationDictionarySO.setParentOrgId(findParentOrgBy(specificationDictionarySO.getParentOrgId()));
        return specificationDictionaryBL.listSpecificationDictionary(specificationDictionarySO);
    }

    public Integer findParentOrgBy(Integer cityId) {
        if (cityId == null) {
            return null;
        }
        Integer parentOrgId = 0;
        if (cityId != 0) {
            parentOrgId = iOrgService.findParentOrgBy(cityId);
        }
        return parentOrgId;
    }
}
