package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ThirdPartyProductConvertor;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdPartyProductsBl;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdPartyProductsRelevanceBl;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdproductsRelationBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsPO;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ThirdPartyProductsRelevancePO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSaasSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductAndRefDetailDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsAndRelevanceDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsRelevanceDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdPartyProductsRelevancePageDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdProductsRelevanceQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdProductsRelevanceWithSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdRelQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThridPartyQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.UpdateThirdProductDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IThirdProductManageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 第三方产品服务接口
 * 
 * <AUTHOR>
 * @date: 2019年12月17日 上午11:11:36
 */
@Service(timeout = 300000)
public class ThirdProductManageServiceImpl implements IThirdProductManageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdProductManageServiceImpl.class);

    @Autowired
    private ThirdPartyProductsRelevanceBl thirdPartyProductsRelevanceBl;
    @Autowired
    private ThirdPartyProductsBl thirdPartyProductsBl;
    @Autowired
    private ThirdproductsRelationBL thirdproductsrelationBl;

    @Override
    public ThirdPartyProductsRelevanceDTO detail(Long id) {
        ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO = thirdPartyProductsRelevanceBl.detail(id);
        return ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevanceDTO(thirdPartyProductsRelevancePO);
    }

    @Override
    public PageList<ThirdProductsRelevanceWithSkuDTO> pageList(ThirdProductsRelevanceQueryDTO queryDTO) {
        return thirdPartyProductsRelevanceBl.pageList(queryDTO);
    }

    @Override
    public PageList<ThirdPartyProductsAndRelevanceDTO>
        listPage(ThirdPartyProductsRelevancePageDTO thirdPartyProductsRelevance) {
        // PageList<ThirdPartyProductsAndRelevanceDTO> result = new PageList<ThirdPartyProductsRelevanceDTO>();
        return thirdPartyProductsRelevanceBl.listPage(thirdPartyProductsRelevance);
        // if (CollectionUtils.isEmpty(pageList.getDataList())) {
        // result.setDataList(new ArrayList<>());
        // result.setPager(new Pager());
        // return result;
        // }
        // result.setDataList(ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevanceDTOList(pageList.getDataList()));
        // result.setPager(pageList.getPager());
        // return result;
    }

    @Override
    public int insert(ThirdPartyProductsRelevanceDTO thirdPartyProductsRelevance) {
        ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePO(thirdPartyProductsRelevance);
        return thirdPartyProductsRelevanceBl.insert(thirdPartyProductsRelevancePO);
    }

    @Override
    public int insertBatch(List<ThirdPartyProductsRelevanceDTO> thirdPartyProductsRelevances) {
        if (CollectionUtils.isEmpty(thirdPartyProductsRelevances)) {
            throw new BusinessException("数据不能为空！");
        }
        List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevancePOs =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePOList(thirdPartyProductsRelevances);
        return thirdPartyProductsRelevanceBl.insertBatch(thirdPartyProductsRelevancePOs);
    }

    @Override
    public void updateRelevance(ThirdPartyProductsRelevanceDTO relevanceDTO) {
        ThirdPartyProductsRelevancePO po =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePO(relevanceDTO);
        thirdPartyProductsRelevanceBl.updateRelevance(po);
    }

    @Override
    public void batchUpdate(List<ThirdPartyProductsRelevanceDTO> thirdPartyProductsRelevances) {
        List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevancePOS =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePOList(thirdPartyProductsRelevances);
        thirdPartyProductsRelevanceBl.batchUpdate(thirdPartyProductsRelevancePOS);
    }

    @Override
    public int delete(ThirdPartyProductsRelevanceDTO thirdPartyProductsRelevance) {
        ThirdPartyProductsRelevancePO thirdPartyProductsRelevancePO =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePO(thirdPartyProductsRelevance);
        return thirdPartyProductsRelevanceBl.delete(thirdPartyProductsRelevancePO);
    }

    @Override
    public List<ThirdPartyProductsRelevanceDTO> listBySkuIds(List<String> skuIds) {
        return ThirdPartyProductConvertor
            .convertorTOThirdPartyProductRelevanceDTOList(thirdPartyProductsRelevanceBl.listBySkuIds(skuIds));
    }

    /**
     * 查询当前城市未绑定skuid，并绑定其他城市已经绑定wms 产品信息对应该城市的skuid
     * 
     * @param cityId
     */
    @Override
    public void findBindingProductSKU(Integer cityId, Integer warehouseId) {
        thirdPartyProductsRelevanceBl.findBindingProductSKU(cityId, warehouseId);
    }

    /**
     * 批量插入第三方产品信息
     */
    @Override
    public int insertBatchThirdPartyProducts(List<ThirdPartyProductsDTO> thirdPartyProductList) {
        if (CollectionUtils.isEmpty(thirdPartyProductList)) {
            throw new BusinessException("数据不能为空！");
        }
        List<ThirdPartyProductsPO> thirdPartyProductPOList =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductPOList(thirdPartyProductList);
        return thirdPartyProductsBl.insertBatch(thirdPartyProductPOList);
    }

    @Override
    public List<ThirdPartyProductsDTO> listThirdPartyProductsBySkuIds(List<String> skuIds, String cityId,
        String warehouseId) {
        return ThirdPartyProductConvertor
            .convertorTOThirdPartyProductDTOList(thirdPartyProductsBl.listBySkuIds(skuIds, cityId, warehouseId));
    }

    /**
     * 批量修改或者新增
     * 
     * @param thirdPartyProductsRelevances
     */
    @Override
    public void batchInsertOrUpdate(List<ThirdPartyProductsRelevanceDTO> thirdPartyProductsRelevances) {
        LOGGER.info("批量修改或者新增参数：{}", JSON.toJSONString(thirdPartyProductsRelevances));
        List<ThirdPartyProductsRelevancePO> thirdPartyProductsRelevancePOS =
            ThirdPartyProductConvertor.convertorTOThirdPartyProductRelevancePOList(thirdPartyProductsRelevances);
        thirdPartyProductsRelevanceBl.batchInsertOrUpdate(thirdPartyProductsRelevancePOS);
    }

    @Override
    public void deleteThirdPartyProduct(ThirdPartyProductsDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getIds())) {
            thirdPartyProductsBl.deleteByIds(dto.getIds());
        }
        if (CollectionUtils.isNotEmpty(dto.getRelevanceIds())) {
            thirdPartyProductsRelevanceBl.deleteByIds(dto.getRelevanceIds());
        }
        if (dto.getCreateTime() != null && dto.getLastUpdateTime() != null) {
            thirdPartyProductsBl.deleteByTime(dto);
            thirdPartyProductsRelevanceBl.deleteByTime(dto);
        }

    }

    /**
     * 查询第三方产品和管理产品详细数据
     * 
     * @param queryDTO
     * @return
     */
    @Override
    public PageList<ThirdPartyProductAndRefDetailDTO> listProdcutAndRef(ThridPartyQueryDTO queryDTO) {
        return thirdPartyProductsRelevanceBl.listProdcutAndRef(queryDTO);
    }

    @Override
    public PageList<String> listThirdSkuId(ThridPartyQueryDTO queryDTO) {
        return thirdPartyProductsRelevanceBl.listThirdSkuIds(queryDTO);
    }

    @Override
    public void updateProductFeature(UpdateThirdProductDTO dto) {
        ThirdPartyProductsPO detail = thirdPartyProductsBl.detail(dto.getThirdProductId());
        ThirdPartyProductsPO po = new ThirdPartyProductsPO();
        po.setId(detail.getId());
        po.setProductFeature(dto.getProductFeature());
        detail.setProductFeature(dto.getProductFeature());
        thirdPartyProductsBl.update(po);

        ThirdproductsRelationQueryDTO queryDTO = new ThirdproductsRelationQueryDTO();
        queryDTO.setCityId(dto.getCityId());
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setOrgId(dto.getServiceId());
        queryDTO.setThirdskuId(detail.getProductSkuId());
        List<ThirdproductsRelationDTO> list = thirdproductsrelationBl.list(queryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(it -> it.setProductFeature(dto.getProductFeature()));
        for (ThirdproductsRelationDTO thirdproductsRelationDTO : list) {
            ThirdproductsRelationDTO relationDTO = new ThirdproductsRelationDTO();
            relationDTO.setId(thirdproductsRelationDTO.getId());
            relationDTO.setProductFeature(dto.getProductFeature());
            thirdproductsrelationBl.update(relationDTO);
        }

    }

    @Override
    public void update(ThirdPartyProductsDTO dto) {
        ThirdPartyProductsPO po = new ThirdPartyProductsPO();
        po.setId(dto.getId());
        po.setProductName(dto.getProductName());
        thirdPartyProductsBl.update(po);
    }

    @Override
    public List<ProductSaasSkuDTO> listProductByProductNameAndSpecName(ThirdRelQueryDTO queryDTO) {
        return thirdPartyProductsRelevanceBl.listProductByProductNameAndSpecName(queryDTO);
    }

}
