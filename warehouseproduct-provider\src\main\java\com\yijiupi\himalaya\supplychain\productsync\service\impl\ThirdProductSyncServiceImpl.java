package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdProductSyncBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ThirdProductSkuSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IThirdProductSyncService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/9 15:28
 */

@Service
public class ThirdProductSyncServiceImpl implements IThirdProductSyncService {

    @Autowired
    private ThirdProductSyncBL thirdProductSyncBl;

    /**
     * 同步第三方产品
     * 
     * @param thirdProductSkuSyncDTOS
     */
    @Override
    public void syncThirdProducts(List<ThirdProductSkuSyncDTO> thirdProductSkuSyncDTOS) {
        thirdProductSyncBl.syncThirdProducts(thirdProductSkuSyncDTOS);
    }
}
