package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdproductsRelationBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.thirdproduct.ThirdproductsRelationQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IThirdproductsRelationService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-13
 */
@Service(timeout = 300000)
public class ThirdproductsRelationServiceImpl implements IThirdproductsRelationService {
    @Autowired(required = false)
    private ThirdproductsRelationBL thirdproductsrelationBl;

    @Override
    public ThirdproductsRelationDTO detail(String id) {
        return thirdproductsrelationBl.detail(id);
    }

    @Override
    public List<ThirdproductsRelationDTO> list(ThirdproductsRelationQueryDTO thirdproductsrelation) {
        return thirdproductsrelationBl.list(thirdproductsrelation);
    }

    @Override
    public void insert(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelationBl.insert(thirdproductsrelation);
    }

    @Override
    public void insertBatch(List<ThirdproductsRelationDTO> thirdproductsrelations) {
        thirdproductsrelationBl.insertBatch(thirdproductsrelations);
    }

    @Override
    public void update(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelationBl.update(thirdproductsrelation);
    }

    @Override
    public void delete(ThirdproductsRelationDTO thirdproductsrelation) {
        thirdproductsrelationBl.delete(thirdproductsrelation);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        thirdproductsrelationBl.deleteByIds(ids);
    }

}
