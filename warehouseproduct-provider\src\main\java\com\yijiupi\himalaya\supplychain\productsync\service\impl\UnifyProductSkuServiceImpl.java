package com.yijiupi.himalaya.supplychain.productsync.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ThirdProductSyncBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.UnifyProductSkuBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.unify.UnifyProductSkuDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IUnifyProductSkuService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 中台产品sku数据库操作
 * 
 * <AUTHOR>
 * @Date 2021/8/17 16:25
 */
@Service
public class UnifyProductSkuServiceImpl implements IUnifyProductSkuService {
    @Autowired
    private UnifyProductSkuBL unifyProductSkuBL;

    @Override
    public UnifyProductSkuDTO getUnifySkuBySkuId(Long skuId) {
        return unifyProductSkuBL.getUnifySkuBySkuId(skuId);
    }

    @Override
    public List<UnifyProductSkuDTO> listUnifySkuByInfoId(Long infoId) {
        return unifyProductSkuBL.listUnifySkuByInfoId(infoId);
    }
}
