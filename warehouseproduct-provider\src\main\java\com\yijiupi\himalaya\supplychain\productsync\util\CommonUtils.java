package com.yijiupi.himalaya.supplychain.productsync.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/30 19:07
 */
public class CommonUtils {

    /**
     * 拆分list
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }
        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

}
