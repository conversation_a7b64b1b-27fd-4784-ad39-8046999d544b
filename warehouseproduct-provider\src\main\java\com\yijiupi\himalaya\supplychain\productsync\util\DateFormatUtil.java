package com.yijiupi.himalaya.supplychain.productsync.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间转换类
 *
 * <AUTHOR> 2018/2/26
 */
public class DateFormatUtil {

    private DateFormatUtil() {}

    /**
     * 带时区日期时间format
     */
    public static final String FORMAT_TIMESTAMPWITHZONE = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSXXX";
    public static final String FORMAT_TIMESTAMP_WITH_T = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String FORMAT_TIMESTAMP = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_DATE = "yyyy-MM-dd";
    public static final String FORMAT_TIMESTAMPWITHZONEW_WITH_T = "yyyy-MM-dd'T'HH:mm:ss.SSS'+'SS:SS";
    public static final String FORMAT_TIMESTAMP_TWO = "yyyy-MM-dd HH:mm:ss.SSS";

    public static String formatDate(Date date, String timeStyle) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(timeStyle).format(date);
    }

    /**
     *
     * @param dateStr 时间字符串
     * @param format pattern格式
     * @return
     */
    public static Date genDateFromStr(String dateStr, String format) {
        return Date.from(LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(format))
            .atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 保质期单位 1：年(365) 2：月(30) 3：日(1)
     */
    private static Byte YEAR = 1;
    private static Integer YEAR_COUNT = 365;
    private static Byte MONTH = 2;
    private static Integer MONTH_COUNT = 30;
    private static Byte DAY = 3;
    private static Integer DAY_COUNT = 1;

    public static void main(String[] args) {
        System.out.println(Long.valueOf("9348820064364100225"));
        // System.out.println(DateFormatUtil.genDateFromString("2019-02-13 17:28", DateFormatUtil.FORMAT_TIMESTAMP));
    }
}
