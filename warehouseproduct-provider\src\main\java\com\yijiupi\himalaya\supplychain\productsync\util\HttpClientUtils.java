/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.productsync.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

import java.util.Map;

/**
 * @Title: HttpClientUtils.java
 * @Package com.yijiupi.himalaya.api.supplychain.util
 * @Description:
 * <AUTHOR>
 * @date 2017年10月23日 下午5:29:10
 * @version
 */
public class HttpClientUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtils.class);

    /**
     * post请求
     * 
     * @param url
     * @param body
     * @return
     */
    public static String doPost(String url, String body) {
        LOGGER.info("POST请求,url:" + url + "参数：" + body);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("Accept", "application/json");
            headers.add("Accpet-Encoding", "gzip");
            headers.add("Content-Encoding", "UTF-8");
            headers.add("Content-Type", "application/json; charset=UTF-8");
            HttpEntity<String> formEntity = new HttpEntity<>(body, headers);
            return RestClient.getClient().postForObject(url, formEntity, String.class);
        } catch (Exception e) {
            LOGGER.error("POST请求出错,url:" + url + "参数：" + body, e);
        }

        return null;
    }

}
