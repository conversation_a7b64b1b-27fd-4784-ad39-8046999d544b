package com.yijiupi.himalaya.supplychain.productsync.util;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024-07-04 17:13
 **/
@Deprecated
public class PageHelperUtils {

    private static final Logger logger = LoggerFactory.getLogger(PageHelperUtils.class);

    /**
     * 分页查询并返回所有组合后的数据
     *
     * @param param 查询参数
     * @param query 查询方法
     * @param <T>   返回参数类型
     * @param <P>   查询条件类型
     * @return 合并后的所有数据
     */
    public static <T extends Serializable, P extends PagerCondition> List<T> pageQuery(P param, PageQuery<T, P> query) {
        return pageQuery(param, param.getPageSize(), query);
    }

    /**
     * 异步分页消费数据
     *
     * @param param    分页查询参数, 这里需要一个深拷贝 Supplier
     * @param executor 线程池
     * @param query    查询方法
     * @param consumer 消费者
     * @param <T>      返回参数类型
     * @param <P>      查询条件类型
     * @return 一个 future, 包含所有消费者线程
     */
    public static <T extends Serializable, P extends PagerCondition> Future<?> pageConsumer(
            Supplier<P> param, Executor executor, PageQuery<T, P> query, Consumer<List<T>> consumer
    ) {
        return pageConsumer(param, param.get().getPageSize(), executor, query, consumer);
    }

    /**
     * 分页查询并返回所有组合后的数据
     *
     * @param param 查询参数
     * @param size  页面大小
     * @param query 查询方法
     * @param <T>   返回参数类型
     * @param <P>   查询条件类型
     * @return 合并后的所有数据
     */
    public static <T extends Serializable, P extends PagerCondition> List<T> pageQuery(P param, int size, PageQuery<T, P> query) {
        Integer pageNum = param.getCurrentPage();
        Integer pageSize = param.getPageSize();
        Integer totalPage = query.query(param).getPager().getTotalPage();
        List<T> result = IntStream.rangeClosed(1, totalPage).mapToObj(page -> {
            logger.info("pageQuery, 正在进行第 {} 次分页查询, page: {}, size: {}", page, page, size);
            param.setCurrentPage(page);
            return query.query(param);
        }).map(PageList::getDataList).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        // 还原入参引用
        param.setCurrentPage(pageNum);
        param.setPageSize(pageSize);
        return result;
    }

    /**
     * 异步分页消费数据
     *
     * @param param    分页查询参数, 这里需要一个深拷贝 Supplier
     * @param size     页面大小
     * @param executor 线程池
     * @param query    查询方法
     * @param consumer 消费者
     * @param <T>      返回参数类型
     * @param <P>      查询条件类型
     * @return 一个 future, 包含所有消费者线程
     */
    public static <T extends Serializable, P extends PagerCondition> Future<?> pageConsumer(
            Supplier<P> param, int size, Executor executor, PageQuery<T, P> query, Consumer<List<T>> consumer
    ) {
        Integer totalPage = query.query(param.get()).getPager().getTotalPage();
        return CompletableFuture.allOf(IntStream.rangeClosed(1, totalPage).mapToObj(page -> CompletableFuture.supplyAsync(() -> {
            long start = System.currentTimeMillis();
            logger.info("pageConsumer, 正在进行第 {} 次分页查询, page: {}, size: {}", page, page, size);
            P copiedParam = param.get();
            copiedParam.setCurrentPage(page);
            copiedParam.setPageSize(size);
            List<T> dataList = query.query(copiedParam).getDataList();
            long end = System.currentTimeMillis();
            logger.info("查询耗时: {}ms", end - start);
            return dataList;
        }, executor).thenAccept(it -> {
            long start = System.currentTimeMillis();
            consumer.accept(it);
            long end = System.currentTimeMillis();
            logger.info("后续操作耗时: {}ms", end - start);
        })).toArray(CompletableFuture<?>[]::new));
    }

    /**
     * 查询方法
     *
     * @param <T> 返回值类型
     * @param <P> 查询入参类型
     */
    public interface PageQuery<T extends Serializable, P extends PagerCondition> {
        PageList<T> query(P param);
    }

}
