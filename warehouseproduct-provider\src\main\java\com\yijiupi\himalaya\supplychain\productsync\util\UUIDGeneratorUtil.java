package com.yijiupi.himalaya.supplychain.productsync.util;

import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * UUID生成器 （uuid始终是19位，取值范围是：4611686018427387904 - 9223372036854775807)
 * 
 * <AUTHOR>
 * @date 2019-08-29 10:24
 */
public class UUIDGeneratorUtil {

    /**
     * UUID生成器实例
     */
    public static final String PRODUCT_INFO = "productInfo";
    public static final String PRODUCT_INFO_SPEC = "productInfoSpecificaiton";
    public static final String PRODUCT_SKU = "productSku";
    public static final String SPEC_DICTIONARY = "specificationdictionary";
    public static final String PRODUCT_CATEGORY_GROUP = "productCategoryGroup";
    public static final String PRODUCT_CATEGORY_RELATED = "productCategoryRelated";
    public static final String PRODUCT_CATEGORY_GROUP_CONFIG = "productCategoryGroupConfig";
    public static final String PRODUCT_INFO_CATEGORY = "productInfoCategory";
    public static final String UPLOAD_IMG = "uploadImg";
    public static final String PRODUCT_SKU_CONFIG = "productSkuConfig";
    public static final String PRODUCT_DB_EXT = "productdbext";

    public static final String UNIFY_PRODUCT_INFO = "unifyProductInfo";
    public static final String UNIFY_PRODUCT_INFO_SPEC = "unifyProductSpecificaiton";
    public static final String UNIFY_PRODUCT_SKU = "unifyProductSku";
    public static final String UNIFY_PRODUCT_PACKAGE = "unifyProductpackage";

    /**
     * 获取uuid
     * 
     * @param table
     * @return
     */
    public static long getUUID(String table) {
        return UUIDGenerator.getUUID(table);
    }
}
