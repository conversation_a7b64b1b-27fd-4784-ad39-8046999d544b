package com.yijiupi.himalaya.supplychain.productsync.util;

import org.springframework.stereotype.Component;

@Component
public class UuidUtil {

    private static int sequence = 100000;
    private static final int MAX_INDEX = 999999;

    public synchronized static Long getUUid() {
        UuidUtil.sequence++;
        // 产生一个时间戳
        long now = System.currentTimeMillis();
        String uuid = now + "" + sequence;
        if (sequence >= MAX_INDEX) {
            sequence = 100000;
        }
        return Long.parseLong(uuid);
    }

}
