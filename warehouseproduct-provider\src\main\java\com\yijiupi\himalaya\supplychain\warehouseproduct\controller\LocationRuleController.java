package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.LocationRuleBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AdministrativeRegionExtInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationDetailsDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationRuleParamDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleManageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleQueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/6
 */
@RestController
@RequestMapping("/location/rule")
public class LocationRuleController {

    @Autowired
    private ILocationRuleManageService iLocationRuleManageService;
    @Autowired
    private ILocationRuleQueryService iLocationRuleQueryService;
    @Autowired
    private LocationRuleBL locationRuleBL;

    /**
     * 保存设置信息
     *
     * @param locationRuleParamDTOList
     * @return
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public BaseResult save(@RequestBody List<LocationRuleParamDTO> locationRuleParamDTOList) {
        List<LocationRuleDTO> locationRuleDTOList = locationRuleParamDTOList.stream().map(locationRuleParamDTO -> {
            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            BeanUtils.copyProperties(locationRuleParamDTO, locationRuleDTO);

            locationRuleDTO.setRuleName(locationRuleParamDTO.getRuleName());
            locationRuleDTO.setRuleId(String.valueOf(locationRuleParamDTO.getRuleId()));
            AdministrativeRegionExtInfoDTO administrativeRegionExtInfoDTO = new AdministrativeRegionExtInfoDTO();
            BeanUtils.copyProperties(locationRuleParamDTO, administrativeRegionExtInfoDTO);
            locationRuleDTO.setExtInfo(JSON.toJSONString(administrativeRegionExtInfoDTO));

            return locationRuleDTO;
        }).collect(Collectors.toList());

        iLocationRuleManageService.saveRule(locationRuleDTOList);
        return BaseResult.getSuccessResult();
    }

    /**
     * 查询列表信息
     *
     * @param locationRuleDTO
     * @return
     */
    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    public ROResult<List<LocationRuleDTO>> queryLocationRuleList(@RequestBody LocationRuleDTO locationRuleDTO) {
        ROResult<List<LocationRuleDTO>> result = new ROResult<>();
        List<LocationRuleDTO> locationRuleDTOS = iLocationRuleQueryService.listLocation(locationRuleDTO);
        result.setData(locationRuleDTOS);
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    /**
     * 条件查询仓库出库位
     *
     * @param queryDTO
     * @return
     */
    @RequestMapping(value = "/findLocationRuleByCon", method = RequestMethod.POST)
    public ROResult<List<LocationRuleDTO>> findLocationRuleByCon(@RequestBody LocationRuleQueryDTO queryDTO) {
        List<LocationRuleDTO> locationRuleDTOS = locationRuleBL.findLocationRuleByCon(queryDTO);
        return ROResult.getResult(locationRuleDTOS);
    }

}
