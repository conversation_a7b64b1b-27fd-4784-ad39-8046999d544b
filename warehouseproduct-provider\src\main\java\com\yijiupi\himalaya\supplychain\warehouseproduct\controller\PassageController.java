package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.PassageBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.passage.EnablePassageDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@RestController
@RequestMapping(value = "/passage")
public class PassageController {

    @Autowired
    private PassageBL passageBL;

    /**
     * 启用通道
     *
     * @param passageDTO
     * @return
     */
    @RequestMapping(value = "/enablePassage", method = RequestMethod.POST)
    public BaseResult enablePassage(@RequestBody EnablePassageDTO passageDTO) {
        passageDTO.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());
        passageBL.enablePassage(passageDTO);
        return BaseResult.getSuccessResult();
    }

    /**
     * 停用通道
     *
     * @param passageDTO
     * @return
     */
    @RequestMapping(value = "/disablePassage", method = RequestMethod.POST)
    public BaseResult disablePassage(@RequestBody EnablePassageDTO passageDTO) {
        passageDTO.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());
        passageBL.disablePassage(passageDTO);
        return BaseResult.getSuccessResult();
    }
}
