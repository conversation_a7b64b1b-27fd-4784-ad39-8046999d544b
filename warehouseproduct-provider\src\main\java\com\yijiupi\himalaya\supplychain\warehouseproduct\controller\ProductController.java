
package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductCategoryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductLocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuServiceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationCheckResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ReplaceProductLocationCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;

/**
 * 产品
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@RestController
public class ProductController {
    private static final Logger LOG = LoggerFactory.getLogger(ProductController.class);
    @Autowired
    private ProductLocationBL productLocationBL;

    @Autowired
    private ProductCategoryBL productCategoryBL;

    @Reference
    private IProductInfoQueryService productInfoQueryService;

    @Autowired
    private ProductSkuBL productSkuBL;
    @Autowired
    private ProductSkuServiceBL productSkuServiceBL;

    /**
     * 产品关联货位条件查询
     */
    @RequestMapping(value = "/product/listProductLocationInfo", method = RequestMethod.POST)
    public BaseResult listProductLocationInfo(@RequestBody ProductInfoQueryParam productInfoQueryParam) {
        LOG.info("产品关联货位条件查询：{}", JSON.toJSONString(productInfoQueryParam));
        AssertUtils.notNull(productInfoQueryParam.getPageSize(), "每页大小不能为空");
        AssertUtils.notNull(productInfoQueryParam.getCurrentPage(), "页码不能为空");
        AssertUtils.notNull(productInfoQueryParam.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(productInfoQueryParam.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productInfoQueryParam.getContent(), "条码不能为空");
        PageList<ProductSkuInfoResultDTO> pageList = productLocationBL.listProductLocationInfo(productInfoQueryParam);
        ROResult<List<ProductSkuInfoResultDTO>> result = new ROResult<>(pageList.getDataList());
        result.setTotalCount(pageList.getPager() != null ? pageList.getPager().getRecordCount() : 0);
        return result;
    }

    /**
     * 根据skuIds批量查询类目日期配置信息，返回根据sku聚合
     *
     * @return
     */
    @RequestMapping(value = "/product/listSkuCategoryPeriod", method = RequestMethod.POST)
    public ROResult<List<ProductCategoryDTO>>
        listSkuCategoryPeriod(@RequestBody ProductInfoQueryParam productInfoQueryParam) {
        LOG.info("根据skuIds批量查询类目日期限制配置入参：{}", JSON.toJSONString(productInfoQueryParam));
        AssertUtils.notNull(productInfoQueryParam, "参数不能为空");
        AssertUtils.notNull(productInfoQueryParam.getSkuIds(), "skuid不能为空");
        List<ProductCategoryDTO> dtoList =
            productCategoryBL.getCategoryPeriodBySkuIds(productInfoQueryParam.getSkuIds());
        ROResult<List<ProductCategoryDTO>> result = new ROResult<>(dtoList);
        return result;
    }

    /**
     * 查找产品的关联货位
     */
    @RequestMapping(value = "/product/getProductLocationInfo", method = RequestMethod.POST)
    public BaseResult getProductLocationInfo(@RequestBody ProductSkuInfoQueryParam productSkuInfoQueryParam) {
        LOG.info("查找产品的关联货位：{}", JSON.toJSONString(productSkuInfoQueryParam));
        AssertUtils.notNull(productSkuInfoQueryParam.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(productSkuInfoQueryParam.getCityId(), "城市id不能为空");
        AssertUtils.notEmpty(productSkuInfoQueryParam.getProductSkuIds(), "产品skuId不能为空");
        List<ProductInfoLocationResultDTO> locationList =
            productLocationBL.getConfigLocationNoMerge(productSkuInfoQueryParam);
        ROResult<List<ProductInfoLocationResultDTO>> result = new ROResult<>(locationList);
        result.setTotalCount(locationList != null ? locationList.size() : 0);
        return result;
    }

    /**
     * 批量重新关联产品货位配置（支持校验和删除旧关联）
     */
    @RequestMapping(value = "/productlocation/replaceProductLocationBatch", method = RequestMethod.POST)
    public BaseResult replaceProductLocationBatch(@RequestBody ReplaceProductLocationBatchDTO replaceDTO) {
        LOG.info("批量重新关联产品货位配置入参：{}", JSON.toJSONString(replaceDTO));

        replaceDTO.setUserId(ThreadLocalUtil.getCurrentUserInfo());
        productLocationBL.replaceProductLocationBatch(replaceDTO);
        return BaseResult.getSuccessResult();
    }

    /**
     * 替换产品关联货位检查
     */
    @RequestMapping(value = "/productlocation/replaceProductLocationCheck", method = RequestMethod.POST)
    public BaseResult replaceProductLocationCheck(@RequestBody List<ReplaceProductLocationCheckDTO> checkDTOS) {
        LOG.info("替换产品关联货位检查入参：{}", JSON.toJSONString(checkDTOS));
        List<ProductLocationCheckResultDTO> lstResult = productLocationBL.replaceProductLocationCheck(checkDTOS);
        ROResult<List<ProductLocationCheckResultDTO>> result = new ROResult<>(lstResult);
        result.setTotalCount(lstResult != null ? lstResult.size() : 0);
        return result;
    }

    /**
     * 根据skuIds检验能否转残次品
     *
     * @return
     */
    @RequestMapping(value = "/product/checkCCPBySkuIds", method = RequestMethod.POST)
    public BaseResult checkCCPBySkuIds(@RequestBody CCPLimitSkuCheckDTO param) {
        LOG.info("根据skuIds批量检验能否转残次品：{}", JSON.toJSONString(param));
        AssertUtils.notNull(param, "参数不能为空");
        AssertUtils.notNull(param.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(param.getLstSkuId(), "SkuId不能为空");
        productInfoQueryService.checkCCPBySkuIds(param);
        return BaseResult.getSuccessResult();
    }

    /**
     * 批量标记SKU可以缺货
     *
     * @return
     */
    @RequestMapping(value = "/product/markSkuCanBeOutOfStock", method = RequestMethod.POST)
    public BaseResult markSkuCanBeOutOfStock(@RequestBody ProductSkuInfoQueryParam param) {
        LOG.info("批量标记SKU可以缺货：{}", JSON.toJSONString(param));
        AssertUtils.notNull(param, "参数不能为空");
        AssertUtils.notNull(param.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(param.getProductSkuIds(), "SkuId不能为空");
        productSkuBL.markSkuCanBeOutOfStock(param.getWarehouseId(), param.getProductSkuIds());
        return BaseResult.getSuccessResult();
    }

    /**
     * 产品分仓属性校验
     * http://yapi.yijiupidev.com/project/3225/interface/api/340477
     */
    @RequestMapping(value = "/product/productWarehouseAllocationTypeVerify", method = RequestMethod.POST)
    public BaseResult productWarehouseAllocationTypeVerify(@RequestBody ProductWarehouseAllocationTypeVerifyQuery query) {
        query.setUserId(ThreadLocalUtil.getCurrentUserInfo());
        productSkuServiceBL.productWarehouseAllocationTypeVerify(query);
        return BaseResult.getSuccessResult();
    }
}
