/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.supplychain.warehouseproduct.constant.WebConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto.ProductLocationInfoResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductLocationResultConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;

/**
 * 货位操作
 *
 * <AUTHOR>
 * @date 2025年8月8日
 */
@RestController
public class ProductLocationController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductLocationController.class);

    @Resource
    private LocationBL locationBL;

    @Autowired
    private ProductLocationResultConvert productLocationResultConvert;

    /**
     * 分页查询货位信息列表
     *
     */
    @RequestMapping(value = "/productlocation/pageList", method = RequestMethod.POST)
    public BaseResult findProductLocationPageList(@RequestBody LocationInfoQueryDTO dto) {
        LOGGER.info("分页查询货位信息列表入参：{}", JSON.toJSONString(dto));
        ROResult<List<ProductLocationInfoResult>> result = new ROResult<>();
        dto.setQueryRelatedProduct(true);
        PageList<ProductLocationInfoResult> dtoPageList = pageListProductLocation(dto);
        result.setData(dtoPageList.getDataList());
        result.setTotalCount(dtoPageList.getPager().getRecordCount());
        result.setResult(WebConstants.RESULT_SUCCESS);
        return result;
    }

    /**
     * 分页查询货位信息列表
     *
     * @param dto
     * @return
     */
    public PageList<ProductLocationInfoResult> pageListProductLocation(LocationInfoQueryDTO dto) {
        PageList<LoactionDTO> dtoPageList = locationBL.pageListLocation(dto);
        PageList<ProductLocationInfoResult> result = new PageList<>();
        if (dtoPageList == null || (dtoPageList.getDataList() == null && !dtoPageList.getDataList().isEmpty())) {
            result.setDataList(new ArrayList<>());
            result.setPager(new Pager(dto.getPageNum(), dto.getPageSize(), 0));
            return result;
        }
        List<ProductLocationInfoResult> list = productLocationResultConvert.convert(dtoPageList.getDataList());

        LOGGER.info(String.format("货位查询分页结果：%s,%s,%s", dtoPageList.getDataList().size(), list.size(),
            JSON.toJSONString(dtoPageList.getPager())));
        result.setDataList(list);
        result.setPager(dtoPageList.getPager());
        return result;
    }

}
