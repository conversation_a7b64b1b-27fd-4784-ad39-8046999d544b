
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionLocationCheckDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IPromotionStoreBatchService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 促销货位检查
 *
 * <AUTHOR>
 * @date 2025/5/12
 */
@RestController
public class PromotionCheckController {
    private static final Logger LOG = LoggerFactory.getLogger(PromotionCheckController.class);

    @Reference
    private IPromotionStoreBatchService iPromotionStoreBatchService;

    /**
     * 促销入库货位检查入参
     */
    @RequestMapping(value = "/promotioncheck/checkInStockLocation", method = RequestMethod.POST)
    public BaseResult checkInStockLocation(@RequestBody List<PromotionLocationCheckDTO> checkDTOS) {
        LOG.info("促销入库货位检查入参：{}", JSON.toJSONString(checkDTOS));
        iPromotionStoreBatchService.checkInStockLocation(checkDTOS);
        return BaseResult.getSuccessResult();
    }

    /**
     * 查询促销产品信息
     */
    @RequestMapping(value = "/product/listProductPromotionInfo", method = RequestMethod.POST)
    public BaseResult listProductPromotionInfo(@RequestBody PromotionStoreBatchQueryDTO queryDTO) {
        LOG.info("查询促销产品信息入参：{}", JSON.toJSONString(queryDTO));
        List<ProductPromotionStoreBatchDTO> lstResult = iPromotionStoreBatchService.queryByCondition(queryDTO);
        return new ROResult<>(lstResult);
    }
}
