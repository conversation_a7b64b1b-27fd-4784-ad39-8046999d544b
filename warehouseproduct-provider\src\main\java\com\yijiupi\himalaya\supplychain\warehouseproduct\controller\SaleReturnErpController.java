package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import com.yijiupi.himalaya.supplychain.warehouseproduct.controller.service.SaleReturnErpService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.erp.RedirectErpDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.GlobalCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2025/7/14 11:45
 * @Version 1.0
 */
@RestController
public class SaleReturnErpController {
	private static final Logger LOG = LoggerFactory.getLogger(SaleReturnErpController.class);
	@Autowired
	private SaleReturnErpService saleReturnErpService;


	@PostMapping("/redirectToErp/erp/**")
	public String redirectToErp(@RequestBody RedirectErpDTO dto, HttpServletRequest request) {
		LOG.info("redirectToErp:迁移到微服务:");
		String url = request.getRequestURL().toString();
		String[] urlStr =url.split("/redirectToErp/erp");

		return saleReturnErpService.redirectToErp(dto.getRequestParam(), urlStr[1]);
	}
}
