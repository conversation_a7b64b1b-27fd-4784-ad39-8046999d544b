package com.yijiupi.himalaya.supplychain.warehouseproduct.controller;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SortGroupBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.SortGroupConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupParam;
import com.yijiupi.himalaya.supplychain.waves.dto.base.BaseResult;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@RestController
@RequestMapping(value = "/sortGroup")
public class SortGroupController {

    @Autowired
    private SortGroupBL sortGroupBL;
    @Autowired
    private SortGroupConvert sortGroupConvert;
    private static final Logger LOGGER = LoggerFactory.getLogger(SortGroupController.class);


    /**
     * 启用分区
     *
     */
    @RequestMapping(value = "/enableSortGroup", method = RequestMethod.POST)
    public BaseResult enableSortGroup(@RequestBody SortGroupDTO dto) {
        AssertUtils.notNull(dto.getId(), "分区信息不能为空！");
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        LOGGER.info("启用分区，用户信息为:{}", userId);
        if (Objects.nonNull(userId)) {
            dto.setCreateUser(userId.toString());
        }
        sortGroupBL.enableSortGroup(dto);
        return BaseResult.getSuccessResult();
    }

    /**
     * 停用分区
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "/disableSortGroup", method = RequestMethod.POST)
    public BaseResult disableSortGroup(@RequestBody SortGroupDTO dto) {
        AssertUtils.notNull(dto.getId(), "分区信息不能为空！");
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        LOGGER.info("停用分区，用户信息为:{}", userId);
        if (Objects.nonNull(userId)) {
            dto.setCreateUser(userId.toString());
        }
        sortGroupBL.disableSortGroup(dto);
        return BaseResult.getSuccessResult();
    }

    /**
     * 修改分区
     *
     * @param param
     */
    @RequestMapping(value = "/updateGroup", method = RequestMethod.POST)
    public BaseResult updateGroup(@RequestBody SortGroupParam param) {
        AssertUtils.notNull(param.getId(), "分区ID不能为空");
        AssertUtils.notNull(param.getName(), "分区名称不能为空");
        param.setExecuteUser(ThreadLocalUtil.getUserId().toString());

        AssertUtils.notNull(param.getExecuteUser(), "操作人不能为空");
        sortGroupBL.updateGroup(sortGroupConvert.convertToGroupDTO(param));
        return BaseResult.getSuccessResult();
    }

}
