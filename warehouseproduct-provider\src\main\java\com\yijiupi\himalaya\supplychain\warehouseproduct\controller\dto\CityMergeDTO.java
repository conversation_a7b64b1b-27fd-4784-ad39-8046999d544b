package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/14 14:13
 * @Version 1.0
 */
public class CityMergeDTO implements Serializable {
	private Integer cityId;
	private Integer warehouseId;
	private Long productSpecificationId;
	private Long productOwnerId;
	private Long productSecOwnerId;
	private BigDecimal count;
	private String businessNo;

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Integer warehouseId) {
		this.warehouseId = warehouseId;
	}

	public Long getProductSpecificationId() {
		return productSpecificationId;
	}

	public void setProductSpecificationId(Long productSpecificationId) {
		this.productSpecificationId = productSpecificationId;
	}

	public Long getProductOwnerId() {
		return productOwnerId;
	}

	public void setProductOwnerId(Long productOwnerId) {
		this.productOwnerId = productOwnerId;
	}

	public Long getProductSecOwnerId() {
		return productSecOwnerId;
	}

	public void setProductSecOwnerId(Long productSecOwnerId) {
		this.productSecOwnerId = productSecOwnerId;
	}

	public BigDecimal getCount() {
		return count;
	}

	public void setCount(BigDecimal count) {
		this.count = count;
	}

	public String getBusinessNo() {
		return businessNo;
	}

	public void setBusinessNo(String businessNo) {
		this.businessNo = businessNo;
	}
}
