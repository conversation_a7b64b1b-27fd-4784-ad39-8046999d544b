/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年8月8日
 */
public class LocationProductInfoResult implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    /**
     * 产品SkuId
     */
    private Long skuId;
    /**
     * 产品sku的名称
     */
    private String productSkuName;

    /**
     * 货主名称
     */
    private String ownerName;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    /**
     * 获取ownerName
     * 
     * @return ownerName ownerName
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     * 设置ownerName
     * 
     * @param ownerName ownerName
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
}
