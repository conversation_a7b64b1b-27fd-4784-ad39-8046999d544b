/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年8月8日
 */
public class ProductLocationInfoResult implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
    /**
     * 区域
     */
    private String area;
    /**
     * 区域id
     */
    private Long area_Id;
    /**
     * 货位排序
     */
    private Integer sequence;
    /**
     * 巷道
     */
    private String roadway;
    /**
     * 货位
     */
    private String productlocation;
    /**
     * 托盘
     */
    private String pallets;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 是否混放
     */
    private Byte isChaosPut;
    /**
     * 是否混批
     */
    private Byte isChaosBatch;
    /**
     * 类型，0：货位，1：货区',
     */
    private Byte category;
    /**
     * 货区/货区类型
     */
    private Byte subcategory;
    /**
     * 货区/货区类型名称
     */
    private String subcategoryName;
    private Integer locationCapacity;
    private List<Long> productSkuIdList;
    private List<LocationProductInfoResult> productSkuList;

    /**
     * 货位分级属性，0：未设置，1：A，2：B，3：C
     */
    private Integer locationGrade;

    /**
     * 快递直发集货位 0：否 1：是
     */
    private Byte express;
    /**
     * 货位状态 0：停用，1：启用
     */
    private Byte state;

    /**
     * 巷道id
     */
    private Long aisleId;

    /**
     * 巷道编号
     */
    private String aisleNo;

    /**
     * 业务类型，1：赠品区
     */
    private Byte businessType;

    /**
     * 通道id
     */
    private Long passageId;
    /**
     * 通道名称
     */
    private String passageName;

    /**
     * 分仓属性：1、酒饮，2、休百
     */
    private Integer storageAttribute;

    public Byte getExpress() {
        return express;
    }

    public void setExpress(Byte express) {
        this.express = express;
    }

    public Integer getLocationGrade() {
        return locationGrade;
    }

    public void setLocationGrade(Integer locationGrade) {
        this.locationGrade = locationGrade;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getRoadway() {
        return roadway;
    }

    public void setRoadway(String roadway) {
        this.roadway = roadway;
    }

    public String getProductlocation() {
        return productlocation;
    }

    public void setProductlocation(String productlocation) {
        this.productlocation = productlocation;
    }

    public String getPallets() {
        return pallets;
    }

    public void setPallets(String pallets) {
        this.pallets = pallets;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<LocationProductInfoResult> getProductSkuList() {
        return productSkuList;
    }

    public void setProductSkuList(List<LocationProductInfoResult> productSkuList) {
        this.productSkuList = productSkuList;
    }

    /**
     * 获取area_Id
     *
     * @return area_Id area_Id
     */
    public Long getArea_Id() {
        return area_Id;
    }

    /**
     * 设置area_Id
     *
     * @param area_Id area_Id
     */
    public void setArea_Id(Long area_Id) {
        this.area_Id = area_Id;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取 是否混放
     */
    public Byte getIsChaosPut() {
        return this.isChaosPut;
    }

    /**
     * 设置 是否混放
     */
    public void setIsChaosPut(Byte isChaosPut) {
        this.isChaosPut = isChaosPut;
    }

    /**
     * 获取 是否混批
     */
    public Byte getIsChaosBatch() {
        return this.isChaosBatch;
    }

    /**
     * 设置 是否混批
     */
    public void setIsChaosBatch(Byte isChaosBatch) {
        this.isChaosBatch = isChaosBatch;
    }

    /**
     * 获取 类型，0：货位，1：货区',
     */
    public Byte getCategory() {
        return this.category;
    }

    /**
     * 设置 类型，0：货位，1：货区',
     */
    public void setCategory(Byte category) {
        this.category = category;
    }

    /**
     * 获取 货区/货区类型
     */
    public Byte getSubcategory() {
        return this.subcategory;
    }

    /**
     * 设置 货区/货区类型
     */
    public void setSubcategory(Byte subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 货区/货区类型名称
     */
    public String getSubcategoryName() {
        return this.subcategoryName;
    }

    /**
     * 设置 货区/货区类型名称
     */
    public void setSubcategoryName(String subcategoryName) {
        this.subcategoryName = subcategoryName;
    }

    public List<Long> getProductSkuIdList() {
        return this.productSkuIdList;
    }

    public void setProductSkuIdList(List<Long> productSkuIdList) {
        this.productSkuIdList = productSkuIdList;
    }

    public Integer getLocationCapacity() {
        return this.locationCapacity;
    }

    public void setLocationCapacity(Integer locationCapacity) {
        this.locationCapacity = locationCapacity;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Long getAisleId() {
        return aisleId;
    }

    public void setAisleId(Long aisleId) {
        this.aisleId = aisleId;
    }

    public String getAisleNo() {
        return aisleNo;
    }

    public void setAisleNo(String aisleNo) {
        this.aisleNo = aisleNo;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Long getPassageId() {
        return passageId;
    }

    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }

    public String getPassageName() {
        return passageName;
    }

    public void setPassageName(String passageName) {
        this.passageName = passageName;
    }

    public Integer getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Integer storageAttribute) {
        this.storageAttribute = storageAttribute;
    }
}
