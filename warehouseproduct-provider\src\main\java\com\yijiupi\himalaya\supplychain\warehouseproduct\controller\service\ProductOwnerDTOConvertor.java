package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2024/4/9
 */
public class ProductOwnerDTOConvertor {

    public static List<ProductOwnerInfoDTO> convertByInventoryReport(List<InventoryReportDTO> inventoryReportDTOList) {
        if (CollectionUtils.isEmpty(inventoryReportDTOList)) {
            return Collections.emptyList();
        }
        return inventoryReportDTOList.stream().map(m -> {
            ProductOwnerInfoDTO productOwnerDTO = new ProductOwnerInfoDTO();
            productOwnerDTO.setOwnerId(m.getOwnerId());
            productOwnerDTO.setProductSpecId(m.getProductSpecificationId());
            return productOwnerDTO;
        }).collect(Collectors.toList());

    }

}
