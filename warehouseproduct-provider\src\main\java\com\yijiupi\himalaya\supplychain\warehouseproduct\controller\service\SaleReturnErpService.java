package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.store.FindStoreInfoVO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.store.FindStorePageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.erp.ErpGetPurchaseReturnProductListQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.erp.ErpGetPurchaseReturnProductListResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.erp.ErpQueryResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ErpRequestEnums;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.GlobalCache;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/14 11:49
 * @Version 1.0
 */
@Component
public class SaleReturnErpService {
	private static final String erpAPIUrl = "http://in-erp5-innerapi.yjp.com";
	private static final ObjectMapper objectMapper = new ObjectMapper();
	private static final Logger LOGGER = LoggerFactory.getLogger(SaleReturnErpService.class);

	@Autowired
	private RestTemplate restTemplate;
	@Autowired
	private StoreInfoApiService storeInfoApiService;
	@Autowired
	private IProductSkuService iProductSkuService;
	@Autowired
	private IProductSkuQueryService iProductSkuQueryService;
	@Reference
	private IBatchInventoryQueryService iBatchInventoryQueryService;
	@Reference
	private OwnerService ownerService;
	@Reference
	private IAdminUserQueryService iAdminUserQueryService;
	@Autowired
	private ProductSkuQueryBL productSkuQueryBL;
	@Autowired
	private GlobalCache globalCache;

	public String redirectToErp(Object request, String url) {

		return request(request, url);
	}

	private String request(Object request, String url) {
		LOGGER.info("请求erp的url:{}; 入参：{}", JSON.toJSONString(url), JSON.toJSONString(request));
		ErpRequestEnums enums = ErpRequestEnums.getEnums(url);
		try {

			if (Objects.isNull(enums)) {
				throw new BusinessValidateException("url不匹配");
			}

			if (enums.getMethod().equals(HttpMethod.GET)) {
				return restTemplate.getForObject(erpAPIUrl.concat(url), String.class);
			}

			if (ErpRequestEnums.获取可退产品.getUrl().contains(url)){
				return getPurchaseReturnProductList(request, url);
			}

			if (ErpRequestEnums.提交采购退申请.getUrl().contains(url)){
				return submitRequisition(request, url);
			}

			if (enums.getMethod().equals(HttpMethod.POST)) {
				if (Objects.nonNull(request)) {
					String requestBody = objectMapper.writeValueAsString(request);
					return restTemplate.postForObject(erpAPIUrl.concat(url), buildEntity(requestBody), String.class);
				}

			}
		} catch (Exception e) {
			LOGGER.warn("调用erp失败，参数：" + JSON.toJSONString(request), e);
			throw new BusinessValidateException("调用erp失败");
		}

		throw  new BusinessValidateException("url不匹配");
	}

	private String submitRequisition(Object request, String url) {
		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(request));
		int userId = ThreadLocalUtil.getCurrentUserInfo();
		Map<Integer, AdminUser> adminUserMap = iAdminUserQueryService.findUserByIds(Collections.singletonList(userId));
		if (CollectionUtils.isEmpty(adminUserMap)) {
			throw new BusinessValidateException("用户不存在！");
		}
		AdminUser adminUser = adminUserMap.get(userId);
		if (Objects.isNull(adminUser)) {
			throw new BusinessValidateException("用户不存在！");
		}
		jsonObject.put("RequisitionUserId", adminUser.getBusinessId());
		String body = jsonObject.toJSONString();
		LOGGER.info("提交采购退入参:{}", body);
		return restTemplate.postForObject(erpAPIUrl.concat(url), buildEntity(body), String.class);
	}

	/**
	 * 1、先通过erp接口获取经销商信息 <br />
	 * 2、通过经销商 查询 erp 的产品信息 <br />
	 * 3、通过erp产品信息 返回的 规格信息，查询 wms 的 sku信息 <br />
	 * 4、通过 wms 的sku 信息 查询 wms 的仓库库存 和销售库存，根据skuId 和 二级货主信息 过滤出 相应的销售库存  <br />
	 * 5、可退数量 取的 erp数量 和 销售库存 + 残次品库存 相比 的最小值  <br />
	 * @param request
	 * @param url
	 * @return
	 */
	private String getPurchaseReturnProductList(Object request, String url) {
		ErpGetPurchaseReturnProductListQueryDTO params = JSON.parseObject(JSON.toJSONString(request), ErpGetPurchaseReturnProductListQueryDTO.class);
		AssertUtils.notNull(params.getOrgId(), "城市信息不能为空！");

		if (StringUtils.isEmpty(params.getBarCode())) {
			return getPurchaseReturnProductListResultDTOS(params, url);
		}

		// 通过瓶码或者箱码拿到sku列表
		List<Long> skuIdList = iProductSkuService.getProductSkuIdByCode(params.getBarCode(), params.getOrgId());
		if (CollectionUtils.isEmpty(skuIdList)) {
			return ErpQueryResultDTO.getInstance();
		}
		// TODO 这里可能是城市级别
		// 分仓属性
		Integer warehouseAllocationType =
				globalCache.getEmployeeWarehouseAllocationType(null, params.getStoreHouseId(), null);
		List<ProductSkuDTO> productSkuDTOList = productSkuQueryBL.findSkuInfoWithConfigNew(params.getStoreHouseId(), skuIdList, warehouseAllocationType);
		if (CollectionUtils.isEmpty(productSkuDTOList)) {
			return ErpQueryResultDTO.getInstance();
		}

		List<ErpGetPurchaseReturnProductListResultDTO> resultList = productSkuDTOList.stream().map(productSku -> getPurchaseReturnProductListResultDTOS(productSku, params, url))
				.filter(com.alibaba.dubbo.common.utils.CollectionUtils :: isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(resultList)) {
			return ErpQueryResultDTO.getInstance();
		}

		List<Long> productSpecIds = resultList.stream().map(ErpGetPurchaseReturnProductListResultDTO :: getProductInfoNewId).map(Long :: valueOf).distinct().collect(Collectors.toList());
		Map<Long, ProductSkuInfoReturnDTO> skuMap = iProductSkuService.getProductInfoBySpecificationIdAndSource(
				params.getOrgId(), getSpecIds(productSpecIds), 0);

		resultList.forEach(m -> {
			ProductSkuInfoReturnDTO productSkuInfoReturnDTO = skuMap.get(Long.valueOf(m.getProductInfoNewId()));
			if (Objects.nonNull(productSkuInfoReturnDTO)) {
				m.setSkuId(productSkuInfoReturnDTO.getSkuId());
			}
		});

		resultList = resultList.stream().filter(m -> Objects.nonNull(m.getSkuId())).collect(Collectors.toList());

		if (CollectionUtils.isEmpty(resultList)) {
			return ErpQueryResultDTO.getInstance();
		}

		List<Long> refProductSkuIds = resultList.stream().map(ErpGetPurchaseReturnProductListResultDTO::getSkuId).distinct().collect(Collectors.toList());

		Map<String, Long> ownerIdMap = getSecOwnerIdMap(resultList);
		Map<String, List<BatchInventoryDTO>> batchInventoryDTOMap = getDefectiveGoodsInfo(refProductSkuIds, params.getStoreHouseId());
		Map<String, FindStoreInfoVO> storeInventoryMap = getStoreInventory(params.getStoreHouseId(), params.getOrgId(), refProductSkuIds);

		resultList.forEach(data -> {
			LOGGER.info("erp库存信息为：{}" ,JSON.toJSONString(data));
			Long secOwnerId = ownerIdMap.get(data.getSecOwnerId());
			List<BatchInventoryDTO> batchInventoryDTOList = batchInventoryDTOMap.get(getKey(data.getSkuId(), secOwnerId));
			LOGGER.info("批次库存信息为：{}" ,JSON.toJSONString(batchInventoryDTOList));
			data.setDefectiveGoodsNum(getDefectiveGoodsNum(batchInventoryDTOList));
			FindStoreInfoVO findStoreInfoVO = storeInventoryMap.get(getKey(data.getSkuId(), secOwnerId));
			LOGGER.info("库存信息为：{}" ,JSON.toJSONString(findStoreInfoVO));
			data.setRefundableNum(getRefundableNum(data, findStoreInfoVO));
		});

		resultList = resultList.stream().filter(m -> m.getRefundableNum() != 0).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(resultList)) {
			return ErpQueryResultDTO.getInstance();
		}

		ErpQueryResultDTO<ErpGetPurchaseReturnProductListResultDTO> resultDTO = new ErpQueryResultDTO<>();
		resultDTO.setData(resultList);
		resultDTO.setSuccess(Boolean.TRUE);
		resultDTO.setTotalCount(resultList.size());
		return JSON.toJSONString(resultDTO);
	}

	private Map<String, Long> getSecOwnerIdMap(List<ErpGetPurchaseReturnProductListResultDTO> resultList) {
		List<String> ownerIds = resultList.stream().map(ErpGetPurchaseReturnProductListResultDTO :: getSecOwnerId).distinct().collect(Collectors.toList());
		Map<String, Long> ownerIdMap = ownerService.getOwnerIdMap(ownerIds);

		return ownerIdMap;
	}

	private List<ProductSkuBySpecificationQueryDTO> getSpecIds(List<Long> productSpecIds) {
		return productSpecIds.stream().map(specId -> {
			ProductSkuBySpecificationQueryDTO dto = new ProductSkuBySpecificationQueryDTO();
			dto.setProductSpecificationId(specId);

			return dto;
		}).collect(Collectors.toList());
	}

	private BigDecimal getDefectiveGoodsNum(List<BatchInventoryDTO> batchInventoryDTOList) {
		if (CollectionUtils.isEmpty(batchInventoryDTOList)) {
			return BigDecimal.ZERO;
		}
		return batchInventoryDTOList.stream().map(BatchInventoryDTO :: getStoreTotalCount).reduce(BigDecimal.ZERO, BigDecimal :: add);
	}

	private Integer getRefundableNum(ErpGetPurchaseReturnProductListResultDTO data, FindStoreInfoVO findStoreInfoVO) {
		if (Objects.isNull(findStoreInfoVO)) {
			return data.getRefundableNum();
		}

		BigDecimal saleUnitTotalCount = findStoreInfoVO.getStoreSaleCountMax().multiply(findStoreInfoVO.getSpecQuantity()).add(findStoreInfoVO.getStoreSaleCountMin());
		BigDecimal totalSaleCount = data.getDefectiveGoodsNum().add(saleUnitTotalCount);


		int count = Integer.min(totalSaleCount.intValue(), data.getRefundableNum());
		return Integer.max(0, count);
	}

	private Map<String, List<BatchInventoryDTO>> getDefectiveGoodsInfo(List<Long> skuIds, Integer warehouseId) {
		BatchInventoryQueryDTO queryDTO = new BatchInventoryQueryDTO();
		queryDTO.setWarehouseId(warehouseId);
		queryDTO.setProductSkuIdList(skuIds);
		queryDTO.setSubCategoryList(Arrays.asList(LocationEnum.残次品位.getType(), LocationAreaEnum.残次品区.getType()));
		List<BatchInventoryDTO> batchInventoryList = iBatchInventoryQueryService.findInventoryLocationBySkuId(queryDTO);
		if (CollectionUtils.isEmpty(batchInventoryList)) {
			return Collections.emptyMap();
		}

		return batchInventoryList.stream().collect(Collectors.groupingBy(k -> getKey(k.getProductSkuId(), k.getSecOwnerId())));
	}

	private List<ErpGetPurchaseReturnProductListResultDTO> getPurchaseReturnProductListResultDTOS(ProductSkuDTO productSku, ErpGetPurchaseReturnProductListQueryDTO params, String url) {
		productSku.setName(productSku.getName().replace("[下架]",""));
		productSku.setName(productSku.getName().replace("[作废]",""));

		params.setProductName(productSku.getName());
		String result = restTemplate.postForObject(erpAPIUrl.concat(url), buildEntity(JSON.toJSON(params)), String.class);
		if (StringUtils.isEmpty(result)) {
			return Collections.emptyList();
		}

		ErpQueryResultDTO<ErpGetPurchaseReturnProductListResultDTO> erpQueryResultDTO = JSON.parseObject(result, new TypeReference<ErpQueryResultDTO<ErpGetPurchaseReturnProductListResultDTO>>(){}.getType());
		if (CollectionUtils.isEmpty(erpQueryResultDTO.getData())) {
			return Collections.emptyList();
		}

//        erpQueryResultDTO.getData().forEach(dto -> {
//            dto.setSkuId(productSku.getProductSkuId());
//        });

		return erpQueryResultDTO.getData();
	}

	private String getPurchaseReturnProductListResultDTOS(ErpGetPurchaseReturnProductListQueryDTO params, String url) {
		String resultStr = restTemplate.postForObject(erpAPIUrl.concat(url), buildEntity(JSON.toJSON(params)), String.class);

		ErpQueryResultDTO<ErpGetPurchaseReturnProductListResultDTO> resultDTO = JSON.parseObject(resultStr, new TypeReference<ErpQueryResultDTO<ErpGetPurchaseReturnProductListResultDTO>>(){});

		return JSON.toJSONString(resultDTO);
	}


	private Map<String, FindStoreInfoVO> getStoreInventory(Integer warehouseId, Integer orgId, List<Long> skuIds) {
		FindStorePageDTO findStorePageDTO = new FindStorePageDTO();
		findStorePageDTO.setProductSkuIds(skuIds);
		findStorePageDTO.setWarehouseIds(Collections.singletonList(warehouseId));
		findStorePageDTO.setCityId(orgId);
		ROResult<List<FindStoreInfoVO>> result = storeInfoApiService.findStorePage(findStorePageDTO);

		if (CollectionUtils.isEmpty(result.getData())) {
			return Collections.emptyMap();
		}

		return result.getData().stream().collect(Collectors.toMap(k -> getKey(k.getProductSkuId(), k.getSecOwnerId()), v -> v));
	}

	private String getKey(Long skuId, Long secOwnerId) {
		return String.format("%s-%s", skuId, secOwnerId);
	}

	private <T> HttpEntity<T> buildEntity(T obj) {
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_JSON_UTF8);
		header.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		return new HttpEntity<>(obj, header);
	}
}
