package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.baseutil.ArrayUtils;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryListQueryService;
import com.yijiupi.himalaya.supplychain.inventory.service.ordercenter.ISaleInventoryService;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto.CityMergeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.store.FindStoreInfoVO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.store.FindStorePageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/14 14:09
 * @Version 1.0
 */
@Component
public class StoreInfoApiService {

	@Reference
	private IStoreWareHouseService iStoreWareHouseService;
	@Reference
	private ISaleInventoryService saleInventoryService;
	@Reference
	private IInventoryListQueryService iInventoryListQueryService;
	@Reference
	private IProductSkuService iProductSkuService;
	@Autowired
	private StoreReportConvert storeReportConvert;

	private static final Gson GSON = new Gson();
	private static final Logger LOG = LoggerFactory.getLogger(StoreInfoApiService.class);


	/**
	 * 库存报表
	 */
	public ROResult<List<FindStoreInfoVO>> findStorePage(FindStorePageDTO findStorePageDTO) {
		if (findStorePageDTO.getWarehouseIds() == null || findStorePageDTO.getWarehouseIds().size() <= 0) {
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.getWareHouseByCityId(findStorePageDTO.getCityId());
			// List<Warehouse> warehouseList =
			// warhouseService.getWarehouseByCityId(findStorePageDTO.getCityId());
			// List<Integer> warehouseIds = warehouseList.stream().map(t ->
			// t.getId()).collect(Collectors.toList());
			findStorePageDTO.setWarehouseIds(Collections.singletonList(wareHouseDTO.getId()));
		}
		StopWatch interfaceWatch = new StopWatch("interfaceWatch");
		interfaceWatch.start("interfaceWatch");
		StopWatch stopWatch = new StopWatch("查询库存信息接口");
		ROResult<List<FindStoreInfoVO>> result = new ROResult<>();
		StockReportSO so = new StockReportSO();
		so.setInventoryPinProperty(findStorePageDTO.getInventoryPinProperty());
		so.setProductSkuName(findStorePageDTO.getProductSkuName());
		so.setWarehouseIds(findStorePageDTO.getWarehouseIds());
		if (findStorePageDTO.getSupplierId() != null && findStorePageDTO.getSupplierId() > 0) {
			so.setSupplierId(findStorePageDTO.getSupplierId());
		}
		if (findStorePageDTO.getStoreOwnerType() != null && findStorePageDTO.getStoreOwnerType() >= 0) {
			so.setStoreOwnerType(findStorePageDTO.getStoreOwnerType());
		}
		so.setProductSkuIds(findStorePageDTO.getProductSkuIds());
		// 是否限制产品范围
		so.setLimitSku(findStorePageDTO.getLimitSku());
		so.setHasRealStoreType(findStorePageDTO.getHasRealStoreType());
		PagerCondition pager = new PagerCondition(findStorePageDTO.getCurrentPage(), findStorePageDTO.getPageSize());
		stopWatch.start("查询库存信息");
		PageList<InventoryReportDTO> pageList = iInventoryListQueryService.findStoreReportPageByAuth(so, pager);
		stopWatch.stop();

		LOG.info("查询库存完成，耗时：{}", stopWatch.prettyPrint());
		List<InventoryReportDTO> dataList = pageList.getDataList();
		if (!CollectionUtils.isEmpty(dataList)) {
			List<Integer> warehouseIds =
					dataList.stream().map(InventoryReportDTO::getWarhouseId).distinct().collect(Collectors.toList());
			List<CityMergeDTO> lstParam = new ArrayList<>();
			dataList.forEach(p -> {
				CityMergeDTO dto = new CityMergeDTO();
				dto.setProductOwnerId(p.getOwnerId());
				dto.setProductSpecificationId(p.getProductSpecificationId());
				lstParam.add(dto);
			});
			// List<CityMergeDTO> lstWaiting =
			// iOrderQueryService.getWaitingDeliveryStateSecCountByProductSkuIds(lstParam, warehouseIds.get(0));
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.findWareHouseById(warehouseIds.get(0));
			// 合作商库存不查
			List<InventoryReportDTO> dataListWithOutPartner = ArrayUtils.deepCopy(dataList);
			dataListWithOutPartner.removeIf(t -> Objects.equals(OwnerTypeConst.合作商, t.getStoreOwnerType()));
			// 获取销售库存
			List<ProductOwnerInfoDTO> productOwnerDTOList =
					ProductOwnerDTOConvertor.convertByInventoryReport(dataListWithOutPartner);
			Map<String, BigDecimal> saleInventoryMap =
					getSaleInventoryMap(productOwnerDTOList, warehouseIds.get(0), findStorePageDTO.getCityId());
			Map<String, BigDecimal> mapWaiting = new HashMap<>();
			//// System.err.println("saleInventoryMap:" + GSON.toJson(saleInventoryMap));
			// if (!CollectionUtils.isEmpty(lstWaiting)) {
			// lstWaiting.forEach(p -> {
			// mapWaiting.put(String.format("%s-%s-%s", p.getProductSpecificationId(), p.getProductOwnerId(),
			//// p.getProductSecOwnerId()), p.getCount());
			// });
			// }
			// 在途库存
			Map<String, BigDecimal> allotDeliveryingCountMap = new HashMap<>();// getAllotDeliveryingCountMap(warehouseIds.get(0),
			// lstParam);

			// System.err.println("mapWaiting:" + GSON.toJson(mapWaiting));
			List<FindStoreInfoVO> convert = storeReportConvert.convert(dataList, wareHouseDTO, mapWaiting,
					saleInventoryMap, allotDeliveryingCountMap);

			stopWatch.start("查询瓶码等信息");
			stopWatch.stop();
			LOG.info("查询库存信息完成，耗时：{}", stopWatch.prettyPrint());
			result.setData(convert);
			result.setTotalCount(pageList.getPager().getRecordCount());
		}
		interfaceWatch.stop();
		LOG.info("interfaceWatch查询库存信息完成，耗时：{}", interfaceWatch.prettyPrint());
		return result;
	}


	/**
	 * 查询销售库存
	 */
	private Map<String, BigDecimal> getSaleInventoryMap(List<ProductOwnerInfoDTO> productOwners, Integer warehouseId,
														Integer cityId) {
		Map<String, BigDecimal> saleMap = new HashMap<>();
		if (CollectionUtils.isEmpty(productOwners)) {
			return Collections.emptyMap();
		}
		List<SaleInventoryInfoDTO> saleInventoryDTOS = findInventoryByProductOwners(cityId, warehouseId, productOwners);
		if (!CollectionUtils.isEmpty(saleInventoryDTOS)) {
			saleInventoryDTOS.forEach(p -> {
				String productSkuIdCityIDWarehouseId = String.format("%s-%s-%s-%s", p.getWarehouseId(),
						p.getProductSpecId(), p.getOwnerId(), p.getSecOwnerId());
				saleMap.put(productSkuIdCityIDWarehouseId, p.getSaleInventoryCount());
			});
		}
		return saleMap;
	}


	public List<SaleInventoryInfoDTO> findInventoryByProductOwners(Integer cityId, Integer warehouseId, List<ProductOwnerInfoDTO> productOwners) {
		ProductOwnerInventoryQueryParam param = ProductOwnerInventoryQueryParam.of(cityId, warehouseId, productOwners);
		return runCaching(() -> saleInventoryService.findInventoryByProductOwners(param), Collections::emptyList);
	}

	private <T> T runCaching(Supplier<T> supplier, Supplier<T> defaultValue) {
		try {
			return supplier.get();
		} catch (Exception e) {
			LOG.warn("出现异常", e);
		}
		return defaultValue.get();
	}


}
