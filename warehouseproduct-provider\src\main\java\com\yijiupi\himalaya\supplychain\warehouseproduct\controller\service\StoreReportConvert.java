package com.yijiupi.himalaya.supplychain.warehouseproduct.controller.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.store.FindStoreInfoVO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/18
 */
@Component
public class StoreReportConvert {

    @Reference
    private IProductLocationService iProductLocationService;

    public List<FindStoreInfoVO> convert(List<InventoryReportDTO> mList, WareHouseDTO wareHouseDTO,
                                         Map<String, BigDecimal> mapWaiting, Map<String, BigDecimal> saleInventoryMap,
                                         Map<String, BigDecimal> allotDeliveryingCountMap) {
        List<FindStoreInfoVO> nList = null;
        if (mList != null) {
            nList = new ArrayList<>(mList.size());
            for (InventoryReportDTO m : mList) {
                FindStoreInfoVO vo = convert(m);
                if (wareHouseDTO != null) {
                    vo.setWarehouseName(wareHouseDTO.getName());
                }
                if (m.getStoreOwnerType() != null) {
                    String ownerTypeName = OwnerTypeConst.getEnmuName(m.getStoreOwnerType());
                    if (ownerTypeName == null) {
                        ownerTypeName = OwnerTypeConst.getEnmuName(OwnerTypeConst.易酒批);
                    }
                    vo.setStoreOwnerTypeName(ownerTypeName);
                }
                if (mapWaiting != null) {
                    vo.setUnDeliveryCount(mapWaiting.get(
                            String.format("%s-%s-%s", m.getProductSpecificationId(), m.getOwnerId(), m.getSecOwnerId())));
                }
                if (m.getDeliveryedCount() != null) {
                    vo.setDeliveryingCount(m.getDeliveryedCount());
                }
                if (vo.getUnDeliveryCount() != null && m.getPackageQuantity() != null) {
                    BigDecimal[] countRemainder = vo.getUnDeliveryCount().divideAndRemainder(m.getPackageQuantity());
                    vo.setUnDeliveryCountMax(countRemainder[0]);
                    vo.setUnDeliveryCountMin(countRemainder[1]);
                }
                if (vo.getDeliveryingCount() != null && m.getPackageQuantity() != null) {
                    BigDecimal[] countRemainder = vo.getDeliveryingCount().divideAndRemainder(m.getPackageQuantity());
                    vo.setDeliveryingCountMax(countRemainder[0]);
                    vo.setDeliveryingCountMin(countRemainder[1]);
                }
                // 销售库存
                BigDecimal saleTotalCount = saleInventoryMap.get(String.format("%s-%s-%s-%s", m.getWarhouseId(),
                        m.getProductSpecificationId(), m.getOwnerId(), m.getSecOwnerId()));
                if (saleTotalCount != null && m.getPackageQuantity() != null) {
                    vo.setStoreSaleCountMax(saleTotalCount.divideAndRemainder(m.getPackageQuantity())[0]);
                    vo.setStoreSaleCountMin(saleTotalCount.divideAndRemainder(m.getPackageQuantity())[1]);
                }
                // 在途库存
                if (allotDeliveryingCountMap != null) {
                    BigDecimal allotDeliveryingTotalCount = allotDeliveryingCountMap.get(String.format("%s-%s-%s-%s",
                            m.getProductSpecificationId(), m.getOwnerId(), m.getWarhouseId(), m.getSecOwnerId()));
                    if (allotDeliveryingTotalCount != null && m.getPackageQuantity() != null) {
                        vo.setAllotDeliveryingCount(allotDeliveryingTotalCount);
                        vo.setAllotDeliveryingCountMax(
                                allotDeliveryingTotalCount.divideAndRemainder(m.getPackageQuantity())[0]);
                        vo.setAllotDeliveryingCountMin(
                                allotDeliveryingTotalCount.divideAndRemainder(m.getPackageQuantity())[1]);
                    }
                }
                vo.setOwnerName(m.getOwnerName());
                vo.setSecOwnerName(m.getSecOwnerName());
                nList.add(vo);
            }
        }
        return nList;
    }

    public FindStoreInfoVO convert(InventoryReportDTO m) {
        FindStoreInfoVO findStoreInfoVO = new FindStoreInfoVO();
        if (m != null) {
            findStoreInfoVO.setAbcAttribute(m.getInventoryPinProperty());
            findStoreInfoVO.setSequence(m.getSequence());
            findStoreInfoVO.setProductStoreId(m.getProductStoreId());
            findStoreInfoVO.setProductSkuName(m.getProductSkuName());
            findStoreInfoVO.setSpecificationName(m.getSpecificationName());
            findStoreInfoVO.setSupplierName(m.getSupplierName());
            findStoreInfoVO.setStoreTotalCount(m.getStoreTotalCount());
            findStoreInfoVO.setStoreCountMax(m.getStoreCountMax());
            findStoreInfoVO.setProductSkuId(m.getProductSkuId());
            findStoreInfoVO.setStoreCountMin(m.getStoreCountMin());
            findStoreInfoVO.setPackageName(m.getPackageName());
            findStoreInfoVO.setUnitName(m.getUnitName());
            findStoreInfoVO.setStoreWaringCount(m.getStoreWaringCount());
            // 只有零售渠道才加可销售库存
            if (Objects.equals(m.getChannel(), 0)) {
                findStoreInfoVO.setStoreSaleTotalCount(m.getStoreSaleCount());
            }
            findStoreInfoVO.setStorePreTotalCount(m.getStorePreCount());
            findStoreInfoVO.setWarehouseId(m.getWarhouseId());
            findStoreInfoVO.setChannel(m.getChannel());// 渠道
            findStoreInfoVO.setSpecQuantity(m.getPackageQuantity());
            if (m.getPackageQuantity() != null && m.getPackageQuantity().intValue() > 0) {
                if (m.getStorePreCount() != null) {
                    BigDecimal[] storePre = m.getStorePreCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStorePreCountMin(storePre[1]);
                    findStoreInfoVO.setStorePreCountMax(storePre[0]);
                }
                if (m.getStoreSaleCount() != null) {
                    BigDecimal[] storeSale = m.getStoreSaleCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStoreSaleCountMax(storeSale[0]);
                    findStoreInfoVO.setStoreSaleCountMin(storeSale[1]);
                }
                if (m.getStoreTotalCount() != null) {
                    BigDecimal[] countRemainder = m.getStoreTotalCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStoreCountMax(countRemainder[0]);
                    findStoreInfoVO.setStoreCountMin(countRemainder[1]);
                }
            }
            findStoreInfoVO.setOwnerName(m.getOwnerName());
            findStoreInfoVO.setOwnerId(m.getOwnerId());
            findStoreInfoVO.setSecOwnerId(m.getSecOwnerId());
            findStoreInfoVO.setSecOwnerName(m.getSecOwnerName());
            findStoreInfoVO.setProductSpecificationId(m.getProductSpecificationId());
            // 平均库龄
            findStoreInfoVO.setAverageStockAge(m.getAverageStockAge());
        }
        return findStoreInfoVO;
    }


}
