package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.AgencyConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AgencyInfoMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AgencyInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 经销商
 *
 * <AUTHOR> 2017/11/26
 */
@Service
public class AgencyBL {

    @Autowired
    private AgencyInfoMapper agencyMapper;

    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    public PageList<AgencyInfoDTO> getAgencyInfoDTO(AgencyInfoQueryDTO agencyInfoQueryDTO) {
        PageResult<AgencyInfoPO> pageResultPO = agencyMapper.getAgencyInfoPO(agencyInfoQueryDTO,
            agencyInfoQueryDTO.getPageNum(), agencyInfoQueryDTO.getPageSize());
        PageList<AgencyInfoPO> pageListPO = pageResultPO.toPageList();
        List<AgencyInfoDTO> agencyInfoDTOS = AgencyConvertor.agencyInfoPOList2DTO(pageListPO.getDataList());
        PageList<AgencyInfoDTO> pageListDTO = new PageList<>();
        pageListDTO.setDataList(agencyInfoDTOS);
        pageListDTO.setPager(pageListPO.getPager());
        return pageListDTO;
    }
}
