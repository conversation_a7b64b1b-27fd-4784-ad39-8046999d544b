package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.dto.PageListUtil;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.AisleConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AislePOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupRfidTypeEnum;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 巷道
 *
 * <AUTHOR>
 * @date 2024/11/2
 */
@Service
public class AisleBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(AisleBL.class);

    @Autowired
    private AislePOMapper aislePOMapper;

    @Autowired
    private LocationAreaPOMapper locationAreaPOMapper;

    @Autowired
    private SortGroupRfidBL sortGroupRfidBL;

    @Autowired
    private LocationPOMapper locationPOMapper;

    private final Integer pageSize = 3000;

    /**
     * 新增巷道
     *
     * @param addDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addAisle(AisleDTO addDTO) {
        LOGGER.info("新增巷道 入参：{}", JSON.toJSONString(addDTO));
        AssertUtils.notNull(addDTO, "参数不能为空");
        AssertUtils.notNull(addDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.hasText(addDTO.getAisleNo(), "巷道编号不能为空");
        AssertUtils.notNull(addDTO.getAreaId(), "库区id不能为空");
        AssertUtils.hasText(addDTO.getAreaName(), "库区名称不能为空");
        AssertUtils.hasText(addDTO.getCreateUser(), "创建人不能为空");
        AssertUtils.isTrue(addDTO.getAisleNo().length() <= 4, "巷道编号不能超过4位字符");

        AislePO aislePO = AisleConvertor.convertorToAislePO(addDTO);
        // 根据巷道NO+货区id判断巷道是否存在
        AislePO existPO = aislePOMapper.getAisleByAisleNoAndAreaId(aislePO);
        if (existPO != null && !Objects.equals(aislePO.getId(), existPO.getId())) {
            throw new DataValidateException("该货区巷道名称已存在");
        }

        // 检查货区是否存在
        LocationAreaPO areaPO = locationAreaPOMapper.selectByPrimaryKey(addDTO.getAreaId());
        if (areaPO == null) {
            throw new BusinessException("货区信息不存在");
        }

        aislePO.setId(UUIDGenerator.getUUID(AislePO.class.getName()));
        aislePO.setState(ConditionStateEnum.是.getType());
        aislePO.setCreateTime(new Date());
        aislePOMapper.insertSelective(aislePO);
    }

    /**
     * 分页查询巷道
     */
    public PageList<AisleDTO> pageListAisle(AisleQueryDTO queryDTO) {
        LOGGER.info("查询巷道 入参：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");

        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        PageResult<AislePO> result = aislePOMapper.pageListAisle(queryDTO);
        LOGGER.info("查询巷道 结果：{}", JSON.toJSONString(result));
        if (CollectionUtils.isEmpty(result)) {
            return PageListUtil.getDefault(new PagerCondition(queryDTO.getPageNum(), pageSize));
        }
        return result.toPageList(it -> AisleConvertor.convertorToAisleDTOList(it));
    }

    /**
     * 查询巷道不分页
     */
    public List<AisleDTO> listAisleNoPage(AisleQueryDTO queryDTO) {
        LOGGER.info("查询巷道不分页 入参：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");

        List<AislePO> list = aislePOMapper.listAisleNoPage(queryDTO);
        LOGGER.info("查询巷道不分页 结果：{}", JSON.toJSONString(list));
        return AisleConvertor.convertorToAisleDTOList(list);
    }

    /**
     * 编辑巷道
     *
     * @param updateDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateAisle(AisleDTO updateDTO) {
        LOGGER.info("编辑巷道 入参：{}", JSON.toJSONString(updateDTO));
        AssertUtils.notNull(updateDTO, "参数不能为空");
        AssertUtils.notNull(updateDTO.getId(), "巷道id不能为空");
        if (!StringUtils.isEmpty(updateDTO.getAisleNo())) {
            AssertUtils.isTrue(updateDTO.getAisleNo().length() <= 4, "巷道编号不能超过4位字符");
        }

        AislePO aislePO = aislePOMapper.selectByPrimaryKey(updateDTO.getId());
        if (aislePO == null) {
            throw new DataValidateException("找不到对应巷道");
        }

        // 判断名称是否重复
        boolean isUpdateAisleNo = false;
        if (!StringUtils.isEmpty(updateDTO.getAisleNo()) && !updateDTO.getAisleNo().equals(aislePO.getAisleNo())) {
            isUpdateAisleNo = true;
            // 根据巷道NO+货区id判断巷道是否存在
            AislePO po = new AislePO();
            po.setWarehouseId(aislePO.getWarehouseId());
            po.setAreaId(updateDTO.getAreaId());
            po.setAisleNo(updateDTO.getAisleNo());
            AislePO existPO = aislePOMapper.getAisleByAisleNoAndAreaId(po);
            if (existPO != null && !Objects.equals(aislePO.getId(), existPO.getId())) {
                throw new DataValidateException("该货区巷道名称已存在");
            }
        }

        // 检查货区是否存在
        LocationAreaPO areaPO = locationAreaPOMapper.selectByPrimaryKey(updateDTO.getAreaId());
        if (areaPO == null) {
            throw new BusinessException("货区信息不存在");
        }

        AislePO updatePO = AisleConvertor.convertorToAislePO(updateDTO);
        aislePOMapper.updateAisle(updatePO);

        // 更新货位的巷道编号
        if (isUpdateAisleNo) {
            updateLocationAisleNo(updateDTO);
        }
    }

    /**
     * 删除巷道
     *
     * @param deleteDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteAisle(AisleDTO deleteDTO) {
        LOGGER.info("删除巷道 入参：{}", JSON.toJSONString(deleteDTO));
        AssertUtils.notNull(deleteDTO, "参数不能为空");
        AssertUtils.notNull(deleteDTO.getId(), "巷道id不能为空");

        AislePO aislePO = aislePOMapper.selectByPrimaryKey(deleteDTO.getId());
        if (aislePO == null) {
            throw new DataValidateException("找不到对应巷道");
        }

        // 检查是否存在关联货位
        checkOrRemoveLocationAisle(deleteDTO);
        // 判断是否存在关联巷道标签
        checkOrDeleteRfid(deleteDTO);
        // 删除巷道
        aislePOMapper.deleteByPrimaryKey(deleteDTO.getId());
        LOGGER.info("删除巷道 完成：{}", JSON.toJSONString(deleteDTO));
    }

    /**
     * 检查或移除货位巷道关系
     *
     * @param aisleDTO
     */
    private void checkOrRemoveLocationAisle(AisleDTO aisleDTO) {
        if (aisleDTO == null || aisleDTO.getId() == null) {
            return;
        }

        List<LoactionDTO> loactionDTOS = findLoationByAisleId(aisleDTO);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            return;
        }

        if (aisleDTO.getDeleteRfid() == null || !aisleDTO.getDeleteRfid()) {
            throw new BusinessValidateException("该巷道已绑定货位，无法删除");
        }

        List<Long> locationIds = loactionDTOS.stream().filter(p -> p.getAisleId() != null).map(p -> p.getId())
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIds)) {
            return;
        }

        // 更新货位上的巷道编号
        LocationModifyDTO modifyDTO = new LocationModifyDTO();
        modifyDTO.setIdList(locationIds);
        locationPOMapper.clearAisleNoBatchById(modifyDTO);
        LOGGER.info("移除货位巷道编号：{}, 货位ids：{}", aisleDTO.getAisleNo(), JSON.toJSONString(locationIds));
    }

    /**
     * 检查或删除巷道标签
     *
     * @param aisleDTO
     */
    private void checkOrDeleteRfid(AisleDTO aisleDTO) {
        if (aisleDTO == null || aisleDTO.getId() == null) {
            return;
        }

        SortGroupRfidSO rfidSO = new SortGroupRfidSO();
        rfidSO.setWarehouseId(aisleDTO.getWarehouseId());
        rfidSO.setSortIdList(Arrays.asList(aisleDTO.getId()));
        rfidSO.setRfidType(SortGroupRfidTypeEnum.巷道.getType());
        List<SortGroupRfidDTO> rfidDTOS = sortGroupRfidBL.listSortGroupRfid(rfidSO);
        if (CollectionUtils.isEmpty(rfidDTOS)) {
            return;
        }

        if (aisleDTO.getDeleteRfid() == null || !aisleDTO.getDeleteRfid()) {
            throw new BusinessValidateException("该巷道已绑定标签，无法删除");
        }

        List<Long> rfidIds = rfidDTOS.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
            .collect(Collectors.toList());
        sortGroupRfidBL.deleteBatchByIds(rfidIds);
        LOGGER.info("检查货删除巷道标签 完成：{}", JSON.toJSONString(rfidDTOS));
    }

    /**
     * 检查巷道关联货位，并更新巷道编号
     *
     * @param aisleDTO
     */
    private void updateLocationAisleNo(AisleDTO aisleDTO) {
        if (aisleDTO == null || aisleDTO.getId() == null || StringUtils.isEmpty(aisleDTO.getAisleNo())) {
            return;
        }

        List<LoactionDTO> loactionDTOS = findLoationByAisleId(aisleDTO);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            return;
        }

        List<Long> locationIds = loactionDTOS.stream().filter(p -> !StringUtils.isEmpty(p.getAisleNo()))
            .map(p -> p.getId()).distinct().collect(Collectors.toList());
        // 更新货位上的巷道编号
        LocationModifyDTO modifyDTO = new LocationModifyDTO();
        modifyDTO.setCityId(loactionDTOS.get(0).getCityId());
        modifyDTO.setWarehouseId(loactionDTOS.get(0).getWarehouseId());
        modifyDTO.setIdList(locationIds);
        modifyDTO.setAisleNo(aisleDTO.getAisleNo());
        modifyDTO.setLastUpdateTime(new Date());
        locationPOMapper.updateSelectiveByIdList(modifyDTO);
        LOGGER.info("更新货位巷道编号：{}, 货位ids：{}", aisleDTO.getAisleNo(), JSON.toJSONString(locationIds));
    }

    /**
     * 根据巷道id获取货位
     *
     * @param aisleDTO
     */
    private List<LoactionDTO> findLoationByAisleId(AisleDTO aisleDTO) {
        if (aisleDTO == null || aisleDTO.getId() == null) {
            return new ArrayList<>();
        }

        LocationInfoQueryDTO query = new LocationInfoQueryDTO();
        query.setAisleId(aisleDTO.getId());
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageResult<LoactionDTO> pageResult =
            locationPOMapper.pageListLocation(query, query.getPageNum(), query.getPageSize());
        LOGGER.info("根据巷道id获取货位 结果：{}", JSON.toJSONString(pageResult));
        if (pageResult == null) {
            return new ArrayList<>();
        }

        return pageResult.getResult();
    }
}
