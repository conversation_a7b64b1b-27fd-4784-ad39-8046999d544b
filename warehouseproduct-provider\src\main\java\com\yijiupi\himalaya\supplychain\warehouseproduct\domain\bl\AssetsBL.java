package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.text.ParseException;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.AssetsChangeRecordConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.AssetsInfoConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AssetsChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AssetsInfoPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AssetsChangeEventEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AssetsRetrieveEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AssetsStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AssetsTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsChangeRecordListQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsListQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.AssetsCodeGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.AssetsIdGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.AssetsStateHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.RedisLock;

@Service
public class AssetsBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssetsBL.class);

    /**
     * 日期格式化
     */
    private static final String DATE_FORMAT_YMD24HMS = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private AssetsCodeGenerator assetsCodeGenerator;

    @Autowired
    private AssetsIdGenerator assetsIdGenerator;

    @Autowired
    private AssetsInfoConvertor assetsInfoConvertor;

    @Autowired
    private AssetsChangeRecordConvertor assetsChangeRecordConvertor;

    @Autowired
    private AssetsInfoPOMapper assetsInfoPOMapper;

    @Autowired
    private AssetsChangeRecordPOMapper assetsChangeRecordPOMapper;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    public AssetsInfoDTO findAssetsInfoByCode(String code) {
        AssertUtils.notNull(code, "资产编码不能为空！");
        return assetsInfoConvertor.convert(assetsInfoPOMapper.selectByCode(code));
    }

    public List<AssetsInfoDTO> findAssetsInfoByCodes(List<String> codes) {
        AssertUtils.notEmpty(codes, "资产编码不能为空！");
        return assetsInfoConvertor.convert(assetsInfoPOMapper.selectByCodeAndState(codes, null));
    }

    /**
     * 知花知果-资产类型枚举类
     * 
     * @return
     */
    public List<AssetsTypeDTO> findAssetsType() {
        List<AssetsTypeDTO> assetsTypeDTOList = Lists.newArrayList();
        EnumSet.allOf(AssetsTypeEnum.class).forEach(e -> {
            AssetsTypeDTO typeDTO = new AssetsTypeDTO();
            typeDTO.setTypeCode(e.getTypeCode());
            typeDTO.setTypeName(e.getTypeName());
            assetsTypeDTOList.add(typeDTO);
        });
        return assetsTypeDTOList;
    }

    /**
     * 分页查询资产信息
     *
     * @param queryDTO
     * @return
     */
    public PageList<AssetsInfoDTO> findAssetsInfoByPage(AssetsListQueryDTO queryDTO) {
        LOGGER.info("查询资产信息参数：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询参数不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空！");
        PageResult<AssetsInfoPO> pageResult = assetsInfoPOMapper.findAssetsInfoByPage(queryDTO);

        PageList<AssetsInfoDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(pageResult.getPager());
        dtoPageList.setDataList(assetsInfoConvertor.convert(pageResult.getResult()));
        return dtoPageList;
    }

    public List<AssetsChangeRecordDTO>
        findAssetsChangeRecordList(AssetsChangeRecordListQueryDTO changeRecordListQueryDTO) {
        LOGGER.info("查询资产变更信息参数：{}", JSON.toJSONString(changeRecordListQueryDTO));
        AssertUtils.notNull(changeRecordListQueryDTO, "查询参数不能为空！");
        AssertUtils.notNull(changeRecordListQueryDTO.getAssetsInfoId(), "资产ID不能为空！");
        List<AssetsChangeRecordPO> changeRecordList =
            assetsChangeRecordPOMapper.findChangeRecordByParentIdAndCondition(changeRecordListQueryDTO);
        return assetsChangeRecordConvertor.convert(changeRecordList);
    }

    /**
     * 登记资产
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addAssets(AssetsInfoDTO addDTO) {
        LOGGER.info("登记资产参数:{}", JSON.toJSONString(addDTO));
        AssertUtils.notNull(addDTO, "资产信息不能为空");
        AssertUtils.notNull(addDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(addDTO.getName(), "资产名称不能为空");
        AssertUtils.notNull(addDTO.getSpecifications(), "资产规格不能为空");
        AssertUtils.notNull(addDTO.getAssetsType(), "资产类型不能为空");
        // 生成资产编号
        Integer batchNumber = ObjectUtils.defaultIfNull(addDTO.getBatchNumber(), 1);
        List<AssetsInfoPO> assetsInfoDTOList = Lists.newArrayList();
        for (int i = 0; i < batchNumber; i++) {
            AssetsInfoPO po = assetsInfoConvertor.reverseConvert(addDTO);
            po.setId(assetsIdGenerator.getUUidLong());
            po.setCode(assetsCodeGenerator.generator(addDTO.getWarehouseId(), addDTO.getAssetsType()));
            po.setState(AssetsStateEnum.IN_STORE.getValue());
            po.setRetrieve(
                addDTO.getRetrieve() == null ? AssetsRetrieveEnum.RECOVERY.getValue() : addDTO.getRetrieve());
            po.setLastUpdateUser(po.getCreateUser());
            assetsInfoDTOList.add(po);
        }

        if (CollectionUtils.isNotEmpty(assetsInfoDTOList)) {
            assetsInfoPOMapper.insertList(assetsInfoDTOList);
        }
    }

    /**
     * 资产操作-领用, 归还, 报废
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void operateAssets(AssetsOperationDTO operationDTO) {
        LOGGER.info("资产变更参数:{}", JSON.toJSONString(operationDTO));
        validateOperationParam(operationDTO);
        List<String> assetsInfoCodeList = operationDTO.getAssetsinfoCodeList();
        List<RedisLock> redisLocks = Lists.newArrayList();
        try {
            redisLocks = setAssetsLock(assetsInfoCodeList);
            // 根据变更类型查询不同资产
            Integer changeEvent = operationDTO.getChangeEvent();
            List<AssetsInfoPO> assetsInfoPOS;
            if (AssetsChangeEventEnum.DISCARD.getValue().equals(changeEvent)) {
                // changeEvent 报废 -> state 在库\领用
                assetsInfoPOS = assetsInfoPOMapper.selectByCodeAndState(assetsInfoCodeList,
                    Lists.newArrayList(AssetsStateEnum.CONSUMING.getValue(), AssetsStateEnum.IN_STORE.getValue()));
            } else {
                Integer queryState = getQueryAssetsState(changeEvent);
                // 不是报废
                assetsInfoPOS =
                    assetsInfoPOMapper.selectByCodeAndState(assetsInfoCodeList, Lists.newArrayList(queryState));
            }
            if (CollectionUtils.isEmpty(assetsInfoPOS)) {
                throw new BusinessValidateException("资产变更失败，请查询后再操作。");
            }
            // 操作时间
            final Date operationDate = new Date();
            try {
                operationDate
                    .setTime(DateUtils.parseDate(operationDTO.getOperationTime(), DATE_FORMAT_YMD24HMS).getTime());
            } catch (ParseException e) {
                LOGGER.warn("资产管理 - operateAssets 操作时间格式化出错！", e);
            }
            // 变更资产信息表数据
            AssetsStateEnum stateEnum = AssetsStateHelper.convertChangeEventToState(operationDTO.getChangeEvent());
            List<AssetsChangeRecordPO> changeRecordPOListForInsert = Lists.newArrayList();
            assetsInfoPOS.stream().forEach(d -> {
                // 资产信息
                d.setState(stateEnum.getValue());
                d.setRemark(operationDTO.getRemark());
                d.setLastUpdateUser(operationDTO.getOperators());
                d.setLastUpdateTime(operationDate);
                // 资产变更信息
                AssetsChangeRecordPO changeRecordPO = new AssetsChangeRecordPO();
                changeRecordPO.setId(assetsIdGenerator.getUUidLong());
                changeRecordPO.setAssetsinfoId(d.getId());
                changeRecordPO.setAssetsinfoCode(d.getCode());
                changeRecordPO.setChangePerson(operationDTO.getOperators());
                changeRecordPO.setChangeTime(operationDate);
                changeRecordPO.setChangeEvent(operationDTO.getChangeEvent());
                changeRecordPO.setRemark(operationDTO.getRemark());
                changeRecordPOListForInsert.add(changeRecordPO);
            });
            // 更新资产信息
            assetsInfoPOMapper.batchUpdateAssetsInfo(assetsInfoPOS);
            // 增加变更记录
            if (CollectionUtils.isNotEmpty(changeRecordPOListForInsert)) {
                LOGGER.info("资产变更后信息:{}", JSON.toJSONString(changeRecordPOListForInsert));
                assetsChangeRecordPOMapper.insertList(changeRecordPOListForInsert);
            }
        } finally {
            afterProcessUnlock(redisLocks);
        }
    }

    /**
     * 获取查询资产状态
     *
     * @param changeEvent
     * @return
     */
    private Integer getQueryAssetsState(Integer changeEvent) {
        if (AssetsChangeEventEnum.GIVE_BACK.getValue().equals(changeEvent)) {
            // changeEvent 归还 -> state 领用
            return AssetsStateEnum.CONSUMING.getValue();
        }

        if (AssetsChangeEventEnum.CONSUMING.getValue().equals(changeEvent)) {
            // changeEvent 领用 -> state 在库
            return AssetsStateEnum.IN_STORE.getValue();
        }
        return null;
    }

    private void validateOperationParam(AssetsOperationDTO operationDTO) {
        AssertUtils.notNull(operationDTO, "资产变更参数不能为空！");
        AssertUtils.notEmpty(operationDTO.getAssetsinfoCodeList(), "资产编码不能为空！");
        AssertUtils.notNull(operationDTO.getChangeEvent(), "资产变更类型不能为空！");
        AssertUtils.notNull(operationDTO.getOperators(), "资产变更操作人不能为空！");
        AssertUtils.notNull(operationDTO.getOperationTime(), "资产变更时间不能为空！");
        List<String> assetsinfoCodeList = operationDTO.getAssetsinfoCodeList().stream().filter(d -> d != null)
            .distinct().collect(Collectors.toList());
        AssertUtils.notEmpty(assetsinfoCodeList, "资产编码不能为空！");
        operationDTO.setAssetsinfoCodeList(assetsinfoCodeList);
    }

    private List<RedisLock> setAssetsLock(List<String> codes) {
        List<RedisLock> redisLocks = Lists.newArrayList();
        for (int i = 0; i < codes.size(); i++) {
            String code = codes.get(i);
            RedisLock lock = new RedisLock(redisTemplate, "supf:assets:codeLock:" + code);
            try {
                if (lock.lock()) {
                    redisLocks.add(lock);
                } else {
                    LOGGER.info("加锁失败：{}", code);
                    codes.remove(code);
                }
            } catch (Exception e) {
                throw new BusinessException("变更资产信息失败！", e);
            }
        }
        return redisLocks;
    }

    /**
     * 释放锁
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2018/4/19 10:27
     */
    private void afterProcessUnlock(List<RedisLock> redisLocks) {
        redisLocks.stream().forEach(input -> {
            input.unlock();
        });

    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateById(AssetsInfoDTO dto) {
        LOGGER.info("修改资产参数:{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "资产信息不能为空");
        AssertUtils.notNull(dto.getId(), "资产ID不能为空");
        AssetsInfoPO assetsInfoPO = new AssetsInfoPO();
        BeanUtils.copyProperties(dto, assetsInfoPO);
        assetsInfoPOMapper.updateByPrimaryKeySelective(assetsInfoPO);
    }

    /**
     * 编辑资产信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateAssetsInfo(AssetsUpdateDTO updateDTO) {
        LOGGER.info("updateAssetsInfo - 修改资产参数:{}", JSON.toJSONString(updateDTO));
        AssertUtils.notNull(updateDTO, "资产信息不能为空");
        AssertUtils.notNull(updateDTO.getId(), "资产ID不能为空");
        AssertUtils.notNull(updateDTO.getName(), "资产名称不能为空");
        AssetsInfoPO assetsInfoPO = new AssetsInfoPO();
        BeanUtils.copyProperties(updateDTO, assetsInfoPO);
        assetsInfoPO.setLastUpdateUser(updateDTO.getOperatorUser());
        if (assetsInfoPO.getVolume() == null) {
            // 计算体积
            assetsInfoPO.setVolume(assetsInfoConvertor.getVolume(assetsInfoPO.getLength(), assetsInfoPO.getWidth(),
                assetsInfoPO.getHeight()));
        }
        //
        AssetsInfoPO infoPO = assetsInfoPOMapper.selectByPrimaryKey(assetsInfoPO.getId());
        if (infoPO == null) {
            throw new BusinessException("资产信息不存在，请刷新列表再操作！");
        }
        if (!Objects.equals(AssetsStateEnum.IN_STORE.getValue(), infoPO.getState())) {
            throw new BusinessValidateException("资产状态已变更不允许修改！");
        }
        assetsInfoPOMapper.updateAssetsInfoByEdit(assetsInfoPO);
    }
}
