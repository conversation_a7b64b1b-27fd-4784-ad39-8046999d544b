package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionPalletCountUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationModifyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
@Service
public class AsyncLocationBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncLocationBL.class);

    @Autowired
    private LocationPOMapper locationPOMapper;

    /**
     * 异步批量更新货位托盘数量
     *
     * @param updateDTO
     * @return
     */
    @Async(value = "warehouseProductTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updatePalletCountBatch(LoactionPalletCountUpdateDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "参数为空");
        AssertUtils.notNull(updateDTO.getWarehouseId(), "仓库Id不能为空");

        Integer warehouseId = updateDTO.getWarehouseId();
        LocationQueryDTO queryDTO = new LocationQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSubcategory(LocationAreaEnum.周转区.getType().byteValue());
        queryDTO.setLocSubcategory(LocationEnum.出库位.getType().byteValue());
        queryDTO.setNoPalletCount(true);
        Map<String, Object> params = convertToMap(queryDTO);
        List<LoactionDTO> loactionDTOS = locationPOMapper.findLocationListByWarehouseIdAndAreaType(params);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            LOGGER.info("批量更新货位托盘数量,无需处理仓库id" + warehouseId);
            return;
        }

        List<Long> locationIds = loactionDTOS.stream().map(LoactionDTO::getId).distinct().collect(Collectors.toList());
        Lists.partition(locationIds, 1000).forEach(list -> {
            LocationModifyDTO modifyDTO = new LocationModifyDTO();
            modifyDTO.setCityId(loactionDTOS.get(0).getCityId());
            modifyDTO.setWarehouseId(warehouseId);
            modifyDTO.setIdList(locationIds);
            modifyDTO.setPalletCount(updateDTO.getPalletCount() == null ? 1 : updateDTO.getPalletCount());
            locationPOMapper.updateSelectiveByIdList(modifyDTO);
        });
        LOGGER.info("批量更新货位托盘数量完成,处理仓库id" + warehouseId);
    }

    private Map<String, Object> convertToMap(LocationQueryDTO dto) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("warehouseId", dto.getWarehouseId());
        params.put("subcategory", dto.getSubcategory());
        params.put("locSubcategory", dto.getLocSubcategory());
        params.put("noPalletCount", dto.getNoPalletCount());
        return params;
    }
}
