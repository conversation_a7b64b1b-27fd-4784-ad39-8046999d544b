package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;

/**
 * 异步更新产品
 *
 * <AUTHOR>
 * @since 2024-8-28
 */
@Service
public class AsyncProductSkuUpdateBL {

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    /**
     * 是否删除更新
     */
    @Async(value = "warehouseProductTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateIsDeleteBatch(Collection<Long> updateSkuIdList, Byte isDelete) {
        if (CollectionUtils.isEmpty(updateSkuIdList) || isDelete == null) {
            return;
        }
        productSkuPOMapper.updateIsDeleteBatch(updateSkuIdList, isDelete);
    }

    /**
     * 更新 sku ProductState
     */
    @Async(value = "warehouseProductTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStateBatch(Collection<Long> updateSkuIdList, Integer state) {
        if (CollectionUtils.isEmpty(updateSkuIdList) || state == null) {
            return;
        }
        Lists.partition(new ArrayList<>(updateSkuIdList), 1000).forEach(it ->
                productSkuPOMapper.updateProductStateBatch(it, state)
        );
    }

}
