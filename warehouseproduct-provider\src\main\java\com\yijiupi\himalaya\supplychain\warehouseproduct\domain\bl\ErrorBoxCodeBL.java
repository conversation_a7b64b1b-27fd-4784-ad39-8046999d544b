package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskCreateParam;
import com.yijiupi.himalaya.assignment.enums.todo.AssignType;
import com.yijiupi.himalaya.assignment.enums.todo.TodoTaskRank;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo.ProductCodeWhitelistBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo.TodoTaskBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ErrorBoxCodeMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ErrorBoxCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ErrorBoxCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductCodeWhitelistDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ErrorBoxCodeSO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 错误条码结果记录
 *
 * <AUTHOR>
 * @since 2019/1/10 12:00
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class ErrorBoxCodeBL {

    @Resource
    private ProductSkuQueryBL productSkuQueryBL;

    @Resource
    private ProductLocationBL productLocationBL;

    @Resource
    private TodoTaskBL todoTaskBL;

    @Autowired
    private ErrorBoxCodeMapper mapper;

    @Resource
    private ProductCodeWhitelistBL productCodeWhitelistBL;

    public static final String TMJZ_CODE = "TMJZ";

    private static final Integer NMAXCODELENGTH = 50;

    private static final String TODO_TASK_TEMPLATE = "%s (关联货位: %s) 疑似条码错误 (系统条码: %s), 请立即检查并矫正!";

    private static final Logger LOG = LoggerFactory.getLogger(ErrorBoxCodeBL.class);

    /**
     * 批量新增错误条码记录
     *
     */
    public void saveErrorBoxCodeList(List<ErrorBoxCodeDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            LOG.warn("批量新增错误条码记录不能为空");
            return;
        }
        dtos.forEach(p -> {
            if (p.getCode() != null && p.getCode().length() > NMAXCODELENGTH) {
                p.setCode(p.getCode().substring(0, NMAXCODELENGTH));
            }
        });
        List<ErrorBoxCodePO> ErrorBoxCodePOS = dtos.stream().map(dto -> {
            ErrorBoxCodePO po = new ErrorBoxCodePO();
            BeanUtils.copyProperties(dto, po);
            po.setId(UUIDGenerator.getUUID(ErrorBoxCodePO.class.getName()));
            return po;
        }).collect(Collectors.toList());
        mapper.insertList(ErrorBoxCodePOS);
        LOG.info("新增错误条码记录：{}条", ErrorBoxCodePOS.size());
        // 创建条码矫正任务
        createTodoTask(dtos);
    }

    /**
     * 查看错误条码记录
     */
    public PageList<ErrorBoxCodeDTO> listErrorBoxCode(ErrorBoxCodeSO so) {
        LOG.info("查看错误条码记录参数：{}", JSON.toJSONString(so));
        PageResult<ErrorBoxCodePO> pageResult = mapper.listRecord(so);
        List<ErrorBoxCodePO> poList = pageResult.toPageList().getDataList();
        List<ErrorBoxCodeDTO> dtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(poList)) {
            dtoList = poList.stream().map(po -> {
                ErrorBoxCodeDTO dto = new ErrorBoxCodeDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        PageList<ErrorBoxCodeDTO> pageList = new PageList<>();
        pageList.setDataList(dtoList);
        pageList.setPager(pageResult.toPageList().getPager());
        return pageList;
    }

    /**
     * 创建待办任务
     *
     * @param codes 错误条码数据
     */
    private void createTodoTask(List<ErrorBoxCodeDTO> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            LOG.info("数据为空, 跳过后续操作");
            return;
        }
        Integer warehouseId = codes.get(0).getWarehouseId();
        Set<Long> skuIds = codes.stream().map(ErrorBoxCodeDTO::getSkuId).collect(Collectors.toSet());
        Map<Long, ProductSkuDTO> skuMap = productSkuQueryBL.findBySkuFull(warehouseId, skuIds).stream()
                .collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, Function.identity()));
        Map<Long, List<LoactionDTO>> skuLocationMap = productLocationBL.findLocationDTOBySkuId(warehouseId, skuIds);
        Map<Long, ProductCodeWhitelistDTO> skuWhitelistMap = productCodeWhitelistBL.selectValidWhitelist(warehouseId, skuIds).stream()
                .collect(Collectors.toMap(ProductCodeWhitelistDTO::getSkuId, Function.identity(), (a, b) -> a));
        Map<Long, Integer> taskPropertyMap = todoTaskBL.calcSkuTodoTaskProperty(warehouseId, skuIds);
        List<TodoTaskCreateParam> tasks = codes.stream().map(ErrorBoxCodeDTO::getSkuId).distinct().map(it -> {
            ProductSkuDTO sku = skuMap.get(it);
            if (sku == null) {
                LOG.info("skuId: {} 不存在", it);
                return null;
            }
            if (skuWhitelistMap.get(it) != null) {
                LOG.info("该 sku 在白名单内, 不生成条码矫正任务");
                return null;
            }
            String barCode = Optional.ofNullable(sku.getBoxCode()).filter(StringUtils::hasText).orElse("无");
            String locations = skuLocationMap.getOrDefault(it, Collections.emptyList())
                    .stream().map(LoactionDTO::getName).collect(Collectors.joining(","));
            locations = locations.isEmpty() ? "无" : locations;
            return todoTaskBL.newBuilder()
                    .setTaskTypeCode(TMJZ_CODE)
                    .setTaskRank(TodoTaskRank.HIGH)
                    // 以 仓库 id, specId, skuId 组成 businessNo, 后续根据这个去重
                    .setBusinessNo(String.format("%s,%s,%s", warehouseId, sku.getProductSpecificationId(), it))
                    .setStartTime(new Date())
                    .setOverdueDays(1)
                    .setAssignType(AssignType.WAREHOUSE)
                    .addAssignId(warehouseId)
                    .addAssignRole(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程)
                    .setTaskDetail(String.format(TODO_TASK_TEMPLATE, sku.getName(), locations, barCode))
                    .setTaskProperty(taskPropertyMap.get(it))
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
        todoTaskBL.createTask(tasks);
    }
}
