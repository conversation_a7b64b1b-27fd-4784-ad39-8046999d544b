package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ImportOwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.EnableStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.excel.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 开仓作业初始数据导入
 *
 * <AUTHOR> 2022/10/25
 */
@Service
public class ImportOpeningDataBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImportOpeningDataBL.class);

    @Autowired
    private OwnerBL ownerBL;

    @Autowired
    private PartnerManagerBl partnerManagerBl;

    public void importOwner(ImportOwnerDTO dto) {
        Assert.notNull(dto, "导入参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市不能为空！");
        AssertUtils.notNull(dto.getFile(), "上传失败，无法找到文件！");
        LOGGER.info("importOwner 批量导入货主,参数：{}", dto.getFile().getOriginalFilename());
        try {
            List<OwnerDTO> list =
                ExcelUtils.readExcelOnwer(dto.getFile().getInputStream(), dto.getFile().getOriginalFilename());
            AssertUtils.notEmpty(list, "不能导入空的excel文件");
            list.forEach(it -> it.setCityId(dto.getCityId()));
            list.forEach(it -> {
                it.setState((byte)EnableStateEnum.ENABLE.getType());
                it.setCreateTime(new Date());
                it.setLastUpdateTime(new Date());
            });
            ownerBL.insertBatchOnwer(list);

        } catch (Exception e) {
            LOGGER.error("importOwner 批量导入货主异常", e);
        }
    }

    public void importPartner(ImportOwnerDTO dto) {
        Assert.notNull(dto, "导入参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市不能为空！");
        AssertUtils.notNull(dto.getFile(), "上传失败，无法找到文件！");
        LOGGER.info("importPartner 批量导入合作伙伴,参数：{}", dto.getFile().getOriginalFilename());
        try {
            List<PartnerManagerDTO> list =
                ExcelUtils.readExcelParter(dto.getFile().getInputStream(), dto.getFile().getOriginalFilename());
            AssertUtils.notEmpty(list, "不能导入空的excel文件");
            list.forEach(it -> {
                it.setCityId(dto.getCityId());
                it.setCreateUser(null != dto.getUserId() ? dto.getUserId().longValue() : null);
                it.setLastUpdateUser(null != dto.getUserId() ? dto.getUserId().longValue() : null);
                it.setStatus((byte)EnableStateEnum.ENABLE.getType());
                it.setCreateTime(new Date());
                it.setLastUpdateTime(new Date());
            });
            partnerManagerBl.insertBatchPartnerManager(list);

        } catch (Exception e) {
            LOGGER.error("importPartner 批量导入合作伙伴异常", e);
        }
    }
}
