package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.common.dto.PageListUtil;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationAreaConvetor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.LocationPageQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CodeGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.excel.ExcelUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * <AUTHOR> 2018/1/19
 */
@Service
public class LocationAreaBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(LocationAreaBL.class);
    @Autowired
    private LocationAreaPOMapper locationAreaMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private CodeGenerator codeGenerator;
    @Autowired
    private LocationBL locationBL;

    private final Integer pageSize = 3000;

    /**
     * 新增货区
     *
     * @param locationAreaDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addLocationArea(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaPO = LocationAreaConvetor.locationAreaDTO2PO(locationAreaDTO);
        // 判断货区是否存在
        LocationAreaPO locationAreaOldPO = locationAreaMapper.getLocationArea(locationAreaPO);
        if (locationAreaOldPO != null) {
            AssertUtils.isTrue(false, "该货区名称已存在");
        }

        locationAreaPO = LocationAreaConvetor.getLocationAreaPO(locationAreaPO, locationAreaDTO);
        locationAreaPO.setId(UUIDGenerator.getUUID(LocationAreaPO.class.getName()));
        locationAreaMapper.addLocationArea(locationAreaPO);
        // LoactionDTO dto = new LoactionDTO();
        // dto.setName(locationAreaDTO.getArea());
        // dto.setArea(locationAreaDTO.getArea());
        // dto.setArea_Id(locationAreaPO.getId());
        // //dto.setProductlocation(locationAreaPO.getArea());
        // dto.setCityId(locationAreaDTO.getCityId());
        // dto.setWarehouseId(locationAreaDTO.getWarehouseId());
        // dto.setUserId(locationAreaDTO.getUserId());
        // productLocationBL.add(dto);
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(locationAreaDTO.getWarehouseId());
    }


    public LocationAreaPO getLocationAreaByName(Integer cityid,Integer warehouseId,String name) {
        LocationAreaPO locationAreaPO = new LocationAreaPO();
        locationAreaPO.setName(name);
        locationAreaPO.setWarehouse_Id(warehouseId);
        locationAreaPO.setCity_Id(cityid);
        return locationAreaMapper.getLocationArea(locationAreaPO);
    }

    public LocationReturnDTO findLocationById(String id) {
        LocationReturnDTO dto = null;
        LocationPO po = locationPOMapper.findLocationById(Long.valueOf(id));
        if (po != null) {
            dto = new LocationReturnDTO();
            BeanUtils.copyProperties(po, dto);

            Byte isChaosBatch = po.getIsChaosBatch();
            if (isChaosBatch != null && isChaosBatch == 0) {
                dto.setIsChaosBatch(false);
            } else if (isChaosBatch != null && isChaosBatch == 1) {
                dto.setIsChaosBatch(true);
            }

            Byte isChaosPu = po.getIsChaosPut();
            if (isChaosPu != null && isChaosPu == 0) {
                dto.setIsChaosPut(false);
            } else if (isChaosPu != null && isChaosPu == 1) {
                dto.setIsChaosPut(true);
            }
        }
        return dto;
    }

    /**
     * 查询货区
     */
    public PageList<LocationAreaReturnDTO> getLocationArea(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaPO = LocationAreaConvetor.locationAreaDTO2PO(locationAreaDTO);
        Integer pageNum = locationAreaDTO.getPageNum();
        Integer pageSize = locationAreaDTO.getPageSize();
        Boolean hasLocation = locationAreaDTO.getHasLocation();
        PageHelper.startPage(pageNum, pageSize);
        PageResult<LocationAreaPO> result = locationAreaMapper.listLocationArea(locationAreaPO, hasLocation);
        if (CollectionUtils.isEmpty(result)) {
            return PageListUtil.getDefault(new PagerCondition(pageNum, pageSize));
        }
        return result.toPageList(it -> LocationAreaConvetor.locationAreaPOList2DTO(it, getLocationAreaState(it)));
    }

    /**
     * 查询货区不分页
     */
    public List<LocationAreaReturnDTO> getLocationAreaNoPage(LocationAreaListDTO locationAreaDTO) {
        List<LocationAreaPO> locationAreaPOList = locationAreaMapper.listLocationAreaNoPage(locationAreaDTO);
        return LocationAreaConvetor.locationAreaPOList2DTO(locationAreaPOList, null);
    }

    /**
     * 获取货区是否绑定产品的 map
     *
     * @param po 货区数据
     * @return key 为 货区 id, value 为 是否绑定产品: 1 有绑定产品, 0: 无绑定产品
     */
    private Map<Long, Integer> getLocationAreaState(List<LocationAreaPO> po) {
        int pageCount = 1;
        List<LocationPO> locationPOList = new ArrayList<>();
        LocationPageQueryDTO query = new LocationPageQueryDTO();
        query.setLocationAreaId(po.stream().map(LocationAreaPO::getId).collect(Collectors.toList()));
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            PageResult<LocationPO> result = locationPOMapper.pageListProductCountList(query, pageNum, pageSize);
            if (pageNum == 1) {
                pageCount = result.getPager().getTotalPage();
            }
            locationPOList.addAll(result);
        }
        return locationPOList.stream().collect(Collectors.groupingBy(LocationPO::getArea_Id)).entrySet().stream()
            .map(it -> {
                Long id = it.getKey();
                boolean hasLoc = it.getValue().stream().map(LocationPO::getProductLocation).anyMatch(Objects::nonNull);
                return Pair.of(id, hasLoc ? 1 : 0);
            }).collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
    }

    /**
     * 编辑货区
     *
     * @param locationAreaDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateLocationArea(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaReturnPO = locationAreaMapper.getLocationAreaById(locationAreaDTO.getId());
        if (locationAreaReturnPO == null) {
            throw new BusinessException("找不到对应货区");
        }

        // 不管怎么样都可以编辑备注和容量
        Boolean isSameName = locationAreaReturnPO.getName().equalsIgnoreCase(locationAreaDTO.getArea())
            && locationAreaReturnPO.getSubcategory().equals(locationAreaDTO.getSubcategory());
        // if (isSameName) {
        // LocationAreaPO locationAreaPO = LocationAreaConvetor.getLocationAreaPO(locationAreaDTO);
        // locationAreaMapper.updateLocationArea(locationAreaPO);
        // return;
        // }
        // 判断有没有绑定产品
        Integer countById = locationPOMapper.listProductCount(locationAreaReturnPO.getId());
        boolean hasBind = (!isSameName || locationAreaDTO.getState() != null) && countById != 0;
        if (hasBind) {
            throw new BusinessValidateException("该货区已绑定产品，无法修改");
        }
        // 判断名称是否重复
        if (locationAreaDTO.getArea() != null && !isSameName) {
            String area = locationAreaMapper.getLocationAreaByArea(locationAreaReturnPO.getWarehouse_Id(),
                locationAreaDTO.getArea(), locationAreaReturnPO.getId());
            AssertUtils.isNull(area, "该货区名称已存在");
        }
        LocationAreaPO locationAreaPO = LocationAreaConvetor.getUpdateLocationAreaPO(locationAreaDTO);
        locationAreaMapper.updateLocationAreaByPO(locationAreaPO);
        if (locationAreaPO.getState() != null) {
            List<LocationPO> locList = locationPOMapper.selectByAreaId(locationAreaDTO.getId());
            if (!CollectionUtils.isEmpty(locList)) {
                List<Long> locationIds = locList.stream().map(LocationPO::getId).collect(Collectors.toList());
                LocationModifyDTO locModifyDTO = new LocationModifyDTO();
                locModifyDTO.setCityId(locationAreaReturnPO.getCity_Id());
                locModifyDTO.setWarehouseId(locationAreaReturnPO.getWarehouse_Id());
                locModifyDTO.setIdList(locationIds);
                locModifyDTO.setState(locationAreaPO.getState());
                locationBL.modifyLocation(locModifyDTO);
            }
        }
        // 更新货位名称
        if (locationAreaDTO.getArea() != null && !isSameName) {
            productLocationBL.update(locationAreaReturnPO.getId(), locationAreaDTO.getUserId());
        }
        // 更新货位业务类型
        updateLocationBusinessTypeByArea(locationAreaDTO);
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(locationAreaReturnPO.getWarehouse_Id());
    }

    /**
     * 删除货区
     *
     * @param locationAreaDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteLocationArea(LocationAreaDTO locationAreaDTO, OperatorDTO operatorDTO) {
        LocationAreaPO locationAreaReturnPO = locationAreaMapper.getLocationAreaById(locationAreaDTO.getId());
        if (locationAreaReturnPO == null) {
            throw new RuntimeException("找不到对应货区");
        }
        // 判断有没有绑定产品
        Integer countById = locationPOMapper.listProductCount(locationAreaReturnPO.getId());
        if (countById != 0) {
            throw new RuntimeException("该货区已绑定产品，无法删除");
        }
        // 记录删除日志
        LocationAreaPO deleteArea = locationAreaMapper.selectByPrimaryKey(locationAreaDTO.getId());
        List<LocationPO> deleteLocations = locationPOMapper.selectByAreaId(locationAreaDTO.getId());
        // 删除货区以及默认创建的货位
        locationAreaMapper.deleteLocation(locationAreaDTO.getId());
        locationPOMapper.deleteByAreaId(locationAreaDTO.getId());
        LOGGER.info("[删除货区]{}, 包含货位：{}, 操作人：{}", JSON.toJSONString(deleteArea), JSON.toJSONString(deleteLocations),
            JSON.toJSONString(operatorDTO));
        // 保存当前仓库最近删除货位操作时间
        codeGenerator.updateLocationDeleteTime(deleteArea != null ? deleteArea.getWarehouse_Id() : null);
    }

    /**
     * 根据id集合获得货区/货位信息
     *
     * @param idList
     * @return
     */
    public List<LocationReturnDTO> findLocationListById(List<String> idList) {
        List<LocationPO> locationPOList = locationPOMapper.findLocationListById(idList);
        return Lists.transform(locationPOList, input -> {
            LocationReturnDTO dto = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto);
            dto.setSubcategoryName(getLocationTypeName(dto.getCategory(), dto.getSubcategory()));
            return dto;
        });
    }

    /**
     * 根据id集合获得货区/货位信息
     *
     * @param idList
     * @return
     */
    public List<LocationReturnDTO> findLocationAreaListById(List<String> idList) {
        List<LocationPO> locationPOList = locationPOMapper.findLocationAreaListById(idList);
        return Lists.transform(locationPOList, input -> {
            LocationReturnDTO dto = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto);
            dto.setSubcategoryName(getLocationTypeName(dto.getCategory(), dto.getSubcategory()));
            return dto;
        });
    }

    /**
     * 获取货位/货区类型名称
     *
     * @param category
     * @param subcategory
     * @return
     */
    private String getLocationTypeName(Byte category, Byte subcategory) {
        if (null == category || null == subcategory) {
            return null;
        }
        // 0 货位
        if (0 == category) {
            return LocationEnum.getEnumStr(Integer.valueOf(subcategory));
            // 1 货区
        } else {
            return LocationAreaEnum.getEnumStr(Integer.valueOf(subcategory));
        }
    }

    /**
     * 根据货位或者货区名称，查询仓库指定货位
     */
    public LocationInfoDTO getLocationByName(String locatioName, Integer warehouseId) {
        LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
        LocationPO locationPO = locationPOMapper.findLocationByName(locatioName, warehouseId);
        if (locationPO != null) {
            locationInfoDTO.setLocationId(locationPO.getId());
            locationInfoDTO.setLocationName(locationPO.getName());
        }
        return locationInfoDTO;
    }

    /**
     * 根据仓库ID查询货区并检查货区是否创建
     *
     * @param warehouseId
     * @return
     */
    public CargoAreaCheckResultDTO isCreatedCargoArea(Integer warehouseId) {
        Assert.notNull(warehouseId, "仓库id为空");
        List<LocationPO> locationPOS = locationPOMapper.selectCargoAreaSingleByWarehouseIdAndCategory(warehouseId,
            CategoryEnum.CARGO_AREA.getValue());
        CargoAreaCheckResultDTO resultDTO = createCargoAreaCheckResult(locationPOS, warehouseId);
        return resultDTO;
    }

    private CargoAreaCheckResultDTO createCargoAreaCheckResult(List<LocationPO> locationPOS, Integer warehouseId) {
        CargoAreaCheckResultDTO resultDTO = new CargoAreaCheckResultDTO();
        resultDTO.setWarehouseId(warehouseId);
        if (CollectionUtils.isEmpty(locationPOS)) {
            resultDTO.setCreatedCargoArea(false);
            resultDTO.setPromptInformation("未找到仓库货区信息！");
            return resultDTO;
        }
        List<LocationAreaEnum> checkCargoAreas = Lists.newArrayList(LocationAreaEnum.getCargoAreaCheckEnums());
        List<Integer> checkCargoAreaTypes = checkCargoAreas.stream().map(d -> d.getType()).collect(Collectors.toList());
        // 删除不检查的Subcategory
        locationPOS.removeIf(d -> !checkCargoAreaTypes.contains(d.getSubcategory().intValue()));
        List<Integer> dbTypes =
            locationPOS.stream().map(d -> d.getSubcategory().intValue()).collect(Collectors.toList());

        if (dbTypes.containsAll(checkCargoAreaTypes)) {
            resultDTO.setCreatedCargoArea(true);
            resultDTO.setPromptInformation("货区已经创建！");
        } else {
            checkCargoAreas.removeIf(e -> dbTypes.contains(e.getType()));
            String msg = checkCargoAreas.stream().map(d -> d.name())
                .collect(Collectors.joining(",", "仓库" + warehouseId + "【", "】未创建！"));
            resultDTO.setPromptInformation(msg);
            resultDTO.setCreatedCargoArea(false);
        }
        return resultDTO;
    }

    /**
     * 根据货位id查询货位和货区信息
     */
    public List<LoactionDTO> findLocationAndAreaInfoById(List<Long> idList) {
        return locationPOMapper.findLocationAndAreaInfoById(idList);
    }

    public void importLocationArea(ImportLocationDTO dto) {
        Assert.notNull(dto, "导入参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库不能为空！");
        AssertUtils.notNull(dto.getFile(), "上传失败，无法找到文件！");
        LOGGER.info("importLocationArea 批量导入库位,参数：{}", dto.getFile().getOriginalFilename());
        try {
            List<LocationAreaDTO> list =
                ExcelUtils.readLocationArea(dto.getFile().getInputStream(), dto.getFile().getOriginalFilename());
            AssertUtils.notEmpty(list, "不能导入空的excel文件");
            list.forEach(it -> {
                it.setCityId(dto.getCityId());
                it.setWarehouseId(dto.getWarehouseId());
                it.setUserId(dto.getUserId());
            });
            for (LocationAreaDTO locationArea : list) {
                addLocationArea(locationArea);
            }
        } catch (Exception e) {
            LOGGER.error("importLocationArea 批量导入库区异常", e);
        }
    }

    /**
     * 根据条件获得货区排除残次品位、残次品区
     *
     * @param idList
     * @return
     */
    public List<LocationReturnDTO> findLocationAreaListExcludeDefective(List<String> idList) {
        List<LocationPO> locationPOList = locationPOMapper.findLocationAreaListExcludeDefective(idList);
        return Lists.transform(locationPOList, input -> {
            LocationReturnDTO dto = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto);
            dto.setSubcategoryName(getLocationTypeName(dto.getCategory(), dto.getSubcategory()));
            return dto;
        });
    }

    private void updateLocationBusinessTypeByArea(LocationAreaDTO locationAreaDTO) {
        if (!Objects.equals(locationAreaDTO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())) {
            return;
        }

        List<LocationPO> locationPOS = locationPOMapper.findLocationByAreaIds(Arrays.asList(locationAreaDTO.getId()));
        if (CollectionUtils.isEmpty(locationPOS)) {
            return;
        }

        List<Integer> subcategoryList = Arrays.asList(LocationEnum.分拣位.getType(), LocationEnum.零拣位.getType());
        List<Long> updateIds = locationPOS.stream().filter(p -> subcategoryList.contains(p.getSubcategory()))
            .map(LocationPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateIds)) {
            return;
        }

        LocationModifyDTO modifyDTO = new LocationModifyDTO();
        modifyDTO.setIdList(updateIds);
        modifyDTO.setBusinessType(locationAreaDTO.getBusinessType());
        locationPOMapper.updateLocationBusinessTypeById(modifyDTO);
        LOGGER.info("货位促销业务类型同步修改 ：{}", JSON.toJSONString(modifyDTO));
    }
}
