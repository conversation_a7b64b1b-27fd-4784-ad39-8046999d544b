package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.LOCATION_AREA;
import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.WORKING_PASSAGE;
import static com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ViewModeType.INVENTORY_DISTRIBUTION;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryListQueryService;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigStorageAttributeEnum;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.LocationProductAttributeBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.LocationRuleBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.LocationAreaInfoQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationConvetor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationWithAreaDTOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.FixLocationAreaInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.LocationSequenceByRouteInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.LocationSequenceByRouteInfoResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.OutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationSequenceQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CodeGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.DateUtil;
import com.yijiupi.himalaya.supplychain.waves.batch.ISowQueryService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * <AUTHOR>
 * @since 2017/11/24
 */
@Service
public class LocationBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationBL.class);

    @Autowired
    private AgencyConnectLocationMapper agencyConnectLocationMapper;

    @Autowired
    private LocationPOMapper locationPOMapper;

    @Autowired
    private ProductLocationPOMapper productLocationPOMapper;

    @Autowired
    private ProductStoreBatchPOMapper productStoreBatchPOMapper;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private IProductLocationService iProductLocationService;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Autowired
    private LocationAreaBL locationAreaBL;

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private LocationAreaService locationAreaService;

    @Autowired
    private NotifyWCSBL notifyWCSBL;

    @Reference
    private ISowQueryService iSowQueryService;

    @Autowired
    private WareHouseChargeBL wareHouseChargeBL;
    @Reference
    private IInventoryListQueryService iInventoryListQueryService;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;
    @Autowired
    private ProductSkuServiceBL productSkuServiceBL;
    @Autowired
    private LocationProductAttributeBL locationProductAttributeBL;
    @Resource
    private VesselInfoPOMapper vesselInfoPOMapper;
    @Resource
    private LocationRuleBL locationRuleBL;

    @Autowired
    private AsyncLocationBL asyncLocationBL;

    @Autowired
    private PassageBL passageBL;

    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;

    /**
     * 新增经销商与货位关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addAgencyLocation(AgencyConnectLocationDTO agencyConnectLocationDTO) {
        // 先删除该经销商下的货位配置,让后再插入.
        agencyConnectLocationMapper.removeAgencyLocationByAgencyId(agencyConnectLocationDTO.getAgencyId());
        List<LocationInfoDTO> locationInfoDTOList = agencyConnectLocationDTO.getLocationInfoDTOList();
        if (!CollectionUtils.isEmpty(locationInfoDTOList)) {
            locationInfoDTOList.forEach(p -> {
                p.setId(UUIDGenerator.getUUID(LocationInfoDTO.class.getName()));
            });
        }
        agencyConnectLocationMapper.insertAgencyLocation(agencyConnectLocationDTO);
    }

    /**
     * 根据经销商id和仓库id查询货位信息
     */
    public List<LocationInfoDTO> getLocation(Long agencyId, Integer warehouseId) {
        return agencyConnectLocationMapper.getLocation(agencyId, warehouseId);
    }

    /**
     * 根据经销商Id，仓库Id和SKUID获取对应的货位信息
     */
    public List<LocationInfoDTO> getLocation(Long agencyId, Integer warehouseId, Long productSkuId) {
        return agencyConnectLocationMapper.getLocationBySku(agencyId, warehouseId, productSkuId);
    }

    /**
     * 查询未被关联的货位,供经销商选择.
     */
    public PageList<LocationInfoDTO> getUnusedLocation(GetUnusedLocationDTO getUnusedLocationDTO) {
        PageResult<LocationInfoDTO> unusedLocation = agencyConnectLocationMapper.getUnusedLocation(getUnusedLocationDTO,
            getUnusedLocationDTO.getPageNum(), getUnusedLocationDTO.getPageSize());
        return unusedLocation.toPageList();
    }

    /**
     * 删除经销商与货位关系
     */
    public void removeAgencyLocation(AgencyConnectLocationDTO agencyConnectLocationDTO) {
        agencyConnectLocationMapper.removeAgencyLocation(agencyConnectLocationDTO);
    }

    /**
     * 根据经销商查询该经销商所有的货位详细信息
     */
    public List<LocationInfoDTO> getLocationInfoByAgencyId(Long agencyId) {
        return agencyConnectLocationMapper.getLocationInfoByAgencyId(agencyId);
    }

    /**
     * 根据经销商id,删除经销商的所有货位配置.
     */
    public void removeAgencyLocationByAgencyId(Long agencyId) {
        agencyConnectLocationMapper.removeAgencyLocationByAgencyId(agencyId);
    }

    /**
     * 查询出已经有货位关联的经销商的id
     */
    public List<Long> getAgencyIdByLocation() {
        return agencyConnectLocationMapper.getAgencyIdByLocation();
    }

    /**
     * 1.location列表只显示货位、不显示货区； 2.locationarea表已作废； 3.删除货位之前判断货位下面是否有产品，有产品则不允许删除并提示货位名称 删除货位
     * 4.productstorebatch表中totalcount_minunit！=0且location_id能找到则不能删除
     *
     * @param locationModifyDTO
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteLocation(LocationModifyDTO locationModifyDTO, OperatorDTO operatorDTO) {
        AssertUtils.notNull(locationModifyDTO, "入参为空");
        AssertUtils.notNull(locationModifyDTO.getWarehouseId(), "仓库id为null");
        AssertUtils.notEmpty(locationModifyDTO.getIdList(), "货位id为null");
        Integer warehouseId = locationModifyDTO.getWarehouseId();
        // 删除货位校验
        checkDeleteLocation(locationModifyDTO.getWarehouseId(), locationModifyDTO.getIdList());
        // 记录删除日志
        List<LocationPO> deleteLocations = locationPOMapper.getLocationByPrimaryKeyList(locationModifyDTO.getIdList());
        locationPOMapper.deleteLocationByPrimaryKeyList(locationModifyDTO.getIdList());
        LOGGER.info("[删除货位]{}, 操作人：{}", JSON.toJSONString(deleteLocations), JSON.toJSONString(operatorDTO));
        // 通知WCS
        notifyWCSBL.deleteLocation(warehouseId, locationModifyDTO.getIdList());
        // 保存当前仓库最近删除货位操作时间
        codeGenerator.updateLocationDeleteTime(warehouseId);
        // 删除相关周转区配置
        locationRuleBL.deleteByLocationIds(locationModifyDTO.getIdList(), warehouseId);
        // 更新产品配置分仓属性
        productLocationBL.updateStorageAttributeByLocationIds(locationModifyDTO.getUserId(),
            locationModifyDTO.getWarehouseId(), locationModifyDTO.getIdList());
    }

    /**
     * 修改货位信息
     *
     * @param locationModifyDTO
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyLocation(LocationModifyDTO locationModifyDTO) {
        if (locationModifyDTO == null) {
            throw new DataValidateException("参数为空");
        }
        Assert.notNull(locationModifyDTO.getCityId(), "城市id为空");
        Assert.notNull(locationModifyDTO.getWarehouseId(), "仓库id为空");
        if (CollectionUtils.isEmpty(locationModifyDTO.getIdList())) {
            locationModifyDTO.setIdList(null);
        }
        locationModifyDTO.setLastUpdateTime(new Date());
        locationPOMapper.updateSelectiveByIdList(locationModifyDTO);
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(locationModifyDTO.getWarehouseId());
        // 更新产品配置分仓属性
        productLocationBL.updateStorageAttributeByLocationIds(locationModifyDTO.getUserId(),
            locationModifyDTO.getWarehouseId(), locationModifyDTO.getIdList());
    }

    /**
     * 给导入的excel提供修改货位信息的接口
     *
     * @param locationModifyWithWarehouseIdDTO
     * <AUTHOR>
     */
    public void modifyLocation(LocationModifyWithWarehouseIdDTO locationModifyWithWarehouseIdDTO) {
        Assert.notNull(locationModifyWithWarehouseIdDTO.getCityId(), "城市id为空");
        Assert.notNull(locationModifyWithWarehouseIdDTO.getWarehouseId(), "仓库id为空");
        List<LocationModifyDTO> locationModifyDTOList = locationModifyWithWarehouseIdDTO.getLocationModifyDTOList();
        if (CollectionUtils.isEmpty(locationModifyDTOList)) {
            throw new DataValidateException("入参为null");
        }
        LocationModifyDTO perLocationModifyDTO = null;
        try {
            for (LocationModifyDTO locationModifyDTO : locationModifyDTOList) {
                perLocationModifyDTO = locationModifyDTO;
                locationModifyDTO.setSubcategory(getLocationByteByString(locationModifyDTO.getSubcategoryStr()));
                locationModifyDTO.setLastUpdateTime(new Date());
                locationModifyDTO.setWarehouseId(locationModifyWithWarehouseIdDTO.getWarehouseId());
                locationModifyDTO.setCityId(locationModifyWithWarehouseIdDTO.getCityId());
                locationPOMapper.updateSelectiveByNameWithWarehouseIdAndCityId(locationModifyDTO);
            }
        } catch (Exception e) {
            throw new BusinessException(
                "记录---》货位:" + perLocationModifyDTO.getName() + ",货位" + "类型" + perLocationModifyDTO.getSubcategoryStr()
                    + ",线路顺序:" + perLocationModifyDTO.getSequence() + ",货位" + "容量:"
                    + perLocationModifyDTO.getLocationCapacity() + ",是否" + "混放:" + perLocationModifyDTO.getIsChaosPut()
                    + ",是否混批:" + perLocationModifyDTO.getIsChaosBatch() + "   " + "   更新未成功，终止更新后续记录");
        }
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(locationModifyWithWarehouseIdDTO.getWarehouseId());
    }

    /**
     * 根据仓库ID，skuIds获取对应产品货位顺序
     *
     * @param query
     * @return
     */
    public List<LocationSequenceReturnDTO> findLocationSequenceBySkuIds(LocationSequenceQuery query) {
        Assert.notNull(query.getWarehouseId(), "仓库id为空");
        Assert.notNull(query.getProductSkuIds(), "产品SkuId为空");
        List<Long> skuIdList =
            query.getProductSkuIds().stream().filter(d -> d != null).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIdList)) {
            // LOGGER.info("产品SkuId为空,无法获取对应产品货位顺序信息！");
            return null;
        }
        Boolean openLocationStock = warehouseConfigService.isOpenLocationStock(query.getWarehouseId());
        // LOGGER.info("仓库[{}],是否开启货位库存: {}", query.getWarehouseId(), openLocationStock);
        /*
         *	1.没开启货位库存用推荐货位
         *	2.开启货位库存则查询对应货位顺序
         */
        if (openLocationStock) {
            return genLocationSequenceByBatchInventory(query.getWarehouseId(), skuIdList);
        } else {
            return genLocationSequenceByRecommendedCargo(query.getWarehouseId(), skuIdList);
        }
    }

    /**
     * 开启货位库存根据已有的货位获取sequence
     *
     * @param warehouseId
     * @param skuIdList
     * @return
     */
    private List<LocationSequenceReturnDTO> genLocationSequenceByBatchInventory(Integer warehouseId,
        List<Long> skuIdList) {
        BatchInventoryQueryDTO locationQuery = new BatchInventoryQueryDTO();
        locationQuery.setWarehouseId(warehouseId);
        locationQuery.setProductSkuIdList(skuIdList);
        // LOGGER.info("仓库[{}], 查询货位顺序参数：{}", warehouseId, JSON.toJSONString(skuIdList));
        List<BatchInventoryDTO> inventorys = iBatchInventoryQueryService.findInventoryLocationBySkuId(locationQuery);
        if (CollectionUtils.isEmpty(inventorys)) {
            // LOGGER.info("仓库[{}],查询货位顺序没有找到批次库存信息！", warehouseId);
            return null;
        }
        // 只要货位数据
        List<BatchInventoryDTO> locationInventorys = inventorys.stream()
            .filter(d -> d.getLocationCategory() != null && d.getLocationCategory() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationInventorys)) {
            // LOGGER.info("仓库[{}],查询货位顺序没有找到货位信息！", warehouseId);
            return null;
        }
        List<LocationSequenceReturnDTO> sequenceReturnDTOS = Lists.transform(locationInventorys, input -> {
            LocationSequenceReturnDTO dto = new LocationSequenceReturnDTO();
            BeanUtils.copyProperties(input, dto);
            dto.setWarehouseId(warehouseId);
            return dto;
        });
        return findOrderLocationSequence(sequenceReturnDTOS);
    }

    /**
     * 未开启货位库存则根据推荐货位获取sequence
     *
     * @param warehouseId
     * @param skuIdList
     * @return
     */
    private List<LocationSequenceReturnDTO> genLocationSequenceByRecommendedCargo(Integer warehouseId,
        List<Long> skuIdList) {
        ProductLocationListSO recommendedCargoQuery = new ProductLocationListSO();
        recommendedCargoQuery.setWarehouseId(warehouseId);
        recommendedCargoQuery.setProductSkuIdList(skuIdList);
        // LOGGER.info("仓库[{}], 查询货位顺序参数：{}", warehouseId, JSON.toJSONString(skuIdList));
        List<ProductLocationListDTO> productLocationListDTOS =
            iProductLocationService.listProductLocationNoPage(recommendedCargoQuery);
        if (CollectionUtils.isEmpty(productLocationListDTOS)) {
            // LOGGER.info("仓库[{}],没有找到推荐货位信息！");
            return null;
        }
        List<LocationSequenceReturnDTO> sequenceReturnDTOS = Lists.transform(productLocationListDTOS, input -> {
            LocationSequenceReturnDTO dto = new LocationSequenceReturnDTO();
            BeanUtils.copyProperties(input, dto);
            dto.setLocationSubcategory(input.getSubcategory());
            dto.setWarehouseId(warehouseId);
            return dto;
        });
        return findOrderLocationSequence(sequenceReturnDTOS);
    }

    /**
     * 按skuId分组并且获取货位顺序
     *
     * @param sequences
     * @return
     */
    private List<LocationSequenceReturnDTO> findOrderLocationSequence(List<LocationSequenceReturnDTO> sequences) {
        /**
         * 1、按 skuId 分组 2、每个 skuId 只获取一个货位sequence： 规则：优先获取分拣货位(24) LocationEnum.分拣位(24), 没有分拣位则随机获取一个
         */
        // LOGGER.info("开始过滤货位顺序：{}", JSON.toJSONString(sequences));
        if (CollectionUtils.isEmpty(sequences)) {
            return null;
        }
        Map<Long, List<LocationSequenceReturnDTO>> skuIdSequences = sequences.stream()
            .filter(d -> d.getProductSkuId() != null).collect(Collectors.groupingBy(d -> d.getProductSkuId()));
        List<LocationSequenceReturnDTO> returnDTOS = Lists.newArrayList();
        for (Map.Entry<Long, List<LocationSequenceReturnDTO>> entry : skuIdSequences.entrySet()) {
            List<LocationSequenceReturnDTO> value = entry.getValue();
            LocationSequenceReturnDTO min =
                value.stream().filter(d -> d.getLocationSubcategory() != null && d.getLocationSequence() != null)
                    .filter(d -> LocationEnum.分拣位.getType().equals(Integer.valueOf(d.getLocationSubcategory())))
                    .min(Comparator.comparing(LocationSequenceReturnDTO::getLocationSequence)).orElse(null);
            if (min != null) {
                returnDTOS.add(min);
            } else {
                Optional<LocationSequenceReturnDTO> any = value.stream().filter(d -> d.getLocationSequence() != null)
                    .min(Comparator.comparing(LocationSequenceReturnDTO::getLocationSequence));
                if (any.isPresent()) {
                    returnDTOS.add(any.get());
                }
            }
        }
        LOGGER.info("返回货位顺序：{}", JSON.toJSONString(returnDTOS));
        return returnDTOS;
    }

    private Byte getLocationByteByString(String locationType) {
        if (locationType == null) {
            return null;
        }
        LocationEnum[] locationEnums = LocationEnum.values();
        for (int i = 0; i < locationEnums.length; i++) {
            if (locationEnums[i].name().equals(locationType.trim())) {
                return locationEnums[i].getType().byteValue();
            }
        }
        return null;
    }

    /**
     * 根据货位名称和仓库查询货位或者货区信息
     *
     * @return
     */
    public List<LocationCategoryDTO> findLocationListByName(LocationCategoryQuery locationCategoryQuery) {
        return locationPOMapper.findLocationListByName(locationCategoryQuery);
    }

    /**
     * 根据货位id查询对应货区下指定货位类型的所有货位信息
     */
    public List<LoactionDTO> findLocationListByIdAndCategory(LocationQueryDTO dto) {
        List<LoactionDTO> result = locationPOMapper.findLocationListByIdAndCategory(dto);
        result.forEach(p -> {
            if (p.getSubcategory() != null) {
                p.setSubcategoryName(LocationEnum.getEnumStr(p.getSubcategory().intValue()));
            }
        });
        return result;
    }

    /**
     * 查询仓库产品货位信息
     *
     * @param query
     * @return
     */
    public List<ProductLocationInfoDTO> findLocationInfoBySkuIds(LocationSequenceQuery query) {
        Assert.notNull(query.getWarehouseId(), "仓库id为空");
        Assert.notEmpty(query.getProductSkuIds(), "产品SkuId为空");
        List<Long> skuIdList =
            query.getProductSkuIds().stream().filter(d -> d != null).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIdList)) {
            return null;
        }
        Boolean openLocationStock = warehouseConfigService.isOpenLocationStock(query.getWarehouseId());
        /*
         *	1.没开启货位库存用推荐货位
         *	2.开启货位库存则查询对应货位顺序
         */
        if (openLocationStock) {
            return genLocationInfoByBatchInventory(query.getWarehouseId(), skuIdList);
        } else {
            return genLocationInfoByRecommendedCargo(query.getWarehouseId(), skuIdList);
        }
    }

    /**
     * 开启货位库存根据已有的货位获取货位信息
     *
     * @param warehouseId
     * @param skuIdList
     * @return
     */
    private List<ProductLocationInfoDTO> genLocationInfoByBatchInventory(Integer warehouseId, List<Long> skuIdList) {
        BatchInventoryQueryDTO locationQuery = new BatchInventoryQueryDTO();
        locationQuery.setWarehouseId(warehouseId);
        locationQuery.setProductSkuIdList(skuIdList);
        List<BatchInventoryDTO> inventorys = iBatchInventoryQueryService.findInventoryLocationBySkuId(locationQuery);
        if (CollectionUtils.isEmpty(inventorys)) {
            return null;
        }
        List<ProductLocationInfoDTO> returnDTOS = Lists.transform(inventorys, input -> {
            ProductLocationInfoDTO dto = new ProductLocationInfoDTO();
            dto.setWarehouseId(warehouseId);
            dto.setLocationId(input.getLocationId());
            dto.setLocationName(input.getLocationName());
            dto.setLocationArea(input.getArea());
            dto.setLocationSequence(input.getLocationSequence());
            dto.setLocationCategory(input.getLocationCategory());
            dto.setLocationSubcategory(input.getLocationSubcategory());
            dto.setProductSkuId(input.getProductSkuId());
            return dto;
        });
        return returnDTOS;
    }

    /**
     * 未开启货位库存则根据产品配置货位获取货位信息
     *
     * @param warehouseId
     * @param skuIdList
     * @return
     */
    private List<ProductLocationInfoDTO> genLocationInfoByRecommendedCargo(Integer warehouseId, List<Long> skuIdList) {
        ProductLocationListSO recommendedCargoQuery = new ProductLocationListSO();
        recommendedCargoQuery.setWarehouseId(warehouseId);
        recommendedCargoQuery.setProductSkuIdList(skuIdList);
        List<ProductLocationListDTO> productLocationListDTOS =
            iProductLocationService.listProductLocationNoPage(recommendedCargoQuery);
        if (CollectionUtils.isEmpty(productLocationListDTOS)) {
            return null;
        }
        List<ProductLocationInfoDTO> returnDTOS = Lists.transform(productLocationListDTOS, input -> {
            ProductLocationInfoDTO dto = new ProductLocationInfoDTO();
            dto.setWarehouseId(warehouseId);
            dto.setLocationId(input.getLocationId());
            dto.setLocationName(input.getLocationName());
            dto.setLocationSequence(input.getLocationSequence());
            dto.setLocationCategory(input.getCategory() == null ? null : input.getCategory().intValue());
            dto.setLocationSubcategory(input.getSubcategory() == null ? null : input.getSubcategory().intValue());
            dto.setProductSkuId(input.getProductSkuId());
            return dto;
        });
        return returnDTOS;
    }

    /**
     * 根据货位/货区名称查询货位/货区信息
     *
     * @return
     */
    public LoactionDTO getLocationByName(Integer warehouseId, String locationName) {
        LocationPO locationPO = locationPOMapper.findLocationByName(locationName, warehouseId);
        if (locationPO == null) {
            return null;
        }
        LoactionDTO loactionDTO = new LoactionDTO();
        BeanUtils.copyProperties(locationPO, loactionDTO);
        loactionDTO.setCityId(locationPO.getCity_Id());
        loactionDTO.setWarehouseId(locationPO.getWarehouse_Id());

        if (loactionDTO.getSubcategory() != null) {
            String subcategoryName = "";
            // 货位
            if (Objects.equals(loactionDTO.getCategory(), CategoryEnum.CARGO_LOCATION.getValue().byteValue())) {
                subcategoryName = LocationEnum.getEnumStr(loactionDTO.getSubcategory().intValue());
            } else if (Objects.equals(loactionDTO.getCategory(), CategoryEnum.CARGO_AREA.getValue().byteValue())) {
                subcategoryName = LocationAreaEnum.getEnumStr(loactionDTO.getSubcategory().intValue());
            }
            loactionDTO.setSubcategoryName(subcategoryName);
        }
        return loactionDTO;
    }

    /**
     * 根据货位/货区名称查询货位/货区信息
     *
     * @return
     */
    public List<LoactionDTO> getLocationByNames(Integer warehouseId, List<String> locationNameList) {
        List<LocationPO> locationPOList = locationPOMapper.findLocationByNames(locationNameList, warehouseId);
        if (CollectionUtils.isEmpty(locationPOList)) {
            return Collections.EMPTY_LIST;
        }
        List<LoactionDTO> loactionDTOList = locationPOList.stream().map(p -> {
            LoactionDTO loactionDTO = new LoactionDTO();
            BeanUtils.copyProperties(p, loactionDTO);
            loactionDTO.setCityId(p.getCity_Id());
            loactionDTO.setWarehouseId(p.getWarehouse_Id());
            return loactionDTO;
        }).collect(Collectors.toList());
        return loactionDTOList;
    }

    /**
     * 根据货位id查询货位信息
     *
     * @param locationIds
     * @return
     */
    public List<LoactionDTO> findLocationByIds(List<Long> locationIds) {
        return locationPOMapper.findLocationByIds(locationIds);
    }

    public List<Long> findLocationAreaIdsByIds(List<Long> locationIds) {
        return locationPOMapper.findLocationAreaIdsByIds(locationIds);
    }

    public LoactionDTO findLocationById(Long locationId) {
        LocationPO locationPO = locationPOMapper.findLocationById(locationId);
        return LocationConvertor.convertToDTO(locationPO);
    }

    /**
     * 分页条件查询货位信息
     */
    public PageList<LoactionDTO> pageListLocation(LocationInfoQueryDTO locationQueryDTO) {
        PageResult<LoactionDTO> pageResult = pageListLocation0(locationQueryDTO);
        List<LoactionDTO> locationDTOS = pageResult.getResult();
        locationDTOS.forEach(location -> {
            if (location.getSubcategory() != null) {
                location.setSubcategoryName(LocationEnum.getEnumStr(location.getSubcategory().intValue()));
            }
        });
        PageList<LoactionDTO> pageList = new PageList<>();
        if (locationQueryDTO.getQueryRelatedProduct() != null && locationQueryDTO.getQueryRelatedProduct()
            && !CollectionUtils.isEmpty(locationDTOS)) {
            List<Long> locationIds = locationDTOS.stream().map(LoactionDTO::getId).collect(Collectors.toList());
            List<ProductLocationPO> productLocationPOS = productLocationPOMapper.findByLocationIds(locationIds);
            Map<Long, List<Long>> productLocationMap =
                productLocationPOS.stream().collect(Collectors.groupingBy(ProductLocationPO::getLocation_Id,
                    Collectors.mapping(ProductLocationPO::getProductSku_Id, Collectors.toList())));
            locationDTOS.forEach(location -> location.setProductSkuIdList(productLocationMap.get(location.getId())));
        }
        // 计算填充货位分仓属性
        calLocationStorageAttribute(locationDTOS);
        pageList.setDataList(locationDTOS);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    public PageList<LoactionDTO> locationRangePageList(LocationRangeQueryDTO rangeQueryDTO) {
        AssertUtils.notNull(rangeQueryDTO, "货位查询查询参数不能为空");
        AssertUtils.notNull(rangeQueryDTO.getWarehouseId(), "仓库ID不能为空");
        // 如果起止货位不为空则查询货位范围
        LocationRangeSequenceDTO rangeSequenceDTO = null;
        if (StringUtils.isNotBlank(rangeQueryDTO.getLocationNameStart())
            || StringUtils.isNotBlank(rangeQueryDTO.getLocationNameEnd())) {
            rangeSequenceDTO = locationPOMapper.findLocationRangeByCondition(rangeQueryDTO);
        }
        LocationInfoQueryDTO queryDTO = new LocationInfoQueryDTO();
        BeanUtils.copyProperties(rangeQueryDTO, queryDTO);
        // 巷道去空
        queryDTO.setRoadway(StringUtils.trimToNull(queryDTO.getRoadway()));
        // 精准查询巷道
        if (StringUtils.isNotBlank(queryDTO.getRoadway()) && StringUtils.isBlank(queryDTO.getExactRoadway())) {
            queryDTO.setExactRoadway(queryDTO.getRoadway());
            // 置空模糊匹配
            queryDTO.setRoadway(null);
        }
        if (rangeSequenceDTO != null) {
            queryDTO.setSequenceStart(rangeSequenceDTO.getMinSequence());
            queryDTO.setSequenceEnd(rangeSequenceDTO.getMaxSequence());
        }
        LOGGER.info("查询货位范围 - locationRangePageList 【参数】 ： {}", JSON.toJSONString(queryDTO));
        return pageListLocation(queryDTO);
    }

    /**
     * 根据名称或者Id，校正错误的货位信息，并返回
     *
     * @return
     */
    public Map<Long, String> findErrorLocation(Integer warehouseId, List<AdjustLoactionInfoDTO> lstLocations) {
        Map<Long, String> mapResult = new HashMap<>(16);

        List<Long> lstErrorLocatioIds =
            lstLocations.stream().filter(p -> StringUtil.isEmpty(p.getName()) && (p.getId() != null))
                .map(p -> p.getId()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lstErrorLocatioIds)) {
            List<LoactionDTO> locationByIds = locationPOMapper.findLocationByIds(lstErrorLocatioIds);
            if (!CollectionUtils.isEmpty(locationByIds)) {
                locationByIds.forEach(p -> {
                    mapResult.put(p.getId(), p.getName());
                });
            }
        }

        List<String> lstErrorLocatioNames = lstLocations.stream()
            .filter(p -> StringUtil.isNotEmpty(p.getName()) && (p.getId() == null || p.getId() < 1000L))
            .map(p -> p.getName()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lstErrorLocatioNames) && warehouseId != null) {
            List<LocationPO> locationByNames = locationPOMapper.selectByNames(lstErrorLocatioNames, warehouseId, null);
            if (!CollectionUtils.isEmpty(locationByNames)) {
                locationByNames.forEach(p -> {
                    mapResult.put(p.getId(), p.getName());
                });
            }
        }
        return mapResult;
    }

    /**
     * 查询货位名称集合
     *
     * @return
     */
    public Map<Long, String> getLocationNameMap(Integer warehouseId, List<Long> locationIdList) {
        if (CollectionUtils.isEmpty(locationIdList)) {
            return Collections.EMPTY_MAP;
        }
        LocationQueryDTO queryDTO = new LocationQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationIdList(locationIdList);
        queryDTO.setCategoryList(Arrays.asList(CategoryEnum.CARGO_LOCATION.getValue().byteValue(),
            CategoryEnum.CARGO_VESSEL.getValue().byteValue()));
        List<LocationPO> locationList = locationPOMapper.listLocationByCondition(queryDTO);
        if (CollectionUtils.isEmpty(locationList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, String> returnMap = new HashMap<>(16);
        locationList.forEach(p -> {
            returnMap.put(p.getId(), p.getName());
        });
        return returnMap;
    }

    public Map<Long, LocationPO> getLocationInfoMap(Integer warehouseId, List<Long> locationIdList) {
        if (CollectionUtils.isEmpty(locationIdList)) {
            return Collections.EMPTY_MAP;
        }
        LocationQueryDTO queryDTO = new LocationQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationIdList(locationIdList);
        queryDTO.setCategoryList(Arrays.asList(CategoryEnum.CARGO_LOCATION.getValue().byteValue(),
            CategoryEnum.CARGO_VESSEL.getValue().byteValue()));
        List<LocationPO> locationList = locationPOMapper.listLocationByCondition(queryDTO);
        if (CollectionUtils.isEmpty(locationList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, LocationPO> returnMap = new HashMap<>(16);
        locationList.forEach(p -> {
            returnMap.put(p.getId(), p);
        });
        return returnMap;
    }

    /**
     * 根据仓库id+类型查询货位或货区，不存在时则自动创建货位
     *
     * @return
     */
    public LocationReturnDTO getLocationByWarehouseIdByAutoCreate(Integer warehouseId, Integer cityId,
        Byte subcategory) {
        // 根据仓库id查询备货区的location信息
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setWarehouseId(warehouseId);
        locationQueryDTO.setSubcategory(subcategory);
        // List<LocationReturnDTO> locationList = iProductLocationService.findLocationList(locationQueryDTO);
        List<LocationReturnDTO> locationList = productLocationBL.findLocationListByCondition(locationQueryDTO);

        if (subcategory != null && CollectionUtils.isEmpty(locationList)) {
            try {
                // 创建货位
                boolean createAreaSuccess = createLocationArea(subcategory, warehouseId, cityId);
                if (createAreaSuccess) {
                    return getLocationByWarehouseIdByAutoCreate(warehouseId, cityId, subcategory);
                }
            } catch (Exception oe) {
                LOGGER.info(
                    String.format("自动创建货区出错！CityId:%s,WarehouseId:%s,SubCategory:%s", cityId, warehouseId, subcategory),
                    oe);
            }
        }
        // 货位排序
        final List<Byte> orderList =
            Arrays.asList((byte)0, (byte)24, (byte)52, (byte)51, (byte)1, (byte)50, (byte)20, (byte)55);
        List<LocationReturnDTO> results = new ArrayList<>();
        if (!CollectionUtils.isEmpty(locationList)) {
            // 转化list
            results.addAll(locationList);
            // List 排序
            Collections.sort(results, (o1, o2) -> {
                int io1 = orderList.indexOf(o1.getSubcategory());
                int io2 = orderList.indexOf(o2.getSubcategory());
                return (io1 == -1 || io2 == -1) ? (io2 - io1) : (io1 - io2);
            });
        }
        boolean isOpenLocationStockGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);
        if (!CollectionUtils.isEmpty(results) && isOpenLocationStockGroup) {
            LocationReturnDTO locationReturn = results.get(0);
            List<LocationReturnDTO> locationReturnDTOS =
                locationAreaService.findLocationAreaListById(Arrays.asList(String.valueOf(locationReturn.getId())));
            // 货位Id值替换成货区Id
            if (!CollectionUtils.isEmpty(locationReturnDTOS)) {
                locationReturn.setId(locationReturnDTOS.get(0).getArea_Id());
            }
        }
        return CollectionUtils.isEmpty(results) ? null : results.get(0);
    }

    private boolean createLocationArea(Byte subcategory, Integer warehouseId, Integer cityId) {
        boolean result = false;
        LocationAreaEnum tmpArea = LocationAreaEnum.getEnum((int)subcategory);
        if (tmpArea != null) {
            String areaCode = tmpArea.name();
            if (StringUtils.isNotEmpty(areaCode)) {
                // 创建货区
                LocationAreaDTO areaDTO = new LocationAreaDTO();
                areaDTO.setWarehouseId(warehouseId);
                areaDTO.setCityId(cityId);
                areaDTO.setArea(areaCode);
                areaDTO.setRemo(String.format("%s-自动创建", tmpArea.name()));
                areaDTO.setSubcategory(subcategory);
                areaDTO.setLocationCapacity(Integer.MAX_VALUE);
                areaDTO.setUserId(1);
                LOGGER.info(String.format("自动创建货区：%s", JSON.toJSONString(areaDTO)));
                locationAreaBL.addLocationArea(areaDTO);
                result = true;
            }
        }
        return result;
    }

    public List<LoactionDTO> findLocationListByAreaId(Long areaId) {
        List<LocationPO> pos = locationPOMapper.listLocationDTO(areaId);
        return LocationConvetor.loactionListPO2DTO(pos);
    }

    public List<LoactionDTO> findLocationListByAreaIds(List<Long> areaIds) {
        List<LocationPO> pos = locationPOMapper.findLocationByAreaIds(areaIds);
        return LocationConvetor.loactionListPO2DTO(pos);
    }

    public void batchInsertLocation(List<LoactionDTO> dtos, Integer warehouseId, Integer cityId) {
        List<LocationPO> pos = LocationConvetor.loactionListDTO2PO(dtos);
        pos.forEach(p -> p.setId(UUIDGenerator.getUUID(LocationPO.class.getName())));
        locationPOMapper.batchInsert(pos, warehouseId, cityId);
    }

    public void updateLocation(LoactionDTO loactionDTO) {
        AssertUtils.notNull(loactionDTO, "参数为空");
        AssertUtils.notNull(loactionDTO.getId(), "参数Id不能为空");
        locationPOMapper.updateSelectiveById(loactionDTO);
    }

    public void deleteByIds(List<Long> ids) {
        locationPOMapper.deleteByPrimaryKeyBatch(ids);
    }

    public PageList<LoactionDTO> listLocationPage(LocationInfoQueryDTO locationInfoQueryDTO) {
        // 默认为货位
        locationInfoQueryDTO.setCategory((byte)0);
        PageResult<LoactionDTO> pageResult = pageListLocation0(locationInfoQueryDTO);
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getResult())) {
            return new PageList<>();
        }
        return pageResult.toPageList();
    }

    public List<LoactionDTO> listLocation(LocationQueryDTO locationQueryDTO) {
        // 默认为货位
        locationQueryDTO.setCategory((byte)0);
        List<LocationPO> locationPOs = locationPOMapper.listLocationByCondition(locationQueryDTO);
        if (CollectionUtils.isEmpty(locationPOs)) {
            return new ArrayList<>();
        }
        return LocationConvetor.loactionListPO2DTO(locationPOs);
    }

    public Map<Long, BigDecimal> getLocationStoreNumByLocationId(List<Long> locationIds) {
        List<LocationStorageNumDTO> locationStorageNumDTOs =
            productStoreBatchPOMapper.countHavingGroupByLocationId(locationIds);
        if (CollectionUtils.isEmpty(locationStorageNumDTOs)) {
            return new HashMap<>(16);
        }
        return locationStorageNumDTOs.stream()
            .collect(Collectors.toMap(LocationStorageNumDTO::getId, LocationStorageNumDTO::getStorageNum));
    }

    /**
     * 是否全量拉取货位信息（PDA）
     */
    public boolean isFullLoadLocation(LocationInfoQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(queryDTO.getLastUpdateTime(), "最后更新时间不能为空");
        // 获取当前仓库最近删除货位操作时间
        String deleteTime = codeGenerator.getLocationDeleteTime(queryDTO.getWarehouseId());
        LOGGER.info("获取仓库最近删除货位时间 warehouseId:{}, deleteTime:{}", queryDTO.getWarehouseId(), deleteTime);
        if (deleteTime == null) {
            return false;
        }
        Date delete = DateUtil.parseDateTime(deleteTime);
        // 最后拉取时间在删除时间之前，需要全量拉取
        if (queryDTO.getLastUpdateTime().before(delete)) {
            return true;
        }
        return false;
    }

    public List<Long> checkLocation(List<LoactionDTO> locationDTOS) {
        List<Long> errLocationIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(locationDTOS)) {
            return errLocationIds;
        }
        List<Long> locationIds = locationDTOS.stream().filter(location -> location.getId() != null)
            .map(LoactionDTO::getId).distinct().collect(Collectors.toList());
        List<LoactionDTO> locationList = locationPOMapper.findLocationByIds(locationIds);

        // 2023-07-20 存在多个仓库的货位时，客户端选择有问题，报错，不能继续处理
        if (CollectionUtils.isEmpty(locationList)
            || locationList.stream().map(LoactionDTO::getWarehouseId).distinct().count() > 1) {
            errLocationIds = locationIds;
        }
        Map<Long, LoactionDTO> locationMap = locationList.stream()
            .collect(Collectors.toMap(LoactionDTO::getId, Function.identity(), (key1, key2) -> key1));
        for (LoactionDTO location : locationDTOS) {
            if (location.getId() == null) {
                continue;
            }
            LoactionDTO loactionDTO = locationMap.get(location.getId());
            if (loactionDTO == null) {
                errLocationIds.add(location.getId());
            } else {
                if (!loactionDTO.getName().equals(location.getName())) {
                    errLocationIds.add(location.getId());
                }
            }
        }
        return errLocationIds;
    }

    /**
     * 分页条件查询暂存位信息并校验播种任务
     */
    public PageList<LoactionDTO> pageListLocationWithoutSowTask(LocationInfoQueryDTO locationQueryDTO) {
        PageResult<LoactionDTO> pageResult = pageListLocation0(locationQueryDTO);
        List<LoactionDTO> locationDTOS = pageResult.getResult();
        locationDTOS.forEach(location -> {
            if (location.getSubcategory() != null) {
                location.setSubcategoryName(LocationEnum.getEnumStr(location.getSubcategory().intValue()));
            }
        });

        // 根据货位id查询播种任务，过滤状态为待播种和播种中的货位
        List<Long> loactionIds = locationDTOS.stream().map(LoactionDTO::getId).collect(Collectors.toList());
        List<Long> disabledLoactionIds = iSowQueryService.findByLocationIds(locationQueryDTO.getCityId(),
            locationQueryDTO.getWarehouseId(), loactionIds);
        LOGGER.info("需过滤货位 disabledLoactionIds:{}", JSON.toJSONString(disabledLoactionIds));
        if (!CollectionUtils.isEmpty(disabledLoactionIds)) {
            locationDTOS.removeIf(d -> disabledLoactionIds.contains(d.getId()));
            pageResult.getPager().setRecordCount(locationDTOS.size());
        }

        PageList<LoactionDTO> pageList = new PageList<>();
        if (locationQueryDTO.getQueryRelatedProduct() != null && locationQueryDTO.getQueryRelatedProduct()
            && !CollectionUtils.isEmpty(locationDTOS)) {
            List<Long> locationIds = locationDTOS.stream().map(LoactionDTO::getId).collect(Collectors.toList());
            List<ProductLocationPO> productLocationPOS = productLocationPOMapper.findByLocationIds(locationIds);
            Map<Long, List<Long>> productLocationMap =
                productLocationPOS.stream().collect(Collectors.groupingBy(ProductLocationPO::getLocation_Id,
                    Collectors.mapping(ProductLocationPO::getProductSku_Id, Collectors.toList())));
            locationDTOS.forEach(location -> location.setProductSkuIdList(productLocationMap.get(location.getId())));
        }
        pageList.setDataList(locationDTOS);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    /**
     * 根据传过来的区域 id 查找对应的货位 根据货位 id 找出该货位下的商品 skuId 根据商品的动销属性(ABC属性)标记该货位为红蓝灰
     */
    public PageList<LocationVisualDTO> listLocationInventoryInfo(LocationQueryDTO locationQueryDTO) {
        LOGGER.info("货位库存查询入参: {}", JSON.toJSONString(locationQueryDTO));
        PageList<LoactionDTO> locationPage = listLocationPage(locationQueryDTO);
        PageList<LocationVisualDTO> pageList = new PageList<>();
        List<LoactionDTO> locationList =
            locationPage.getDataList().stream().filter(p -> Objects.nonNull(p.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationList)) {
            return new PageList<>();
        }
        // 默认不传为库存分布
        final Byte viewMode =
            Optional.ofNullable(locationQueryDTO.getViewMode()).orElse(INVENTORY_DISTRIBUTION.getType());
        Map<String, Set<Long>> resultMap =
            locationProductAttributeBL.getResultMapByViewMode(locationQueryDTO, viewMode);
        // 查询货位关联的物料箱数据
        Map<Long, List<LoactionDTO>> locationVesselMap = listVesselLocation(locationList);
        resultMap.putAll(
            locationProductAttributeBL.getVesselResultMap(locationQueryDTO, locationVesselMap.values(), viewMode));
        List<LocationVisualDTO> result =
            locationList.stream().map(it -> convertToDTO(it, viewMode, resultMap, locationVesselMap.get(it.getId())))
                .collect(Collectors.toList());
        pageList.setDataList(result);
        pageList.setPager(locationPage.getPager());
        return pageList;
    }

    /**
     * 模型转换
     *
     * @param location 货位数据
     * @param viewMode 视图模式
     * @param resultMap 货位、物料箱额外属性
     * @param vesselLocation 物料箱数据, 可为 null
     * @return 转换后的模型
     */
    private LocationVisualDTO convertToDTO(LoactionDTO location, Byte viewMode, Map<String, Set<Long>> resultMap,
        List<LoactionDTO> vesselLocation) {
        // 填充额外信息后的货位数据
        LocationVisualDTO locationVisualDTO =
            locationProductAttributeBL.convertToVisualDTO(viewMode, location, resultMap);
        if (!CollectionUtils.isEmpty(vesselLocation)) {
            // 填充额外信息后的物料箱数据
            List<LocationVisualDTO> vesselLocationVisualList = vesselLocation.stream()
                .map(it -> locationProductAttributeBL.convertToVisualDTO(viewMode, it, resultMap))
                .collect(Collectors.toList());
            if (vesselLocationVisualList.size() > 1) {
                LOGGER.info("查到多个关联的物料箱: {}", JSON.toJSONString(vesselLocationVisualList));
            }
            locationVisualDTO.setVesselLocation(vesselLocationVisualList.get(0));
        }
        return locationVisualDTO;
    }

    /**
     * 分页查找货位关联的物料箱数据
     *
     * @param locationList 货位 数据
     * @return 以货位 id 为 key, 其关联的物料箱为 value 的 map
     */
    private Map<Long, List<LoactionDTO>> listVesselLocation(List<LoactionDTO> locationList) {
        List<Long> ids = locationList.stream().map(LoactionDTO::getId).collect(Collectors.toList());
        List<LocationPO> vesselInfoList = locationPOMapper.selectLocationByAreaIds(ids);
        if (vesselInfoList.isEmpty()) {
            return Collections.emptyMap();
        }
        return locationPOMapper
            .findLocationByIds(vesselInfoList.stream().map(LocationPO::getId).collect(Collectors.toList())).stream()
            .collect(Collectors.groupingBy(LoactionDTO::getAreaId));
    }

    /**
     * 分页查询货位数据
     *
     * @param locationQueryDTO 查询条件
     * @return 分页结果, 永不为 null, dataList 也不会为 null
     */
    public PageList<LoactionDTO> listLocationPage(LocationQueryDTO locationQueryDTO) {
        PageList<LoactionDTO> pageList = new PageList<>();
        pageList.setDataList(Collections.emptyList());
        locationQueryDTO.setAreaIdList(Collections.singletonList(locationQueryDTO.getAreaId()));
        // 默认为货位
        locationQueryDTO.setCategory(CategoryEnum.CARGO_LOCATION.getByteValue());
        PageHelper.startPage(locationQueryDTO.getPageNum(), locationQueryDTO.getPageSize());
        PageResult<LocationPO> locationPageList = locationPOMapper.findLocationPageList(locationQueryDTO);
        if (CollectionUtils.isEmpty(locationPageList)) {
            return pageList;
        }
        pageList.setDataList(LocationConvetor.convertDTOS(locationPageList.getResult()));
        pageList.setPager(locationPageList.getPager());
        return pageList;
    }

    public List<LocationWithAreaDTO> findLocationAndAreaByIds(List<Long> locationIds) {
        List<LoactionDTO> locationList = locationPOMapper.findLocationByIds(locationIds);
        List<Long> areaIds =
            locationList.stream().map(LoactionDTO::getArea_Id).filter(Objects::nonNull).collect(Collectors.toList());

        List<LoactionDTO> areaList = locationPOMapper.findLocationByIds(areaIds);

        return LocationWithAreaDTOConvertor.convertList(locationList, areaList);
    }

    /**
     * 分页查询货位信息
     *
     * @param query 查询条件
     * @return 查询结果
     */
    public PageResult<LoactionDTO> pageListLocation0(LocationInfoQueryDTO query) {
        if (Objects.isNull(query.getPageNum())) {
            query.setPageNum(1);
        }
        if (Objects.isNull(query.getPageSize())) {
            query.setPageSize(Integer.MAX_VALUE);
        }
        return locationPOMapper.pageListLocation(query, query.getPageNum(), query.getPageSize());
    }

    /**
     * 根据货位id查询容器位
     *
     * @param queryDTO
     * @return
     */
    public List<LoactionDTO> findVesselLocationByIds(LocationQueryDTO queryDTO) {
        Assert.notNull(queryDTO, "查询参数为空");
        Assert.notNull(queryDTO.getWarehouseId(), "仓库id为空");
        Assert.notEmpty(queryDTO.getLocationIdList(), "货位ids为空");

        List<LocationPO> locationPOList = new ArrayList<>();

        List<Long> locationIds = queryDTO.getLocationIdList().stream().collect(Collectors.toList());
        Lists.partition(locationIds, 300).forEach(list -> {
            List<LocationPO> locationPOS = locationPOMapper.listVesselByAreaIds(list, queryDTO.getWarehouseId());
            if (!CollectionUtils.isEmpty(locationPOS)) {
                locationPOList.addAll(locationPOS);
            }
        });

        return LocationConvetor.loactionListPO2DTO(locationPOList);
    }

    /**
     * 修复货位和货区不匹配工具
     *
     * @param fixDTOList
     */
    @Transactional
    public void fixLocationAreaInfo(List<FixLocationAreaInfoDTO> fixDTOList) {
        fixDTOList.forEach(fixDTO -> {
            LocationAreaInfoQueryBO queryBO = new LocationAreaInfoQueryBO();
            queryBO.setAreaIds(Collections.singletonList(fixDTO.getOldAreaId()));
            queryBO.setWarehouseId(fixDTO.getWarehouseId());
            queryBO.setCategory(CategoryEnum.CARGO_LOCATION.getByteValue());
            List<LocationPO> locationPOList = locationPOMapper.selectByAreaInfo(queryBO);
            if (!CollectionUtils.isEmpty(locationPOList)) {
                locationPOList.forEach(locationPO -> {
                    LocationPO updateLocation = new LocationPO();
                    updateLocation.setId(locationPO.getId());
                    updateLocation.setArea_Id(fixDTO.getNewAreaId());
                    updateLocation.setArea(fixDTO.getNewAreaName());
                    locationPOMapper.updateByPrimaryKeySelective(updateLocation);
                });
            }
        });

    }

    /**
     * 批量更新货位工具
     *
     * @param modifyDTO
     * @return
     */
    public void updateLocationBatchById(LocationQueryDTO modifyDTO) {
        AssertUtils.notNull(modifyDTO, "参数为空");
        AssertUtils.notEmpty(modifyDTO.getLocationIdList(), "参数Id不能为空");
        locationPOMapper.updateLocationBatchById(modifyDTO);
    }

    /**
     * 货位删除校验
     *
     * <AUTHOR>
     */
    public void checkDeleteLocation(Integer warehouseId, List<Long> locationIds) {
        // 检查是否存在产品关联货位
        Integer ifCanDelete = productLocationPOMapper.countHavingSku(locationIds);
        if (ifCanDelete > 0) {
            throw new BusinessValidateException("有" + ifCanDelete + "个货位与商品存在关联关系,请先删除关联关系后再删除货位!");
        }

        boolean isOpenLocationStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenLocationStock) {
            processNotOpenLocationStock(warehouseId, locationIds);
            return;
        }

        // 开启货位组的，要检查货位组是否有库存
        boolean isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);
        if (isOpenLocationGroup) {
            List<LocationReturnDTO> locationReturnDTOS = locationAreaBL.findLocationAreaListById(
                locationIds.stream().map(String::valueOf).distinct().collect(Collectors.toList()));
            List<Long> areaIds = locationReturnDTOS.stream().filter(p -> p != null && p.getArea_Id() != null).distinct()
                .map(LocationReturnDTO::getArea_Id).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(areaIds)) {
                locationIds.addAll(areaIds);
            }
        }

        if (CollectionUtils.isEmpty(locationIds)) {
            return;
        }

        // 检查是否存在货位库存
        ifCanDelete = productStoreBatchPOMapper.countHavingSku(locationIds);
        if (ifCanDelete > 0) {
            throw new BusinessValidateException("存在货位存放有库存，请先做移库操作再删除");
        }
    }

    /**
     * 未开启货位库存的，如果有用到当前货位的，自动转移库存
     *
     * @param warehouseId
     * @param locationIds
     */
    private void processNotOpenLocationStock(Integer warehouseId, List<Long> locationIds) {
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setLocationIds(locationIds);
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        PageList<BatchInventoryDTO> pageList =
            iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(pageList.getDataList())) {
            LocationAreaListDTO areaListDTO = new LocationAreaListDTO();
            areaListDTO.setWarehouseId(warehouseId);
            List<Byte> lstAreaType =
                Arrays.asList(LocationAreaEnum.存储区.getType().byteValue(), LocationAreaEnum.拣货区.getType().byteValue(),
                    LocationAreaEnum.零拣区.getType().byteValue(), LocationAreaEnum.暂存区.getType().byteValue());
            areaListDTO.setSubcategoryList(lstAreaType);
            List<LocationAreaReturnDTO> lstArea = locationAreaBL.getLocationAreaNoPage(areaListDTO);
            lstArea.removeIf(p -> locationIds.contains(p.getId()));
            if (CollectionUtils.isEmpty(lstArea)) {
                return;
            }
            LocationAreaReturnDTO areaDTO = lstArea.stream().findAny().get();
            // 修改货位
            List<String> lstBatchId =
                pageList.getDataList().stream().map(BatchInventoryDTO::getStoreBatchId).collect(Collectors.toList());
            Lists.partition(lstBatchId, 1000).forEach(ids -> {
                productStoreBatchPOMapper.updateProductStoreBatchLocation(ids, areaDTO.getId(), areaDTO.getName());
            });
        }
    }

    /**
     * 更新货位托盘数量
     *
     * @param updateDTO
     * @return
     */
    public void updatePalletCount(LoactionDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "参数为空");
        AssertUtils.notNull(updateDTO.getId(), "货位Id不能为空");
        AssertUtils.notNull(updateDTO.getPalletCount(), "托盘数量不能为空");
        LocationPO locationPO = locationPOMapper.findLocationById(updateDTO.getId());
        if (locationPO == null) {
            throw new BusinessValidateException("所选货位信息不存在！");
        }
        locationPOMapper.updateSelectiveById(updateDTO);
    }

    /**
     * 批量更新货位托盘数量工具
     *
     * @param updateDTO
     * @return
     */
    public void updatePalletCountBatch(LoactionPalletCountUpdateDTO updateDTO) {
        LOGGER.info("批量更新货位托盘数量 入参" + JSON.toJSONString(updateDTO));
        AssertUtils.notNull(updateDTO, "参数为空");
        AssertUtils.notEmpty(updateDTO.getWarehouseIdList(), "仓库Id不能为空");
        updateDTO.getWarehouseIdList().stream().forEach(warehouseId -> {
            LoactionPalletCountUpdateDTO dto = new LoactionPalletCountUpdateDTO();
            dto.setWarehouseId(warehouseId);
            dto.setPalletCount(updateDTO.getPalletCount());
            asyncLocationBL.updatePalletCountBatch(dto);
        });
    }

    /**
     * 根据线路ID，查询对应出库位对应的货位序号（批量）
     *
     * @param queryDTO
     * @return
     */
    public List<LocationSequenceByRouteInfoResultDTO>
        findLocationSequenceByRouteInfo(LocationSequenceByRouteInfoQueryDTO queryDTO) {
        boolean equation =
            CollectionUtils.isEmpty(queryDTO.getRouteIds()) && CollectionUtils.isEmpty(queryDTO.getAreaIds());
        AssertUtils.isTrue(!equation, "线路片区信息不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");

        Byte conditional = !CollectionUtils.isEmpty(queryDTO.getRouteIds()) ? ConditionStateEnum.是.getType()
            : ConditionStateEnum.否.getType();

        OutLocationQueryDTO outLocationQueryDTO = getRecommendOutLocationQueryDTOList(queryDTO);

        List<LocationRuleDTO> locationRuleDTOList = locationRuleBL.getOutLocation(outLocationQueryDTO);
        if (CollectionUtils.isEmpty(locationRuleDTOList)) {
            return Collections.emptyList();
        }

        List<Long> locationIds = locationRuleDTOList.stream().filter(m -> Objects.nonNull(m.getLocationId()))
            .map(LocationRuleDTO::getLocationId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(locationIds)) {
            return Collections.emptyList();
        }

        List<LoactionDTO> loactionDTOList = findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return Collections.emptyList();
        }

        Map<Long, LoactionDTO> loactionDTOMap =
            loactionDTOList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));

        return locationRuleDTOList.stream().map(locationRuleDTO -> {
            LoactionDTO locationDTO = loactionDTOMap.get(locationRuleDTO.getLocationId());

            LocationSequenceByRouteInfoResultDTO resultDTO = new LocationSequenceByRouteInfoResultDTO();
            resultDTO.setLocationId(locationRuleDTO.getLocationId());
            resultDTO.setLocationName(locationRuleDTO.getLocationName());
            String ruleId = locationRuleDTO.getRuleId();
            if (ConditionStateEnum.是.getType().equals(conditional)) {
                if (StringUtils.isNotBlank(ruleId)) {
                    resultDTO.setRouteId(Long.valueOf(ruleId));
                }
            } else {
                if (StringUtils.isNotBlank(ruleId)) {
                    resultDTO.setAreaId(Long.valueOf(ruleId));
                }
            }
            if (Objects.nonNull(locationDTO)) {
                resultDTO.setSequence(locationDTO.getSequence());
            }
            return resultDTO;
        }).collect(Collectors.toList());

    }

    private OutLocationQueryDTO getRecommendOutLocationQueryDTOList(LocationSequenceByRouteInfoQueryDTO queryDTO) {
        OutLocationQueryDTO outLocationQueryDTO = new OutLocationQueryDTO();
        outLocationQueryDTO.setWarehouseId(queryDTO.getWarehouseId());

        if (!CollectionUtils.isEmpty(queryDTO.getRouteIds())) {
            outLocationQueryDTO
                .setRuleIdList(queryDTO.getRouteIds().stream().map(String::valueOf).collect(Collectors.toList()));
            outLocationQueryDTO.setCode(LocationRuleEnum.LINE.getCode());
        }

        if (!CollectionUtils.isEmpty(queryDTO.getAreaIds())) {
            outLocationQueryDTO
                .setRuleIdList(queryDTO.getAreaIds().stream().map(String::valueOf).collect(Collectors.toList()));

            outLocationQueryDTO.setCode(LocationRuleEnum.DISTRICT.getCode());
        }

        return outLocationQueryDTO;
    }

    public void calLocationStorageAttribute(List<LoactionDTO> locationDTOS) {
        if (CollectionUtils.isEmpty(locationDTOS)) {
            return;
        }

        LOGGER.info("计算填充货位分仓属性 入参：{}", JSON.toJSONString(locationDTOS));
        Integer warehouseId = locationDTOS.stream().findFirst().get().getWarehouseId();

        List<String> ids = locationDTOS.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toList());
        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setWarehouseId(warehouseId);
        passageItemSO.setRelateIdList(ids);
        passageItemSO.setState(YesOrNoEnum.YES.getValue().byteValue());
        List<PassageDTO> passageDTOS = passageBL.listPassageByRelate(passageItemSO);
        LOGGER.info("计算填充货位分仓属性通道信息：{}", JSON.toJSONString(passageDTOS));

        Map<Long, PassageDTO> passageMap =
            passageDTOS.stream().filter(p -> p != null).collect(Collectors.toMap(PassageDTO::getId, p -> p));
        Map<String, Long> passageItemMap = passageDTOS.stream()
            .filter(p -> p != null && !CollectionUtils.isEmpty(p.getItemList())).flatMap(p -> p.getItemList().stream())
            .collect(Collectors.toMap(p -> p.getRelateId(), p -> p.getPassageId(), (v1, v2) -> v1));

        // 没开分仓, 直接返回
        boolean enabledWarehouseSplit = iWarehouseAllocationConfigService.isEnabledWarehouseSplit(warehouseId);
        Map<Byte, Set<Long>> typeAreaMap;
        Map<Byte, Set<Long>> typePassageMap;
        // 没开分仓，不需要查询分仓数据，只查询货位关联数据
        if (enabledWarehouseSplit) {
            List<WarehouseAllocationConfigDTO> configs =
                iWarehouseAllocationConfigService.getConfigByWarehouseId(warehouseId);
            typeAreaMap = configs.stream()
                .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToAreaId()));
            typePassageMap = configs.stream()
                .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToPassageId()));
        } else {
            typeAreaMap = new HashMap<>();
            typePassageMap = new HashMap<>();
        }

        locationDTOS.forEach(p -> {
            Long passageId = passageItemMap.getOrDefault(p.getId().toString(), null);
            if (passageId != null) {
                p.setPassageId(passageId);
                p.setPassageName(passageMap.getOrDefault(passageId, new PassageDTO()).getPassageName());
            }

            // 计算分仓属性
            // 1、开启分仓配置，优先判断货位所属货区对应分仓类型，其次判断货位所属通道对应分仓类型
            Byte type = null;
            if (enabledWarehouseSplit) {
                type = matchConfigType(typeAreaMap, p.getAreaId());
                if (type == null) {
                    type = matchConfigType(typePassageMap, p.getPassageId());
                }
            }

            // 2、没开启分仓配置 或 分仓配置匹配结果为空，则直接判断货位类型
            if (Objects.isNull(type)) {
                if (Objects.equals(p.getSubcategory(), LocationEnum.零拣位.getType().byteValue())) {
                    type = ProductConfigStorageAttributeEnum.REST.getValue();
                } else if (Objects.equals(p.getSubcategory(), LocationEnum.分拣位.getType().byteValue())) {
                    type = ProductConfigStorageAttributeEnum.DRINKING.getValue();
                }
            }

            if (Objects.nonNull(type)) {
                p.setStorageAttribute(type.intValue());
            }
        });

        LOGGER.info("计算填充货位分仓属性 结果：{}", JSON.toJSONString(locationDTOS));
    }

    private Byte matchConfigType(Map<Byte, Set<Long>> configMap, Long targetId) {
        if (configMap.isEmpty() || targetId == null) {
            return null;
        }
        for (Map.Entry<Byte, Set<Long>> entry : configMap.entrySet()) {
            Byte configType = entry.getKey();
            Set<Long> configIds = entry.getValue();
            if (CollectionUtils.isEmpty(configIds)) {
                continue;
            }
            if (configIds.contains(targetId)) {
                return configType;
            }
        }
        return null;
    }

    /**
     * 提取出货区 id
     */
    private Collector<WarehouseAllocationConfigDTO, ?, Set<Long>> mappingToAreaId() {
        return StreamUtils.flatMapping(
            it -> it.getItems().stream().filter(item -> LOCATION_AREA.valueEquals(item.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId).filter(NumberUtils::isDigits).map(Long::valueOf),
            Collectors.toSet());
    }

    /**
     * 提取出通道 id
     */
    private Collector<WarehouseAllocationConfigDTO, ?, Set<Long>> mappingToPassageId() {
        return StreamUtils.flatMapping(
            it -> it.getItems().stream().filter(item -> WORKING_PASSAGE.valueEquals(item.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId).filter(NumberUtils::isDigits).map(Long::valueOf),
            Collectors.toSet());
    }
}
