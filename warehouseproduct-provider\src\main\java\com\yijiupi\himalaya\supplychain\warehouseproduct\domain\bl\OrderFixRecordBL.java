package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OrderFixRecordPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderfixrecord.OrderFixRecordDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrderFixRecordBL {

    @Autowired
    private OrderFixRecordPOMapper orderFixRecordPOMapper;

    public void batchSave(List<OrderFixRecordDTO> orderFixRecordDTOS) {
        orderFixRecordPOMapper.batchInsert(orderFixRecordDTOS);
    }

    public List<OrderFixRecordDTO> findAll() {
        return orderFixRecordPOMapper.findAll();
    }
}
