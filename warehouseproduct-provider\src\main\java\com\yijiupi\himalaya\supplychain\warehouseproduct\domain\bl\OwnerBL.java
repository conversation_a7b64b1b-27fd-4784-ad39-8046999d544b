package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.OwnerConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OwnerMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.BatchImportOwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.BusinessCodeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PartnertStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CommonUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ValidationUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.excel.ExcelUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 货主
 */
@Service
public class OwnerBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(OwnerBL.class);
    private static final List<Integer> MERGE_SUPPLIERS = Arrays.asList(OwnerTypeConst.微酒, OwnerTypeConst.易款连锁);

    /**
     * 获取灰度仓库的KEY
     */
    private static final String DEFAULT_OWNER_NAME = "DefaultOwnerName";

    @Autowired
    private OwnerMapper ownerMapper;

    @Reference
    private IOrgService iOrgService;

    @Reference
    private IVariableValueService variableValueService;

    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    public String getDefaultOwnerName() {
        String ownerName = "";
        VariableValueQueryDTO valueQuery = new VariableValueQueryDTO();
        valueQuery.setVariableKey(DEFAULT_OWNER_NAME);
        valueQuery.setWarehouseId(1);
        VariableDefAndValueDTO value = variableValueService.detailVariable(valueQuery);
        if (value != null) {
            ownerName = value.getVariableData();
        }
        return ownerName;
    }

    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    public List<OwnerDTO> getOwnerInfoDTO(OwnerQueryDTO queryDTO) {
        List<OwnerDTO> lstPO = ownerMapper.getOwnerInfoDTO(queryDTO);
        return lstPO;
    }

    public List<OwnerDTO> getOwnerIdByErpOwnerId(List<String> erpOwnerId) {
        List<OwnerDTO> lstPO = ownerMapper.getOwnerIdByErpOwnerId(erpOwnerId);
        return lstPO;
    }

    /**
     * 新增或修改货主(易经商)
     */
    public void insertOrUpdateOwnerByEasyBusiness(List<OwnerDTO> dtoList) {
        AssertUtils.notEmpty(dtoList, "新增或修改货主参数不能为空");
        LOGGER.info("(易经商)新增或修改货主参数:{}", JSON.toJSONString(dtoList));
        ownerMapper.insertOrUpdateBatch(OwnerConvertor.converToOwnerPOList(dtoList));
    }

    /**
     * 新增或修改货主
     */
    public void insertOrUpdateOwner(List<OwnerDTO> dtoList) {
        AssertUtils.notEmpty(dtoList, "新增或修改货主参数不能为空");
        List<OwnerPO> poList = OwnerConvertor.converToOwnerPOList(dtoList);
        LOGGER.info("新增或修改货主参数old:{}", JSON.toJSONString(poList));
        ownerMapper.insertOrUpdateBatch(poList);
    }

    /**
     * 新增或修改货主(关联外部货主ID)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertOrUpdateOwnerByRefPartner(List<OwnerDTO> dtoList) {
        AssertUtils.notEmpty(dtoList, "新增或修改货主参数不能为空");
        LOGGER.info("新增或修改货主参数:{}", JSON.toJSONString(dtoList));
        List<OwnerPO> ownerPOs = OwnerConvertor.converToOwnerPOList(dtoList);
        List<OwnerPO> insertOwnerPOs = new ArrayList<>(); // 新增集合
        List<OwnerPO> updateOwnerPOs = new ArrayList<>(); // 修改集合

        List<String> refPartnerIds = new ArrayList<>();
        List<OwnerPO> refOwnerPOs = new ArrayList<>();
        ownerPOs.forEach(ownerPO -> {
            if (null != ownerPO.getId()) {
                updateOwnerPOs.add(ownerPO);
                return;
            }
            if (null != ownerPO.getRefPartnerId()) {
                refPartnerIds.add(ownerPO.getRefPartnerId());
                refOwnerPOs.add(ownerPO);
            } else {
                insertOwnerPOs.add(ownerPO);
            }
        });

        // 根据外部关联id 找到对应的数据
        if (!CollectionUtils.isEmpty(refPartnerIds)) {
            List<OwnerPO> exitedOwnPOs = ownerMapper.listOwnerByRefPartnerIds(refPartnerIds, OwnerTypeConst.供应商);
            if (!CollectionUtils.isEmpty(exitedOwnPOs)) {
                Map<String, List<OwnerPO>> map =
                    exitedOwnPOs.stream().collect(Collectors.groupingBy(p -> p.getRefPartnerId()));
                refOwnerPOs.forEach(refOwnerPO -> {
                    List<OwnerPO> tempList = map.get(refOwnerPO.getRefPartnerId());
                    if (!CollectionUtils.isEmpty(tempList)) {
                        tempList.forEach(temp -> {
                            OwnerPO newOwnerPO = new OwnerPO();
                            BeanUtils.copyProperties(refOwnerPO, newOwnerPO);
                            newOwnerPO.setId(temp.getId());
                            updateOwnerPOs.add(newOwnerPO);
                        });
                    } else {
                        insertOwnerPOs.add(refOwnerPO);
                    }
                });
            } else {
                insertOwnerPOs.addAll(refOwnerPOs);
            }
        }

        if (!CollectionUtils.isEmpty(insertOwnerPOs)) {
            insertOwnerPOs.forEach(owner -> {
                owner.setId(UUIDGenerator.getUUID(owner.getClass().getName()));
            });
            ownerMapper.batchInsert(insertOwnerPOs);
        }
        if (!CollectionUtils.isEmpty(updateOwnerPOs)) {
            updateOwnerPOs.forEach(updateOwnerPO -> ownerMapper.updateByPrimaryKeySelective(updateOwnerPO));
        }
    }

    /**
     * 更新货主状态
     */
    public void updateStateOwner(Long id, Byte state) {
        AssertUtils.notNull(id, "id不能为空");
        AssertUtils.notNull(state, "状态不能为空");
        OwnerPO ownerPO = new OwnerPO();
        ownerPO.setId(id);
        ownerPO.setState(state);
        ownerMapper.updateByPrimaryKeySelective(ownerPO);
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    public List<OwnerDTO> findOwnerByOrgId(Integer orgId) {
        List<OwnerDTO> lstResult = new ArrayList<>();
        List<OwnerPO> lstPO = ownerMapper.findOwnerByOrgId(orgId);
        if (!CollectionUtils.isEmpty(lstPO)) {
            lstPO.forEach(p -> {
                OwnerDTO dto = new OwnerDTO();
                BeanUtils.copyProperties(p, dto);
                lstResult.add(dto);
            });
        }
        return lstResult;
    }

    /**
     * 根据ERP供应商，查找供应链货主ID
     *
     * @param refPartnerIds
     * @param ownerType OwnerTypeConst.供应商
     * @return
     */
    public List<OwnerDTO> findOwnerByErpId(List<String> refPartnerIds, Integer ownerType) {
        List<OwnerDTO> lstResult = new ArrayList<>();
        List<OwnerPO> lstPO = ownerMapper.listOwnerByRefPartnerIds(refPartnerIds, ownerType);
        if (!CollectionUtils.isEmpty(lstPO)) {
            lstPO.forEach(p -> {
                OwnerDTO dto = new OwnerDTO();
                BeanUtils.copyProperties(p, dto);
                lstResult.add(dto);
            });
        }
        return lstResult;
    }

    /**
     * 根据仓库查询所有有货的货主
     *
     * @param warehouseId
     * @return
     */
    public List<OwnerDTO> findOwnerByWarehouseId(Integer warehouseId, Integer ownerType) {
        List<OwnerDTO> lstResult = new ArrayList<>();
        List<OwnerPO> lstPO = ownerMapper.findOwnerByWarehouseId(warehouseId, ownerType);
        if (!CollectionUtils.isEmpty(lstPO)) {
            lstPO.forEach(p -> {
                OwnerDTO dto = new OwnerDTO();
                BeanUtils.copyProperties(p, dto);
                lstResult.add(dto);
            });
        }
        return lstResult;
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    public List<OwnerDTO> findOwnerByOrgIdAndType(Integer orgId, Integer ownerType) {
        List<Integer> ownerTypeList = new ArrayList<>();
        ownerTypeList.add(ownerType);
        if (ownerType == OwnerTypeConst.供应商) {
            ownerTypeList.addAll(MERGE_SUPPLIERS);
        }

        return findOwnerByOrgIdAndTypeList(orgId, ownerTypeList);
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    public List<OwnerDTO> findOwnerByOrgIdAndTypeList(Integer orgId, List<Integer> ownerTypeList) {
        List<OwnerDTO> lstResult = new ArrayList<>();
        List<OwnerPO> lstPO = ownerMapper.findOwnerByOrgIdAndTypeList(orgId, ownerTypeList);
        if (!CollectionUtils.isEmpty(lstPO)) {
            lstPO.forEach(p -> {
                OwnerDTO dto = new OwnerDTO();
                BeanUtils.copyProperties(p, dto);
                lstResult.add(dto);
            });
        }
        return lstResult;
    }

    /**
     * 根据货主id获取货主信息
     *
     * @return
     */
    public OwnerDTO getOwnerById(Long id) {
        AssertUtils.notNull(id, "货主id不能为空");
        OwnerPO ownerPO = ownerMapper.selectByPrimaryKey(id);
        if (null == ownerPO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        BeanUtils.copyProperties(ownerPO, ownerDTO);
        return ownerDTO;
    }

    /**
     * 根据货主id批量获取货主信息
     *
     * @return
     */
    public List<OwnerDTO> listOwnerByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "货主ids不能为空");
        List<OwnerPO> ownerPOList = ownerMapper.listOwnerByIds(ids);
        if (CollectionUtils.isEmpty(ownerPOList)) {
            return Collections.EMPTY_LIST;
        }
        List<OwnerDTO> ownerDTOList = ownerPOList.stream().map(po -> {
            OwnerDTO ownerDTO = new OwnerDTO();
            BeanUtils.copyProperties(po, ownerDTO);
            return ownerDTO;
        }).collect(Collectors.toList());
        return ownerDTOList;
    }

    /**
     * 更新货主状态
     *
     * @param id
     * @param status
     */
    public void updateStatus(String id, Byte status, Long userId) {
        ownerMapper.updateStatus(Long.valueOf(id), status, userId);
    }

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    public PageList<OwnerDTO> findOwnerByCondition(OwnerQueryDTO dto) {
        // 兼容处理，将OwnerType=10 将微酒和易久连锁类型包含进去
        List<Integer> ownTypeList = setParamifSupplier(dto);
        dto.setOwnerTypes(ownTypeList);
        PageResult<OwnerDTO> list = ownerMapper.findOwnerByCondition(dto);
        return list.toPageList();
    }

    /**
     *
     * 如果查询供应商类型，则兼容处理微酒和易久连锁
     *
     * @param dto
     * @return
     */
    private List<Integer> setParamifSupplier(OwnerQueryDTO dto) {
        Integer ownType = dto.getOwnerType();
        List<Integer> ownTypeList = dto.getOwnerTypes() == null ? new ArrayList<>() : dto.getOwnerTypes();

        if (!ownTypeList.isEmpty()) {
            for (Integer ownTypeConstant : ownTypeList) {
                if (ownTypeConstant.equals(OwnerTypeConst.供应商)) {
                    ownTypeList.addAll(MERGE_SUPPLIERS);
                }
            }
        }

        if (null != ownType && ownType.equals(OwnerTypeConst.供应商)) {
            ownTypeList.add(ownType);
            ownTypeList.addAll(MERGE_SUPPLIERS);
            dto.setOwnerType(null);
        }
        return ownTypeList;
    }

    /**
     * 新增或更新业务伙伴
     *
     * @param dto
     * @return
     */
    public OwnerDTO insertOrUpdateOwnerManager(OwnerDTO dto) {
        // 判断是否存在，存在则修改，不存在则新增
        OwnerPO po = new OwnerPO();
        dto.setLastUpdateTime(new Date());
        if (dto.getId() != null) {
            BeanUtils.copyProperties(dto, po);
            ownerMapper.updateByPrimaryKeySelective(po);
            return dto;
        }
        dto.setCreateTime(new Date());
        dto.setId(UUIDUtils.randonUUID());
        BeanUtils.copyProperties(dto, po);
        ownerMapper.insertSelective(po);
        return dto;
    }

    /**
     * 校验编号唯一性业务伙伴
     *
     * @param ownerNo
     * @param id
     */
    public boolean checkOwnerNoUniq(String ownerNo, String id) {
        int count = ownerMapper.checkOwnerNoUniq(ownerNo, id);
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 批量更新业务伙伴
     */
    public void insertBatchOnwer(List<OwnerDTO> list) {
        HashSet hashSet = new HashSet();
        for (OwnerDTO dto : list) {
            if (StringUtils.isEmpty(dto.getOwnerNo())) {
                dto.setOwnerNo(CommonUtils.getFirstSpell(dto.getOwnerName()));
            }
            validator(dto, hashSet);
            dto.setId(UUIDUtils.randonUUID());
        }
        validatorNo(new ArrayList<>(hashSet));
        ownerMapper.insertOrUpdateBatch(OwnerConvertor.converToOwnerPOList(list));
    }

    /**
     * 校验数据正确性
     *
     * @param dto
     * @param hashSet
     */
    private void validator(OwnerDTO dto, HashSet hashSet) {
        if (hashSet.contains(dto.getOwnerNo())) {
            throw new BusinessException(dto.getOwnerName() + BusinessCodeEnum.DUPLICATE_CODE.getText(),
                BusinessCodeEnum.DUPLICATE_CODE.getCode());
        } else {
            hashSet.add(dto.getOwnerNo());
        }

        // boolean checkOwnerNo = checkOwnerNoUniq(dto.getOwnerNo(), null);
        // if (!checkOwnerNo) {
        // throw new BusinessException(dto.getOwnerNo() + BusinessCodeEnum.DUPLICATE_CODE.getText(),
        // BusinessCodeEnum.DUPLICATE_CODE.getCode());
        // }

        if (!StringUtils.isEmpty(dto.getMobileNo())) {
            boolean checkPhone = ValidationUtil.validationPhone(dto.getMobileNo());
            if (!checkPhone) {
                throw new BusinessException(dto.getOwnerName() + BusinessCodeEnum.ERROR_PHONE_CODE.getText(),
                    BusinessCodeEnum.ERROR_PHONE_CODE.getCode());
            }
        }

    }

    private void validatorNo(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<OwnerDTO> owners = ownerMapper.findOwnerByNo(list);
        if (!owners.isEmpty()) {
            throw new BusinessException(owners.get(0).getOwnerNo() + BusinessCodeEnum.DUPLICATE_CODE.getText(),
                BusinessCodeEnum.DUPLICATE_CODE.getCode());
        }
    }

    /**
     * 获取货主实体列表
     *
     * @param ownerNos
     * @return
     */
    public Map<String, OwnerDTO> findOwnerByOwnerNos(List<String> ownerNos) {
        if (null == ownerNos || ownerNos.isEmpty()) {
            throw new BusinessException("ownerNos参数为空", BusinessCodeEnum.NULL_ENTITY.getCode());
        }
        return ownerMapper.findOwnerByOwnerNoIn(ownerNos);
    }

    public List<OwnerDTO> listOwnerByCondition(OwnerQueryDTO dto) {
        dto.setOwnerTypes(setParamifSupplier(dto));
        List<OwnerPO> ownerPOs = ownerMapper.listOwnerByCondition(dto);
        return OwnerConvertor.converToOwnerDTOList(ownerPOs);
    }

    /**
     * 根据外部关联id或货主id查询我们自己的货主ID
     *
     * @return
     */
    public Map<String, Long> getOwnerIdMap(List<String> ownerIds) {
        AssertUtils.notEmpty(ownerIds, "货主ID不能为空");
        Map<String, Long> resultMap = new HashMap<>(16);

        // 根据外部关联id或货主id获取批量获取指定货主信息
        List<OwnerPO> ownerPOList = ownerMapper.listOwnerByRefPartnerIdsOrOwnerIds(ownerIds, OwnerTypeConst.供应商);
        ownerIds.forEach(ownerId -> {
            Optional<OwnerPO> optional = ownerPOList.stream()
                .filter(
                    p -> Objects.equals(p.getId().toString(), ownerId) || Objects.equals(p.getRefPartnerId(), ownerId))
                .findFirst();
            if (optional.isPresent()) {
                resultMap.put(ownerId, optional.get().getId());
            }
        });
        return resultMap;
    }

    /**
     * 根据货主id获取货主名称
     *
     * @return
     */
    public Map<Long, String> getOwnerNameMap(List<Long> ids) {
        AssertUtils.notEmpty(ids, "货主ids不能为空");
        List<OwnerPO> ownerPOList = ownerMapper.listOwnerByIds(ids);
        if (CollectionUtils.isEmpty(ownerPOList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, String> ownerNameMap = new HashMap<>(16);
        ownerPOList.forEach(p -> {
            ownerNameMap.put(p.getId(), p.getOwnerName());
        });
        return ownerNameMap;
    }

    /**
     * 根据仓库查询所有产品的货主
     *
     * @param warehouseId
     * @return
     */
    public List<OwnerDTO> findOwnerByWarehouseIdWithSKU(Integer warehouseId, Integer ownerType) {
        List<OwnerDTO> lstResult = new ArrayList<>();
        List<OwnerPO> lstPO = ownerMapper.findOwnerByWarehouseIdWithSKU(warehouseId, ownerType);
        if (!CollectionUtils.isEmpty(lstPO)) {
            lstPO.forEach(p -> {
                OwnerDTO dto = new OwnerDTO();
                BeanUtils.copyProperties(p, dto);
                lstResult.add(dto);
            });
        }
        return lstResult;
    }

    /**
     * 根据条件查询业务伙伴
     *
     * @param dto
     * @return
     */
    public PageList<OwnerDTO> findOwnerListByCondition(OwnerQueryDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        List<Integer> cityIdList = new ArrayList<>();
        // 如果是顶级组织则查本身以及下级，如果是下级组织只查自己
        PageList<OrgDTO> orgDTOPageList = iOrgService.listSelfAndSubById(dto.getCityId());
        LOGGER.info("findOwnerListByCondition 下级以及本组织：" + JSON.toJSONString(orgDTOPageList));
        if (orgDTOPageList != null) {
            if (!CollectionUtils.isEmpty(orgDTOPageList.getDataList())) {
                orgDTOPageList.getDataList().stream().forEach(orgDTO -> {
                    cityIdList.add(orgDTO.getId());
                });
            }
        }

        Integer parentOrgId = iOrgService.findParentOrgBy(dto.getCityId());
        if (!CollectionUtils.isEmpty(cityIdList)) {
            dto.setCityId(null);
        } else {
            throw new BusinessException("城市id不存在");
        }
        cityIdList.add(parentOrgId);
        Set<Integer> citySet = new HashSet<>();
        citySet.addAll(cityIdList);
        dto.setCityIdList(new ArrayList<>(citySet));
        /** 默认查询启用状态 */
        if (dto.getState() == null) {
            dto.setState(OwnerStateEnum.启用.getType());
        }

        // 兼容处理，将OwnerType=10 将微酒和易久连锁类型包含进去
        List<Integer> ownTypeList = setParamifSupplier(dto);
        dto.setOwnerTypes(ownTypeList);
        PageResult<OwnerDTO> list = ownerMapper.findOwnerByCondition(dto);
        return list.toPageList();
    }

    /**
     * 校验编号唯一性业务伙伴
     *
     * @param dto
     */
    public boolean checkOwnerNoUniqueness(OwnerDTO dto) {
        int count =
            ownerMapper.checkOwnerNoUniq(dto.getOwnerNo(), null != dto.getId() ? String.valueOf(dto.getId()) : "");
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 批量导入
     *
     * @param dto
     */
    public void productOwnerUpload(BatchImportOwnerDTO dto) {
        try {
            List<OwnerDTO> list =
                ExcelUtils.readExcelOnwer(dto.getFile().getInputStream(), dto.getFile().getOriginalFilename());
            AssertUtils.notEmpty(list, "不能导入空的excel文件");
            list.forEach(it -> {
                it.setCityId(dto.getCityId());
                it.setState(new Byte(PartnertStateEnum.启用.getType()));
                it.setCreateTime(new Date());
                it.setLastUpdateTime(new Date());
            });

            insertBatchOnwer(list);
        } catch (Exception e) {
            LOGGER.error("批量导入货主失败", e);
        }
    }
}
