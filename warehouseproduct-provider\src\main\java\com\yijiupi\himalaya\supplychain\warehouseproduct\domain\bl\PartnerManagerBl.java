package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.PartnerConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PartnerManagerMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PartnerManagerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.BusinessCodeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CommonUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.OrgQueryUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ValidationUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-08-20
 */
@Service
public class PartnerManagerBl {
    @Autowired(required = false)
    private PartnerManagerMapper partnerManagerMapper;

    @Autowired
    private OrgQueryUtil orgQueryUtil;

    /**
     * 根据id获取业务伙伴详情
     *
     * @param id
     * @return
     */
    public PartnerManagerDTO getPartnerManagerDetailById(String id) {
        PartnerManagerPO managerPO = partnerManagerMapper.getPartnerManagerDetailById(id);
        PartnerManagerDTO dto = PartnerConvertor.converToDTO(managerPO);
        return dto;
    }

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    public PageList<PartnerManagerDTO> findPartnerManagerByCondition(PartnerManagerDTO dto) {
        PageList<PartnerManagerDTO> pageList = new PageList<>();
        PageResult<PartnerManagerDTO> list = partnerManagerMapper.findPartnerManagerByCondition(dto);
        pageList.setDataList(list);
        pageList.setPager(list.getPager());
        return pageList;
    }

    /**
     * 新增或更新业务伙伴
     *
     * @param dto
     * @return
     */
    public PartnerManagerDTO insertOrUpdatePartnerManager(PartnerManagerDTO dto) {
        // 判断是否存在，存在则修改，不存在则新增
        if (checkIsExists(dto)) {
            partnerManagerMapper.update(dto);
            return dto;
        }
        if (dto.getId() == null) {
            dto.setId(UUIDUtils.randonUUID());
        }
        partnerManagerMapper.insert(dto);
        return dto;
    }

    private Boolean checkIsExists(PartnerManagerDTO dto) {
        if (dto.getId() == null) {
            return false;
        }
        PartnerManagerPO partnerManagerDetailById =
            partnerManagerMapper.getPartnerManagerDetailById(dto.getId().toString());
        if (partnerManagerDetailById == null) {
            return false;
        }
        return true;
    }

    public void insert(PartnerManagerDTO dto) {
        if (dto.getId() == null) {
            dto.setId(UUIDUtils.randonUUID());
        }
        partnerManagerMapper.insert(dto);
    }

    public void update(PartnerManagerDTO dto) {
        partnerManagerMapper.update(dto);
    }

    /**
     * 更新业务伙伴状态
     *
     * @param id
     * @param status
     */
    public void updateStatus(String id, Byte status, Long userId) {
        partnerManagerMapper.updateStatus(Long.valueOf(id), status, userId);
    }

    /**
     * 批量更新业务伙伴
     */
    public void insertBatchPartnerManager(List<PartnerManagerDTO> list) {
        HashSet hashSet = new HashSet();
        for (PartnerManagerDTO dto : list) {
            if (StringUtils.isEmpty(dto.getPartnerNo())) {
                dto.setPartnerNo(CommonUtils.getFirstSpell(dto.getPartnerName()));
            }
            validator(dto, hashSet);
            dto.setId(UUIDUtils.randonUUID());
        }
        validatorNo(new ArrayList<>(hashSet));
        partnerManagerMapper.insertBatch(list);
    }

    /**
     * 校验编号唯一性业务伙伴
     *
     * @param partnerNo
     * @param id
     */
    public boolean checkPartnerManagerNoUniq(String partnerNo, String id) {
        int count = partnerManagerMapper.checkPartnerManagerNoUniq(partnerNo, id);
        if (count > 0) {
            return false;
        }
        return true;
    }

    /**
     * 校验数据正确性
     *
     * @param dto
     * @param hashSet
     */
    private void validator(PartnerManagerDTO dto, HashSet hashSet) {
        if (hashSet.contains(dto.getPartnerNo())) {
            throw new BusinessException(dto.getPartnerName() + BusinessCodeEnum.DUPLICATE_CODE.getText(),
                BusinessCodeEnum.DUPLICATE_CODE.getCode());
        } else {
            hashSet.add(dto.getPartnerNo());
        }

        if (!StringUtils.isEmpty(dto.getPhone())) {
            boolean checkPhone = ValidationUtil.validationPhone(dto.getPhone());
            if (!checkPhone) {
                throw new BusinessException(dto.getPartnerName() + BusinessCodeEnum.ERROR_PHONE_CODE.getText(),
                    BusinessCodeEnum.ERROR_PHONE_CODE.getCode());
            }
        }
    }

    private void validatorNo(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<PartnerManagerDTO> partners = partnerManagerMapper.findPartnerByNo(list);
        if (partners.size() > 0) {
            throw new BusinessException(partners.get(0).getPartnerNo() + BusinessCodeEnum.DUPLICATE_CODE.getText(),
                BusinessCodeEnum.DUPLICATE_CODE.getCode());
        }
    }

    /**
     * 根据合作商编码列表获取合作商实体集合
     *
     * @param partnerNos
     * @return
     */
    public Map<String, PartnerManagerDTO> findPartnerByPartnerNos(List<String> partnerNos) {
        if (null == partnerNos || partnerNos.isEmpty()) {
            throw new BusinessException("partnerNos参数为空", BusinessCodeEnum.NULL_ENTITY.getCode());
        }
        return partnerManagerMapper.findPartnerByPartnerNoIn(partnerNos);
    }

    /**
     * 根据idlist获取贸易伙伴
     * 
     * @param ids
     * @return
     */
    public List<PartnerManagerDTO> listPartnerByIds(List<Long> ids) {
        return partnerManagerMapper.listPartnerByIds(ids);
    }

    /**
     * 保存外部的业务伙伴（外部的供应商）
     *
     * @param dto
     * @return
     */
    public void saveRefPartnerManager(PartnerManagerDTO dto) {
        AssertUtils.notNull(dto, "供应商信息不能为空");
        AssertUtils.notNull(dto.getRefPartnerId(), "外部供应商id不能为空");
        PartnerManagerPO partnerManagerPO = partnerManagerMapper.getPartnerManagerDetailByRefId(dto.getRefPartnerId());
        // 判断是否存在，存在则修改，不存在则新增
        if (partnerManagerPO == null) {
            dto.setId(UUIDUtils.randonUUID());
            partnerManagerMapper.insert(dto);
        } else {
            dto.setId(partnerManagerPO.getId());
            partnerManagerMapper.update(dto);
        }
    }

    /**
     * 根据外部的供应商Id查供应商信息
     * 
     * @return
     */
    public Map<String, PartnerManagerDTO> getParterMapByRefId(List<String> refParterId) {
        AssertUtils.notEmpty(refParterId, "外部的供应商Id不能为空");
        List<PartnerManagerPO> partnerManagerPOList = partnerManagerMapper.listPartnerManagerDetailByRefId(refParterId);
        if (CollectionUtils.isEmpty(partnerManagerPOList)) {
            return Collections.EMPTY_MAP;
        }
        Map<String, PartnerManagerDTO> resultMap = new HashMap<>(16);
        partnerManagerPOList.forEach(p -> {
            PartnerManagerDTO dto = new PartnerManagerDTO();
            BeanUtils.copyProperties(p, dto);
            resultMap.put(p.getRefPartnerId(), dto);
        });
        return resultMap;
    }

    public PageList<PartnerManagerDTO> listPartner(PartnerManagerDTO dto) {
        List<Integer> cityIdList = orgQueryUtil.findSelfAndSubOrg(dto.getCityId());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(cityIdList)) {
            dto.setCityId(null);
        } else {
            throw new BusinessException("城市id不能为空");
        }
        dto.setCityIdList(cityIdList);
        if (dto.getStatus() == null) {
            dto.setStatus(Byte.valueOf("1"));
        }
        return findPartnerManagerByCondition(dto);
    }
}
