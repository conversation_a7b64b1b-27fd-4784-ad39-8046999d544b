package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.PassageConverter;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageItemMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.VesselInfoPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.passage.EnablePassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 通道
 *
 * <AUTHOR>
 * @date 2018/8/8 16:08
 */
@Service
public class PassageBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(PassageBL.class);

    @Autowired
    private PassageMapper passageMapper;
    @Autowired
    private PassageItemMapper passageItemMapper;
    @Autowired
    private LocationBL locationBL;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private NotifyWCSBL notifyWCSBL;
    @Autowired
    private PassageItemBL passageItemBL;
    @Autowired
    private VesselInfoPOMapper vesselInfoPOMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    /**
     * 获取通道列表
     *
     * @param so
     * @return
     */
    public PageList<PassageDTO> listPassage(PassageSO so) {
        AssertUtils.notNull(so, "参数不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        PageResult<PassagePO> poPageResult = new PageResult<>();
        List<PassagePO> passagePOList = new ArrayList<>();
        if (so.getQueryCondition() == QueryConditionsEnum.查询全部.getType()) {
            int pageCount = 1;
            so.setPageSize(3000);
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                so.setPageNum(pageNum);
                poPageResult = passageMapper.listPassageAllByCondition(so);
                if (pageNum == 1) {
                    pageCount = poPageResult.getPager().getTotalPage();
                }
                passagePOList.addAll(poPageResult.getResult());
            }
        } else {
            poPageResult = passageMapper.listPassageByCondition(so);
        }
        List<PassageDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(passagePOList)) {
            dtoList = PassageConverter.POS2DTOS(poPageResult.getResult());
        } else {
            dtoList = PassageConverter.POS2DTOS(passagePOList);
        }
        // 封装分页列表对象
        PageList<PassageDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(poPageResult.toPageList().getPager());
        dtoPageList.setDataList(dtoList);
        return dtoPageList;
    }

    /**
     * 查看通道详情
     *
     * @param passageId
     * @return
     */
    public PassageDTO getPassage(Long passageId) {
        AssertUtils.notNull(passageId, "通道id不能为空");
        PassageDTO passageDTO = new PassageDTO();
        // 获取通道
        PassagePO passagePO = passageMapper.selectByPrimaryKey(passageId);
        BeanUtils.copyProperties(passagePO, passageDTO);
        // 获取通道详情
        List<PassageItemPO> passageItemPOList = passageItemMapper.listPassageItemByPassageId(passageId);
        List<PassageItemDTO> itemList =
            passageItemPOList.stream().filter(m -> PassageRelateTypeEnum.集货位.getType() != m.getRelateType()).map(po -> {
                PassageItemDTO dto = new PassageItemDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());

        List<PassageItemDTO> collectLocationList =
            passageItemPOList.stream().filter(m -> PassageRelateTypeEnum.集货位.getType() == m.getRelateType()).map(po -> {
                PassageItemDTO dto = new PassageItemDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());
        passageDTO.setItemList(itemList);
        passageDTO.setCollectLocationList(collectLocationList);
        return passageDTO;

    }

    /**
     * 新增通道
     *
     * @param passageDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void savePassage(PassageDTO passageDTO) {
        AssertUtils.notNull(passageDTO, "新增通道参数不能为空");
        AssertUtils.notNull(passageDTO.getPassageName(), "通道名称不能为空");
        AssertUtils.notNull(passageDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(passageDTO.getPassageType(), "通道类型不能为空");
        AssertUtils.notNull(passageDTO.getPickingType(), "拣货方式不能为空");
        AssertUtils.notNull(passageDTO.getOperateUser(), "操作人不能为空");

        // 1、判断通道类型是否不一致
        List<Byte> passageTypeList = passageMapper.listPassageTypeByWarehouseId(passageDTO.getWarehouseId());
        if (CollectionUtils.isNotEmpty(passageTypeList)) {
            if (passageTypeList.stream().noneMatch(e -> e.equals(passageDTO.getPassageType()))) {
                throw new BusinessValidateException("只能配置同一种通道类型！");
            }
        }

        // 2、填充通道明细项
        fillPassageItem(passageDTO);

        // 3、检查通道子项是否重复
        validatePassageItem(passageDTO);

        // 4、新增通道
        PassagePO passagePO = new PassagePO();
        BeanUtils.copyProperties(passageDTO, passagePO);
        passagePO.setCreateUser(passageDTO.getOperateUser());
        passagePO.setId(UUIDGenerator.getUUID(PassagePO.class.getName()));
        if (Objects.isNull(passageDTO.getState())) {
            passagePO.setState(ConditionStateEnum.否.getType());
        }
        passageMapper.insert(passagePO);

        // 5、新增通道详情
        List<PassageItemPO> passageItemPOS = savePassageItem(passageDTO, passagePO.getId());

        // 通道调整的货位重新计算分仓属性
        updateStorageAttribute(passageDTO.getWarehouseId(), null, null, passageItemPOS);

        LOGGER.info("新增通道 passagePO：{}", JSON.toJSONString(passagePO));
    }

    /**
     * 新增通道详情
     */
    private List<PassageItemPO> savePassageItem(PassageDTO passageDTO, Long id) {
        if (CollectionUtils.isEmpty(passageDTO.getItemList())) {
            return Collections.emptyList();
        }
        LOGGER.info("通道id ： {} ; 保存通道明细信息:  item : {}", passageDTO.getId(),
            JSON.toJSONString(passageDTO.getItemList()));

        List<PassageItemPO> passageItemPOList = passageDTO.getItemList().stream().map(dto -> {
            PassageItemPO po = new PassageItemPO();
            BeanUtils.copyProperties(dto, po);
            po.setWarehouseId(passageDTO.getWarehouseId());
            po.setPassageId(id);
            po.setId(UUIDGenerator.getUUID(PassageItemPO.class.getName()));
            return po;
        }).collect(Collectors.toList());

        // TODO 需要校验一下
        if (CollectionUtils.isNotEmpty(passageDTO.getCollectLocationList())) {
            List<PassageItemPO> collectLocationList = passageDTO.getCollectLocationList().stream().map(dto -> {
                PassageItemPO po = new PassageItemPO();
                BeanUtils.copyProperties(dto, po);
                po.setWarehouseId(passageDTO.getWarehouseId());
                po.setPassageId(id);
                po.setId(UUIDGenerator.getUUID(PassageItemPO.class.getName()));
                po.setRelateType(PassageRelateTypeEnum.集货位.getType());
                return po;
            }).collect(Collectors.toList());

            passageItemPOList.addAll(collectLocationList);
        }

        if (CollectionUtils.isNotEmpty(passageItemPOList)) {
            passageItemMapper.insertBatch(passageItemPOList);
        }

        return passageItemPOList;
    }

    /**
     * 填充通道明细
     *
     * @param passageDTO
     */
    private void fillPassageItem(PassageDTO passageDTO) {
        if (CollectionUtils.isEmpty(passageDTO.getItemList())) {
            return;
        }
        // 货位类型通道才需要填充
        if (Objects.equals(passageDTO.getPassageType(), PassageTypeEnum.货位.getType())
            || Objects.equals(passageDTO.getPassageType(), PassageTypeEnum.播种.getType())) {
            // 1、如果是货位，则将货位的名称填充上
            fillPassageItemByLocation(passageDTO);
            // 2、如果是货区，则将货区下的所有货位填充上
            fillPassageItemByArea(passageDTO);
        }
    }

    /**
     * 根据货位ID填充货位名称
     */
    private void fillPassageItemByLocation(PassageDTO passageDTO) {
        // 通道详情关联项类型不是货区
        List<PassageItemDTO> locationItemList = passageDTO.getItemList().stream()
            .filter(p -> !Objects.equals(p.getRelateType(), PassageRelateTypeEnum.货区.getType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationItemList)) {
            return;
        }
        List<Long> locationIdList = locationItemList.stream().filter(p -> StringUtils.isNotEmpty(p.getRelateId()))
            .map(p -> Long.valueOf(p.getRelateId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIdList)) {
            return;
        }
        // 查询货位名称集合
        Map<Long, LocationPO> locationInfoMap =
            locationBL.getLocationInfoMap(passageDTO.getWarehouseId(), locationIdList);

        // 填充货位名称
        locationItemList.forEach(p -> {
            if (StringUtils.isEmpty(p.getRelateId())) {
                return;
            }
            LocationPO locationPO = locationInfoMap.get(Long.valueOf(p.getRelateId()));
            if (Objects.isNull(locationPO)) {
                return;
            }
            if (StringUtils.isEmpty(p.getRelateName())) {
                p.setRelateName(locationPO.getName());
            }
            if (Objects.isNull(p.getRelateType())) {
                p.setRelateType(PassageRelateTypeEnum.货位.getType());
            }
        });
        locationItemList.removeIf(p -> StringUtils.isEmpty(p.getRelateId()) || StringUtils.isEmpty(p.getRelateName()));
    }

    /**
     * 将货区下的所有货位填充上
     */
    private void fillPassageItemByArea(PassageDTO passageDTO) {
        List<PassageItemDTO> passageItemList = passageDTO.getItemList();
        // 通道详情关联项类型是货区
        List<PassageItemDTO> areaItemList =
            passageItemList.stream().filter(p -> Objects.equals(p.getRelateType(), PassageRelateTypeEnum.货区.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaItemList)) {
            return;
        }
        List<Long> areaIdList = areaItemList.stream().filter(p -> StringUtils.isNotEmpty(p.getRelateId()))
            .map(p -> Long.valueOf(p.getRelateId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaIdList)) {
            return;
        }
        // 根据货区ID查询货位
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setWarehouseId(passageDTO.getWarehouseId());
        locationQueryDTO.setAreaIdList(areaIdList);
        List<LocationReturnDTO> locationReturnDTOS = productLocationBL.listLocationByCondition(locationQueryDTO);
        // 填充货区下的货位
        if (CollectionUtils.isNotEmpty(locationReturnDTOS)) {
            locationReturnDTOS.forEach(p -> {
                if (p == null || p.getId() == null || StringUtils.isEmpty(p.getName())) {
                    return;
                }
                PassageItemDTO passageItemDTO = new PassageItemDTO();
                passageItemDTO.setRelateId(p.getId() != null ? p.getId().toString() : null);
                passageItemDTO.setRelateName(p.getName());
                passageItemDTO.setRelateType(PassageRelateTypeEnum.货位.getType());
                passageItemList.add(passageItemDTO);
            });
        }
        // 移除货区
        passageItemList.removeAll(areaItemList);
    }

    /**
     * 修改通道
     *
     * @param passageDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePassage(PassageDTO passageDTO) {
        AssertUtils.notNull(passageDTO, "新增通道参数不能为空");
        AssertUtils.notNull(passageDTO.getId(), "通道ID不能为空");
        AssertUtils.notNull(passageDTO.getPassageName(), "通道名称不能为空");
        AssertUtils.notNull(passageDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(passageDTO.getPickingType(), "拣货方式不能为空");
        AssertUtils.notNull(passageDTO.getOperateUser(), "操作人不能为空");

        // 1、填充通道明细项
        fillPassageItem(passageDTO);

        // 2、检查通道子项是否重复
        validatePassageItem(passageDTO);

        // 开启了机器人的，填充物料箱信息
        fillVesselInfo(passageDTO);

        PassagePO oriPassageDTO = passageMapper.selectByPrimaryKey(passageDTO.getId());
        List<PassageItemPO> oriPassageItemPOList = passageItemMapper.listPassageItemByPassageId(passageDTO.getId());

        // 3、修改通道
        PassagePO passagePO = new PassagePO();
        BeanUtils.copyProperties(passageDTO, passagePO);
        passagePO.setLastUpdateUser(passageDTO.getOperateUser());
        passageMapper.updateByPrimaryKey(passagePO);

        // 4、先删除原来关联的通道详情，再保存新的通道详情
        passageItemMapper.deleteByPassageId(passageDTO.getId());
        List<PassageItemPO> passageItemPOS = savePassageItem(passageDTO, passageDTO.getId());

        notifyWCSBL.notifyWCSByPassage(passageDTO, oriPassageDTO, oriPassageItemPOList);

        // 通道调整的货位重新计算分仓属性
        updateStorageAttribute(passageDTO.getWarehouseId(), null, oriPassageItemPOList, passageItemPOS);

        LOGGER.info("修改通道 passagePO：{}", JSON.toJSONString(passagePO));
    }

    private void fillVesselInfo(PassageDTO passageDTO) {
        Boolean isRobotPicking = warehouseConfigService.checkWarehouseIsRobotPicking(passageDTO.getWarehouseId());
        if (BooleanUtils.isFalse(isRobotPicking)) {
            return;
        }

        if (!PassageRobotPickEnum.开启.getType().equals(passageDTO.getIsRobotPick())) {
            return;
        }

        if (CollectionUtils.isEmpty(passageDTO.getItemList())) {
            return;
        }

        List<Long> locationIds = passageDTO.getItemList().stream().map(PassageItemDTO::getRelateId).map(Long::valueOf)
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIds)) {
            return;
        }

        List<LocationPO> vesselLocationList =
            locationPOMapper.listVesselByAreaIds(locationIds, passageDTO.getWarehouseId());

        LOGGER.info("通道id ： {} ; 查询物料箱信息:  vesselInfoList : {}", passageDTO.getId(),
            JSON.toJSONString(vesselLocationList));
        if (CollectionUtils.isEmpty(vesselLocationList)) {
            return;
        }

        List<PassageItemDTO> passageItemDTOList = vesselLocationList.stream().map(m -> {
            PassageItemDTO passageItemDTO = new PassageItemDTO();
            passageItemDTO.setRelateId(m.getId().toString());
            passageItemDTO.setRelateName(m.getName());
            passageItemDTO.setRelateType(PassageRelateTypeEnum.货位.getType());

            return passageItemDTO;
        }).collect(Collectors.toList());

        LOGGER.info("通道变更添加的物料箱信息为:{}", JSON.toJSONString(passageItemDTOList));

        passageDTO.getItemList().addAll(passageItemDTOList);
    }

    /**
     * 检查通道子项是否重复
     *
     * @param passageDTO
     */
    private void validatePassageItem(PassageDTO passageDTO) {
        if (CollectionUtils.isNotEmpty(passageDTO.getItemList())) {
            int pageCount = 1;
            passageDTO.setPageSize(3000);
            List<PassageItemPO> passageItemPOList = new ArrayList<>();
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                passageDTO.setPageNum(pageNum);
                PageResult<PassageItemPO> pageResult = passageItemMapper.pageListPassageItem(passageDTO);
                List<PassageItemPO> result = pageResult.getResult();
                if (pageNum == 1) {
                    pageCount = pageResult.getPager().getTotalPage();
                }
                if (!CollectionUtils.isEmpty(result)) {
                    passageItemPOList.addAll(result);
                }
            }
            // 根据仓库id查询已配置的所有通道子项
            List<PassageItemDTO> repeatList = new ArrayList<>();
            passageDTO.getItemList().forEach(item -> {
                // 遍历去判断已配置的通道子项列表中是否包括该子项
                if (passageItemPOList.stream()
                    .anyMatch(po -> po.getRelateType().equals(item.getRelateType())
                        && po.getRelateId().equals(item.getRelateId())
                        && po.getRelateName().equals(item.getRelateName()))) {
                    repeatList.add(item);
                }
            });
            // 存在重复子项
            if (CollectionUtils.isNotEmpty(repeatList)) {
                List<String> relateName =
                    repeatList.stream().map(PassageItemDTO::getRelateName).collect(Collectors.toList());
                throw new BusinessValidateException(JSON.toJSONString(relateName) + "在其他通道已配置，不能重复配置！");
            }
        }
    }

    /**
     * 删除通道
     *
     * @param passageId
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void removePassage(Long passageId) {
        AssertUtils.notNull(passageId, "通道id不能为空");

        List<PassageItemPO> origPassageItemPOList = passageItemMapper.listPassageItemByPassageId(passageId);
        PassageItemPO itemPO =
            origPassageItemPOList.stream().filter(p -> p != null).findFirst().orElse(new PassageItemPO());
        // 删除通道
        passageMapper.deleteByPrimaryKey(passageId);
        // 删除通道详情
        passageItemMapper.deleteByPassageId(passageId);
        // 通道移除对应货位重新计算分仓属性
        updateStorageAttribute(itemPO.getWarehouseId(), null, origPassageItemPOList, null);
    }

    /**
     * 根据子项id获取通道配置
     *
     * @param passageItemSO
     * @return
     */
    public List<PassageDTO> listPassageByRelate(PassageItemSO passageItemSO) {
        // TODO 为什么要查询出这么多数据：是否可以把整仓的数据做本地缓存定期刷新，在内存中做筛选
        PassageItemScrollSO passageItemScroll = new PassageItemScrollSO();
        passageItemScroll.setWarehouseId(passageItemSO.getWarehouseId());
        passageItemScroll.setRelateType(passageItemSO.getRelateType());
        passageItemScroll.setRelateIdList(passageItemSO.getRelateIdList());
        passageItemScroll.setPassageType(passageItemSO.getPassageType());
        passageItemScroll.setPassageId(passageItemSO.getPassageId());
        passageItemScroll.setPassageIdList(passageItemSO.getPassageIdList());
        passageItemScroll.setState(passageItemSO.getState());
        passageItemScroll.setLastMaxId(0L);
        passageItemScroll.setBatchCount(3000);
        List<PassagePO> totalResult = new ArrayList<>();
        for (; ; ) {
            List<PassagePO> batchResult = passageMapper.pageListPassageItem(passageItemScroll);
            totalResult.addAll(batchResult);
            if (batchResult.size() != passageItemScroll.getBatchCount()) {
                break;
            }
            // TODO 这里是不是直接返回扁平的PassageItem，分批查询破坏了Passage.PassageItemList的结构
            OptionalLong maxItemId = batchResult.stream()
                    .flatMap(passage -> passage.getPassageItemPOS().stream())
                    .mapToLong(PassageItemPO::getPassageId)
                    .max();
            if (maxItemId.isPresent()) {
                passageItemScroll.setLastMaxId(maxItemId.getAsLong());
            } else {
                break;
            }
        }
        return PassageConverter.POS2DTOS(totalResult);
    }

    /**
     * 根据仓库id查询通道类型
     *
     * @param id
     * @return
     */
    public Byte getPassageTypeByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return passageMapper.selectPassageTypeByWarehouseId(warehouseId);
    }

    /**
     * 查找通道下的明细
     *
     * @param passageItemSO
     * @return
     */
    public List<PassageItemDTO> findPassageItem(PassageItemSO passageItemSO) {
        List<PassageItemPO> itemList = passageItemMapper.listPassageItemByRelate(passageItemSO);
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        return PassageConverter.itemPOS2ItemDTOS(itemList);
    }

    private void updateStorageAttribute(Integer warehouseId, String operateUser,
        List<PassageItemPO> oldPassageItemPOList, List<PassageItemPO> passageItemPOS) {
        if (CollectionUtils.isEmpty(oldPassageItemPOList) && CollectionUtils.isEmpty(passageItemPOS)) {
            return;
        }

        // 根据新老通道数据货位差异重新计算分仓属性
        List<Long> oldLocationIds =
            CollectionUtils.isEmpty(oldPassageItemPOList) ? Collections.emptyList()
                : oldPassageItemPOList.stream()
                    .filter(p -> p != null && !StringUtils.isEmpty(p.getRelateId())
                        && StringUtils.isNumeric(p.getRelateId())
                        && (Objects.equals(p.getRelateType(), PassageRelateTypeEnum.货位.getType())
                            || Objects.equals(p.getRelateType(), PassageRelateTypeEnum.集货位.getType())))
                    .map(p -> Long.valueOf(p.getRelateId())).distinct().collect(Collectors.toList());
        List<Long> newLocationIds = CollectionUtils.isEmpty(passageItemPOS) ? Collections.emptyList() : passageItemPOS
            .stream()
            .filter(p -> p != null && !StringUtils.isEmpty(p.getRelateId()) && StringUtils.isNumeric(p.getRelateId())
                && (Objects.equals(p.getRelateType(), PassageRelateTypeEnum.货位.getType())
                    || Objects.equals(p.getRelateType(), PassageRelateTypeEnum.集货位.getType())))
            .map(p -> Long.valueOf(p.getRelateId())).distinct().collect(Collectors.toList());

        List<Long> differenceLocationIds = org.apache.commons.collections4.CollectionUtils
            .disjunction(oldLocationIds, newLocationIds).stream().collect(Collectors.toList());

        // 更新产品配置分仓属性
        productLocationBL.updateStorageAttributeByLocationIds(null, warehouseId, differenceLocationIds);
    }

    /**
     * 启用通道
     *
     * @param enablePassageDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void enablePassage(EnablePassageDTO enablePassageDTO) {
        PassagePO passagePO = passageMapper.selectByPrimaryKey(enablePassageDTO.getPassageId());
        if (Objects.isNull(passagePO)) {
            throw new BusinessValidateException("通道信息不存在！");
        }

        PassagePO updatePassagePO = new PassagePO();
        updatePassagePO.setId(enablePassageDTO.getPassageId());
        updatePassagePO.setLastUpdateUser(enablePassageDTO.getOptUserId().toString());
        updatePassagePO.setState(ConditionStateEnum.是.getType());
        passageMapper.updateByPrimaryKey(updatePassagePO);
    }

    /**
     * 停用通道
     *
     * @param enablePassageDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void disablePassage(EnablePassageDTO enablePassageDTO) {
        PassagePO passagePO = passageMapper.selectByPrimaryKey(enablePassageDTO.getPassageId());
        if (Objects.isNull(passagePO)) {
            throw new BusinessValidateException("通道信息不存在！");
        }

        PassagePO updatePassagePO = new PassagePO();
        updatePassagePO.setId(enablePassageDTO.getPassageId());
        updatePassagePO.setLastUpdateUser(enablePassageDTO.getOptUserId().toString());
        updatePassagePO.setState(ConditionStateEnum.否.getType());
        passageMapper.updateByPrimaryKey(updatePassagePO);
    }
}
