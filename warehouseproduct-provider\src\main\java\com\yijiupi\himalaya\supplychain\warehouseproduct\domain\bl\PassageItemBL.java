package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageItemMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.PageHelperUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Service
public class PassageItemBL {

    @Autowired
    private PassageItemMapper passageItemMapper;

    private static final Integer LIMIT_SIZE = 3000;

    public List<PassageItemPO> listPassageItemByPassageId(Long passageId) {
        return PageHelperUtils.pageQuery(() -> passageItemMapper.listPassageItemByPassageId(passageId), LIMIT_SIZE);
    }

}
