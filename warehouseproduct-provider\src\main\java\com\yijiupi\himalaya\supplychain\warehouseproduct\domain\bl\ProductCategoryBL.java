package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductCategoryMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCategoryPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品类目信息
 *
 * <AUTHOR>
 * @date 2018/9/11 14:12
 */
@Service
public class ProductCategoryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCategoryBL.class);

    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    /**
     * 根据id 查询类目信息
     *
     * @param id
     * @return
     */
    public ProductCategoryDTO getProductCategoryById(Long id) {
        ProductCategoryPO productCategoryPO = productCategoryMapper.getProductCategoryById(id);
        if (Objects.isNull(productCategoryPO)) {
            return null;
        }
        ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
        productCategoryDTO.setId(productCategoryPO.getId());
        productCategoryDTO.setRefCategoryId(productCategoryPO.getRefCategoryId());
        productCategoryDTO.setName(productCategoryPO.getName());
        return productCategoryDTO;
    }

    /**
     * 根据规格id 批量查询类目信息
     *
     * @param lstSpecId
     * @return
     */
    public List<ProductCategoryDTO> getProductCategoryBySpecId(List<Long> lstSpecId) {
        if (CollectionUtils.isEmpty(lstSpecId)) {
            return null;
        }
        return productCategoryMapper.getProductCategoryBySpecId(lstSpecId);
    }

    /**
     * 根据skuIds查询类目日期配置，返回根据sku聚合
     * @param skuIds
     * @return
     */
    public List<ProductCategoryDTO> getCategoryPeriodBySkuIds(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }

        LOGGER.info("根据skuIds查询类目日期配置 入参：{}", JSON.toJSONString(skuIds));
        List<ProductCategoryDTO> skuCategoryPeriodList = productCategoryMapper.listCategoryPeriodBySkuIds(skuIds);
        if (CollectionUtils.isEmpty(skuCategoryPeriodList)) {
            return Collections.EMPTY_LIST;
        }

        List<ProductCategoryDTO> productCategoryDTOList = new ArrayList<>();
        Map<Long, List<ProductCategoryDTO>> skuMap = skuCategoryPeriodList.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
        skuMap.forEach((skuId, categoryList) -> {
            ProductCategoryDTO statisticsClass = categoryList.stream().filter(p -> p.getParentId() == null).findFirst().orElse(null);
            ProductCategoryDTO secondStatisticsClass = categoryList.stream().filter(p -> p.getParentId() != null).findFirst().orElse(null);
            if(Objects.isNull(statisticsClass) && Objects.isNull(secondStatisticsClass)){
                return;
            }

            ProductCategoryDTO skuCategoryDTO = new ProductCategoryDTO();
            skuCategoryDTO.setProductSkuId(skuId);
            // 优先取二级类目配置
            if(Objects.nonNull(secondStatisticsClass)){
                skuCategoryDTO.setAttentionPeriod(secondStatisticsClass.getAttentionPeriod());
                skuCategoryDTO.setNearExpiryPeriod(secondStatisticsClass.getNearExpiryPeriod());
                skuCategoryDTO.setForbidSalesPeriod(secondStatisticsClass.getForbidSalesPeriod());
                skuCategoryDTO.setUnsalablePeriod(secondStatisticsClass.getUnsalablePeriod());
            }

            if(Objects.nonNull(statisticsClass)){
                if(!StringUtils.hasText(skuCategoryDTO.getAttentionPeriod())){
                    skuCategoryDTO.setAttentionPeriod(statisticsClass.getAttentionPeriod());
                }
                if(!StringUtils.hasText(skuCategoryDTO.getNearExpiryPeriod())){
                    skuCategoryDTO.setNearExpiryPeriod(statisticsClass.getNearExpiryPeriod());
                }
                if(!StringUtils.hasText(skuCategoryDTO.getForbidSalesPeriod())){
                    skuCategoryDTO.setForbidSalesPeriod(statisticsClass.getForbidSalesPeriod());
                }
                if(!StringUtils.hasText(skuCategoryDTO.getUnsalablePeriod())){
                    skuCategoryDTO.setUnsalablePeriod(statisticsClass.getUnsalablePeriod());
                }
            }

            productCategoryDTOList.add(skuCategoryDTO);
        });

        LOGGER.info("根据skuIds查询类目日期配置 结果：{}", JSON.toJSONString(productCategoryDTOList));
        return productCategoryDTOList;
    }
}
