package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductControlConfigConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigItemPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductImageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控货策略
 *
 * <AUTHOR>
 * @date 2019-10-12 10:52
 */
@Service
public class ProductControlConfigBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductControlConfigBL.class);

    @Autowired
    private ProductControlConfigPOMapper productControlConfigPOMapper;

    @Autowired
    private ProductControlConfigItemPOMapper productControlConfigItemPOMapper;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    /**
     * 控货策略列表
     * 
     * @return
     */
    public PageList<ProductControlConfigDTO> listProductControlConfig(ProductControlConfigSO productControlConfigSO) {
        AssertUtils.notNull(productControlConfigSO, "参数不能为空");
        AssertUtils.notNull(productControlConfigSO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(productControlConfigSO.getWarehouseId(), "仓库ID不能为空");

        // 1、获取控货策略列表
        PageResult<ProductControlConfigDTO> pageResult =
            productControlConfigPOMapper.listProductControlConfig(productControlConfigSO);

        // 2、获取控货策略明细
        if (pageResult != null && pageResult.toPageList() != null
            && CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            List<Long> configIds =
                pageResult.toPageList().getDataList().stream().map(p -> p.getId()).collect(Collectors.toList());
            // 根据控货策略ID查询控货策略明细
            Map<Long, List<ProductControlConfigItemDTO>> itemMap = getConfigItemMap(configIds);
            if (itemMap != null) {
                pageResult.toPageList().getDataList().forEach(p -> {
                    p.setItemList(itemMap.get(p.getId()));
                    // 控货策略拼接
                    p.setConfigItemName(getConfigItemName(p.getItemList()));
                });
            }
            return pageResult.toPageList();
        } else {
            PageList<ProductControlConfigDTO> pageList = new PageList<>();
            pageList.setDataList(new ArrayList<>());
            pageList.setPager(new Pager(1, 20, 0));
            return pageList;
        }
    }

    /**
     * 根据控货策略ID查询控货策略明细
     * 
     * @return
     */
    private Map<Long, List<ProductControlConfigItemDTO>> getConfigItemMap(List<Long> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return null;
        }
        List<ProductControlConfigItemPO> itemPOList =
            productControlConfigItemPOMapper.listProductControlConfigItemByConfigId(configIds);
        if (CollectionUtils.isEmpty(itemPOList)) {
            return null;
        }
        // 按控货策略ID分组
        List<ProductControlConfigItemDTO> itemDTOList = itemPOList.stream()
            .map(ProductControlConfigConvertor::convertToProductControlConfigItemDTO).collect(Collectors.toList());
        Map<Long, List<ProductControlConfigItemDTO>> itemMap =
            itemDTOList.stream().collect(Collectors.groupingBy(p -> p.getConfigId()));
        return itemMap;
    }

    /**
     * 拼接控货策略明细 例：【武汉市-洪山区】商超客户、【武汉市-江夏区】餐饮客户
     * 
     * @return
     */
    private String getConfigItemName(List<ProductControlConfigItemDTO> itemDTOS) {
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        itemDTOS.forEach(p -> {
            if (p == null) {
                return;
            }
            // 市、区、渠道任意一个不为空时
            if (StringUtils.isNotEmpty(p.getCity()) || StringUtils.isNotEmpty(p.getCounty())
                || StringUtils.isNotEmpty(p.getChannel())) {
                sb.append("、");
                if (StringUtils.isNotEmpty(p.getCity()) || StringUtils.isNotEmpty(p.getCounty())) {
                    sb.append("【");
                    if (StringUtils.isNotEmpty(p.getCity())) {
                        sb.append(p.getCity() != null ? p.getCity() : "");
                        if (StringUtils.isNotEmpty(p.getCounty())) {
                            sb.append("-").append(p.getCounty() != null ? p.getCounty() : "");
                        }
                    } else {
                        sb.append(p.getCounty() != null ? p.getCounty() : "");
                    }
                    sb.append("】");
                }
                if (StringUtils.isNotEmpty(p.getChannel())) {
                    sb.append(p.getChannel() != null ? p.getChannel() : "");
                }
            }
        });
        return sb.length() > 0 ? sb.substring(1) : "";
    }

    /**
     * 根据控货策略id查询控货策略
     * 
     * @return
     */
    public List<ProductControlConfigDTO> listProductControlConfigByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "控货策略ID不能为空");

        // 获取控货策略
        List<ProductControlConfigPO> configPOS = productControlConfigPOMapper.listProductControlConfigByIds(ids);
        if (CollectionUtils.isEmpty(configPOS)) {
            return null;
        }
        List<ProductControlConfigDTO> configDTOS = configPOS.stream()
            .map(ProductControlConfigConvertor::convertToProductControlConfigDTO).collect(Collectors.toList());
        // 获取产品图片
        List<Long> skuIdList =
            configDTOS.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        Map<Long, ProductImageDTO> productImageDTOMap = productSkuQueryBL.getProductImageUrl(skuIdList);

        // 根据控货策略ID查询控货策略明细
        Map<Long, List<ProductControlConfigItemDTO>> itemMap = getConfigItemMap(ids);
        if (itemMap != null) {
            configDTOS.forEach(p -> {
                p.setItemList(itemMap.get(p.getId()));
                // 控货策略拼接
                p.setConfigItemName(getConfigItemName(p.getItemList()));
                // 产品图片
                if (productImageDTOMap != null) {
                    p.setProductImage(productImageDTOMap.get(p.getProductSkuId()));
                }
            });
        }

        return configDTOS;
    }

    /**
     * 根据控货策略ID查询控货策略名称
     * 
     * @return
     */
    public Map<Long, String> getConfigItemNameMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        // 根据控货策略ID查询控货策略明细
        Map<Long, List<ProductControlConfigItemDTO>> itemMap = getConfigItemMap(ids);
        if (itemMap == null) {
            return null;
        }
        Map<Long, String> itemNameMap = new HashMap<>(16);
        itemMap.forEach((configId, configItems) -> {
            itemNameMap.put(configId, getConfigItemName(configItems));
        });
        return itemNameMap;
    }

    /**
     * 获取控货策略详情
     * 
     * @return
     */
    public ProductControlConfigDTO getProductControlConfig(Long id) {
        AssertUtils.notNull(id, "控货策略ID不能为空");
        // 1、查询控货策略
        ProductControlConfigDTO productControlConfigDTO = productControlConfigPOMapper.getProductControlConfig(id);
        if (productControlConfigDTO == null) {
            return null;
        }

        // 2、查询控货策略明细
        List<ProductControlConfigItemPO> itemPOList =
            productControlConfigItemPOMapper.listProductControlConfigItemByConfigId(Arrays.asList(id));
        if (CollectionUtils.isNotEmpty(itemPOList)) {
            List<ProductControlConfigItemDTO> itemDTOList = itemPOList.stream()
                .map(ProductControlConfigConvertor::convertToProductControlConfigItemDTO).collect(Collectors.toList());
            productControlConfigDTO.setItemList(itemDTOList);
            // 控货策略
            productControlConfigDTO.setConfigItemName(getConfigItemName(productControlConfigDTO.getItemList()));
        }
        return productControlConfigDTO;
    }

    /**
     * 新增控货策略
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Long saveProductControlConfig(ProductControlConfigDTO productControlConfigDTO) {
        AssertUtils.notNull(productControlConfigDTO, "参数不能为空");
        AssertUtils.notNull(productControlConfigDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(productControlConfigDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(productControlConfigDTO.getProvider(), "供应商不能为空");
        AssertUtils.notNull(productControlConfigDTO.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notEmpty(productControlConfigDTO.getItemList(), "控货策略明细不能为空");

        // 1、保存控货策略
        ProductControlConfigPO productControlConfigPO =
            ProductControlConfigConvertor.convertToProductControlConfigPO(productControlConfigDTO);
        // 默认停用
        if (productControlConfigPO.getState() == null) {
            productControlConfigPO.setState(ConditionStateEnum.否.getType());
        }
        productControlConfigPOMapper.insertSelective(productControlConfigPO);

        // 2、保存控货策略明细
        List<ProductControlConfigItemPO> itemPOList = productControlConfigDTO.getItemList().stream().map(dto -> {
            ProductControlConfigItemPO itemPO = ProductControlConfigConvertor.convertToProductControlConfigItemPO(dto);
            itemPO.setConfigId(productControlConfigPO.getId());
            return itemPO;
        }).collect(Collectors.toList());
        productControlConfigItemPOMapper.insertBatch(itemPOList);
        return productControlConfigPO.getId();
    }

    /**
     * 更新控货策略
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductControlConfig(ProductControlConfigDTO productControlConfigDTO) {
        AssertUtils.notNull(productControlConfigDTO, "参数不能为空");
        AssertUtils.notNull(productControlConfigDTO.getId(), "控货策略ID不能为空");

        // 1、更新控货策略
        ProductControlConfigPO productControlConfigPO =
            ProductControlConfigConvertor.convertToProductControlConfigPO(productControlConfigDTO);
        productControlConfigPOMapper.updateByPrimaryKeySelective(productControlConfigPO);

        // 2、更新控货策略明细（先删除，再新增）
        if (CollectionUtils.isNotEmpty(productControlConfigDTO.getItemList())) {
            productControlConfigItemPOMapper.deleteByConfigId(productControlConfigDTO.getId());

            List<ProductControlConfigItemPO> itemPOList = productControlConfigDTO.getItemList().stream().map(dto -> {
                ProductControlConfigItemPO itemPO =
                    ProductControlConfigConvertor.convertToProductControlConfigItemPO(dto);
                itemPO.setConfigId(productControlConfigPO.getId());
                return itemPO;
            }).collect(Collectors.toList());
            productControlConfigItemPOMapper.insertBatch(itemPOList);
        }
    }

    /**
     * 删除控货策略（逻辑删除）
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteProductControlConfig(Long id) {
        AssertUtils.notNull(id, "控货策略ID不能为空");
        // 更新控货策略是否删除字段
        ProductControlConfigPO productControlConfigPO = new ProductControlConfigPO();
        productControlConfigPO.setId(id);
        productControlConfigPO.setIsDelete(ConditionStateEnum.是.getType());
        productControlConfigPOMapper.updateByPrimaryKeySelective(productControlConfigPO);
        LOGGER.info("删除控货策略：{}", id);
    }

    /**
     * 根据订单的用户地址，查询产品对应的控货策略（Key -> skuId，Value -> 控货策略）
     * 
     * @return
     */
    public Map<Long, ProductControlConfigDTO> getControlConfigIdBySkuId(ProductControlConfigQuery query) {
        AssertUtils.notNull(query, "参数不能为空");
        AssertUtils.notNull(query.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(query.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(query.getProductSkuIds(), "产品skuId不能为空");
        if (StringUtils.isEmpty(query.getCity()) && StringUtils.isEmpty(query.getCounty())) {
            throw new DataValidateException("用户地址不能为空");
        }
        LOGGER.info("根据地址查询控货策略ID参数：{}", JSON.toJSONString(query));
        List<ProductControlConfigDTO> configDTOS =
            productControlConfigPOMapper.listProductControlConfigByAddress(query);
        if (CollectionUtils.isEmpty(configDTOS)) {
            return null;
        }
        // 获取控货策略名称
        List<Long> configIds = configDTOS.stream().map(p -> p.getId()).distinct().collect(Collectors.toList());
        Map<Long, String> configItemNameMap = getConfigItemNameMap(configIds);

        // 找到产品sku对应的控货策略（多个随机取一条）
        Map<Long, ProductControlConfigDTO> resultMap = new HashMap<>(16);
        Map<Long, List<ProductControlConfigDTO>> configMap =
            configDTOS.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
        configMap.forEach((skuId, configList) -> {
            ProductControlConfigDTO configDTO = new ProductControlConfigDTO();
            configDTO.setId(configList.get(0).getId());
            configDTO.setConfigItemName(configItemNameMap != null ? configItemNameMap.get(configDTO.getId()) : "");
            resultMap.put(skuId, configDTO);
        });
        LOGGER.info("根据地址查询控货策略返回Map：{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

}
