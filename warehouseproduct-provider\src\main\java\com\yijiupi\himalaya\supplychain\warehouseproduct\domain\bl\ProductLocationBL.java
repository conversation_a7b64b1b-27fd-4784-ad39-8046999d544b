/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dto.*;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoSO;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.service.ILocationReportService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productlocation.ProductLocationHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productlocation.ProductLocationValidateBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ProductLocationAssociateBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.ProductLocationValidateOperationLegalBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.PickLocationModToBatchTaskDTOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductLocationListDTOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationCheckResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ReplaceProductLocationCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuBaseInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.*;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskManageService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickLocationModToBatchTaskDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2017年7月11日 下午3:10:01
 */
@Service
public class ProductLocationBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductLocationBL.class);
    @Autowired
    private ProductLocationPOMapper productLocationPOMapper;
    @Autowired
    private ProductIdGenerator productIdGenerator;
    @Autowired
    private LocationAreaPOMapper locationAreaPOMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;
    @Autowired
    private ProductSkuServiceBL productSkuServiceBL;
    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;
    @Autowired
    private CodeGenerator codeGenerator;
    @Autowired
    private GlobalCache globalCache;
    @Resource
    private ProductLocationHelper productLocationHelper;
    @Resource
    private ProductLocationValidateBL productLocationValidateBL;

    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IAdminUserQueryService iAdminUserQueryService;

    private static final Gson GSON = new Gson();

    private static final String PRODUCT_LOCATION_RELATION_CONFIG = "productLocationRelationConfig";
    @Reference
    private ILocationReportService iLocationReportService;
    @Reference
    private IBatchTaskManageService iBatchTaskManageService;

    @Autowired
    private StorageAttributeService storageAttributeService;

    @Autowired
    private LocationBL locationBL;

    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    private final Integer pageSize = 3000;

    @Transactional(rollbackFor = RuntimeException.class)
    public void add(LoactionDTO dto) {
        LOGGER.info("添加货位信息参数：{}", JSON.toJSONString(dto));
        // 添加库位信息
        LocationPO record = new LocationPO(dto);
        record.setCreateTime(new Date());
        record.setCreateUserId(dto.getUserId());
        String locationName = getName(dto, true);
        record.setName(dto.getName() == null ? locationName : dto.getName());
        LocationPO po = locationPOMapper.selectByName(locationName, dto.getWarehouseId(), dto.getCityId());
        if (po != null) {
            throw new BusinessValidateException("货位名称重复，已有名称为" + record.getName() + "的货位！");
        }
        /*
         * if (dto.getSequence() != null) { int sequenceCount =
         * locationPOMapper.selectBySequence(null, dto.getSequence(), dto.getWarehouseId(),
         * dto.getCityId()); if (sequenceCount != 0) { throw new
         * BusinessValidateException("线路顺序重复，请重新输入！"); } }
         */
        LocationAreaPO area = locationAreaPOMapper.selectByPrimaryKey(dto.getArea_Id());
        // LOGGER.info("dto.getArea:{},dto.getArea_Id:{},area.getArea:{},area.getName:{}", dto.getArea(),
        // dto.getArea_Id(), area == null ? "" : area.getArea(), area == null ? "" : area.getName());
        if (area == null || dto.getArea() == null || !dto.getArea().equals(area.getName())
        // 获取ID与当前仓库ID不一致，拦截，处理批量导入货位的场景
            || !Objects.equals(dto.getWarehouseId(), area.getWarehouse_Id())) {
            throw new BusinessException("货区信息不存在或有误,请检查货区信息");
        }
        record.setId(UUIDGenerator.getUUID(LocationPO.class.getName()));
        locationPOMapper.insertSelective(record);
        // 添加关系表信息
        List<ProductLocationPO> productLocationPOS = new ArrayList<>();
        if (dto.getProductSkuIdList() != null && !dto.getProductSkuIdList().isEmpty()) {
            for (Long productSkuId : dto.getProductSkuIdList()) {
                ProductLocationPO relPO = new ProductLocationPO(dto, record.getId(), productSkuId);
                relPO.setId(productIdGenerator.generatorLocationId(dto.getCityId()));
                relPO.setCreateTime(new Date());
                relPO.setCreateUserId(dto.getUserId());
                productLocationHelper.insertSelective(relPO);
                productLocationPOS.add(relPO);
            }
        }
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(dto.getWarehouseId());
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(dto.getUserId(), productLocationPOS);
    }

    private String getString(String name, Boolean symble) {
        if (name == null || "".equals(name.trim())) {
            return "";
        }
        return (symble ? "-" : "") + name;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void modify(LoactionDTO dto) {
        LOGGER.info("修改货位信息参数：{}", JSON.toJSONString(dto));
        LocationPO record = new LocationPO(dto);
        record.setLastUpdateTime(new Date());
        record.setLastUpdateUserId(dto.getUserId());
        record.setName(dto.getName() == null ? getName(dto, true) : dto.getName());
        LocationAreaPO area = locationAreaPOMapper.selectByPrimaryKey(dto.getArea_Id());
        // LOGGER.info("dto.getArea:{},dto.getArea_Id:{},area.getArea:{},area.getName:{}", dto.getArea(),
        // dto.getArea_Id(), area == null ? "" : area.getArea(), area == null ? "" : area.getName());
        if (area == null || dto.getArea() == null || !dto.getArea().equals(area.getName())) {
            throw new BusinessException("货区信息不存在或有误,请检查货区信息");
        }
        String locationName = getName(dto, true);
        LocationPO po = locationPOMapper.selectByName(locationName, dto.getWarehouseId(), dto.getCityId());
        if (po != null && !po.getId().equals(dto.getId())) {
            throw new BusinessValidateException("货位名称重复，已有名称为" + record.getName() + "的货位！");
        }
        /*
         * if (dto.getSequence() != null) { int sequenceCount =
         * locationPOMapper.selectBySequence(dto.getId(), dto.getSequence(), dto.getWarehouseId(),
         * dto.getCityId()); if (sequenceCount != 0) { throw new
         * BusinessValidateException("线路顺序重复，请重新输入！"); } }
         */
        int count = locationPOMapper.updateByPrimaryKeySelective(record);
        if (count < 1) {
            throw new BusinessException("找不到该货位");
        }
        productLocationPOMapper.deleteByLocationId(record.getId());
        LOGGER.info("修改货位信息事时删除原产品关联货位：{}", JSON.toJSONString(record));
        // 添加关系表信息
        List<ProductLocationPO> productLocationPOS = new ArrayList<>();
        if (dto.getProductSkuIdList() != null && !dto.getProductSkuIdList().isEmpty()) {
            for (Long productSkuId : dto.getProductSkuIdList()) {
                ProductLocationPO relPO = new ProductLocationPO(dto, record.getId(), productSkuId);
                relPO.setId(productIdGenerator.generatorLocationId(dto.getCityId()));
                relPO.setCreateTime(new Date());
                relPO.setCreateUserId(dto.getUserId());
                productLocationHelper.insertSelective(relPO);
                productLocationPOS.add(relPO);
            }
        }

        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(dto.getWarehouseId());
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(dto.getUserId(), productLocationPOS);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(Long locationId, OperatorDTO operatorDTO) {
        deleteBatch(Collections.singletonList(locationId), operatorDTO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBatch(List<Long> lstIds, OperatorDTO operatorDTO) {
        List<ProductLocationPO> po = productLocationPOMapper.findByLocationIds(lstIds);
        if (po != null && !po.isEmpty()) {
            throw new BusinessValidateException("有" + po.size() + "个货位与商品存在关联关系,请先删除关联关系后再删除此货位!");
        }
        ProductLocationPO productLocationPO = po.stream().findFirst().get();
        locationBL.checkDeleteLocation(productLocationPO.getWarehouse_Id(), lstIds);

        int count = locationPOMapper.deleteByPrimaryKeyBatch(lstIds);
        if (count < 1) {
            throw new BusinessValidateException("删除货位信息失败");
        }
        productLocationPOMapper.deleteByLocationIds(lstIds);
        LOGGER.info("[批量删除关联货位]{}, 操作人：{}", JSON.toJSONString(po), JSON.toJSONString(operatorDTO));
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(operatorDTO.getUserId(), po);
    }

    /**
     * 根据SKUID集合查找配置货位
     */
    public List<ProductLoactionItemDTO> findLocationBySkuId(Integer warehouseId, Collection<Long> productSkuIds) {
        return productLocationPOMapper.findLocationBySkuId(productSkuIds, warehouseId);
    }

    /**
     * 根据仓库ID和产品skuId删除产品货位关系
     *
     * @param productSkuIds
     * @param warehouseId
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBySkuId(Integer warehouseId, List<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "产品SkuID不能为空");
        productLocationPOMapper.deleteBySkuId(productSkuIds, warehouseId);
        LOGGER.info("根据仓库ID和产品skuId删除产品货位 仓库：{}， skuids ：{}", warehouseId, JSON.toJSONString(productSkuIds));
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(null, warehouseId, productSkuIds);
    }

    public Map<Long, List<LoactionDTO>> findLocationDTOBySkuId(Integer warehouseId, Collection<Long> productSkuIds) {
        Map<Long, List<LoactionDTO>> result = new HashMap<>(16);
        List<ProductLoactionItemDTO> locationBySkuId = findLocationBySkuId(warehouseId, productSkuIds);
        if (CollectionUtils.isNotEmpty(locationBySkuId)) {
            List<LoactionDTO> locations = new ArrayList<>();
            locationBySkuId.forEach(p -> {
                LoactionDTO dto = new LoactionDTO();
                dto.setName(p.getLocationName());
                dto.setId(p.getLocationId());
                dto.setArea(p.getAreaName());
                dto.setArea_Id(p.getAreaId());
                dto.setCategory(p.getCategory());
                dto.setCityId(p.getCityId());
                dto.setIsChaosBatch(p.getIsChaosBatch());
                dto.setLocationCapacity(p.getLocationCapacity());
                dto.setIsChaosPut(p.getIsChaosPut());
                dto.setPallets(p.getPallets());
                dto.setRoadway(p.getRoadway());
                dto.setSequence(p.getSequence());
                dto.setSubcategory(p.getSubcategory());
                dto.setSubcategoryName(p.getSubcategoryName());
                dto.setWarehouseId(p.getWarehouseId());
                dto.setProductSkuId(p.getProductSkuId());
                dto.setBusinessType(p.getBusinessType());
                dto.setLastUpdateTime(p.getLastUpdateTime() != null ? p.getLastUpdateTime() : p.getCreateTime());
                locations.add(dto);
            });
            result = locations.stream().collect(Collectors.groupingBy(LoactionDTO::getProductSkuId));
        }
        return result;
    }

    public void update(Long areaId, Integer userId) {
        List<LocationPO> loactionDTOList = locationPOMapper.listLocationDTO(areaId);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return;
        }
        List<LocationPO> locationPOList = new ArrayList<>();
        for (LocationPO po : loactionDTOList) {
            LocationPO record = po;
            record.setLastUpdateTime(new Date());
            record.setLastUpdateUserId(userId);
            record.setName(getName(po));
            record.setArea(po.getArea());
            locationPOList.add(record);
        }
        locationPOMapper.updateName(locationPOList);
    }

    private String getName(LocationPO po) {
        StringBuilder builder = new StringBuilder();
        builder.append(getString(po.getArea(), false));
        builder.append(getString(po.getRoadWay(), false));
        builder.append(getString(po.getProductLocation(), true));
        return builder.toString();
    }

    private String getName(LoactionDTO dto, Boolean regex) {
        StringBuilder builder = new StringBuilder();
        if (regex) {
            LocationAreaPO locationAreaPO = locationAreaPOMapper.getLocationAreaById(dto.getArea_Id());
            if (locationAreaPO == null) {
                throw new RuntimeException("无法获取到货区信息");
            }
            builder.append(getString(locationAreaPO.getName(), false));
        } else {
            builder.append(getString(dto.getArea(), false));
        }
        builder.append(getString(dto.getRoadway(), false));
        builder.append(getString(dto.getProductlocation(), true));
        return builder.toString();
    }

    public ProductLocationDetailDTO getProductLocationBySkuId(LocationQueryDTO dto) {
        ProductLocationDetailDTO result = locationPOMapper.getProductLocationBySkuId(dto);
        return result;
    }

    public List<ProductLocationDetailDTO> findProductLocationBySkuId(LocationQueryDTO dto) {
        int pageCount = 1;
        List<ProductLocationDetailDTO> resultList = new ArrayList<>();
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            PageResult<ProductLocationDetailDTO> pageResult =
                    locationPOMapper.pageListProductLocationBySkuIds(dto, pageNum, pageSize);
            if (pageNum == 1) {
                pageCount = pageResult.getPager().getTotalPage();
            }
            List<ProductLocationDetailDTO> result = pageResult.getResult();
            if (!CollectionUtils.isEmpty(result)) {
                resultList.addAll(result);
            }
        }
        return resultList;
    }

    public List<ProductLocationDetailDTO> findProductLocationBySkuIds(LocationQueryDTO dto) {
        return locationPOMapper.findProductLocationBySkuIds(dto);
    }

    /**
     * 经销商入库确认收货. (sku和货位管理)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void agencyPutInStore(List<LoactionDTO> dto) {
        // 添加关系表信息
        dto.forEach(n -> {
            LocationPO record = new LocationPO(n);
            if (n.getProductSkuIdList() != null && !n.getProductSkuIdList().isEmpty()) {
                for (Long productSkuId : n.getProductSkuIdList()) {
                    ProductLocationPO relPO = new ProductLocationPO(n, record.getId(), productSkuId);
                    Long id = productIdGenerator.generatorLocationId(n.getCityId());
                    relPO.setId(id);
                    relPO.setCreateTime(new Date());
                    relPO.setCreateUserId(n.getUserId());
                    // todo 如果有了就不加了。
                    Long productLocationCount = productLocationPOMapper.getProductLocationCount(productSkuId,
                            record.getId(), n.getWarehouseId());
                    if (productLocationCount == 0) {
                        productLocationHelper.insertSelective(relPO);
                    }
                }
            }
        });
    }

    public PageList<LoactionDTO> findProductLocationPageList(LocationQueryDTO dto) {
        // Map<String, Object> params = convertToMap(dto);
        // Integer count = locationPOMapper.findProductLocationCount(params);
        // PageList<LoactionDTO> result = new PageList<>();
        // if (count == null || count == 0) {
        // result.setDataList(new ArrayList<>(0));
        // result.setPager(new Pager(dto.getCurrentPage(), dto.getPageSize(), 0));
        // return result;
        // }
        PageResult<LoactionDTO> modelList = locationPOMapper.findProductLocationPageList(dto);
        modelList.forEach(p -> {
            if (p.getSubcategory() != null) {
                p.setSubcategoryName(LocationEnum.getEnumStr(p.getSubcategory().intValue()));
            }
        });
        // LOGGER.info(String.format("查询货位：%s,Result:%s",JSON.toJSONString(dto),JSON.toJSONString(modelList)));
        // result.setDataList(modelList);
        // result.setPager(new Pager(dto.getCurrentPage(), dto.getPageSize(), count));
        return modelList.toPageList();
    }

    /**
     * 查询货位信息列表 ，老方法存在分页不正确问题
     */
    public PageList<LoactionDTO> findProductLocationPageListNew(LocationQueryDTO dto) {
        PageResult<LoactionDTO> modelList = locationPOMapper.findProductLocationPageListNew(dto);
        modelList.forEach(p -> {
            List<ProductLocationPO> list = productLocationPOMapper.findByLocationId(p.getId());
            if (!list.isEmpty()) {
                p.setProductSkuIdList(
                        list.stream().map(ProductLocationPO::getProductSku_Id).collect(Collectors.toList()));
            }
            if (p.getSubcategory() != null) {
                p.setSubcategoryName(LocationEnum.getEnumStr(p.getSubcategory().intValue()));
            }
        });
        return modelList.toPageList();
    }

    /**
     * 查询货区和货位信息列表
     *
     * @param dto
     * @return
     */
    public PageList<LoactionDTO> findProductLocationOrAreaPageListNew(LocationQueryDTO dto) {
        PageResult<LoactionDTO> modelList = locationPOMapper.findProductLocationOrAreaPageListNew(dto);
        modelList.forEach(p -> {
            List<ProductLocationPO> list = productLocationPOMapper.findByLocationId(p.getId());
            if (list != null && list.size() > 0) {
                p.setProductSkuIdList(list.stream().map(x -> x.getProductSku_Id()).collect(Collectors.toList()));
            }
            if (p.getSubcategory() != null) {
                p.setSubcategoryName(LocationEnum.getEnumStr(p.getSubcategory().intValue()));
            }
        });
        return modelList.toPageList();
    }

    /**
     * 查询指定类型货区下的所有货位
     *
     * @param dto
     * @return
     */
    public List<LoactionDTO> findLocationListByWarehouseIdAndAreaType(LocationQueryDTO dto) {
        Map<String, Object> params = convertToMap(dto);
        List<LoactionDTO> result = locationPOMapper.findLocationListByWarehouseIdAndAreaType(params);
        result.forEach(p -> {
            if (p.getSubcategory() != null) {
                p.setSubcategoryName(LocationEnum.getEnumStr(p.getSubcategory().intValue()));
            }
        });
        return result;
    }

    public List<LoactionDTO> findProductLocationList(LocationQueryDTO dto) {
        // Map<String, Object> params = convertToMap(dto);
        // Integer count = locationPOMapper.findProductLocationCount(params);
        // if (count == null || count == 0) {
        // return new ArrayList<>();
        // }
        PageResult<LoactionDTO> modelList = locationPOMapper.findProductLocationPageList(dto);
        return modelList;
    }

    private Map<String, Object> convertToMap(LocationQueryDTO dto) {
        Map<String, Object> params = new HashMap<>(16);
        params.put("cityId", dto.getCityId());
        params.put("warehouseId", dto.getWarehouseId());
        params.put("productName", dto.getProductName());
        params.put("name", dto.getName());
        params.put("area", dto.getArea());
        params.put("limitnum", dto.getLimitnum());
        params.put("offset", dto.getOffset());
        params.put("isChaosPut", dto.getIsChaosPut());
        params.put("isChaosBatch", dto.getIsChaosBatch());
        params.put("subcategory", dto.getSubcategory());
        params.put("locSubcategory", dto.getLocSubcategory());
        return params;
    }

    /**
     * 根据货位模糊查询商品skuid
     */
    public List<Long> findProductSkuByProductLocation(LocationQueryDTO dto) {
        List<Long> skuList = locationPOMapper.findProductSkuByProductLocation(dto);
        return skuList;
    }

    /**
     * 批量导入货位信息,不传area_id,需要根据areaName查询id
     *
     * @param dto
     * @return: void
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void importLocation(LocationImportDTO dto) {
        long start = System.currentTimeMillis();
        List<LoactionDTO> locationList = dto.getLocationList();
        List<String> areaList = locationList.stream().map(LoactionDTO::getArea).collect(Collectors.toList());
        List<LocationAreaPO> po =
                locationAreaPOMapper.findAreaIdByAreaList(areaList, dto.getWarehouse_Id(), dto.getCity_Id());
        List<String> errorAreaList = new ArrayList<>();
        List<LocationPO> locationPoList = new ArrayList<>();
        Map<String, Long> areaMap =
                po.stream().collect(Collectors.toMap(LocationAreaPO::getName, LocationAreaPO::getId));
        List<String> names = new ArrayList<>();
        for (int i = 0; i < locationList.size(); i++) {
            // 检查货位是否重复,每满100个查询一次,并且把列表清空
            if (names.size() > 0 && names.size() % 100 == 0) {
                List<LocationPO> repeatPoList =
                        locationPOMapper.selectByNames(names, dto.getWarehouse_Id(), dto.getCity_Id());
                if (repeatPoList.size() > 0) {
                    List<String> collect = repeatPoList.stream().map(t -> getName(t)).collect(Collectors.toList());
                    throw new BusinessValidateException("货位名称重复，已有名称为" + GSON.toJson(collect) + "的货位！");
                }
                names.clear();
            }
            names.add(getName(locationList.get(i), false));
        }
        // 剩下的不满100个的也要校验货位
        List<LocationPO> repeatPoList = locationPOMapper.selectByNames(names, dto.getWarehouse_Id(), dto.getCity_Id());
        if (repeatPoList.size() > 0) {
            List<String> collect = repeatPoList.stream().map(t -> getName(t)).collect(Collectors.toList());
            throw new BusinessValidateException("货位名称重复，已有名称为" + GSON.toJson(collect) + "的货位！");
        }
        for (LoactionDTO locationDto : locationList) {
            Long area_Id = areaMap.get(locationDto.getArea());
            if (area_Id == null) {
                errorAreaList.add(locationDto.getArea());
                continue;
            }
            locationDto.setArea_Id(area_Id);
            LocationPO locationPo = new LocationPO(locationDto);
            Date createTime = new Date();
            locationPo.setName(getName(locationDto, true));
            locationPo.setCreateTime(createTime);
            locationPo.setCreateUserId(dto.getUser_Id());
            locationPo.setLastUpdateTime(createTime);
            locationPo.setLastUpdateUserId(dto.getUser_Id());
            locationPo.setSubcategory(LocationEnum.valueOf(locationDto.getSubcategoryName()).getType().byteValue());
            locationPo.setIsChaosBatch(locationDto.getIsChaosBatch());
            locationPo.setIsChaosPut(locationDto.getIsChaosPut());
            locationPo.setLocationCapacity(locationDto.getLocationCapacity());
            locationPo.setId(UUIDGenerator.getUUID(LocationPO.class.getName()));
            locationPoList.add(locationPo);
        }
        if (!errorAreaList.isEmpty()) {
            throw new BusinessException("导入失败!货区" + GSON.toJson(errorAreaList) + "不存在,请先导入货区信息!");
        }
        locationPOMapper.batchInsert(locationPoList, dto.getWarehouse_Id(), dto.getCity_Id());
        // long end = System.currentTimeMillis();
        // LOGGER.info("total:" + (end - start));
    }

    public List<LocationReturnDTO> findLocationList(LocationQueryDTO dto) {
        List<LocationPO> locationList = PageHelperUtils.pageQuery(() -> locationPOMapper.findLocationList(dto));
        return Lists.transform(locationList, input -> {
            LocationReturnDTO dto1 = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto1);
            return dto1;
        });
    }

    /**
     * 根据条件查询货位
     *
     * @return
     */
    public List<LocationReturnDTO> listLocationByCondition(LocationQueryDTO dto) {
        List<LocationPO> locationList = locationPOMapper.listLocationByCondition(dto);
        List<LocationReturnDTO> returnDTOS = Lists.transform(locationList, input -> {
            LocationReturnDTO dto1 = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto1);
            return dto1;
        });
        return returnDTOS;
    }

    /**
     * 获取产品货位配置列表
     *
     * @param so
     * @return
     */
    public PageList<ProductLocationListDTO> listProductLocation(ProductLocationListSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        // SCM2-8596 店仓多货主调整: 店仓不抹货主
        // Boolean ownerInfoEraseConfig = iInStockQueryService.findOwnerInfoEraseWarehouseConfig(so.getWarehouseId());
        // so.setEraseOwnerId(ownerInfoEraseConfig ? 1 : 0);
        // 查询产品货位配置列表
        PageResult<ProductLocationListPO> poPageResult = productLocationPOMapper.listProductLocation(so);

        return ProductLocationListDTOConvertor.convert(poPageResult, so);
    }

    /**
     * 供应链客户端查询关联货位
     *
     * @param so
     * @return
     */
    public PageList<ProductLocationListDTO> findProductLocationForSupplyChainClient(ProductLocationListSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        // 查询产品货位配置列表

        PageResult<ProductLocationListPO> poPageResult;
        if (Objects.nonNull(so.getHasRealStore()) && so.getHasRealStore() == 1) {
            poPageResult = productLocationPOMapper.listProductLocationWithInventory(so);
        } else {
            poPageResult = productLocationPOMapper.listProductLocationWithoutInventory(so);
        }

        return ProductLocationListDTOConvertor.convert(poPageResult, so);
    }

    /**
     * 获取产品货位配置列表（不分页）
     *
     * @param so
     * @return
     */
    public List<ProductLocationListDTO> listProductLocationNoPage(ProductLocationListSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        List<ProductLocationListPO> listPOList = productLocationPOMapper.listProductLocationNoPage(so);
        // 对象转换
        List<ProductLocationListDTO> dtoList = listPOList.stream().map(po -> {
            if (null == po) {
                return null;
            }
            ProductLocationListDTO dto = new ProductLocationListDTO();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());
        // LOGGER.info("产品货位配置列表（不分页）：{}", JSON.toJSONString(dtoList));
        return dtoList;
    }

    /**
     * 新增产品货位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveProductLocation(ProductLocationDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notNull(dto.getLocationId(), "货位Id不能为空");
        AssertUtils.notNull(dto.getUserId(), "操作人Id不能为空");

        LOGGER.info("新增产品货位入参: {}", JSON.toJSONString(dto));
        // 开启分区的仓库，验证人 和 货区 是否属于一个分仓
        productLocationValidateBL.validateOperationLegal(ProductLocationValidateOperationLegalBO
            .ofLocationIds(dto.getUserId(), dto.getWarehouseId(), Collections.singletonList(dto.getLocationId())));

        Long productLocationCount = productLocationPOMapper.getProductLocationCount(dto.getProductSkuId(),
                dto.getLocationId(), dto.getWarehouseId());
        if (productLocationCount > 0) {
            throw new BusinessValidateException("该产品货位设置已存在！");
        }
        // 验证货位是否不允许混放，不允许混放的货位不能关联多个产品
        checklocationMixup(dto.getProductSkuId(), dto.getLocationId(), dto.getWarehouseId());

        // 判断货位中是否已有相同类目或相同产品类型的产品
        checkLocationSameProduct(dto.getProductSkuId(), dto.getLocationId(), dto.getWarehouseId());

        // 产品关联货位根据货位业务类型检查
        validateByBusinessType(dto,true);

        ProductLocationPO po = new ProductLocationPO();
        Long id = productIdGenerator.generatorLocationId(dto.getCityId());
        po.setId(id);
        po.setCity_Id(dto.getCityId());
        po.setWarehouse_Id(dto.getWarehouseId());
        po.setProductSku_Id(dto.getProductSkuId());
        po.setLocation_Id(dto.getLocationId());
        po.setRemo(dto.getRemo());
        po.setCreateTime(new Date());
        po.setCreateUserId(dto.getUserId());
        productLocationHelper.insertSelective(po);
        LOGGER.info("新增产品货位: {}", JSON.toJSONString(po));
        handleVersion4Location(dto);
        handle2p5plusLocation(dto);
        handleBatchTaskInfo(po, new ProductLocationAssociateBO(dto, Boolean.TRUE), dto.getLocationId());
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(dto.getUserId(), Collections.singletonList(po));
        return id;
    }

    /**
     * 判断货位中是否已有相同类目或相同产品类型的产品
     *
     * @param warehouseId
     * @param locationId
     * @param productSkuId
     */
    private void checkLocationSameProduct(Long productSkuId, Long locationId, Integer warehouseId) {
        VariableValueQueryDTO variableValue = new VariableValueQueryDTO();
        variableValue.setVariableKey(PRODUCT_LOCATION_RELATION_CONFIG);
        variableValue.setWarehouseId(warehouseId);
        VariableDefAndValueDTO variableDefAndValueDTO = iVariableValueService.detailVariable(variableValue);

        if (variableDefAndValueDTO == null || StringUtils.isEmpty(variableDefAndValueDTO.getVariableData())) {
            return;
        }

        int locationRelationConfig = Integer.valueOf(variableDefAndValueDTO.getVariableData());
        LOGGER.info("locationRelationConfig配置项：" + locationRelationConfig);

        if (locationRelationConfig != ProductLocationRelationConfigEnum.按品牌类目.getType()
                && locationRelationConfig != ProductLocationRelationConfigEnum.按条码.getType()) {
            return;
        }

        // 从货位中查找产品
        List<ProductSkuPO> locationProducts = productSkuPOMapper.findProductInLocation(locationId, warehouseId);

        if (CollectionUtils.isEmpty(locationProducts)) {
            return;
        }

        // 根据产品skuId查询产品品牌、一级类目ID
        ProductSkuPO curProduct = productSkuPOMapper.findProductBrandCategoryBySku(productSkuId);

        if (curProduct == null) {
            return;
        }

        // 判断货位中是否有相同品牌类型的产品
        if (locationRelationConfig == ProductLocationRelationConfigEnum.按品牌类目.getType()) {
            if (StringUtils.isEmpty(curProduct.getProductBrand()) || curProduct.getStatisticsClassId() == null) {
                return;
            }
            boolean isSameBrandCategory = locationProducts.stream()
                    .anyMatch(productElem -> Objects.equals(curProduct.getProductBrand(), productElem.getProductBrand())
                            && Objects.equals(curProduct.getStatisticsClassId(), productElem.getStatisticsClassId()));
            if (isSameBrandCategory) {
                throw new BusinessValidateException("相同类目品牌的产品不允许放在同一个货位，请先移除原货位关联产品，或者将当前产品绑定到其他货位");
            }
            // 判断货位中是否有相同品条码的产品
        } else if (locationRelationConfig == ProductLocationRelationConfigEnum.按条码.getType()) {
            Set<Long> skuIdSet = new HashSet<>();
            skuIdSet.add(productSkuId);

            locationProducts.stream().forEach(skuElem -> skuIdSet.add(skuElem.getProductSku_Id()));

            // 获取货位中所有产品、当前产品的条码
            Map<Long, ProductCodeDTO> packageAndUnitCode =
                    productSkuServiceBL.getPackageAndUnitCode(skuIdSet, curProduct.getCity_Id());

            if (packageAndUnitCode == null || packageAndUnitCode.size() == 0) {
                return;
            }

            ProductCodeDTO curProductCode = packageAndUnitCode.get(productSkuId);

            if (curProductCode == null) {
                return;
            }

            List<String> curPackageCode = curProductCode.getPackageCode();
            List<String> curUnitCode = curProductCode.getUnitCode();

            if (CollectionUtils.isEmpty(curPackageCode) && CollectionUtils.isEmpty(curUnitCode)) {
                return;
            }

            packageAndUnitCode.remove(productSkuId);

            Collection<ProductCodeDTO> productCodes = packageAndUnitCode.values();

            boolean isSameCode = productCodes.stream().anyMatch(locProduceCode -> {
                boolean isSamePackageCode = false;
                boolean isSameUnitCode = false;
                if (CollectionUtils.isNotEmpty(curPackageCode)
                        && CollectionUtils.isNotEmpty(locProduceCode.getPackageCode())) {
                    isSamePackageCode = locProduceCode.getPackageCode().stream()
                            .anyMatch(code -> code != null && curPackageCode.contains(code));
                }
                if (CollectionUtils.isNotEmpty(curUnitCode)
                        && CollectionUtils.isNotEmpty(locProduceCode.getUnitCode())) {
                    isSameUnitCode = locProduceCode.getUnitCode().stream()
                            .anyMatch(code -> code != null && curUnitCode.contains(code));
                }
                return isSamePackageCode || isSameUnitCode;
            });

            if (isSameCode) {
                throw new BusinessValidateException("相同条码的产品不允许放在同一个货位，请先移除原货位关联产品，或者将当前产品绑定到其他货位");
            }
        }
    }

    /**
     * 修改产品货位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProductLocation(ProductLocationDTO dto) {
        AssertUtils.notNull(dto.getId(), "id不能为空");
        AssertUtils.notNull(dto.getLocationId(), "货位Id不能为空");
        AssertUtils.notNull(dto.getUserId(), "操作人Id不能为空");
        ProductLocationPO productLocationPO = productLocationPOMapper.selectByPrimaryKey(dto.getId());
        if (null == productLocationPO) {
            throw new BusinessException("该产品货位设置不存在，请联系管理员！");
        }
        Long productLocationCount = productLocationPOMapper.getProductLocationCount(
                productLocationPO.getProductSku_Id(), dto.getLocationId(), productLocationPO.getWarehouse_Id());
        if (productLocationCount > 0) {
            throw new BusinessValidateException("该产品货位设置已存在！");
        }

        // 开启分区的仓库，验证人 和 货区 是否属于一个分仓
        productLocationValidateBL.validateOperationLegal(ProductLocationValidateOperationLegalBO
            .ofLocationIds(dto.getUserId(), productLocationPO.getWarehouse_Id(), Collections.singletonList(dto.getLocationId())));

        // 验证货位是否不允许混放，不允许混放的货位不能关联多个产品
        checklocationMixup(productLocationPO.getProductSku_Id(), dto.getLocationId(),
                productLocationPO.getWarehouse_Id());

        // 产品关联货位根据货位业务类型检查
        validateByBusinessType(dto,false);

        ProductLocationPO po = new ProductLocationPO();
        po.setId(dto.getId());
        po.setLocation_Id(dto.getLocationId());
        po.setLastUpdateTime(new Date());
        po.setLastUpdateUserId(dto.getUserId());
        productLocationPOMapper.updateByPrimaryKeySelective(po);
        LOGGER.info("修改产品货位: {}", JSON.toJSONString(po));

        dto.setWarehouseId(productLocationPO.getWarehouse_Id());
        dto.setProductSkuId(productLocationPO.getProductSku_Id());
        handleVersion4Location(dto);
        handleBatchTaskInfo(productLocationPO, new ProductLocationAssociateBO(dto, Boolean.FALSE), dto.getLocationId());
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(dto.getUserId(), Collections.singletonList(productLocationPO));
    }

    private void handleBatchTaskInfo(ProductLocationPO productLocationPO, ProductLocationAssociateBO bo,
                                     Long newLocationId) {
        try {
            LocationPO locationPO = locationPOMapper.findLocationById(newLocationId);
            PickLocationModToBatchTaskDTO pickLocationModToBatchTaskDTO =
                    PickLocationModToBatchTaskDTOConvertor.convert(productLocationPO, bo, locationPO);
            if (Objects.nonNull(pickLocationModToBatchTaskDTO)) {
                iBatchTaskManageService.pickLocationModToBatchTask(pickLocationModToBatchTaskDTO);
            }
        } catch (Exception e) {
            LOGGER.warn("修改拣货任务信息失败:", e);
        }
    }

    private void handleVersion4Location(ProductLocationDTO dto) {
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(dto.getWarehouseId());

        if (BooleanUtils.isFalse(warehouseConfigDTO.getIsOpenLocationStock())) {
            return;
        }
        boolean isOpenVersion4TransBatchInventory = globalCache.isOpenVersion4TransBatchInventory(dto.getWarehouseId());
        if (BooleanUtils.isFalse(isOpenVersion4TransBatchInventory)) {
            return;
        }

        LocationPO locationPO = locationPOMapper.findLocationById(dto.getLocationId());
        if (LocationEnum.零拣位.getType().byteValue() != locationPO.getSubcategory()
                && LocationEnum.分拣位.getType().byteValue() != locationPO.getSubcategory()) {
            return;
        }

        if(Objects.equals(locationPO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())){
            return;
        }

        List<ProductLoactionItemDTO> productLoactionItemDTOList = productLocationPOMapper
                .findLocationBySkuId(Collections.singletonList(dto.getProductSkuId()), dto.getWarehouseId());

        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(dto.getWarehouseId());
        batchInventoryQueryDTO.setSkuIds(Collections.singletonList(dto.getProductSkuId()));
        batchInventoryQueryDTO.setLocationCategory(CategoryEnum.CARGO_LOCATION.getValue());
        batchInventoryQueryDTO
                .setSubCategoryList(Arrays.asList(LocationEnum.零拣位.getType(), LocationEnum.分拣位.getType()));
        // batchInventoryQueryDTO
        // .setSubCategoryList(Arrays.asList(LocationAreaEnum.拣货区.getType(), LocationAreaEnum.零拣区.getType()));
        // 自动移库排除促销货位
        batchInventoryQueryDTO.setExcludeLocationBusinessTypes(Arrays.asList(LocationBusinessTypeEnum.促销.getType()));
        PageList<BatchInventoryDTO> pageList =
                iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
        LOGGER.info("4.0查到货位库存！， 入参 :{}", JSON.toJSONString(pageList));
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            LOGGER.info("没查到货位库存！， 入参 :{}", JSON.toJSONString(batchInventoryQueryDTO));
            return;
        }

        List<Long> locationIds = pageList.getDataList().stream().map(BatchInventoryDTO::getLocationId).distinct()
                .collect(Collectors.toList());
        locationIds.add(dto.getLocationId());

        List<LoactionDTO> locationList = locationPOMapper.findLocationByIds(locationIds);
        Map<Long, LoactionDTO> locationMap =
                locationList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));

        String userName = iAdminUserQueryService.queryUserNameById(dto.getUserId().longValue());
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(dto.getWarehouseId());

        Date currentTime = new Date();
        StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
        storeTransferOrderDTO.setOrg_id(warehouse.getCityId());
        // storeTransferOrderDTO.setStoreTransferNo();
        storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
        storeTransferOrderDTO.setStartTime(currentTime);
        // storeTransferOrderDTO.setFinishTime();
        storeTransferOrderDTO.setWarehouse_Id(dto.getWarehouseId());
        storeTransferOrderDTO.setWarehouseName(warehouse.getName());
        storeTransferOrderDTO.setSorter_id(dto.getUserId());
        storeTransferOrderDTO.setSorterName(userName);
        storeTransferOrderDTO.setRemark("货位关联");
        storeTransferOrderDTO.setCreateUser(userName);
        storeTransferOrderDTO.setStartTimeStr(DateUtil.formatTime(currentTime));
        // storeTransferOrderDTO.setIgnoreProductionDate();
        // storeTransferOrderDTO.setIgnoreHasNotEnoughStore();
        // storeTransferOrderDTO.setProcessLocationInventory();
        // storeTransferOrderDTO.setLocationIds();
        // storeTransferOrderDTO.setStoreTransferId();
        // storeTransferOrderDTO.setOperateUser();

        List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = pageList.getDataList().stream().map(inventory -> {
            StoreTransferOrderItemDTO itemDTO = new StoreTransferOrderItemDTO();
            LoactionDTO newLocation = locationMap.get(dto.getLocationId());
            LoactionDTO oldLocation = locationMap.get(inventory.getLocationId());

            if (dto.getLocationId().equals(inventory.getLocationId())) {
                return null;
            }

            itemDTO.setProductSpecificationId(inventory.getProductSpecificationId());
            itemDTO.setSecOwnerId(inventory.getSecOwnerId());
            itemDTO.setBatchTime(inventory.getBatchTime());
            itemDTO.setExpireTime(inventory.getExpireTime());
            itemDTO.setProductionDate(inventory.getProductionDate());
            itemDTO.setOrg_id(warehouse.getCityId());
            // itemDTO.setStoreTransfer_id();
            itemDTO.setState(StoreTransferStateEnum.待移库.getType());
            itemDTO.setOwner_id(inventory.getOwnerId());
            itemDTO.setOwnerName(inventory.getOwnerName());
            itemDTO.setSkuId(inventory.getProductSkuId());
            itemDTO.setProductName(inventory.getProductSkuName());
            // itemDTO.setProductBrand();
            // itemDTO.setCategoryName();
            itemDTO.setSpecName(inventory.getSpecificationName());
            itemDTO.setSpecQuantity(inventory.getPackageQuantity());
            itemDTO.setPackageName(inventory.getPackageName());
            BigDecimal[] count = inventory.getStoreTotalCount().divideAndRemainder(inventory.getPackageQuantity());
            itemDTO.setPackageCount(count[0]);
            itemDTO.setUnitName(inventory.getUnitName());
            itemDTO.setUnitCount(count[1]);
            // itemDTO.setUnitTotalCount();
            itemDTO.setOverMovePackageCount(count[0]);
            itemDTO.setOverMoveUnitCount(count[1]);
            itemDTO.setFromChannel(inventory.getChannel().toString());
            itemDTO.setToLocationName(newLocation.getName());
            itemDTO.setToChannel(inventory.getChannel().toString());
            itemDTO.setRemark("货位关联");
            itemDTO.setCreateUser(userName);
            // itemDTO.setCreatetime();
            // itemDTO.setLastupdateuser();
            // itemDTO.setLastupdatetime();
            // itemDTO.setId();
            itemDTO.setToLocation_id(newLocation.getId());
            itemDTO.setFromLocationName(oldLocation.getName());
            itemDTO.setFromLocation_id(oldLocation.getId());
            // itemDTO.setPackageCode();
            // itemDTO.setUnitCode();
            // itemDTO.setFromChannelText();
            // itemDTO.setToChannelText();
            // itemDTO.setStateText();
            itemDTO.setProductStoreId(inventory.getProductStoreId());
            itemDTO.setProductStoreBatchId(inventory.getStoreBatchId());
            itemDTO.setBatchAttributeInfoNo(inventory.getBatchAttributeInfoNo());
            // itemDTO.setAutoAllotFlag();
            // itemDTO.setFromLocationSubcategory();
            // itemDTO.setToLocationSubcategory();
            // itemDTO.setVesselId();
            // itemDTO.setVesselName();
            // itemDTO.setRelationOrderType();
            // itemDTO.setRelationProductionDate();

            return itemDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeTransferOrderItemDTOS)) {
            return;
        }
        storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
        LOGGER.info("4.0进行库存转移，参数：{}", JSON.toJSONString(storeTransferOrderDTO));
        iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
    }

    private void handle2p5plusLocation(ProductLocationDTO dto) {
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(dto.getWarehouseId());
        if (BooleanUtils.isFalse(warehouseConfigDTO.isOpen2p5plus())) {
            return;
        }

        LocationPO locationPO = locationPOMapper.findLocationById(dto.getLocationId());
        LocationPO areaPO = locationPOMapper.findLocationById(locationPO.getArea_Id());
        if (LocationAreaEnum.拣货区.getType().byteValue() != areaPO.getSubcategory()
                && LocationAreaEnum.零拣区.getType().byteValue() != areaPO.getSubcategory()) {
            return;
        }

        if(Objects.equals(locationPO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())
                || Objects.equals(areaPO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())){
            return;
        }

        List<ProductLoactionItemDTO> productLoactionItemDTOList = productLocationPOMapper
                .findLocationBySkuId(Collections.singletonList(dto.getProductSkuId()), dto.getWarehouseId());

        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(dto.getWarehouseId());
        batchInventoryQueryDTO.setSkuIds(Collections.singletonList(dto.getProductSkuId()));
        batchInventoryQueryDTO.setLocationCategory(CategoryEnum.CARGO_AREA.getValue());
        batchInventoryQueryDTO
                .setSubCategoryList(Arrays.asList(LocationAreaEnum.拣货区.getType(), LocationAreaEnum.零拣区.getType()));
        // 自动移库排除促销货位
        batchInventoryQueryDTO.setExcludeLocationBusinessTypes(Arrays.asList(LocationBusinessTypeEnum.促销.getType()));
        PageList<BatchInventoryDTO> pageList =
                iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
        LOGGER.info("查到货位库存！， 入参 :{}", JSON.toJSONString(pageList));
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            LOGGER.info("没查到货位库存！， 入参 :{}", JSON.toJSONString(batchInventoryQueryDTO));
            return;
        }

        List<Long> locationIds = pageList.getDataList().stream().map(BatchInventoryDTO::getLocationId).distinct()
                .collect(Collectors.toList());
        locationIds.add(dto.getLocationId());

        List<LoactionDTO> locationList = locationPOMapper.findLocationByIds(locationIds);
        Map<Long, LoactionDTO> locationMap =
                locationList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));

        String userName = iAdminUserQueryService.queryUserNameById(dto.getUserId().longValue());
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(dto.getWarehouseId());

        Date currentTime = new Date();
        StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
        storeTransferOrderDTO.setOrg_id(warehouse.getCityId());
        // storeTransferOrderDTO.setStoreTransferNo();
        storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
        storeTransferOrderDTO.setStartTime(currentTime);
        // storeTransferOrderDTO.setFinishTime();
        storeTransferOrderDTO.setWarehouse_Id(dto.getWarehouseId());
        storeTransferOrderDTO.setWarehouseName(warehouse.getName());
        storeTransferOrderDTO.setSorter_id(dto.getUserId());
        storeTransferOrderDTO.setSorterName(userName);
        storeTransferOrderDTO.setRemark("货位关联");
        storeTransferOrderDTO.setCreateUser(userName);
        storeTransferOrderDTO.setStartTimeStr(DateUtil.formatTime(currentTime));
        // storeTransferOrderDTO.setIgnoreProductionDate();
        // storeTransferOrderDTO.setIgnoreHasNotEnoughStore();
        // storeTransferOrderDTO.setProcessLocationInventory();
        // storeTransferOrderDTO.setLocationIds();
        // storeTransferOrderDTO.setStoreTransferId();
        // storeTransferOrderDTO.setOperateUser();

        List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = pageList.getDataList().stream().map(inventory -> {
            StoreTransferOrderItemDTO itemDTO = new StoreTransferOrderItemDTO();
            LoactionDTO newLocation = locationMap.get(dto.getLocationId());
            LoactionDTO oldLocation = locationMap.get(inventory.getLocationId());

            itemDTO.setProductSpecificationId(inventory.getProductSpecificationId());
            itemDTO.setSecOwnerId(inventory.getSecOwnerId());
            itemDTO.setBatchTime(inventory.getBatchTime());
            itemDTO.setExpireTime(inventory.getExpireTime());
            itemDTO.setProductionDate(inventory.getProductionDate());
            itemDTO.setOrg_id(warehouse.getCityId());
            // itemDTO.setStoreTransfer_id();
            itemDTO.setState(StoreTransferStateEnum.待移库.getType());
            itemDTO.setOwner_id(inventory.getOwnerId());
            itemDTO.setOwnerName(inventory.getOwnerName());
            itemDTO.setSkuId(inventory.getProductSkuId());
            itemDTO.setProductName(inventory.getProductSkuName());
            // itemDTO.setProductBrand();
            // itemDTO.setCategoryName();
            itemDTO.setSpecName(inventory.getSpecificationName());
            itemDTO.setSpecQuantity(inventory.getPackageQuantity());
            itemDTO.setPackageName(inventory.getPackageName());
            BigDecimal[] count = inventory.getStoreTotalCount().divideAndRemainder(inventory.getPackageQuantity());
            itemDTO.setPackageCount(count[0]);
            itemDTO.setUnitName(inventory.getUnitName());
            itemDTO.setUnitCount(count[1]);
            // itemDTO.setUnitTotalCount();
            itemDTO.setOverMovePackageCount(count[0]);
            itemDTO.setOverMoveUnitCount(count[1]);
            itemDTO.setFromChannel(inventory.getChannel().toString());
            itemDTO.setToLocationName(newLocation.getName());
            itemDTO.setToChannel(inventory.getChannel().toString());
            itemDTO.setRemark("货位关联");
            itemDTO.setCreateUser(userName);
            // itemDTO.setCreatetime();
            // itemDTO.setLastupdateuser();
            // itemDTO.setLastupdatetime();
            // itemDTO.setId();
            itemDTO.setToLocation_id(newLocation.getId());
            itemDTO.setFromLocationName(oldLocation.getName());
            itemDTO.setFromLocation_id(oldLocation.getId());
            // itemDTO.setPackageCode();
            // itemDTO.setUnitCode();
            // itemDTO.setFromChannelText();
            // itemDTO.setToChannelText();
            // itemDTO.setStateText();
            itemDTO.setProductStoreId(inventory.getProductStoreId());
            itemDTO.setProductStoreBatchId(inventory.getStoreBatchId());
            itemDTO.setBatchAttributeInfoNo(inventory.getBatchAttributeInfoNo());
            // itemDTO.setAutoAllotFlag();
            // itemDTO.setFromLocationSubcategory();
            // itemDTO.setToLocationSubcategory();
            // itemDTO.setVesselId();
            // itemDTO.setVesselName();
            // itemDTO.setRelationOrderType();
            // itemDTO.setRelationProductionDate();

            return itemDTO;
        }).collect(Collectors.toList());
        storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
        LOGGER.info("进行库存转移，参数：{}", JSON.toJSONString(storeTransferOrderDTO));
        iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
    }

    /**
     * 验证货位是否不允许混放，不允许混放的货位不能关联多个产品
     */
    private void checklocationMixup(Long productSkuId, Long locationId, Integer warehouseId) {
        Long productLocationCount =
                productLocationPOMapper.getProductLocationByMixUpCount(productSkuId, locationId, warehouseId);
        if (productLocationCount > 0) {
            throw new BusinessValidateException("该货位不允许混放，不能同时关联多个产品！");
        }
    }

    /**
     * 删除产品货位
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeProductLocation(Long id, OperatorDTO operatorDTO) {
        AssertUtils.notNull(id, "id不能为空");
        ProductLocationPO productLocationPO = productLocationPOMapper.selectByPrimaryKey(id);
        if(Objects.isNull(productLocationPO)){
            LOGGER.info("[删除产品货位]货位不存在，id:{}", id);
            return;
        }

        // 开启分区的仓库，验证人 和 货区 是否属于一个分仓
        productLocationValidateBL.validateOperationLegal(ProductLocationValidateOperationLegalBO.ofLocationIds(
                operatorDTO.getUserId(), productLocationPO.getWarehouse_Id(), Collections.singletonList(productLocationPO.getLocation_Id())));

        productLocationPOMapper.deleteByPrimaryKey(id);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(operatorDTO.getUserId(), Collections.singletonList(productLocationPO));
        LOGGER.info("[删除产品货位]{}, 操作人：{}", JSON.toJSONString(productLocationPO), JSON.toJSONString(operatorDTO));
    }

    /**
     * 批量删除产品货位
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeProductLocationBatch(List<Long> ids, OperatorDTO operatorDTO) {
        AssertUtils.notEmpty(ids, "ids不能为空");
        List<ProductLocationPO> productLocationPOS = productLocationPOMapper.selectByIds(ids);
        if(CollectionUtils.isEmpty(productLocationPOS)){
            LOGGER.info("[批量删除产品货位]货位id不存在：{}", JSON.toJSONString(ids));
            return;
        }

        Integer warehouseId = productLocationPOS.stream().findFirst().get().getWarehouse_Id();
        List<Long> locationIds = productLocationPOS.stream().map(ProductLocationPO :: getLocation_Id).distinct().collect(Collectors.toList());
        // 开启分区的仓库，验证人 和 货区 是否属于一个分仓
        productLocationValidateBL.validateOperationLegal(
                ProductLocationValidateOperationLegalBO.ofLocationIds(operatorDTO.getUserId(), warehouseId, locationIds));

        productLocationPOMapper.deleteBatch(ids);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(operatorDTO.getUserId(), productLocationPOS);
        LOGGER.info("[批量删除产品货位]{}, 操作人：{}", JSON.toJSONString(productLocationPOS), JSON.toJSONString(operatorDTO));
    }

    /**
     * 导入产品货位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void importProductLocation(ProductLocationImportDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getUserId(), "操作人Id不能为空");
        AssertUtils.notEmpty(dto.getItemList(), "货位不能为空");

        // 1、验证导入的数据是否存在重复
        List<ProductLocationImportItemDTO> distinctList = validateRepet(dto.getItemList());
        LOGGER.info("待导入列表: {}", JSON.toJSONString(distinctList));

        // 2、验证产品skuId和货位名称是否存在
        // 2.1 根据导入的货位名称查询存在的货位id
        List<String> locationNameList = distinctList.stream().map(ProductLocationImportItemDTO::getLocationName)
                .distinct().collect(Collectors.toList());
        List<LocationPO> locationPOList =
                locationPOMapper.selectByNames(locationNameList, dto.getWarehouseId(), dto.getCityId());
        Map<String, Long> locationNameMap =
                locationPOList.stream().collect(Collectors.toMap(p -> p.getName(), p -> p.getId()));
        LOGGER.info("货位名称Map: {}", JSON.toJSONString(locationNameMap));
        // 2.2 根据导入的skuId查询存在的skuId
        List<Long> disProductSkuIdList = distinctList.stream().map(ProductLocationImportItemDTO::getProductSkuId)
                .distinct().collect(Collectors.toList());
        List<ProductSkuPO> productSkuPOList = productSkuPOMapper.getProductSkuInfoBySkuList(disProductSkuIdList);
        List<Long> productSkuIdList =
                productSkuPOList.stream().map(ProductSkuPO::getProductSku_Id).collect(Collectors.toList());
        LOGGER.info("产品SkuIdList: {}", JSON.toJSONString(productSkuIdList));
        // 2.3 查询所有产品货位记录
        ProductLocationSO productLocationSO = new ProductLocationSO();
        productLocationSO.setCityId(dto.getCityId());
        productLocationSO.setWarehouseId(dto.getWarehouseId());
        List<ProductLocationPO> productLocationList =
                productLocationPOMapper.listProductLocationSelective(productLocationSO);
        LOGGER.info("productLocationPOList: {}", JSON.toJSONString(productLocationList));
        // 2.4 遍历数据进行验证
        List<ProductLocationPO> productLocationPOList = new ArrayList<>();
        Date now = new Date();
        distinctList.forEach(item -> {
            if (!productSkuIdList.contains(item.getProductSkuId())) {
                throw new DataValidateException(String.format("产品skuId: %s不存在", item.getProductSkuId()));
            }
            if (null == locationNameMap.get(item.getLocationName())) {
                throw new DataValidateException(String.format("货位: %s不存在", item.getLocationName()));
            }
            if (!productLocationList.stream().anyMatch(e -> e.getProductSku_Id().equals(item.getProductSkuId())
                    && e.getLocation_Id().equals(locationNameMap.get(item.getLocationName())))) {
                ProductLocationPO po = new ProductLocationPO();
                po.setId(productIdGenerator.generatorLocationId(dto.getCityId()));
                po.setCity_Id(dto.getCityId());
                po.setWarehouse_Id(dto.getWarehouseId());
                po.setProductSku_Id(item.getProductSkuId());
                po.setLocation_Id(locationNameMap.get(item.getLocationName()));
                po.setCreateTime(now);
                po.setCreateUserId(dto.getUserId());
                productLocationPOList.add(po);
            }
        });
        if (CollectionUtils.isNotEmpty(productLocationPOList)) {
            // 3、批量保存操作
            LOGGER.info("导入产品货位: {}", JSON.toJSONString(productLocationPOList));
            productLocationHelper.insertBatch(productLocationPOList);
        }
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(dto.getUserId(), productLocationPOList);
    }

    /**
     * 验证导入记录是否重复
     *
     * @param list
     */
    private List<ProductLocationImportItemDTO> validateRepet(List<ProductLocationImportItemDTO> list) {
        List<ProductLocationImportItemDTO> distinctList = new ArrayList<>();
        list.forEach(dto -> {
            AssertUtils.notNull(dto.getProductSkuId(), "产品skuId不能为空");
            AssertUtils.notNull(dto.getLocationName(), "货位名称不能为空");
            dto.setLocationName(dto.getLocationName().trim());
            if (!distinctList.stream().anyMatch(e -> dto.getProductSkuId().equals(e.getProductSkuId())
                    && dto.getLocationName().equals(e.getLocationName()))) {
                distinctList.add(dto);
            }
        });
        return distinctList;
    }

    /**
     * 获取所有货区及货位信息
     *
     * @param warehouseId
     * @return
     */
    public List<LocationAreaInfoDTO> listLocation(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        // 根据仓库id获取所有货位
        List<LocationPO> locationPOList = locationPOMapper.listLocation(warehouseId, null);
        // List<LocationPO> locationPOList =
        // LocationConvetor.reportToLocationPO(this.listAllLocationReport(warehouseId,null));
        List<LocationAreaInfoDTO> locationAreaInfoDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(locationPOList)) {
            // 按货区id分组
            Map<Long, List<LocationPO>> areaMap = locationPOList.stream().filter(p -> null != p.getArea_Id())
                    .collect(Collectors.groupingBy(LocationPO::getArea_Id));
            areaMap.forEach((areaId, poList) -> {
                // 获取货区
                LocationAreaInfoDTO locationAreaInfoDTO = new LocationAreaInfoDTO();
                locationAreaInfoDTO.setLocationAreaId(areaId);
                if (CollectionUtils.isNotEmpty(poList)) {
                    locationAreaInfoDTO.setLocationAreaName(poList.get(0).getArea());
                    List<LocationRoadWayInfoDTO> roadWayList = new ArrayList<>();
                    // 根据巷道分组
                    Map<String, List<LocationPO>> roadWayMap =
                            poList.stream().filter(p -> StringUtils.isNotEmpty(p.getRoadWay()))
                                    .collect(Collectors.groupingBy(LocationPO::getRoadWay));
                    roadWayMap.forEach((roadWay, locationList) -> {
                        // 获取巷道
                        LocationRoadWayInfoDTO roadWayInfoDTO = new LocationRoadWayInfoDTO();
                        roadWayInfoDTO.setRoadWayName(roadWay);
                        if (CollectionUtils.isNotEmpty(locationList)) {
                            // 获取货位
                            List<LocationInfoDTO> locationInfoDTOList = new ArrayList<>();
                            for (LocationPO locationPO : locationList) {
                                if (locationInfoDTOList.stream().filter(s -> !ObjectUtils.isEmpty(s.getLocationId()))
                                        .anyMatch(s -> s.getLocationId().compareTo(locationPO.getId()) == 0)) {
                                    continue;
                                }
                                LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
                                locationInfoDTO.setLocationId(locationPO.getId());
                                locationInfoDTO.setLocationName(locationPO.getName());
                                locationInfoDTOList.add(locationInfoDTO);
                            }
                            roadWayInfoDTO.setLocationList(locationInfoDTOList);
                        }
                        roadWayList.add(roadWayInfoDTO);

                    });
                    locationAreaInfoDTO.setRoadWayList(roadWayList);
                }
                locationAreaInfoDTOList.add(locationAreaInfoDTO);
            });
        }
        return locationAreaInfoDTOList;
    }

    /**
     * 根据货位模糊查找匹配的所有货位
     *
     * @param warehouseId
     * @param locatinName
     * @return
     */
    public List<LocationInfoDTO> listLocationByName(Integer warehouseId, String locatinName) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(locatinName, "货位名称不能为空");
        // List<LocationPO> locationPOList = locationPOMapper.listLocation(warehouseId, locatinName);
        List<ReportLocationDTO> locationPOList = this.listAllLocationReport(warehouseId, locatinName);
        List<LocationInfoDTO> locationInfoDTOList = locationPOList.stream().map(po -> {
            LocationInfoDTO dto = new LocationInfoDTO();
            dto.setLocationId(po.getId());
            dto.setLocationName(po.getName());
            return dto;
        }).collect(Collectors.toList());
        LOGGER.info("模糊查找匹配的货位:{}", JSON.toJSONString(locationInfoDTOList));
        return locationInfoDTOList;
    }

    /**
     * 根据货位名称查找货位
     *
     * @param warehouseId
     * @param cityId
     * @param locatinName
     * @return
     */
    public LoactionDTO getLocationIdByName(Integer warehouseId, Integer cityId, String locatinName) {
        LocationPO po = locationPOMapper.selectByName(locatinName, warehouseId, cityId);
        if (null != po) {
            LoactionDTO loactionDTO = new LoactionDTO();
            BeanUtils.copyProperties(po, loactionDTO);
            return loactionDTO;
        }
        return null;
    }

    /**
     * 根据skuId列表查询所有货位产品关联信息
     *
     * <AUTHOR>
     */
    public List<ProductLocationDTO> listProductLocationPOBySkuIdList(List<Long> skuIdList) {
        List<ProductLocationPO> productLocationPOList = productLocationPOMapper.selectBySkuIds(skuIdList);
        List<ProductLocationDTO> productLocationDTOList = null;
        if (!CollectionUtils.isEmpty(productLocationPOList)) {
            productLocationDTOList = productLocationPOList.stream().map(productLocationPO -> {
                ProductLocationDTO productLocationDTO = new ProductLocationDTO();
                productLocationDTO.setProductSkuId(productLocationPO.getProductSku_Id());
                productLocationDTO.setLocationId(productLocationPO.getLocation_Id());
                return productLocationDTO;
            }).collect(Collectors.toList());
        }
        return productLocationDTOList;
    }

    /**
     * 批量添加货位商品关联信息
     *
     * @param productLocationDTOList
     * @return
     * <AUTHOR>
     */
    public void insertBatch(List<ProductLocationDTO> productLocationDTOList) {
        AssertUtils.notEmpty(productLocationDTOList, "产品关联货位列表不能为空");
        productLocationDTOList.forEach(p -> {
            AssertUtils.notNull(p.getCityId(), "关联货位的城市ID不能为空");
            AssertUtils.notNull(p.getWarehouseId(), "关联货位的仓库ID不能为空");
            AssertUtils.notNull(p.getProductSkuId(), "关联货位的skuID不能为空");
            AssertUtils.notNull(p.getLocationId(), "关联货位的货位ID不能为空");
        });

        // 检查已存在产品关联货位，过滤出需要删除的ids
        validateInsertBatch(productLocationDTOList);

        List<ProductLocationPO> productLocationPOList =
                productLocationDTOList.stream().filter(p -> checkSkuIdAndLocationId(p)).map(productLocationDTO -> {
                    ProductLocationPO productLocationPO = getProductLocationPO(productLocationDTO);
                    return productLocationPO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productLocationPOList)) {
            return;
        }

        productLocationHelper.insertBatch(productLocationPOList);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(null, productLocationPOList);
    }

    /**
     * 验证sku和货位是否存在
     *
     * @return
     */
    private boolean checkSkuIdAndLocationId(ProductLocationDTO productLocationDTO) {
        if (productLocationDTO == null) {
            return false;
        }
        // 验证sku是否存在
        ProductSkuPO productSkuPO =
                productSkuPOMapper.selectByCityIdAndProductSkuId(productLocationDTO.getProductSkuId());
        if (productSkuPO == null || !Objects.equals(productLocationDTO.getCityId(), productSkuPO.getCity_Id())) {
            return false;
        }

        // 验证货位是否存在
        LocationPO locationPO = locationPOMapper.findLocationById(productLocationDTO.getLocationId());
        if (locationPO == null || !Objects.equals(productLocationDTO.getCityId(), locationPO.getCity_Id())
                || !Objects.equals(productLocationDTO.getWarehouseId(), locationPO.getWarehouse_Id())) {
            return false;
        }
        return true;
    }

    private ProductLocationPO getProductLocationPO(ProductLocationDTO productLocationDTO) {
        ProductLocationPO productLocationPO = new ProductLocationPO();
        productLocationPO.setId(productIdGenerator.generatorLocationId(productLocationDTO.getCityId()));
        productLocationPO.setCity_Id(productLocationDTO.getCityId());
        productLocationPO.setWarehouse_Id(productLocationDTO.getWarehouseId());
        productLocationPO.setProductSku_Id(productLocationDTO.getProductSkuId());
        productLocationPO.setLocation_Id(productLocationDTO.getLocationId());
        productLocationPO.setCreateTime(new Date());
        productLocationPO.setCreateUserId(productLocationDTO.getUserId());
        productLocationPO.setLastUpdateTime(productLocationPO.getCreateTime());
        productLocationPO.setLastUpdateUserId(productLocationDTO.getUserId());
        return productLocationPO;
    }

    /**
     * 批量添加货位商品关联信息（排除已存在的关联货位）
     *
     * @return
     */
    public void insertBatchExcludeExist(List<ProductLocationDTO> productLocationDTOList) {
        AssertUtils.notEmpty(productLocationDTOList, "产品关联货位列表不能为空");

        List<ProductLocationPO> productLocationPOList = new ArrayList<>();
        for (ProductLocationDTO productLocationDTO : productLocationDTOList) {
            // 当前产品关联货位设置不存在时，才新增
            if (!isExistProductLocation(productLocationDTO.getWarehouseId(), productLocationDTO.getProductSkuId(),
                productLocationDTO.getLocationId())) {
                ProductLocationPO productLocationPO = getProductLocationPO(productLocationDTO);
                productLocationPOList.add(productLocationPO);
            }
        }
        if (CollectionUtils.isEmpty(productLocationPOList)) {
            return;
        }

        productLocationHelper.insertBatch(productLocationPOList);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(null, productLocationPOList);
    }

    /**
     * 判断当前产品关联货位设置是否已经存在
     *
     * @param warehouseId
     * @param productSkuId
     * @param locationId
     * @return
     */
    public Boolean isExistProductLocation(Integer warehouseId, Long productSkuId, Long locationId) {
        Long productLocationCount =
                productLocationPOMapper.getProductLocationCount(productSkuId, locationId, warehouseId);
        if (productLocationCount > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 检查关联货位 1: 不允许混放的货位，不允许关联多个产品 2: 允许混放的货位，需要提示出已经过关联的产品
     *
     * @return
     */
    public ProductLocationCheckDTO checkProductLocation(ProductLocationCheckSO productLocationCheckSO) {
        AssertUtils.notNull(productLocationCheckSO, "参数不能为空");
        AssertUtils.notNull(productLocationCheckSO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(productLocationCheckSO.getItems(), "关联信息不能为空");
        productLocationCheckSO.getItems().forEach(p -> {
            AssertUtils.notNull(p.getLocationId(), "货位id不能为空");
            AssertUtils.notNull(p.getProductSkuId(), "skuId不能为空");
        });

        ProductLocationCheckDTO productLocationCheckDTO = new ProductLocationCheckDTO();
        productLocationCheckDTO.setType(ProductLocationCheckEnum.允许关联.getType());

        // 1、根据货位ID查询货位信息及关联的产品skuId
        List<Long> locationIds = productLocationCheckSO.getItems().stream().map(p -> p.getLocationId()).distinct()
                .collect(Collectors.toList());
        List<ProductLoactionItemDTO> productLoactionList =
                productLocationPOMapper.findLocationByLocationIds(locationIds, productLocationCheckSO.getWarehouseId());
        if (CollectionUtils.isEmpty(productLoactionList)) {
            throw new BusinessException("查不到货位信息！");
        }

        List<ProductLocationCheckItemDTO> notMixUpItem = new ArrayList<>();
        List<ProductLocationCheckItemDTO> mixUpItem = new ArrayList<>();
        // 2、根据货位ID分组
        Map<Long, List<ProductLocationCheckItemSO>> checkMap =
                productLocationCheckSO.getItems().stream().collect(Collectors.groupingBy(p -> p.getLocationId()));
        if (checkMap != null) {
            checkMap.forEach((locationId, checkItem) -> {
                List<ProductLoactionItemDTO> filterList = productLoactionList.stream()
                        .filter(p -> Objects.equals(p.getLocationId(), locationId)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterList)) {
                    throw new BusinessException("货位不存在，locationId: " + locationId);
                }
                ProductLoactionItemDTO loactionItemDTO = filterList.get(0);
                // 不允许混放
                if (Objects.equals(loactionItemDTO.getIsChaosPut(), ConditionStateEnum.否.getType())) {

                    // 如果待检查的货位同时存在多个sku，或者该货位已经关联了其他的产品，则不允许关联
                    boolean isCannotContine = checkItem.stream().map(p -> p.getProductSkuId()).distinct()
                            .collect(Collectors.toList()).size() > 1
                            || filterList.stream()
                            .filter(p -> p.getProductSkuId() != null
                                    && !Objects.equals(p.getProductSkuId(), checkItem.get(0).getProductSkuId()))
                            .collect(Collectors.toList()).size() > 0;
                    if (isCannotContine) {
                        ProductLocationCheckItemDTO checkDTO = new ProductLocationCheckItemDTO();
                        checkDTO.setLocationName(loactionItemDTO.getLocationName());
                        notMixUpItem.add(checkDTO);
                    }

                    // 允许混放
                } else {
                    // 如果当前货位关联了其他产品，则提示出来
                    List<ProductLoactionItemDTO> productlocations =
                            filterList.stream().filter(p -> p.getProductSkuId() != null).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(productlocations)) {
                        Set<String> productNames = new HashSet<>();
                        productlocations.forEach(pl -> {
                            if (!checkItem.stream()
                                    .anyMatch(p -> Objects.equals(p.getProductSkuId(), pl.getProductSkuId()))) {
                                productNames.add(pl.getProductName());
                            }
                        });
                        if (CollectionUtils.isNotEmpty(productNames)) {
                            ProductLocationCheckItemDTO checkDTO = new ProductLocationCheckItemDTO();
                            checkDTO.setLocationName(loactionItemDTO.getLocationName());
                            checkDTO.setRelateProductList(new ArrayList<>(productNames));
                            mixUpItem.add(checkDTO);
                        }
                    }
                }
            });
        }

        // 3、返回类型
        if (CollectionUtils.isNotEmpty(notMixUpItem)) {
            productLocationCheckDTO.setType(ProductLocationCheckEnum.不允许关联.getType());
            productLocationCheckDTO.setItems(notMixUpItem);
        } else if (CollectionUtils.isNotEmpty(mixUpItem)) {
            productLocationCheckDTO.setType(ProductLocationCheckEnum.需要提示.getType());
            productLocationCheckDTO.setItems(mixUpItem);
        }

        return productLocationCheckDTO;
    }

    public void batchAdd(List<LoactionDTO> loactionDTOS, Integer warehouseId, Integer cityId, Integer userId) {
        List<String> locationNames = new ArrayList<>();
        List<LocationPO> locationPOS = new ArrayList<>();
        // 添加库位信息
        loactionDTOS.forEach(dto -> {
            LocationPO record = new LocationPO(dto);
            record.setCreateTime(new Date());
            record.setCreateUserId(dto.getUserId());
            String locationName = getName(dto, true);
            locationNames.add(locationName);
            record.setName(dto.getName() == null ? locationName : dto.getName());
            record.setId(UUIDGenerator.getUUID(LocationPO.class.getName()));
            locationPOS.add(record);
        });
        if (CollectionUtils.isEmpty(locationNames)) {
            LOGGER.info("名称不存在:{}", JSON.toJSONString(locationNames));
            return;
        }
        List<LocationPO> existNames = locationPOMapper.selectByNames(locationNames, warehouseId, cityId);
        if (CollectionUtils.isNotEmpty(existNames)) {
            throw new BusinessValidateException("货位名称重复，已有名称为" + existNames.get(0).getName() + "的货位！");
        }

        if (CollectionUtils.isEmpty(locationPOS)) {
            LOGGER.info("库位不存在:{}", JSON.toJSONString(locationPOS));
            return;
        }
        locationPOMapper.batchInsert(locationPOS, warehouseId, cityId);
        Map<String, List<LocationPO>> listMap =
                locationPOS.stream().collect(Collectors.groupingBy(LocationPO::getName));
        // 添加关系表信息
        List<ProductLocationPO> relPOs = new ArrayList<>();
        for (LoactionDTO dto : loactionDTOS) {
            if (dto.getProductSkuIdList() != null && !dto.getProductSkuIdList().isEmpty()) {
                for (Long productSkuId : dto.getProductSkuIdList()) {
                    List<LocationPO> pos = listMap.get(dto.getName());
                    if (CollectionUtils.isNotEmpty(pos)) {
                        ProductLocationPO relPO = new ProductLocationPO();
                        relPO.setCity_Id(cityId);
                        relPO.setLocation_Id(pos.get(0).getId());
                        relPO.setProductSku_Id(productSkuId);
                        relPO.setWarehouse_Id(warehouseId);
                        relPO.setId(productIdGenerator.generatorLocationId(cityId));
                        relPO.setCreateTime(new Date());
                        relPO.setCreateUserId(userId);
                        relPOs.add(relPO);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(relPOs)) {
            LOGGER.info("库位关联产品不存在:{}", JSON.toJSONString(locationPOS));
            return;
        }
        productLocationHelper.insertBatch(relPOs);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(userId, relPOs);
    }

    /**
     * 根据产品配置货位信息查询相关产品SKU基础信息
     */
    public PageList<ProductSkuBaseInfoDTO> findProductBaseInfoByProductLocation(ProductLocationListSO so) {
        LOGGER.info("根据产品配置货位信息查询相关产品SKU基础信息参数：{}", JSON.toJSONString(so));
        AssertUtils.notNull(so, "查询参数不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        // 查询产品货位配置列表
        PageResult<ProductSkuBaseInfoDTO> pageResult = productLocationPOMapper.findProductBaseInfoByProductLocation(so);
        if (CollectionUtils.isNotEmpty(pageResult)) {
            pageResult.stream().filter(Objects::nonNull).forEach(baseInfo -> {
                if (null != baseInfo.getSaleModel()) {
                    baseInfo.setSaleModelName(ProductSaleMode.getEnumName((int) baseInfo.getSaleModel()));
                }
            });
        }
        return pageResult.toPageList();
    }

    /**
     * 根据产品SKU信息查询仓库产品配置货位信息
     */
    public List<ProductLocationListDTO> findProductLocationBySkuInfo(ProductLocationListSO query) {
        LOGGER.info("根据产品SKU查询产品仓库配置货位信息参数：{}", JSON.toJSONString(query));
        AssertUtils.notNull(query, "查询参数不能为空");
        AssertUtils.notNull(query.getWarehouseId(), "仓库ID不能为空");
        List<ProductLocationListPO> productLocationList = productLocationPOMapper.findProductLocationBySkuInfo(query);
        if (CollectionUtils.isNotEmpty(productLocationList)) {
            return productLocationList.stream().filter(Objects::nonNull).map(po -> {
                ProductLocationListDTO dto = new ProductLocationListDTO();
                BeanUtils.copyProperties(po, dto);
                if (null != po.getSubcategory()) {
                    dto.setSubcategoryName(LocationEnum.getEnumStr(Integer.valueOf(po.getSubcategory())));
                }
                if (null != po.getSaleModel()) {
                    dto.setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(po.getSaleModel())));
                }
                return dto;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 查找货位报表（所有）
     */
    public List<ReportLocationDTO> listAllLocationReport(Integer warehouseId, String locationName) {
        int pageSize = 5000;

        ReportLocationQueryDTO queryDTO = new ReportLocationQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationName(locationName);

        List<ReportLocationDTO> resultList = new ArrayList<>();
        Integer pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {

            queryDTO.setPageNum(pageNum);
            queryDTO.setPageSize(pageSize);
            PageList<ReportLocationDTO> pageList = iLocationReportService.pageListLocation(queryDTO);
            if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
                if (pageNum == 1) {
                    pageCount = pageList.getPager().getTotalPage();
                }
                resultList.addAll(pageList.getDataList());
                pageList.getDataList().clear();
            }
        }
        return resultList;
    }

    public List<EnumDTO> productLocationList(CategoryTypeDTO categoryTypeDTO) {
        List<EnumDTO> locationEnumDTOS = new ArrayList<>();
        if (categoryTypeDTO.getCategory() == 1) {
            for (LocationAreaEnum locationAreaEnum : LocationAreaEnum.values()) {
                EnumDTO dto = new EnumDTO();
                dto.setType(locationAreaEnum.getType());
                dto.setTypeName(locationAreaEnum.name());
                locationEnumDTOS.add(dto);
            }
        }
        if (categoryTypeDTO.getCategory() == 0) {
            for (LocationEnum locationEnum : LocationEnum.values()) {
                EnumDTO dto = new EnumDTO();
                dto.setType(locationEnum.getType());
                dto.setTypeName(locationEnum.name());
                locationEnumDTOS.add(dto);
            }
        }
        return locationEnumDTOS;
    }

    /**
     * 根据SKUID集合查找配置货位
     *
     * @param queryDTO
     * @return
     */
    public List<ProductLoactionItemDTO> findLocationItemByCon(ProductLocationItemQueryDTO queryDTO) {
        List<ProductLoactionItemDTO> locationItemDTOList = productLocationPOMapper.findLocationItemByCon(queryDTO);
        return locationItemDTOList;
    }

    /**
     * 根据货位查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    public List<ProductLoactionSkuInfoDTO> findProductLocationGroup(LocationProductQuery queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        List<ProductLoactionSkuInfoDTO> locationItemDTOList =
                productLocationPOMapper.findProductByRelationLocation(queryDTO);
        return locationItemDTOList;
    }

    /**
     * 根据货区查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    public List<ProductLoactionSkuInfoDTO> findProductLocationGroupByArea(LocationProductQuery queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        List<ProductLoactionSkuInfoDTO> locationItemDTOList =
                productLocationPOMapper.findProductByLocationGroup(queryDTO);
        if (CollectionUtils.isEmpty(locationItemDTOList)) {
            return locationItemDTOList;
        }

        try {
            // 如果有上架的产品就只返回上架的, 否则就返回所有数据
            return locationItemDTOList.stream().collect(Collectors.groupingBy(this::getGroupName)).values().stream()
                    .map(this::validSkuFirst).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.warn("数据处理异常, 返回所有产品货位数据: ", e);
        }

        return locationItemDTOList;
    }

    /**
     * 按照 规格-货主-二级货主 来分组
     *
     * @param loactionSkuInfoDTO
     */
    private String getGroupName(ProductLoactionSkuInfoDTO loactionSkuInfoDTO) {
        Long specId = loactionSkuInfoDTO.getProductSpecificationId();
        Long ownerId = loactionSkuInfoDTO.getCompanyId();
        Long secOwnerId = loactionSkuInfoDTO.getSecOwnerId();
        return String.format("%s-%s-%s", specId, ownerId, secOwnerId);
    }

    /**
     * 优先返回上架的一条 sku 数据, 如果没有则返回下架的一条数据, 如果都没有那就返回空 list
     *
     * @param infoDTOS 原始数据
     */
    private List<ProductLoactionSkuInfoDTO> validSkuFirst(List<ProductLoactionSkuInfoDTO> infoDTOS) {
        if (CollectionUtils.isEmpty(infoDTOS)) {
            return Collections.emptyList();
        }

        Optional<ProductLoactionSkuInfoDTO> validSkus = infoDTOS.stream()
                .filter(item -> ProductSkuStateEnum.上架.getType().equals(item.getProductState())).findFirst();

        // 只会返回一条数据
        return validSkus.map(Collections::singletonList).orElse(Collections.singletonList(infoDTOS.get(0)));
    }

    public List<LocationReturnDTO> findLocationListByCondition(LocationQueryDTO dto) {
        // TODO 为什么要查询出这么多数据：是否可以把整仓的数据做本地缓存定期刷新，在内存中做筛选
        LocationScrollQueryDTO scrollQuery = new LocationScrollQueryDTO();
        scrollQuery.setSkuId(dto.getSkuId());
        scrollQuery.setWarehouseId(dto.getWarehouseId());
        scrollQuery.setCityId(dto.getCityId());
        scrollQuery.setSkuIdList(dto.getSkuIdList());
        scrollQuery.setProductName(dto.getProductName());
        scrollQuery.setName(dto.getName());
        scrollQuery.setProductLocation(dto.getProductLocation());
        scrollQuery.setArea(dto.getArea());
        scrollQuery.setIsChaosPut(dto.getIsChaosPut());
        scrollQuery.setIsChaosBatch(dto.getIsChaosBatch());
        scrollQuery.setSubcategory(dto.getSubcategory());
        scrollQuery.setSubcategoryList(dto.getSubcategoryList());
        scrollQuery.setLocSubcategory(dto.getLocSubcategory());
        scrollQuery.setLocationId(dto.getLocationId());
        scrollQuery.setLocationIdList(dto.getLocationIdList());
        scrollQuery.setCategory(dto.getCategory());
        scrollQuery.setQueryRelatedProduct(dto.getQueryRelatedProduct());
        scrollQuery.setAreaState(dto.getAreaState());
        scrollQuery.setAreaIdList(dto.getAreaIdList());
        scrollQuery.setAreaId(dto.getAreaId());
        scrollQuery.setExpress(dto.getExpress());
        scrollQuery.setState(dto.getState());
        scrollQuery.setCategoryList(dto.getCategoryList());
        scrollQuery.setViewMode(dto.getViewMode());
        scrollQuery.setHasLocation(dto.getHasLocation());
        scrollQuery.setLocSubCategoryList(dto.getLocSubCategoryList());
        scrollQuery.setOnlyEmptyLocation(dto.getOnlyEmptyLocation());
        scrollQuery.setNoPalletCount(dto.getNoPalletCount());
        scrollQuery.setBusinessTypeList(dto.getBusinessTypeList());
        scrollQuery.setBatchCount(3000);

        Byte category = dto.getCategory();
        List<Byte> categoryList = new ArrayList<>();
        if (category == null) {
            categoryList.add(CategoryEnum.CARGO_LOCATION.getByteValue());
            categoryList.add(CategoryEnum.CARGO_AREA.getByteValue());
        } else {
            categoryList.add(category);
        }

        List<LocationPO> locationList = new ArrayList<>();
        for (Byte currentCategory : categoryList) {
            scrollQuery.setCategory(currentCategory);
            scrollQuery.setLastMaxId(0L);
            for (; ; ) {
                List<LocationPO> batchResult = locationPOMapper.pageListLocationByCondition(scrollQuery);
                locationList.addAll(batchResult);
                if (batchResult.size() != scrollQuery.getBatchCount()) {
                    break;
                }
                scrollQuery.setLastMaxId(batchResult.get(batchResult.size() - 1).getId());
            }
        }

        List<LocationReturnDTO> returnDTOS = Lists.transform(locationList, input -> {
            LocationReturnDTO dto1 = new LocationReturnDTO();
            BeanUtils.copyProperties(input, dto1);
            return dto1;
        });

        return returnDTOS;
    }

    /**
     * 根据条件查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    public List<ProductLoactionSkuInfoDTO> findProductByCondition(LocationProductQuery queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        List<ProductLoactionSkuInfoDTO> locationItemDTOList = productLocationPOMapper.findProductByCondition(queryDTO);
        if (CollectionUtils.isEmpty(locationItemDTOList)) {
            return locationItemDTOList;
        }

        try {
            // 如果有上架的产品就只返回上架的, 否则就返回所有数据
            return locationItemDTOList.stream().collect(Collectors.groupingBy(this::getGroupName)).values().stream()
                    .map(this::validSkuFirst).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.warn("数据处理异常, 返回所有产品货位数据: ", e);
        }

        return locationItemDTOList;
    }

    /**
     * 根据条件查找产品关联货位信息
     *
     * @param queryDTO
     * @return
     */
    public List<ProductLoactionSkuInfoDTO> findProductLocationByCondition(LocationProductQuery queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        List<ProductLoactionSkuInfoDTO> locationItemDTOList =
                productLocationPOMapper.findProductLocationByCondition(queryDTO);
        return locationItemDTOList;
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param userId
     * @param productLocationPOS
     */
    public void updateSkuConfigStorageAttribute(Integer userId, List<ProductLocationPO> productLocationPOS) {
        storageAttributeService.updateSkuConfigStorageAttribute(userId, productLocationPOS);
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param userId
     * @param warehouseId
     * @param skuIds
     */
    public void updateSkuConfigStorageAttribute(Integer userId, Integer warehouseId, List<Long> skuIds) {
        storageAttributeService.updateSkuConfigStorageAttribute(userId, warehouseId, skuIds);
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param userId
     * @param locationIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStorageAttributeByLocationIds(Integer userId, Integer warehouseId, List<Long> locationIds) {
        if (warehouseId == null || CollectionUtils.isEmpty(locationIds)) {
            return;
        }

        try {
            List<ProductLocationPO> productLocationPOS = productLocationPOMapper.findByLocationIds(locationIds);
            if (CollectionUtils.isEmpty(productLocationPOS)) {
                return;
            }

            List<Long> skuIds = productLocationPOS.stream().filter(p -> p.getProductSku_Id() != null)
                    .map(p -> p.getProductSku_Id()).distinct().collect(Collectors.toList());
            ProductConfigStorageAttributeUpdateDTO updateDTO = new ProductConfigStorageAttributeUpdateDTO();
            updateDTO.setWarehouseId(warehouseId);
            updateDTO.setProductSkuIdList(skuIds);
            updateDTO.setUserId(userId);
            storageAttributeService.updateStorageAttributeBySkuIds(updateDTO);
        } catch (Exception e) {
            LOGGER.error("更新产品配置分仓属性异常 ", e);
        }
    }

    /**
     * 产品关联货位查询
     *
     * @param param
     * @return
     */
    public PageList<ProductSkuInfoResultDTO> listProductLocationInfo(ProductInfoQueryParam param) {
        PageList<ProductSkuInfoResultDTO> pageList = new PageList<>();
        // 1、如果查询内容全部为数字，则表示按条码查询
        if (param.getContent().matches("^[0-9]+$")) {
            LOGGER.info("按条码查询...");
            pageList = this.findProductBaseInfoByCode(param);
        }

        // 2、如果上述条件没找到，则按产品名称查询
        // 2025-01-20 搜索条件包含中文且长度大于1再搜索
        if (CollectionUtils.isEmpty(pageList.getDataList())
                && !StringUtils.isEmpty(param.getContent())
                && param.getContent().length() > 1
                && param.getContent().matches(".*[\\u4e00-\\u9fa5].*")) {
            LOGGER.info("按产品名称查询...");
            pageList = this.listProductInfoByProductName(param);
        }

        if (Objects.equals(true, param.getLimitSku())) {
            // 移除仓库没导入的产品
            removeNotImportProduct(pageList.getDataList(), param.getWarehouseId(), param.getCityId());
        }

        // 填充产品关联的配置货位
        if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
            List<Long> productSkuIds = pageList.getDataList().stream().map(dto -> Long.valueOf(dto.getProductSkuId()))
                    .collect(Collectors.toList());
            // 获取产品的配置货位
            List<ProductInfoLocationResultDTO> locationList =
                    getConfigLocation(productSkuIds, param.getWarehouseId(), param.getCityId());
            Map<Long, List<ProductInfoLocationResultDTO>> locationMap =
                    locationList.stream().collect(Collectors.groupingBy(p -> p.getProductSkuId()));
            pageList.getDataList().forEach(infoListDTO -> {
                infoListDTO.setLocationList(locationMap.get(Long.valueOf(infoListDTO.getProductSkuId())));
            });
        }
        return pageList;
    }

    /**
     * 按条码查询产品基本信息
     */
    private PageList<ProductSkuInfoResultDTO> findProductBaseInfoByCode(ProductInfoQueryParam param) {
        PageList<ProductSkuInfoResultDTO> pageList = new PageList<>();

        // 通过瓶码或者箱码拿到sku列表
        List<Long> skuIdList = productSkuServiceBL.getProductSkuIdByCode(param.getContent(), param.getCityId());
        LOGGER.info("findProductBaseInfoByCode - 按条码查询skuIdList：{}", GSON.toJson(skuIdList));

        if (CollectionUtils.isNotEmpty(skuIdList)) {
            com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoSO productSkuInfoSO = new ProductSkuInfoSO();
            productSkuInfoSO.setPageSize(param.getPageSize());
            productSkuInfoSO.setPageNum(param.getCurrentPage());
            productSkuInfoSO.setWarehouseId(param.getWarehouseId());
            productSkuInfoSO.setProductSkuIdList(skuIdList);
            productSkuInfoSO.setSource(param.getSource());
            productSkuInfoSO.setStoreType(param.getStoreType());
            productSkuInfoSO.setOwnerType(param.getOwnerType());

            // 分仓属性
            productSkuInfoSO.setWarehouseAllocationType(param.getWarehouseAllocationType());

            PageList<ProductSkuInfoDTO> infoPageList =
                    iWarehouseInventoryQueryService.findProductBaseInfo(productSkuInfoSO);
            List<ProductSkuInfoResultDTO> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(infoPageList.getDataList())) {
                list = infoPageList.getDataList().stream().map(info -> {
                    ProductSkuInfoResultDTO infoList = new ProductSkuInfoResultDTO();
                    BeanUtils.copyProperties(info, infoList);
                    infoList.setPackageQuantity(new BigDecimal(info.getPackageQuantity()));
                    infoList.setOwnerId(info.getOwnerId() != null ? Long.valueOf(info.getOwnerId()) : null);
                    return infoList;
                }).collect(Collectors.toList());
            }
            pageList.setDataList(list);
            pageList.setPager(infoPageList.getPager());
        }
        return pageList;
    }

    /**
     * 按产品名称查询
     */
    private PageList<ProductSkuInfoResultDTO> listProductInfoByProductName(ProductInfoQueryParam param) {
        PageList<ProductSkuInfoResultDTO> pageList = new PageList<>();
        // 按产品名称查询
        ProductSkuInfoSO productSkuInfoSO = new ProductSkuInfoSO();
        productSkuInfoSO.setPageSize(param.getPageSize());
        productSkuInfoSO.setPageNum(param.getCurrentPage());
        productSkuInfoSO.setWarehouseId(param.getWarehouseId());
        productSkuInfoSO.setCityId(param.getCityId());
        productSkuInfoSO.setProductSkuName(param.getContent());
        productSkuInfoSO.setSource(param.getSource());
        productSkuInfoSO.setWithoutLocation(param.getWithoutLocation());
        productSkuInfoSO.setStoreType(param.getStoreType());
        PageList<ProductSkuInfoDTO> infoPageList = iWarehouseInventoryQueryService.listProductSkuInfo(productSkuInfoSO);
        // 转换结果
        List<ProductSkuInfoResultDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(infoPageList.getDataList())) {
            list = infoPageList.getDataList().stream().map(info -> {
                ProductSkuInfoResultDTO infoList = new ProductSkuInfoResultDTO();
                BeanUtils.copyProperties(info, infoList);
                infoList.setPackageQuantity(new BigDecimal(info.getPackageQuantity()));
                return infoList;
            }).collect(Collectors.toList());
        }
        List<ProductSkuInfoResultDTO> collect = list.stream().filter(p -> Objects.nonNull(p.getProductSkuId())).distinct()
                .collect(Collectors.collectingAndThen(
                        Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(ProductSkuInfoResultDTO::getProductSkuId))),
                        ArrayList::new));
        pageList.setDataList(collect);
        pageList.setPager(infoPageList.getPager());
        // LOG.info("按产品名称查询结果：{}", GSON.toJson(pageList));
        return pageList;
    }

    private void removeNotImportProduct(List<ProductSkuInfoResultDTO> skuInfoDTOList, Integer warehouseId,
                                        Integer cityId) {
        if (CollectionUtils.isEmpty(skuInfoDTOList)) {
            return;
        }
        List<Long> lstAllSukId =
                skuInfoDTOList.stream().filter(p -> p != null && org.apache.commons.lang3.StringUtils.isNotEmpty(p.getProductSkuId()))
                        .map(p -> Long.valueOf(p.getProductSkuId())).distinct().collect(Collectors.toList());
        List<Long> skuMap = storageAttributeService.getSkuConfigBySkuIdsAndWarehouseId(lstAllSukId, warehouseId);
        LOGGER.info(String.format("仓库已关联产品：%s", JSON.toJSONString(skuMap)));
        skuInfoDTOList.removeIf(old -> !skuMap.contains(Long.valueOf(old.getProductSkuId())));
    }

    /**
     * 获取配置货位
     *
     * @return
     */
    public List<ProductInfoLocationResultDTO> getConfigLocation(List<Long> productSkuIds, Integer warehouseId,
                                                          Integer cityId) {
        List<ProductInfoLocationResultDTO> locationList = new ArrayList<>();
        ProductLocationListSO so = new ProductLocationListSO();
        so.setWarehouseId(warehouseId);
        so.setCityId(cityId);
        so.setProductSkuIdList(productSkuIds);
        so.setPageSize(100);
        PageList<ProductLocationListDTO> productLocationListDTOList = listProductLocation(so);
        if (CollectionUtils.isNotEmpty(productLocationListDTOList.getDataList())) {
            locationList = productLocationListDTOList.getDataList().stream()
                    .filter(p -> Objects.nonNull(p.getLocationId())).distinct().map(p -> {
                        ProductInfoLocationResultDTO dto = new ProductInfoLocationResultDTO();
                        dto.setProductLocationId(
                                p.getProductLocationId() != null ? p.getProductLocationId().toString() : null);
                        dto.setLocationId(p.getLocationId() != null ? p.getLocationId().toString() : null);
                        dto.setLocationName(p.getLocationName());
                        dto.setAreaId(p.getAreaId());
                        dto.setAreaName(p.getAreaName());
                        dto.setProductSkuId(p.getProductSkuId());
                        dto.setSubcategory(p.getSubcategory());
                        dto.setLocationCategory(p.getCategory() != null ? p.getCategory().intValue() : null);
                        dto.setAreaSubcategory(Objects.equals(dto.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())
                                ? dto.getSubcategory() : p.getAreaSubcategory());
                        dto.setBusinessType(p.getBusinessType());
                        return dto;
                    })
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(
                                    () -> new TreeSet<>(Comparator.comparing(p -> p.getProductSkuId() + p.getLocationId()))),
                            ArrayList::new));
        }
        return locationList;
    }

    /**
     * 产品关联货位根据货位业务类型检查
     *
     * @param checkDTO
     */
    private void validateByBusinessType(ProductLocationDTO checkDTO, boolean isAdd) {
        if (Objects.isNull(checkDTO) || Objects.isNull(checkDTO.getLocationId())
                || Objects.isNull(checkDTO.getProductSkuId())) {
            return;
        }

        LocationPO locationPO = locationPOMapper.findLocationById(checkDTO.getLocationId());
        if (Objects.isNull(locationPO)) {
            throw new BusinessValidateException("产品关联的货位信息不存在");
        }

        checkDTO.setSubcategory(locationPO.getSubcategory());
        checkDTO.setBusinessType(locationPO.getBusinessType());

        // 促销货位只能是零拣位或分拣位, 相同业务类型的分拣位、零拣位只能关联一个
        List<Byte> subcategoryList =
                Arrays.asList(LocationEnum.分拣位.getType().byteValue(), LocationEnum.零拣位.getType().byteValue(),
                        LocationAreaEnum.拣货区.getType().byteValue(), LocationAreaEnum.零拣区.getType().byteValue());
        if (Objects.equals(checkDTO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())
                && !subcategoryList.contains(checkDTO.getSubcategory())) {
            throw new BusinessValidateException("产品关联促销货位必须是零拣位、分拣位或对应货区");
        }

        // 相同业务类型的分拣位、零拣位或对应货区只能关联一个
        List<ProductLoactionItemDTO> existItemDTOS = productLocationPOMapper
                .findLocationBySkuId(Arrays.asList(checkDTO.getProductSkuId()), checkDTO.getWarehouseId());
        if (CollectionUtils.isEmpty(existItemDTOS)) {
            return;
        }

        Map<String, List<ProductLoactionItemDTO>> existItemMap =
                existItemDTOS.stream().filter(p -> subcategoryList.contains(p.getSubcategory()))
                        .collect(Collectors.groupingBy(p -> getCheckKey(p.getProductSkuId(), p.getBusinessType(), p.getSubcategory())));
        if (existItemMap.isEmpty()) {
            return;
        }

        existItemMap.forEach((itemKey, itemList) -> {
            if (itemList.size() > 1) {
                throw new BusinessValidateException("相同业务类型的分拣位或零拣位或对应货区重复关联");
            }

            if (isAdd && itemKey.equals(getCheckKey(checkDTO.getProductSkuId(), checkDTO.getBusinessType(), checkDTO.getSubcategory()))) {
                throw new BusinessValidateException("已存在关联的相同业务类型分拣位或零拣位或对应货区");
            }
        });
    }

    private String getCheckKey(Long productSkuId, Byte businessType, Byte subcategory) {
        return String.format("%s-%s-%s", productSkuId, businessType, subcategory);
    }


    /**
     * 批量重新关联产品货位配置（支持校验和删除旧关联）
     *
     * @param replaceDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void replaceProductLocationBatch(ReplaceProductLocationBatchDTO replaceDTO) {
        LOGGER.info("批量重新关联产品关联货位数据 参数：{}", JSON.toJSONString(replaceDTO));
        AssertUtils.notNull(replaceDTO, "参数不能为空");
        AssertUtils.notNull(replaceDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(replaceDTO.getUserId(), "用户id不能为空");
        AssertUtils.notEmpty(replaceDTO.getReplaceItemDTOS(), "新增参数不能为空");
        replaceDTO.getReplaceItemDTOS().stream().forEach(addDTO -> {
            AssertUtils.notNull(addDTO.getLocationId(), "货位id不能为空");
            AssertUtils.notNull(addDTO.getProductSkuId(), "skuid不能为空");
        });

        Integer warehouseId = replaceDTO.getWarehouseId();
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
        AssertUtils.notNull(warehouse, "仓库信息不能为空");
        replaceDTO.setCityId(warehouse.getCityId());
        Integer userId = replaceDTO.getUserId();
        List<ReplaceProductLocationItemDTO> replaceItems = replaceDTO.getReplaceItemDTOS();
        List<Long> locationIds =
                replaceItems.stream().map(p -> p.getLocationId()).distinct().collect(Collectors.toList());

        // 开启分区的仓库，验证人 和 货区 是否属于一个分仓
        productLocationValidateBL.validateOperationLegal(
                ProductLocationValidateOperationLegalBO.ofLocationIds(userId, warehouseId, locationIds));
        // 填充货位业务类型等信息
        fillLocationInfo(replaceItems);
        // 检查已存在产品关联货位，过滤出需要删除的ids
        List<Long> deleteIds = validateByBusinessTypeBatch(replaceDTO);
        // 删除已存在产品关联货位
        removeProductLocationBatch(deleteIds, userId);
        // 新增产品关联货位
        List<ProductLocationPO> addPOList = addProductLocationBatch(replaceDTO);
        // 产品货位同步更新产品配置分仓属性
        updateSkuConfigStorageAttribute(userId, addPOList);
    }

    public List<ProductLocationPO> addProductLocationBatch(ReplaceProductLocationBatchDTO replaceDTO) {
        List<ProductLocationPO> poList = new ArrayList<>();
        replaceDTO.getReplaceItemDTOS().stream().forEach(dto -> {
            ProductLocationDTO productLocationDTO = new ProductLocationDTO();
            productLocationDTO.setCityId(replaceDTO.getCityId());
            productLocationDTO.setWarehouseId(replaceDTO.getWarehouseId());
            productLocationDTO.setLocationId(dto.getLocationId());
            productLocationDTO.setProductSkuId(dto.getProductSkuId());
            productLocationDTO.setUserId(replaceDTO.getUserId());
            productLocationDTO.setBusinessType(dto.getBusinessType());
            ProductLocationPO po = addProductLocation(productLocationDTO, replaceDTO.getIsAutoMove());
            poList.add(po);
        });

        return poList;
    }

    /**
     * 新增产品货位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public ProductLocationPO addProductLocation(ProductLocationDTO dto, boolean isMove) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notNull(dto.getLocationId(), "货位Id不能为空");
        AssertUtils.notNull(dto.getUserId(), "操作人Id不能为空");

        Long productLocationCount = productLocationPOMapper.getProductLocationCount(dto.getProductSkuId(),
                dto.getLocationId(), dto.getWarehouseId());
        if (productLocationCount > 0) {
            throw new BusinessValidateException("该产品货位设置已存在！");
        }
        // 验证货位是否不允许混放，不允许混放的货位不能关联多个产品
        checklocationMixup(dto.getProductSkuId(), dto.getLocationId(), dto.getWarehouseId());

        // 判断货位中是否已有相同类目或相同产品类型的产品
        checkLocationSameProduct(dto.getProductSkuId(), dto.getLocationId(), dto.getWarehouseId());

        ProductLocationPO po = new ProductLocationPO();
        Long id = productIdGenerator.generatorLocationId(dto.getCityId());
        po.setId(id);
        po.setCity_Id(dto.getCityId());
        po.setWarehouse_Id(dto.getWarehouseId());
        po.setProductSku_Id(dto.getProductSkuId());
        po.setLocation_Id(dto.getLocationId());
        po.setRemo(dto.getRemo());
        po.setCreateTime(new Date());
        po.setCreateUserId(dto.getUserId());
        LOGGER.info("新增产品关联货位数据: {}", JSON.toJSONString(po));
        productLocationHelper.insertSelective(po);

        // 是否对其它分拣位和零件位批次库存，移库到当前新增关联货位
        if (isMove) {
            handleVersion4Location(dto);
            handle2p5plusLocation(dto);
            handleBatchTaskInfo(po, new ProductLocationAssociateBO(dto, Boolean.TRUE), dto.getLocationId());
        }

        return po;
    }

    /**
     * 批量校验产品关联货位的业务类型是否符合规则
     *
     * @param replaceDTO
     */
    public List<Long> validateByBusinessTypeBatch(ReplaceProductLocationBatchDTO replaceDTO) {
        List<ReplaceProductLocationItemDTO> checkDTOList = replaceDTO.getReplaceItemDTOS();
        if (CollectionUtils.isEmpty(checkDTOList)) {
            return Collections.emptyList();
        }

        // 相同业务类型的分拣位、零拣位、零拣区、拣货区只能关联一个， 促销货位只能是零拣位或分拣位
        List<Byte> subcategoryList =
                Arrays.asList(LocationEnum.分拣位.getType().byteValue(), LocationEnum.零拣位.getType().byteValue(),
                        LocationAreaEnum.拣货区.getType().byteValue(), LocationAreaEnum.零拣区.getType().byteValue());

        // 重复数据检查
        checkForDuplicates(checkDTOList, subcategoryList);

        Integer warehouseId = replaceDTO.getWarehouseId();
        List<Long> productSkuIds =
                checkDTOList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        // 获取已存在产品关联货位信息
        Map<Long, List<ProductLoactionItemDTO>> productLocationMap =
                productLocationPOMapper.findLocationBySkuId(productSkuIds, warehouseId).stream()
                        .collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));
        LOGGER.info("已存在产品关联货位信息 ：{}", JSON.toJSONString(productLocationMap));

        List<Long> deleteItemIds = new ArrayList<>();
        for (ReplaceProductLocationItemDTO checkDTO : checkDTOList) {
            Long locationId = checkDTO.getLocationId();
            Long productSkuId = checkDTO.getProductSkuId();
            // 指定relatedLocationId后直接删除
            if(Objects.nonNull(checkDTO.getRelatedLocationId()) && !Objects.equals(checkDTO.getLocationId(), checkDTO.getRelatedLocationId())){
                deleteItemIds.add(checkDTO.getRelatedLocationId());
            }

            // 检查促销货位是否是零拣位或分拣位
            if (Objects.equals(checkDTO.getBusinessType(), LocationBusinessTypeEnum.促销.getType())
                    && !subcategoryList.contains(checkDTO.getSubcategory())) {
                throw new BusinessValidateException("产品关联促销货位必须是零拣位或分拣位，货位ID: " + locationId);
            }

            // 获取当前SKU的所有已有货位配置
            List<ProductLoactionItemDTO> existItemDTOS =
                    productLocationMap.getOrDefault(productSkuId, Collections.emptyList());

            Map<String, List<ProductLoactionItemDTO>> existItemMap =
                    existItemDTOS.stream().filter(p -> subcategoryList.contains(p.getSubcategory()))
                            .collect(Collectors.groupingBy(p -> getCheckKey(p.getProductSkuId(), p.getBusinessType(), p.getSubcategory())));
            if (existItemMap.isEmpty()) {
                return Collections.emptyList();
            }

            existItemMap.forEach((itemKey, itemList) -> {
                if (itemList.size() > 1) {
                    deleteItemIds.addAll(itemList.stream().skip(1).map(ProductLoactionItemDTO::getId).distinct()
                            .collect(Collectors.toList()));
                }

                if (itemKey.equals(getCheckKey(checkDTO.getProductSkuId(), checkDTO.getBusinessType(), checkDTO.getSubcategory()))) {
                    deleteItemIds.add(itemList.stream().findFirst().get().getId());
                }
            });
        }

        LOGGER.info("待删除已存在产品关联货位数据 ids：{}", JSON.toJSONString(deleteItemIds));
        return deleteItemIds;
    }

    /**
     * 批量自动删除产品货位
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeProductLocationBatch(List<Long> ids, Integer userId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<ProductLocationPO> productLocationPOS = productLocationPOMapper.selectByIds(ids);
        if (CollectionUtils.isEmpty(productLocationPOS)) {
            LOGGER.info("[新增时自动删除产品货位]货位id不存在：{}", JSON.toJSONString(ids));
            return;
        }

        productLocationPOMapper.deleteBatch(ids);
        LOGGER.info("[新增时自动删除产品货位]{}, 操作人：{}", JSON.toJSONString(productLocationPOS), JSON.toJSONString(userId));
    }

    /**
     * 填充货位业务类型等信息
     *
     * @param dtoList
     */
    public void fillLocationInfo(List<ReplaceProductLocationItemDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        List<Long> locationIds =
                dtoList.stream().map(p -> p.getLocationId()).distinct().collect(Collectors.toList());
        Map<Long, LoactionDTO> locationPOMap = locationPOMapper.findLocationByIds(locationIds).stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(LoactionDTO::getId, Function.identity()));
        if(locationPOMap.isEmpty()){
            throw new BusinessValidateException("产品关联的货位信息不存在");
        }

        for (ReplaceProductLocationItemDTO dto : dtoList) {
            LoactionDTO locationDTO = locationPOMap.get(dto.getLocationId());
            if (Objects.isNull(locationDTO)) {
                throw new BusinessValidateException("产品关联的货位信息不存在，货位ID: " + dto.getLocationId());
            }
            // 赋值业务类型和货位类型
            dto.setSubcategory(locationDTO.getSubcategory());
            dto.setBusinessType(locationDTO.getBusinessType());
        }
    }

    private void checkForDuplicates(List<ReplaceProductLocationItemDTO> checkDTOList, List<Byte> subcategoryList) {
        Map<String, Long> sameDuplicateMap = checkDTOList.stream()
                .collect(Collectors.groupingBy(dto -> dto.getProductSkuId() + "-" + dto.getLocationId(), Collectors.counting()));
        sameDuplicateMap.forEach((key, count) -> {
            if (count > 1) {
                throw new BusinessValidateException("存在重复关联相同产品货位的组合: " + key);
            }
        });

        Map<String, Long> duplicateMap = checkDTOList.stream().filter(p -> subcategoryList.contains(p.getSubcategory()))
                .collect(Collectors.groupingBy(dto -> getCheckKey(dto.getProductSkuId(), dto.getBusinessType(), dto.getSubcategory()), Collectors.counting()));
        if(!duplicateMap.isEmpty()){
            duplicateMap.forEach((key, count) -> {
                if (count > 1) {
                    throw new BusinessValidateException("存在重复关联相同业务类型货位的组合: " + key);
                }
            });
        }
    }

    /**
     * 替换产品关联货位检查
     *
     * @param checkDTOS
     * @return
     */
    public List<ProductLocationCheckResultDTO> replaceProductLocationCheck(List<ReplaceProductLocationCheckDTO> checkDTOS){
        AssertUtils.notEmpty(checkDTOS, "新增参数不能为空");
        checkDTOS.stream().forEach(dto -> {
            AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(dto.getToLocationId(), "货位id不能为空");
            AssertUtils.notNull(dto.getSkuId(), "skuid不能为空");
        });
        LOGGER.info("替换产品关联货位检查参数：{}", JSON.toJSONString(checkDTOS));

        List<Long> toLocationIds =
                checkDTOS.stream().map(p -> p.getToLocationId()).distinct().collect(Collectors.toList());
        List<LoactionDTO> locationPOS = locationPOMapper.findLocationByIds(toLocationIds);
        if(CollectionUtils.isEmpty(locationPOS)){
            throw new BusinessValidateException("目标货位信息不存在");
        }

        List<Byte> subcategoryList =
                Arrays.asList(LocationEnum.分拣位.getType().byteValue(), LocationEnum.零拣位.getType().byteValue(), LocationAreaEnum.拣货区.getType().byteValue(), LocationAreaEnum.零拣区.getType().byteValue());
        List<Long> locationIds = locationPOS.stream().filter(p -> subcategoryList.contains(p.getSubcategory())).map(LoactionDTO::getId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(locationIds)){
            return Collections.emptyList();
        }
        checkDTOS.removeIf(p -> !locationIds.contains(p.getToLocationId()));
        if(CollectionUtils.isEmpty(checkDTOS)){
            return Collections.emptyList();
        }

        Integer warehouseId = checkDTOS.stream().findFirst().get().getWarehouseId();
        List<Long> productSkuIds =
                checkDTOS.stream().map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        // 获取已存在分拣位、零拣位、拣货区、零拣区产品关联货位信息
        Map<Long, List<ProductLoactionItemDTO>> productLocationMap =
                productLocationPOMapper.findLocationBySkuId(productSkuIds, warehouseId).stream().filter(p -> subcategoryList.contains(p.getSubcategory()))
                        .collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));
        LOGGER.info("替换产品关联货位检查 已存在指定货位类型产品货位信息 ：{}", JSON.toJSONString(productLocationMap));
        if(productLocationMap.isEmpty()){
            return Collections.emptyList();
        }

        Map<Long, LoactionDTO> locationMap = locationPOS.stream()
                .collect(Collectors.toMap(LoactionDTO::getId, Function.identity(), (key1, key2) -> key1));
        List<ProductLocationCheckResultDTO> resultDTOS = new ArrayList<>();
        checkDTOS.forEach(checkDTO -> {
            List<ProductLoactionItemDTO> itemDTOS = productLocationMap.get(checkDTO.getSkuId());
            if(CollectionUtils.isEmpty(itemDTOS)){
                return;
            }

            // 判断目标货位是否已存在关联
            if(itemDTOS.stream().anyMatch(p -> Objects.equals(p.getLocationId(), checkDTO.getToLocationId()))){
                return;
            }

            LoactionDTO loactionDTO = locationMap.get(checkDTO.getToLocationId());
            if(Objects.isNull(loactionDTO)){
                throw new BusinessValidateException("目标货位信息不存在 货位id:" + checkDTO.getToLocationId());
            }

            itemDTOS.stream().forEach(itemDTO -> {
                if(Objects.equals(itemDTO.getLocationId(), checkDTO.getToLocationId())){
                    return;
                }

                if(!Objects.equals(itemDTO.getSubcategory(), loactionDTO.getSubcategory())){
                    return;
                }

                if(!Objects.equals(itemDTO.getBusinessType(), loactionDTO.getBusinessType())){
                    return;
                }

                ProductLocationCheckResultDTO resultDTO = new ProductLocationCheckResultDTO();
                resultDTO.setWarehouseId(checkDTO.getWarehouseId());
                resultDTO.setToLocationId(checkDTO.getToLocationId());
                resultDTO.setProductLocationId(itemDTO.getLocationId());
                resultDTO.setProductLocationName(itemDTO.getLocationName());
                resultDTO.setSubcategory(itemDTO.getSubcategory());
                resultDTO.setBusinessType(itemDTO.getBusinessType());
                resultDTO.setSkuId(checkDTO.getSkuId());
                resultDTOS.add(resultDTO);
            });
        });

        LOGGER.info("替换产品关联货位检查 结果 ：{}", JSON.toJSONString(resultDTOS));
        return resultDTOS;
    }

    /**
     * 获取配置货位
     */
    public List<ProductInfoLocationResultDTO> getConfigLocationNoMerge(ProductSkuInfoQueryParam param) {
        Map<Long, ProductSkuDTO> skuConfigDTOMap = getSkuConfig(param);
        Map<Long, List<ProductLocationListDTO>> skuLocationMap = getSkuLocationMap(param);

        return param.getProductSkuIds().stream().map(skuId -> {
            List<ProductLocationListDTO> locationListDTOList = skuLocationMap.get(skuId);
            ProductSkuDTO skuConfigDTO = skuConfigDTOMap.get(skuId);


            List<ProductInfoLocationResultDTO> productInfoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(locationListDTOList)) {
                locationListDTOList.forEach(locationListDTO -> {
                    ProductInfoLocationResultDTO dto = new ProductInfoLocationResultDTO();
                    dto.setProductLocationId(locationListDTO.getProductLocationId() != null
                            ? locationListDTO.getProductLocationId().toString() : null);
                    dto.setLocationId(
                            locationListDTO.getLocationId() != null ? locationListDTO.getLocationId().toString() : null);
                    dto.setLocationName(locationListDTO.getLocationName());
                    dto.setAreaId(locationListDTO.getAreaId());
                    dto.setAreaName(locationListDTO.getAreaName());
                    dto.setProductSkuId(locationListDTO.getProductSkuId());
                    dto.setSubcategory(locationListDTO.getSubcategory());
                    dto.setLocationCategory(
                            locationListDTO.getCategory() != null ? locationListDTO.getCategory().intValue() : null);
                    boolean isCargoArea = Objects.equals(dto.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue());
                    dto.setAreaSubcategory(isCargoArea ? dto.getSubcategory() : locationListDTO.getAreaSubcategory());
                    dto.setPackageQuantity(locationListDTO.getPackageQuantity());
                    dto.setBusinessType(locationListDTO.getBusinessType());
                    productInfoList.add(dto);
                });

            }
            if (Objects.nonNull(skuConfigDTO)) {
                if (CollectionUtils.isNotEmpty(productInfoList)) {
                    productInfoList.forEach(dto -> {
                        dto.setMinReplenishment(skuConfigDTO.getMinReplenishment());
                        dto.setMaxReplenishment(skuConfigDTO.getMaxReplenishment());
                        dto.setPalletQuantity(skuConfigDTO.getPalletQuantity());
                        if (Objects.nonNull(skuConfigDTO.getProductFeature())) {
                            dto.setProductFeature(skuConfigDTO.getProductFeature().intValue());
                        }
                        if (Objects.isNull(dto.getPackageQuantity()) && Objects.nonNull(skuConfigDTO.getPackageQuantity())) {
                            dto.setPackageQuantity(skuConfigDTO.getPackageQuantity());
                        }
                    });
                } else {
                    ProductInfoLocationResultDTO dto = new ProductInfoLocationResultDTO();
                    dto.setProductSkuId(skuId);
                    dto.setMinReplenishment(skuConfigDTO.getMinReplenishment());
                    dto.setMaxReplenishment(skuConfigDTO.getMaxReplenishment());
                    dto.setPalletQuantity(skuConfigDTO.getPalletQuantity());
                    if (Objects.nonNull(skuConfigDTO.getProductFeature())) {
                        dto.setProductFeature(skuConfigDTO.getProductFeature().intValue());
                    }
                    if (Objects.isNull(dto.getPackageQuantity()) && Objects.nonNull(skuConfigDTO.getPackageQuantity())) {
                        dto.setPackageQuantity(skuConfigDTO.getPackageQuantity());
                    }
                    productInfoList.add(dto);
                }

            }
            return productInfoList;
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(it -> String.format("%s-%s", it.getProductSkuId(), it.getLocationId())))),
                ArrayList::new));
    }


    private Map<Long, ProductSkuDTO> getSkuConfig(ProductSkuInfoQueryParam param) {
        ProductLocationListSO so = new ProductLocationListSO();
        so.setWarehouseId(param.getWarehouseId());
        so.setProductSkuIdList(param.getProductSkuIds());
        List<ProductSkuDTO> productSkuDTOList = productSkuQueryBL.findSkuInfoWithConfig(param.getWarehouseId(), param.getProductSkuIds());

        if (CollectionUtils.isEmpty(productSkuDTOList)) {
            return Collections.emptyMap();
        }
        return productSkuDTOList.stream().collect(Collectors.toMap(ProductSkuDTO::getProductSkuId, v -> v));
    }

    private Map<Long, List<ProductLocationListDTO>> getSkuLocationMap(ProductSkuInfoQueryParam param) {
        ProductLocationListSO so = new ProductLocationListSO();
        so.setWarehouseId(param.getWarehouseId());
        so.setProductSkuIdList(param.getProductSkuIds());

        PageList<ProductLocationListDTO> productLocationListDTOList = listProductLocation(so);
        if (productLocationListDTOList == null || CollectionUtils.isEmpty(productLocationListDTOList.getDataList())) {
            return Collections.emptyMap();
        }

        List<ProductLocationListDTO> locationListDTOList =
                productLocationListDTOList.getDataList().stream().filter(p -> Objects.nonNull(p.getLocationId()))
                        .collect(Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(
                                        Comparator.comparing(it -> String.format("%s-%s", it.getProductSkuId(), it.getLocationId())))),
                                ArrayList::new));

        return locationListDTOList.stream().collect(Collectors.groupingBy(ProductLocationListDTO::getProductSkuId));
    }


    private void validateInsertBatch(List<ProductLocationDTO> productLocationDTOList) {
        if(CollectionUtils.isEmpty(productLocationDTOList)){
            return;
        }

        ProductLocationDTO productLocationDTO = productLocationDTOList.stream().findFirst().orElse(null);
        if(Objects.isNull(productLocationDTO)){
            return;
        }
        Integer warehouseId = productLocationDTO.getWarehouseId();
        Integer userId = productLocationDTO.getUserId();

        ReplaceProductLocationBatchDTO replaceDTO = new ReplaceProductLocationBatchDTO();
        replaceDTO.setWarehouseId(warehouseId);
        List<ReplaceProductLocationItemDTO> replaceItemDTOS = new ArrayList<>();
        productLocationDTOList.stream().forEach(p -> {
            ReplaceProductLocationItemDTO itemDTO = new ReplaceProductLocationItemDTO();
            itemDTO.setProductSkuId(p.getProductSkuId());
            itemDTO.setLocationId(p.getLocationId());
            replaceItemDTOS.add(itemDTO);
        });
        replaceDTO.setReplaceItemDTOS(replaceItemDTOS);
        List<Long> deleteIds = validateByBusinessTypeBatch(replaceDTO);
        // 删除已存在产品关联货位
        removeProductLocationBatch(deleteIds, userId);
    }
}
