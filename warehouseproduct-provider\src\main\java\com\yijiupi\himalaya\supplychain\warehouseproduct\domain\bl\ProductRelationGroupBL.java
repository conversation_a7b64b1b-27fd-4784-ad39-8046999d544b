package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductRelationGroupConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductRelationGroupPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.RedisLock;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;

/**
 * 产品关系分组BL
 */
@Service
public class ProductRelationGroupBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductRelationGroupBL.class);

    /**
     * 处理产品分组redis key
     */
    private static final String PROCESSING_REDIS_LOCK_KEY = "supf:productRelationGroup:processing:";
    /**
     * 初始化产品分组redis key
     */
    private static final String INIT_REDIS_LOCK_KEY = "supf:productRelationGroup:initProcess:";

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private ProductRelationGroupPOMapper productRelationGroupPOMapper;

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    /**
     * 保存产品关联关系 forceCheck : true - 新增分组产品有多个分组则报错 false - 新增分组产品有多个分组则跳过
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveProductRelation(List<ProductRelationGroupAddDTO> groupAddList, boolean forceCheck) {
        LOGGER.info("批量新增产品分组关系信息参数：{}", JSON.toJSONString(groupAddList));
        AssertUtils.notEmpty(groupAddList, "新增产品分组关系不能为空");
        Optional<ProductRelationGroupAddDTO> notAllowOpt =
            groupAddList.stream().filter(e -> e != null && e.getWarehouseId() == null || e.getCityId() == null
                || CollectionUtils.isEmpty(e.getGroupRelations())).findAny();
        if (notAllowOpt.isPresent()) {
            throw new BusinessValidateException(
                String.format("仓库ID、城市ID及分组关系不能为空！异常数据：%s", JSON.toJSONString(notAllowOpt.get())));
        }
        List<ProductRelationGroupDTO> newGroupList = buildRelationConfig(groupAddList);
        if (CollectionUtils.isEmpty(newGroupList)) {
            LOGGER.info("saveProductRelation - 产品分组关系过滤没有需要新增信息");
            if (forceCheck) {
                throw new BusinessValidateException("新增分组产品过滤后没有找到需要新增数据!");
            }
            return;
        }
        // 回填产品信息
        backFillProductInfo(newGroupList);
        // preconditioning();
        List<ProductRelationGroupPO> addPOList = ProductRelationGroupConvertor.toGroupPO(newGroupList);
        // 查询数据库中已有数据
        List<Long> skuIdList = addPOList.stream().filter(e -> e != null).map(ProductRelationGroupPO::getProductSkuId)
            .distinct().collect(Collectors.toList());
        List<Integer> warehouseIdList = addPOList.stream().filter(e -> e != null)
            .map(ProductRelationGroupPO::getWarehouseId).distinct().collect(Collectors.toList());
        List<ProductRelationGroupPO> oldGroupList =
            productRelationGroupPOMapper.selectExistProductGroupInfo(skuIdList, warehouseIdList);
        LOGGER.info("批量新增产品分组关系-数据库中已经存在关联关系：{}", JSON.toJSONString(oldGroupList));
        List<ProductRelationGroupPO> newRelationGroups = removeRepeatConfigByWarehouse(addPOList, oldGroupList);
        if (CollectionUtils.isNotEmpty(newRelationGroups)) {
            if (newRelationGroups.size() <= 1000) {
                // 新增关系过多不打印具体信息
                LOGGER.info("批量新增产品分组关系信息结果：{}", JSON.toJSONString(newRelationGroups));
            } else {
                // 大于 1000 条则只打印出新增记录数
                Set<Integer> warehouseIds =
                    newRelationGroups.stream().filter(e -> e != null && e.getWarehouseId() != null)
                        .map(e -> e.getWarehouseId()).collect(Collectors.toSet());
                LOGGER.info("仓库 {} 批量新增产品分组关系信息总共 {} 条！", JSON.toJSONString(warehouseIds), newRelationGroups.size());
            }
            // 初始化时产品关系数据比较多,分批次插入
            List<List<ProductRelationGroupPO>> partitionList = Lists.partition(newRelationGroups, 100);
            for (int i = 0; i < partitionList.size(); i++) {
                productRelationGroupPOMapper.insertOrUpdateBatchList(partitionList.get(i));
            }
        }
    }

    /**
     * 删除产品关联关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void delProductRelation(ProductRelationGroupDelDTO delDTO) {
        LOGGER.info("批量删除产品分组关系信息参数：{}", JSON.toJSONString(delDTO));
        AssertUtils.notNull(delDTO, "删除信息不能为空");
        AssertUtils.notNull(delDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(delDTO.getDelSkuIds(), "删除产品信息不能为空");
        // 1、先根据仓库+sku查出分组信息
        // 2、删除后如果该分组只有个产品则整组关系删除
        List<Long> delSkuIds =
            delDTO.getDelSkuIds().stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        List<ProductRelationGroupPO> groupPOList = productRelationGroupPOMapper
            .selectTotalGroupConfig(new ProductTotalGroupQueryDTO(delDTO.getWarehouseId(), delSkuIds));
        if (CollectionUtils.isEmpty(groupPOList)) {
            return;
        }
        Map<Long, List<ProductRelationGroupPO>> groupIdMap = groupPOList.stream().filter(e -> e != null)
            .collect(Collectors.groupingBy(ProductRelationGroupPO::getGroupId));
        List<Long> lockKeys = Lists.newArrayList(groupIdMap.keySet());
        List<RedisLock> redisLocks = Lists.newArrayList();
        try {
            redisLocks.addAll(setProcessingGroupLock(lockKeys));
            List<Long> totalDeleteIds = new ArrayList<>();
            for (Map.Entry<Long, List<ProductRelationGroupPO>> entry : groupIdMap.entrySet()) {
                if (entry == null) {
                    continue;
                }
                List<ProductRelationGroupPO> value = entry.getValue();
                List<Long> groupTotalIds = value.stream().filter(e -> e != null).map(ProductRelationGroupPO::getId)
                    .collect(Collectors.toList());
                List<Long> delIdList = value.stream().filter(e -> delSkuIds.contains(e.getProductSkuId()))
                    .map(ProductRelationGroupPO::getId).collect(Collectors.toList());
                // 移除删除项
                groupTotalIds.removeIf(e -> e != null && delIdList.contains(e));
                if (CollectionUtils.isNotEmpty(groupTotalIds) && groupTotalIds.size() == 1) {
                    // 分组中只剩下一个产品直接按组删除
                    delIdList.addAll(groupTotalIds);
                }
                totalDeleteIds.addAll(delIdList);
            }
            if (CollectionUtils.isNotEmpty(totalDeleteIds)) {
                productRelationGroupPOMapper.deleteByPrimaryKeyList(totalDeleteIds);
            }
        } finally {
            unlockRedis(redisLocks);
        }
    }

    /**
     * 拆分关联关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void splitProductRelation(Integer warehouseId, Long splitSkuId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(splitSkuId, "拆分产品不能为空");
        List<ProductRelationGroupPO> groupProductList = productRelationGroupPOMapper
            .selectExistProductGroupInfo(Lists.newArrayList(splitSkuId), Lists.newArrayList(warehouseId));
        if (groupProductList == null || groupProductList.isEmpty()) {
            throw new BusinessValidateException("分组关系不存在或者已经删除！");
        }
        // 按规格ID分组
        Map<Long, List<ProductRelationGroupPO>> specIdMap = groupProductList.stream().filter(e -> e != null)
            .collect(Collectors.groupingBy(ProductRelationGroupPO::getProductSpecificationId));
        if (specIdMap.size() == 1) {
            // 只有一个规格ID不允许拆分
            throw new BusinessValidateException("规格ID相同产品不允许拆分！");
        }
        Set<Long> groupIdList =
            groupProductList.stream().filter(e -> e != null).map(e -> e.getGroupId()).collect(Collectors.toSet());
        List<RedisLock> redisLocks = Lists.newArrayList();
        try {
            // 加入分布式锁
            redisLocks.addAll(setProcessingGroupLock(groupIdList));
            List<Long> delIdList = new ArrayList<>(groupProductList.size());
            for (Map.Entry<Long, List<ProductRelationGroupPO>> entry : specIdMap.entrySet()) {
                // 同规格且产品数大于1则建立新关联关系
                if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                List<ProductRelationGroupPO> newGroupList = entry.getValue();
                if (newGroupList.size() >= 2) {
                    long newGroupId = UUIDUtils.randonUUID();
                    newGroupList.stream().filter(e -> e != null).forEach(e -> e.setGroupId(newGroupId));
                } else {
                    delIdList.add(newGroupList.get(0).getId());
                }
            }
            // 移除解除关系的产品
            groupProductList.removeIf(e -> e != null && delIdList.contains(e.getId()));
            // 删除关联关系
            if (CollectionUtils.isNotEmpty(delIdList)) {
                productRelationGroupPOMapper.deleteByPrimaryKeyList(delIdList);
            }
            // 更新分组ID
            if (CollectionUtils.isNotEmpty(groupProductList)) {
                productRelationGroupPOMapper.insertOrUpdateBatchList(groupProductList);
            }
        } finally {
            unlockRedis(redisLocks);
        }
    }

    /**
     * 仓库维度初始化产品分组关系
     */
    @Async(value = "warehouseProductTaskExecutor")
    @Transactional(rollbackFor = RuntimeException.class)
    public void initProductGroup(Integer warehouseId) {
        LOGGER.info("仓库[{}]开始初始化产品分组信息", warehouseId);
        AssertUtils.notNull(warehouseId, "仓库信息不能为空");
        ProductGroupInitDTO initDTO = new ProductGroupInitDTO();
        initDTO.setWarehouseIdList(Lists.newArrayList(warehouseId));
        List<ProductGroupInitQueryResultDTO> sameSpecProducts =
            productRelationGroupPOMapper.findYkAndJpSameSpecProducts(initDTO);
        if (CollectionUtils.isEmpty(sameSpecProducts)) {
            LOGGER.info("仓库[{}]初始化产品分组关系没有找到符合条件的产品！", warehouseId);
            return;
        }
        List<RedisLock> redisLocks = new ArrayList<>();
        try {
            // 加锁
            redisLocks.addAll(setInitGroupLock(initDTO.getWarehouseIdList()));
            // 开始业务数据处理
            List<ProductRelationGroupAddDTO> newGroupList = createNewProductGroupList(sameSpecProducts);
            if (CollectionUtils.isNotEmpty(newGroupList)) {
                // 新增分组关系
                saveProductRelation(newGroupList, false);
            } else {
                LOGGER.info("仓库[{}]初始化产品分组关系过滤后无新增分组信息", warehouseId);
            }
        } finally {
            unlockRedis(redisLocks);
        }
    }

    /**
     * 去取重复分组信息
     */
    private List<ProductRelationGroupPO> removeRepeatConfigByWarehouse(List<ProductRelationGroupPO> newGroupList,
        List<ProductRelationGroupPO> oldGroupList) {
        Map<Integer, List<ProductRelationGroupPO>> newGroupMap = newGroupList.stream().filter(e -> e != null)
            .collect(Collectors.groupingBy(ProductRelationGroupPO::getWarehouseId));
        Map<Integer, List<ProductRelationGroupPO>> oldGroupMap =
            CollectionUtils.isEmpty(oldGroupList) ? Collections.EMPTY_MAP : oldGroupList.stream().filter(e -> e != null)
                .collect(Collectors.groupingBy(ProductRelationGroupPO::getWarehouseId));
        List<ProductRelationGroupPO> rs = new ArrayList<>();
        for (Map.Entry<Integer, List<ProductRelationGroupPO>> entry : newGroupMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            rs.addAll(removeRepeatConfig(entry.getValue(), oldGroupMap.get(entry.getKey())));
        }
        return rs;
    }

    /**
     * 去取重复分组信息
     */
    private List<ProductRelationGroupPO> removeRepeatConfig(List<ProductRelationGroupPO> newGroupList,
        List<ProductRelationGroupPO> oldGroupList) {
        if (CollectionUtils.isEmpty(newGroupList) || CollectionUtils.isEmpty(oldGroupList)) {
            return newGroupList;
        }
        List<ProductRelationGroupPO> rs = new ArrayList<>();
        Map<Long, List<ProductRelationGroupPO>> newGroupMap =
            newGroupList.stream().collect(Collectors.groupingBy(ProductRelationGroupPO::getGroupId));
        for (Map.Entry<Long, List<ProductRelationGroupPO>> entry : newGroupMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            List<ProductRelationGroupPO> value = entry.getValue();
            Set<Long> skuKeys =
                value.stream().filter(e -> e != null).map(e -> e.getProductSkuId()).collect(Collectors.toSet());
            Set<Long> specIdKeys = value.stream().filter(e -> e != null).map(e -> e.getProductSpecificationId())
                .collect(Collectors.toSet());
            // 数据库中匹配上的分组
            List<Long> existGroupIdList = oldGroupList.stream()
                .filter(e -> e != null
                    && (skuKeys.contains(e.getProductSkuId()) || specIdKeys.contains(e.getProductSpecificationId()))
                    && e.getGroupId() != null)
                .map(e -> e.getGroupId()).distinct().collect(Collectors.toList());
            // 新分组产品要么不存在数据库，要么都在一个分组中。否则不能建立新分组关系
            if (CollectionUtils.isNotEmpty(existGroupIdList)) {
                List<ProductRelationGroupPO> oldMatchGroupList = oldGroupList.stream()
                    .filter(e -> e != null && existGroupIdList.contains(e.getGroupId())).collect(Collectors.toList());
                long newGroupId = oldMatchGroupList.get(0).getGroupId();
                List<Long> oldSkuIdList = oldMatchGroupList.stream().filter(e -> e != null)
                    .map(ProductRelationGroupPO::getProductSkuId).collect(Collectors.toList());
                List<ProductRelationGroupPO> needUpdateGroupPOList = oldMatchGroupList.stream()
                    .filter(e -> e != null && !Objects.equals(e.getGroupId(), newGroupId)).collect(Collectors.toList());
                // 去除已经存在的分组产品
                value.removeIf(e -> e != null && oldSkuIdList.contains(e.getProductSkuId()));
                // 需要变更分组信息产品
                value.addAll(needUpdateGroupPOList);
                // 设置新分组ID
                value.stream().filter(e -> e != null).forEach(e -> e.setGroupId(newGroupId));
            }
            rs.addAll(value);
        }
        return rs;
    }

    /**
     * 回填新增关系产品属性
     */
    private void backFillProductInfo(List<ProductRelationGroupDTO> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return;
        }
        List<Long> allSkuIds = groupList.stream().filter(e -> e != null && e.getProductSkuId() != null)
            .map(e -> e.getProductSkuId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allSkuIds)) {
            return;
        }
        // 如果强制校验则抛出异常
        List<ProductSkuPO> skuPOList = productSkuPOMapper.getProductInfoBySkuId(allSkuIds);
        if (CollectionUtils.isEmpty(skuPOList)) {
            return;
        }
        Map<Long, ProductSkuPO> skuPOMap = skuPOList.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> e.getProductSku_Id(), Function.identity()));
        for (ProductRelationGroupDTO groupDTO : groupList) {
            if (groupDTO == null) {
                continue;
            }
            ProductSkuPO productSkuPO = skuPOMap.get(groupDTO.getProductSkuId());
            if (productSkuPO == null) {
                continue;
            }
            groupDTO.setProductSpecificationId(productSkuPO.getProductSpecification_Id());
            groupDTO.setProductName(productSkuPO.getName());
        }
    }

    /**
     * 仓库维度 - 批量过滤关联关系集合
     */
    private List<ProductRelationGroupDTO> buildRelationConfig(List<ProductRelationGroupAddDTO> groupAddList) {
        List<ProductRelationGroupDTO> newGroupList = new ArrayList<>(groupAddList.size() * 2);
        for (ProductRelationGroupAddDTO addDTO : groupAddList) {
            if (addDTO == null) {
                continue;
            }
            // 仓库维度处理分组关系
            newGroupList.addAll(buildRelationConfig(addDTO));
        }
        return newGroupList;
    }

    /**
     * 过滤关联关系集合
     */
    private List<ProductRelationGroupDTO> buildRelationConfig(ProductRelationGroupAddDTO addDTO) {
        List<ProductRelationGroupAddItemDTO> groupRelations = addDTO.getGroupRelations();
        // 结果集合
        List<Set<Long>> rsGroup = new ArrayList<>();
        for (int i = 0; i < groupRelations.size(); i++) {
            ProductRelationGroupAddItemDTO outDTO = groupRelations.get(i);
            if (outDTO == null || outDTO.getHasCount()) {
                continue;
            }
            Set<Long> inRs = new HashSet<>();
            List<Long> outSku = outDTO.getGroupSkuIds();
            inRs.addAll(outSku);
            for (int j = i + 1; j < groupRelations.size(); j++) {
                ProductRelationGroupAddItemDTO inDTO = groupRelations.get(j);
                if (inDTO == null || inDTO.getHasCount()) {
                    continue;
                }
                List<Long> inSku = inDTO.getGroupSkuIds();
                boolean retainAll = hasElement(outSku, inSku);
                if (retainAll) {
                    inRs.addAll(inSku);
                    inDTO.setHasCount(true);
                }
            }
            // 用外层结果在此循环没有被标记过的配置信息
            List<ProductRelationGroupAddItemDTO> unprocessed =
                groupRelations.stream().filter(e -> e != null && !ObjectUtils.defaultIfNull(e.getHasCount(), false))
                    .collect(Collectors.toList());
            for (ProductRelationGroupAddItemDTO unpro : unprocessed) {
                if (unpro == null || unpro.getHasCount()) {
                    continue;
                }
                List<Long> unSkuIds = unpro.getGroupSkuIds();
                boolean retainAll = hasElement(inRs, unSkuIds);
                if (retainAll) {
                    inRs.addAll(unSkuIds);
                    unpro.setHasCount(true);
                }
            }
            outDTO.setHasCount(true);
            rsGroup.add(inRs);
        }
        LOGGER.info("产品关联关系过滤后结果：{}", JSON.toJSONString(rsGroup));
        List<ProductRelationGroupDTO> newGroup = new ArrayList<>(rsGroup.size() * 2);
        // 只有一个产品的集合不能建立关联关系
        rsGroup.stream().filter(skuList -> CollectionUtils.isNotEmpty(skuList) && skuList.size() > 1)
            .forEach(skuList -> {
                long groupId = UUIDUtils.randonUUID();
                for (Long sku : skuList) {
                    ProductRelationGroupDTO groupDTO = new ProductRelationGroupDTO();
                    groupDTO.setCityId(addDTO.getCityId());
                    groupDTO.setWarehouseId(addDTO.getWarehouseId());
                    groupDTO.setProductSkuId(sku);
                    groupDTO.setGroupId(groupId);
                    groupDTO.setCreateUser(addDTO.getOperatorUser());
                    groupDTO.setLastUpdateUser(addDTO.getOperatorUser());
                    newGroup.add(groupDTO);
                }
            });
        return newGroup;
    }

    /**
     * 如果强制校验则抛出异常信息
     */
    private void throwsCheckException(List<ProductRelationGroupPO> existProductList) {
        if (CollectionUtils.isNotEmpty(existProductList)) {
            String errorMsg = existProductList.stream().filter(e -> e != null)
                .map(e -> StringUtils.join(e.getProductName() == null ? "" : e.getProductName(),
                    "[" + (e.getProductSkuId() == null ? "" : e.getProductSkuId()) + "]"))
                .distinct().collect(Collectors.joining(","));

            throw new BusinessValidateException(String.format("产品%s已经分组，不可重复操作！", errorMsg));
        }
    }

    /**
     * 初始化设置产品关联关系
     */
    private List<ProductRelationGroupAddDTO>
        createNewProductGroupList(List<ProductGroupInitQueryResultDTO> sameSpecProducts) {
        List<ProductRelationGroupAddDTO> newGroupList = new ArrayList<>(sameSpecProducts.size());
        // 1、按仓库分组
        Map<Integer, List<ProductGroupInitQueryResultDTO>> houseProductMap =
            sameSpecProducts.stream().filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getWarehouseId()));
        for (Map.Entry<Integer, List<ProductGroupInitQueryResultDTO>> entry : houseProductMap.entrySet()) {
            if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<ProductGroupInitQueryResultDTO> houseProducts = entry.getValue();
            ProductGroupInitQueryResultDTO defaultGroupDTO = houseProducts.get(0);
            // 同规格产品关系结果
            List<ProductRelationGroupAddItemDTO> addItemDTOList = new ArrayList<>();
            // 2、同仓库再按规格ID分组
            Map<Long, List<ProductGroupInitQueryResultDTO>> groupProductMap = houseProducts.stream()
                .filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getProductSpecificationId()));
            for (Map.Entry<Long, List<ProductGroupInitQueryResultDTO>> initProductEntry : groupProductMap.entrySet()) {
                if (initProductEntry == null || CollectionUtils.isEmpty(initProductEntry.getValue())) {
                    continue;
                }
                List<Long> sameSpecSkuIds =
                    initProductEntry.getValue().stream().filter(e -> e != null && e.getProductSkuId() != null)
                        .map(e -> e.getProductSkuId()).distinct().collect(Collectors.toList());
                // 同规格ID为一组:同组内的产品数必须大于1,单个产品不进行分组
                if (CollectionUtils.isNotEmpty(sameSpecSkuIds) && sameSpecSkuIds.size() > 1) {
                    ProductRelationGroupAddItemDTO addItemDTO = new ProductRelationGroupAddItemDTO();
                    addItemDTO.setGroupSkuIds(sameSpecSkuIds);
                    // 记录同组产品
                    addItemDTOList.add(addItemDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(addItemDTOList)) {
                // 分组DTO
                ProductRelationGroupAddDTO houseGroup = new ProductRelationGroupAddDTO();
                houseGroup.setWarehouseId(defaultGroupDTO.getWarehouseId());
                houseGroup.setCityId(defaultGroupDTO.getCityId());
                houseGroup.setOperatorUser("系统初始化");
                // 设置不同规格sku分组集合
                houseGroup.setGroupRelations(addItemDTOList);
                // 记录仓库分组信息
                newGroupList.add(houseGroup);
            }
        }
        return newGroupList;
    }

    private boolean hasElement(Collection<Long> src, Collection<Long> tag) {
        if (src == null || tag == null) {
            return false;
        }
        for (Long s : src) {
            boolean contains = tag.contains(s);
            if (contains) {
                return true;
            }
        }
        return false;
    }

    // 设置删除 key
    private List<RedisLock> setProcessingGroupLock(Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        List<String> keys = groupIds.stream().filter(e -> e != null).map(e -> String.valueOf(e)).distinct()
            .collect(Collectors.toList());
        return createRedisLock(keys, PROCESSING_REDIS_LOCK_KEY);
    }

    // 初始化产品 key
    private List<RedisLock> setInitGroupLock(List<Integer> warehouseIds) {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return Lists.newArrayList();
        }
        List<String> keys = warehouseIds.stream().filter(e -> e != null).map(e -> String.valueOf(e)).distinct()
            .collect(Collectors.toList());
        return createRedisLock(keys, INIT_REDIS_LOCK_KEY);
    }

    private List<RedisLock> createRedisLock(List<String> keys, String keyPrefix) {
        List<RedisLock> redisLocks = Lists.newArrayList();
        if (CollectionUtils.isEmpty(keys)) {
            return redisLocks;
        }
        keys = keys.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        for (int i = 0; i < keys.size(); i++) {
            String groupId = keys.get(i);
            if (groupId == null) {
                continue;
            }
            RedisLock lock = new RedisLock(redisTemplate, String.format("%s%s", keyPrefix, groupId));
            try {
                if (lock.lock()) {
                    redisLocks.add(lock);
                }
            } catch (Exception e) {
                throw new BusinessValidateException("产品分组信息正在处理中，请稍后再试！", e);
            }
        }
        return redisLocks;
    }

    // 解锁 redis
    private void unlockRedis(List<RedisLock> redisLocks) {
        if (CollectionUtils.isNotEmpty(redisLocks)) {
            for (RedisLock redisLock : redisLocks) {
                if (redisLock == null) {
                    continue;
                }
                redisLock.unlock();
            }
        }
    }

    public void preconditioning() {
        long sleepTime = ThreadLocalRandom.current().nextLong(300, 1500);
        try {
            TimeUnit.MILLISECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
