package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductRelationGroupPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WareHouseMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品关系分组查询BL
 */
@Service
public class ProductRelationGroupQueryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductRelationGroupQueryBL.class);

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Autowired
    private ProductRelationGroupPOMapper productRelationGroupPOMapper;

    @Reference
    private IInStockQueryService iInStockQueryService;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private WareHouseMapper wareHouseMapper;

    /**
     * 根据仓库类型\skuId查询产品分组信息（判断是否开启货位库存及是否店仓仓库）
     */
    public Map<ProductSkuDTO, List<ProductSkuDTO>> findGroupTotalProductBySkuIds(Integer warehouseId,
        List<Long> skuIdList) {
        LOGGER.info("仓库[{}]产品分组关系查询SKU集合：{}", warehouseId, JSON.toJSONString(skuIdList));
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(skuIdList, "产品信息不能为空");
        return Collections.emptyMap();
        // 是否店仓仓库：店仓仓库走混盘
        // Boolean ownerInfoErase = iInStockQueryService.findOwnerInfoEraseWarehouseConfig(warehouseId);
        // // 是否开启货位库存 -1:未选择, 0:否，1:是
        // WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
        // boolean isOpenCargoStock =
        // WarehouseConfigConstants.CARGO_STOCK_ON.equals(warehouseConfigDTO.getIsOpenCargoStock());
        // LOGGER.info("仓库[{}] 是否店仓仓库:{} 是否开启货位库存：{}", warehouseId, ownerInfoErase, isOpenCargoStock);
        // if (!ownerInfoErase || isOpenCargoStock) {
        // return Collections.EMPTY_MAP;
        // }
        // return findGroupTotalProductDirect(warehouseId, skuIdList);
    }

    /**
     * 根据仓库类型\skuId查询产品分组信息（判断是否开启货位库存及是否店仓仓库）
     */
    public Map<Long, List<ProductSkuDTO>> findSameGroupProductBySkuIds(Integer warehouseId, List<Long> skuIdList) {
        // Map<ProductSkuDTO, List<ProductSkuDTO>> groupMap = findGroupTotalProductBySkuIds(warehouseId, skuIdList);
        // if (groupMap == null) {
        // return Collections.EMPTY_MAP;
        // }
        // Map<Long, List<ProductSkuDTO>> result = new HashMap<>(16);
        // for (Map.Entry<ProductSkuDTO, List<ProductSkuDTO>> entry : groupMap.entrySet()) {
        // if (entry == null || entry.getKey() == null) {
        // continue;
        // }
        // result.put(entry.getKey().getProductSkuId(), entry.getValue());
        // }
        return Collections.emptyMap();
    }

    /**
     * 直接查询仓库产品分组信息（数据库中有就返回）
     */
    public Map<Long, List<ProductSkuDTO>> findSameGroupProductDirect(Integer warehouseId, List<Long> skuIdList) {
        // Map<ProductSkuDTO, List<ProductSkuDTO>> groupMap = findGroupTotalProductDirect(warehouseId, skuIdList);
        // if (groupMap == null) {
        // return Collections.EMPTY_MAP;
        // }
        // Map<Long, List<ProductSkuDTO>> result = new HashMap<>(16);
        // for (Map.Entry<ProductSkuDTO, List<ProductSkuDTO>> entry : groupMap.entrySet()) {
        // if (entry == null || entry.getKey() == null) {
        // continue;
        // }
        // result.put(entry.getKey().getProductSkuId(), entry.getValue());
        // }
        return Collections.emptyMap();
    }

    /**
     * 直接查询仓库产品分组信息（数据库中有就返回）
     */
    public Map<ProductSkuDTO, List<ProductSkuDTO>> findGroupTotalProductDirect(Integer warehouseId,
        List<Long> skuIdList) {
        LOGGER.info("仓库[{}]直接查询产品分组关系SKU集合：{}", warehouseId, JSON.toJSONString(skuIdList));
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(skuIdList, "产品信息不能为空");
        return Collections.emptyMap();
        // sku 去重
        // List<Long> afterDupSkuIdList = skuIdList.stream().filter(e -> e !=
        // null).distinct().collect(Collectors.toList());
        // // 查询完整信息
        // List<ProductRelationGroupPO> groupPOList = productRelationGroupPOMapper.selectTotalGroupConfig(new
        // ProductTotalGroupQueryDTO(warehouseId, afterDupSkuIdList));
        // if (CollectionUtils.isEmpty(groupPOList)) {
        // return Collections.EMPTY_MAP;
        // }
        // List<Long> totalSkuIdList = groupPOList.stream().filter(e -> e !=
        // null).map(ProductRelationGroupPO::getProductSkuId).distinct().collect(Collectors.toList());
        // // 增加查询sku信息
        // totalSkuIdList.addAll(skuIdList);
        // // 先处理关联产品
        // List<ProductSkuDTO> allSkuDTOList = productSkuQueryBL.findSkuInfoWithConfig(warehouseId, totalSkuIdList);
        // // 按关联产品sku转成 map
        // Map<Long, ProductSkuDTO> allSkuMap = allSkuDTOList.stream().filter(e -> e != null).collect(Collectors.toMap(e
        // -> e.getProductSkuId(), Function.identity()));
        // // 产品所在分组ID Map
        // Map<Long, Long> productGroupFlagMap = groupPOList.stream().filter(e -> e != null &&
        // afterDupSkuIdList.contains(e.getProductSkuId()))
        // .collect(Collectors.toMap(ProductRelationGroupPO::getProductSkuId, ProductRelationGroupPO::getGroupId, (v1,
        // v2) -> v1 != null ? v1 : v2));
        // // 分组产品集合
        // Map<Long, List<ProductRelationGroupPO>> groupProductMap = groupPOList.stream().filter(e -> e !=
        // null).collect(Collectors.groupingBy(ProductRelationGroupPO::getGroupId));
        // Map<ProductSkuDTO, List<ProductSkuDTO>> result = new HashMap<>(16);
        // for (Long sku : afterDupSkuIdList) {
        // ProductSkuDTO mainSkuDTO = allSkuMap.get(sku);
        // if (mainSkuDTO == null) {
        // LOGGER.info("仓库[{}] 产品SKU [{}] 信息不存在！", warehouseId, sku);
        // continue;
        // }
        // Long groupId = productGroupFlagMap.get(sku);
        // if (groupId == null) {
        // continue;
        // }
        // List<ProductRelationGroupPO> groupProducts = groupProductMap.get(groupId);
        // if (CollectionUtils.isEmpty(groupProducts)) {
        // continue;
        // }
        // List<ProductSkuDTO> refSkuList = groupProducts.stream()
        // .filter(e -> e != null && !Objects.equals(e.getProductSkuId(), sku))
        // .map(e -> e.getProductSkuId()).distinct().filter(e -> e != null)
        // .map(e -> {
        // ProductSkuDTO productSkuDTO = allSkuMap.get(e);
        // if (productSkuDTO == null) {
        // LOGGER.info("仓库[{}] 产品SKU [{}] 信息不存在！", warehouseId, e);
        // return null;
        // }
        // productSkuDTO.setProductRelationGroupId(groupId);
        // return productSkuDTO;
        // }).filter(e -> e != null).collect(Collectors.toList());
        // if (CollectionUtils.isEmpty(refSkuList)) {
        // LOGGER.info("仓库[{}] 产品SKU [{}] 分组ID[{}] 排除自身后没有其他关联产品！", warehouseId, sku, groupId);
        // continue;
        // }
        // // 设置分组ID
        // mainSkuDTO.setProductRelationGroupId(groupId);
        // result.put(mainSkuDTO, refSkuList);
        // }
        // return result;
    }

    public Map<Long, List<Long>> findGroupProductDirect(Integer warehouseId, List<Long> skuIdList) {
        LOGGER.info("findGroupProductDirect - 仓库[{}]直接查询产品分组关系SKU集合：{}", warehouseId, JSON.toJSONString(skuIdList));
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(skuIdList, "产品信息不能为空");
        return Collections.emptyMap();
        // // sku 去重
        // List<Long> afterDupSkuIdList = skuIdList.stream().filter(e -> e !=
        // null).distinct().collect(Collectors.toList());
        // // 查询完整信息
        // List<ProductRelationGroupPO> groupPOList = productRelationGroupPOMapper.selectTotalGroupConfig(new
        // ProductTotalGroupQueryDTO(warehouseId, afterDupSkuIdList));
        // if (CollectionUtils.isEmpty(groupPOList)) {
        // return Collections.EMPTY_MAP;
        // }
        // // 产品所在分组ID Map
        // Map<Long, Long> productGroupFlagMap = groupPOList.stream().filter(e -> e != null &&
        // afterDupSkuIdList.contains(e.getProductSkuId()))
        // .collect(Collectors.toMap(ProductRelationGroupPO::getProductSkuId, ProductRelationGroupPO::getGroupId, (v1,
        // v2) -> v1 != null ? v1 : v2));
        // // 分组产品集合
        // Map<Long, List<ProductRelationGroupPO>> groupProductMap = groupPOList.stream().filter(e -> e !=
        // null).collect(Collectors.groupingBy(ProductRelationGroupPO::getGroupId));
        // Map<Long, List<Long>> result = new HashMap<>(16);
        // for (Long sku : afterDupSkuIdList) {
        // Long groupId = productGroupFlagMap.get(sku);
        // if (groupId == null) {
        // continue;
        // }
        // List<ProductRelationGroupPO> groupProducts = groupProductMap.get(groupId);
        // if (CollectionUtils.isEmpty(groupProducts)) {
        // continue;
        // }
        // List<Long> refSkuList = groupProducts.stream().filter(e -> e != null && !Objects.equals(e.getProductSkuId(),
        // sku))
        // .map(e -> e.getProductSkuId()).distinct().collect(Collectors.toList());
        // if (CollectionUtils.isEmpty(refSkuList)) {
        // LOGGER.info("仓库[{}] 产品SKU [{}] 分组ID[{}] 排除自身后没有其他关联产品！", warehouseId, sku, groupId);
        // continue;
        // }
        // result.put(sku, refSkuList);
        // }
        // return result;
    }

    public List<Integer> findDianCangWareHouseList() {
        List<WareHouseDTO> dianCangWareHouseList = wareHouseMapper.findDianCangWareHouseList();
        if (CollectionUtils.isEmpty(dianCangWareHouseList)) {
            return Collections.EMPTY_LIST;
        }
        return dianCangWareHouseList.stream().filter(e -> e != null && e.getId() != null).map(e -> e.getId())
            .collect(Collectors.toList());
    }

    /**
     * 统计指定仓库有多少关联关系
     */
    public int countRelationByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        return productRelationGroupPOMapper.countRelationByWarehouseId(warehouseId);
    }
}
