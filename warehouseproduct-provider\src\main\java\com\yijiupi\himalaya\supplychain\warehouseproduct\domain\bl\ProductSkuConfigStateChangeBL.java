package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseClassEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuConfigPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应SKU停用启用
 * <AUTHOR>
 * @Date 2025/5/21 11:44
 * @Version 1.0
 */
@Component
public class ProductSkuConfigStateChangeBL {
	private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuConfigStateChangeBL.class);
	private static final int BATCH_QUERY_SIZE = 1000;
	@Reference(timeout = 60000)
	private IWarehouseQueryService warehouseQueryService;
	@Autowired
	private ProductSkuConfigPOMapper productSkuConfigPOMapper;
	@Autowired
	private ProductLocationPOMapper productLocationPOMapper;
	// 限流器，每秒允许执行次数
	private final RateLimiter rateLimiter = RateLimiter.create(1);

	private static final List<Integer> EXCLUDE_WAREHOUSE_ID_List = Arrays.asList(1, 2);
	// 已下架无库存的作废SKU
	@XxlJob("wms_batchDisableProductSkuConfigState")
	public void batchDisableProductSkuConfigState() {
		LOGGER.info("开始按仓库调度，已下架无库存的作废SKU");

		List<Warehouse> warehouseList = new ArrayList<>();
		String warehouseIdStr = XxlJobContext.getXxlJobContext().getJobParam();
		if (StringUtils.isNotEmpty(warehouseIdStr) && !Objects.equals(warehouseIdStr, "{}")) {
			List<Integer> warehouseIdList = Arrays.stream(warehouseIdStr.split("、"))
					.filter(p -> !StringUtils.isEmpty(p) && StringUtils.isNumeric(p)).map(p -> Integer.valueOf(p))
					.collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(warehouseIdList)) {
				warehouseList = warehouseQueryService.listWarehouseByIds(warehouseIdList);
			}
		} else {
			// 已启用仓库
			// 城市仓库((byte) 0),
			// 总部仓库((byte) 1),
			// 集货点((byte) 5),
			// 店仓合一((byte) 6),
			// 前置仓((byte) 8)
			warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0, 1, 5, 6, 8)).stream()
					.filter(p -> p != null && p.getCityId() != null && p.getCityId() >= 100 && p.getCityId() <= 900
							&& !Objects.equals(WarehouseClassEnum.虚仓.getType(), p.getWarehouseClass())
							&& !EXCLUDE_WAREHOUSE_ID_List.contains(p.getId()))
					.collect(Collectors.toList());
		}
		if (CollectionUtils.isEmpty(warehouseList)) {
			LOGGER.info("[已下架无库存的作废SKU]没有查询到任何仓库");
			return;
		}

		for (Warehouse warehouse : warehouseList) {
			try {
				LOGGER.info("按仓库调整供应链SKU状态为作废:warehouseId:{}, warehouseName:{}", warehouse.getId(), warehouse.getName());
				disableProductSkuConfigState(warehouse.getId());
			} catch (Exception ex) {
				LOGGER.error("已下架无库存的作废SKU任务异常:warehouseId={}:name={}:", warehouse.getId(), warehouse.getName(), ex);
			}
		}
	}

	/**
	 * 定时调度 -> 库SKU状态变更为作废
	 * 久批SKU下架(productsku.ProductState=0)，仓库SKU状态为正常(productskuconfig.State=1)，库存为0，且最后一次入库记录至今超过了3个月（90天）
	 */
	public void disableProductSkuConfigState(Integer warehouseId) {
		AssertUtils.notNull(warehouseId, "warehouseId不能为空");
		Long maxId = productSkuConfigPOMapper.getSkuOffShelfAndStockZeroThreeMonthsAgoMaxId(warehouseId);
		Long minId = maxId;

		StopWatch stopWatch = new StopWatch("按仓库调整供应链SKU状态为作废:warehouseId=" + warehouseId);
		stopWatch.start();

		while (true) {
			List<Long> skuConfigIdIds = productSkuConfigPOMapper.findSkuOffShelfAndStockZeroThreeMonthsAgo(warehouseId, minId);
			if (CollectionUtils.isEmpty(skuConfigIdIds)) {
				break;
			}

			// 处理当前批次的 skuIds
			LOGGER.info("处理当前批次的:warehouseId={}:skuConfigIdIds.size={}", warehouseId, skuConfigIdIds.size());
			disableProductSku(skuConfigIdIds);

			// 取 skuIds 最小的 id
			Optional<Long> newMinId = skuConfigIdIds.stream().min(Long::compare);
			if (newMinId.isPresent()) {
				minId = newMinId.get();
			} else {
				break;
			}
		}

		stopWatch.stop();
		LOGGER.info(stopWatch.toString());
	}

	// 仓库SKU状态变更为作废 -> 删除所有关联货位
	private void disableProductSku(List<Long> skuConfigIdIds) {
		// 1000个一批处理
		List<List<Long>> partitions = Lists.partition(skuConfigIdIds, BATCH_QUERY_SIZE);
		for (List<Long> skuConfigIdIdsPartition : partitions) {
			// 获取许可，如果没有可用许可，会阻塞直到有可用许可
			double waitTime = rateLimiter.acquire();
			/// LOGGER.info("获取许可，等待时间：{}ms", waitTime);

			List<ProductSkuConfigPO> skuConfigById = productSkuConfigPOMapper.findSkuConfigById(skuConfigIdIdsPartition);
			List<Long> skuIdList = skuConfigById.stream().map(ProductSkuConfigPO::getProductSkuId).collect(Collectors.toList());

			List<ProductLocationPO> productLocationPOList = productLocationPOMapper.selectBySkuIds(skuIdList);
			if (CollectionUtils.isEmpty(productLocationPOList)) {
				productSkuConfigPOMapper.disableProductSku(skuConfigIdIdsPartition);
				LOGGER.info("SKU作废，停用SKU成功:skuConfigIdList={}", skuConfigIdIdsPartition);
			} else {
				List<Long> productLocationIds = productLocationPOList.stream().map(ProductLocationPO::getId).collect(Collectors.toList());
				productLocationPOMapper.deleteBatch(productLocationIds);
				productSkuConfigPOMapper.disableProductSku(skuConfigIdIdsPartition);
				LOGGER.info("SKU作废，删除关联货位及停用SKU成功:skuConfigIdList={}:productLocationPOList={}", skuConfigIdIdsPartition, JSON.toJSONString(productLocationPOList));
			}

			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
	}

	// 库存变更消息 -> 仓库SKU状态变更为正常
	@RabbitListener(queues = "mq.supplychain.productSkuConfigState.change")
	public void productSkuConfigStateEnable(List<WarehouseInventoryChangeBO> changeMessages) {
		LOGGER.info("收到库存变更消息:changeMessages={}", JSON.toJSONString(changeMessages));

		if (CollectionUtils.isEmpty(changeMessages)) {
			return;
		}

		// 按warehouseId分组, key为warehouseId, value为skuId列表, 过滤条件count>0
		Map<Integer, List<Long>> abc = changeMessages.stream().filter(item -> item.getCount().compareTo(BigDecimal.ZERO) > 0)
				.collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getWarehouseId, Collectors.mapping(WarehouseInventoryChangeBO::getProductSkuId, Collectors.toList())));

		if (CollectionUtils.isEmpty(abc)) {
			return;
		}

		// 遍历分组
		abc.forEach((key, value) -> {
			// 根据warehouseId和skuId查询库存配置
			List<ProductSkuConfigPO> productSkuConfigList = productSkuConfigPOMapper.findDisableSkuConfigBySkuIdsAndWarehouseId(key, value);
			if (CollectionUtils.isEmpty(productSkuConfigList)) {
				return;
			}

			// 根据主键ID更新
			List<Long> skuConfigIdList = productSkuConfigList.stream().map(ProductSkuConfigPO::getId).collect(Collectors.toList());
			productSkuConfigPOMapper.enableProductSku(skuConfigIdList);
			LOGGER.info("收到库存变更消息:SKU启用成功:warehouseId={}, skuIdList={}, ", key, value);
		});
	}

}
