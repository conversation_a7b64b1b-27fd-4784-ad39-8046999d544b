package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:10
 * @Version 1.0
 */
@Component
//@Profile({"dev", "release", "pre"})
public class ProductSkuConfigStateChangeJob {
	private static final Logger LOG = LoggerFactory.getLogger(ProductSkuConfigStateChangeJob.class);

	@Autowired
	private ProductSkuConfigStateChangeBL productSkuConfigStateChangeBL;

	// 每天晚上23点执行
	// @Scheduled(cron = "0 0 23 * *?")
	public void execute() {
		LOG.info("ProductSkuConfigStateChangeJob execute");
		productSkuConfigStateChangeBL.batchDisableProductSkuConfigState();
	}
}
