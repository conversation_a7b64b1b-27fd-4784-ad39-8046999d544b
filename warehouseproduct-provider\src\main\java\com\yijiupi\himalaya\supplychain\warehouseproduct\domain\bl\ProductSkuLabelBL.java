package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuLabelMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LabelEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LabelTypeEnum;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品标签管理
 *
 * <AUTHOR>
 * @date 2019-11-26 17:44
 */
@Service
public class ProductSkuLabelBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuLabelBL.class);

    @Autowired
    private ProductSkuLabelMapper productSkuLabelMapper;

    /**
     * 保存产品标签
     */
    public void saveProductSkuLabel(ProductSkuLabelDTO productSkuLabelDTO) {
        AssertUtils.notNull(productSkuLabelDTO, "保存产品标签参数不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getProductSkuId(), "skuID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getLabelType(), "产品标签类型不能为空");

        // 配送标签
        if (Objects.equals(productSkuLabelDTO.getLabelType(), LabelTypeEnum.配送.getType())) {
            AssertUtils.notNull(productSkuLabelDTO.getLabelId(), "产品标签id不能为空");
            if (Objects.equals(productSkuLabelDTO.getLabelId(), LabelEnum.仓配.getTypeId())) {
                productSkuLabelDTO.setLabelName(LabelEnum.仓配.name());
            } else if (Objects.equals(productSkuLabelDTO.getLabelId(), LabelEnum.直配.getTypeId())) {
                productSkuLabelDTO.setLabelName(LabelEnum.直配.name());
            }
        }

        ProductSkuLabelPO productSkuLabelPO = new ProductSkuLabelPO();
        BeanUtils.copyProperties(productSkuLabelDTO, productSkuLabelPO);

        // 判断当前产品的标签是否存在，不存在新增，存在则更新
        ProductSkuLabelPO oldProductSkuLabelPO = productSkuLabelMapper.getProductSkuLabelByType(
            productSkuLabelPO.getOrgId(), productSkuLabelPO.getWarehouseId(), productSkuLabelPO.getProductSkuId(),
            productSkuLabelPO.getLabelType());
        if (oldProductSkuLabelPO == null) {
            productSkuLabelPO.setId(UUIDGenerator.getUUID(ProductSkuLabelPO.class.getName()));
            productSkuLabelMapper.insertSelective(productSkuLabelPO);
            LOGGER.info("保存产品标签：{}", JSON.toJSONString(productSkuLabelPO));
        } else {
            productSkuLabelPO.setId(oldProductSkuLabelPO.getId());
            productSkuLabelMapper.updateByPrimaryKeySelective(productSkuLabelPO);
            LOGGER.info("修改产品标签：{}", JSON.toJSONString(productSkuLabelPO));
        }
    }

    /**
     * 删除产品标签
     */
    public void deleteProductSkuLabel(ProductSkuLabelDTO productSkuLabelDTO) {
        AssertUtils.notNull(productSkuLabelDTO, "保存产品标签参数不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getProductSkuId(), "skuID不能为空");
        AssertUtils.notNull(productSkuLabelDTO.getLabelType(), "产品标签类型不能为空");

        ProductSkuLabelPO oldProductSkuLabelPO = productSkuLabelMapper.getProductSkuLabelByType(
            productSkuLabelDTO.getOrgId(), productSkuLabelDTO.getWarehouseId(), productSkuLabelDTO.getProductSkuId(),
            productSkuLabelDTO.getLabelType());
        if (oldProductSkuLabelPO != null) {
            productSkuLabelMapper.deleteByPrimaryKey(oldProductSkuLabelPO.getId());
            LOGGER.info("删除产品标签：{}", JSON.toJSONString(oldProductSkuLabelPO));
        }
    }

    /**
     * 根据skuId查询是否是直配产品
     * 
     * @return key: skuId value: true=直配
     */
    public Map<Long, Boolean> getProductByDirectDeliveryMap(ProductSkuLabelSO so) {
        // 查询产品的配送标签类型
        Map<Long, String> deliveryMap = getProductSkuLabelByDeliveryMap(so);
        // 判断是否是直配产品
        Map<Long, Boolean> resultMap = new HashMap<>(16);
        so.getProductSkuIds().forEach(skuId -> {
            if (Objects.equals(deliveryMap.get(skuId), LabelEnum.直配.getTypeId())) {
                // 是直配产品
                resultMap.put(skuId, true);
            } else {
                resultMap.put(skuId, false);
            }
        });
        return resultMap;
    }

    /**
     * 查询产品的配送标签类型
     * 
     * @return key: skuId value: 0-仓配 1-直配
     */
    public Map<Long, String> getProductSkuLabelByDeliveryMap(ProductSkuLabelSO so) {
        AssertUtils.notNull(so, "参数不能为空");
        AssertUtils.notEmpty(so.getProductSkuIds(), "产品skuId不能为空");

        so.setLabelType(LabelTypeEnum.配送.getType());
        List<ProductSkuLabelPO> productSkuLabelPOS = productSkuLabelMapper.listProductSkuLabel(so);

        Map<Long, String> resultMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(productSkuLabelPOS)) {
            productSkuLabelPOS.forEach(p -> {
                resultMap.put(p.getProductSkuId(), p.getLabelId());
            });
        }
        return resultMap;
    }

    /**
     * 查询产品在本仓库是否可销售
     * 
     * @return key: skuId value: true 可销售
     */
    public Map<Long, Boolean> getProductSkuLabelByEnableSellMap(ProductSkuLabelSO so) {
        AssertUtils.notNull(so, "参数不能为空");
        AssertUtils.notNull(so.getOrgId(), "城市id不能为空");
        AssertUtils.notEmpty(so.getProductSkuIds(), "产品skuId不能为空");

        so.setLabelType(LabelTypeEnum.可销售.getType());
        List<ProductSkuLabelPO> productSkuLabelPOS = productSkuLabelMapper.listProductSkuLabel(so);

        Map<Long, Boolean> resultMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(productSkuLabelPOS)) {
            productSkuLabelPOS.forEach(p -> {
                resultMap.put(p.getProductSkuId(), true);
            });
        }
        return resultMap;
    }

    /**
     * 查询指定仓库下可销售的产品
     * 
     * @return
     */
    public List<Long> getEnableSellProductByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        ProductSkuLabelSO so = new ProductSkuLabelSO();
        so.setWarehouseId(warehouseId);
        so.setLabelType(LabelTypeEnum.可销售.getType());
        List<ProductSkuLabelPO> productSkuLabelPOS = productSkuLabelMapper.listProductSkuLabel(so);
        if (CollectionUtils.isEmpty(productSkuLabelPOS)) {
            return Collections.emptyList();
        }
        return productSkuLabelPOS.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
    }

}
