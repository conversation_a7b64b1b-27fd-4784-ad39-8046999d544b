/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoSpecificationPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoSpecificationPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductFeatureConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuBySaasService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSkuPOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.mq.ExceptionOrderSyncMQ;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelevanceconfig.AddProductRelevanceConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LabelTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品操作
 *
 * <AUTHOR>
 * @since 2018年10月31日 下午5:06:39
 */
@Service
public class ProductSkuProcessBL {
    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;
    @Autowired
    private ProductSkuPOConvertor productSkuConvertor;
    @Autowired
    private ProductInfoSpecificationPOMapper productInfoSpecificationPOMapper;
    @Autowired
    private ExceptionOrderSyncMQ exceptionOrderSyncMQ;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;
    @Autowired
    private ProductSkuLabelBL productSkuLabelBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    // @Reference
    // private IProductSkuIndexSyncService iProductSkuIndexSyncService;
    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;
    @Reference
    private IProductSkuBySaasService productSkuBySaasService;
    @Autowired
    private AsyncProductSkuUpdateBL asyncProductSkuUpdateBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCategoryBL.class);

    /**
     * update产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void update(ProductSkuDTO queryDto) {
        AssertUtils.notNull(queryDto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(queryDto.getProductSkuId(), "产品skuId不能为空");
        AssertUtils.notNull(queryDto.getProductSpecificationId(), "产品规格Id不能为空");
        ProductSkuPO selectByPrimaryKey = productSkuPOMapper.selectByCityIdAndProductSkuId(queryDto.getProductSkuId());
        if (selectByPrimaryKey == null) {
            throw new DataValidateException(String.format("skuId：%s不存在!", queryDto.getProductSkuId()));
        }
        // 验证库存比例加起来等于100%
        validateInventoryRatio(queryDto.getInventoryRatioList());
        // 更新产品关联配置
        setProductRelevanceConfig(queryDto);
        // 产品是否补全
        setIsComplete(queryDto);
        // 更新产品sku
        ProductSkuPO productSkuPO = productSkuConvertor.reverseConvert(queryDto);
        // 保存产品sku配置
        saveProductSkuConfig(Collections.singletonList(productSkuPO));
        // 更新产品规格
        ProductInfoSpecificationPO spec = convertToSpec(queryDto);
        if (null != spec) {
            productInfoSpecificationPOMapper.updateByPrimaryKeySelective(spec);
        }
        // 更新产品的配送标签
        updateProductLabelByDelivery(queryDto);
        // 更新产品在本仓库是否可销售
        updateProductLabelByEnableSell(queryDto);
    }

    /**
     * 保存产品sku配置
     */
    private void saveProductSkuConfig(List<ProductSkuPO> productSkuPOS) {
        if (CollectionUtils.isEmpty(productSkuPOS)) {
            return;
        }
        List<ProductSkuConfigDTO> configDTOS = productSkuPOS.stream().map(skuPO -> {
            ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
            BeanUtils.copyProperties(skuPO, configDTO);
            configDTO.setProductSkuId(skuPO.getProductSku_Id());
            configDTO.setUnpackage(skuPO.getUnpackage() == null ? null
                : (skuPO.getUnpackage() ? ProductFeatureConstant.拆包 : ProductFeatureConstant.不拆包));
            configDTO
                .setProductFeature(skuPO.getProductFeature() == null ? null : skuPO.getProductFeature().intValue());
            return configDTO;
        }).collect(Collectors.toList());
        productSkuConfigBL.saveProductSkuConfigBatch(configDTOS);
    }

    /**
     * 更新产品的配送标签
     */
    private void updateProductLabelByDelivery(ProductSkuDTO queryDto) {
        ProductSkuLabelDTO productSkuLabelDTO = new ProductSkuLabelDTO();
        productSkuLabelDTO.setOrgId(queryDto.getCityId());
        productSkuLabelDTO.setWarehouseId(queryDto.getWarehouseId());
        productSkuLabelDTO.setProductSkuId(queryDto.getProductSkuId());
        productSkuLabelDTO.setLabelType(LabelTypeEnum.配送.getType());
        if (StringUtils.isNotEmpty(queryDto.getDeliveryLabelId())) {
            // 保存或修改
            productSkuLabelDTO.setLabelId(queryDto.getDeliveryLabelId());
            productSkuLabelBL.saveProductSkuLabel(productSkuLabelDTO);
        } else {
            // 删除
            productSkuLabelBL.deleteProductSkuLabel(productSkuLabelDTO);
        }
    }

    /**
     * 更新产品在本仓库是否可销售
     */
    private void updateProductLabelByEnableSell(ProductSkuDTO queryDto) {
        // 不修改是否可销售属性时，判断该产品是否可销售，若可销售，则同步给ES
        if (queryDto.getEnableSell() == null) {
            // 查询产品是否可销售
            ProductSkuLabelSO so = new ProductSkuLabelSO();
            so.setOrgId(queryDto.getCityId());
            so.setWarehouseId(queryDto.getWarehouseId());
            List<Long> skuIdList = new ArrayList<>();
            skuIdList.add(queryDto.getProductSkuId());
            so.setProductSkuIds(skuIdList);
            // Map<Long, Boolean> enableSellMap = productSkuLabelBL.getProductSkuLabelByEnableSellMap(so);
            // if (enableSellMap != null && Objects.equals(enableSellMap.get(queryDto.getProductSkuId()), true)) {
            // // 同步给ES
            // ProductSkuSyncDTO skuSyncDTO = new ProductSkuSyncDTO();
            // skuSyncDTO.setWarehouseId(queryDto.getWarehouseId());
            // skuSyncDTO.setProductSkuIdList(skuIdList);
            // skuSyncDTO.setSyncType(ProductSkuSyncTypeEnum.条件同步.getType());
            // iProductSkuIndexSyncService.syncProductSkuIndex(skuSyncDTO);
            // }

            // 修改是否可销售属性时，都要同步给ES
        } else {

            ProductSkuLabelDTO productSkuLabelDTO = new ProductSkuLabelDTO();
            productSkuLabelDTO.setOrgId(queryDto.getCityId());
            productSkuLabelDTO.setWarehouseId(queryDto.getWarehouseId());
            productSkuLabelDTO.setProductSkuId(queryDto.getProductSkuId());
            productSkuLabelDTO.setLabelType(LabelTypeEnum.可销售.getType());
            // // ES同步类型
            // Byte syncType = ProductSkuSyncTypeEnum.条件同步.getType();
            // 设置为可销售时
            if (Objects.equals(queryDto.getEnableSell(), ConditionStateEnum.是.getType())) {
                productSkuLabelBL.saveProductSkuLabel(productSkuLabelDTO);
                // 设置为不可销售时，删除
            } else {
                productSkuLabelBL.deleteProductSkuLabel(productSkuLabelDTO);
                // syncType = ProductSkuSyncTypeEnum.删除产品索引.getType();
            }

            // // 同步给ES
            // ProductSkuSyncDTO skuSyncDTO = new ProductSkuSyncDTO();
            // skuSyncDTO.setWarehouseId(queryDto.getWarehouseId());
            // List<Long> skuIdList = new ArrayList<>();
            // skuIdList.add(queryDto.getProductSkuId());
            // skuSyncDTO.setProductSkuIdList(skuIdList);
            // skuSyncDTO.setSyncType(syncType);
            // iProductSkuIndexSyncService.syncProductSkuIndex(skuSyncDTO);
        }
    }

    /**
     * 更新产品关联配置
     */
    private void setProductRelevanceConfig(ProductSkuDTO queryDto) {
        // 更新产品关联配置
        if (CollectionUtils.isNotEmpty(queryDto.getRefProductSkuIdList())) {
            AddProductRelevanceConfigDTO configDTO = new AddProductRelevanceConfigDTO();
            configDTO.setCityId(queryDto.getCityId());
            configDTO.setWarehouseId(queryDto.getWarehouseId());
            configDTO.setProductSkuId(queryDto.getProductSkuId());
            configDTO.setRefProductSkuIdList(queryDto.getRefProductSkuIdList());
            // 新增产品关联配置
            // productRelevanceConfigBL.insertConfig(configDTO);

            // 产品关联状态
            queryDto.setProductRelevantState(ConditionStateEnum.是.getType());
        } else {
            List<Integer> warehouseIds = new ArrayList<>();
            warehouseIds.add(queryDto.getWarehouseId());
            List<Long> productSkuIds = new ArrayList<>();
            productSkuIds.add(queryDto.getProductSkuId());
            // 删除产品关联配置
            // productRelevanceConfigBL.deleteProductRelationBySkuIds(warehouseIds, productSkuIds);

            // 产品关联状态
            queryDto.setProductRelevantState(ConditionStateEnum.否.getType());
        }
    }

    /**
     * 产品是否补全
     */
    private void setIsComplete(ProductSkuDTO queryDto) {
        byte isComplete = 0;
        // 如果长宽高都有数据,,就手动写一下体积
        if (queryDto.getLength() != null && !Objects.equals(queryDto.getLength(), Double.valueOf(0))
            && queryDto.getWidth() != null && !Objects.equals(queryDto.getWidth(), Double.valueOf(0))
            && queryDto.getHeight() != null && !Objects.equals(queryDto.getHeight(), Double.valueOf(0))) {
            // 体积
            queryDto
                .setVolume(String.format("%s*%s*%s", queryDto.getLength(), queryDto.getWidth(), queryDto.getHeight()));

            // 是否补全信息
            if (queryDto.getWeight() != null && !Objects.equals(queryDto.getWeight(), Double.valueOf(0))
                && queryDto.getMaxInventory() != null && queryDto.getMaxInventory().compareTo(BigDecimal.ZERO) != 0
                && queryDto.getMinInventory() != null && queryDto.getMinInventory().compareTo(BigDecimal.ZERO) != 0
                && queryDto.getMaxReplenishment() != null
                && queryDto.getMaxReplenishment().compareTo(BigDecimal.ZERO) != 0
                && queryDto.getMinReplenishment() != null
                && queryDto.getMinReplenishment().compareTo(BigDecimal.ZERO) != 0) {
                isComplete = 1;
            }
        }
        // 是否补全信息
        queryDto.setIsComplete(isComplete);
    }

    /**
     * 组建spec
     */
    private ProductInfoSpecificationPO convertToSpec(ProductSkuDTO productSkuQueryDTO) {
        if (productSkuQueryDTO.getHeight() == null && productSkuQueryDTO.getWidth() == null
            && productSkuQueryDTO.getWeight() == null && productSkuQueryDTO.getLength() == null
            && productSkuQueryDTO.getVolume() == null) {
            return null;
        }
        ProductInfoSpecificationPO spec = new ProductInfoSpecificationPO();
        spec.setId(productSkuQueryDTO.getProductSpecificationId());
        spec.setHeight(productSkuQueryDTO.getHeight());
        spec.setWidth(productSkuQueryDTO.getWidth());
        spec.setWeight(productSkuQueryDTO.getWeight());
        spec.setLength(productSkuQueryDTO.getLength());
        spec.setVolume(productSkuQueryDTO.getVolume());
        return spec;
    }

    /**
     * 批量update产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdate(List<ProductSkuDTO> productSkuDTOList) {
        AssertUtils.notEmpty(productSkuDTOList, "产品信息不能为空");
        productSkuDTOList.forEach(p -> {
            AssertUtils.notNull(p.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(p.getProductSkuId(), "产品skuId不能为空");
            AssertUtils.notNull(p.getProductSpecificationId(), "产品规格Id不能为空");
        });
        LOGGER.info("批量更新产品信息参数size：{}", productSkuDTOList.size());
        // 50个一组分批更新
        List<List<ProductSkuDTO>> lists = splitList(productSkuDTOList, 50);
        for (List<ProductSkuDTO> dtoList : lists) {
            // 产品是否补全
            dtoList.forEach(this::setIsComplete);
            // 批量更新产品sku
            List<ProductSkuPO> skuPOList =
                dtoList.stream().map(productSkuConvertor::reverseConvert).collect(Collectors.toList());
            // 保存产品sku配置
            saveProductSkuConfig(skuPOList);
            // 批量更新产品规格
            List<ProductInfoSpecificationPO> specPOList =
                dtoList.stream().map(this::convertToSpec).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(specPOList)) {
                productInfoSpecificationPOMapper.updateBatch(specPOList);
            }
        }
    }

    /**
     * 批量设置属性
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdateAttr(List<ProductSkuDTO> productSkuDTOList) {
        AssertUtils.notEmpty(productSkuDTOList, "产品信息不能为空");
        productSkuDTOList.forEach(p -> {
            AssertUtils.notNull(p.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(p.getProductSkuId(), "产品skuId不能为空");
        });
        LOGGER.info("批量设置属性参数size：{}", productSkuDTOList.size());
        // 100个一组分批更新
        List<List<ProductSkuDTO>> lists = splitList(productSkuDTOList, 100);
        for (List<ProductSkuDTO> dtoList : lists) {
            // 批量更新产品sku
            List<ProductSkuPO> skuPOList =
                dtoList.stream().map(productSkuConvertor::reverseConvert).collect(Collectors.toList());
            // 保存产品sku配置
            saveProductSkuConfig(skuPOList);

            // 更新产品在本仓库是否可销售
            dtoList.forEach(skuDto -> {
                updateProductLabelByEnableSell(skuDto);
            });
        }
    }

    /**
     * 批量设置库存占用比例
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdateInventoryRatio(ProductInventoryRatioUpdateDTO productInventoryRatioUpdateDTO) {
        AssertUtils.notNull(productInventoryRatioUpdateDTO, "批量设置库存占用比例参数不能为空");
        AssertUtils.notNull(productInventoryRatioUpdateDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(productInventoryRatioUpdateDTO.getProductSkuIdList(), "skuId不能为空");
        AssertUtils.notEmpty(productInventoryRatioUpdateDTO.getInventoryRatioList(), "库存占比不能为空");
        productInventoryRatioUpdateDTO.getInventoryRatioList().forEach(p -> {
            AssertUtils.notNull(p.getOwnerName(), "销售渠道不能为空");
            AssertUtils.notNull(p.getRatio(), "占用比例不能为空");
        });

        // 验证比例加起来等于100%
        validateInventoryRatio(productInventoryRatioUpdateDTO.getInventoryRatioList());

        // 库存占比转为json存储
        // 保存产品sku配置
        List<ProductSkuPO> productSkuPOS = productInventoryRatioUpdateDTO.getProductSkuIdList().stream().map(skuId -> {
            ProductSkuPO productSkuPO = new ProductSkuPO();
            productSkuPO.setProductSku_Id(skuId);
            productSkuPO.setWarehouseId(productInventoryRatioUpdateDTO.getWarehouseId());
            productSkuPO.setInventoryRatio(JSON.toJSONString(productInventoryRatioUpdateDTO.getInventoryRatioList()));
            return productSkuPO;
        }).collect(Collectors.toList());
        saveProductSkuConfig(productSkuPOS);
    }

    /**
     * 验证库存比例加起来等于100%
     */
    private void validateInventoryRatio(List<ProductSkuByInventoryRatioDTO> inventoryRatioList) {
        if (CollectionUtils.isNotEmpty(inventoryRatioList)) {
            if (new BigDecimal("100").compareTo(
                inventoryRatioList.stream().map(p -> p.getRatio()).reduce(BigDecimal.ZERO, BigDecimal::add)) != 0) {
                throw new BusinessValidateException("库存占用比例总数必须等于100%");
            }
        }
    }

    /**
     * 拆分list
     * 
     * @return
     */
    private static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 批量设置属性(是否独家、是否允许混放)
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdateAttrByUniqueAndFleeGoods(List<ProductSkuDTO> productSkuDTOList) {
        AssertUtils.notEmpty(productSkuDTOList, "产品信息不能为空");
        for (int i = 0; i < productSkuDTOList.size(); i++) {
            ProductSkuDTO skuDTO = productSkuDTOList.get(i);
            if (skuDTO == null) {
                continue;
            }
            AssertUtils.notNull(skuDTO.getWarehouseId(), "仓库Id不能为空");
            AssertUtils.notNull(skuDTO.getProductSkuId(), "产品skuId不能为空");
        }
        LOGGER.info("批量设置属性(是否独家、是否允许混放)参数size：{}", productSkuDTOList.size());
        // 100个一组分批更新
        List<List<ProductSkuDTO>> lists = Lists.partition(productSkuDTOList, 100);
        for (int i = 0; i < lists.size(); i++) {
            List<ProductSkuDTO> dtoList = lists.get(i);
            if (CollectionUtils.isEmpty(dtoList)) {
                continue;
            }
            // 批量更新产品sku
            List<ProductSkuPO> skuPOList =
                dtoList.stream().map(productSkuConvertor::reverseConvert).collect(Collectors.toList());
            // 保存产品sku配置
            saveProductSkuConfig(skuPOList);
        }
    }

    /**
     * 修改异常订单的产品信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateByExceptionOrder(List<ProductSkuDTO> productSkuList) {
        LOGGER.info("修改异常订单的产品信息参数：{}", JSON.toJSONString(productSkuList));
        // 校验数据
        validateExceptionOrderDTO(productSkuList);

        // 1、更新产品关联设置
        updateProductRelevanceConfig(productSkuList);

        // 2、更新产品属性
        List<ProductSkuPO> skuPOList =
            productSkuList.stream().map(productSkuConvertor::reverseConvert).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuPOList)) {
            // 保存产品sku配置
            saveProductSkuConfig(skuPOList);
        }

        // 3、更新产品关联货位
        updateProductLocation(productSkuList);

        // 4、发送异常订单处理成功消息给OMS
        sendMqToOms(productSkuList);
    }

    /**
     * 校验数据
     */
    private void validateExceptionOrderDTO(List<ProductSkuDTO> productSkuList) {
        AssertUtils.notEmpty(productSkuList, "产品不能为空");
        productSkuList.forEach(p -> {
            AssertUtils.notNull(p.getProductSkuId(), "产品skuID不能为空");
            AssertUtils.notNull(p.getCityId(), "城市ID不能为空");
            AssertUtils.notNull(p.getWarehouseId(), "仓库ID不能为空");
            // 设置关联产品状态
            if (CollectionUtils.isNotEmpty(p.getRefProductSkuIdList())) {
                p.setProductRelevantState(ConditionStateEnum.是.getType());
            } else {
                p.setProductRelevantState(ConditionStateEnum.否.getType());
            }
        });
    }

    /**
     * 更新产品关联设置
     */
    private void updateProductRelevanceConfig(List<ProductSkuDTO> productSkuList) {
        List<ProductRelevanceConfigDTO> configDTOList = new ArrayList<>();
        List<Long> productSkuIds = new ArrayList<>();
        List<Integer> warehouseIds = new ArrayList<>();
        productSkuList.forEach(skuDTO -> {
            // 新增
            if (CollectionUtils.isNotEmpty(skuDTO.getRefProductSkuIdList())) {
                skuDTO.getRefProductSkuIdList().forEach(refSkuId -> {
                    ProductRelevanceConfigDTO configDTO = new ProductRelevanceConfigDTO();
                    configDTO.setCityId(skuDTO.getCityId());
                    configDTO.setWarehouseId(skuDTO.getWarehouseId());
                    configDTO.setProductSkuId(skuDTO.getProductSkuId());
                    configDTO.setRefProductSkuId(refSkuId);
                    configDTOList.add(configDTO);
                });
                // 删除
            } else {
                warehouseIds.add(skuDTO.getWarehouseId());
                productSkuIds.add(skuDTO.getProductSkuId());
            }
        });

        // 新增或修改
        if (CollectionUtils.isNotEmpty(configDTOList)) {
            // productRelevanceConfigBL.insert(configDTOList);
        }

        // 删除
        if (CollectionUtils.isNotEmpty(warehouseIds) && CollectionUtils.isNotEmpty(productSkuIds)) {
            // productRelevanceConfigBL.deleteProductRelationBySkuIds(warehouseIds, productSkuIds);
        }
    }

    /**
     * 更新产品关联货位
     */
    private void updateProductLocation(List<ProductSkuDTO> productSkuList) {
        List<ProductLocationDTO> productLocationDTOList = new ArrayList<>();
        productSkuList.forEach(skuDTO -> {
            if (CollectionUtils.isNotEmpty(skuDTO.getLocationIdList())) {
                skuDTO.getLocationIdList().forEach(locationId -> {
                    ProductLocationDTO locationDTO = new ProductLocationDTO();
                    locationDTO.setCityId(skuDTO.getCityId());
                    locationDTO.setWarehouseId(skuDTO.getWarehouseId());
                    locationDTO.setProductSkuId(skuDTO.getProductSkuId());
                    locationDTO.setLocationId(locationId);
                    // 当前产品关联货位设置不存在时，才新增，否则跳过
                    if (!productLocationBL.isExistProductLocation(locationDTO.getWarehouseId(),
                        locationDTO.getProductSkuId(), locationDTO.getLocationId())) {
                        productLocationDTOList.add(locationDTO);
                    }
                });
            }
        });
        if (CollectionUtils.isNotEmpty(productLocationDTOList)) {
            productLocationBL.insertBatch(productLocationDTOList);
        }
    }

    /**
     * 发送异常订单处理成功消息给OMS
     * 
     * @param productSkuList
     */
    private void sendMqToOms(List<ProductSkuDTO> productSkuList) {
        Map<String, List<ProductSkuDTO>> skuMap = productSkuList.stream()
            .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getCityId(), p.getWarehouseId())));
        skuMap.forEach((k, skuList) -> {
            if (CollectionUtils.isNotEmpty(skuList)) {
                ExceptionOrderSyncDTO exceptionOrderSyncDTO = new ExceptionOrderSyncDTO();
                exceptionOrderSyncDTO.setCityId(skuList.get(0).getCityId());
                exceptionOrderSyncDTO.setWarehouseId(skuList.get(0).getWarehouseId());
                List<Long> productSkuIds = new ArrayList<>();
                skuList.forEach(sku -> {
                    // 设置了产品属性 && 有关联货位
                    if (productSkuQueryBL.isConfigProductAttr(sku.getUnique(), sku.getFleeGoods(),
                        sku.getProductRelevantState())
                        && isConfigProductLocation(exceptionOrderSyncDTO.getWarehouseId(), sku.getLocationIdList())) {
                        productSkuIds.add(sku.getProductSkuId());
                    }
                });
                if (CollectionUtils.isNotEmpty(productSkuIds)) {
                    exceptionOrderSyncDTO.setProductSkuIds(productSkuIds);
                    exceptionOrderSyncMQ.send(exceptionOrderSyncDTO);
                }
            }
        });
    }

    /**
     * 是否有关联货位
     * 
     * @return
     */
    private Boolean isConfigProductLocation(Integer warehouseId, List<Long> locationIdList) {
        Boolean isOpen = warehouseConfigService.isOpenLocationStock(warehouseId);
        // 开了货位库存，不需要校验关联货位
        if (isOpen) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(locationIdList)) {
            return true;
        }
        return false;
    }

    public void updateReplaceToSkuId(List<Long> skuIdList, Long replaceToSkuId) {
        productSkuPOMapper.updateReplaceToSkuId(skuIdList, replaceToSkuId);
    }

    public void deleteBySkuId(Long skuId) {
        productSkuPOMapper.deleteByPrimaryKey(skuId);
    }

    /**
     * 历史重复产品标记删除或还原
     *
     * @return
     */
    public void deleteOrRemoveRepeatProductSku(RepeatProductDeleteDTO deleteDTO) {
        LOGGER.info("历史重复产品标记删除或还原 入参：{}", JSON.toJSONString(deleteDTO));
        AssertUtils.notNull(deleteDTO, "参数不能为空");
        AssertUtils.notNull(deleteDTO.getIsDelete(), "是否删除不能为空");
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(deleteDTO.getOrgIdList())
            || CollectionUtils.isNotEmpty(deleteDTO.getProductSkuIdList()), "城市Id和产品skuId不能同时为空");

        ProductSkuQueryDTO queryDTO = new ProductSkuQueryDTO();
        queryDTO.setCityIdList(deleteDTO.getOrgIdList());
        queryDTO.setSkuIdList(deleteDTO.getProductSkuIdList());

        PageResult<ProductSkuPO> poPageResult = new PageResult<>();
        List<ProductSkuPO> productSkuPOS = new ArrayList<>();
        int pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            PageHelper.startPage(pageNum, 5000);
            poPageResult = productSkuPOMapper.pageListProductSkuInfo(queryDTO);
            if (poPageResult == null || CollectionUtils.isEmpty(poPageResult.getResult())) {
                return;
            }

            if (pageNum == 1) {
                pageCount = poPageResult.getPager().getTotalPage();
            }

            productSkuPOS.addAll(poPageResult.getResult());
        }
        if (CollectionUtils.isEmpty(productSkuPOS)) {
            return;
        }
        LOGGER.info("历史重复产品标记删除或还原 产品查询结果数量：{}", productSkuPOS.size());
        List<Long> updateSkuIdList = new ArrayList<>();
        if (Objects.equals(YesOrNoEnum.YES.getValue().byteValue(), deleteDTO.getIsDelete())) {
            // 重复skuid标记删除
            List<Long> notDeleteSkuIdList = productSkuPOS.stream().collect(Collectors.groupingBy(this::getGroupName))
                .values().stream().map(this::validSkuFirst).flatMap(Collection::stream).collect(Collectors.toList())
                .stream().filter(p -> p != null && p.getProductSku_Id() != null).map(ProductSkuPO::getProductSku_Id)
                .distinct().collect(Collectors.toList());

            List<Long> deleteSkuIdList = productSkuPOS.stream()
                .filter(p -> !notDeleteSkuIdList.contains(p.getProductSku_Id())
                    && !Objects.equals(p.getIsDelete(), YesOrNoEnum.YES.getValue().byteValue()))
                .map(ProductSkuPO::getProductSku_Id).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteSkuIdList)) {
                updateSkuIdList.addAll(deleteSkuIdList);
                LOGGER.info("历史重复产品标记删除或还原 skuid删除：{}", JSON.toJSONString(updateSkuIdList));
            }
        } else if (Objects.equals(YesOrNoEnum.NO.getValue().byteValue(), deleteDTO.getIsDelete())) {
            // 已标记删除skuid还原
            List<Long> notDeleteSkuIdList = productSkuPOS.stream()
                .filter(p -> Objects.equals(YesOrNoEnum.YES.getValue().byteValue(), p.getIsDelete()))
                .map(ProductSkuPO::getProductSku_Id).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notDeleteSkuIdList)) {
                updateSkuIdList.addAll(notDeleteSkuIdList);
                LOGGER.info("历史重复产品标记删除 skuid还原：{}", JSON.toJSONString(updateSkuIdList));
            }
        }

        if (CollectionUtils.isNotEmpty(updateSkuIdList)) {
            Lists.partition(updateSkuIdList, 500).forEach(list -> {
                asyncProductSkuUpdateBL.updateIsDeleteBatch(list, deleteDTO.getIsDelete());
            });
        }

        LOGGER.info("历史重复产品标记删除 skuid处理成功");
    }

    public void logicDelete(Collection<Long> skuIds, boolean isDelete) {
        YesOrNoEnum yesOrNo = isDelete ? YesOrNoEnum.YES : YesOrNoEnum.NO;
        asyncProductSkuUpdateBL.updateIsDeleteBatch(skuIds, yesOrNo.getValue().byteValue());
    }

    public void updateProductStateBatch(Collection<Long> skuIds, Integer productStatus) {
        AssertUtils.notNull(ProductSkuStateEnum.getEnmuName(productStatus), "状态有误: " + productStatus);
        asyncProductSkuUpdateBL.updateProductStateBatch(skuIds, productStatus);
    }

    /**
     * 按照 城市-规格-货主-二级货主 来分组 sku
     *
     * @param sku sku 数据
     */
    private String getGroupName(ProductSkuPO sku) {
        Integer cityId = sku.getCity_Id();
        Long specId = sku.getProductSpecification_Id();
        Long ownerId = sku.getCompany_Id();
        Long secOwnerId = sku.getSecOwnerId();
        return String.format("%s-%s-%s-%s", cityId, specId, ownerId, secOwnerId);
    }

    /**
     * 优先返回上架的一条 sku 数据, 如果没有则返回下架的一条数据, 如果都没有那就返回空 list
     *
     * @param source 原始数据
     */
    private List<ProductSkuPO> validSkuFirst(List<ProductSkuPO> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        // 按最后更新时间降序排序
        List<ProductSkuPO> sortList = source.stream()
            .sorted(Comparator.comparing(ProductSkuPO::getLastUpdateTime).reversed()).collect(Collectors.toList());
        // 未删除状态
        Optional<ProductSkuPO> noDeleteSkus = sortList.stream()
            .filter(item -> Objects.equals(YesOrNoEnum.NO.getValue().byteValue(), item.getIsDelete())).findFirst();
        // 上架状态
        Optional<ProductSkuPO> validSkus = sortList.stream()
            .filter(item -> ProductSkuStateEnum.上架.getType().equals(item.getProductState())).findFirst();
        // 只会返回一条数据
        return noDeleteSkus.map(Collections::singletonList)
            .orElse(validSkus.map(Collections::singletonList).orElse(Collections.singletonList(sortList.get(0))));
    }

}
