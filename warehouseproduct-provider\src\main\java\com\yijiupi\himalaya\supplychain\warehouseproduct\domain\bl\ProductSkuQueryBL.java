/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.xxl.job.core.util.DateUtil;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductionDateDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductionDateQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductionDateSpecQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.FileQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.ScmProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ScmProductSkuDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductSkuSearchDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductSkuStateEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuCreateDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuManageService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig.ProductSkuConfigHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSkuPOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuDeliveryPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.model.DataResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.search.SpellSearchResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.search.SpellSearchSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductExceptionReasonEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.RestDrinkCategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductStockRatioQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.QuerySkuDetailsParamDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ConfigUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.supplychain.serviceutils.constant.SaleModeConstant;
import com.yijiupi.supplychain.serviceutils.constant.company.CompanyCode;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2018年10月31日 下午5:45:37
 */
@Service
public class ProductSkuQueryBL {

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;
    @Autowired
    private ProductSkuPOConvertor productSkuPOConvertor;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private ProductLocationPOMapper productLocationPOMapper;

    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    @Autowired
    private ProductSkuServiceBL productSkuServiceBL;

    @Autowired
    private ProductLocationBL productLocationBL;

    @Autowired
    private ProductSkuLabelBL productSkuLabelBL;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private FileQueryService fileQueryService;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductSkuQueryService scmProductSkuQueryService;

    @Reference
    private com.yijiupi.himalaya.supplychain.file.service.IFileService iFileSCMService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference
    private IProductSkuManageService iProductSkuManageService;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IContentConfigurationService iContentConfigurationService;

    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;

    @Reference
    private IWarehouseInventoryCheckService iWarehouseInventoryCheckService;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Autowired
    private RelationPackageSkuBL relationPackageSkuBL;

    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private ProductSkuConfigHelper productSkuConfigHelper;

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuQueryBL.class);

    /**
     * list 分组大小
     */
    private static final int PART_SIZE = 500;

    // 产品图片查询方式
    private static final String PRODUCT_IMAGE_QUERY_TYPE = "ProductImageQueryType";

    /**
     * 分页查询产品列表
     */
    public PageList<ProductSkuDTO> pageList(ProductSkuQueryDTO so) {
        // 1、查询产品列表
        PageList<ProductSkuDTO> resultList = listProductSku(so);
        // 2、填充产品其他属性
        List<ProductSkuDTO> convert = resultList.getDataList();
        boolean isSetBarCode =
            !CollectionUtils.isEmpty(convert) && (so.getQueryBarcode() == null || so.getQueryBarcode());
        if (isSetBarCode) {
            // 设置瓶码箱码
            Set<Long> skuIdSet = convert.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toSet());
            Map<Long, ProductCodeDTO> codeMap = productSkuServiceBL.getPackageAndUnitCode(skuIdSet, so.getCityId());
            // 查询产品的配送标签
            ProductSkuLabelSO productSkuLabelSO = new ProductSkuLabelSO();
            productSkuLabelSO.setOrgId(so.getCityId());
            productSkuLabelSO.setWarehouseId(so.getWarehouseId());
            productSkuLabelSO.setProductSkuIds(new ArrayList<>(skuIdSet));
            Map<Long, String> deliveryMap = productSkuLabelBL.getProductSkuLabelByDeliveryMap(productSkuLabelSO);
            // 查询产品在本仓库是否可销售
            Map<Long, Boolean> enableSellMap = productSkuLabelBL.getProductSkuLabelByEnableSellMap(productSkuLabelSO);
            convert.forEach(p -> {
                // 设置瓶码和箱码
                setPackageAndUnitCode(codeMap, p);
                // 产品的配送标签
                if (deliveryMap != null) {
                    p.setDeliveryLabelId(deliveryMap.get(p.getProductSkuId()));
                }
                // 产品在本仓库是否可销售
                if (enableSellMap != null) {
                    if (Objects.equals(enableSellMap.get(p.getProductSkuId()), true)) {
                        p.setEnableSell(ConditionStateEnum.是.getType());
                    }
                }
            });
        }
        return resultList;
    }

    /**
     * 设置瓶码和箱码
     */
    private void setPackageAndUnitCode(Map<Long, ProductCodeDTO> codeMap, ProductSkuDTO p) {
        if (codeMap == null) {
            return;
        }
        ProductCodeDTO productCodeDTO = codeMap.get(p.getProductSkuId());
        if (productCodeDTO != null) {
            // 箱码
            if (!CollectionUtils.isEmpty(productCodeDTO.getPackageCode())) {
                p.setPackageCode(String.join("、", productCodeDTO.getPackageCode()));
            }
            // 瓶码
            if (!CollectionUtils.isEmpty(productCodeDTO.getUnitCode())) {
                p.setBoxCode(String.join("、", productCodeDTO.getUnitCode()));
            }
            // 最新的箱码
            if (!CollectionUtils.isEmpty(productCodeDTO.getPackageCodeList())) {
                Optional<CodeDTO> optional = productCodeDTO.getPackageCodeList().stream()
                    .sorted(Comparator.comparing(CodeDTO::getId, Comparator.nullsFirst(Long::compareTo)).reversed())
                    .findFirst();
                if (optional.isPresent()) {
                    p.setNewPackageCode(optional.get().getCode());
                }
            }
            // 最新的瓶码
            if (!CollectionUtils.isEmpty(productCodeDTO.getUnitCodeList())) {
                Optional<CodeDTO> optional = productCodeDTO.getUnitCodeList().stream()
                    .sorted(Comparator.comparing(CodeDTO::getId, Comparator.nullsFirst(Long::compareTo)).reversed())
                    .findFirst();
                if (optional.isPresent()) {
                    p.setNewBoxCode(optional.get().getCode());
                }
            }
        }
    }

    /**
     * 查询产品列表
     */
    public PageList<ProductSkuDTO> listProductSku(ProductSkuQueryDTO so) {
        AssertUtils.notNull(so.getCityId(), "城市id不能为空");
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        // 按条码查询
        if (StringUtils.isNotEmpty(so.getProductCode())) {
            // 通过瓶码或者箱码查询sku列表
            List<Long> skuIdList = productSkuServiceBL.getProductSkuIdByCode(so.getProductCode(), so.getCityId());
            if (CollectionUtils.isEmpty(skuIdList)) {
                skuIdList.add(0L);
            }
            so.setSkuIdList(skuIdList);
        }
        if (so.getIsDelete() == null) {
            so.setIsDelete(ConditionStateEnum.否.getType());
        }
        if (so.getNewProductFlag() != null && so.getNewProductFlag()) {
            so.setStartTime(DateUtil.addMonths(new Date(), -1));
        }
        return productSkuPOMapper.listProductSku(so, so.getCurrentPage(), so.getPageSize())
            .toPageList(productSkuPOConvertor::convert);
    }

    /**
     * 查询产品列表(完整的sku仓库配置)
     */
    public PageList<ProductSkuDTO> listProductSkuFull(ProductSkuSO productSkuSO) {
        AssertUtils.notNull(productSkuSO, "参数不能为空");
        AssertUtils.notNull(productSkuSO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productSkuSO.getWarehouseId(), "仓库id不能为空");
        // 按条码查询
        if (StringUtils.isNotEmpty(productSkuSO.getProductCode())) {
            // 通过瓶码或者箱码查询sku列表
            List<Long> skuIdList =
                productSkuServiceBL.getProductSkuIdByCode(productSkuSO.getProductCode(), productSkuSO.getCityId());
            if (!CollectionUtils.isEmpty(skuIdList)) {
                productSkuSO.setSkuIdList(skuIdList);
            }
        }
        PageResult<ProductSkuPO> pageResult = productSkuPOMapper.listProductSkuFull(productSkuSO);
        List<ProductSkuDTO> convert = productSkuPOConvertor.convert(pageResult.toPageList().getDataList());

        PageList<ProductSkuDTO> resultList = new PageList<>();
        resultList.setDataList(convert);
        resultList.setPager(pageResult.getPager());
        return resultList;
    }

    /**
     * 根据skuId查询产品
     *
     * @return
     */
    public Map<Long, ProductSkuDTO> findBySkuMap(List<Long> productSkuIds) {
        List<ProductSkuDTO> skuDTOS = findBySku(productSkuIds);
        if (CollectionUtils.isEmpty(skuDTOS)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, ProductSkuDTO> resultMap = new HashMap<>(16);
        skuDTOS.forEach(p -> {
            resultMap.put(p.getProductSkuId(), p);
        });
        return resultMap;
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置）
     */
    public List<ProductSkuDTO> findBySkuFull(Integer warehouseId, Collection<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        List<ProductSkuPO> po = productSkuPOMapper.findBySkuFull(warehouseId, productSkuIds);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }
        List<ProductSkuDTO> resultList = productSkuPOConvertor.convert(po);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        // 获取瓶码和箱码
        Set<Long> skuIdSet = resultList.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> codeMap =
            productSkuServiceBL.getPackageAndUnitCode(skuIdSet, resultList.get(0).getCityId());
        resultList.forEach(sku -> {
            // 设置瓶码和箱码
            setPackageAndUnitCode(codeMap, sku);
        });
        return resultList;
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置） 没有条码/箱码
     */
    public List<ProductSkuDTO> findSkuInfoWithConfig(Integer warehouseId, List<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        // 自身去重
        productSkuIds = productSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 结果集合
        List<ProductSkuPO> poList = new ArrayList<>(productSkuIds.size());
        // 500条分组查询，防止大SQL
        List<List<Long>> partitions = Lists.partition(productSkuIds, PART_SIZE);
        for (List<Long> part : partitions) {
            List<ProductSkuPO> pos = productSkuPOMapper.findBySkuFull(warehouseId, part);
            if (CollectionUtils.isEmpty(pos)) {
                continue;
            }
            poList.addAll(pos);
        }
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<ProductSkuDTO> resultList = productSkuPOConvertor.convert(poList);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        return resultList;
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置） 没有条码/箱码
     */
    public List<ProductSkuDTO> findSkuInfoWithConfigNew(Integer warehouseId, List<Long> productSkuIds, Integer warehouseAllocationType) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        // 自身去重
        productSkuIds = productSkuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 结果集合
        List<ProductSkuPO> poList = new ArrayList<>(productSkuIds.size());
        // 500条分组查询，防止大SQL
        List<List<Long>> partitions = Lists.partition(productSkuIds, PART_SIZE);
        for (List<Long> part : partitions) {
            List<ProductSkuPO> pos = productSkuPOMapper.findBySkuFullNew(warehouseId, part, warehouseAllocationType);
            if (CollectionUtils.isEmpty(pos)) {
                continue;
            }
            poList.addAll(pos);
        }
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<ProductSkuDTO> resultList = productSkuPOConvertor.convert(poList);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        return resultList;
    }

    /**
     * 根据skuId查找产品
     */
    public List<ProductSkuDTO> findBySku(List<Long> productSkuIds) {
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        List<ProductSkuPO> po = productSkuPOMapper.findBySku(productSkuIds);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }

        return productSkuPOConvertor.convert(po);
    }

    /**
     * 根据规格查询产品
     */
    public List<ProductSkuDTO> findBySpec(ProductSkuBySpecificationSO productSkuBySpecSO) {
        List<ProductSkuPO> po = productSkuPOMapper.findBySpec(productSkuBySpecSO);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }
        return productSkuPOConvertor.convert(po);
    }

    /**
     * 根据大区ID+规格Id+source查找sku
     *
     * @return
     */
    public ProductSkuDTO getProductSkuBySpecIdAndSource(Integer cityId, Long productSpecId, Integer productSource) {
        ProductSkuPO productSkuPO =
            productSkuPOMapper.getProductSkuBySpecIdAndSource(cityId, productSpecId, productSource);
        if (productSkuPO == null) {
            return null;
        }
        return productSkuPOConvertor.convert(productSkuPO);
    }

    /**
     * 查询加工产品列表
     */
    public PageList<ProcessProductSkuDTO>
        pageListProcessProductSku(ProcessProductSkuQueryDTO processProductSkuQueryDTO) {
        PageList<ProcessProductSkuDTO> pageList = new PageList<>();

        // 分页查询可加工产品
        PageResult<ProcessProductSkuDTO> pageResult = productSkuPOMapper.pageListProcessProductSku(
            processProductSkuQueryDTO, processProductSkuQueryDTO.getPageNum(), processProductSkuQueryDTO.getPageSize());
        List<ProcessProductSkuDTO> result = pageResult.getResult();

        if (CollectionUtils.isEmpty(result)) {
            return pageList;
        }

        // 查询产品信息
        List<Long> productSkuIds =
            result.stream().map(ProcessProductSkuDTO::getProductSkuId).collect(Collectors.toList());
        Map<Long, List<ProductSkuPO>> skuMap = productSkuPOMapper.findBySku(productSkuIds).stream()
            .collect(Collectors.groupingBy(ProductSkuPO::getProductSku_Id));

        // 查询待出库数量
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIds = new ArrayList<>();
        result.forEach(sku -> {
            List<ProductSkuPO> productSkuPOS = skuMap.get(sku.getProductSkuId());
            if (!CollectionUtils.isEmpty(productSkuPOS)) {
                ProductSkuPO productSkuPO = productSkuPOS.get(0);
                sku.setOrgId(productSkuPO.getCity_Id());
                sku.setOwnerId(productSkuPO.getCompany_Id());
                sku.setProductInfoId(productSkuPO.getProductInfoId());
                sku.setProductSpecificationId(productSkuPO.getProductSpecification_Id());
                sku.setOwnerName(productSkuPO.getOwnerName());
                sku.setSource(productSkuPO.getSource());
                sku.setStatisticsClassName(productSkuPO.getStatisticsClass());
                sku.setProductName(productSkuPO.getName());
                sku.setSpecificationName(productSkuPO.getSpecificationName());
                sku.setProductState((byte)productSkuPO.getProductState().intValue());
                sku.setPackageName(productSkuPO.getPackageName());
                sku.setPackageQuantity(productSkuPO.getPackageQuantity());
                sku.setConvertProductInfoSpecId(productSkuPO.getConvertProductInfoSpecId());
                sku.setConvertSpecQuantity(productSkuPO.getConvertSpecQuantity());
                sku.setUnitName(productSkuPO.getUnitName());
                sku.setProductBrand(productSkuPO.getProductBrand());

                ProductSpecAndOwnerIdDTO productSpecAndOwnerIdDTO = new ProductSpecAndOwnerIdDTO();
                productSpecAndOwnerIdDTO.setOwnerId(sku.getOwnerId());
                productSpecAndOwnerIdDTO.setProductSpecId(sku.getProductSpecificationId());

                specAndOwnerIds.add(productSpecAndOwnerIdDTO);
            }
        });
        Map<String, BigDecimal> waitDeliveryCountMap = iWarehouseInventoryQueryService.getWaitDeliveryCountMap(
            processProductSkuQueryDTO.getOrgId(), processProductSkuQueryDTO.getWarehouseId(), specAndOwnerIds);
        LOGGER.info("加工产品待出库Map:{}", JSON.toJSONString(waitDeliveryCountMap));
        result.forEach(processProductSku -> {
            String key =
                String.format("%s-%s", processProductSku.getProductSpecificationId(), processProductSku.getOwnerId());
            processProductSku.setWaitTotalCountMinUnit(waitDeliveryCountMap.get(key));
        });

        pageList.setDataList(result);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    /**
     * 通过infoId、规格id、货主id查询对应的可加工产品
     */
    public Map<Long, List<ProcessProductSkuDTO>>
        listProcessProductSkuByInfo(ProcessProductInfoQueryDTO processProductInfoQueryDTO) {
        Map<Long, List<ProcessProductSkuDTO>> processProductSkuMap = new HashMap<>(16);
        List<ProcessProductSkuDTO> processProductSkuDTOS =
            productSkuPOMapper.listProcessProductSkuByInfo(processProductInfoQueryDTO);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(processProductSkuDTOS)) {
            List<ProcessProductSkuDTO> signBatchProcessProductSkus = processProductSkuDTOS.stream()
                .filter(StreamUtils.distinctByKey(ProcessProductSkuDTO::getProductStoreBatchId))
                .collect(Collectors.toList());

            processProductInfoQueryDTO.getProductSkuQueryDTOS().forEach(querySku -> {
                List<ProcessProductSkuDTO> lstProcessProductSkuList = signBatchProcessProductSkus.stream().filter(
                    queryStore -> queryStore.getProductSpecificationId().equals(querySku.getConvertProductInfoSpecId())
                        || queryStore.getConvertProductInfoSpecId().equals(querySku.getProductSpecificationId()))
                    .collect(Collectors.toList());

                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(lstProcessProductSkuList)) {
                    lstProcessProductSkuList = lstProcessProductSkuList.stream()
                        .filter(StreamUtils.distinctByKey(ProcessProductSkuDTO::getProductStoreBatchId))
                        .filter(sku -> !sku.getProductSpecificationId().equals(querySku.getProductSpecificationId()))
                        .collect(Collectors.toList());
                }
                processProductSkuMap.put(querySku.getProductSpecificationId(), lstProcessProductSkuList);
            });

        }
        return processProductSkuMap;
    }

    /**
     * 通过infoId查询对应的可加工产品
     */
    public Map<Long, List<ProcessProductSkuDTO>>
        listProcessProductSkuByInfoId(ProcessProductInfoQueryDTO processProductInfoQueryDTO) {
        Map<Long, List<ProcessProductSkuDTO>> result = new HashMap<>(16);
        List<ProcessProductSkuDTO> processProductSkuDTOS =
            productSkuPOMapper.listProcessProductSkuByInfoId(processProductInfoQueryDTO);
        LOGGER.info("infoId查询对应的可加工产品:{}", JSON.toJSONString(processProductSkuDTOS));
        if (CollectionUtils.isEmpty(processProductSkuDTOS)) {
            return result;
        }
        Map<Long, List<ProcessProductSkuDTO>> processProductSkuMap =
            processProductSkuDTOS.stream().collect(Collectors.groupingBy(ProcessProductSkuDTO::getProductSkuId));
        for (Map.Entry<Long, List<ProcessProductSkuDTO>> entry : processProductSkuMap.entrySet()) {
            Map<Long, List<ProcessProductSkuDTO>> locationMap =
                entry.getValue().stream().filter(it -> it.getLocationId() != null)
                    .collect(Collectors.groupingBy(ProcessProductSkuDTO::getLocationId));
            List<ProcessProductSkuDTO> noLocationList =
                entry.getValue().stream().filter(it -> it.getLocationId() == null).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(noLocationList)) {
                List<ProcessProductSkuDTO> skuDTOS = result.get(entry.getKey());
                if (CollectionUtils.isEmpty(skuDTOS)) {
                    skuDTOS = new ArrayList<>();
                    skuDTOS.addAll(noLocationList);
                    result.put(entry.getKey(), skuDTOS);
                } else {
                    skuDTOS.addAll(noLocationList);
                    result.put(entry.getKey(), skuDTOS);
                }
            }

            if (CollectionUtils.isEmpty(locationMap)) {
                continue;
            }
            for (List<ProcessProductSkuDTO> value : locationMap.values()) {
                ProcessProductSkuDTO processProductSkuDTO = new ProcessProductSkuDTO();
                BeanUtils.copyProperties(value.get(0), processProductSkuDTO);
                processProductSkuDTO.setTotalCountMinUnit(BigDecimal.ZERO);
                processProductSkuDTO.setWaitTotalCountMinUnit(BigDecimal.ZERO);
                value.forEach(it -> {
                    processProductSkuDTO.setTotalCountMinUnit(processProductSkuDTO.getTotalCountMinUnit()
                        .add(it.getTotalCountMinUnit() == null ? BigDecimal.ZERO : it.getTotalCountMinUnit()));
                    processProductSkuDTO.setWaitTotalCountMinUnit(processProductSkuDTO.getWaitTotalCountMinUnit()
                        .add(it.getWaitTotalCountMinUnit() == null ? BigDecimal.ZERO : it.getWaitTotalCountMinUnit()));
                });
                List<ProcessProductSkuDTO> productSkuDTOS = result.get(entry.getKey());
                if (CollectionUtils.isEmpty(productSkuDTOS)) {
                    productSkuDTOS = new ArrayList<>();
                    productSkuDTOS.add(processProductSkuDTO);
                    result.put(entry.getKey(), productSkuDTOS);
                } else {
                    productSkuDTOS.add(processProductSkuDTO);
                    result.put(entry.getKey(), productSkuDTOS);
                }
            }
        }
        return processProductSkuMap;
    }

    /**
     * 查找货位上产品
     *
     * @return
     */
    public List<ProductSkuDTO> findLocationProducts(LocationProductQuery productQuery) {
        LOGGER.info("查找货位产品信息参数：{}", JSON.toJSONString(productQuery));
        AssertUtils.notNull(productQuery, "查询参数不能为空！");
        AssertUtils.notNull(productQuery.getCityId(), "城市ID不能为空！");
        AssertUtils.notNull(productQuery.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notNull(productQuery.getLocationName(), "货位名称不能为空！");
        /*
         * 1、开启货位库存，查货位上有库存的产品
         * 2、没开启货位库存，查货位关联产品列表
         *
         */
        List<ProductSkuPO> productSkuPOList = findLocationProductSkuList(productQuery);
        if (CollectionUtils.isEmpty(productSkuPOList)) {
            return Collections.EMPTY_LIST;
        }

        List<ProductSkuDTO> productSkuDTOList = productSkuPOConvertor.convert(productSkuPOList);

        // 根据sku查询对应产品库存数量
        Map<Long, List<ProductSkuDTO>> skuMap =
            productSkuDTOList.stream().filter(d -> d != null && d.getProductSkuId() != null)
                .collect(Collectors.groupingBy(d -> d.getProductSkuId()));
        Map<Long, ProductStoreDTO> productStoreMap = iWarehouseInventoryQueryService
            .listProductInventoryBySkuIds(Lists.newArrayList(skuMap.keySet()), productQuery.getWarehouseId());
        if (productStoreMap != null && !productStoreMap.isEmpty()) {
            skuMap.forEach((key, value) -> {
                ProductStoreDTO productStoreDTO = productStoreMap.get(key);
                if (productStoreDTO != null && value != null && !value.isEmpty()) {
                    value.forEach(d -> d.setCurrentInventory(productStoreDTO.getUnitTotolCount()));
                }
            });
        }
        // sku 当前库存为 null 给默认值 0
        productSkuDTOList.stream().filter(d -> d.getCurrentInventory() == null)
            .forEach(d -> d.setCurrentInventory(BigDecimal.ZERO));
        return productSkuDTOList;
    }

    public List<ProductSkuPO> findLocationProductSkuList(LocationProductQuery productQuery) {
        /*
         * 1、开启货位库存，查货位上有库存的产品
         * 2、没开启货位库存，查货位关联产品列表
         *
         */
        Boolean openLocationStock = warehouseConfigService.isOpenLocationStock(productQuery.getWarehouseId());
        List<ProductSkuPO> productSkuPOList;
        if (openLocationStock) {
            productSkuPOList = productSkuPOMapper.findLocationPruductsByBatchInfo(productQuery);
        } else {
            productSkuPOList = productLocationPOMapper.findLocationPruductsByRelation(productQuery);
        }
        return productSkuPOList;
    }

    public List<ProductSkuDTO> findByProductSpecificationIds(List<Long> productSpecificationIds, Long ownerId,
        Integer orgId) {
        List<ProductSkuPO> po =
            productSkuPOMapper.findByProductSpecificationIds(productSpecificationIds, ownerId, orgId);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }
        return productSkuPOConvertor.convert(po);
    }

    public ProductCheckByRatioDTO stockRatioProductInfoIsExist(ProductStockRatioQuery ratioQuery) {
        LOGGER.info("货主库存分配比例查询参数：{}", JSON.toJSONString(ratioQuery));
        AssertUtils.notNull(ratioQuery, "库存分配比例查询参数不能为空");
        AssertUtils.notNull(ratioQuery.getCity(), "库存分配比例查询城市不能为空");
        AssertUtils.notEmpty(ratioQuery.getItemQueryList(), "库存分配比例货主信息不能为空");

        List<Long> skuIdList = ratioQuery.getItemQueryList().stream().filter(e -> e != null && e.getSkuId() != null)
            .map(e -> e.getSkuId()).distinct().collect(Collectors.toList());
        List<ProductSkuPO> productInfoList = productSkuPOMapper.getProductInfoBySkuId(skuIdList);

        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new BusinessValidateException("库存分配产品信息校验,产品SKU信息都不存在！");
        }
        // 根据sku分组
        Map<Long, ProductSkuPO> sourceSkuMap = productInfoList.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> e.getProductSku_Id(), Function.identity()));
        // 非酒批
        List<Long> productSpecificationIdList = Lists.newArrayList();
        // 酒批
        List<Long> jiuPiSpecIdList = Lists.newArrayList();
        // owner集合
        List<Long> ownerIdList = Lists.newArrayList();
        // 查询 key 集合
        Set<String> queryKey = new HashSet<>();
        // 结果 key 集合
        Set<String> existKey = new HashSet<>();

        ratioQuery.getItemQueryList().stream().filter(e -> e != null).forEach(e -> {
            ProductSkuPO skuPO = sourceSkuMap.get(e.getSkuId());
            Long specId = null;
            if (skuPO != null) {
                // 获取规格ID
                specId = skuPO.getProductSpecification_Id();
                productSpecificationIdList.add(specId);
                if (e.getHasJiup()) {
                    jiuPiSpecIdList.add(specId);
                }
            }
            if (!CollectionUtils.isEmpty(e.getOwnerIdList())) {
                List<Long> ownerList = e.getOwnerIdList();
                ownerIdList.addAll(ownerList);
                for (int i = 0; i < ownerList.size(); i++) {
                    Long oId = ownerList.get(i);
                    if (oId == null) {
                        continue;
                    }
                    queryKey.add(String.format("%s-%s-%s-%s", e.getSkuId(), ratioQuery.getCity(), specId, oId));
                }
            }
            if (e.getHasJiup() != null && e.getHasJiup()) {
                queryKey.add(String.format("%s-%s-%s-%s", e.getSkuId(), ratioQuery.getCity(), specId, ""));
            }
        });

        List<ProductSkuPO> otherProductSkuList = new ArrayList<>();
        List<ProductSkuPO> jiuPiProductSkuList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ownerIdList) && !CollectionUtils.isEmpty(productSpecificationIdList)) {
            otherProductSkuList = productSkuPOMapper.findProductInfoBySpecificationIdAndOwners(ratioQuery.getCity(),
                productSpecificationIdList, ownerIdList);
        }
        if (!CollectionUtils.isEmpty(jiuPiSpecIdList)) {
            jiuPiProductSkuList =
                productSkuPOMapper.getProductInfoBySpecificationId(ratioQuery.getCity(), jiuPiSpecIdList, null, 0);
        }

        if (!CollectionUtils.isEmpty(otherProductSkuList)) {
            otherProductSkuList.stream().filter(e -> e != null).forEach(e -> existKey.add(String.format("%s-%s-%s-%s",
                e.getProductSku_Id(), e.getCity_Id(), e.getProductSpecification_Id(), e.getCompany_Id())));
        }
        if (!CollectionUtils.isEmpty(jiuPiProductSkuList)) {
            jiuPiProductSkuList.stream().filter(e -> e != null).forEach(e -> existKey.add(String.format("%s-%s-%s-%s",
                e.getProductSku_Id(), e.getCity_Id(), e.getProductSpecification_Id(), "")));
        }
        LOGGER.info("queryKey 集合 ：{}", JSON.toJSONString(queryKey));
        LOGGER.info("existKey 集合 ：{}", JSON.toJSONString(existKey));
        // 返回结果
        ProductCheckByRatioDTO ratioDTO = new ProductCheckByRatioDTO();
        // 去掉最前面的sku,用于后面比较
        Set<String> existOwnerKey = existKey.stream().filter(e -> e != null).map(e -> e.substring(e.indexOf("-") + 1))
            .collect(Collectors.toSet());
        queryKey.removeIf(e -> e != null && existOwnerKey.contains(e.substring(e.indexOf("-") + 1)));
        if (CollectionUtils.isEmpty(queryKey)) {
            ratioDTO.setAllExist(true);
        } else {
            List<ProductCheckByRatioItemDTO> ratioItemDTOS = Lists.newArrayList();
            queryKey.stream().filter(e -> e != null).forEach(e -> {
                String[] infoStr = e.split("-");
                if (infoStr.length == 4) {
                    ProductCheckByRatioItemDTO itemDTO = new ProductCheckByRatioItemDTO();
                    itemDTO.setSkuId(Long.valueOf(infoStr[0]));
                    itemDTO.setCityId(Integer.valueOf(infoStr[1]));
                    itemDTO.setOwnerId(StringUtils.isNumeric(infoStr[3]) ? Long.valueOf(infoStr[3]) : null);
                    ratioItemDTOS.add(itemDTO);
                } else if (infoStr.length == 3) {
                    ProductCheckByRatioItemDTO itemDTO = new ProductCheckByRatioItemDTO();
                    itemDTO.setSkuId(Long.valueOf(infoStr[0]));
                    itemDTO.setCityId(Integer.valueOf(infoStr[1]));
                    ratioItemDTOS.add(itemDTO);
                }
            });
            ratioDTO.setAllExist(false);
            ratioDTO.setNonExistentProduct(ratioItemDTOS);
        }
        return ratioDTO;
    }

    /**
     * 获取产品的异常原因
     *
     * @return 正常(0), 未设置产品属性(1), 未关联货位(2)
     */
    public Integer getProductExceptionReason(Integer cityId, Integer warehouseId, List<Long> productSkuIds,
        Boolean isNP) {
        AssertUtils.notNull(cityId, "城市Id不能为空");
        AssertUtils.notNull(warehouseId, "仓库Id不能为空");
        AssertUtils.notEmpty(productSkuIds, "产品skuId不能为空");

        // 1、查询产品属性
        List<ProductSkuPO> productSkuPOS = productSkuPOMapper.findBySkuFull(warehouseId, productSkuIds);
        if (CollectionUtils.isEmpty(productSkuPOS)) {
            throw new BusinessException("产品信息不存在！");
        }
        // 当是否独家产品为空 || 是否窜货为空 || 非独家且可窜货且未关联产品，表示未设置产品属性
        if (productSkuPOS.stream()
            .anyMatch(p -> !isConfigProductAttr(p.getUnique(), p.getFleeGoods(), p.getProductRelevantState()))) {
            return ProductExceptionReasonEnum.未设置产品属性.getType();
        }

        // 2、查询产品的关联货位（未开启货位库存时，且不是内配单原单时，才需要校验关联货位）
        if (!isOpenStock(warehouseId) && !Objects.equals(isNP, true)) {
            ProductLocationListSO productLocationListSO = new ProductLocationListSO();
            productLocationListSO.setCityId(cityId);
            productLocationListSO.setWarehouseId(warehouseId);
            productLocationListSO.setProductSkuIdList(productSkuIds);
            PageList<ProductLocationListDTO> pageList = productLocationBL.listProductLocation(productLocationListSO);
            if (pageList != null && !CollectionUtils.isEmpty(pageList.getDataList())) {
                for (Long skuId : productSkuIds) {
                    List<ProductLocationListDTO> filterList = pageList.getDataList().stream()
                        .filter(p -> Objects.equals(p.getProductSkuId(), skuId)).collect(Collectors.toList());
                    // 当产品的关联货位为空时，表示未关联货位
                    if (CollectionUtils.isEmpty(filterList)
                        || filterList.stream().allMatch(p -> p.getProductLocationId() == null)) {
                        return ProductExceptionReasonEnum.未关联货位.getType();
                    }
                }
            }
        }
        return ProductExceptionReasonEnum.正常.getType();
    }

    /**
     * 判断是否设置产品属性 true：已设置 false：未设置
     *
     * @return
     */
    public Boolean isConfigProductAttr(Byte unique, Byte fleeGoods, Byte productRelevantState) {
        // 当是否独家产品为空 || 非独家时是否可窜货为空 || 非独家且可窜货且未关联产品，表示未设置产品属性
        boolean result = unique == null || (Objects.equals(unique, ConditionStateEnum.否.getType()) && fleeGoods == null)
            || (Objects.equals(unique, ConditionStateEnum.否.getType())
                && Objects.equals(fleeGoods, ConditionStateEnum.是.getType())
                && !Objects.equals(productRelevantState, ConditionStateEnum.是.getType()));
        if (result) {
            return false;
        }
        return true;
    }

    /**
     * 获取存在异常原因的产品skuId
     */
    public List<Long> getExceptionProductSkuIds(ExceptionProductSkuQuery exceptionProductSkuQuery) {
        AssertUtils.notNull(exceptionProductSkuQuery, "参数不能为空");
        AssertUtils.notNull(exceptionProductSkuQuery.getCityId(), "城市Id不能为空");
        AssertUtils.notNull(exceptionProductSkuQuery.getWarehouseId(), "仓库Id不能为空");
        AssertUtils.notEmpty(exceptionProductSkuQuery.getProductSkuIds(), "产品skuId不能为空");

        List<Long> exceptionProductSkuIds = new ArrayList<>();
        List<Long> skuIdsByAttr = new ArrayList<>();
        List<Long> skuIdsByLocation = new ArrayList<>();

        // 查询产品属性
        List<ProductSkuPO> productSkuPOS = productSkuPOMapper.findBySkuFull(exceptionProductSkuQuery.getWarehouseId(),
            exceptionProductSkuQuery.getProductSkuIds());

        // 查询产品的关联货位
        ProductLocationListSO productLocationListSO = new ProductLocationListSO();
        productLocationListSO.setCityId(exceptionProductSkuQuery.getCityId());
        productLocationListSO.setWarehouseId(exceptionProductSkuQuery.getWarehouseId());
        productLocationListSO.setProductSkuIdList(exceptionProductSkuQuery.getProductSkuIds());
        PageList<ProductLocationListDTO> pageList = productLocationBL.listProductLocation(productLocationListSO);

        if (!CollectionUtils.isEmpty(productSkuPOS) && pageList != null
            && !CollectionUtils.isEmpty(pageList.getDataList())) {
            productSkuPOS.forEach(productSkuPO -> {
                // 1、是否设置产品属性
                if (!isConfigProductAttr(productSkuPO.getUnique(), productSkuPO.getFleeGoods(),
                    productSkuPO.getProductRelevantState())) {
                    skuIdsByAttr.add(productSkuPO.getProductSku_Id());
                    return;
                }

                // 2、是否关联货位
                List<ProductLocationListDTO> filterLocationList = pageList.getDataList().stream()
                    .filter(p -> Objects.equals(p.getProductSkuId(), productSkuPO.getProductSku_Id()))
                    .collect(Collectors.toList());
                // 当产品的关联货位为空时，表示未关联货位
                if (CollectionUtils.isEmpty(filterLocationList)
                    || filterLocationList.stream().allMatch(p -> p.getProductLocationId() == null)) {
                    skuIdsByLocation.add(productSkuPO.getProductSku_Id());
                    return;
                }
            });
        }
        if (!CollectionUtils.isEmpty(skuIdsByAttr)) {
            exceptionProductSkuIds.addAll(skuIdsByAttr);
            LOGGER.info("未设置产品属性的skuId：{}", JSON.toJSONString(skuIdsByAttr));
        }
        // 未开启货位库存时，才需要校验关联货位
        if (!isOpenStock(exceptionProductSkuQuery.getWarehouseId())) {
            if (!CollectionUtils.isEmpty(skuIdsByLocation)) {
                exceptionProductSkuIds.addAll(skuIdsByLocation);
                LOGGER.info("未关联货位的skuId：{}", JSON.toJSONString(skuIdsByLocation));
            }
        }
        return exceptionProductSkuIds;
    }

    /**
     * 是否开启货位库存 true：3.0开启了货位库存 false：2.0、2.5、或者3.0没有开启货位库存
     *
     * @param warehouseId
     * @return
     */
    private Boolean isOpenStock(Integer warehouseId) {
        return warehouseConfigService.isOpenLocationStock(warehouseId);
    }

    /**
     * 根据skuId查询产品（规格id、货主id、source）
     *
     * @return
     */
    public Map<Long, ProductSkuDTO> getProductSkuMapBySkuIds(List<Long> skuIds) {
        AssertUtils.notEmpty(skuIds, "产品skuId不能为空");

        List<ProductSkuPO> productSkuPOList = productSkuPOMapper.listProductSkuByIds(skuIds);
        if (CollectionUtils.isEmpty(productSkuPOList)) {
            return null;
        }
        List<ProductSkuDTO> productSkuDTOList = productSkuPOConvertor.convert(productSkuPOList);
        if (CollectionUtils.isEmpty(productSkuDTOList)) {
            return null;
        }
        Map<Long, ProductSkuDTO> skuMap = new HashMap<>(16);
        productSkuDTOList.forEach(p -> {
            skuMap.put(p.getProductSkuId(), p);
        });
        return skuMap;
    }

    /**
     * 根据产品规格ID + ownerId + source查找sku (适用于产品关联查询)
     * <p>
     * 说明：易款连锁默认去掉 ownerId, 相同城市：规格ID + source 确定唯一 此方法主要用于关联产品查询
     * <p>
     * key -》 规格ID-ownerId-source value -》ProductSkuDTO
     */
    public Map<String, ProductSkuDTO> findProductSkuByProductRelation(Integer cityId,
        List<ProductSkuBySpecSO> specSOList) {
        AssertUtils.notEmpty(specSOList, "规格信息不能为空");

        List<ProductSkuPO> skuPOList = productSkuPOMapper.findProductSkuByProductRelation(cityId, specSOList);
        if (CollectionUtils.isEmpty(skuPOList)) {
            return null;
        }
        List<ProductSkuDTO> skuDTOList = productSkuPOConvertor.convert(skuPOList);
        return skuDTOList.stream().filter(e -> e != null).collect(Collectors
            .toMap(e -> e.genProductRelationIdentityCode(), Function.identity(), (v1, v2) -> v1 == null ? v2 : v1));
    }

    /**
     * 根据skuId获取产品图片url
     *
     * @return
     */
    public Map<Long, ProductImageDTO> getProductImageUrl(List<Long> skuIds) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                return Collections.EMPTY_MAP;
            }
            // 1、根据skuID获取图片ID
            List<ProductSkuDTO> skuDTOList = findBySku(skuIds);
            if (CollectionUtils.isEmpty(skuDTOList)) {
                return Collections.EMPTY_MAP;
            }

            // 2、根据内容配置查询产品图片查询方式
            boolean queryType = false;
            String contentValue = iContentConfigurationService.getContentValue(PRODUCT_IMAGE_QUERY_TYPE, null, "");
            if (Objects.equals(contentValue, "0")) {
                queryType = true;
            }

            Map<Long, ProductImageDTO> resultMap = new HashMap<>(16);
            // （1）查询供应链
            if (queryType) {
                skuIds.forEach(skuId -> {
                    Optional<ProductSkuDTO> optional =
                        skuDTOList.stream().filter(p -> Objects.equals(p.getProductSkuId(), skuId)).findFirst();
                    if (optional.isPresent()) {
                        if (optional.get().getProductInfoId() != null) {
                            List<String> imgUrlList =
                                iFileSCMService.listImgUrl(optional.get().getProductInfoId().toString());
                            if (!CollectionUtils.isEmpty(imgUrlList)) {
                                ProductImageDTO productImageDTO = new ProductImageDTO();
                                productImageDTO.setDefaultImageFile(imgUrlList.get(0));
                                productImageDTO.setImageFiles(imgUrlList);
                                resultMap.put(skuId, productImageDTO);
                            }
                        }
                    }
                });

                // （2）查询交易
            } else {
                // 根据图片ID查询默认图片url
                List<Integer> fileIds =
                    skuDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getDefaultImageFileId()))
                        .map(p -> Integer.valueOf(p.getDefaultImageFileId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(fileIds)) {
                    return Collections.EMPTY_MAP;
                }
                // 分50个一组获取默认图片
                Map<Integer, String> defaultFileMap = new HashMap<>(16);
                List<List<Integer>> lists = splitList(fileIds, 50);
                for (List<Integer> list : lists) {
                    Map<Integer, String> fileMap = fileQueryService.queryUrlByIds(new HashSet<>(list));
                    if (fileMap != null && fileMap.size() > 0) {
                        defaultFileMap.putAll(fileMap);
                    }
                }
                // 封装对象
                skuIds.forEach(skuId -> {
                    Optional<ProductSkuDTO> optional =
                        skuDTOList.stream().filter(p -> Objects.equals(p.getProductSkuId(), skuId)).findFirst();
                    if (optional.isPresent()) {
                        ProductImageDTO productImageDTO = new ProductImageDTO();
                        // 默认图片
                        if (StringUtils.isNotEmpty(optional.get().getDefaultImageFileId())) {
                            String fileURL =
                                defaultFileMap.get(Integer.valueOf(optional.get().getDefaultImageFileId()));
                            if (StringUtils.isNotEmpty(fileURL)) {
                                productImageDTO.setDefaultImageFile(fileURL);
                            }
                        }
                        resultMap.put(skuId, productImageDTO);
                    }
                });
            }
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("根据skuId获取产品图片url异常", e);
        }
        return Collections.EMPTY_MAP;
    }

    /**
     * 拆分list
     *
     * @return
     */
    private <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 根据订单产品查询实际发货产品sku （key：订单产品skuId，value：实际发货sku） 这个方法有两个含义： 1、生成的sku是 cityId和warehouseId的sku；
     * 这里查询的时候主要查询的是cityId的sku是否存在，left了 deliveryCityId 入参的skuId是 deliveryCity的skuId 2、查询的时候是另外一个含义
     * 
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, ProductSkuDTO> getProductSkuDeliveryMap(ProductSkuDeliverySO deliverySO) {
        LOGGER.info("订单查询发货产品sku参数: {}", JSON.toJSONString(deliverySO));
        // 校验参数
        validateDeliverySO(deliverySO);

        // 1、根据规格ID+ownerId查询实际发货skuId
        List<ProductSkuBySpecificationQueryDTO> specList = getSpecListByOrder(deliverySO);
        // from 下单城市
        // inner join 中心仓
        List<ProductSkuDeliveryPO> deliveryPOList =
            productSkuPOMapper.listProductSkuDelivery(specList, deliverySO.getCityId(), deliverySO.getDeliveryCityId());

        // 2、判断用户下单城市（前置仓）sku是否存在
        List<String> identityList = deliveryPOList.stream().map(p -> p.getIdentityKey()).collect(Collectors.toList());
        List<ProductSkuDeliveryItemSO> noExistList = deliverySO.getSkuList().stream()
            .filter(p -> !identityList.contains(p.getIdentityKey())).collect(Collectors.toList());
        // 不存在，说明用户下单城市（前置仓）不存在SKU，需要自动创建
        if (!CollectionUtils.isEmpty(noExistList)) {
            // 创建用户下单城市（前置仓）sku
            if (isCreateSku(deliverySO.getCompanyCode())) {
                Map<Long, ProductSkuDTO> mapCreateSku = createSkuByOrderCity(noExistList, deliveryPOList,
                    deliverySO.getCityId(), deliverySO.getWarehouseId());
                // // 创建下单城市sku后重新查询发货skuId
                // deliveryPOList = productSkuPOMapper.listProductSkuDelivery(specList, deliverySO.getCityId(),
                // deliverySO.getDeliveryCityId());
            } else {
                List<Long> noExistSkuList =
                    noExistList.stream().map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
                // 触发易款sku同步
                if (Objects.equals(deliverySO.getCompanyCode(), CompanyCode.YKBL)) {
                    sendEasyChainProductSkuSyncMessage(noExistSkuList);
                }
                throw new BusinessException(String.format("下单城市SKU不存在：%s, cityId：%s", JSON.toJSONString(noExistSkuList),
                    deliverySO.getCityId()));
            }
        }

        // 3、判断发货城市（中心仓）sku是否存在
        // 中心仓有库存的情况下才能下单，所以SKU必须存在，不存在则抛异常
        List<ProductSkuDeliveryPO> noExistDeliveryList =
            deliveryPOList.stream().filter(p -> p.getDeliverySkuId() == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noExistDeliveryList)) {
            throw new BusinessException(String.format("找不到发货城市SKU：%s, cityId：%s, deliveryCityId：%s",
                JSON.toJSONString(noExistDeliveryList), deliverySO.getCityId(), deliverySO.getDeliveryCityId()));
        }

        // 4、组装返回对象
        // 再次判断中心仓SKUID是否存在
        Map<Long, ProductSkuDTO> resultMap = new HashMap<>(16);
        // 根据skuId查询产品（中心仓SKU）
        List<Long> deliverySkuIds =
            deliveryPOList.stream().map(p -> p.getDeliverySkuId()).distinct().collect(Collectors.toList());
        Map<Long, ProductSkuDTO> skuDTOMap = findBySkuMap(deliverySkuIds);

        for (ProductSkuDeliveryItemSO sku : deliverySO.getSkuList()) {
            List<ProductSkuDeliveryPO> filerList = deliveryPOList.stream()
                .filter(p -> Objects.equals(p.getIdentityKey(), sku.getIdentityKey())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filerList)) {
                LOGGER.info("下单城市SKU查找异常，sku: {}, deliveryPOList: {}", JSON.toJSONString(sku),
                    JSON.toJSONString(deliveryPOList));
                throw new BusinessException("下单城市SKU查找异常，请联系管理员");
            }
            // 找到发货城市skuId
            Long deliverySkuId = filerList.get(0).getDeliverySkuId();
            // 找到多个发货城市sku时，如果同时存在作废与没作废的产品，保留非作废产品
            if (filerList.size() > 1
                && filerList.stream()
                    .anyMatch(p -> Objects.equals(p.getProductState(), ProductSkuStateEnum.作废.getType()))
                && filerList.stream()
                    .anyMatch(p -> !Objects.equals(p.getProductState(), ProductSkuStateEnum.作废.getType()))) {
                deliverySkuId = filerList.stream()
                    .filter(p -> !Objects.equals(p.getProductState(), ProductSkuStateEnum.作废.getType()))
                    .map(p -> p.getDeliverySkuId()).findFirst().get();
            }
            // 找到发货城市（中心仓）sku
            ProductSkuDTO deliverCitySkuDto = skuDTOMap.get(deliverySkuId);
            if (deliverCitySkuDto == null) {
                LOGGER.info("发货城市SKU查找异常，sku: {}", deliverySkuId);
                throw new BusinessException("发货城市sku查找异常，请联系管理员");
            }
            if (OwnerTypeConst.isFsCompanyId(deliverCitySkuDto.getCompany_Id())) {
                deliverCitySkuDto.setSecOwnerId(null);
            }
            resultMap.put(sku.getSkuId(), deliverCitySkuDto);
        }
        LOGGER.info("订单查询发货产品sku结果: {}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 获取下单产品的规格ID+ownerId
     *
     * @return
     */
    private List<ProductSkuBySpecificationQueryDTO> getSpecListByOrder(ProductSkuDeliverySO deliverySO) {
        // 获取掌上快销的规格ID和ownerId（掌销产品只有skuId）
        if (Objects.equals(deliverySO.getCompanyCode(), CompanyCode.ZSKX)) {
            // 验证掌上快销sku是否存在
            List<Long> skuIds =
                deliverySO.getSkuList().stream().map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
            List<ProductSkuDTO> findSkuDTOS = findBySku(skuIds);
            if (CollectionUtils.isEmpty(findSkuDTOS)) {
                throw new BusinessException(
                    String.format("[掌销]下单城市SKU不存在：%s, cityId：%s", JSON.toJSONString(skuIds), deliverySO.getCityId()));
            }
            List<Long> findSkuIds =
                findSkuDTOS.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            List<Long> noExistSkuList =
                skuIds.stream().filter(p -> !findSkuIds.contains(p)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noExistSkuList)) {
                throw new BusinessException(String.format("[掌销]下单城市SKU不存在：%s, cityId：%s",
                    JSON.toJSONString(noExistSkuList), deliverySO.getCityId()));
            }
            // 填充掌上快销的规格ID和ownerId
            deliverySO.getSkuList().forEach(p -> {
                List<ProductSkuDTO> filterList = findSkuDTOS.stream()
                    .filter(sku -> Objects.equals(p.getSkuId(), sku.getProductSkuId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) {
                    p.setSpecId(filterList.get(0).getProductSpecificationId());
                    p.setOwnerId(filterList.get(0).getCompany_Id());
                }
            });
        }
        return deliverySO.getSkuList().stream().map(p -> {
            ProductSkuBySpecificationQueryDTO queryDTO = new ProductSkuBySpecificationQueryDTO();
            queryDTO.setProductSpecificationId(p.getSpecId());
            queryDTO.setOwnerId(p.getOwnerId());
            return queryDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 创建下单城市sku
     */
    private Map<Long, ProductSkuDTO> createSkuByOrderCity(List<ProductSkuDeliveryItemSO> noExistList,
        List<ProductSkuDeliveryPO> deliveryPOList, Integer cityId, Integer warehouseId) {
        // 创建下单城市不存在的sku
        List<ProductSkuCreateDTO> createDTOList = noExistList.stream().map(p -> {
            ProductSkuCreateDTO createDTO = new ProductSkuCreateDTO();
            createDTO.setRefProductSkuId(p.getSkuId().toString());
            createDTO.setCityId(cityId);
            createDTO.setWarehouseId(warehouseId);
            createDTO.setProductSpecificationId(p.getSpecId());
            createDTO.setOwnerId(p.getOwnerId());
            createDTO.setSaleModel(p.getSaleModel());
            return createDTO;
        }).collect(Collectors.toList());
        Map<Long, ProductSkuDTO> createProductSkuMap =
            iProductSkuManageService.createProductSkuWithResult(createDTOList);
        LOGGER.info("创建下单城市sku: {}", JSON.toJSONString(createProductSkuMap));

        if (createProductSkuMap != null) {
            noExistList.forEach(p -> {
                // 若sku创建成功则拼接到返回对象中
                ProductSkuDTO productSkuDTO = createProductSkuMap.get(p.getSkuId());
                if (productSkuDTO != null) {
                    ProductSkuDeliveryPO deliveryPO = new ProductSkuDeliveryPO();
                    deliveryPO.setDeliverySkuId(p.getSkuId());
                    deliveryPO.setSkuId(productSkuDTO.getProductSkuId());
                    deliveryPO.setSpecId(p.getSpecId());
                    deliveryPO.setOwnerId(p.getOwnerId());
                    deliveryPOList.add(deliveryPO);
                }
            });
        }
        // 判断
        List<String> identityList = deliveryPOList.stream().map(p -> p.getIdentityKey()).collect(Collectors.toList());
        List<Long> noExistSkuList = noExistList.stream().filter(p -> !identityList.contains(p.getIdentityKey()))
            .map(p -> p.getSkuId()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noExistSkuList)) {
            throw new BusinessException(
                String.format("###下单城市SKU不存在：%s, cityId：%s", JSON.toJSONString(noExistSkuList), cityId));
        }
        return createProductSkuMap;
    }

    /**
     * 是否需要创建sku
     *
     * @return
     */
    private boolean isCreateSku(String companyCode) {
        if (Objects.equals(companyCode, CompanyCode.YJP) || Objects.equals(companyCode, CompanyCode.ZSKX)) {
            return true;
        }
        return false;
    }

    /**
     * 校验查询实际发货产品sku参数
     */
    private void validateDeliverySO(ProductSkuDeliverySO deliverySO) {
        AssertUtils.notNull(deliverySO, "参数不能为空");
        AssertUtils.notNull(deliverySO.getCompanyCode(), "企业编码不能为空");
        AssertUtils.notNull(deliverySO.getCityId(), "下单城市ID不能为空");
        AssertUtils.isTrue(deliverySO.getDeliveryCityId() != null || deliverySO.getDeliveryWarehouseId() != null,
            "发货城市ID或仓库ID不能为空");
        AssertUtils.notEmpty(deliverySO.getSkuList(), "产品sku不能为空");
        deliverySO.getSkuList().forEach(p -> {
            AssertUtils.notNull(p.getSkuId(), "产品skuId不能为空");
            // 非掌销产品
            if (!Objects.equals(deliverySO.getCompanyCode(), CompanyCode.ZSKX)) {
                AssertUtils.notNull(p.getSpecId(), "规格ID不能为空");
                // 需要创建sku的
                if (isCreateSku(deliverySO.getCompanyCode())) {
                    if (p.getSaleModel() == null) {
                        p.setSaleModel(SaleModeConstant.SALEMODEL_AGENT);
                    }
                    // AssertUtils.notNull(p.getSaleModel(), "销售模式不能为空");
                }
            }
            // 处理ownerId
            boolean isOwnerEmpty = Objects.equals(deliverySO.getCompanyCode(), CompanyCode.YKBL)
                || (p.getOwnerId() != null && p.getOwnerId() == 0);
            if (isOwnerEmpty) {
                p.setOwnerId(null);
            }
        });
        // 获取发货城市
        if (deliverySO.getDeliveryCityId() == null) {
            if (deliverySO.getDeliveryWarehouseId() == null) {
                throw new DataValidateException("发货仓库Id不能为空！");
            }
            // 根据仓库ID获取城市ID
            Warehouse warehouse = iWarehouseQueryService.findWarehouseById(deliverySO.getDeliveryWarehouseId());
            deliverySO.setDeliveryCityId(warehouse != null ? warehouse.getCityId() : null);
            if (deliverySO.getDeliveryCityId() == null) {
                throw new BusinessValidateException("获取发货城市ID为null");
            }
        }
    }

    /**
     * 触发易款sku同步
     */
    public void sendEasyChainProductSkuSyncMessage(List<Long> skuIds) {
        // try {
        // if (CollectionUtils.isEmpty(skuIds)) {
        // return;
        // }
        // LOGGER.info("触发易款sku同步：{}", JSON.toJSONString(skuIds));
        // regionProductInfoThirdService.sendRegionProductInfoMessageById(new HashSet(skuIds));
        // } catch (Exception e) {
        // LOGGER.error("触发易款sku同步异常", e);
        // }
    }

    /**
     * 根据skuId\refSkuId查找产品
     */
    public Map<String, ProductSkuDTO> findBySkuAndRef(List<String> productSkuIds) {
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        List<Long> numSkuList = productSkuIds.stream().filter(e -> StringUtils.isNumeric(e)).map(e -> Long.valueOf(e))
            .distinct().collect(Collectors.toList());
        List<ProductSkuPO> po = productSkuPOMapper.findBySkuAndRef(numSkuList, productSkuIds);
        if (CollectionUtils.isEmpty(po)) {
            return Collections.EMPTY_MAP;
        }
        List<ProductSkuDTO> skuDTOList = productSkuPOConvertor.convert(po);
        Map<String, ProductSkuDTO> skuMap = skuDTOList.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> String.valueOf(e.getProductSkuId()), Function.identity()));
        Map<String,
            ProductSkuDTO> refskuMap = skuDTOList.stream()
                .filter(e -> e != null && StringUtils.isNotBlank(e.getRefProductSkuId())).collect(Collectors
                    .toMap(ProductSkuDTO::getRefProductSkuId, Function.identity(), (v1, v2) -> v1 != null ? v1 : v2));
        Map<String, ProductSkuDTO> result = new HashMap<>(16);
        for (String skuId : productSkuIds) {
            if (StringUtils.isBlank(skuId) || result.containsKey(skuId)) {
                continue;
            }
            // 优先取供应链自己的sku, 没有则取关联sku
            ProductSkuDTO productSkuDTO = skuMap.get(skuId);
            if (productSkuDTO == null) {
                productSkuDTO = refskuMap.get(skuId);
            }
            if (productSkuDTO != null) {
                result.put(skuId, productSkuDTO);
            }

        }
        return result;
    }

    /**
     * 获取产品相关价格（成本价/销售价）
     *
     * @return
     */
    public List<ProductPriceDTO> getProductPrice(List<ProductPriceQueryDTO> productPriceQueryDTOS) {
        AssertUtils.notEmpty(productPriceQueryDTOS, "获取产品相关价格参数不能为空");
        productPriceQueryDTOS.forEach(p -> {
            AssertUtils.notNull(p.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(p.getProductSpecificationId(), "规格ID不能为空");
        });

        LOGGER.info("获取产品相关价格参数：{}", JSON.toJSONString(productPriceQueryDTOS));

        // 1、查询产品配置表的成本价/销售价
        List<ProductPriceDTO> priceDTOList = productSkuPOMapper.listProuductPrice(productPriceQueryDTOS);
        if (CollectionUtils.isEmpty(priceDTOList)) {
            LOGGER.info("获取产品相关价格为空");
            return Collections.EMPTY_LIST;
        }

        List<ProductSkuConfigDTO> configDTOS = new ArrayList<>();
        // 2、成本价为空时，调ERP接口获取成本价
        try {
            List<ProductPriceDTO> costPrices = priceDTOList.stream()
                .filter(p -> p.getCostPrice() == null || p.getCostPrice().compareTo(BigDecimal.ZERO) == 0)
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(costPrices)) {
                // 按仓库分组
                Map<Integer, List<ProductPriceDTO>> costPriceMap =
                    costPrices.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId()));
                costPriceMap.forEach((warehouseId, costPriceList) -> {
                    if (CollectionUtils.isEmpty(costPriceList)) {
                        return;
                    }
                    // 查询ERP产品规格成本价
                    List<Long> specIds = costPriceList.stream().map(p -> p.getProductSpecificationId()).distinct()
                        .collect(Collectors.toList());
                    List<ProductSpecCostPriceDTO> erpSpecCostPriceDTOS = getProductSpecCostPriceByERP(
                        costPriceList.get(0).getCityId(), costPriceList.get(0).getWarehouseId(), specIds);
                    LOGGER.info("查询ERP产品规格成本价：{}, cityId: {}, warehouseId: {}, specIds: {}",
                        JSON.toJSONString(erpSpecCostPriceDTOS), costPriceList.get(0).getCityId(),
                        costPriceList.get(0).getWarehouseId(), JSON.toJSONString(specIds));
                    if (!CollectionUtils.isEmpty(erpSpecCostPriceDTOS)) {
                        costPriceList.forEach(priceDTO -> {
                            Optional<ProductSpecCostPriceDTO> optional = erpSpecCostPriceDTOS.stream()
                                .filter(p -> Objects.equals(p.getSpecId(), priceDTO.getProductSpecificationId())
                                    && p.getCostPrice() != null && p.getCostPrice().compareTo(BigDecimal.ZERO) != 0)
                                .findFirst();
                            if (optional.isPresent()) {
                                // 获取成本价
                                priceDTO.setCostPrice(optional.get().getCostPrice());
                                // 更新成本价
                                if (configDTOS.stream()
                                    .anyMatch(p -> Objects.equals(p.getWarehouseId(), priceDTO.getWarehouseId())
                                        && Objects.equals(p.getProductSkuId(), priceDTO.getProductSkuId()))) {
                                    ProductSkuConfigDTO oldConfigDTO = configDTOS.stream()
                                        .filter(p -> Objects.equals(p.getWarehouseId(), priceDTO.getWarehouseId())
                                            && Objects.equals(p.getProductSkuId(), priceDTO.getProductSkuId()))
                                        .findFirst().get();
                                    oldConfigDTO.setCostPrice(priceDTO.getCostPrice());
                                } else {
                                    ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
                                    configDTO.setWarehouseId(priceDTO.getWarehouseId());
                                    configDTO.setProductSkuId(priceDTO.getProductSkuId());
                                    configDTO.setCostPrice(priceDTO.getCostPrice());
                                    configDTOS.add(configDTO);
                                }
                            }
                        });
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("查询ERP产品规格成本价异常", e);
        }

        // 3、销售价为空时，调交易接口获取销售价
        try {
            List<ProductPriceDTO> sellPrices = priceDTOList.stream()
                .filter(p -> (p.getSellingPrice() == null || p.getSellingPrice().compareTo(BigDecimal.ZERO) == 0)
                    && p.getProductSpecificationId() != null && p.getProductSpecificationId() <= Integer.MAX_VALUE)
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sellPrices)) {
                // 按仓库分组
                Map<Integer, List<ProductPriceDTO>> sellPriceMap =
                    sellPrices.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId()));
                sellPriceMap.forEach((warehouseId, sellPriceList) -> {
                    if (CollectionUtils.isEmpty(sellPriceList)) {
                        return;
                    }
                    // 查询交易产品销售价
                    List<ScmProductSkuSearchDTO> searchDTOS = sellPriceList.stream().map(p -> {
                        ScmProductSkuSearchDTO searchDTO = new ScmProductSkuSearchDTO();
                        searchDTO.setProductInfoSpecId(p.getProductSpecificationId().intValue());
                        searchDTO.setWarehouseId(p.getWarehouseId());
                        return searchDTO;
                    }).collect(Collectors.toList());
                    // 500条分组查询，防止大SQL
                    List<ScmProductSkuDTO> scmProductSkuDTOS = new ArrayList<>();
                    List<List<ScmProductSkuSearchDTO>> partitions = Lists.partition(searchDTOS, 10);
                    for (List<ScmProductSkuSearchDTO> part : partitions) {
                        List<ScmProductSkuDTO> scmProductSkuDTOList = scmProductSkuQueryService.listProductSku(part);
                        if (!CollectionUtils.isEmpty(scmProductSkuDTOList)) {
                            scmProductSkuDTOS.addAll(scmProductSkuDTOList);
                        }
                    }
                    LOGGER.info("查询交易产品规格销售价：{}, 参数: {}", JSON.toJSONString(scmProductSkuDTOS),
                        JSON.toJSONString(searchDTOS));
                    if (!CollectionUtils.isEmpty(scmProductSkuDTOS)) {
                        sellPriceList.forEach(priceDTO -> {
                            Optional<ScmProductSkuDTO> optional = scmProductSkuDTOS.stream()
                                .filter(p -> Objects.equals(p.getProductInfoSpecificationId(),
                                    priceDTO.getProductSpecificationId().intValue())
                                    && Objects.equals((Objects.equals(p.getOwnerId(), 0L) ? null : p.getOwnerId()),
                                        priceDTO.getOwnerId())
                                    && p.getSellingPrice() != null
                                    && p.getSellingPrice().compareTo(BigDecimal.ZERO) != 0)
                                .findFirst();
                            if (optional.isPresent()) {
                                BigDecimal sellingPrice = optional.get().getSellingPrice();
                                // 销售价-立减金额
                                if (optional.get().getReduceAmount() != null) {
                                    sellingPrice = sellingPrice.subtract(optional.get().getReduceAmount());
                                }
                                // 小于零时当作零
                                if (sellingPrice.compareTo(BigDecimal.ZERO) < 0) {
                                    sellingPrice = BigDecimal.ZERO;
                                }
                                priceDTO.setSellingPrice(sellingPrice);
                                priceDTO.setSellingPriceUnit(optional.get().getSellingPriceUnit());
                                // 更新销售价
                                if (configDTOS.stream()
                                    .anyMatch(p -> Objects.equals(p.getWarehouseId(), priceDTO.getWarehouseId())
                                        && Objects.equals(p.getProductSkuId(), priceDTO.getProductSkuId()))) {
                                    ProductSkuConfigDTO oldConfigDTO = configDTOS.stream()
                                        .filter(p -> Objects.equals(p.getWarehouseId(), priceDTO.getWarehouseId())
                                            && Objects.equals(p.getProductSkuId(), priceDTO.getProductSkuId()))
                                        .findFirst().get();
                                    oldConfigDTO.setSellingPrice(priceDTO.getSellingPrice());
                                    oldConfigDTO.setSellingPriceUnit(priceDTO.getSellingPriceUnit());
                                } else {
                                    ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
                                    configDTO.setWarehouseId(priceDTO.getWarehouseId());
                                    configDTO.setProductSkuId(priceDTO.getProductSkuId());
                                    configDTO.setSellingPrice(priceDTO.getSellingPrice());
                                    configDTO.setSellingPriceUnit(priceDTO.getSellingPriceUnit());
                                    configDTOS.add(configDTO);
                                }
                            }
                        });
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.info("查询交易产品销售价异常", e);
        }

        // 4、更新产品配置表的成本价
        try {
            if (!CollectionUtils.isEmpty(configDTOS)) {
                productSkuConfigBL.saveProductSkuConfigBatch(configDTOS);
                LOGGER.info("更新成本价/销售价：{}", JSON.toJSONString(configDTOS));
            }
        } catch (Exception e) {
            LOGGER.error("更新成本价/销售价异常", e);
        }
        LOGGER.info("获取产品相关价格结果：{}", JSON.toJSONString(priceDTOList));
        return priceDTOList;
    }

    /**
     * 获取产品成本价
     *
     * @return
     */
    public Map<Long, BigDecimal> getProductCostPriceMap(ProductCostPriceQueryDTO productCostPriceQueryDTO) {
        AssertUtils.notNull(productCostPriceQueryDTO, "获取产品成本价参数不能为空");
        AssertUtils.notNull(productCostPriceQueryDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(productCostPriceQueryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(productCostPriceQueryDTO.getSkuIds(), "skuId不能为空");
        LOGGER.info("获取产品成本价参数：{}", JSON.toJSONString(productCostPriceQueryDTO));

        // 1、查询产品配置表的成本价
        List<ProductSkuPO> productSkuPOS = productSkuPOMapper.findBySkuFull(productCostPriceQueryDTO.getWarehouseId(),
            productCostPriceQueryDTO.getSkuIds());
        if (CollectionUtils.isEmpty(productSkuPOS)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, BigDecimal> costPriceMap = new HashMap<>(16);
        productSkuPOS.stream().filter(p -> p.getCostPrice() != null && p.getCostPrice().compareTo(BigDecimal.ZERO) != 0)
            .forEach(p -> {
                costPriceMap.put(p.getProductSku_Id(), p.getCostPrice());
            });

        // 2、成本价为空时，调ERP接口获取成本价
        try {
            List<Long> specIds = productSkuPOS.stream()
                .filter(p -> p.getCostPrice() == null || p.getCostPrice().compareTo(BigDecimal.ZERO) == 0)
                .map(p -> p.getProductSpecification_Id()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(specIds)) {
                // 查询ERP产品规格成本价
                List<ProductSpecCostPriceDTO> productSpecCostPriceDTOS = getProductSpecCostPriceByERP(
                    productCostPriceQueryDTO.getCityId(), productCostPriceQueryDTO.getWarehouseId(), specIds);
                LOGGER.info("查询ERP产品规格成本价：{}, specIds: {}", JSON.toJSONString(productSpecCostPriceDTOS),
                    JSON.toJSONString(specIds));
                if (!CollectionUtils.isEmpty(productSpecCostPriceDTOS)) {
                    List<ProductSkuConfigDTO> configDTOS = new ArrayList<>();
                    productSpecCostPriceDTOS.forEach(specCostPrice -> {
                        productSkuPOS.stream()
                            .filter(p -> Objects.equals(p.getProductSpecification_Id(), specCostPrice.getSpecId())
                                && p.getCostPrice() != null && p.getCostPrice().compareTo(BigDecimal.ZERO) != 0)
                            .forEach(p -> {
                                costPriceMap.put(p.getProductSku_Id(), specCostPrice.getCostPrice());
                                // 更新产品成本价
                                ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
                                configDTO.setWarehouseId(productCostPriceQueryDTO.getWarehouseId());
                                configDTO.setProductSkuId(p.getProductSku_Id());
                                configDTO.setCostPrice(specCostPrice.getCostPrice());
                                configDTOS.add(configDTO);
                            });
                    });

                    // 3、更新产品配置表的成本价
                    if (!CollectionUtils.isEmpty(configDTOS)) {
                        productSkuConfigBL.saveProductSkuConfigBatch(configDTOS);
                        LOGGER.info("更新成本价：{}", JSON.toJSONString(configDTOS));
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("查询ERP产品规格成本价异常", e);
        }
        LOGGER.info("获取产品成本价结果：{}", JSON.toJSONString(costPriceMap));
        return costPriceMap;
    }

    /**
     * 查询ERP产品规格成本价
     *
     * @return
     */
    private List<ProductSpecCostPriceDTO> getProductSpecCostPriceByERP(Integer cityId, Integer warehouseId,
        List<Long> specIds) {
        List<ProductSpecCostPriceDTO> returnList = new ArrayList<>();
        List<List<Long>> partitions = Lists.partition(specIds, 50);
        for (List<Long> specList : partitions) {
            ProductSpecCostPriceParam costPriceParam = new ProductSpecCostPriceParam();
            costPriceParam.setCityId(cityId);
            costPriceParam.setStoreHouseId(warehouseId);
            costPriceParam.setSpecIds(specList);

            DataResult<List<ProductSpecCostPriceDTO>> dataResult =
                restTemplate.exchange(ConfigUtil.erpAPIUrl + "/Setting/GetProductSpecCostPrice", HttpMethod.POST,
                    new HttpEntity<>(costPriceParam),
                    new ParameterizedTypeReference<DataResult<List<ProductSpecCostPriceDTO>>>() {}).getBody();
            if (dataResult == null) {
                throw new BusinessValidateException("远程调用查询ERP产品规格成本价失败");
            }
            if (!dataResult.getSuccess()) {
                LOGGER.info("远程调用查询ERP产品规格成本价失败：{}", JSON.toJSONString(dataResult));
                throw new BusinessValidateException(dataResult.getMessage());
            }
            if (!CollectionUtils.isEmpty(dataResult.getData())) {
                returnList.addAll(dataResult.getData());
            }
        }
        return returnList;
    }

    /**
     * 查询产品属性：批次生产日、分拣占用及残次品信息
     */
    public List<ProductRelevantInfoDTO> findProductRelevantInfo(ProductRelevantInfoQueryDTO relevantInfoQueryDTO) {
        LOGGER.info("findProductRelevantInfo - 查询产品【批次生产日、分拣占用及残次品】信息参数：{}", JSON.toJSONString(relevantInfoQueryDTO));
        AssertUtils.notNull(relevantInfoQueryDTO, "查询参数不能为空！");
        AssertUtils.notNull(relevantInfoQueryDTO.getCityId(), "城市ID不能为空！");
        AssertUtils.notNull(relevantInfoQueryDTO.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notEmpty(relevantInfoQueryDTO.getSpecList(), "产品规格信息不能为空！");
        // 规格信息去重
        List<ProductRelevantInfoSpecQueryDTO> specList = relevantInfoQueryDTO.getSpecList();
        List<ProductRelevantInfoSpecQueryDTO> nonRepeatSpecList = specList.stream().filter(Objects::nonNull)
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                    o -> String.format("%s%s%s", o.getProductSpecificationId(), o.getOwnerId(), o.getSecOwnerId())))),
                ArrayList::new));
        // 中台sku兼容
        conversionProductUnifySku(nonRepeatSpecList);
        // 根据规格信息转化成WMS产品sku
        ProductSkuBySpecificationSO specSO = new ProductSkuBySpecificationSO();
        specSO.setCityId(relevantInfoQueryDTO.getCityId());
        specSO.setWarehouseId(relevantInfoQueryDTO.getWarehouseId());
        specSO.setSpecList(nonRepeatSpecList.stream().filter(Objects::nonNull).map(e -> {
            ProductSkuBySpecificationQueryDTO specQueryDTO = new ProductSkuBySpecificationQueryDTO();
            specQueryDTO.setProductSpecificationId(e.getProductSpecificationId());
            specQueryDTO.setOwnerId(e.getOwnerId());
            specQueryDTO.setSecOwnerId(e.getSecOwnerId());
            return specQueryDTO;
        }).collect(Collectors.toList()));
        List<ProductSkuDTO> skuDTOList = findBySpec(specSO);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            LOGGER.warn("findProductRelevantInfo - 根据产品规格信息没有找到WMS对应产品：{}", JSON.toJSONString(relevantInfoQueryDTO));
            return Collections.emptyList();
        }
        LOGGER.info("findProductRelevantInfo - 转化产品SKU结果：{}", JSON.toJSONString(skuDTOList));
        List<Long> skuIdList =
            skuDTOList.stream().filter(Objects::nonNull).map(e -> e.getProductSkuId()).collect(Collectors.toList());
        // 分拣占用数量
        Map<Long, BigDecimal> pickedCountForSCM25 = iBatchTaskQueryService.findSCM25PickedCountForStoreCheck(
            relevantInfoQueryDTO.getCityId(), relevantInfoQueryDTO.getWarehouseId(), skuIdList, null);
        if (pickedCountForSCM25 == null) {
            pickedCountForSCM25 = Collections.EMPTY_MAP;
        }
        // 内配单未出库数量
        Map<Long, BigDecimal> internalPickedCount = iBatchTaskQueryService.findCreateAllocationPickedCountForStoreCheck(
            relevantInfoQueryDTO.getCityId(), relevantInfoQueryDTO.getWarehouseId(), skuIdList, null);
        if (internalPickedCount == null) {
            internalPickedCount = Collections.EMPTY_MAP;
        }
        // 产品生产日期
        List<BatchProductionDateDTO> batchProductionDateList =
            findBatchProductionDate(relevantInfoQueryDTO, nonRepeatSpecList);
        // 产品关联货位
        List<ProductLocationListDTO> productConfigLocationList =
            findProductConfigLocation(relevantInfoQueryDTO, skuIdList);
        // 处理品/残次品集合
        List<DisposedProductInventorDTO> disposedProductInventors =
            iWarehouseInventoryCheckService.findDisposedProductInventorBySkuId(relevantInfoQueryDTO.getCityId(),
                relevantInfoQueryDTO.getWarehouseId(), skuIdList);
        List<ProductRelevantInfoDTO> result = new ArrayList<>(skuDTOList.size());
        for (ProductSkuDTO skuDTO : skuDTOList) {
            if (skuDTO == null) {
                continue;
            }
            Long productSkuId = skuDTO.getProductSkuId();
            // 分拣占用数量
            BigDecimal pickedCount = ObjectUtils.defaultIfNull(pickedCountForSCM25.get(productSkuId), BigDecimal.ZERO);
            // 内配单未出库数量
            BigDecimal internalCount =
                    ObjectUtils.defaultIfNull(internalPickedCount.get(productSkuId), BigDecimal.ZERO);
            // 产品生产日期: 接口无调用，直接删除 FIXME
            // 产品关联货位
            List<ProductConfigLocationDTO> productLocationList = productConfigLocationList.stream()
                .filter(e -> e != null && Objects.equals(productSkuId, e.getProductSkuId())).map(e -> {
                    ProductConfigLocationDTO configLocationDTO = new ProductConfigLocationDTO();
                    configLocationDTO.setCityId(relevantInfoQueryDTO.getCityId());
                    configLocationDTO.setWarehouseId(relevantInfoQueryDTO.getWarehouseId());
                    configLocationDTO.setLocationId(e.getLocationId());
                    configLocationDTO.setLocationName(e.getLocationName());
                    configLocationDTO.setProductSkuId(e.getProductSkuId());
                    configLocationDTO.setSubcategory(e.getSubcategory());
                    configLocationDTO.setSubcategoryName(e.getSubcategoryName());
                    return configLocationDTO;
                }).collect(Collectors.toList());
            // 处理品/残次品集合
            List<DisposedProductInventorDTO> disposedProductInventorList = disposedProductInventors.stream()
                .filter(e -> e != null && Objects.equals(productSkuId, e.getProductSkuId()))
                .collect(Collectors.toList());
            // 组装结果
            ProductRelevantInfoDTO relevantInfoDTO = new ProductRelevantInfoDTO();
            relevantInfoDTO.setCityId(relevantInfoQueryDTO.getCityId());
            relevantInfoDTO.setWarehouseId(relevantInfoQueryDTO.getWarehouseId());
            relevantInfoDTO.setSpecificationName(skuDTO.getSpecificationName());
            relevantInfoDTO.setPackageName(skuDTO.getPackageName());
            relevantInfoDTO.setUnitName(skuDTO.getUnitName());
            relevantInfoDTO.setProductSpecificationId(skuDTO.getProductSpecificationId());
            relevantInfoDTO.setOwnerId(skuDTO.getCompany_Id());
            relevantInfoDTO.setSecOwnerId(skuDTO.getSecOwnerId());
            relevantInfoDTO.setPickedCount(pickedCount);
            relevantInfoDTO.setInternalPickedCount(internalCount);
            relevantInfoDTO.setProductConfigLocationList(productLocationList);
            relevantInfoDTO.setDisposedProductInventorList(disposedProductInventorList);
            result.add(relevantInfoDTO);
        }
        LOGGER.info("findProductRelevantInfo - 查询产品【批次生产日、分拣占用及残次品】信息结果：{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 转化中台sku
     */
    private void conversionProductUnifySku(List<ProductRelevantInfoSpecQueryDTO> nonRepeatSpecList) {
        if (CollectionUtils.isEmpty(nonRepeatSpecList)) {
            return;
        }
        List<Long> unifySkuIds = nonRepeatSpecList.stream().filter(item -> item != null && item.getUnifySkuId() != null)
            .map(ProductRelevantInfoSpecQueryDTO::getUnifySkuId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unifySkuIds)) {
            return;
        }
        Map<Long, ProductSpecIdAndOwnerIdDTO> unifySkuIdMap = new HashMap<>(16);
        // //根据unifySkuIds查询规格货主信息
        // Map<Long, ProductSpecIdAndOwnerIdDTO> unifySkuMap =
        // unifySkuCacheBL.findProductSpecIdAndOwnerIdMapByUnifySkuIds(unifySkuIds);
        // if (unifySkuMap != null && !unifySkuMap.isEmpty()) {
        // unifySkuIdMap.putAll(unifySkuMap);
        // }
        nonRepeatSpecList.forEach(item -> {
            if (item != null && item.getUnifySkuId() != null) {
                ProductSpecIdAndOwnerIdDTO productSpecIdAndOwnerIdDTO = unifySkuIdMap.get(item.getUnifySkuId());
                if (productSpecIdAndOwnerIdDTO == null) {
                    throw new BusinessValidateException("中台 SKU 信息在WMS系统不存在，unifySkuId:" + item.getUnifySkuId());
                }
                item.setProductSpecificationId(productSpecIdAndOwnerIdDTO.getProductSpecId());
                item.setOwnerId(productSpecIdAndOwnerIdDTO.getOwnerId());
            }
        });
    }

    /**
     * 产品关联货位
     */
    private List<ProductLocationListDTO> findProductConfigLocation(ProductRelevantInfoQueryDTO relevantInfoQueryDTO,
        List<Long> skuIdList) {
        ProductLocationListSO locationListSO = new ProductLocationListSO();
        locationListSO.setCityId(relevantInfoQueryDTO.getCityId());
        locationListSO.setWarehouseId(relevantInfoQueryDTO.getWarehouseId());
        locationListSO.setProductSkuIdList(skuIdList);
        PageList<ProductLocationListDTO> productLocationList = productLocationBL.listProductLocation(locationListSO);
        List<ProductLocationListDTO> dataList = productLocationList.getDataList();
        return dataList == null ? Lists.newArrayList() : dataList;
    }

    /**
     * 查找产品生产日期
     */
    private List<BatchProductionDateDTO> findBatchProductionDate(ProductRelevantInfoQueryDTO relevantInfoQueryDTO,
        List<ProductRelevantInfoSpecQueryDTO> nonRepeatSpecList) {
        BatchProductionDateQueryDTO productionDateQueryDTO = new BatchProductionDateQueryDTO();
        productionDateQueryDTO.setCityId(relevantInfoQueryDTO.getCityId());
        productionDateQueryDTO.setWarehouseId(relevantInfoQueryDTO.getWarehouseId());
        productionDateQueryDTO.setSpecList(nonRepeatSpecList.stream().filter(Objects::nonNull).map(e -> {
            BatchProductionDateSpecQueryDTO specQueryDTO = new BatchProductionDateSpecQueryDTO();
            specQueryDTO.setProductSpecificationId(e.getProductSpecificationId());
            specQueryDTO.setOwnerId(e.getOwnerId());
            specQueryDTO.setSecOwnerId(e.getSecOwnerId());
            return specQueryDTO;
        }).collect(Collectors.toList()));
        List<BatchProductionDateDTO> productionDateFromStoreBatch =
            iBatchInventoryQueryService.findProductionDateFromStoreBatch(productionDateQueryDTO);
        return productionDateFromStoreBatch == null ? Lists.newArrayList() : productionDateFromStoreBatch;
    }

    /**
     * 根据条件查询产品信息及类目信息
     */
    public List<ProductSkuInfoDTO> findProductInfoAndCategory(ProductInfoCategoryQuery query) {
        LOGGER.info("根据条件查询产品信息及类目信息参数：{}", JSON.toJSONString(query));
        AssertUtils.notNull(query, "查询参数不能为空");
        AssertUtils.notNull(query.getCityId(), "城市ID不能为空");
        AssertUtils.notEmpty(query.getProductSkuIdList(), "产品SKU集合不能为空");
        return productSkuPOMapper.findProductInfoAndCategory(query);
    }

    /**
     * 查询有仓库库存[包含负库存]产品信息及类目
     */
    public PageList<ProductSkuInfoDTO> findHaveInventoryProductInfo(ProductInfoCategoryQuery query) {
        LOGGER.info("查询有仓库库存[包含负库存]产品信息及类目参数：{}", JSON.toJSONString(query));
        AssertUtils.notNull(query, "查询参数不能为空");
        AssertUtils.notNull(query.getWarehouseId(), "仓库ID不能为空");
        if (ObjectUtils.defaultIfNull(query.getSelectTotal(), false)) {
            // 一次性查询所有产品数据量过大，分批查询
            List<ProductSkuInfoDTO> result = new ArrayList<>(500);
            query.setPageSize(500);
            // 所有页码
            int totalPage = 1;
            // 查询条数
            int pageSize = 500;
            for (int p = 1; p <= totalPage; p++) {
                if (p == 1) {
                    PageHelper.startPage(p, pageSize);
                } else {
                    PageHelper.startPage(p, pageSize, false);
                }
                query.setPageNum(p);
                PageResult<ProductSkuInfoDTO> pageResult = productSkuPOMapper.findHaveInventoryProductInfo(query);
                if (!CollectionUtils.isEmpty(pageResult) && pageResult.size() > 0) {
                    if (p == 1) {
                        totalPage = pageResult.getPager().getTotalPage();
                    }
                    result.addAll(pageResult.getResult());
                    pageResult.clear();
                }
            }
            PageList pageList = new PageList();
            pageList.setDataList(result);
            pageList.setPager(new Pager(1, pageSize, result.size()));
            return pageList;
        } else {
            PageResult<ProductSkuInfoDTO> pageResult = productSkuPOMapper.findHaveInventoryProductInfo(query);
            return pageResult.toPageList();
        }
    }

    /**
     * 拼音搜索
     */
    public List<String> getSpellSearch(SpellSearchSO spellSearchSO) {
        AssertUtils.notNull(spellSearchSO, "拼音搜索参数不能为空");
        AssertUtils.notNull(spellSearchSO.getQuery(), "搜索词不能为空");
        // 返回数量不超过10
        if (spellSearchSO.getNum() < 1 || spellSearchSO.getNum() > 100) {
            spellSearchSO.setNum(1);
        }
        SpellSearchResult dataResult =
            restTemplate.exchange(String.format("%s/ai/search/api/pinyin", ConfigUtil.searchAIUrl), HttpMethod.POST,
                new HttpEntity<>(spellSearchSO), SpellSearchResult.class).getBody();
        LOGGER.info("调用拼音搜索参数：{}，返回：{}", JSON.toJSONString(spellSearchSO), JSON.toJSONString(dataResult));
        return dataResult == null ? Collections.EMPTY_LIST : dataResult.getPinyin();
    }

    /**
     * 产品是否休食类或者酒饮类
     */
    public List<ProductRestOrDrinkCategoryDTO> findProductRestOrDrinkCategory(ProductInfoCategoryQuery query) {
        LOGGER.info("查询产品休食或者酒饮类目信息参数：{}", JSON.toJSONString(query));
        AssertUtils.notNull(query, "查询参数不能为空");
        AssertUtils.notEmpty(query.getProductSkuIdList(), "产品SKU集合不能为空");
        List<ProductSkuInfoDTO> productList = productSkuPOMapper.findProductAndTopCategory(query);
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.emptyList();
        }
        final String restCategoryName = "休食百货";
        Map<Long, String> categoryMap =
            productList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductSkuInfoDTO::getProductSkuId,
                e -> StringUtils.trimToEmpty(e.getCategoryRemark()), (v1, v2) -> StringUtils.isNotBlank(v1) ? v1 : v2));
        // 只要不是【休食百货】就都按酒饮类处理
        return query.getProductSkuIdList().stream().filter(Objects::nonNull).distinct().map(skuId -> {
            boolean isRest = StringUtils.equals(restCategoryName, StringUtils.trimToNull(categoryMap.get(skuId)));
            return new ProductRestOrDrinkCategoryDTO(skuId,
                isRest ? RestDrinkCategoryEnum.REST.getValue() : RestDrinkCategoryEnum.DRINK.getValue());
        }).collect(Collectors.toList());
    }

    /**
     * 根据城市、规格、货主查找sku
     */
    public ProductSkuDTO getProductSkuBySpecId(Integer cityId, Long specId, Long ownerId, Integer source) {
        AssertUtils.notNull(cityId, "城市Id不能为空");
        AssertUtils.notNull(specId, "规格Id不能为空");
        ProductSkuPO productSkuPO = productSkuPOMapper.getProductSkuBySpecId(cityId, specId, ownerId, source);
        return productSkuPO == null ? null : productSkuPOConvertor.convert(productSkuPO);
    }

    /**
     * 根据skuId查找sku
     */
    public ProductSkuDTO selectBySkuId(Long skuId) {
        AssertUtils.notNull(skuId, "skuId不能为空");
        ProductSkuPO productSkuPO = productSkuPOMapper.selectBySkuId(skuId);
        return productSkuPO == null ? null : productSkuPOConvertor.convert(productSkuPO);
    }

    /**
     * 根据城市id、规格id、货主id判断sku中是否含Replace_To_Sku_Id字段
     */
    public boolean isHaveReplaceToSku(List<ProductSkuDTO> productSkuList) {
        if (CollectionUtils.isEmpty(productSkuList)) {
            return false;
        }
        productSkuList.forEach(elem -> {
            AssertUtils.notNull(elem.getProductSpecificationId(), "规格不能为空");
            AssertUtils.notNull(elem.getCityId(), "城市Id不能为空");
        });

        return productSkuPOMapper.isHaveReplaceSku(productSkuList) > 0;
    }

    /**
     * 根据cityId、skuId查询有package关联的sku
     */
    public Map<Long, List<ProductSkuDTO>> listRelationPackageSku(ProductSkuQueryDTO queryDto) {
        AssertUtils.notNull(queryDto, "查询参数不能为空");
        AssertUtils.notNull(queryDto.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(queryDto.getSkuIdList(), "产品skuID不能为空");

        // 根据sku查出相同中台info的sku
        List<ProductSkuPO> productSkuPOS =
            productSkuPOMapper.listSameUnifyProductInfoSku(queryDto.getCityId(), queryDto.getSkuIdList());

        Map<Long, List<ProductSkuPO>> productSkuPOMap =
            productSkuPOS.stream().collect(Collectors.groupingBy(ProductSkuPO::getId));

        Map<Long, List<ProductSkuDTO>> res = new HashMap<>(16);
        productSkuPOMap.forEach((skuId, productSkuPOList) -> {
            List<ProductSkuDTO> productSkuDTOList = productSkuPOConvertor.convert(productSkuPOList);

            // 所有关联sku都具有大单位packageId，才会计算minimumPackageQuantity，防止计算错误
            boolean isWhole = productSkuDTOList.stream().allMatch(elem -> elem.getPackageUnifyPackageId() != null);
            if (isWhole) {
                productSkuDTOList.forEach(curSkuDTO -> {
                    // 获取当前sku的所有子sku
                    List<ProductSkuDTO> sonSkuList = relationPackageSkuBL.iterSonSku(curSkuDTO, productSkuDTOList);
                    BigDecimal minimumPackageQuantity = sonSkuList.stream().map(ProductSkuDTO::getPackageQuantity)
                        .reduce(BigDecimal.ONE, BigDecimal::multiply);
                    curSkuDTO.setMinimumPackageQuantity(minimumPackageQuantity);
                });
            } else {
                LOGGER.warn("[查询有package关联的sku]关联sku列表的大单位packageId有null，不计算minimumPackageQuantity，城市Id:{}，sku的id:{}",
                    queryDto.getCityId(), skuId);
            }

            res.put(skuId, productSkuDTOList);
        });

        return res;
    }

    public List<ProductSkuDTO> listSkuDetails(QuerySkuDetailsParamDTO queryDTO) {
        LOGGER.info("ProductSkuQueryBL.listSkuDetails 根据仓库货主规格查询sku信息: {}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(queryDTO.getQuerySkuParamList(), "参数不能为空");
        for (QuerySkuDetailsParamDTO.QuerySkuParam querySkuParam : queryDTO.getQuerySkuParamList()) {
            AssertUtils.notNull(querySkuParam.getSpecificationId(), "规格id不能为空");
        }
        List<ProductSkuDTO> skus = productSkuConfigHelper.listSkuDetails(queryDTO);
        try {
            // 如果有上架的产品就只返回上架的, 否则就返回所有数据
            return skus.stream().collect(Collectors.groupingBy(this::getGroupName)).values().stream()
                .map(this::validSkuFirst).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.warn("出现异常, 返回所有 sku 数据: ", e);
        }
        return skus;
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置）
     */
    public List<ProductSkuDTO> findProductBySkuFull(Integer warehouseId, List<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "skuId不能为空");
        List<ProductSkuPO> po = productSkuPOMapper.findProductBySkuFull(warehouseId, productSkuIds);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }
        List<ProductSkuDTO> resultList = productSkuPOConvertor.convert(po);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        // 获取瓶码和箱码
        Set<Long> skuIdSet = resultList.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> codeMap =
            productSkuServiceBL.getPackageAndUnitCode(skuIdSet, resultList.get(0).getCityId());
        resultList.forEach(sku -> {
            // 设置瓶码和箱码
            setPackageAndUnitCode(codeMap, sku);
        });
        return productSkuConfigHelper.fillAdditionProp(resultList, warehouseId);
    }

    /**
     * 获取融销sku信息
     *
     * @param queryDTO
     * @return
     */
    public List<ProductSkuDTO> getFsProductSku(FinanceSaleProductSkuQueryDTO queryDTO) {
        List<ProductSkuPO> productSkuPO = productSkuPOMapper.getProductSkuByCon(queryDTO.getCityId(),
            queryDTO.getDealerId(), queryDTO.getCompanyId(), queryDTO.getProductSpecificationIds());
        if (Objects.isNull(productSkuPO)) {
            throw new BusinessValidateException("产品sku信息不存在！");
        }
        return productSkuPOConvertor.convert(productSkuPO);
    }

    /**
     * 根据外部编码查询产品列表
     */
    public List<ProductSkuDTO> findBySkuOuterCode(Integer cityId, List<String> outerCodeList) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notEmpty(outerCodeList, "skuId不能为空");
        List<ProductSkuPO> po = productSkuPOMapper.findBySkuOuterCode(cityId, outerCodeList);
        if (CollectionUtils.isEmpty(po)) {
            return new ArrayList<>();
        }
        List<ProductSkuDTO> resultList = productSkuPOConvertor.convert(po);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        return resultList;
    }

    /**
     * 根据订单产品查询实际发货产品sku （key：订单产品skuId，value：实际发货sku）
     *
     * @param productSkuDeliverySO
     * @return
     */
    public Map<Long, ProductSkuDTO> getProductSkuDeliveryMapWithSecOwnerId(ProductSkuDeliverySO productSkuDeliverySO) {

        return null;
    }

    /**
     * 按照 规格-仓库-货主-二级货主 来分组 sku
     *
     * @param sku sku 数据
     */
    private String getGroupName(ProductSkuDTO sku) {
        Long specId = sku.getProductSpecificationId();
        Integer warehouseId = sku.getWarehouseId();
        Long ownerId = sku.getCompany_Id();
        Long secOwnerId = sku.getSecOwnerId();
        return String.format("%s-%s-%s-%s", specId, warehouseId, ownerId, secOwnerId);
    }

    /**
     * 优先返回上架的一条 sku 数据, 如果没有则返回下架的一条数据, 如果都没有那就返回空 list
     *
     * @param source 原始数据
     */
    private List<ProductSkuDTO> validSkuFirst(List<ProductSkuDTO> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        // Optional<ProductSkuDTO> validSkus =
        // source.stream().filter(item -> ProductSkuStateEnum.上架.getType().equals(item.getProductState())).findFirst();
        // // 只会返回一条数据
        // return validSkus.map(Collections::singletonList).orElse(Collections.singletonList(source.get(0)));

        // 按最后更新时间降序排序
        List<ProductSkuDTO> sortList = source.stream()
            .sorted(Comparator.comparing(ProductSkuDTO::getLastUpdateTime).reversed()).collect(Collectors.toList());
        // 未删除状态
        Optional<ProductSkuDTO> noDeleteSkus = sortList.stream()
            .filter(item -> Objects.equals(YesOrNoEnum.NO.getValue().byteValue(), item.getIsDelete())).findFirst();
        // 上架状态
        Optional<ProductSkuDTO> validSkus = sortList.stream()
            .filter(item -> ProductSkuStateEnum.上架.getType().equals(item.getProductState())).findFirst();
        // 只会返回一条数据
        return noDeleteSkus.map(Collections::singletonList)
            .orElse(validSkus.map(Collections::singletonList).orElse(Collections.singletonList(sortList.get(0))));
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置） 有条码/箱码 是否计算分仓属性
     */
    public List<ProductSkuDTO> findSkuFullByCondition(ProductSkuQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDTO.getSkuIdList(), "skuId不能为空");
        List<ProductSkuDTO> resultList = productSkuPOConvertor
            .convert(productSkuPOMapper.findProductBySkuFull(queryDTO.getWarehouseId(), queryDTO.getSkuIdList()));
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        // 获取瓶码和箱码
        Set<Long> skuIdSet = resultList.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toSet());
        Map<Long, ProductCodeDTO> codeMap =
            productSkuServiceBL.getPackageAndUnitCode(skuIdSet, resultList.stream().findFirst().get().getCityId());
        resultList.forEach(sku -> {
            // 设置瓶码和箱码
            setPackageAndUnitCode(codeMap, sku);
        });

        // 默认计算分仓属性
        if (queryDTO.getCalStorageAttribute() != null && queryDTO.getCalStorageAttribute()) {
            return productSkuConfigHelper.fillAdditionProp(resultList, queryDTO.getWarehouseId());
        }

        return resultList;
    }
}
