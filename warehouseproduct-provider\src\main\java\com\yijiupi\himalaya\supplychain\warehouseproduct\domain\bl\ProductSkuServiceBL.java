package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.config.UserWarehouseAllocation;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationQuery;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IProductInfoSpecificationQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.product.ScmProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductInfoBoxCodeOwnerType;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecificationMain;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductCodeDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductCodeListDTO;
import com.yijiupi.himalaya.supplychain.enums.WarehouseAllocationTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCodeInfoQueryService;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoCategoryService;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productiondate.ProductionDateBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ProductWarehouseAllocationTypeVerifyBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.SkuInventoryAndProductionDateHelperBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.SkuInventoryAndProductionDateQO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.cache.ScmProductCache;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuAndCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productiondate.ProductionDateQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductPutawayStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductSkuServiceBL {

    private static final Logger logger = LoggerFactory.getLogger(ProductSkuServiceBL.class);

    @Autowired
    private ProductSkuPOMapper mapper;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductInfoSpecificationQueryService iProductInfoSpecificationQueryService;

    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    private Integer batchSize = 200;

    /**
     * 产品SKU接口
     */
    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductInfoQueryService scmProductInfoQueryService;

    @Reference
    private IOrgService iOrgService;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Reference
    private IProductInfoCategoryService productInfoCategoryService;
    @Reference
    private IProductCodeInfoQueryService iProductCodeInfoQueryService;

    @Autowired
    private StockAgeStrategyBL stockAgeStrategyBL;
    @Autowired
    private ScmProductCache scmProductCache;
    @Resource
    private ProductionDateBL productionDateBL;
    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;

    @Transactional(rollbackFor = RuntimeException.class)
    public void add(ProductSkuDTO productSkuDTO) {
        ProductSkuPO productSkuPO = mapper.selectByCityIdAndProductSkuId(productSkuDTO.getProductSkuId());
        if (productSkuPO != null) {
            productSkuPO.setName(productSkuDTO.getName());
            productSkuPO.setLastUpdateTime(new Date());
            if (productSkuDTO.getSaleModel() != null) {
                productSkuPO.setSaleModel(productSkuDTO.getSaleModel());
            }
            if (productSkuDTO.getCompany_Id() != null) {
                productSkuPO.setCompany_Id(productSkuDTO.getCompany_Id());
            }
            if (StringUtils.isNotBlank(productSkuDTO.getOwnerName())) {
                productSkuPO.setOwnerName(productSkuDTO.getOwnerName());
            }
            mapper.updateByPrimaryKeySelective(productSkuPO);
        } else {
            // 根据skuid查询配送系数.
            ProductInfoSpecificationMain productInfoSpecificationMain = iProductInfoSpecificationQueryService
                .getProductInfoSpecificationMain(productSkuDTO.getProductSpecificationId().intValue());
            productSkuPO = new ProductSkuPO();
            productSkuPO.setCity_Id(productSkuDTO.getCityId());
            productSkuPO.setProductSku_Id(productSkuDTO.getProductSkuId());
            productSkuPO.setProductSpecification_Id(productSkuDTO.getProductSpecificationId());
            productSkuPO.setName(productSkuDTO.getName());
            productSkuPO.setCreateTime(new Date());
            productSkuPO.setSaleModel(productSkuDTO.getSaleModel());
            productSkuPO.setCompany_Id(productSkuDTO.getCompany_Id());
            productSkuPO
                .setDistributionPercentForAmount(new BigDecimal(productInfoSpecificationMain.getDistributionPercent()));
            productSkuPO.setOwnerName(productSkuDTO.getOwnerName());
            productSkuPO.setId(UUIDGenerator.getUUID(ProductSkuPO.class.getName()));
            mapper.insertByCity(productSkuPO);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateName(ProductSkuUpdateNameDTO dto) {
        if (dto != null && CollectionUtils.isNotEmpty(dto.getIds()) && StringUtils.isNotBlank(dto.getName())) {
            String name = dto.getName();
            List<Long> skuIds = mapper.selectSkuIdsWithDifferenceName(name, dto.getIds());
            if (CollectionUtils.isNotEmpty(skuIds)) {
                mapper.updateSkuName(name, skuIds);
                logger.info("SKU同步产品信息名称：{} {} => skuIds = {}", dto.getIds(), name, skuIds);
            }
        }
    }

    public Map<Long, Integer> getSequenceByCityAndProductSkuIds(Integer cityId, List<Long> productSkuIds) {
        Map<Long, Integer> map = new HashMap<>(16);
        if (cityId != null && productSkuIds != null && productSkuIds.size() > 0) {
            List<ProductSkuPO> list = mapper.getSequenceByCityAndProductSkuIds(cityId, productSkuIds);
            for (ProductSkuPO po : list) {
                map.put(po.getProductSku_Id(), po.getSequence());
            }
        }
        return map;
    }

    /**
     * 根据城市id,产品名称查询产品列表.
     */
    public PageList<ProductSkuInfoReturnDTO> getProductSkuInfo(ProductSkuInfoSearchDTO dto) {
        return mapper.getProductSkuInfo(dto, dto.getPageNum(), dto.getPageSize()).toPageList();
    }

    /**
     * 根据skuid批量查询配送系数
     *
     * @param productSkuIdList
     * @return
     * @return: Map<skuid, DistributionPercentForAmount>
     */
    public Map<Long, BigDecimal> getProductInfoBySkuList(List<Long> productSkuIdList) {
        List<ProductSkuPO> poList = mapper.getProductSkuInfoBySkuList(productSkuIdList);
        Map<Long, BigDecimal> collect = poList.stream().collect(
            Collectors.toMap(ProductSkuPO::getProductSku_Id, t -> t.getDistributionPercentForAmount(), (s, a) -> s));
        return collect;
    }

    /**
     * 根据时间段查询SKU的数量
     *
     * @param params
     * @return
     */
    public int countProductInPeriod(Map<String, Object> params) {
        return mapper.countProductInPeriod(params);
    }

    /**
     * 判断skuIdList中skuid是否都存在
     *
     * @param productSkuIdList
     * @return
     */
    public Boolean checkProductBySkuIdList(List<Long> productSkuIdList) {
        Boolean bl = true;
        List<ProductSkuPO> poList = mapper.getProductSkuInfoBySkuList(productSkuIdList);
        Map<Long, List<ProductSkuPO>> map =
            poList.stream().collect(Collectors.groupingBy(ProductSkuPO::getProductSku_Id));
        if (map.size() != productSkuIdList.size()) {
            bl = false;
        }
        return bl;
    }

    // /**
    // * 判断规格参数id集合中规格参数id是否都存在
    // *
    // * @param productSpecificationList
    // * @return
    // */
    // public Boolean checkProductBySpecificationIdList(List<Long> productSpecificationList) {
    // Boolean bl = true;
    // List<ProductSkuPO> poList = mapper.getProductSkuInfoBySpecificationList(productSpecificationList);
    // Map<Long, List<ProductSkuPO>> map =
    // poList.stream().collect(Collectors.groupingBy(ProductSkuPO::getProductSpecification_Id));
    // if (map.size() != productSpecificationList.size()) {
    // bl = false;
    // }
    // return bl;
    // }

    /**
     * 查询货主名称
     *
     * @param productSkuList
     * @param source
     * @return
     */
    public List<OwnerInfoDTO> getOwnerInfoBySkuId(List<Long> productSkuList) {
        ArrayList<OwnerInfoDTO> ownerInfoDTOS = new ArrayList<>();
        List<OwnerInfoPO> OwnerInfoPOS = mapper.getOwnerInfoBySkuId(productSkuList);
        // List<Long> ownerIdList = new ArrayList();// 合作商
        // Set<Long> ownerIdShopList = new HashSet<>();// 入驻商
        // for (OwnerInfoPO po : OwnerInfoPOS) {
        // int saleModel = po.getSaleModel().intValue();
        // if (saleModel == 2) { // 合作商
        // ownerIdList.add(po.getCompanyId().longValue());
        // } else if (saleModel == 6) {// 入驻商
        // ownerIdShopList.add(po.getCompanyId().longValue());
        // }
        // }
        // Map<Long, Partner> ownerMap = new HashMap<>(16);
        // Map<Long, ShopDTO> ownerShopMap = new HashMap<>(16);
        // if (CollectionUtils.isNotEmpty(ownerIdList)) {
        // ownerMap = iPartnerService.findPartnerByIds(ownerIdList);
        // }
        // if (CollectionUtils.isNotEmpty(ownerIdShopList)) {
        // ownerShopMap = shopService.getShopMapByIdSet(ownerIdShopList);
        // }
        for (OwnerInfoPO ownerInfoPO : OwnerInfoPOS) {
            OwnerInfoDTO ownerInfoDTO = new OwnerInfoDTO();
            ownerInfoDTO.setProductName(ownerInfoPO.getProductName());
            ownerInfoDTO.setProductSkuId(ownerInfoPO.getProductSkuId());
            ownerInfoDTO.setOwnerName(ownerInfoPO.getOwnerName());
            // int saleModel = ownerInfoPO.getSaleModel().intValue();
            // if (saleModel == 0 || saleModel == 1 || saleModel == 3 || saleModel == 7 || saleModel == 8) { // 酒批
            // ownerInfoDTO.setOwnerName("易酒批");
            // } else if (saleModel == 2) {
            // Partner partner = ownerMap.get(ownerInfoPO.getCompanyId().longValue());
            // ownerInfoDTO.setOwnerName(partner == null ? null : partner.getName());
            // } else if (saleModel == 6) {
            // ShopDTO shopDTO = ownerShopMap.get(ownerInfoPO.getCompanyId().longValue());
            // ownerInfoDTO.setOwnerName(shopDTO == null ? null : shopDTO.getName());
            // }
            ownerInfoDTOS.add(ownerInfoDTO);
        }
        return ownerInfoDTOS;
    }

    public Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySkuId(List<Long> productSkuList) {
        List<ProductSkuPO> list = mapper.getProductInfoBySkuId(productSkuList);
        Map<Long, ProductSkuInfoReturnDTO> map = new HashMap<>(16);
        for (ProductSkuPO po : list) {
            ProductSkuInfoReturnDTO dto = new ProductSkuInfoReturnDTO();
            BeanUtils.copyProperties(po, dto);
            dto.setProductName(po.getName());
            dto.setCityId(po.getCity_Id());
            dto.setSkuId(po.getProductSku_Id());
            dto.setCompanyId(po.getCompany_Id());
            dto.setProductSpecificationId(po.getProductSpecification_Id());
            dto.setProductInfoId(po.getProductInfoId() == null ? null : po.getProductInfoId().longValue());
            map.put(po.getProductSku_Id(), dto);
        }
        return map;
    }

    /**
     * 获取商品特征 (若其中一个产品为大件，则返回大件)
     *
     * @return 1:大件,2:小件
     */
    public Byte getProductFeatureBySkuId(List<Long> productSkuList, Integer warehouseId) {
        List<ProductSkuPO> productSkuPOList = mapper.findBySkuFull(warehouseId, productSkuList);
        if (CollectionUtils.isEmpty(productSkuPOList)) {
            return null;
        }
        // 任意一个为大件，则返回大件
        if (productSkuPOList.stream()
            .anyMatch(p -> Objects.equals(p.getProductFeature(), ProductFeatureEnum.大件.getType()))) {
            return ProductFeatureEnum.大件.getType();
        } else {
            return ProductFeatureEnum.小件.getType();
        }
    }

    /**
     * 根据规格参数id查询产品信息
     *
     * @param productSpecificationList
     */
    public Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySpecificationId(Integer cityId,
        List<ProductSkuBySpecificationQueryDTO> productSpecificationList, Integer source) {
        Map<Long, ProductSkuInfoReturnDTO> map = new HashMap<>(16);
        List<Long> lstOwnerIds =
            productSpecificationList.stream().map(p -> p.getOwnerId()).distinct().collect(Collectors.toList());
        lstOwnerIds.forEach(ownerId -> {
            List<Long> lstSpecificationIds =
                productSpecificationList.stream().filter(p -> Objects.equals(p.getOwnerId(), ownerId))
                    .map(p -> p.getProductSpecificationId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstSpecificationIds)) {
                Map<Long, ProductSkuInfoReturnDTO> productInfoBySpecificationId =
                    getProductInfoBySpecificationId(cityId, lstSpecificationIds, ownerId, source);
                map.putAll(productInfoBySpecificationId);
            }
        });
        return map;
    }

    /**
     * 根据规格参数id查询产品信息
     *
     * @param productSpecificationList
     */
    private Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySpecificationId(Integer cityId,
        List<Long> productSpecificationList, Long ownerId, Integer source) {
        List<ProductSkuPO> list =
            mapper.getProductInfoBySpecificationId(cityId, productSpecificationList, ownerId, source);
        Map<Long, ProductSkuInfoReturnDTO> map = new HashMap<>(16);
        for (ProductSkuPO po : list) {
            ProductSkuInfoReturnDTO dto = new ProductSkuInfoReturnDTO();
            BeanUtils.copyProperties(po, dto);
            dto.setProductName(po.getName());
            dto.setCityId(po.getCity_Id());
            dto.setCompanyId(po.getCompany_Id());
            dto.setProductSpecificationId(po.getProductSpecification_Id());
            dto.setSkuId(po.getProductSku_Id());
            dto.setSaleModel(po.getSaleModel() != null ? po.getSaleModel().intValue() : null);
            if (dto.getSaleModel() != null) {
                dto.setProductTypeName(StringUtils.isEmpty(ProductSaleMode.getEnumName(dto.getSaleModel())) ? ""
                    : ProductSaleMode.getEnumName(dto.getSaleModel()));
            }

            if (map != null && map.size() > 0 && map.containsKey(po.getProductSpecification_Id())
                && Objects.equals(po.getIsDelete(), YesOrNoEnum.YES.getValue().byteValue())) {
                continue;
            }
            map.put(po.getProductSpecification_Id(), dto);
        }
        return map;
    }

    /**
     * 获取商品详情
     *
     * @param productSkuInfoSO
     * @return
     */
    public PageList<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO) {
        AssertUtils.notNull(productSkuInfoSO, "参数不能为空");
        // AssertUtils.notNull(productSkuInfoSO.getCityId(), "城市Id不能为空");
        // 按条码查询
        if (StringUtils.isNotEmpty(productSkuInfoSO.getProductCode())) {
            // 通过瓶码或者箱码查询sku列表
            List<Long> skuIdList =
                getProductSkuIdByCode(productSkuInfoSO.getProductCode(), productSkuInfoSO.getCityId());
            if (CollectionUtils.isEmpty(skuIdList)) {
                skuIdList.add(Long.valueOf(0));
            }
            productSkuInfoSO.setProductSkuIdList(skuIdList);
        }
        // 是否限制产品范围
        if (Objects.equals(productSkuInfoSO.getLimitSku(), ConditionStateEnum.是.getType())) {
            AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库Id不能为空");
        }

        // logger.info("获取商品详情-查询参数：{}", JSON.toJSONString(productSkuInfoSO));
        if (productSkuInfoSO.getPageNum() == null) {
            productSkuInfoSO.setPageNum(1);
        }
        if (productSkuInfoSO.getPageSize() == null) {
            productSkuInfoSO.setPageSize(Integer.MAX_VALUE);
        }

        boolean isFirstPage = Objects.equals(productSkuInfoSO.getPageNum(), 1);
        PageHelper.startPage(productSkuInfoSO.getPageNum(), productSkuInfoSO.getPageSize(), isFirstPage);
        PageResult<ProductSkuInfoDTO> pageResult = mapper.listProductSkuInfo(productSkuInfoSO);

        // 2025-02-08 只有首页获取总条数，其他页不获取
        if (isFirstPage) {
            Long count = mapper.listProductSkuInfoCount(productSkuInfoSO);
            pageResult.setTotal(count);
        }

        // 仓库ID
        if (CollectionUtils.isNotEmpty(pageResult) && productSkuInfoSO.getWarehouseId() != null) {
            // 仓库ID 不为空则回填库龄管控标识
            List<Long> skuIdList = pageResult.stream().filter(Objects::nonNull).map(ProductSkuInfoDTO::getProductSkuId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                // // 查询产品是否为库龄管控管控产品
                Map<Long, Boolean> productStockAgeControls = stockAgeStrategyBL.isRefProductStockAgeControls(
                    productSkuInfoSO.getCityId(), productSkuInfoSO.getWarehouseId(), skuIdList);
                for (ProductSkuInfoDTO skuInfoDTO : pageResult) {
                    Boolean ageControl = productStockAgeControls.get(skuInfoDTO.getProductSkuId());
                    skuInfoDTO.setStockAgeControlProduct(ageControl == null ? Boolean.FALSE : ageControl);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(pageResult)) {
            pageResult.forEach(p -> {
                p.setProductStateText(ProductPutawayStateEnum.getEnmuName(p.getProductState()));
                if (Objects.nonNull(p.getSaleModel())) {
                    p.setSaleModelText(com.alibaba.dubbo.common.utils.StringUtils
                        .isEmpty(ProductSaleMode.getEnumName(p.getSaleModel().intValue())) ? ""
                            : ProductSaleMode.getEnumName(p.getSaleModel().intValue()));
                }
            });
        }

        return pageResult.toPageList();
    }

    /**
     * 获取商品详情（包含库存数）
     *
     * @param productSkuInfoSO
     * @return
     */
    public PageList<ProductSkuInfoDTO> listProductSkuInfoIncludeStore(ProductSkuInfoSO productSkuInfoSO) {
        logger.info("获取商品详情（包含库存数）查询参数：{}", JSON.toJSONString(productSkuInfoSO));
        AssertUtils.notNull(productSkuInfoSO, "参数不能为空");
        AssertUtils.notNull(productSkuInfoSO.getCityId(), "城市Id不能为空");
        AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库Id不能为空");
        // 判断 key 类型
        String key = productSkuInfoSO.getKey();
        // 备份查询参数原始 skuId 集合
        List<Long> origSkuIdList = productSkuInfoSO.getProductSkuIdList() == null ? null
            : Lists.newArrayList(productSkuInfoSO.getProductSkuIdList());
        PageResult<ProductSkuInfoDTO> pageResult = new PageResult<>();
        boolean goSelect = true;
        if (StringUtils.isNotBlank(key)) {
            // 1、如果查询内容全部为数字，则表示按条码查询
            if (key.matches("^[0-9]+$")) {
                logger.info("按条码查询产品 key : {}", key);
                goSelect = false;
                OrgDTO orgDTO = iOrgService.getOrg(productSkuInfoSO.getCityId());
                if (null == orgDTO) {
                    throw new BusinessException("找不到仓库");
                }
                // sku 信息
                List<Long> skuIds = new ArrayList<>();
                if (Objects.equals(OrgConstant.ORG_TYPE_JIUPI, orgDTO.getFromOrgType())) {
                    List<Long> codeSkuList = getProductSkuIdByCode(key, productSkuInfoSO.getCityId());
                    skuIds.addAll(codeSkuList);
                } else {
                    skuIds.add(Long.valueOf(key));
                }
                logger.info("按条码查询产品 key : {} 区域类型：{} 产品skuId集合: {}", key, orgDTO.getFromOrgType(),
                    JSON.toJSONString(skuIds));
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    // 加上原有sku信息
                    if (origSkuIdList != null) {
                        skuIds.addAll(origSkuIdList);
                    }
                    productSkuInfoSO.setProductSkuIdList(
                        skuIds.stream().filter(e -> e != null).distinct().collect(Collectors.toList()));
                    // 根据条码查询不分页
                    productSkuInfoSO.setPageNum(1);
                    productSkuInfoSO.setPageSize(Integer.MAX_VALUE);
                    // 产品关联关系
                    // Map<Long, List<ProductSkuDTO>> groupProductList =
                    // productRelationGroupQueryBL.findSameGroupProductBySkuIds(
                    // productSkuInfoSO.getWarehouseId(),
                    // productSkuInfoSO.getProductSkuIdList()
                    // );
                    // 分组产品
                    // Map<Long, List<ProductSkuInfoDTO>> hasGroupProducts =
                    // getGroupProductInfo(productSkuInfoSO.getProductSkuIdList(), groupProductList);
                    logger.info("按条码查询产品 key : {} 产品信息参数: {}", key, JSON.toJSONString(productSkuInfoSO));
                    pageResult = listProductSkuInfoIncludeStorePageResult(productSkuInfoSO);

                    // if (CollectionUtils.isNotEmpty(pageResult)) {
                    // pageResult.stream().filter(e -> e != null )
                    // .forEach( e -> {
                    // List<ProductSkuInfoDTO> groupProducts = hasGroupProducts.get(e.getProductSkuId());
                    // if (CollectionUtils.isNotEmpty(groupProducts)) {
                    // e.setProductRelationGroupId(groupProducts.get(0).getProductRelationGroupId());
                    // e.setGroupProducts(groupProducts);
                    // }
                    // });
                    // }
                }
            }

            // 2、如果上述没找到，且查询内容只包含字母、数字、或-，则按货位名称查询
            if (goSelect && CollectionUtils.isEmpty(pageResult.toPageList().getDataList())
                && key.matches("^[a-zA-Z\\d]+[-a-zA-Z\\d]*$")) {
                logger.info("按货位查询产品 key : {}", key);
                // 清除之前添加sku信息
                LocationProductQuery query = new LocationProductQuery();
                query.setCityId(productSkuInfoSO.getCityId());
                query.setWarehouseId(productSkuInfoSO.getWarehouseId());
                query.setLocationName(key);
                List<ProductSkuPO> locationProductSkuList = productSkuQueryBL.findLocationProductSkuList(query);
                List<Long> locationSkuList =
                    locationProductSkuList.stream().filter(e -> e != null && e.getProductSku_Id() != null)
                        .map(e -> e.getProductSku_Id()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(locationSkuList)) {
                    if (origSkuIdList != null) {
                        locationSkuList.addAll(origSkuIdList);
                    }
                    productSkuInfoSO.setProductSkuIdList(locationSkuList);
                    logger.info("按货位查询产品 key : {} 产品信息参数: {}", key, JSON.toJSONString(productSkuInfoSO));
                    pageResult = listProductSkuInfoIncludeStorePageResult(productSkuInfoSO);
                }
            }
            // 3、如果上述条件没找到，则按产品名称查询
            if (goSelect && CollectionUtils.isEmpty(pageResult.toPageList().getDataList())) {
                logger.info("按产品名称查询产品 key : {}", key);
                productSkuInfoSO.setProductName(key);
                // 还原传入sku信息
                productSkuInfoSO.setProductSkuIdList(origSkuIdList);
                pageResult = listProductSkuInfoIncludeStorePageResult(productSkuInfoSO);
            }
        } else {
            pageResult = listProductSkuInfoIncludeStorePageResult(productSkuInfoSO);
        }
        // List<ProductSkuInfoDTO> dataList = pageResult.toPageList().getDataList();
        // if (CollectionUtils.isNotEmpty(dataList) &&
        // ObjectUtils.defaultIfNull(productSkuInfoSO.getForceCheckRefProductStore(), false)) {
        // List<Long> querySkuIds = dataList.stream().filter(e -> e != null).map(e ->
        // e.getProductSkuId()).distinct().collect(Collectors.toList());
        // // 判断产品是否有多个关联产品，且是否关联产品是否都有库存,多出存的产品需要标记
        // Map<Long, Boolean> replaceMap =
        // productRelevanceConfigBL.filterSkuByRefProductStore(productSkuInfoSO.getWarehouseId(),
        // productSkuInfoSO.getCityId(), querySkuIds);
        // if (replaceMap != null && !replaceMap.isEmpty()) {
        // // 不为空则进行替换处理
        // List<Long> newSkuIdList = Lists.newArrayList(replaceMap.keySet()) ;
        // ProductSkuInfoSO newSo = new ProductSkuInfoSO();
        // newSo.setWarehouseId(productSkuInfoSO.getWarehouseId());
        // newSo.setProductSkuIdList(newSkuIdList);
        // // 变更结果集
        // pageResult = mapper.listProductSkuInfoIncludeStore(newSo);
        // // 给产品打标记
        // filterProductSkuInfoByRefProductStoreLimit(pageResult.toPageList().getDataList(), replaceMap);
        // }
        // }
        PageList<ProductSkuInfoDTO> pageList = pageResult.toPageList();
        // 查询库存
        if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
            // 批量查询库存
            List<Long> skuIdList =
                pageList.getDataList().stream().map(p -> p.getProductSkuId()).collect(Collectors.toList());
            Map<Long, ProductStoreDTO> productStoreMap = iWarehouseInventoryQueryService
                .listProductInventoryBySkuIds(skuIdList, productSkuInfoSO.getWarehouseId());

            // 获取产品关联的合并产品的库存总和
            // Map<Long, BigDecimal> refInventory =
            // iWarehouseInventoryQueryService.getRefProductInventoryMap(productSkuInfoSO.getCityId(),
            // productSkuInfoSO.getWarehouseId(), skuIdList);
            Map<Long, BigDecimal> refInventory = Collections.emptyMap();

            // 查询产品是否为库龄管控管控产品
            Map<Long, Boolean> productStockAgeControls = stockAgeStrategyBL.isRefProductStockAgeControls(
                productSkuInfoSO.getCityId(), productSkuInfoSO.getWarehouseId(), skuIdList);

            pageList.getDataList().forEach(dto -> {
                BigDecimal totalCount = productStoreMap.get(dto.getProductSkuId()) == null ? BigDecimal.ZERO
                    : productStoreMap.get(dto.getProductSkuId()).getUnitTotolCount();
                // 若开启了合并产品盘点，则需要把关联的产品库存合并起来
                if (refInventory != null && refInventory.size() > 0
                    && refInventory.containsKey(dto.getProductSkuId())) {
                    totalCount = totalCount.add(refInventory.get(dto.getProductSkuId()));
                }
                // 设置库龄管控标识
                Boolean ageControl = productStockAgeControls.get(dto.getProductSkuId());
                dto.setStockAgeControlProduct(ageControl == null ? false : ageControl);
                dto.setTotalCount(totalCount);
            });
        }
        return pageList;
    }

    /**
     * 分页查询优化
     * 
     * @param productSkuInfoSO
     * @return
     */
    private PageResult<ProductSkuInfoDTO> listProductSkuInfoIncludeStorePageResult(ProductSkuInfoSO productSkuInfoSO) {
        if (productSkuInfoSO.getPageNum() == null) {
            productSkuInfoSO.setPageNum(1);
        }
        if (productSkuInfoSO.getPageSize() == null) {
            productSkuInfoSO.setPageSize(Integer.MAX_VALUE);
        }
        PageHelper.startPage(productSkuInfoSO.getPageNum(), productSkuInfoSO.getPageSize(),
            Objects.equals(productSkuInfoSO.getPageNum(), 1));
        PageResult<ProductSkuInfoDTO> pageResult = mapper.listProductSkuInfoIncludeStore(productSkuInfoSO);

        return pageResult;
    }

    private void filterProductSkuInfoByRefProductStoreLimit(List<ProductSkuInfoDTO> dataList,
        Map<Long, Boolean> replaceMap) {
        if (CollectionUtils.isEmpty(dataList) || replaceMap.isEmpty()) {
            return;
        }
        dataList.stream().filter(e -> e != null).forEach(e -> {
            Boolean hasMoreStore = replaceMap.get(e.getProductSkuId());
            if (hasMoreStore == null) {
                return;
            }
            // 多库存重复标记设置
            e.setHasManyStore(hasMoreStore);
        });
    }

    /**
     * 根据sku组装其关联产品
     */
    private Map<Long, List<ProductSkuInfoDTO>> getGroupProductInfo(List<Long> skuIdList,
        Map<Long, List<ProductSkuDTO>> groupProductList) {
        if (CollectionUtils.isEmpty(skuIdList) || groupProductList == null || groupProductList.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, List<ProductSkuInfoDTO>> result = new HashMap<>(16);
        for (Map.Entry<Long, List<ProductSkuDTO>> entry : groupProductList.entrySet()) {
            if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            if (skuIdList.contains(entry.getKey())) {
                List<Long> refSkuIds = entry.getValue().stream().filter(e -> e != null).map(e -> e.getProductSkuId())
                    .distinct().collect(Collectors.toList());
                // 移除
                skuIdList.removeAll(refSkuIds);
                // 转化对象
                List<ProductSkuInfoDTO> skuInfoList = buildSkuInfoByProductSku(entry.getValue());
                if (CollectionUtils.isNotEmpty(skuInfoList)) {
                    result.put(entry.getKey(), skuInfoList);
                }
            }
        }
        return result;
    }

    private List<ProductSkuInfoDTO> buildSkuInfoByProductSku(List<ProductSkuDTO> skuDTOList) {
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return null;
        }
        return skuDTOList.stream().filter(e -> e != null).map(e -> {
            ProductSkuInfoDTO skuInfoDTO = new ProductSkuInfoDTO();
            // id
            skuInfoDTO.setId(e.getProductSkuId());
            // 产品skuId
            skuInfoDTO.setProductSkuId(e.getProductSkuId());
            // 规格ID
            skuInfoDTO.setProductSpecificationId(e.getProductSpecificationId());
            // 产品名称
            skuInfoDTO.setProductName(e.getName());
            // 包装规格名称
            skuInfoDTO.setSpecName(e.getSpecificationName());
            // 包装规格大小单位转换系数
            skuInfoDTO.setSpecQuantity(e.getPackageQuantity());
            // 包装规格大单位
            skuInfoDTO.setPackageName(e.getPackageName());
            // 包装规格小单位
            skuInfoDTO.setUnitName(e.getUnitName());
            // 产品品牌
            skuInfoDTO.setProductBrand(e.getProductBrand());
            // 产品状态
            skuInfoDTO.setProductState(
                e.getProductState() == null ? ProductPutawayStateEnum.下架.getType() : e.getProductState().byteValue());
            // 货主名称
            skuInfoDTO.setOwnerName(e.getOwnerName());
            // 类目
            skuInfoDTO.setStatisticsClassName(e.getStatisticsClass());
            // 保质期
            skuInfoDTO.setMonthOfShelfLife((e.getMonthOfShelfLife()));
            // 保质期单位 1：年（365） 2：月（30） 3：日（1）
            skuInfoDTO.setShelfLifeUnit(e.getShelfLifeUnit() == null ? null : e.getShelfLifeUnit().byteValue());
            // 产品来源
            skuInfoDTO.setSource(e.getSource());
            // 货主ID
            skuInfoDTO.setCompanyId(e.getCompany_Id());
            // 产品关联分组ID
            skuInfoDTO.setProductRelationGroupId(e.getProductRelationGroupId());
            return skuInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据产品skuId查询产品信息
     */
    public List<ProductSkuInfoDTO> listProductSkuInfoBySkuId(Integer cityId, List<Long> productSkuIds) {
        return mapper.listProductSkuInfoBySkuId(cityId, productSkuIds);
    }

    /**
     * 根据产品skuId查询产品信息
     */
    public List<ProductSkuInfoDTO> findProductSkuInfo(Integer cityId, List<Long> productSkuIds) {
        return mapper.findProductSkuInfo(cityId, productSkuIds);
    }

    /**
     * 查询城市所有SKU数量
     *
     * @return
     */
    public int countProductByCity(Integer cityId) {
        return mapper.countProductByCity(cityId);
    }

    /**
     * 根据产品skuId查询产品生产日期详情
     *
     * @param cityId
     * @param warehouseId
     * @param productSkuIds
     * @return
     */
    public List<ProductionDateDTO> listProductionDate(Integer cityId, Integer warehouseId, List<Long> productSkuIds) {
        SkuInventoryAndProductionDateQO qo = new SkuInventoryAndProductionDateQO();
        qo.setCityId(cityId);
        qo.setWarehouseId(warehouseId);
        qo.setSkuIds(productSkuIds);
        List<ProductionDateDTO> productionDateDTOList = mapper.listProductionDate(qo);
        // logger.info("产品生产日期详情：{}", JSON.toJSONString(productionDateDTOList));
        return productionDateDTOList;
    }

    /**
     * 获取箱码和瓶码集合
     *
     * @param skuIdSet
     * @return
     */
    public Map<Long, ProductCodeDTO> getPackageAndUnitCode(Set<Long> skuIdSet, Integer cityId) {
        AssertUtils.notEmpty(skuIdSet, "skuId不能为空");
        // if (skuIdSet.size() > 100) {
        // throw new BusinessException("sku个数不能超过100");
        // }
        Map<Long, ProductCodeDTO> codeMap = new HashMap<>(16);

        // 根据skuId查询产品来源
        List<ProductSkuPO> productSkuPOList = mapper.getProductSkuInfoBySkuList(new ArrayList(skuIdSet));
        if (CollectionUtils.isNotEmpty(productSkuPOList)) {

            // 1、酒批的产品，调交易接口获取箱码和瓶码
            List<ProductSkuPO> yjpProductSkuList = productSkuPOList.stream().filter(
                p -> p.getSource() != null && p.getProductSku_Id() != null && ProductSourceType.易酒批 == p.getSource())
                .collect(Collectors.toList());
            setPackageAndUnitCodeByOP(codeMap, yjpProductSkuList, cityId);

            // 2、没有获取到的产品，调wms接口获取箱码和瓶码
            List<Long> otherProductSkuIds = productSkuPOList.stream().filter(p -> p.getProductSku_Id() != null)
                .map(p -> p.getProductSku_Id()).distinct().collect(Collectors.toList());
            // 2021-05-28 移除已经获取到条码的SKU，避免酒批数据影响交易数据
            codeMap.forEach((sku, code) -> {
                if (code == null) {
                    return;
                }
                if (CollectionUtils.isNotEmpty(code.getPackageCodeList())
                    && CollectionUtils.isNotEmpty(code.getUnitCodeList())) {
                    otherProductSkuIds.remove(sku);
                }
            });
            if (CollectionUtils.isNotEmpty(otherProductSkuIds)) {
                setPackageAndUnitCodeByWine(codeMap, otherProductSkuIds, cityId);
            }
        }
        // 箱码和瓶码去重
        if (codeMap != null) {
            codeMap.forEach((skuId, code) -> {
                if (code == null) {
                    return;
                }
                if (CollectionUtils.isNotEmpty(code.getPackageCode())) {
                    code.setPackageCode(code.getPackageCode().stream().distinct().collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(code.getUnitCode())) {
                    code.setUnitCode(code.getUnitCode().stream().distinct().collect(Collectors.toList()));
                }
            });
        }
        return codeMap;
    }

    /**
     * 每次50个
     *
     * @param list
     * @param len
     * @return
     */
    public static <E> List<Set<E>> splitList(Set<E> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<Set<E>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        List<E> lstAll = new ArrayList<>(list);
        for (int i = 0; i < count; i++) {
            HashSet subList = new HashSet(lstAll.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1))));
            result.add(subList);
        }
        return result;
    }

    /**
     * 调用交易的接口获取箱码和瓶码
     */
    private void setPackageAndUnitCodeByOP(Map<Long, ProductCodeDTO> codeMap, List<ProductSkuPO> yjpProductSkuList,
        Integer cityId) {
        try {
            if (CollectionUtils.isEmpty(yjpProductSkuList)) {
                return;
            }
            Set<Integer> sepcIdSet = yjpProductSkuList.stream()
                .filter(
                    p -> p.getProductSpecification_Id() != null && p.getProductSpecification_Id() <= Integer.MAX_VALUE)
                .map(p -> p.getProductSpecification_Id().intValue()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(sepcIdSet)) {
                // 拿到瓶码箱码信息
                Map<Integer, ScmProductCodeListDTO> productCodeListMap = scmProductCache.get(sepcIdSet);
                if (null != productCodeListMap && !productCodeListMap.isEmpty()) {
                    productCodeListMap.forEach((specId, productCodeListDTO) -> {
                        if (null != productCodeListDTO) {
                            // 根据规格id匹配对应的skuId
                            List<ProductSkuPO> filterList = yjpProductSkuList.stream()
                                .filter(p -> p.getProductSpecification_Id() != null
                                    && Objects.equals(p.getProductSpecification_Id().intValue(), specId))
                                .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(filterList)) {
                                ProductCodeDTO productCodeDTO = getProductCodeDTO(productCodeListDTO, cityId);
                                filterList.forEach(skuPO -> {
                                    codeMap.put(skuPO.getProductSku_Id(), productCodeDTO);
                                });
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            logger.error("获取交易瓶码箱码失败", e);
        }
    }

    /**
     * 获取酒批的瓶码和箱码
     */
    public ProductCodeDTO getProductCodeDTO(ScmProductCodeListDTO productCodeListDTO, Integer cityId) {
        // 组装条码和箱码
        ProductCodeDTO productCodeDTO = new ProductCodeDTO();
        // 箱码
        if (CollectionUtils.isNotEmpty(productCodeListDTO.getBoxCodeList())) {
            // 找出启用的箱码
            List<ScmProductCodeDTO> boxCodeList = productCodeListDTO.getBoxCodeList().stream()
                .filter(p -> p != null && Objects.equals(p.getState(), 1)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(boxCodeList)) {
                // 优先找当前城市下的箱码，若当前城市没有箱码，则取所有的箱码
                boolean isHasCityCode = cityId != null && boxCodeList.stream()
                    .anyMatch(p -> Objects.equals(p.getOwnerType(), ProductInfoBoxCodeOwnerType.CITY)
                        && Objects.equals(p.getOwnerId(), cityId.toString()));
                if (isHasCityCode) {
                    boxCodeList = boxCodeList.stream()
                        .filter(p -> Objects.equals(p.getOwnerType(), ProductInfoBoxCodeOwnerType.CITY)
                            && Objects.equals(p.getOwnerId(), cityId.toString()))
                        .collect(Collectors.toList());
                    logger.info("当前城市{}的箱码：{}", cityId, JSON.toJSONString(boxCodeList));
                }

                List<CodeDTO> packageCodeList = boxCodeList.stream().map(p -> {
                    CodeDTO codeDTO = new CodeDTO();
                    codeDTO.setId(p.getId());
                    codeDTO.setCode(p.getCode());
                    codeDTO.setState(p.getState());
                    return codeDTO;
                }).collect(Collectors.toList());
                productCodeDTO.setPackageCodeList(packageCodeList);

                if (CollectionUtils.isNotEmpty(productCodeDTO.getPackageCodeList())) {
                    productCodeDTO.setPackageCode(productCodeDTO.getPackageCodeList().stream().map(p -> p.getCode())
                        .distinct().collect(Collectors.toList()));
                }
            }

        }
        List<CodeDTO> lstBottleCode = new ArrayList<>();
        // 条码久批
        if (CollectionUtils.isNotEmpty(productCodeListDTO.getJiupiBottleCodeList())) {
            List<CodeDTO> unitCodeList = productCodeListDTO.getJiupiBottleCodeList().stream()
                .filter(p -> p != null && Objects.equals(p.getState(), 1)).map(p -> {
                    CodeDTO codeDTO = new CodeDTO();
                    codeDTO.setId(p.getId());
                    codeDTO.setCode(p.getCode());
                    codeDTO.setState(p.getState());
                    return codeDTO;
                }).collect(Collectors.toList());
            lstBottleCode.addAll(unitCodeList);
        }
        // 条码连锁
        if (CollectionUtils.isNotEmpty(productCodeListDTO.getRetailBottleCodeList())) {
            List<CodeDTO> unitCodeList = productCodeListDTO.getRetailBottleCodeList().stream()
                .filter(p -> p != null && Objects.equals(p.getState(), 1)).map(p -> {
                    CodeDTO codeDTO = new CodeDTO();
                    codeDTO.setId(p.getId());
                    codeDTO.setCode(p.getCode());
                    codeDTO.setState(p.getState());
                    return codeDTO;
                }).collect(Collectors.toList());
            lstBottleCode.addAll(unitCodeList);
        }

        productCodeDTO.setUnitCodeList(lstBottleCode);
        if (CollectionUtils.isNotEmpty(productCodeDTO.getUnitCodeList())) {
            productCodeDTO.setUnitCode(productCodeDTO.getUnitCodeList().stream().map(p -> p.getCode()).distinct()
                .collect(Collectors.toList()));
        }
        return productCodeDTO;
    }

    /**
     * 获取WMS的瓶码和箱码
     */
    private void setPackageAndUnitCodeByWine(Map<Long, ProductCodeDTO> codeMap, List<Long> skuIds, Integer cityId) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                return;
            }
            Map<Long, ProductCodeDTO> productCodeMap =
                iProductCodeInfoQueryService.findEnableProductCodeBySkuIds(cityId, skuIds);
            if (productCodeMap != null) {
                logger.info("WMS条码: {}", JSON.toJSONString(productCodeMap));
                productCodeMap.forEach((skuId, code) -> {
                    if (code == null) {
                        return;
                    }
                    ProductCodeDTO exitCode = codeMap.get(skuId);
                    if (exitCode == null) {
                        exitCode = new ProductCodeDTO();
                        codeMap.put(skuId, exitCode);
                    }
                    // wms有箱码
                    if (CollectionUtils.isNotEmpty(code.getPackageCode())) {
                        if (CollectionUtils.isNotEmpty(exitCode.getPackageCode())) {
                            exitCode.getPackageCode().addAll(code.getPackageCode());
                        } else {
                            exitCode.setPackageCode(code.getPackageCode());
                        }

                        List<CodeDTO> codeDTOS = code.getPackageCode().stream().map(p -> {
                            CodeDTO codeDTO = new CodeDTO();
                            codeDTO.setCode(p);
                            return codeDTO;
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(exitCode.getPackageCodeList())) {
                            exitCode.getPackageCodeList().addAll(codeDTOS);
                        } else {
                            exitCode.setPackageCodeList(codeDTOS);
                        }
                    }
                    // wms有箱码
                    if (CollectionUtils.isNotEmpty(code.getUnitCode())) {
                        if (CollectionUtils.isNotEmpty(exitCode.getUnitCode())) {
                            exitCode.getUnitCode().addAll(code.getUnitCode());
                        } else {
                            exitCode.setUnitCode(code.getUnitCode());
                        }

                        List<CodeDTO> codeDTOS = code.getUnitCode().stream().map(p -> {
                            CodeDTO codeDTO = new CodeDTO();
                            codeDTO.setCode(p);
                            return codeDTO;
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(exitCode.getUnitCodeList())) {
                            exitCode.getUnitCodeList().addAll(codeDTOS);
                        } else {
                            exitCode.setUnitCodeList(codeDTOS);
                        }
                    }
                });
            }
        } catch (Exception e) {
            logger.error("获取WMS瓶码箱码失败", e);
        }
    }

    public Map<Long, ProductSkuAndCategoryDTO> getProductSkuAndCategoryInfoBySkuId(List<Long> productSkuList) {
        AssertUtils.notEmpty(productSkuList, "产品SKUID不能为空！");
        // 去重
        productSkuList = productSkuList.stream().filter(d -> d != null).distinct().collect(Collectors.toList());
        // 查询SKU信息
        Map<Long, ProductSkuInfoReturnDTO> productSkuInfoMap = getProductInfoBySkuId(productSkuList);
        // 产品类目
        // List<ProductCategoryDTO> categoryDTOList = productCategoryBL.findCategoryBySkuIds(productSkuList);
        ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO = new ProductInfoCategoryQueryDTO();
        productInfoCategoryQueryDTO.setSkuIds(productSkuList);
        List<ProductInfoCategoryDTO> productInfoCategoryDTOS =
            productInfoCategoryService.findProductCategoryBySkuIds(productInfoCategoryQueryDTO);
        List<ProductCategoryDTO> categoryDTOList =
            ProductCategoryConvertor.productInfoCategoryDTOS2ProductCategoryDTOS(productInfoCategoryDTOS);
        Map<Long, ProductCategoryDTO> categoryDTOMap = new HashMap<>(16);
        categoryDTOList.forEach(p -> categoryDTOMap.put(p.getProductSkuId(), p));
        Map<Long, ProductSkuAndCategoryDTO> result = new HashMap<>(16);
        productSkuList.forEach(d -> {
            ProductSkuAndCategoryDTO dto = new ProductSkuAndCategoryDTO();
            dto.setProductSkuId(d);
            dto.setProductSkuInfoDTO(productSkuInfoMap.get(d));
            dto.setProductCategoryDTO(categoryDTOMap.get(d));
            result.put(d, dto);
        });
        return result;
    }

    /**
     * 根据产品skuId查询产品信息
     *
     * @return
     */
    public ProductSkuInfoReturnDTO findProductSkuInfoBySkuId(Long productSkuId) {
        ProductSkuPO po = mapper.findProductSkuInfoBySkuId(productSkuId);
        if (po == null) {
            return null;
        }
        ProductSkuInfoReturnDTO dto = new ProductSkuInfoReturnDTO();
        BeanUtils.copyProperties(po, dto);
        dto.setProductName(po.getName());
        dto.setCityId(po.getCity_Id());
        dto.setSkuId(po.getProductSku_Id());
        dto.setCompanyId(po.getCompany_Id());
        dto.setProductSpecificationId(po.getProductSpecification_Id());
        dto.setProductInfoId(po.getProductInfoId() == null ? null : po.getProductInfoId().longValue());
        return dto;
    }

    /**
     * 通过瓶码或者箱码查询sku列表
     * 
     * @param code 条码
     * @param cityId
     * @return
     */
    public List<Long> getProductSkuIdByCode(String code, Integer cityId) {
        if (cityId == null || StringUtils.isEmpty(code)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> skuIdList = new ArrayList<>();
        boolean isJiuPiCode = false;

        // 1、酒批
        try {
            Set<Integer> specIdSet = scmProductInfoQueryService.queryProductInfoSpecByCode(code);
            logger.info("【酒批】按条码：{}查询规格：{}", code, JSON.toJSONString(specIdSet));
            if (CollectionUtils.isNotEmpty(specIdSet)) {
                isJiuPiCode = true;
                // 根据规格id查询skuid
                List<Long> specIds = specIdSet.stream().map(p -> p.longValue()).collect(Collectors.toList());
                List<Long> skuIds = mapper.getSkuIdByCityIdAndSpecId(cityId, specIds, ProductSourceType.易酒批);
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    skuIdList.addAll(skuIds);
                    logger.info("【酒批】按条码查询sku：{}", JSON.toJSONString(skuIds));
                }
            }
        } catch (Exception e) {
            logger.info("【酒批】按条码查询sku异常", e);
        }

        // 久批条码查到的，不继续查WMS
        if (!isJiuPiCode) {
            // 3、其他
            try {
                List skuIdListByOther = iProductCodeInfoQueryService.getProductSkuIdByCode(code, cityId);
                if (CollectionUtils.isNotEmpty(skuIdListByOther)) {
                    skuIdList.addAll(skuIdListByOther);
                    logger.info("【WMS】按条码查询sku：{}", JSON.toJSONString(skuIdListByOther));
                }
            } catch (Exception e) {
                logger.info("【WMS】按条码查询sku异常", e);
            }
        }
        return skuIdList;
    }

    /**
     * 获取商品特征
     *
     * @param productSkuList
     * @return
     */
    public List<ProductSkuDTO> getProductCharacteristic(Set<Long> productSkuList, Integer warehouseId) {
        List<ProductSkuDTO> productSkuDTOList = new ArrayList<>();
        List<Long> collect = productSkuList.stream().collect(Collectors.toList());
        List<List<Long>> partition = Lists.partition(collect, batchSize);
        List<ProductSkuPO> productSkuPOList = new ArrayList<>();
        for (List<Long> longList : partition) {
            List<ProductSkuPO> productCharacteristicBySkuIds =
                mapper.getProductCharacteristicBySkuIds(longList, warehouseId);
            if (!org.springframework.util.CollectionUtils.isEmpty(productCharacteristicBySkuIds)) {
                productSkuPOList.addAll(productCharacteristicBySkuIds);
            }
        }
        for (ProductSkuPO productSkuPO : productSkuPOList) {
            ProductSkuDTO productSkuDTO = new ProductSkuDTO();
            productSkuDTO.setProductFeature(productSkuPO.getProductFeature());
            productSkuDTO.setProductSkuId(productSkuPO.getProductSku_Id());
            productSkuDTOList.add(productSkuDTO);
        }
        return productSkuDTOList;
    }

    /**
     * 查询sku条码信息
     * 
     * @param skuDTOList
     * @return
     */
    public List<ProductSkuDTO> listSkuCode(List<ProductSkuDTO> skuDTOList) {
        List<ProductSkuDTO> returnSkuList = new ArrayList<>();
        List<List<ProductSkuDTO>> partition = Lists.partition(skuDTOList, batchSize);
        for (List<ProductSkuDTO> productSkuDTOS : partition) {
            if (CollectionUtils.isEmpty(productSkuDTOS)) {
                continue;
            }
            List<Long> skuList = productSkuDTOS.stream().filter(s -> !ObjectUtils.isEmpty(s.getProductSkuId()))
                .map(ProductSkuDTO::getProductSkuId).collect(Collectors.toList());
            List<ProductSkuDTO> SkuCodeList = mapper.listSkuBottleCode(skuList, productSkuDTOS.get(0).getCityId());
            if (!org.springframework.util.CollectionUtils.isEmpty(SkuCodeList)) {
                returnSkuList.addAll(SkuCodeList);
            }
        }
        return returnSkuList;
    }

    /**
     * 根据SKUID获取商品条码
     *
     * @param productSkuInfoSO
     * @return
     */
    public List<ProductInfoCodeDTO> getProductInfoCodes(ProductSkuInfoSO productSkuInfoSO) {
        logger.info("getProductInfoCodes 根据SKUID获取商品条码 入参：{}", JSON.toJSONString(productSkuInfoSO));
        AssertUtils.notNull(productSkuInfoSO, "查询参数不能为空");
        AssertUtils.notNull(productSkuInfoSO.getCityId(), "城市id不能为空");
        AssertUtils.notEmpty(productSkuInfoSO.getProductSkuIdList(), "skuIds不能为空");

        Set<Long> skuIdSet = new HashSet<>(productSkuInfoSO.getProductSkuIdList());
        // 拿到瓶码箱码信息
        Map<Long, ProductCodeDTO> codeMap = getPackageAndUnitCode(skuIdSet, productSkuInfoSO.getCityId());
        List<ProductInfoCodeDTO> productInfoCodeDTOList = new ArrayList<>();
        skuIdSet.forEach(skuId -> {
            ProductInfoCodeDTO productInfoCodeDTO = new ProductInfoCodeDTO();
            productInfoCodeDTO.setSukId(skuId);
            ProductCodeDTO productCodeDTO = codeMap.get(skuId);
            if (null != productCodeDTO) {
                // 箱码
                productInfoCodeDTO.setPackageCode(productCodeDTO.getPackageCode());
                // 瓶码
                productInfoCodeDTO.setUnitCode(productCodeDTO.getUnitCode());
            }
            productInfoCodeDTOList.add(productInfoCodeDTO);
        });
        logger.info("getProductInfoCodes 返回的商品条码：{}", JSON.toJSONString(productInfoCodeDTOList));
        return productInfoCodeDTOList;
    }

    /**
     * 清理条码缓存
     *
     * @param productSpecificationId
     */
    public void clearProductCodeCache(Integer productSpecificationId) {
        scmProductCache.clearProductCodeCache(productSpecificationId);
    }

    /**
     * 根据产品skuId查询sku下的库存和最新生产日期，区分仓库配置版本 2.5，,2.5+
     */
    public List<ProductionDateDTO> querySkuInventoryAndProductionDate(ProductionDateQueryDTO queryDTO) {
        logger.info("查询生产日期库存: {}", JSON.toJSONString(queryDTO, SerializerFeature.WriteMapNullValue));
        List<SkuInventoryAndProductionDateHelperBO> boList = productionDateBL.getHelperInfo(queryDTO);
        List<ProductionDateDTO> productionDateDTOList = productionDateBL.queryProductionDate(boList, queryDTO);
        logger.info("查询生产日期库存： {} ； 入参: {}", JSON.toJSONString(productionDateDTOList), JSON.toJSONString(boList));
        Map<String, List<ProductionDateDTO>> productionDateGroupMap = productionDateDTOList.stream()
            .collect(Collectors.groupingBy(m -> m.getProductSkuId() + "-" + m.getLocationId()));
        List<ProductionDateDTO> productionDateDTOS = new ArrayList<>();
        for (SkuInventoryAndProductionDateHelperBO bo : boList) {
            List<ProductionDateDTO> tmpList = productionDateGroupMap.get(bo.getSkuId() + "-" + bo.getAreaId());
            if (CollectionUtils.isEmpty(tmpList)) {
                continue;
            }
            ProductionDateDTO tmpDTO = tmpList.get(0);
            BigDecimal unitTotalCount =
                tmpList.stream().map(ProductionDateDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Date productionDate = tmpList.stream().map(ProductionDateDTO::getProductionDate).filter(Objects::nonNull)
                .max(Comparator.naturalOrder()).orElse(null);
            ProductionDateDTO productionDateDTO = new ProductionDateDTO();
            productionDateDTO.setProductionDate(productionDate);
            productionDateDTO.setUnitTotalCount(unitTotalCount);
            productionDateDTO.setProductSkuId(tmpDTO.getProductSkuId());
            productionDateDTO.setLocationId(bo.getLocationId());
            productionDateDTO.setLocationName(bo.getLocationName());
            productionDateDTO.setPackageQuantity(tmpDTO.getPackageQuantity());
            productionDateDTOS.add(productionDateDTO);
        }
        return productionDateDTOS;
    }

    public Map<Long, String> getSkuConfigStorageAttribute(ProductSkuInfoSO productSkuInfoSO) {
        AssertUtils.notNull(productSkuInfoSO, "查询参数不能为空");
        AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(productSkuInfoSO.getProductSkuIdList(), "skuIds不能为空");
        Map<Long,
            String> skuStorageAttributeMap = mapper.getSkuConfigStorageAttribute(productSkuInfoSO).stream()
                .filter(p -> p != null && p.getStorageAttribute() != null).collect(Collectors
                    .toMap(p -> p.getProductSku_Id(), p -> String.valueOf(p.getStorageAttribute()), (v1, v2) -> v1));
        return skuStorageAttributeMap;
    }

    public void productWarehouseAllocationTypeVerify(ProductWarehouseAllocationTypeVerifyQuery query) {
        AssertUtils.notNull(query, "参数不能为空。");
        AssertUtils.notNull(query.getWarehouseId(), "仓库ID不能为空。");

        if (query.getUserId() == null) {
            AssertUtils.notNull(query.getUserId(), "用户ID不能和分仓属性不能同时为空。");
        }

        if (CollectionUtils.isEmpty(query.getProductSkuIds())) {
            if (CollectionUtils.isEmpty(query.getProductInfoIds()) && query.getWarehouseId() == null) {
                AssertUtils.notNull(query.getProductInfoIds(), "产品ID和仓库ID不能同时为空。");
            }
        }

        // 查询员工所有分仓权限
        Integer warehouseAllocationType = queryConfigByUser(query.getWarehouseId(), query.getUserId());
        if (warehouseAllocationType == null) {
            // 有多分仓权限
            logger.info("产品分仓属性校验:员工无分仓属性:仓库Id={}:用户Id={}", query.getWarehouseId(), query.getUserId());
            return;
        }

        List<ProductWarehouseAllocationTypeVerifyBO> verifyBOList
                = mapper.findProductWarehouseAllocationTypeVerifyBO(query);
        if (CollectionUtils.isEmpty(verifyBOList)) {
            logger.info("产品分仓属性校验:没有查询到商品分仓属性:query={}", JSON.toJSONString(query));
            return;
        }

        // 验证和人员分仓属性是否一致
        for (ProductWarehouseAllocationTypeVerifyBO verifyBO : verifyBOList) {
            if (!Objects.equals(warehouseAllocationType, verifyBO.getWarehouseAllocationType())) {
                WarehouseAllocationTypeEnum warehouseAllocationTypeEnum = WarehouseAllocationTypeEnum.getEnum(verifyBO.getWarehouseAllocationType());
                if (warehouseAllocationTypeEnum == WarehouseAllocationTypeEnum.UNDEFINED) {
                    logger.info("产品分仓属性校验:商品未设置分仓属性:仓库Id={}:用户Id={}:商品Id={}:ProductName={}",
                            query.getWarehouseId(), query.getUserId(), verifyBO.getProductSkuId(), verifyBO.getProductName());
                    continue;
                }

                // 没权限的提示错误信息：无权限操作，xxx商品属于xx分仓。
                String msg = String.format("无权限操作，%s商品属于%s分仓。",
                        verifyBO.getProductName(), warehouseAllocationTypeEnum.getDesc());
                throw new BusinessValidateException(msg);
            }
        }

    }

    private Integer queryConfigByUser(Integer warehouseId, Integer userId) {
        Integer warehouseAllocationType = null;
        WarehouseAllocationQuery query = new WarehouseAllocationQuery();
        query.setWarehouseIds(Collections.singletonList(warehouseId));
        query.setUserId(userId);
        List<UserWarehouseAllocation> userWarehouseAllocations = iWarehouseAllocationConfigService.queryConfigByUser(query);
        if (!CollectionUtils.isEmpty(userWarehouseAllocations)) {
            // 有多个属性时
            if (userWarehouseAllocations.size() > 1) {
                logger.info("查询人员分仓属性2:有多个分仓属性:仓库Id={}:用户Id={}:分仓属性={}", warehouseId, userId, userWarehouseAllocations);
            } else {
                UserWarehouseAllocation userWarehouseAllocation = userWarehouseAllocations.get(0);
                if (userWarehouseAllocation.getEnableWarehouseSplit()) {
                    warehouseAllocationType = userWarehouseAllocation.getConfigType();
                }
            }
        }

        return warehouseAllocationType;
    }
}
