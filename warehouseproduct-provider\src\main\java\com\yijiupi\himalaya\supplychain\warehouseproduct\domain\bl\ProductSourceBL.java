package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSourceConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodePOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSourceCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CodeGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ProductSourceBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSourceBL.class);

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private ProductSourceCodePOMapper productSourceCodePOMapper;

    @Autowired
    private ProductSourceCodeRecordPOMapper productSourceCodeRecordPOMapper;

    @Autowired
    private ProductControlConfigBL productControlConfigBL;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Autowired
    private ProductControlConfigPOMapper productControlConfigPOMapper;

    @Reference
    private IAdminUserQueryService iAdminUserQueryService;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    /**
     * 溯源码生成
     */
    public List<String> generateProductSourceCode(ProductSourceCodeGeneratorDTO productSourceCodeGeneratorDTO) {
        List<String> productSourceCodes = new ArrayList<>();
        String initCode = codeGenerator.generatorNo(productSourceCodeGeneratorDTO.getWarehouseId(),
            CodeGenerator.PRODUCT_SOURCE_CODE);
        for (int i = 1; i <= productSourceCodeGeneratorDTO.getCount(); i++) {
            productSourceCodes.add(initCode + i);
        }
        return productSourceCodes;
    }

    /**
     * 溯源码批量保存
     * 
     * @param productSourceCodeDTOS
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public String batchSaveProductSourceCode(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        String msg = null;
        // List<String> sourceCodes =
        // productSourceCodeDTOS.stream().map(ProductSourceCodeDTO::getCode).collect(Collectors.toList());
        // List<ProductSourceCodePO> productSourceCodes =
        // productSourceCodePOMapper.findBySourceCodesAndState(sourceCodes, null);
        //
        // List<ProductSourceCodeDTO> identicalProductSourceCodes = new ArrayList<>();
        // List<ProductSourceCodeDTO> diffProductSourceCodes = new ArrayList<>();
        // checkProductSourceCodes(productSourceCodeDTOS, productSourceCodes, identicalProductSourceCodes,
        // diffProductSourceCodes);
        // if (!CollectionUtils.isEmpty(identicalProductSourceCodes)) {
        // String codes =
        // identicalProductSourceCodes.stream().map(ProductSourceCodeDTO::getCode).collect(Collectors.joining(","));
        // msg = "下列溯源码在系统中已存在:" + codes;
        // }
        // List<ProductSourceCodePO> productSourceCodePOS =
        // ProductSourceConvertor.productSourceCodeDTOS2NewProductSourceCodePOS(diffProductSourceCodes);
        // if (!CollectionUtils.isEmpty(productSourceCodePOS)) {
        // productSourceCodePOMapper.batchInsertOrUpdate(productSourceCodePOS);
        // }
        // checkRepeatedProductSourceCodes(productSourceCodeDTOS);
        List<ProductSourceCodePO> productSourceCodePOS =
            ProductSourceConvertor.productSourceCodeDTOS2NewProductSourceCodePOS(productSourceCodeDTOS);
        productSourceCodePOMapper.batchInsertOrUpdate(productSourceCodePOS);
        simpleSaveProductSourceCodeRecord(
            ProductSourceConvertor.productSourceCodeDTOS2ProductSourceCodeRecordDTOS(productSourceCodeDTOS));
        return msg;
    }

    public void simpleSaveProductSourceCodeRecord(List<ProductSourceCodeRecordDTO> recordDTOList) {
        AssertUtils.notEmpty(recordDTOList, "新增溯源码记录参数不能为空");
        recordDTOList.forEach(recordDTO -> {
            AssertUtils.notNull(recordDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(recordDTO.getCode(), "溯源码不能为空");
            AssertUtils.notNull(recordDTO.getAfterState(), "变更后状态不能为空");
        });
        LOGGER.info("新增溯源码记录参数：{}", JSON.toJSONString(recordDTOList));

        // 根据溯源码查控货策略信息
        List<Long> configIds = recordDTOList.stream().filter(record -> record.getConfigId() != null)
            .map(ProductSourceCodeRecordDTO::getConfigId).collect(Collectors.toList());
        Map<Long, ProductControlConfigPO> configPOMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(configIds)) {
            List<ProductControlConfigPO> configPOS =
                productControlConfigPOMapper.listProductControlConfigByIds(configIds);
            if (!CollectionUtils.isEmpty(configPOS)) {
                configPOMap = configPOS.stream().collect(
                    Collectors.toMap(ProductControlConfigPO::getId, Function.identity(), (key1, key2) -> key1));
            }
        }

        List<ProductSourceCodeRecordPO> recordPOList = new ArrayList<>();
        // 组装溯源码记录
        for (ProductSourceCodeRecordDTO recordDTO : recordDTOList) {

            ProductSourceCodeRecordPO recordPO = new ProductSourceCodeRecordPO();
            BeanUtils.copyProperties(recordDTO, recordPO);
            recordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SOURCE_CODE_RECORD));
            if (recordDTO.getConfigId() != null && configPOMap.get(recordDTO.getConfigId()) != null) {
                ProductControlConfigPO configPO = configPOMap.get(recordDTO.getConfigId());
                recordPO.setProviderId(configPO.getProviderId());
                recordPO.setProvider(configPO.getProvider());
            }
            recordPOList.add(recordPO);
        }

        if (!CollectionUtils.isEmpty(recordPOList)) {
            // 获取变更前状态
            recordPOList.forEach(p -> {
                Byte beforeState =
                    productSourceCodeRecordPOMapper.getProductSourceCodeState(p.getProductSourceCodeId());
                p.setBeforeState(beforeState != null ? beforeState : ProductSourceCodeRecordStateEnum.在库.getType());
            });
            productSourceCodeRecordPOMapper.insertBatch(recordPOList);
            LOGGER.info("新增溯源码记录：{}", JSON.toJSONString(recordPOList));
        }
    }

    /**
     * 溯源码关联订单
     * 
     * @param productSourceCodeDTOS
     */
    public void productSourceCodesAssociateOrder(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        productSourceCodePOMapper.productSourceCodesAssociateOrder(productSourceCodeDTOS);
    }

    /**
     * 校验溯源码、控货策略关联关系
     */
    public Boolean checkProductSource(Long configId, String code) {
        Boolean flag = false;
        int codesCount = productSourceCodePOMapper.getCodesCountByConfigIdAndCode(configId, code, null);
        if (codesCount > 0) {
            flag = true;
        }
        return flag;
    }

    /**
     * 条件分页查询
     * 
     * @param productSourceCodeQueryDTO
     * @return
     */
    public PageList<ProductSourceCodeDTO>
        pageListProductSourceCode(ProductSourceCodeQueryDTO productSourceCodeQueryDTO) {
        PageResult<ProductSourceCodePO> pageResult = productSourceCodePOMapper.pageListProductSourceCode(
            productSourceCodeQueryDTO, productSourceCodeQueryDTO.getPageNum(), productSourceCodeQueryDTO.getPageSize());

        List<ProductSourceCodeDTO> productSourceCodeDTOS =
            ProductSourceConvertor.productSourceCodePOS2ProductSourceCodeDTOS(pageResult.getResult());
        PageList<ProductSourceCodeDTO> pageList = new PageList<>();
        pageList.setDataList(productSourceCodeDTOS);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    /**
     * 采集溯源码
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void collectProductSourceCode(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        LOGGER.info("溯源码绑定参数:{}", JSON.toJSONString(productSourceCodeDTOS));

        // 先清除订单关联溯源码信息，再更新
        List<String> businessNos = productSourceCodeDTOS.stream().map(ProductSourceCodeDTO::getBusinessNo).distinct()
            .collect(Collectors.toList());
        productSourceCodePOMapper.clearByBusinessNos(businessNos, null);

        checkProductSourceCodes(productSourceCodeDTOS);

        productSourceCodeDTOS.stream().collect(Collectors.groupingBy(ProductSourceCodeDTO::getConfigId))
            .forEach((configId, sourceCodes) -> {
                productSourceCodePOMapper.collectProductSourceCode(configId, sourceCodes);
            });
        saveProductSourceCodeRecord(
            ProductSourceConvertor.productSourceCodeDTOS2ProductSourceCodeRecordDTOS(productSourceCodeDTOS));
    }

    /**
     * 根据来源单据删除溯源码
     * 
     * @param sourceBusinessNos
     */
    public void deleteBySourceBusinessNos(List<String> sourceBusinessNos) {
        productSourceCodePOMapper.deleteBySourceBusinessNos(sourceBusinessNos);
    }

    /**
     * 溯源码校验
     */
    public void checkProductSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        // 同一个溯源码不能重复使用
        Map<String, List<String>> errMap = new HashMap<>(16);
        productSourceCodeDTOS.stream().filter(code -> code.getBusinessNo() != null)
            .collect(Collectors.groupingBy(ProductSourceCodeDTO::getCode,
                Collectors.mapping(ProductSourceCodeDTO::getBusinessNo, Collectors.toList())))
            .forEach((code, businessNos) -> {
                if (businessNos.size() > 1) {
                    errMap.put(code, businessNos);
                }
            });

        if (errMap.size() > 0) {
            throw new BusinessValidateException(createErrorMessage(errMap));
        }

        List<String> productSourceCodes =
            productSourceCodeDTOS.stream().map(ProductSourceCodeDTO::getCode).collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOS =
            productSourceCodePOMapper.findBySourceCodesAndState(productSourceCodes, null);

        if (CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            throw new BusinessValidateException("以下溯源码未采集，请先完成相应采集任务:" + StringUtils.join(productSourceCodes, ","));
        }

        Map<String, ProductSourceCodePO> ProductSourceCodeMap = productSourceCodePOS.stream()
            .collect(Collectors.toMap(ProductSourceCodePO::getCode, Function.identity(), (key1, key2) -> key1));

        List<String> existCodes = new ArrayList<>();
        List<String> notExistCodes = new ArrayList<>();
        List<String> errCodes = new ArrayList<>();
        for (ProductSourceCodeDTO productSourceCode : productSourceCodeDTOS) {
            ProductSourceCodePO sourceCode = ProductSourceCodeMap.get(productSourceCode.getCode());
            if (sourceCode == null) {
                notExistCodes.add(productSourceCode.getCode());
                continue;
            }
            if (sourceCode.getState() == ProductSourceCodeStateEnum.已使用.getType() && Objects.equals(
                productSourceCode.getProductSourceCodeRecordState(), ProductSourceCodeRecordStateEnum.在途.getType())) {
                existCodes.add(sourceCode.getCode());
                continue;
            }
            if (!sourceCode.getConfigId().equals(productSourceCode.getConfigId())) {
                errCodes.add(sourceCode.getCode());
            }
        }

        if (!CollectionUtils.isEmpty(existCodes)) {
            throw new BusinessValidateException("以下溯源码已使用:" + StringUtils.join(existCodes, ","));
        }
        if (!CollectionUtils.isEmpty(notExistCodes)) {
            throw new BusinessValidateException("以下溯源码未采集，请先完成相应采集任务:" + StringUtils.join(notExistCodes, ","));
        }
        if (!CollectionUtils.isEmpty(errCodes)) {
            throw new BusinessValidateException("以下溯源码关联控货策略不对:" + StringUtils.join(errCodes, ","));
        }
    }

    private String createErrorMessage(Map<String, List<String>> errMap) {
        StringBuilder errorMessage = new StringBuilder("溯源码重复使用!\n");
        errMap.forEach((code, businessNos) -> {
            errorMessage.append(String.format("溯源码 :%s,单号:%s %n", code, StringUtils.join(businessNos, ",")));
        });

        return errorMessage.toString();
    }

    /**
     * 检查溯源码是否重复
     * 
     * @param productSourceCodeDTOS
     */
    public void checkRepeatedProductSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        if (CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            return;
        }
        List<String> sourceCodes =
            productSourceCodeDTOS.stream().map(ProductSourceCodeDTO::getCode).collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodes =
            productSourceCodePOMapper.findBySourceCodesAndState(sourceCodes, null);
        if (!CollectionUtils.isEmpty(productSourceCodes)) {
            throw new BusinessValidateException("下列溯源码在系统中已存在:"
                + productSourceCodes.stream().map(ProductSourceCodePO::getCode).collect(Collectors.joining(",")));
        }
    }

    /**
     * 修改溯源码
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProductSourceCode(ProductSourceCodeDTO codeDTO) {
        AssertUtils.notNull(codeDTO, "修改溯源码参数不能为空");
        AssertUtils.notNull(codeDTO.getId(), "溯源码id不能为空");
        AssertUtils.notNull(codeDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(codeDTO.getLastUpdateUser(), "更新人不能为空");
        ProductSourceCodePO oldCodePO = productSourceCodePOMapper.selectByPrimaryKey(codeDTO.getId());
        if (oldCodePO == null) {
            throw new BusinessException("修改失败，溯源码不存在：" + codeDTO.getId());
        }

        // 1、修改溯源码
        ProductSourceCodePO codePO = new ProductSourceCodePO();
        BeanUtils.copyProperties(codeDTO, codePO);
        productSourceCodePOMapper.updateByPrimaryKeySelective(codePO);
        LOGGER.info("修改溯源码: {}", JSON.toJSONString(codePO));

        // 2、如果变更了溯源码状态，则新增记录
        if (codePO.getState() != null && !Objects.equals(codePO.getState(), oldCodePO.getState())) {
            ProductControlConfigDTO controlConfigDTO =
                productControlConfigBL.getProductControlConfig(oldCodePO.getConfigId());
            if (controlConfigDTO == null) {
                throw new BusinessException("查不到控货策略：" + oldCodePO.getConfigId());
            }

            // 新增溯源码记录
            ProductSourceCodeRecordPO recordPO = new ProductSourceCodeRecordPO();
            recordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SOURCE_CODE_RECORD));
            recordPO.setWarehouseId(codeDTO.getWarehouseId());
            recordPO.setProductSourceCodeId(oldCodePO.getId());
            recordPO.setCode(oldCodePO.getCode());
            recordPO.setBeforeState(oldCodePO.getState());
            recordPO.setAfterState(codePO.getState());
            recordPO.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.手动修改.getType());
            recordPO.setLastUpdateUser(codeDTO.getLastUpdateUser());
            recordPO.setProviderId(controlConfigDTO.getProviderId());
            recordPO.setProvider(controlConfigDTO.getProvider());
            productSourceCodeRecordPOMapper.insertSelective(recordPO);
            LOGGER.info("修改溯源码记录: {}", JSON.toJSONString(recordPO));
        }
    }

    /**
     * 删除溯源码
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductSourceCode(Long id) {
        AssertUtils.notNull(id, "溯源码id不能为空");
        ProductSourceCodePO productSourceCodePO = productSourceCodePOMapper.selectByPrimaryKey(id);
        if (productSourceCodePO == null) {
            throw new BusinessException("删除失败，溯源码不存在：" + id);
        }
        // 1、删除溯源码
        productSourceCodePOMapper.deleteByPrimaryKey(id);
        LOGGER.info("删除溯源码: {}", productSourceCodePO.getCode());

        // 2、删除溯源码记录
        List<ProductSourceCodeRecordPO> recordPOList = productSourceCodeRecordPOMapper.listByCodeId(id);
        if (!CollectionUtils.isEmpty(recordPOList)) {
            List<Long> recordIds = recordPOList.stream().map(p -> p.getId()).collect(Collectors.toList());
            productSourceCodeRecordPOMapper.deleteBatch(recordIds);
        }
    }

    /**
     * 查询溯源码
     * 
     * @return
     */
    public ProductSourceCodeDTO getProductSourceCodeDTO(String code) {
        AssertUtils.notNull(code, "溯源码不能为空");
        ProductSourceCodePO productSourceCodePO = productSourceCodePOMapper.getProductSourceCode(code);
        if (productSourceCodePO == null) {
            return null;
        }
        ProductSourceCodeDTO codeDTO = new ProductSourceCodeDTO();
        BeanUtils.copyProperties(productSourceCodePO, codeDTO);

        // 获取溯源码记录
        ProductSourceCodeRecordSO recordSO = new ProductSourceCodeRecordSO();
        recordSO.setCode(code);
        PageList<ProductSourceCodeRecordDTO> pageRecordList = listProductSourceCodeRecord(recordSO);
        if (pageRecordList != null) {
            codeDTO.setRecordList(pageRecordList.getDataList());
        }
        return codeDTO;
    }

    /**
     * 获取溯源码记录列表
     * 
     * @return
     */
    public PageList<ProductSourceCodeRecordDTO> listProductSourceCodeRecord(ProductSourceCodeRecordSO recordSO) {
        AssertUtils.notNull(recordSO, "参数不能为空");
        AssertUtils.notNull(recordSO.getCode(), "溯源码不能为空");
        // 获取溯源码记录列表
        PageResult<ProductSourceCodeRecordPO> pageResult =
            productSourceCodeRecordPOMapper.listProductSourceCodeRecord(recordSO);
        List<ProductSourceCodeRecordPO> poList = pageResult.toPageList().getDataList();
        List<ProductSourceCodeRecordDTO> dtoList = null;
        if (!CollectionUtils.isEmpty(poList)) {
            // 查询仓库名称
            List<Integer> warhouseIds =
                poList.stream().map(p -> p.getWarehouseId()).distinct().collect(Collectors.toList());
            Map<Integer, String> warehosueMap = getWarehouseMap(warhouseIds);

            dtoList = poList.stream().map(po -> {
                ProductSourceCodeRecordDTO dto = new ProductSourceCodeRecordDTO();
                BeanUtils.copyProperties(po, dto);
                dto.setBeforeStateText(ProductSourceCodeRecordStateEnum.getEnmuName(dto.getBeforeState()));
                dto.setAfterStateText(ProductSourceCodeRecordStateEnum.getEnmuName(dto.getAfterState()));
                dto.setBusinessTypeText(ProductSourceCodeRecordBusinessTypeEnum.getEnmuName(dto.getBusinessType()));
                dto.setWarehouseName(warehosueMap != null ? warehosueMap.get(dto.getWarehouseId()) : null);
                return dto;
            }).collect(Collectors.toList());
        }

        PageList<ProductSourceCodeRecordDTO> pageList = new PageList<>();
        pageList.setDataList(dtoList);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    /**
     * 查询仓库名称
     */
    private Map<Integer, String> getWarehouseMap(List<Integer> warhouseIds) {
        if (CollectionUtils.isEmpty(warhouseIds)) {
            return null;
        }
        // 查询仓库名称
        List<Warehouse> warehouseList = iWarehouseQueryService.listWarehouseByIds(warhouseIds);
        if (CollectionUtils.isEmpty(warehouseList)) {
            return null;
        }
        Map<Integer, String> warehosueMap = new HashMap<>(16);
        warehouseList.forEach(warehouse -> {
            warehosueMap.put(warehouse.getId(), warehouse.getName());
        });
        return warehosueMap;
    }

    /**
     * 新增溯源码记录
     */
    public void saveProductSourceCodeRecord(List<ProductSourceCodeRecordDTO> recordDTOList) {
        AssertUtils.notEmpty(recordDTOList, "新增溯源码记录参数不能为空");
        recordDTOList.forEach(recordDTO -> {
            AssertUtils.notNull(recordDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(recordDTO.getCode(), "溯源码不能为空");
            AssertUtils.notNull(recordDTO.getAfterState(), "变更后状态不能为空");
            AssertUtils.notNull(recordDTO.getBusinessType(), "单据类型不能为空");
        });
        LOGGER.info("新增溯源码记录参数：{}", JSON.toJSONString(recordDTOList));

        // 查询溯源码详情
        List<String> codeList = recordDTOList.stream().map(p -> p.getCode()).distinct().collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOList =
            productSourceCodePOMapper.getProductSourceByCodeList(codeList, null);
        if (CollectionUtils.isEmpty(productSourceCodePOList)) {
            throw new BusinessException("查不到溯源码：" + JSON.toJSONString(codeList));
        }

        // 查控货策略
        List<Long> configIdList =
            productSourceCodePOList.stream().map(p -> p.getConfigId()).distinct().collect(Collectors.toList());
        List<ProductControlConfigDTO> productControlConfigDTOList =
            productControlConfigBL.listProductControlConfigByIds(configIdList);
        if (CollectionUtils.isEmpty(productControlConfigDTOList)) {
            throw new BusinessException("查不到控货策略：" + JSON.toJSONString(configIdList));
        }

        List<ProductSourceCodeRecordPO> recordPOList = new ArrayList<>();
        // 组装溯源码记录
        recordDTOList.forEach(recordDTO -> {
            // 筛选溯源码详情
            List<ProductSourceCodePO> filterSourceCodeList = productSourceCodePOList.stream()
                .filter(p -> Objects.equals(p.getCode(), recordDTO.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSourceCodeList)) {
                throw new BusinessException("查询溯源码为空：" + recordDTO.getCode());
            }
            if (filterSourceCodeList.size() > 1) {
                throw new BusinessException("查询溯源码存在重复：" + JSON.toJSONString(filterSourceCodeList));
            }
            ProductSourceCodePO productSourceCodePO = filterSourceCodeList.get(0);

            // 筛选控货策略
            Optional<ProductControlConfigDTO> optionalControlConfigDTO = productControlConfigDTOList.stream()
                .filter(p -> Objects.equals(p.getId(), productSourceCodePO.getConfigId())).findFirst();
            if (!optionalControlConfigDTO.isPresent()) {
                throw new BusinessException("查询溯源码对应控货策略为空：" + JSON.toJSONString(productSourceCodePO));
            }

            ProductSourceCodeRecordPO recordPO = new ProductSourceCodeRecordPO();
            BeanUtils.copyProperties(recordDTO, recordPO);
            recordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SOURCE_CODE_RECORD));
            recordPO.setProductSourceCodeId(productSourceCodePO.getId());
            recordPO.setProviderId(optionalControlConfigDTO.get().getProviderId());
            recordPO.setProvider(optionalControlConfigDTO.get().getProvider());
            recordPOList.add(recordPO);
        });

        if (!CollectionUtils.isEmpty(recordPOList)) {
            // 删除重复的溯源码记录
            List<Long> existIds = productSourceCodeRecordPOMapper.listProductSourceCodeRecordIds(recordPOList);
            if (!CollectionUtils.isEmpty(existIds)) {
                productSourceCodeRecordPOMapper.deleteBatch(existIds);
                LOGGER.info("删除重复溯源码记录：{}", JSON.toJSONString(existIds));
            }
            // 获取变更前状态
            recordPOList.forEach(p -> {
                Byte beforeState =
                    productSourceCodeRecordPOMapper.getProductSourceCodeState(p.getProductSourceCodeId());
                p.setBeforeState(beforeState != null ? beforeState : ProductSourceCodeRecordStateEnum.在库.getType());
            });
            productSourceCodeRecordPOMapper.insertBatch(recordPOList);
            LOGGER.info("新增溯源码记录：{}", JSON.toJSONString(recordPOList));
        }
    }

    /**
     * 溯源码操作
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void processSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        LOGGER.info("溯源码操作参数:{}", JSON.toJSONString(productSourceCodeDTOS));
        // 补齐差异数据
        productSourceCodeDTOS = fillDiff(productSourceCodeDTOS);

        checkProductSourceCodes(productSourceCodeDTOS);

        List<ProductSourceCodeRecordDTO> recordDTOList =
            ProductSourceConvertor.productSourceCodeDTOS2ProductSourceCodeRecordDTOS(productSourceCodeDTOS);
        // 在库的需要清除掉单号信息
        productSourceCodeDTOS.stream().filter(code -> Objects.equals(code.getProductSourceCodeRecordState(),
            ProductSourceCodeRecordStateEnum.在库.getType())).forEach(code -> {
                code.setBusinessId(null);
                code.setBusinessNo(null);
            });
        productSourceCodePOMapper.processSourceCodes(productSourceCodeDTOS);
        saveProductSourceCodeRecord(recordDTOList);
    }

    /**
     * 补齐差异数据
     */
    private List<ProductSourceCodeDTO> fillDiff(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        if (CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            return productSourceCodeDTOS;
        }
        List<ProductSourceCodeDTO> result =
            productSourceCodeDTOS.stream().filter(code -> code.getCode() != null).collect(Collectors.toList());

        List<String> businessNos = productSourceCodeDTOS.stream().filter(code -> code.getBusinessNo() != null)
            .map(ProductSourceCodeDTO::getBusinessNo).distinct().collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOS = productSourceCodePOMapper.findByBusinessNos(businessNos);
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            throw new BusinessException("未找到单据关联溯源码信息:" + StringUtils.join(businessNos, ","));
        }

        Map<String, List<ProductSourceCodePO>> orderCodeMap =
            productSourceCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getBusinessNo));
        productSourceCodeDTOS.stream().filter(code -> code.getBusinessNo() != null)
            .collect(Collectors.groupingBy(ProductSourceCodeDTO::getBusinessNo)).forEach((orderNo, sourceCodes) -> {
                List<ProductSourceCodePO> oldSourceCodes = orderCodeMap.get(orderNo);
                if (CollectionUtils.isEmpty(oldSourceCodes)) {
                    throw new BusinessException("未找到单据关联溯源码信息:" + orderNo);
                }

                List<String> newCodes = sourceCodes.stream().filter(code -> StringUtils.isNotEmpty(code.getCode()))
                    .map(ProductSourceCodeDTO::getCode).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(newCodes)) {
                    oldSourceCodes.removeIf(code -> newCodes.contains(code.getCode()));
                }

                if (!CollectionUtils.isEmpty(oldSourceCodes)) {
                    oldSourceCodes.forEach(code -> {
                        ProductSourceCodeDTO codeDTO = new ProductSourceCodeDTO();
                        codeDTO.setWarehouseId(sourceCodes.get(0).getWarehouseId());
                        codeDTO.setCode(code.getCode());
                        codeDTO.setConfigId(code.getConfigId());
                        codeDTO.setBusinessId(sourceCodes.get(0).getBusinessId());
                        codeDTO.setBusinessNo(orderNo);
                        codeDTO.setProductSourceCodeRecordBusinessType(
                            ProductSourceCodeRecordBusinessTypeEnum.销售订单.getType());
                        codeDTO.setState(ProductSourceCodeStateEnum.未使用.getType());
                        codeDTO.setProductSourceCodeRecordState(ProductSourceCodeRecordStateEnum.在库.getType());
                        codeDTO.setLastUpdateUser(sourceCodes.get(0).getLastUpdateUser());

                        result.add(codeDTO);
                    });
                }
            });
        return result;
    }

    /**
     * 根据控货策略和对应溯源码返回有问题的数据
     */
    public Map<Long, List<String>> findErrProductSourceCodes(Map<Long, List<String>> configIdAndSourceCodeMap) {
        if (CollectionUtils.isEmpty(configIdAndSourceCodeMap)) {
            return configIdAndSourceCodeMap;
        }

        Map<Long, List<String>> result = new HashMap<>(16);
        List<ProductSourceCodePO> productSourceCodePOS = productSourceCodePOMapper.findBySourceCodesAndState(
            configIdAndSourceCodeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()), null);
        configIdAndSourceCodeMap.forEach((configId, sourceCodes) -> {
            sourceCodes.removeIf(productSourceCodePOS::contains);
            if (!CollectionUtils.isEmpty(sourceCodes)) {
                result.put(configId, sourceCodes);
            }
        });
        return result;
    }

    /**
     * 根据溯源码清除关联关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void clearRelationByCodes(List<String> codes, String operator) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<ProductSourceCodePO> productSourceCodePOS =
            productSourceCodePOMapper.findBySourceCodesAndState(codes, ProductSourceCodeStateEnum.已使用.getType());
        clearRelation(productSourceCodePOS, operator);
    }

    /**
     * 根据单号清除关联关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void clearRelationByBusinessNos(List<String> orderNos, String operator) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return;
        }
        List<ProductSourceCodePO> productSourceCodePOS = productSourceCodePOMapper.findByBusinessNos(orderNos);
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            return;
        }
        productSourceCodePOS = productSourceCodePOS.stream()
            .filter(code -> code.getState() == ProductSourceCodeStateEnum.已使用.getType()).collect(Collectors.toList());
        clearRelation(productSourceCodePOS, operator);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void clearRelation(List<ProductSourceCodePO> productSourceCodePOS, String operator) {
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            return;
        }
        List<String> codes = new ArrayList<>();
        List<ProductSourceCodeRecordDTO> recordDTOList = new ArrayList<>();
        productSourceCodePOS.forEach(code -> {
            codes.add(code.getCode());
            ProductSourceCodeRecordDTO recordDTO = new ProductSourceCodeRecordDTO();
            recordDTO.setWarehouseId(code.getWarehouseId());
            recordDTO.setCode(code.getCode());
            recordDTO.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.手动修改.getType());
            recordDTO.setAfterState(ProductSourceCodeRecordStateEnum.在库.getType());
            recordDTO.setCreateUser(operator);

            recordDTOList.add(recordDTO);
        });
        productSourceCodePOMapper.clearRelationByCodes(codes, operator);
        saveProductSourceCodeRecord(recordDTOList);
    }

    /**
     * 溯源码绑定
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void bindProductSourceCodes(List<ProductSourceCodeBindDTO> productSourceCodeBindDTOS) {

        // 校验溯源码
        checkProductSourceCodeAndConfig(
            ProductSourceConvertor.ProductSourceCodeBindDTOS2ProductSourceCodeCheckDTOS(productSourceCodeBindDTOS));

        List<ProductSourceCodePO> updateList = new ArrayList<>();
        List<ProductSourceCodeRecordDTO> recordDTOList = new ArrayList<>();

        List<String> codes = productSourceCodeBindDTOS.stream().map(ProductSourceCodeBindDTO::getSourceCode)
            .collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOS =
            productSourceCodePOMapper.getProductSourceByCodeList(codes, null);
        Map<String, List<ProductSourceCodePO>> productSourceCodeMap =
            productSourceCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getCode));
        Map<String, List<ProductSourceCodeBindDTO>> sourceCodeBindMap =
            productSourceCodeBindDTOS.stream().collect(Collectors.groupingBy(ProductSourceCodeBindDTO::getSourceCode));

        for (Map.Entry<String, List<ProductSourceCodeBindDTO>> entry : sourceCodeBindMap.entrySet()) {
            String sourceCode = entry.getKey();
            List<ProductSourceCodeBindDTO> sourceCodeBindList = entry.getValue();
            List<ProductSourceCodePO> sourceCodePOS = productSourceCodeMap.get(sourceCode);
            List<Long> configIds =
                sourceCodePOS.stream().map(ProductSourceCodePO::getConfigId).distinct().collect(Collectors.toList());

            sourceCodePOS = sourceCodePOS.stream().filter(code -> {
                if (code.getConfigId() == null) {
                    return true;
                } else {
                    return configIds.contains(code.getConfigId());
                }
            }).sorted(Comparator.comparing(ProductSourceCodePO::getState)).collect(Collectors.toList());

            if (sourceCodeBindList.size() > sourceCodePOS.size()) {
                throw new BusinessValidateException("溯源码数据重复");
            }

            for (int i = 0; i < sourceCodeBindList.size(); i++) {
                ProductSourceCodeBindDTO bindDTO = sourceCodeBindList.get(i);
                ProductSourceCodePO codePO = sourceCodePOS.get(i);
                ProductSourceCodePO update = new ProductSourceCodePO();
                update.setId(codePO.getId());
                update.setBusinessId(bindDTO.getBusinessId());
                update.setBusinessNo(bindDTO.getBusinessNo());
                update.setState(ProductSourceCodeStateEnum.已使用.getType());
                update.setLastUpdateUser(bindDTO.getOperator());

                updateList.add(update);

                ProductSourceCodeRecordDTO record = new ProductSourceCodeRecordDTO();
                record.setWarehouseId(bindDTO.getWarehouseId());
                record.setProductSourceCodeId(codePO.getId());
                record.setCode(bindDTO.getSourceCode());
                record.setBusinessId(bindDTO.getBusinessId().toString());
                record.setBusinessNo(bindDTO.getBusinessNo());
                record.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.销售订单.getType());
                record.setAfterState(ProductSourceCodeRecordStateEnum.出库.getType());
                record.setCreateUser(bindDTO.getOperator());
                record.setConfigId(bindDTO.getConfigId());

                recordDTOList.add(record);
            }
        }

        productSourceCodePOMapper.bindProductSourceCodes(updateList);
        simpleSaveProductSourceCodeRecord(recordDTOList);
    }

    /**
     * 溯源码校验
     */
    public void checkProductSourceCodeAndConfig(List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS) {
        if (CollectionUtils.isEmpty(productSourceCodeCheckDTOS)) {
            return;
        }

        List<String> productSourceCodes = productSourceCodeCheckDTOS.stream()
            .map(ProductSourceCodeCheckDTO::getSourceCode).distinct().collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOS =
            productSourceCodePOMapper.getProductSourceByCodeList(productSourceCodes, null);
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            throw new BusinessException("溯源码不存在！");
        }

        Set<String> notExistCodes = new HashSet<>();
        Map<String, Integer> errCodes = new HashMap<>(16);
        Map<String, Integer> errConfig = new HashMap<>(16);

        Map<String, List<ProductSourceCodePO>> productSourceCodeMap =
            productSourceCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getCode));
        Map<String, List<ProductSourceCodeCheckDTO>> productSourceCodeCheckMap = productSourceCodeCheckDTOS.stream()
            .collect(Collectors.groupingBy(ProductSourceCodeCheckDTO::getSourceCode));

        for (Map.Entry<String, List<ProductSourceCodeCheckDTO>> entry : productSourceCodeCheckMap.entrySet()) {
            String code = entry.getKey();
            List<ProductSourceCodeCheckDTO> checkDTOS = entry.getValue();
            List<ProductSourceCodePO> sourceCodePOS = productSourceCodeMap.get(code);
            if (CollectionUtils.isEmpty(sourceCodePOS)) {
                notExistCodes.add(code);
                continue;
            }

            // 校验控货策略和溯源码关联关系
            List<Long> configIds = checkDTOS.stream().filter(check -> check.getConfigId() != null)
                .map(ProductSourceCodeCheckDTO::getConfigId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(configIds)) {
                sourceCodePOS =
                    sourceCodePOS.stream().filter(sourceCodePO -> configIds.contains(sourceCodePO.getConfigId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sourceCodePOS)) {
                    errConfig.put(code, checkDTOS.size());
                    continue;
                }

                int checkConfigCount = (int)checkDTOS.stream().filter(check -> check.getConfigId() != null)
                    .map(ProductSourceCodeCheckDTO::getConfigId).count();
                if (checkConfigCount > sourceCodePOS.size()) {
                    errConfig.put(code, checkConfigCount - sourceCodePOS.size());
                    continue;
                }
            }

            // 校验溯源码数量
            if (checkDTOS.size() > sourceCodePOS.size()) {
                errCodes.put(code, checkDTOS.size() - sourceCodePOS.size());
            }
        }

        if (!CollectionUtils.isEmpty(notExistCodes)) {
            throw new BusinessException("下列溯源码在系统中不存在：[" + StringUtils.join(notExistCodes, ",") + "]");
        }
        if (!CollectionUtils.isEmpty(errCodes)) {
            StringBuilder errMessages = new StringBuilder("溯源码重复使用!\n");
            errCodes.forEach((code, count) -> {
                errMessages.append(String.format("溯源码：[%s],重复使用%s个 %n", code, count));
            });

            throw new BusinessValidateException(errMessages.toString());
        }
        if (!CollectionUtils.isEmpty(errConfig)) {
            StringBuilder errMessages = new StringBuilder("溯源码和控货策略的关联关系不匹配!\n");
            errCodes.forEach((code, count) -> {
                errMessages.append(String.format("溯源码：%s,有%s个不匹配 %n", code, count));
            });

            throw new BusinessValidateException(errMessages.toString());
        }
    }

    /**
     * 根据溯源码和控货策略组合(支持重复)
     */
    public Map<Long, List<String>> sourceCodeMatchControlConfig(List<String> sourceCodes, List<Long> configIds) {
        LOGGER.info("根据溯源码和控货策略组合溯源码参数:{}，控货策略参数：{}", JSON.toJSONString(sourceCodes), JSON.toJSONString(configIds));
        Map<Long, List<String>> result = new HashMap<>(16);
        List<String> codes = sourceCodes.stream().distinct().collect(Collectors.toList());
        List<ProductSourceCodePO> productSourceCodePOS =
            productSourceCodePOMapper.findBySourceCodesAndState(codes, null);
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            return result;
        }

        Map<String, Long> sourceCodeCountMap =
            sourceCodes.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<Long, Long> configCountMap =
            configIds.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<String, List<ProductSourceCodePO>> productSourceCodeMap =
            productSourceCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getCode));

        for (Map.Entry<String, Long> entry : sourceCodeCountMap.entrySet()) {
            String code = entry.getKey();
            int codeCount = entry.getValue().intValue();
            List<ProductSourceCodePO> sourceCodePOS = productSourceCodeMap.get(code);
            // 判断溯源码存不存在
            if (CollectionUtils.isEmpty(sourceCodePOS)) {
                continue;
            }

            Map<Long, List<ProductSourceCodePO>> productSourceConfigMap =
                sourceCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getConfigId));
            for (Map.Entry<Long, List<ProductSourceCodePO>> configEntry : productSourceConfigMap.entrySet()) {
                Long configId = configEntry.getKey();
                List<ProductSourceCodePO> productSourceConfigList = configEntry.getValue();
                Long configCount = configCountMap.get(configId);
                // 判断参数里未使用的控货策略数据够不够
                if (configCount == null || configCount == 0) {
                    continue;
                }
                // 计算该溯源码可以往这个控货策略装载数
                int count = Math.min(codeCount, configCount.intValue());
                if (count > productSourceConfigList.size()) {
                    count = productSourceConfigList.size();
                }

                List<String> resultCodes = result.get(configId);
                if (resultCodes == null) {
                    resultCodes = new ArrayList<>();
                }
                for (int i = 0; i < count; i++) {
                    resultCodes.add(code);
                }

                codeCount -= count;
                configCount -= count;
                configCountMap.put(configId, configCount);
                result.put(configId, resultCodes);
                if (codeCount == 0) {
                    break;
                }
            }

        }
        LOGGER.info("根据溯源码和控货策略组合溯源码结果：{}", JSON.toJSONString(result));
        return result;
    }

    public List<ProductSourceCodeDTO> getProductSourceCodeAndRecord(String code) {
        AssertUtils.notNull(code, "溯源码不能为空");
        List<ProductSourceCodePO> productSourceCodePOS = productSourceCodePOMapper.getProductSourceCodeAndRecord(code);
        List<ProductSourceCodeDTO> productSourceCodeDTOS =
            ProductSourceConvertor.productSourceCodePOS2ProductSourceCodeDTOS(productSourceCodePOS);
        if (!CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            // 查询仓库名称
            List<Integer> warehouseIds = productSourceCodeDTOS.stream().map(ProductSourceCodeDTO::getWarehouseId)
                .distinct().collect(Collectors.toList());
            Map<Integer, String> warehouseMap = getWarehouseMap(warehouseIds);

            productSourceCodeDTOS.stream().flatMap(sourceCode -> sourceCode.getRecordList().stream())
                .forEach(record -> {
                    record.setBeforeStateText(ProductSourceCodeRecordStateEnum.getEnmuName(record.getBeforeState()));
                    record.setAfterStateText(ProductSourceCodeRecordStateEnum.getEnmuName(record.getAfterState()));
                    record.setBusinessTypeText(
                        ProductSourceCodeRecordBusinessTypeEnum.getEnmuName(record.getBusinessType()));
                    record.setWarehouseName(warehouseMap != null ? warehouseMap.get(record.getWarehouseId()) : null);
                });
        }
        return productSourceCodeDTOS;
    }

    @Deprecated
    public List<ProductSourceCodeInfoDTO>
        checkProductSourceId(List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS) {
        // 1、查溯源码信息（仓库+溯源码维度）
        List<ProductSourceCodeInfoDTO> dataSourceCodeInfoList = productSourceCodePOMapper
            .getProductSourceByCodeInfoList(productSourceCodeCheckDTOS, ProductSourceCodeStateEnum.未使用.getType());
        if (CollectionUtils.isEmpty(dataSourceCodeInfoList)) {
            throw new BusinessException("溯源码不存在！");
        }
        LOGGER.info("溯源码校验-查询DB结果：{}", JSON.toJSONString(dataSourceCodeInfoList));
        // 2、查询sku信息（请求的sku、DB溯源码的sku）
        List<Long> skuIdList = new ArrayList<>();
        skuIdList.addAll(productSourceCodeCheckDTOS.stream().map(ProductSourceCodeCheckDTO::getProductSkuId).distinct()
            .collect(Collectors.toList()));
        skuIdList.addAll(dataSourceCodeInfoList.stream().map(ProductSourceCodeInfoDTO::getProductSkuId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, ProductSkuDTO> skuDTOMap = productSkuQueryBL.getProductSkuMapBySkuIds(skuIdList);
        if (CollectionUtils.isEmpty(skuDTOMap)) {
            throw new BusinessException("Sku不存在！");
        }

        List<ProductSourceCodeInfoDTO> productSourceCodeInfoDTOS = new ArrayList<>();
        List<Integer> matchedIndexList = new ArrayList<>();
        // 遍历DB中的溯源码 （基于sql再过滤DB溯源码，筛选出与request相同产品的DB溯源码，一比一关系）
        dataSourceCodeInfoList.forEach(dbCode -> {
            // DB溯源码的sku产品信息
            ProductSkuDTO dbSku = skuDTOMap.get(dbCode.getProductSkuId());
            if (dbSku == null) {
                return;
            }
            // 遍历request中的溯源码
            for (int ind = 0; ind < productSourceCodeCheckDTOS.size(); ind++) {
                // request溯源码已经匹配到DB溯源码，无需再匹配
                if (matchedIndexList.contains(ind)) {
                    continue;
                }
                ProductSourceCodeCheckDTO reqCode = productSourceCodeCheckDTOS.get(ind);
                // request溯源码的sku产品信息
                ProductSkuDTO reqSku = skuDTOMap.get(reqCode.getProductSkuId());
                // 根据仓库+Code+规格+货主，遍历请求溯源码，匹配到DB溯源码 注：一种内配场景的请求：二级仓ID、二级仓skuID、二级仓code 数据库的溯源码：二级仓ID、中心仓skuID、二级仓code
                if (reqSku != null && Objects.equals(dbCode.getWarehouseId(), reqCode.getWarehouseId())
                    && Objects.equals(dbCode.getProductSourceCode(), reqCode.getSourceCode())
                    && Objects.equals(dbSku.getProductSpecificationId(), reqSku.getProductSpecificationId())
                    && Objects.equals(dbSku.getCompany_Id(), reqSku.getCompany_Id())) {

                    dbCode.setProductSkuId(reqCode.getProductSkuId());
                    productSourceCodeInfoDTOS.add(dbCode);
                    matchedIndexList.add(ind);
                    // DB溯源码已经匹配到request溯源码，无需再匹配
                    break;
                }
            }
        });

        LOGGER.info("溯源码校验-根据SKU匹配完的结果：{}", JSON.toJSONString(productSourceCodeInfoDTOS));

        if (CollectionUtils.isEmpty(productSourceCodeInfoDTOS)) {
            throw new BusinessException("溯源码不存在！");
        }

        Set<String> notExistCodes = new HashSet<>();
        Map<String, Integer> errCodes = new HashMap<>(16);

        // 数据库中的溯源码（一个商品的溯源码：仓库id + skuId + 溯源码名称）
        Map<String, List<ProductSourceCodeInfoDTO>> productSourceCodeMap =
            productSourceCodeInfoDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s",
                elem.getWarehouseId(), elem.getProductSkuId(), elem.getProductSourceCode())));
        // 请求参数的溯源码
        Map<String, List<ProductSourceCodeCheckDTO>> productSourceCodeCheckMap =
            productSourceCodeCheckDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s",
                elem.getWarehouseId(), elem.getProductSkuId(), elem.getSourceCode())));

        List<ProductSourceCodeInfoDTO> checkResult = new ArrayList<>();
        productSourceCodeCheckMap.forEach((sourceCodeKey, checkList) -> {
            // 根据请求的溯源码获取数据库中的溯源码
            List<ProductSourceCodeInfoDTO> sourceCodePOS = productSourceCodeMap.get(sourceCodeKey);
            // 获取不到溯源码，则溯源码不存在
            if (CollectionUtils.isEmpty(sourceCodePOS)) {
                notExistCodes.add(checkList.get(0).getSourceCode());
                return;
            }

            // 校验溯源码数量
            if (checkList.size() > sourceCodePOS.size()) {
                errCodes.put(checkList.get(0).getSourceCode(), checkList.size() - sourceCodePOS.size());
            } else {
                checkResult.addAll(sourceCodePOS.subList(0, checkList.size()));
            }
        });

        if (!CollectionUtils.isEmpty(notExistCodes)) {
            throw new BusinessException("下列溯源码在系统中不存在：[" + StringUtils.join(notExistCodes, ",") + "]");
        }

        if (!CollectionUtils.isEmpty(errCodes)) {
            StringBuilder errMessages = new StringBuilder("溯源码重复使用!\n");
            errCodes.forEach((productSourceCodeId, count) -> {
                errMessages.append(String.format("溯源码：[%s],重复使用%s个 %n", productSourceCodeId, count));
            });

            throw new BusinessValidateException(errMessages.toString());
        }

        return checkResult;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateProductSourceCodeInfo(List<UpdateProductSourceCodeDTO> updateProductSourceCodeDTOS) {
        List<Long> productSourceCodeIds = updateProductSourceCodeDTOS.stream()
            .map(UpdateProductSourceCodeDTO::getProductSourceCodeId).distinct().collect(Collectors.toList());
        List<ProductSourceCodePO> oldCodePOS =
            productSourceCodePOMapper.getProductSourceByIdList(productSourceCodeIds, null);
        if (CollectionUtils.isEmpty(oldCodePOS)) {
            throw new BusinessException("修改失败，溯源码不存在！");
        }
        Set<String> notExistCodes = new HashSet<>();
        Map<Long, List<ProductSourceCodePO>> oldProductSourceCodeMap =
            oldCodePOS.stream().collect(Collectors.groupingBy(ProductSourceCodePO::getId));
        Map<Long, List<UpdateProductSourceCodeDTO>> updateProductSourceCodeMap = updateProductSourceCodeDTOS.stream()
            .collect(Collectors.groupingBy(UpdateProductSourceCodeDTO::getProductSourceCodeId));
        for (Map.Entry<Long, List<UpdateProductSourceCodeDTO>> entry : updateProductSourceCodeMap.entrySet()) {
            Long productSourceCodeId = entry.getKey();
            List<ProductSourceCodePO> sourceCodePOS = oldProductSourceCodeMap.get(productSourceCodeId);
            if (CollectionUtils.isEmpty(sourceCodePOS)) {
                notExistCodes.add(String.valueOf(productSourceCodeId));
                continue;
            }
        }
        if (!CollectionUtils.isEmpty(notExistCodes)) {
            throw new BusinessException("下列溯源码ID在系统中不存在：[" + StringUtils.join(notExistCodes, ",") + "]");
        }

        List<Integer> operateUserIds =
            updateProductSourceCodeDTOS.stream().map(UpdateProductSourceCodeDTO::getOperateUserId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Integer, AdminUser> userInfoMap =
            CollectionUtils.isEmpty(operateUserIds) ? null : iAdminUserQueryService.findUserByIds(operateUserIds);

        List<ProductSourceCodePO> productSourceCodePOS = new ArrayList<>();
        updateProductSourceCodeDTOS.forEach(updateSourceCode -> {
            // 1、修改溯源码
            ProductSourceCodePO productSourceCodePO = new ProductSourceCodePO();
            productSourceCodePO.setId(updateSourceCode.getProductSourceCodeId());
            productSourceCodePO.setWarehouseId(updateSourceCode.getWarehouseId());

            AdminUser userInfo = userInfoMap == null ? null : userInfoMap.get(updateSourceCode.getOperateUserId());
            if (userInfo != null) {
                productSourceCodePO.setLastUpdateUser(
                    userInfo.getTrueName() != null ? userInfo.getTrueName() : userInfo.getUserName());
            }

            if (!updateSourceCode.getNeedToChangeWarehouse()) {
                productSourceCodePO.setWarehouseId(null);
            }
            Byte state = Objects.equals(updateSourceCode.getBusinessType(),
                ProductSourceCodeRecordBusinessTypeEnum.销售订单.getType()) ? ProductSourceCodeStateEnum.已使用.getType()
                    : ProductSourceCodeStateEnum.未使用.getType();
            productSourceCodePO.setState(state);
            productSourceCodePO.setBusinessId(updateSourceCode.getBusinessId());
            productSourceCodePO.setBusinessNo(updateSourceCode.getBusinessNo());
            productSourceCodePOS.add(productSourceCodePO);
        });
        if (!CollectionUtils.isEmpty(productSourceCodePOS)) {
            productSourceCodePOMapper.batchUpdateProductSourceCode(productSourceCodePOS);
            LOGGER.info("修改溯源码: {}", JSON.toJSONString(productSourceCodePOS));
        }
    }

    public List<ProductSourceCodeInfoDTO>
        checkProductSourceIdNew(List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS) {
        Set<String> checkSourceCode = productSourceCodeCheckDTOS.stream().map(ProductSourceCodeCheckDTO::getSourceCode)
            .collect(Collectors.toSet());
        // 1、查溯源码信息（仓库+溯源码维度）,不限制状态
        List<ProductSourceCodeInfoDTO> dataSourceCodeInfoList =
            productSourceCodePOMapper.getProductSourceByCodeInfoList(productSourceCodeCheckDTOS, null);
        if (CollectionUtils.isEmpty(dataSourceCodeInfoList)) {
            throw new BusinessException("以下溯源码没有登记入库，不能使用：[" + StringUtils.join(checkSourceCode, ",") + "]");
        }
        LOGGER.info("溯源码校验-查询DB结果：{}", JSON.toJSONString(dataSourceCodeInfoList));
        // 2、查询sku信息（请求的sku、DB溯源码的sku）
        List<Long> skuIdList = new ArrayList<>();
        skuIdList.addAll(productSourceCodeCheckDTOS.stream().map(ProductSourceCodeCheckDTO::getProductSkuId).distinct()
            .collect(Collectors.toList()));
        skuIdList.addAll(dataSourceCodeInfoList.stream().map(ProductSourceCodeInfoDTO::getProductSkuId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, ProductSkuDTO> skuDTOMap = productSkuQueryBL.getProductSkuMapBySkuIds(skuIdList);
        if (CollectionUtils.isEmpty(skuDTOMap)) {
            throw new BusinessException("Sku不存在！");
        }

        List<ProductSourceCodeInfoDTO> productSourceCodeInfoDTOS = new ArrayList<>();
        List<ProductSourceCodeInfoDTO> inValidProductSourceCodeInfoDTOS = new ArrayList<>();
        productSourceCodeCheckDTOS.stream().forEach(reqCode -> {
            // request溯源码的sku产品信息
            ProductSkuDTO reqSku = skuDTOMap.get(reqCode.getProductSkuId());
            if (reqSku == null) {
                return;
            }

            if (CollectionUtils.isEmpty(dataSourceCodeInfoList)) {
                return;
            }

            List<ProductSourceCodeInfoDTO> sameCodeList = dataSourceCodeInfoList.stream()
                .filter(dbCode -> Objects.equals(dbCode.getWarehouseId(), reqCode.getWarehouseId())
                    && Objects.equals(dbCode.getProductSourceCode(), reqCode.getSourceCode())
                    && Objects.equals(dbCode.getProductSpecificationId(), reqSku.getProductSpecificationId())
                    && Objects.equals(dbCode.getCompanyId(), reqSku.getCompany_Id()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sameCodeList)) {
                return;
            }

            List<ProductSourceCodeInfoDTO> validCodeList = sameCodeList.stream()
                .filter(codeInfo -> Objects.equals(ProductSourceCodeStateEnum.未使用.getType(), codeInfo.getState()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(validCodeList)) {
                ProductSourceCodeInfoDTO dbCodeInfoDTO = validCodeList.get(0);
                dbCodeInfoDTO.setProductSkuId(reqCode.getProductSkuId());
                productSourceCodeInfoDTOS.add(dbCodeInfoDTO);
                dataSourceCodeInfoList
                    .removeIf(p -> Objects.equals(dbCodeInfoDTO.getProductSourceCodeId(), p.getProductSourceCodeId()));
            }

            List<ProductSourceCodeInfoDTO> inValidCodeList = sameCodeList.stream()
                .filter(codeInfo -> Objects.equals(ProductSourceCodeStateEnum.已使用.getType(), codeInfo.getState()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(inValidCodeList)) {
                ProductSourceCodeInfoDTO dbCodeInfoDTO = inValidCodeList.get(0);
                dbCodeInfoDTO.setProductSkuId(reqCode.getProductSkuId());
                inValidProductSourceCodeInfoDTOS.add(dbCodeInfoDTO);
                dataSourceCodeInfoList
                    .removeIf(p -> Objects.equals(dbCodeInfoDTO.getProductSourceCodeId(), p.getProductSourceCodeId()));
            }
        });

        LOGGER.info("溯源码校验-根据SKU匹配完的有效结果：{}，无效结果：{}", JSON.toJSONString(productSourceCodeInfoDTOS),
            JSON.toJSONString(inValidProductSourceCodeInfoDTOS));
        if (CollectionUtils.isEmpty(productSourceCodeInfoDTOS)
            && CollectionUtils.isEmpty(inValidProductSourceCodeInfoDTOS)) {
            // throw new BusinessException("相同产品溯源码不存在！");
            throw new BusinessException("以下溯源码没有登记入库，不能使用：[" + StringUtils.join(checkSourceCode, ",") + "]");
        }

        Set<String> notExistCodes = new HashSet<>();
        Set<String> invalidCodes = new HashSet<>();
        Map<String, Integer> errCodes = new HashMap<>(16);

        // 数据库中的溯源码（一个商品的溯源码：仓库id + skuId + 溯源码名称）
        Map<String, List<ProductSourceCodeInfoDTO>> productSourceCodeMap =
            productSourceCodeInfoDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s",
                elem.getWarehouseId(), elem.getProductSkuId(), elem.getProductSourceCode())));
        // 请求参数的溯源码
        Map<String, List<ProductSourceCodeCheckDTO>> productSourceCodeCheckMap =
            productSourceCodeCheckDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s",
                elem.getWarehouseId(), elem.getProductSkuId(), elem.getSourceCode())));
        // 数据库中的无效溯源码
        Map<String, List<ProductSourceCodeInfoDTO>> invalidProductSourceCodeMap =
            inValidProductSourceCodeInfoDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s",
                elem.getWarehouseId(), elem.getProductSkuId(), elem.getProductSourceCode())));

        List<ProductSourceCodeInfoDTO> checkResult = new ArrayList<>();
        productSourceCodeCheckMap.forEach((sourceCodeKey, checkList) -> {
            // 根据请求的溯源码获取数据库中的溯源码
            List<ProductSourceCodeInfoDTO> sourceCodePOS = productSourceCodeMap.get(sourceCodeKey);
            // 无效溯源码
            List<ProductSourceCodeInfoDTO> invalidSourceCodePOS = invalidProductSourceCodeMap.get(sourceCodeKey);
            // 获取不到溯源码，则溯源码不存在
            if (CollectionUtils.isEmpty(sourceCodePOS)) {
                if (!CollectionUtils.isEmpty(invalidSourceCodePOS)) {
                    invalidCodes.add(checkList.get(0).getSourceCode());
                } else {
                    notExistCodes.add(checkList.get(0).getSourceCode());
                }

                return;
            }

            // 校验溯源码数量
            if (checkList.size() > sourceCodePOS.size()) {
                if (!CollectionUtils.isEmpty(invalidSourceCodePOS)) {
                    invalidCodes.add(checkList.get(0).getSourceCode());
                } else {
                    errCodes.put(checkList.get(0).getSourceCode(), checkList.size() - sourceCodePOS.size());
                }
            } else {
                checkResult.addAll(sourceCodePOS.subList(0, checkList.size()));
            }
        });

        if (!CollectionUtils.isEmpty(notExistCodes)) {
            throw new BusinessException("以下溯源码没有登记入库，不能使用：[" + StringUtils.join(notExistCodes, ",") + "]");
        }

        if (!CollectionUtils.isEmpty(invalidCodes)) {
            throw new BusinessException("以下溯源码已经登记出库，不能重复使用：[" + StringUtils.join(invalidCodes, ",") + "]");
        }

        if (!CollectionUtils.isEmpty(errCodes)) {
            StringBuilder errMessages = new StringBuilder("溯源码重复使用!\n");
            errCodes.forEach((productSourceCodeId, count) -> {
                errMessages.append(String.format("溯源码：[%s],重复使用%s个 %n", productSourceCodeId, count));
            });

            throw new BusinessValidateException(errMessages.toString());
        }

        return checkResult;
    }
}
