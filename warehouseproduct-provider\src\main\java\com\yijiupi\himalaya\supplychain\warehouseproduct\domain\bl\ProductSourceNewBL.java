package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSourceConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodePOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/8/3 18:04
 */
@Service
public class ProductSourceNewBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSourceNewBL.class);

    @Autowired
    private ProductSourceCodePOMapper productSourceCodePOMapper;

    @Autowired
    private ProductControlConfigBL productControlConfigBL;

    @Autowired
    private ProductSourceCodeRecordPOMapper productSourceCodeRecordPOMapper;

    @Reference
    private IAdminUserQueryService iAdminUserQueryService;

    /**
     * 修改溯源码的状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyProductSourceCodeState(Long codeId, Byte state, String operator) {
        AssertUtils.notNull(codeId, "溯源码id不能为空");
        AssertUtils.notNull(state, "溯源码状态不能为空");

        ProductSourceCodePO oldProductSourceCodePO = productSourceCodePOMapper.selectByPrimaryKey(codeId);

        if (oldProductSourceCodePO == null) {
            throw new BusinessException("溯源码不存在，溯源码ID：" + codeId);
        }

        // 修改溯源码状态
        productSourceCodePOMapper.updateProductSourceState(codeId, state, operator);
        LOGGER.info("[修改溯源码的状态]溯源码ID：{}，状态：{}", codeId, ProductSourceCodeStateEnum.getEnmuName(state));

        List<ProductSourceCodeDTO> productSourceCodeDTOS = ProductSourceConvertor
            .productSourceCodePOS2ProductSourceCodeDTOS(Collections.singletonList(oldProductSourceCodePO));
        List<ProductSourceCodeRecordDTO> productSourceCodeRecordDTOS =
            ProductSourceConvertor.productSourceCodeDTOS2ProductSourceCodeRecordDTOS(productSourceCodeDTOS);
        productSourceCodeRecordDTOS.forEach(elem -> {
            elem.setAfterState(Objects.equals(state, ProductSourceCodeStateEnum.未使用.getType())
                ? ProductSourceCodeRecordStateEnum.在库.getType() : ProductSourceCodeRecordStateEnum.出库.getType());
            elem.setCreateUser(operator);
            elem.setRemark("手动更改状态");
            elem.setBusinessType(ProductSourceCodeRecordBusinessTypeEnum.手动修改.getType());
            elem.setBusinessId(null);
            elem.setBusinessNo(null);
        });

        saveProductSourceCodeRecord(productSourceCodeRecordDTOS, false, true, false);

    }

    /**
     * 新增一条溯源码记录
     *
     * @param recordDTOS 原新增的记录信息
     * @param isQueryCode 是否采用数据库中溯源码信息
     * @param isQueryControl 是否采用数据库中的控火策略信息
     */
    private void saveProductSourceCodeRecord(List<ProductSourceCodeRecordDTO> recordDTOS, boolean isQueryCode,
        boolean isQueryControl, boolean isQueryBusiness) {
        AssertUtils.notEmpty(recordDTOS, "新增溯源码记录参数不能为空");
        recordDTOS.forEach(recordDTO -> {
            AssertUtils.notNull(recordDTO.getProductSourceCodeId(), "溯源码ID不能为空");
            AssertUtils.notNull(recordDTO.getAfterState(), "变更后状态不能为空");
            AssertUtils.notNull(recordDTO.getBusinessType(), "单据类型不能为空");
        });

        List<Long> codeIds = recordDTOS.stream().map(ProductSourceCodeRecordDTO::getProductSourceCodeId).distinct()
            .collect(Collectors.toList());

        Map<Long, ProductSourceCodePO> codeMap = null;
        List<ProductSourceCodePO> productSourceCodePOS = null;

        // 查找溯源码
        if (isQueryCode || isQueryBusiness) {
            productSourceCodePOS = productSourceCodePOMapper.getProductSourceByCodeIds(codeIds);
            if (CollectionUtils.isEmpty(productSourceCodePOS)) {
                throw new BusinessException("查不到溯源码：" + JSON.toJSONString(codeIds));
            }
            codeMap = productSourceCodePOS.stream()
                .collect(Collectors.toMap(ProductSourceCodePO::getId, Function.identity()));
        }

        Map<Long, ProductControlConfigDTO> controlConfigMap = null;
        // 查找控货策略
        if (isQueryControl) {
            Set<Long> configIds = recordDTOS.stream().map(ProductSourceCodeRecordDTO::getConfigId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(configIds) && isQueryCode) {
                configIds.addAll(productSourceCodePOS.stream().map(ProductSourceCodePO::getConfigId)
                    .filter(Objects::nonNull).collect(Collectors.toSet()));
            }
            List<ProductControlConfigDTO> productControlConfigDTOS =
                productControlConfigBL.listProductControlConfigByIds(new ArrayList<>(configIds));
            if (CollectionUtils.isEmpty(productControlConfigDTOS)) {
                throw new BusinessException("查不到控货策略：" + JSON.toJSONString(configIds));
            }
            controlConfigMap = productControlConfigDTOS.stream()
                .collect(Collectors.toMap(ProductControlConfigDTO::getId, Function.identity()));
        }

        List<ProductSourceCodeRecordPO> newRecordPOS = new ArrayList<>();
        for (ProductSourceCodeRecordDTO recordElem : recordDTOS) {
            ProductSourceCodeRecordPO newRecordPO = new ProductSourceCodeRecordPO();
            BeanUtils.copyProperties(recordElem, newRecordPO);
            newRecordPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SOURCE_CODE_RECORD));

            ProductSourceCodePO codeElem = null;
            if (isQueryCode) {
                codeElem = codeMap.get(recordElem.getProductSourceCodeId());
                if (codeElem == null) {
                    LOGGER.info("[溯源码记录新增]溯源码不存在，溯源码ID：{}", recordElem.getProductSourceCodeId());
                    continue;
                }
                newRecordPO.setWarehouseId(codeElem.getWarehouseId());
                newRecordPO.setCode(codeElem.getCode());
            }

            if (isQueryBusiness && recordElem.getBusinessType() != null) {
                codeElem = codeMap.get(recordElem.getProductSourceCodeId());
                if (codeElem == null) {
                    LOGGER.info("[溯源码记录新增]溯源码不存在，溯源码ID：{}", recordElem.getProductSourceCodeId());
                    continue;
                }
                newRecordPO
                    .setBusinessId(codeElem.getBusinessId() == null ? null : codeElem.getBusinessId().toString());
                newRecordPO.setBusinessNo(codeElem.getBusinessNo());
                if (Objects.equals(recordElem.getBusinessType(),
                    ProductSourceCodeRecordBusinessTypeEnum.采集订单.getType())) {
                    newRecordPO.setBusinessId(codeElem.getSourceBusinessId());
                    newRecordPO.setBusinessNo(codeElem.getSourceBusinessNo());
                }
            }

            if (isQueryControl) {
                ProductControlConfigDTO controlConfigElem = null;
                if (recordElem.getConfigId() != null) {
                    controlConfigElem = controlConfigMap.get(recordElem.getConfigId());
                } else if (isQueryCode) {
                    controlConfigElem = controlConfigMap.get(codeElem.getConfigId());
                }
                if (controlConfigElem != null) {
                    newRecordPO.setProviderId(controlConfigElem.getProviderId());
                    newRecordPO.setProvider(controlConfigElem.getProvider());
                } else {
                    LOGGER.info("[溯源码记录新增]控货策略不存在，溯源码ID：{}", recordElem.getProductSourceCodeId());
                }
            }

            newRecordPOS.add(newRecordPO);
        }

        if (!CollectionUtils.isEmpty(newRecordPOS)) {
            Lists.partition(newRecordPOS, 100).forEach(batchRecordPO -> {
                productSourceCodeRecordPOMapper.insertBatch(batchRecordPO);
                LOGGER.info("[新增溯源码记录]结果：{}", JSON.toJSONString(batchRecordPO));
            });
        }
    }

    /**
     * 同步TMS的溯源码变更记录
     */
    public void addProductSourceCodeRecordFromTms(ProductSourceCodeRecordSyncDTO codeRecordSyncDTO) {
        setCodeRecordSyncUserInfo(codeRecordSyncDTO);
        saveProductSourceCodeRecord(
            ProductSourceConvertor.productSourceCodeRecordSyncDTO2ProductSourceCodeRecordDTOS(codeRecordSyncDTO), true,
            true, false);
    }

    /**
     * 根据溯源码查找控货策略
     *
     * @param sourceCodeIds 溯源码ID
     * @return 溯源码code -> 控货策略List
     */
    public Map<Long, ProductControlConfigDTO> getControlConfigBySourceCodeId(List<Long> sourceCodeIds) {
        AssertUtils.notEmpty(sourceCodeIds, "溯源码ID列表不能为空");
        List<ProductSourceCodePO> sourceCodePOList = productSourceCodePOMapper.getProductSourceByCodeIds(sourceCodeIds);
        if (CollectionUtils.isEmpty(sourceCodePOList)) {
            throw new BusinessException("溯源码不存在，溯源码ID：" + JSON.toJSONString(sourceCodeIds));
        }
        List<Long> configIds = sourceCodePOList.stream().map(ProductSourceCodePO::getConfigId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        List<ProductControlConfigDTO> controlConfigDTOList =
            productControlConfigBL.listProductControlConfigByIds(configIds);
        if (CollectionUtils.isEmpty(controlConfigDTOList)) {
            throw new BusinessException("查不到控货策略，控货策略ID：" + JSON.toJSONString(configIds));
        }

        // 根据溯源码ID的configId，拼接出 code -> 控货策略DTO列表
        Map<Long, ProductControlConfigDTO> codeControlMap = new HashMap<>(16);

        sourceCodePOList.forEach(codePO -> {
            Optional<ProductControlConfigDTO> configDTO = controlConfigDTOList.stream()
                .filter(elem -> Objects.equals(codePO.getConfigId(), elem.getId())).findFirst();
            if (configDTO.isPresent()) {
                codeControlMap.put(codePO.getId(), configDTO.get());
            }
        });
        return codeControlMap;
    }

    /**
     * 同步TMS的溯源码变更记录
     */
    public void syncProductSourceCodeTrace(ProductSourceCodeRecordSyncDTO codeRecordSyncDTO) {
        setCodeRecordSyncUserInfo(codeRecordSyncDTO);
        saveProductSourceCodeRecord(
            ProductSourceConvertor.productSourceCodeRecordSyncDTO2ProductSourceCodeRecordDTOS(codeRecordSyncDTO), true,
            true, false);
    }

    /**
     * 设置ProductSourceCodeRecordSyncDTO的user信息
     */
    private void setCodeRecordSyncUserInfo(ProductSourceCodeRecordSyncDTO codeRecordSyncDTO) {
        if (codeRecordSyncDTO.getLastUpdateUser() == null) {
            return;
        }
        Map<Integer, AdminUser> userMap =
            iAdminUserQueryService.findUserByIds(Collections.singletonList(codeRecordSyncDTO.getLastUpdateUser()));
        if (CollectionUtils.isEmpty(userMap)) {
            return;
        }
        AdminUser userInfo = userMap.get(codeRecordSyncDTO.getLastUpdateUser());
        codeRecordSyncDTO
            .setCreateUserName(userInfo.getTrueName() != null ? userInfo.getTrueName() : userInfo.getUserName());
        codeRecordSyncDTO.setLastUpdateUserName(codeRecordSyncDTO.getCreateUserName());
    }
}
