package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSupplierConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSupplierPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierBySpecIdDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierSpecIdDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;

/**
 * 产品供应商BL
 */
@Service
public class ProductSupplierBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSupplierBL.class);

    private static final int LIST_PART_SIZE = 50;

    @Autowired
    private ProductSupplierPOMapper productSupplierPOMapper;

    @Autowired
    private ProductSkuServiceBL productSkuServiceBL;

    /**
     * 批量保存产品供应商
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchSaveProductSupplier(List<ProductSupplierDTO> productSupplierList) {
        LOGGER.info("批量新增产品供应商参数：{}", JSON.toJSONString(productSupplierList));
        AssertUtils.notEmpty(productSupplierList, "新增产品供应商信息不能为空");
        // 校验
        validateProductSupplier(productSupplierList);
        // 填充sku信息
        fillProductInfo(productSupplierList);
        // 去重
        List<ProductSupplierDTO> newAddProductSupplierList = productSupplierList.stream().filter(e -> e != null)
            .collect(collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getUniqueIdentification()))),
                ArrayList::new));
        // 仓库ID
        List<Integer> warehouseIdList =
            newAddProductSupplierList.stream().map(e -> e.getWarehouseId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseIdList)) {
            LOGGER.info(String.format("仓库Id为空！原始数据：{}", JSON.toJSONString(productSupplierList)));
            return;
        }
        // 产品集合
        List<ProductSupplierSpecIdDTO> specInfoList = newAddProductSupplierList.stream().filter(e -> e != null)
            .collect(collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getUniqueSpecIdentification()))),
                ArrayList::new))
            .stream().map(e -> {
                ProductSupplierSpecIdDTO specIdDTO = new ProductSupplierSpecIdDTO();
                specIdDTO.setProductSpecificationId(e.getProductSpecificationId());
                specIdDTO.setOwnerId(e.getOwnerId());
                specIdDTO.setSecOwnerId(e.getSecOwnerId());
                return specIdDTO;
            }).collect(Collectors.toList());
        ProductSupplierBySpecIdDTO supplierQuery = new ProductSupplierBySpecIdDTO();
        supplierQuery.setWarehouseId(warehouseIdList.get(0));
        supplierQuery.setSpecList(specInfoList);
        // 查询现有供应商
        List<ProductSupplierDTO> existProductSupplierList = selectProductSupplierBySpecInfo(supplierQuery);
        // 新旧数据匹配
        preInsertProductSupplier(newAddProductSupplierList, existProductSupplierList);
        // 转化成PO数据
        List<ProductSupplierPO> newProductSupplierList =
            ProductSupplierConvertor.toProductSupplierPO(newAddProductSupplierList);
        LOGGER.info("批量新增产品供应商信息：{}", JSON.toJSONString(newProductSupplierList));
        // 新增信息
        Lists.partition(newProductSupplierList, LIST_PART_SIZE)
            .forEach(part -> productSupplierPOMapper.insertOrUpdateBatchSupplierList(part));
    }

    /**
     * 查询产品供应商
     */
    public List<ProductSupplierDTO> selectProductSupplierBySpecInfo(ProductSupplierBySpecIdDTO queryDTO) {
        LOGGER.info("查询产品供应商参数：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库Id不能为空");
        AssertUtils.notEmpty(queryDTO.getSpecList(), "产品信息不能为空");
        List<ProductSupplierPO> supplierList = new ArrayList<>();
        Lists.partition(queryDTO.getSpecList(), LIST_PART_SIZE).forEach(partSpec -> {
            ProductSupplierBySpecIdDTO partQuery = new ProductSupplierBySpecIdDTO();
            partQuery.setWarehouseId(queryDTO.getWarehouseId());
            partQuery.setSpecList(partSpec);
            List<ProductSupplierPO> partList = productSupplierPOMapper.selectProductSupplierBySpecInfo(partQuery);
            if (CollectionUtils.isNotEmpty(partList)) {
                supplierList.addAll(partList);
            }
        });
        if (CollectionUtils.isEmpty(supplierList)) {
            return Collections.emptyList();
        }
        return ProductSupplierConvertor.toProductSupplierDTO(supplierList);
    }

    private void preInsertProductSupplier(List<ProductSupplierDTO> newAddProductSupplierList,
        List<ProductSupplierDTO> oldProductSupplierList) {
        if (CollectionUtils.isEmpty(oldProductSupplierList)) {
            return;
        }
        Map<String,
            ProductSupplierDTO> existSupplierMap = oldProductSupplierList.stream().filter(e -> e != null)
                .collect(Collectors.toMap(e -> e.getUniqueIdentification(), Function.identity(), (v1,
                    v2) -> v1 != null && Objects.equals(v1.getIsDefault(), ConditionStateEnum.是.getType()) || v2 == null
                        ? v1 : v2));
        newAddProductSupplierList.stream().filter(e -> e != null).forEach(e -> {
            ProductSupplierDTO existDTO = existSupplierMap.get(e.getUniqueIdentification());
            if (existDTO != null) {
                e.setId(existDTO.getId());
                Byte isDefault = ConditionStateEnum.是.getType().equals(existDTO.getIsDefault())
                    || ConditionStateEnum.是.getType().equals(e.getIsDefault()) ? ConditionStateEnum.是.getType()
                        : ConditionStateEnum.否.getType();
                e.setIsDefault(isDefault);
            }
        });
    }

    private void validateProductSupplier(List<ProductSupplierDTO> productSupplierList) {
        boolean hasWarehouseEmpty = productSupplierList.stream().anyMatch(e -> e != null && e.getWarehouseId() == null);
        boolean hasSpecIdEmpty =
            productSupplierList.stream().anyMatch(e -> e != null && e.getProductSpecificationId() == null);
        boolean hasSupplierTypeEmpty =
            productSupplierList.stream().anyMatch(e -> e != null && e.getSupplierType() == null);
        boolean hasSupplierIdEmpty = productSupplierList.stream().anyMatch(e -> e != null && e.getSupplierId() == null);
        AssertUtils.isTrue(!hasWarehouseEmpty, "仓库ID不能为空");
        AssertUtils.isTrue(!hasSpecIdEmpty, "规格ID不能为空");
        AssertUtils.isTrue(!hasSupplierTypeEmpty, "供应商类型不能为空");
        AssertUtils.isTrue(!hasSupplierIdEmpty, "供应商ID不能为空");
    }

    /**
     * 根据SkuId回填产品货主信息
     */
    private void fillProductInfo(List<ProductSupplierDTO> productSupplierList) {
        List<ProductSupplierDTO> needFillProductInfoList = productSupplierList.stream()
            .filter(s -> s != null && s.getProductSkuId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needFillProductInfoList)) {
            return;
        }
        List<Long> skuIdList =
            needFillProductInfoList.stream().map(ProductSupplierDTO::getProductSkuId).collect(Collectors.toList());
        Map<Long, ProductSkuInfoReturnDTO> productSkuInfoMap = productSkuServiceBL.getProductInfoBySkuId(skuIdList);
        if (productSkuInfoMap == null || productSkuInfoMap.isEmpty()) {
            return;
        }
        needFillProductInfoList.forEach(d -> {
            ProductSkuInfoReturnDTO skuInfoReturnDTO = productSkuInfoMap.get(d.getProductSkuId());
            if (skuInfoReturnDTO != null) {
                if (d.getOwnerId() == null) {
                    d.setOwnerId(skuInfoReturnDTO.getCompanyId());
                }
                // 产品信息存在则要用产品二级货主,入库单据中的二级货主可能不是产品[productsku表]二级货主
                d.setSecOwnerId(skuInfoReturnDTO.getSecOwnerId());
            }
        });
    }

}
