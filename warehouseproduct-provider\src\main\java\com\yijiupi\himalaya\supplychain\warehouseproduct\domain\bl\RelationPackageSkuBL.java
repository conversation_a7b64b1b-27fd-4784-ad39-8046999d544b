package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 有package关联的sku的相关操作
 *
 * <AUTHOR>
 * @Date 2021/9/8 9:54
 */
@Service
public class RelationPackageSkuBL {
    /**
     * 在无父子顺序的skuList中，获取当前sku在list中的所有祖先sku，且按父>子排序
     */
    public List<ProductSkuDTO> iterFatherSku(ProductSkuDTO curSku, List<ProductSkuDTO> skuList) {
        List<ProductSkuDTO> fatherSkuList = new ArrayList<>();
        Long curPackagePkgId = curSku.getPackageUnifyPackageId();

        ProductSkuDTO fatherSku =
            skuList.stream().filter(skuElem -> Objects.equals(skuElem.getUnitUnifyPackageId(), curPackagePkgId))
                .findFirst().orElse(null);

        if (fatherSku != null) {
            fatherSkuList.addAll(iterFatherSku(fatherSku, skuList));
            fatherSkuList.add(fatherSku);
        }

        return fatherSkuList;
    }

    /**
     * 在无父子顺序的skuList中，获取当前sku在list中的所有子sku，且按父>子排序
     */
    public List<ProductSkuDTO> iterSonSku(ProductSkuDTO curSku, List<ProductSkuDTO> skuList) {
        List<ProductSkuDTO> sonSkuList = new ArrayList<>();
        Long curUnitPkgId = curSku.getUnitUnifyPackageId();

        ProductSkuDTO sonSku =
            skuList.stream().filter(skuElem -> Objects.equals(skuElem.getPackageUnifyPackageId(), curUnitPkgId))
                .findFirst().orElse(null);

        if (sonSku != null) {
            sonSkuList.add(sonSku);
            sonSkuList.addAll(iterSonSku(sonSku, skuList));
        }

        return sonSkuList;
    }
}
