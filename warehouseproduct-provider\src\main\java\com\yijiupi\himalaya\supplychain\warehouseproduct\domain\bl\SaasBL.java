package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SaasBL {

    @Reference
    private IOrgService iOrgService;

    @Autowired
    private PartnerManagerBl partnerManagerBl;

    public PageList<PartnerManagerDTO> findPartnerManagerByCondition(PartnerManagerDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        if (dto.getStatus() == null) {
            dto.setStatus(new Byte("1"));
        }
        List<Integer> cityIdList = findSelfAndSubOrg(dto.getCityId());
        if (CollectionUtils.isNotEmpty(cityIdList)) {
            dto.setCityId(null);
        } else {
            throw new BusinessException("城市id不能为空");
        }
        dto.setCityIdList(cityIdList);
        return partnerManagerBl.findPartnerManagerByCondition(dto);
    }

    public List<Integer> findSelfAndSubOrg(Integer cityId) {
        List<Integer> cityIdList = new ArrayList<>();
        PageList<OrgDTO> orgDTOPageList = iOrgService.listSelfAndSubById(cityId);
        if (orgDTOPageList != null) {
            if (!org.springframework.util.CollectionUtils.isEmpty(orgDTOPageList.getDataList())) {
                orgDTOPageList.getDataList().stream().forEach(orgDTO -> {
                    cityIdList.add(orgDTO.getId());
                });
            }
        }
        return cityIdList;
    }
}
