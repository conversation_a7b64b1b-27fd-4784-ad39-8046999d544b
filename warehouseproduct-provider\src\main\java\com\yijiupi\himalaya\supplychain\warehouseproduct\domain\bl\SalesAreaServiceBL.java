package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SalesAreaConfigMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SalesAreaItemMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaQueryDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

// import com.yijiupi.himalaya.supplychain.elasticsearch.dto.ProductSkuSyncDTO;
// import com.yijiupi.himalaya.supplychain.elasticsearch.enums.ProductSkuSyncTypeEnum;
// import com.yijiupi.himalaya.supplychain.elasticsearch.service.IProductSkuIndexSyncService;

@Service
public class SalesAreaServiceBL {

    private static final Logger logger = LoggerFactory.getLogger(SalesAreaServiceBL.class);

    @Autowired
    private SalesAreaConfigMapper configMapper;
    @Autowired
    private SalesAreaItemMapper itemMapper;

    // @Reference
    // private IProductSkuIndexSyncService productSkuIndexSyncService;

    /**
     * 新增销售区域配置
     */
    public SalesAreaConfigDTO insertOrUpdate(SalesAreaConfigDTO areaConfigDTO) {
        AssertUtils.notNull(areaConfigDTO, "字段不能为空");
        Long parentId = areaConfigDTO.getId();
        if (parentId == null) {
            areaConfigDTO.setId(UUIDGenerator.getUUID(SalesAreaConfigDTO.class.getName()));
            configMapper.insert(areaConfigDTO);
            parentId = areaConfigDTO.getId();
        } else {
            configMapper.update(areaConfigDTO);
        }
        if (CollectionUtils.isNotEmpty(areaConfigDTO.getItems())) {
            for (SalesAreaItemDTO dto : areaConfigDTO.getItems()) {
                dto.setParentId(parentId);
            }
        } else {
            processESCacheById(parentId);
        }

        return configMapper.selectAllById(parentId);
    }

    /**
     * 销售范围变更，触发ES同步
     *
     * @param parentId
     */
    private void processESCacheById(Long parentId) {
        // ProductSkuSyncDTO dto = new ProductSkuSyncDTO();
        // dto.setSaleAreaConfigId(parentId);
        // productSkuIndexSyncService.syncProductSkuIndex(dto);
    }

    /**
     * 新增销售区域配置-子表
     */
    public List<SalesAreaItemDTO> insertItemOrUpdateItem(List<SalesAreaItemDTO> items) {
        AssertUtils.notEmpty(items, "配置项不能为空");
        items.forEach(p -> {
            AssertUtils.notNull(p.getParentId(), "父节点Id不能为空");
        });
        if (CollectionUtils.isNotEmpty(items)) {
            List<SalesAreaItemDTO> lstNoIds =
                items.stream().filter(p -> p.getId() == null).collect(Collectors.toList());
            List<SalesAreaItemDTO> lstHasIds =
                items.stream().filter(p -> p.getId() != null).collect(Collectors.toList());
            // 有Id的，做更新操作
            if (CollectionUtils.isNotEmpty(lstHasIds)) {
                itemMapper.insertUpdateBatch(lstHasIds);
            }
            // 没有Id的，统一新增
            if (CollectionUtils.isNotEmpty(lstNoIds)) {
                lstNoIds.forEach(p -> p.setId(UUIDGenerator.getUUID(SalesAreaItemDTO.class.getName())));
                itemMapper.insertBatch(lstNoIds);
            }
            processESCacheById(items.get(0).getParentId());
        }
        List<Long> parentIds = items.stream().map(SalesAreaItemDTO::getParentId).collect(Collectors.toList());
        return itemMapper.selectByParentIds(parentIds);
    }

    /**
     * 查询销售区域配置(仓库Id，企业编码)
     */
    public PageList<SalesAreaConfigDTO> selectPageList(SalesAreaQueryDTO query) {
        PageResult<SalesAreaConfigDTO> salesAreaConfigDTOS =
            configMapper.selectByWarehouseId(query, query.getCurrentPage(), query.getPageSize());
        PageList<SalesAreaConfigDTO> dtoPageList = salesAreaConfigDTOS.toPageList();
        if (dtoPageList != null) {
            dtoPageList.setDataList(processConfigItems(dtoPageList.getDataList()));
        }
        return dtoPageList;
    }

    /**
     * 根据条件查询销售范围
     *
     * @return
     */
    public List<SalesAreaConfigDTO> selectByIds(List<Long> ids) {
        List<SalesAreaConfigDTO> salesAreaConfigDTOS = configMapper.selectByIds(ids);
        salesAreaConfigDTOS = processConfigItems(salesAreaConfigDTOS);
        return salesAreaConfigDTOS;
    }

    /**
     * 根据主单信息，填充子项
     *
     * @param salesAreaConfigDTOS
     * @return
     */
    private List<SalesAreaConfigDTO> processConfigItems(List<SalesAreaConfigDTO> salesAreaConfigDTOS) {
        if (CollectionUtils.isNotEmpty(salesAreaConfigDTOS)) {
            List<Long> lstParentIds = salesAreaConfigDTOS.stream().map(p -> p.getId()).collect(Collectors.toList());
            List<SalesAreaItemDTO> itemDTOS = itemMapper.selectByParentIds(lstParentIds);
            if (CollectionUtils.isNotEmpty(itemDTOS)) {
                Map<Long, List<SalesAreaItemDTO>> itemMap =
                    itemDTOS.stream().collect(Collectors.groupingBy(p -> p.getParentId()));
                salesAreaConfigDTOS.forEach(p -> {
                    List<SalesAreaItemDTO> tmpItems = itemMap.get(p.getId());
                    if (CollectionUtils.isNotEmpty(tmpItems)) {
                        p.setItems(tmpItems);
                    }
                });
            }
        }
        return salesAreaConfigDTOS;
    }

    /**
     * 根据条件查询销售范围-子表
     *
     * @return
     */
    public List<SalesAreaItemDTO> selectItemsByParentId(Long parentId) {
        return itemMapper.selectByParentIds(Collections.singletonList(parentId));
    }

    /**
     * 根据条件删除销售范围
     */
    public void deleteConfigByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "ID不能为空");
        List<SalesAreaConfigDTO> salesAreaConfigDTOS = selectByIds(ids);
        configMapper.deleteByIds(ids);
        itemMapper.deleteParentIds(ids);

        // salesAreaConfigDTOS.forEach(salesAreaConfigDTO -> {
        // ProductSkuSyncDTO dto = new ProductSkuSyncDTO();
        // dto.setWarehouseId(salesAreaConfigDTO.getWarehouseId());
        // dto.setSyncType(ProductSkuSyncTypeEnum.批量删除产品索引.getType());
        // productSkuIndexSyncService.syncProductSkuIndex(dto);
        // });
    }

    /**
     * 根据条件删除销售范围
     */
    public void deleteItemByIds(Long configId, List<Long> ids) {
        AssertUtils.notNull(configId, "configID不能为空");
        AssertUtils.notEmpty(ids, "ID不能为空");
        SalesAreaConfigDTO salesAreaConfigDTO = configMapper.selectById(configId);
        itemMapper.deleteByIds(ids);

        // ProductSkuSyncDTO dto = new ProductSkuSyncDTO();
        // dto.setWarehouseId(salesAreaConfigDTO.getWarehouseId());
        // dto.setSyncType(ProductSkuSyncTypeEnum.批量删除产品索引.getType());
        // productSkuIndexSyncService.syncProductSkuIndex(dto);
        //// processESCacheById(configId);
    }

    /**
     * 根据条件，启用/禁用销售范围
     */
    public void updateConfigState(List<Long> ids, Byte state) {
        configMapper.updateState(ids, state);
    }

    /**
     * 根据条件，启用/禁用销售范围
     */
    public void updateItemState(List<Long> ids, Byte state) {
        itemMapper.updateStateByIds(ids, state);
    }

    /**
     * 查询销售区域配置(仓库Id，企业编码)-子表
     * 
     * @param query
     */
    public PageList<SalesAreaItemDTO> pageListSalesAreaItems(SalesAreaItemQueryDTO query) {
        PageResult<SalesAreaItemDTO> result =
            itemMapper.pageListSalesAreaItems(query, query.getPageNum(), query.getPageSize());
        return result.toPageList();
    }
}
