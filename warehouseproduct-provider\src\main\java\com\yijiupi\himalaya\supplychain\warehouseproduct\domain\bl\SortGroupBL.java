package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.SortGroupConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupSettingMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupUserMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.EnableStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.DateUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.PageHelperUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 分区
 *
 * <AUTHOR>
 * @date 2018/5/15 19:26
 */
@Service
public class SortGroupBL {

    @Autowired
    private SortGroupMapper sortGroupMapper;

    @Autowired
    private SortGroupUserMapper sortGroupUserMapper;

    @Autowired
    private SortGroupSettingMapper sortGroupSettingMapper;

    // @Autowired
    // private ProductLocationBL productLocationBL;

    @Autowired
    private SortGroupSettingBL sortGroupSettingBL;

    @Autowired
    private LocationPOMapper locationPOMapper;

    @Autowired
    private AisleBL aisleBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(SortGroupBL.class);

    /**
     * 获取分区列表
     */
    public PageList<SortGroupListDTO> listGroup(SortGroupSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库Id不能为空");
        AssertUtils.notNull(so.getFlag(), "分区标识不能为空");
        PageResult<SortGroupListPO> poPageResult = sortGroupMapper.listGroupByCondition(so);
        List<SortGroupListDTO> dtoList = poPageResult.toPageList().getDataList().stream()
            .map(SortGroupConvertor::convertToSortGroupListDTO).collect(Collectors.toList());
        // 封装分页列表对象
        PageList<SortGroupListDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(poPageResult.toPageList().getPager());
        dtoPageList.setDataList(dtoList);
        return dtoPageList;
    }

    /**
     * 查看分区
     *
     * @param id 分区 id
     */
    public SortGroupDTO getGroup(Long id) {
        // 1、获取分区详情
        SortGroupPO sortGroupPO = sortGroupMapper.selectByPrimaryKey(id);
        SortGroupDTO sortGroupDTO = SortGroupConvertor.convertToSortGroupDTO(sortGroupPO);
        // 2、获取分区下所有类别
        List<SortGroupSettingPO> settingPOList = pageListByGroupId(Collections.singleton(id));
        if (null != settingPOList) {
            List<SortGroupSettingDTO> settingDTOList = settingPOList.stream()
                .map(SortGroupConvertor::convertToSortGroupSettingDTO).collect(Collectors.toList());
            // 填充货位巷道id和编号
            fillLocationAisle(settingDTOList);
            sortGroupDTO.setGroupSettingList(settingDTOList);
        }
        return sortGroupDTO;
    }

    /**
     * 根据条件查询分区信息
     *
     * @param so 查询条件
     */
    public List<SortGroupDTO> findSortGroupTotalList(SortGroupSO so) {
        LOGGER.info("查询分区集合参数：{}", JSON.toJSONString(so));
        AssertUtils.notNull(so.getWarehouseId(), "仓库Id不能为空");
        AssertUtils.notNull(so.getFlag(), "分区标识不能为空");
        // 1、获取分区详情
        List<SortGroupPO> sortGroupPOList = sortGroupMapper.selectBySortGroupDTOList(so);
        if (CollectionUtils.isEmpty(sortGroupPOList)) {
            return Collections.emptyList();
        }
        List<Long> groupIdList =
            sortGroupPOList.stream().filter(Objects::nonNull).map(SortGroupPO::getId).collect(Collectors.toList());
        // 2、获取分区下所有类别
        Map<Long, List<SortGroupSettingPO>> settingPOGroupMap = pageListByGroupId(groupIdList).stream()
            .filter(Objects::nonNull).collect(Collectors.groupingBy(SortGroupSettingPO::getSortGroupId));
        // 组装结果
        List<SortGroupDTO> result = new ArrayList<>();
        for (SortGroupPO po : sortGroupPOList) {
            if (po == null) {
                continue;
            }
            SortGroupDTO sortGroupDTO = SortGroupConvertor.convertToSortGroupDTO(po);
            // 转化 setting
            List<SortGroupSettingPO> settingPOS = settingPOGroupMap.get(po.getId());
            if (CollectionUtils.isNotEmpty(settingPOS)) {
                List<SortGroupSettingDTO> settingDTOList = settingPOS.stream().filter(Objects::nonNull)
                    .map(SortGroupConvertor::convertToSortGroupSettingDTO).collect(Collectors.toList());
                sortGroupDTO.setGroupSettingList(settingDTOList);
            }
            result.add(sortGroupDTO);
        }
        return result;
    }

    /**
     * 新增分区
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Long insertGroup(SortGroupDTO dto) {
        SortGroupPO groupPO = SortGroupConvertor.convertToSortGroupPO(dto);
        if (null == groupPO) {
            return null;
        }
        groupPO.setId(UUIDGenerator.getUUID(SortGroupPO.class.getName()));
        // 1、新增分区
        sortGroupMapper.insert(groupPO);
        Long groupId = groupPO.getId();
        if (null == groupId) {
            return null;
        }

        // 2、新增分区类别
        List<SortGroupSettingDTO> settingDTOList = dto.getGroupSettingList();
        if (CollectionUtils.isNotEmpty(settingDTOList)) {
            List<SortGroupSettingPO> settingPOList = new ArrayList<>();
            for (SortGroupSettingDTO sortGroupSettingDTO : settingDTOList) {
                SortGroupSettingPO sortGroupSettingPO =
                    SortGroupConvertor.convertToSortGroupSettingPO(sortGroupSettingDTO);
                sortGroupSettingPO.setSortGroupId(groupId);
                sortGroupSettingPO.setId(UUIDGenerator.getUUID(SortGroupSettingPO.class.getName()));
                settingPOList.add(sortGroupSettingPO);
            }
            sortGroupSettingMapper.insertBatch(settingPOList);
        }

        // 3、新增分区人员
        List<SortGroupUserDTO> userDTOList = dto.getGroupUserList();
        if (CollectionUtils.isNotEmpty(userDTOList)) {
            List<SortGroupUserPO> userPOList = new ArrayList<>();
            for (SortGroupUserDTO sortGroupUserDTO : userDTOList) {
                SortGroupUserPO sortGroupUserPO = SortGroupConvertor.convertToSortGroupUserPO(sortGroupUserDTO);
                sortGroupUserPO.setSortGroupId(groupId);
                sortGroupUserPO.setState(EnableStateEnum.ENABLE.getType());
                sortGroupUserPO.setCreateUser(groupPO.getCreateUser());
                sortGroupUserPO.setId(UUIDGenerator.getUUID(SortGroupUserPO.class.getName()));
                userPOList.add(sortGroupUserPO);
            }
            sortGroupUserMapper.insertBatch(userPOList);
        }

        return groupPO.getId();
    }

    /**
     * 修改分区
     *
     * @param dto
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroup(SortGroupDTO dto) {
        SortGroupPO groupPO = SortGroupConvertor.convertToSortGroupPO(dto);
        if (null == groupPO) {
            return;
        }
        // 1、修改分区
        sortGroupMapper.updateByPrimaryKey(groupPO);

        // 2、修改分区类型
        List<SortGroupSettingDTO> settingDTOList = dto.getGroupSettingList();
        if (null != settingDTOList) {
            // (1) 删除该分区下所有类型
            sortGroupSettingMapper.deleteByGroupId(groupPO.getId());

            // (2) 批量新增所有类别
            List<SortGroupSettingPO> settingPOList = new ArrayList<>();
            for (SortGroupSettingDTO sortGroupSettingDTO : settingDTOList) {
                SortGroupSettingPO sortGroupSettingPO =
                    SortGroupConvertor.convertToSortGroupSettingPO(sortGroupSettingDTO);
                sortGroupSettingPO.setSortGroupId(groupPO.getId());
                sortGroupSettingPO.setId(UUIDGenerator.getUUID(SortGroupSettingPO.class.getName()));
                settingPOList.add(sortGroupSettingPO);
            }
            sortGroupSettingMapper.insertBatch(settingPOList);
        }
    }

    /**
     * 删除分区
     *
     * @param id 分区ID
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroup(Long id) {
        // 1、删除分区
        sortGroupMapper.deleteByPrimaryKey(id);
        // 2、删除分区下所有类别
        sortGroupSettingMapper.deleteByGroupId(id);
        // 3、删除分区下所有人员
        sortGroupUserMapper.deleteByGroupId(id);
    }

    /**
     * 获取分区人员列表
     */
    public PageList<SortGroupUserDTO> listGroupUser(SortGroupUserSO so) {
        PageResult<SortGroupUserPO> poPageResult = sortGroupUserMapper.listGroupUserByGroupId(so);
        List<SortGroupUserDTO> dtoList = poPageResult.toPageList().getDataList().stream()
            .map(SortGroupConvertor::convertToSortGroupUserDTO).collect(Collectors.toList());
        // 封装分页列表对象
        PageList<SortGroupUserDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(poPageResult.toPageList().getPager());
        dtoPageList.setDataList(dtoList);
        return dtoPageList;
    }

    /**
     * 查看分区人员
     *
     * @param id
     * @return
     */
    public SortGroupUserDTO getGroupUser(Long id) {
        SortGroupUserPO sortGroupUserPO = sortGroupUserMapper.selectByPrimaryKey(id);
        SortGroupUserDTO sortGroupUserDTO = SortGroupConvertor.convertToSortGroupUserDTO(sortGroupUserPO);
        return sortGroupUserDTO;
    }

    /**
     * 新增分区人员
     *
     * @param dto
     * @return
     */
    public Long insertGroupUser(SortGroupUserDTO dto) {
        SortGroupUserPO sortGroupUserPO = SortGroupConvertor.convertToSortGroupUserPO(dto);
        if (null == sortGroupUserPO) {
            return null;
        }
        sortGroupUserPO.setState(EnableStateEnum.ENABLE.getType());
        sortGroupUserPO.setId(UUIDGenerator.getUUID(SortGroupUserPO.class.getName()));
        sortGroupUserMapper.insert(sortGroupUserPO);
        return sortGroupUserPO.getId();
    }

    /**
     * 修改分区人员
     *
     * @param dto
     * @return
     */
    public void updateGroupUser(SortGroupUserDTO dto) {
        SortGroupUserPO sortGroupUserPO = SortGroupConvertor.convertToSortGroupUserPO(dto);
        if (null == sortGroupUserPO) {
            return;
        }
        sortGroupUserMapper.updateByPrimaryKey(sortGroupUserPO);
    }

    /**
     * 删除分区人员
     */
    public void deleteGroupUser(Long id) {
        sortGroupUserMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据分区设置获取分区人员列表
     */
    public List<SortGroupUserDTO> listGroupUserBySelect(SortGroupUserSelectSO so) {
        LOGGER.info("获取分区人员参数：{}", JSON.toJSONString(so));
        // 获取指定仓库下指定分拣属性的分区人员
        List<SortGroupUserPO> poList = sortGroupUserMapper.listGroupUserByWarehouseId(so);
        // 筛选出符合条件的分区人员
        List<SortGroupUserPO> conformList = this.filterUserList(poList);
        if (conformList == null) {
            return Collections.emptyList();
        }
        List<SortGroupUserDTO> dtoList =
            conformList.stream().map(SortGroupConvertor::convertToSortGroupUserDTO).collect(Collectors.toList());
        // 获取分区下的所有类别
        List<Long> groupIds =
            dtoList.stream().map(SortGroupUserDTO::getSortGroupId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<SortGroupSettingPO> settingPOList = pageListByGroupId(groupIds);
            if (CollectionUtils.isNotEmpty(settingPOList)) {
                Map<Long, List<SortGroupSettingDTO>> settingMap =
                    settingPOList.stream().map(SortGroupConvertor::convertToSortGroupSettingDTO)
                        .collect(Collectors.groupingBy(SortGroupSettingDTO::getSortGroupId));
                dtoList.forEach(p -> p.setSettingDTOList(settingMap.get(p.getSortGroupId())));
            }
        }
        return dtoList;
    }

    /**
     * 筛选出符合条件的分区人员
     *
     * @param list
     * @return
     */
    private List<SortGroupUserPO> filterUserList(List<SortGroupUserPO> list) {
        if (null == list) {
            return null;
        }
        List<SortGroupUserPO> conformList = new ArrayList<>();
        // 获取当前年月日时分秒
        Date nowDateTime = DateUtil.curDateTime();
        // 获取当前年月日
        Date nowDate = DateUtil.curDate();
        // 获取当前时分秒
        Date nowTime = DateUtil.curTime();
        // 获取今天是几号
        int dayOfMonth = DateUtil.getDayOfMonth();
        // 获取今天是星期几
        int dayOfWeek = DateUtil.getDayOfWeek();

        for (SortGroupUserPO po : list) {
            // 判断今天是否在工作开始日期和工作结束日期之间
            if (!DateUtil.betweenDate(nowDate, po.getWorkStartDate(), po.getWorkEndDate())) {
                continue;
            }

            // 判断今天是否在工作日内
            List<String> workDayList = Arrays.asList(po.getWorkDayDetail().split(","));
            // -每周
            if (new Integer("1").equals(po.getWorkDayType())) {
                if (!workDayList.contains(DateUtil.fomcatDayOfWeek(dayOfWeek))) {
                    continue;
                }
                // -每月
            } else if (new Integer("2").equals(po.getWorkDayType())) {
                if (!workDayList.contains(String.valueOf(dayOfMonth))) {
                    continue;
                }
            }

            // 判断当前时间是否在工作开始时段和工作结束时段之间
            Date workStartTime =
                DateUtil.parseDateTime(DateUtil.formatDate(nowDate) + " " + DateUtil.formatTime(po.getWorkStartTime()));
            Date workEndTime =
                DateUtil.parseDateTime(DateUtil.formatDate(nowDate) + " " + DateUtil.formatTime(po.getWorkEndTime()));
            if (!DateUtil.betweenDate(nowDateTime, workStartTime, workEndTime)) {
                continue;
            }
            // 去重，保证分区人员是唯一的
            if (conformList.stream().noneMatch(p -> Objects.equals(p.getUserId(), po.getUserId()))) {
                conformList.add(po);
            }
        }
        return conformList;
    }

    /**
     * 获取分区人员所属的分区id集合
     *
     * @param flag 分区标识 0：分区拣货 1：分区补货 2：分区上架
     * @return 查询结果
     */
    public List<Long> listGroupIdByUserId(Integer userId, Byte flag) {
        return sortGroupUserMapper.listGroupIdByUserId(userId, flag);
    }

    private List<SortGroupSettingPO> pageListByGroupId(Collection<Long> groupIds) {
        LOGGER.info("分页查询分区, 入参: {}", groupIds);
        return PageHelperUtils.splitWithPageQuery(groupIds, list -> sortGroupSettingMapper.newPageListByGroupId(list));
    }

    public List<String> listSortNameByGroupIds(List<Long> groupIds) {
        return sortGroupMapper.selectSortNameByIds(groupIds);
    }

    public List<String> listLocationNameByGroupIds(List<Long> groupIds) {
        LOGGER.info("listLocationNameByGroupIds 入参：{}", JSON.toJSONString(groupIds));
        List<SortGroupSettingPO> sortGroupSettingPOS = sortGroupSettingMapper.listByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(sortGroupSettingPOS)) {
            return Collections.EMPTY_LIST;
        }

        List<String> locationNameList = new ArrayList<>();
        List<String> sortNameList = sortGroupSettingPOS.stream().filter(p -> !StringUtils.isEmpty(p.getSortName()))
            .map(SortGroupSettingPO::getSortName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sortNameList)) {
            locationNameList.addAll(sortNameList);
        }

        List<Long> areaIds = sortGroupSettingPOS.stream()
            .filter(p -> StringUtils.isNumeric(p.getSortId())
                && Objects.equals(p.getSortType().byteValue(), SortType.SORT_AREA))
            .map(p -> Long.valueOf(p.getSortId())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaIds)) {
            return locationNameList;
        }

        List<LocationPO> locationPOS = locationPOMapper.listProductCountList(areaIds);
        if (CollectionUtils.isEmpty(locationPOS)) {
            return locationNameList;
        }

        List<String> areaLocationNames = locationPOS.stream().filter(p -> !StringUtils.isEmpty(p.getName()))
            .map(LocationPO::getName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(areaLocationNames)) {
            locationNameList.addAll(areaLocationNames);
        }

        LOGGER.info("listLocationNameByGroupIds 结果：{}", JSON.toJSONString(areaLocationNames));
        return locationNameList;
    }

    /**
     * 填充货位巷道信息
     *
     * @param settingDTOList
     */
    public void fillLocationAisle(List<SortGroupSettingDTO> settingDTOList) {
        LOGGER.info("填充货位巷道信息参数：{}", JSON.toJSONString(settingDTOList));
        try {
            if (CollectionUtils.isEmpty(settingDTOList)) {
                return;
            }

            List<SortGroupSettingDTO> fillDTOList = settingDTOList.stream()
                .filter(p -> Objects.equals(p.getSortType(), Integer.valueOf(SortType.SORT_LOCATION))
                    && StringUtils.isNotEmpty(p.getSortId()) && StringUtils.isNumeric(p.getSortId()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(fillDTOList)) {
                return;
            }

            List<Long> locationIds =
                fillDTOList.stream().map(p -> Long.valueOf(p.getSortId())).distinct().collect(Collectors.toList());
            List<LoactionDTO> loactionDTOS =
                PageHelperUtils.splitWithPageQuery(locationIds, list -> locationPOMapper.findLocationByIds(list));
            if (CollectionUtils.isEmpty(loactionDTOS)) {
                return;
            }

            Map<String, LoactionDTO> loactionMap = loactionDTOS.stream().filter(p -> p.getId() != null)
                .collect(Collectors.toMap(p -> String.valueOf(p.getId()), Function.identity(), (key1, key2) -> key2));
            if (loactionMap == null || loactionMap.size() <= 0) {
                return;
            }

            fillDTOList.forEach(dto -> {
                LoactionDTO loactionDTO = loactionMap.get(dto.getSortId());
                if (loactionDTO == null) {
                    return;
                }

                dto.setAisleId(loactionDTO.getAisleId());
                dto.setAisleNo(loactionDTO.getAisleNo());
                dto.setAreaId(loactionDTO.getAreaId());
            });
            LOGGER.info("填充货位巷道信息结果：{}", JSON.toJSONString(settingDTOList));
        } catch (Exception e) {
            LOGGER.warn("填充货位巷道信息失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddAisle(List<AisleDTO> addDTOList) {
        if (CollectionUtils.isEmpty(addDTOList)) {
            return;
        }

        addDTOList.stream().forEach(dto -> {
            aisleBL.addAisle(dto);
        });
    }

    /**
     * 启用分区
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void enableSortGroup(SortGroupDTO dto) {
        AssertUtils.notNull(dto.getId(), "分区信息不能为空！");
        AssertUtils.notNull(dto.getCreateUser(), "操作人信息不能为空！");

        SortGroupPO sortGroupPO = sortGroupMapper.selectByPrimaryKey(dto.getId());
        if (Objects.isNull(sortGroupPO)) {
            throw new BusinessValidateException("分区信息不存在！");
        }

        SortGroupPO po = new SortGroupPO();
        po.setId(dto.getId());
        po.setState(ConditionStateEnum.是.getType());
        po.setCreateUser(dto.getCreateUser());
        sortGroupMapper.updateByPrimaryKey(po);
    }

    /**
     * 停用分区
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void disableSortGroup(SortGroupDTO dto) {
        AssertUtils.notNull(dto.getId(), "分区信息不能为空！");
        AssertUtils.notNull(dto.getCreateUser(), "操作人信息不能为空！");

        SortGroupPO sortGroupPO = sortGroupMapper.selectByPrimaryKey(dto.getId());
        if (Objects.isNull(sortGroupPO)) {
            throw new BusinessValidateException("分区信息不存在！");
        }

        SortGroupPO po = new SortGroupPO();
        po.setId(dto.getId());
        po.setState(ConditionStateEnum.否.getType());
        po.setCreateUser(dto.getCreateUser());
        sortGroupMapper.updateByPrimaryKey(po);
    }

}
