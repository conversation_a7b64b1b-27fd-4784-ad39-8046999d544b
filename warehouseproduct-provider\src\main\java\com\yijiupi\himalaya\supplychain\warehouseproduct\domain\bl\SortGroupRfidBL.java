package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.ControlInfoResultDTO;
import com.yijiupi.himalaya.supplychain.dto.SowControlDTO;
import com.yijiupi.himalaya.supplychain.dto.SowControlListSO;
import com.yijiupi.himalaya.supplychain.service.ISowConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.SortGroupRfidConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AislePOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupRfidMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupRfidPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleByAreaQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupRfidTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.PageHelperUtils;

/**
 * 分区标签
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Service
public class SortGroupRfidBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(SortGroupRfidBL.class);

    @Autowired
    private SortGroupRfidMapper sortGroupRfidMapper;

    @Autowired
    private LocationPOMapper locationPOMapper;

    @Autowired
    private LocationAreaPOMapper locationAreaPOMapper;

    @Autowired
    private AislePOMapper aislePOMapper;

    @Autowired
    private SortGroupBL sortGroupBL;

    @Reference(timeout = 30000)
    private IWarehouseQueryService warehouseQueryService;

    @Reference(timeout = 60000)
    private ISowConfigService iSowConfigService;

    /**
     * 全量获取分区标签列表
     *
     * @return
     */
    public List<SortGroupRfidDTO> listSortGroupRfid(SortGroupRfidSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        LOGGER.info("全量获取分区标签列表 入参：{}", JSON.toJSONString(so));
        int pageCount = 1;
        so.setPageSize(3000);
        List<SortGroupRfidDTO> resultList = new ArrayList<>();
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            so.setCurrentPage(pageNum);
            PageResult<SortGroupRfidDTO> pageResult = sortGroupRfidMapper.listSortGroupRfid(so);
            if (pageNum == 1) {
                pageCount = pageResult.getPager().getTotalPage();
            }
            List<SortGroupRfidDTO> result = pageResult.getResult();
            if (!CollectionUtils.isEmpty(result)) {
                resultList.addAll(result);
            }
        }
        // 填充控制器信息
        fillControlInfo(resultList);

        LOGGER.info("全量获取分区标签列表 结果：{}", JSON.toJSONString(resultList));
        return resultList;
    }

    private void fillControlInfo(List<SortGroupRfidDTO> rfidDTOS) {
        if (CollectionUtils.isEmpty(rfidDTOS)) {
            return;
        }

        Integer warehouseId = rfidDTOS.get(0).getWarehouseId();
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse == null) {
            throw new BusinessValidateException("所选仓库不存在!");
        }
        Integer cityId = warehouse.getCityId();

        List<SortGroupRfidDTO> existControlDTOS =
            rfidDTOS.stream().filter(p -> p.getControlId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existControlDTOS)) {
            return;
        }

        SowControlListSO controlSO = new SowControlListSO();
        controlSO.setOrgId(cityId);
        controlSO.setWarehouseId(warehouseId);
        PageList<SowControlDTO> controlPageList = iSowConfigService.listSowControl(controlSO);
        if (controlPageList == null || CollectionUtils.isEmpty(controlPageList.getDataList())) {
            return;
        }

        Map<Long, SowControlDTO> sowControlMap =
            controlPageList.getDataList().stream().filter(p -> p != null && p.getId() != null)
                .collect(Collectors.toMap(p -> p.getId(), Function.identity(), (key1, key2) -> key2));
        existControlDTOS.forEach(dto -> {
            SowControlDTO sowControlDTO = sowControlMap.get(dto.getControlId());
            if (sowControlDTO == null) {
                return;
            }

            dto.setControlPort(sowControlDTO.getPort());
            dto.setControlIpAddress(sowControlDTO.getIpAddress());
            dto.setControlTypeName(sowControlDTO.getControlTypeName());
        });
    }

    /**
     * 分页获取分区标签列表
     *
     * @return
     */
    public PageList<SortGroupRfidDTO> pageListSortGroupRfid(SortGroupRfidSO so) {
        AssertUtils.notNull(so.getWarehouseId(), "仓库id不能为空");
        LOGGER.info("分页获取分区标签列表 入参：{}", JSON.toJSONString(so));
        PageHelper.startPage(so.getCurrentPage(), so.getPageSize());
        PageResult<SortGroupRfidDTO> pageResult = sortGroupRfidMapper.listSortGroupRfid(so);
        LOGGER.info("分页获取分区标签列表 结果：{}", JSON.toJSONString(pageResult));

        PageList<SortGroupRfidDTO> pageList = new PageList<>();
        pageList.setDataList(pageResult.getResult());
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    /**
     * 批量新增分区标签列
     *
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchSaveSortGroupRfid(List<SortGroupRfidDTO> addDTOList) {
        LOGGER.info("批量新增分区标签列参数：{}", JSON.toJSONString(addDTOList));
        AssertUtils.notEmpty(addDTOList, "参数不能为空");
        addDTOList.stream().forEach(addDTO -> {
            AssertUtils.notNull(addDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(addDTO.getRfidType(), "电子标签类型不能为空");
            AssertUtils.notNull(addDTO.getControlId(), "控制器id不能为空");
            AssertUtils.hasText(addDTO.getControlName(), "控制器名称不能为空");
            AssertUtils.notNull(addDTO.getDeviceId(), "设备id不能为空");
            AssertUtils.hasText(addDTO.getDeviceTagNo(), "设备编号不能为空");
            AssertUtils.hasText(addDTO.getDeviceTypeName(), "设备类型名称不能为空");
            AssertUtils.notNull(addDTO.getBusId(), "总线id不能为空");
            AssertUtils.hasText(addDTO.getBusTagNo(), "总线标签号不能为空");
            AssertUtils.hasText(addDTO.getBusTypeName(), "总线类型名称不能为空");
            AssertUtils.notNull(addDTO.getSortId(), "关联项id不能为空");
            AssertUtils.hasText(addDTO.getSortName(), "关联项名称不能为空");
            AssertUtils.hasText(addDTO.getCreateUser(), "操作人不能为空");
        });

        Integer warehouseId = addDTOList.get(0).getWarehouseId();

        List<Long> sortIdList = addDTOList.stream().filter(p -> p.getSortId() != null).map(p -> p.getSortId())
            .distinct().collect(Collectors.toList());

        // 检查货位是否绑定巷道
        List<Long> locationIdList = addDTOList.stream()
            .filter(p -> p.getSortId() != null && Objects.equals(SortGroupRfidTypeEnum.货位.getType(), p.getRfidType()))
            .map(p -> p.getSortId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(locationIdList)) {
            List<LoactionDTO> loactionDTOS =
                PageHelperUtils.splitWithPageQuery(locationIdList, list -> locationPOMapper.findLocationByIds(list));
            if (CollectionUtils.isEmpty(loactionDTOS)) {
                throw new BusinessException("所选货位不存在");
            }
            List<String> noAisleLocationList =
                loactionDTOS.stream().filter(p -> p.getAisleId() == null || StringUtils.isEmpty(p.getAisleNo()))
                    .map(p -> p.getName()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noAisleLocationList)) {
                String errorMsg = noAisleLocationList.stream().collect(Collectors.joining(","));
                throw new BusinessValidateException(String.format("所选货位未绑定巷道,请先进行绑定,货位:%s！", errorMsg));
            }
        }

        // 检查关联id是否存在绑定标签，先删除再新增
        checkAndDeleteBySortIds(warehouseId, addDTOList);

        // 批量新增
        List<SortGroupRfidPO> poList = SortGroupRfidConvertor.convertorToAddPOList(addDTOList);
        Lists.partition(poList, 500).forEach(list -> {
            int count = sortGroupRfidMapper.insertBatch(list);
            if (count == 0) {
                throw new BusinessException("批量新增分区标签列 数据插入失败");
            }
        });

        LOGGER.info("批量新增分区标签列完成");
    }

    /**
     * 批量删除分区标签
     *
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteBatchByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "分区标签id不能为空");
        LOGGER.info("批量删除分区标签参数：{}", ids);
        int count = sortGroupRfidMapper.deleteByPrimaryKeyBatch(ids);
        if (count < 1) {
            throw new BusinessValidateException("批量删除分区标签失败");
        }
    }

    /**
     * 批量修改标签(导入)
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<SortGroupRfidDTO> updateDTOList) {
        LOGGER.info("批量修改标签(导入) 参数：{}", JSON.toJSONString(updateDTOList));
        validateBatchUpdate(updateDTOList);
        // 根据导入编号和名称获取数据,目前导入只支持货位
        updateDTOList.removeIf(p -> !Objects.equals(SortGroupRfidTypeEnum.货位.getType(), p.getRfidType()));
        if (CollectionUtils.isEmpty(updateDTOList)) {
            throw new BusinessValidateException("目前仅支持货位标签类型数据导入!");
        }
        Integer warehouseId = updateDTOList.get(0).getWarehouseId();
        Long sortGroupId = updateDTOList.get(0).getSortGroupId();
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse == null) {
            throw new BusinessValidateException("所选仓库不存在!");
        }
        Integer cityId = warehouse.getCityId();

        // 获取分区货位数据
        Map<String, SortGroupSettingDTO> settingMap = getSettingBySortGroupId(sortGroupId);
        // 过滤所选分区中不存在的货位数据
        Map<String, List<SortGroupRfidDTO>> rfidMap = filterRfidDTOS(updateDTOList, settingMap);
        // 根据控制器名称 + 总线编号 + 设备编号获取控制器相关信息
        Map<String, ControlInfoResultDTO> controlMap = getControlInfo(warehouseId, rfidMap);
        List<SortGroupRfidDTO> addDTOList =
            SortGroupRfidConvertor.convertorToSortGroupRfidDTOList(rfidMap, controlMap, settingMap);
        if (CollectionUtils.isEmpty(addDTOList)) {
            return;
        }
        // 更新货位巷道绑定关系
        updateLocationAisleInfo(cityId, warehouseId, addDTOList);
        // 检查关联id是否存在绑定标签，先删除再新增
        checkAndDeleteBySortIds(warehouseId, addDTOList);
        // 批量新增
        List<SortGroupRfidPO> poList = SortGroupRfidConvertor.convertorToAddPOList(addDTOList);
        Lists.partition(poList, 500).forEach(list -> {
            int count = sortGroupRfidMapper.insertBatch(list);
            if (count == 0) {
                throw new BusinessException("批量新增分区标签列 数据插入失败");
            }
        });

        LOGGER.info("批量修改标签(导入)完成");
    }

    private void validateBatchUpdate(List<SortGroupRfidDTO> updateDTOList) {
        AssertUtils.notEmpty(updateDTOList, "参数不能为空");
        updateDTOList.stream().forEach(dto -> {
            AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(dto.getRfidType(), "电子标签类型不能为空");
            AssertUtils.hasText(dto.getControlName(), "控制器名称不能为空");
            AssertUtils.hasText(dto.getDeviceTagNo(), "设备编号不能为空");
            AssertUtils.hasText(dto.getBusTagNo(), "总线标签号不能为空");
            AssertUtils.hasText(dto.getSortName(), "关联项名称不能为空");
            AssertUtils.hasText(dto.getCreateUser(), "操作人不能为空");
            AssertUtils.notNull(dto.getSortGroupId(), "分区id不能为空");
        });
    }

    /**
     * 获取分区货位数据
     *
     * @return
     */
    private Map<String, SortGroupSettingDTO> getSettingBySortGroupId(Long sortGroupId) {
        if (Objects.isNull(sortGroupId)) {
            return Collections.EMPTY_MAP;
        }

        // 根据分区id获取分区货位数据
        SortGroupDTO sortGroupDTO = sortGroupBL.getGroup(sortGroupId);
        LOGGER.info("批量修改标签(导入) 分区查询结果：{}", JSON.toJSONString(sortGroupDTO));
        if (sortGroupDTO == null || CollectionUtils.isEmpty(sortGroupDTO.getGroupSettingList())) {
            throw new BusinessValidateException("所选分区数据不存在!");
        }

        if (!Objects.equals(sortGroupDTO.getSortGroupType(), SortType.SORT_LOCATION)) {
            throw new BusinessValidateException("所选分区类型不是货位分区!");
        }

        List<SortGroupSettingDTO> settingDTOList = sortGroupDTO.getGroupSettingList().stream()
            .filter(p -> !StringUtils.isEmpty(p.getSortId()) && StringUtils.isNumeric(p.getSortId())
                && !StringUtils.isEmpty(p.getSortName())
                && Objects.equals(p.getSortType(), Integer.valueOf(SortType.SORT_LOCATION)))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(settingDTOList)) {
            throw new BusinessValidateException("所选分区没有货位分区数据!");
        }

        Map<String, SortGroupSettingDTO> settingMap = settingDTOList.stream()
            .collect(Collectors.toMap(p -> p.getSortName(), Function.identity(), (key1, key2) -> key2));
        LOGGER.info("批量修改标签(导入) 分区数据组装结果：{}", JSON.toJSONString(settingMap));
        return settingMap;
    }

    /**
     * 所选分区中不存在的货位数据
     *
     * @return
     */
    private Map<String, List<SortGroupRfidDTO>> filterRfidDTOS(List<SortGroupRfidDTO> updateDTOList,
        Map<String, SortGroupSettingDTO> settingMap) {
        if (CollectionUtils.isEmpty(updateDTOList) || settingMap == null || settingMap.size() <= 0) {
            return Collections.EMPTY_MAP;
        }

        List<SortGroupRfidDTO> updateDTOS = updateDTOList.stream()
            .filter(p -> !StringUtils.isEmpty(p.getSortName()) && settingMap.containsKey(p.getSortName()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateDTOS)) {
            throw new BusinessValidateException("所选货位不在所选分区内!");
        }

        if (updateDTOList.size() > updateDTOS.size()) {
            String errorMsg = updateDTOList.stream()
                .filter(p -> !StringUtils.isEmpty(p.getSortName()) && !settingMap.containsKey(p.getSortName()))
                .map(p -> p.getSortName()).distinct().collect(Collectors.joining(","));
            throw new BusinessValidateException(String.format("货位%s不在所选分区内，不可导入！", errorMsg));
        }

        Map<String,
            List<SortGroupRfidDTO>> rfidMap = updateDTOS.stream()
                .filter(p -> !StringUtils.isEmpty(p.getControlName()) && !StringUtils.isEmpty(p.getBusTagNo())
                    && !StringUtils.isEmpty(p.getDeviceTagNo()))
                .collect(Collectors.groupingBy(
                    p -> String.format("%s-%s-%s", p.getControlName(), p.getBusTagNo(), p.getDeviceTagNo())));
        LOGGER.info("批量修改标签(导入) rfidMap数据：{}", JSON.toJSONString(rfidMap));
        if (rfidMap == null || rfidMap.size() <= 0) {
            throw new BusinessValidateException("所选控制器设备数据不完整，请重新选择!");
        }

        return rfidMap;
    }

    /**
     * 获取控制器及设备相关信息
     *
     * @return
     */
    private Map<String, ControlInfoResultDTO> getControlInfo(Integer warehouseId,
        Map<String, List<SortGroupRfidDTO>> rfidMap) {
        if (warehouseId == null || rfidMap == null || rfidMap.size() <= 0) {
            return Collections.EMPTY_MAP;
        }

        // 根据控制器名称 + 总线编号 + 设备编号获取控制器相关信息
        List<SowControlListSO> querySOList = new ArrayList<>();
        rfidMap.forEach((control, list) -> {
            SowControlListSO querySO = new SowControlListSO();
            querySO.setWarehouseId(warehouseId);
            querySO.setControlName(list.get(0).getControlName());
            querySO.setBusTagNo(list.get(0).getBusTagNo());
            querySO.setDeviceTagNo(list.get(0).getDeviceTagNo());
            querySOList.add(querySO);
        });
        List<ControlInfoResultDTO> controlInfoResultDTOS = iSowConfigService.listSowControlByNo(querySOList);
        LOGGER.info("获取控制器及设备相关信息 查询结果：{}", JSON.toJSONString(controlInfoResultDTOS));
        if (CollectionUtils.isEmpty(controlInfoResultDTOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<String,
            ControlInfoResultDTO> controlMap = controlInfoResultDTOS.stream()
                .collect(Collectors.toMap(
                    p -> String.format("%s-%s-%s", p.getControlName(), p.getBusTagNo(), p.getDeviceTagNo()),
                    Function.identity(), (key1, key2) -> key2));
        return controlMap;
    }

    /**
     * 根据关联id删除已存在标签数据
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkAndDeleteBySortIds(Integer warehouseId, List<SortGroupRfidDTO> rfidDTOS) {
        LOGGER.info("根据关联id删除已存在标签数据 入参：{}", JSON.toJSONString(rfidDTOS));
        if (CollectionUtils.isEmpty(rfidDTOS)) {
            return;
        }

        // 检查是否存在重复绑定
        List<Long> deleteIds = new ArrayList<>();
        List<Long> addSortIdList = rfidDTOS.stream().filter(p -> p.getSortId() != null).map(p -> p.getSortId())
            .distinct().collect(Collectors.toList());
        SortGroupRfidSO so = new SortGroupRfidSO();
        so.setWarehouseId(warehouseId);
        so.setSortIdList(addSortIdList);
        List<SortGroupRfidDTO> existRfidDTOList = listSortGroupRfid(so);
        if (!CollectionUtils.isEmpty(existRfidDTOList)) {
            List<Long> existIdList = existRfidDTOList.stream().filter(p -> p.getId() != null).map(p -> p.getId())
                .distinct().collect(Collectors.toList());
            deleteIds.addAll(existIdList);
        }

        if (CollectionUtils.isEmpty(deleteIds)) {
            return;
        }
        // 删除
        deleteBatchByIds(deleteIds);
        LOGGER.info("根据关联id删除已存在标签数据完成");
    }

    /**
     * 更新货位巷道数据
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLocationAisleInfo(Integer cityId, Integer warehouseId, List<SortGroupRfidDTO> rfidDTOS) {
        LOGGER.info("批量导入更新货位巷道数据入参：{}", JSON.toJSONString(rfidDTOS));
        if (CollectionUtils.isEmpty(rfidDTOS)) {
            return;
        }

        List<SortGroupRfidDTO> existRfidDTOList = rfidDTOS.stream()
            .filter(p -> !StringUtils.isEmpty(p.getAisleNo()) && p.getSortId() != null && p.getAreaId() != null)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existRfidDTOList)) {
            throw new BusinessValidateException("导入货位对应货区不存在，请先检查货位!");
        }

        // 巷道不存在则直接新增
        List<AislePO> aislePOS = getAndAddAisle(cityId, warehouseId, existRfidDTOList);
        LOGGER.info("批量导入更新货位巷道数据查询结果：{}", JSON.toJSONString(aislePOS));
        if (CollectionUtils.isEmpty(aislePOS)) {
            throw new BusinessValidateException("所选巷道数据不存在，请先创建巷道!");
        }

        Map<String, AislePO> aisleMap =
            aislePOS.stream().collect(Collectors.toMap(p -> String.format("%s-%s", p.getAreaId(), p.getAisleNo()),
                Function.identity(), (key1, key2) -> key2));

        Map<String, List<SortGroupRfidDTO>> aisleLocationMap = existRfidDTOList.stream()
            .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getAreaId(), p.getAisleNo())));
        aisleLocationMap.forEach((areaAisle, dtos) -> {
            List<Long> locationIds = dtos.stream().map(p -> p.getSortId()).distinct().collect(Collectors.toList());
            AislePO aislePO = aisleMap.get(areaAisle);
            if (Objects.isNull(aislePO)) {
                return;
            }
            // 更新货位上的巷道编号
            LocationModifyDTO modifyDTO = new LocationModifyDTO();
            modifyDTO.setCityId(cityId);
            modifyDTO.setWarehouseId(warehouseId);
            modifyDTO.setIdList(locationIds);
            modifyDTO.setAisleId(aislePO.getId());
            modifyDTO.setAisleNo(aislePO.getAisleNo());
            modifyDTO.setLastUpdateTime(new Date());
            locationPOMapper.updateSelectiveByIdList(modifyDTO);
            LOGGER.info("批量导入更新货位巷道编号：{}, 货位ids：{}", aislePO.getAisleNo(), JSON.toJSONString(locationIds));
        });

        LOGGER.info("批量导入更新货位巷道数据完成");
    }

    @Transactional(rollbackFor = Exception.class)
    public List<AislePO> getAndAddAisle(Integer cityId, Integer warehouseId, List<SortGroupRfidDTO> existRfidDTOList) {
        if (CollectionUtils.isEmpty(existRfidDTOList)) {
            return Collections.emptyList();
        }

        List<AisleByAreaQueryDTO> addAreaAisleNoList = new ArrayList<>();
        List<AisleByAreaQueryDTO> aisleByAreaQueryDTOS = new ArrayList<>();
        Map<String, List<SortGroupRfidDTO>> aisleLocationMap = existRfidDTOList.stream()
            .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getAreaId(), p.getAisleNo())));
        aisleLocationMap.forEach((areaAisel, rfidDTOS) -> {
            SortGroupRfidDTO rfidDTO = rfidDTOS.get(0);
            AisleByAreaQueryDTO queryDTO = new AisleByAreaQueryDTO();
            queryDTO.setAreaId(rfidDTO.getAreaId());
            queryDTO.setAisleNo(rfidDTO.getAisleNo());
            aisleByAreaQueryDTOS.add(queryDTO);
        });

        AisleQueryDTO queryDTO = new AisleQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setAreaAisleNoList(aisleByAreaQueryDTOS);
        List<AislePO> aislePOS = aislePOMapper.listAisleNoPage(queryDTO);
        if (CollectionUtils.isEmpty(aislePOS)) {
            addAreaAisleNoList.addAll(aisleByAreaQueryDTOS);
        } else {
            List<String> existAreaAisleNoList =
                aislePOS.stream().map(p -> String.format("%s-%s", p.getAreaId(), p.getAisleNo())).distinct()
                    .collect(Collectors.toList());
            List<AisleByAreaQueryDTO> noExistAreaAisleNoList = aisleByAreaQueryDTOS.stream()
                .filter(p -> !existAreaAisleNoList.contains(String.format("%s-%s", p.getAreaId(), p.getAisleNo())))
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noExistAreaAisleNoList)) {
                addAreaAisleNoList.addAll(noExistAreaAisleNoList);
            }
        }
        if (CollectionUtils.isEmpty(addAreaAisleNoList)) {
            return aislePOS;
        }

        String createUser = existRfidDTOList.get(0).getCreateUser();
        List<String> areaIdList =
            addAreaAisleNoList.stream().map(p -> p.getAreaId().toString()).distinct().collect(Collectors.toList());
        List<LocationPO> areaPOS = locationPOMapper.findLocationListById(areaIdList).stream()
            .filter(p -> p != null && Objects.equals(p.getCategory(), CategoryEnum.CARGO_AREA.getByteValue()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaPOS)) {
            throw new BusinessValidateException("所选货位货区不存在,货区：" + JSON.toJSONString(areaIdList));
        }
        Map<Long, String> areaMap = areaPOS.stream().collect(Collectors.toMap(LocationPO::getId, LocationPO::getName));

        List<AisleDTO> aisleDTOS = new ArrayList<>();
        addAreaAisleNoList.stream().forEach(p -> {
            String areaName = areaMap.get(p.getAreaId());
            AisleDTO addDTO = new AisleDTO();
            addDTO.setCityId(cityId);
            addDTO.setWarehouseId(warehouseId);
            addDTO.setAisleNo(p.getAisleNo());
            addDTO.setAreaId(p.getAreaId());
            addDTO.setAreaName(areaName);
            addDTO.setCreateUser(createUser);
            aisleDTOS.add(addDTO);
        });

        sortGroupBL.batchAddAisle(aisleDTOS);

        aislePOS = aislePOMapper.listAisleNoPage(queryDTO);
        return aislePOS;
    }

}
