package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupSettingMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupSettingPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Service
public class SortGroupSettingBL {

    @Autowired
    private SortGroupSettingMapper sortGroupSettingMapper;

    private static final Integer LIMIT_SIZE = 8000;
    List<SortGroupSettingPO> newPageListByGroupId(@Param("groupId") Long groupId) {
        Integer count = sortGroupSettingMapper.findListByGroupIdCount(groupId);
        if (count <= 0) {
            return Collections.emptyList();
        }
        if (count <= LIMIT_SIZE) {
            return sortGroupSettingMapper.findListByGroupId(groupId);
        }

        List<SortGroupSettingPO> sortGroupSettingPOList = sortGroupSettingMapper.pageListByGroupIdPage(groupId, 1, LIMIT_SIZE);
        Long maxId = sortGroupSettingPOList.stream().map(SortGroupSettingPO :: getId).max(Comparator.naturalOrder()).get();

        List<SortGroupSettingPO> leftsortGroupSettingPOList = sortGroupSettingMapper.findListByGroupIdAndBiggerId(groupId, maxId);
        if (CollectionUtils.isEmpty(leftsortGroupSettingPOList)) {
            return sortGroupSettingPOList;
        }

        sortGroupSettingPOList.addAll(leftsortGroupSettingPOList);

        return sortGroupSettingPOList;
    }

}
