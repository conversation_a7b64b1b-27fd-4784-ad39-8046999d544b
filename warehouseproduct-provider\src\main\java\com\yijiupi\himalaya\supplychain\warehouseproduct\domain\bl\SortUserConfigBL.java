package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dubbop.dto.StatisticsCategoryWithChild;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.AdminUserRoleConstant;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortUserConfigMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortUserConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductStatisCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 波次
 *
 * <AUTHOR> 2018/3/15
 */
@Service
public class SortUserConfigBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(SortUserConfigBL.class);
    @Autowired
    private LocationAreaBL locationAreaBL;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private SortUserConfigMapper sortUserConfigMapper;
    @Reference
    private IAdminUserService iAdminUserService;
    @Reference
    private IAdminUserQueryService iAdminUserQueryService;
    // @Autowired
    // private ProductCategoryBL productCategoryBL;
    @Reference
    private IProductCategoryGroupService iProductCategoryGroupService;

    /**
     * 通过仓库id查找分拣员信息
     *
     * @param warehouseId
     */
    public List<SortUserConfigDTO> findSortUserByWarehouseId(Integer warehouseId) {
        HashSet<String> set = Sets.newHashSet(AdminUserRoleConstant.ROLE_CODE_装卸人员);
        List<AdminUser> list = iAdminUserQueryService.listAdminUserWithoutAuthByRole(set, warehouseId);
        // LOGGER.info("AdminUser:{}", JSON.toJSONString(list));
        List<SortUserConfigDTO> sortUserDTOList = new ArrayList<>();
        for (AdminUser adminUser : list) {
            if (adminUser.getEnableState() == 1) {
                SortUserConfigDTO sortUserConfigDTO = new SortUserConfigDTO();
                sortUserConfigDTO.setUserId(adminUser.getId());
                sortUserConfigDTO.setUserName(adminUser.getTrueName());
                sortUserDTOList.add(sortUserConfigDTO);
            }
        }
        return sortUserDTOList;
    }

    /**
     * 通过用户id查找分拣员信息
     *
     * @param id
     * @return
     */
    public SortUserConfigDTO findSortUserById(Integer id) {
        AdminUser adminUser = iAdminUserService.getAdminUserWithoutAuthById(id);
        AssertUtils.notNull(adminUser, "找不到分拣员信息");
        SortUserConfigDTO sortUserDTO = new SortUserConfigDTO();
        sortUserDTO.setUserId(adminUser.getId());
        sortUserDTO.setUserName(adminUser.getTrueName());
        return sortUserDTO;
    }

    /**
     * 添加分拣员分拣策略
     *
     * @param sortUserConfigDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addSortUserConfig(SortUserConfigDTO sortUserConfigDTO) {
        List<SortUserConfigPO> sortUserConfigPOList = getSortUserConfigPOS(sortUserConfigDTO);
        List<SortUserConfigPO> sortUserConfigPOList1 =
            sortUserConfigMapper.findSortUserProperty(sortUserConfigDTO.getUserId(), sortUserConfigDTO.getType());
        // 判断是否已经有相同属性配置
        for (SortUserConfigPO po : sortUserConfigPOList1) {
            for (int i = sortUserConfigPOList.size() - 1; i >= 0; i--) {
                if (po.getSortName().equals(sortUserConfigPOList.get(i).getSortName())) {
                    sortUserConfigPOList.remove(i);
                }
            }
        }
        if (!CollectionUtils.isEmpty(sortUserConfigPOList)) {
            sortUserConfigMapper.addSortUserConfigList(sortUserConfigPOList);
        }
    }

    /**
     * 修改分拣员分拣策略
     *
     * @param sortUserConfigDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateSortUserConfig(SortUserConfigDTO sortUserConfigDTO) {
        List<SortUserConfigPO> sortUserConfigPOList = getSortUserConfigPOS(sortUserConfigDTO);
        // 编辑的时候先删后增
        sortUserConfigMapper.deleteSortUserConfig(sortUserConfigDTO.getUserId(), sortUserConfigDTO.getType());
        if (!CollectionUtils.isEmpty(sortUserConfigPOList)) {
            sortUserConfigMapper.addSortUserConfigList(sortUserConfigPOList);
        }
    }

    private List<SortUserConfigPO> getSortUserConfigPOS(SortUserConfigDTO sortUserConfigDTO) {
        List<SortUserConfigPO> sortUserConfigPOList = new ArrayList<>();
        if (sortUserConfigDTO.getLocationAreaList() != null) {
            for (LocationAreaDTO dto : sortUserConfigDTO.getLocationAreaList()) {
                SortUserConfigPO sortUserConfigPO = getSortUserConfigPO(sortUserConfigDTO);
                sortUserConfigPO.setSortId(dto.getId());
                sortUserConfigPO.setSortName(dto.getArea());
                sortUserConfigPO.setType(sortUserConfigDTO.getType());
                sortUserConfigPO.setSortType(SortType.SORT_AREA);
                sortUserConfigPO.setRemark(sortUserConfigDTO.getRemark());
                sortUserConfigPOList.add(sortUserConfigPO);
            }
        }
        if (sortUserConfigDTO.getLocationList() != null) {
            for (LocationDTO dto : sortUserConfigDTO.getLocationList()) {
                SortUserConfigPO sortUserConfigPO = getSortUserConfigPO(sortUserConfigDTO);
                sortUserConfigPO.setSortId(dto.getId());
                sortUserConfigPO.setSortName(dto.getName());
                sortUserConfigPO.setType(sortUserConfigDTO.getType());
                sortUserConfigPO.setSortType(SortType.SORT_LOCATION);
                sortUserConfigPO.setRemark(sortUserConfigDTO.getRemark());
                sortUserConfigPOList.add(sortUserConfigPO);
            }
        }
        if (sortUserConfigDTO.getCategoryList() != null) {
            for (String category : sortUserConfigDTO.getCategoryList()) {
                SortUserConfigPO sortUserConfigPO = getSortUserConfigPO(sortUserConfigDTO);
                sortUserConfigPO.setSortName(category);
                sortUserConfigPO.setType(sortUserConfigDTO.getType());
                sortUserConfigPO.setSortType(SortType.SORT_CATEGORY);
                sortUserConfigPO.setRemark(sortUserConfigDTO.getRemark());
                sortUserConfigPOList.add(sortUserConfigPO);
            }
        }
        return sortUserConfigPOList;
    }

    public SortUserConfigPO getSortUserConfigPO(SortUserConfigDTO sortUserConfigDTO) {
        SortUserConfigPO sortUserConfigPO = new SortUserConfigPO();
        BeanUtils.copyProperties(sortUserConfigDTO, sortUserConfigPO);
        return sortUserConfigPO;
    }

    /**
     * 根据查询条件获取已经配置策略的分拣员
     *
     * @param selectSortUser
     * @return
     */
    public PageList<SortUserConfigDTO> selectSortUser(SelectSortUser selectSortUser) {
        PageResult<SortUserConfigPO> pageList = sortUserConfigMapper.selectSortUser(selectSortUser,
            selectSortUser.getCurrentPage(), selectSortUser.getPageSize());
        List<SortUserConfigDTO> sortUserConfigDTOList = Lists.transform(pageList, (input) -> {
            SortUserConfigDTO dto = new SortUserConfigDTO();
            BeanUtils.copyProperties(input, dto);
            return dto;
        });
        PageList<SortUserConfigDTO> dtoPageList = new PageList<>();
        dtoPageList.setDataList(sortUserConfigDTOList);
        dtoPageList.setPager(pageList.getPager());
        return dtoPageList;
    }

    /**
     * 获得货区和类目
     *
     * @param findAllProperty
     */
    public SortPropertyDTO findSortProperty(FindAllProperty findAllProperty) {
        // 货位查询
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setWarehouseId(findAllProperty.getWarehouseId());
        locationQueryDTO.setSubcategoryList(findAllProperty.getLocationSubcategoryList());
        List<LoactionDTO> loactionDTOList = productLocationBL.findProductLocationList(locationQueryDTO);
        // 货区查询
        LocationAreaListDTO locationAreaDTO = new LocationAreaListDTO();
        locationAreaDTO.setWarehouseId(findAllProperty.getWarehouseId());
        locationAreaDTO.setSubcategoryList(findAllProperty.getAreaSubcategoryList());
        List<LocationAreaReturnDTO> areaReturnDTOList = locationAreaBL.getLocationAreaNoPage(locationAreaDTO);
        SortPropertyDTO sortPropertyDTO = new SortPropertyDTO();
        // List<ProductStatisCategoryDTO> statisticsCategories =
        // productCategoryBL.findCategoryByCityId(findAllProperty.getCityId());
        ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO = new ProductCategoryGroupQueryDTO();
        productCategoryGroupQueryDTO.setOrgId(findAllProperty.getCityId());
        List<ProductCategoryGroupDTO> productCategoryGroupTree =
            iProductCategoryGroupService.findProductCategoryGroupTree(productCategoryGroupQueryDTO);
        List<ProductStatisCategoryDTO> statisticsCategories =
            ProductCategoryConvertor.productCategoryGroupTree2ProductStatisCategoryDTOS(productCategoryGroupTree);

        sortPropertyDTO.setCategoryList(
            statisticsCategories.stream().map(ProductStatisCategoryDTO::getName).collect(Collectors.toList()));
        sortPropertyDTO.setLoactionDTOList(loactionDTOList);
        sortPropertyDTO.setAreaList(areaReturnDTOList);
        return sortPropertyDTO;
    }

    /**
     * 获得货区和类目
     */
    public List<StatisticsCategoryDTO> findCategorys(Integer cityId) {
        List<StatisticsCategoryDTO> lstCategory = new ArrayList<>();
        ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO = new ProductCategoryGroupQueryDTO();
        productCategoryGroupQueryDTO.setOrgId(cityId);
        List<ProductCategoryGroupDTO> productCategoryGroupTree =
            iProductCategoryGroupService.findProductCategoryGroupTree(productCategoryGroupQueryDTO);
        List<ProductStatisCategoryDTO> statisticsCategories =
            ProductCategoryConvertor.productCategoryGroupTree2ProductStatisCategoryDTOS(productCategoryGroupTree);
        statisticsCategories.forEach(parent -> {
            StatisticsCategoryDTO categoryDTO = getCategoryDTO(parent);
            if (!CollectionUtils.isEmpty(parent.getChilds())) {
                List<StatisticsCategoryDTO> lstChildCategory = new ArrayList<>();
                parent.getChilds().forEach(child -> {
                    StatisticsCategoryDTO childDTO = getCategoryDTO(child);
                    childDTO.setParentId(parent.getId());
                    lstChildCategory.add(childDTO);
                });
                categoryDTO.setChilds(lstChildCategory);
            }
            lstCategory.add(categoryDTO);
        });
        return lstCategory;
    }

    private StatisticsCategoryDTO getCategoryDTO(ProductStatisCategoryDTO p) {
        StatisticsCategoryDTO secCategoryDTO = new StatisticsCategoryDTO();
        secCategoryDTO.setId(p.getId());
        secCategoryDTO.setName(p.getName());
        return secCategoryDTO;
    }

    /**
     * 拿到分拣员已设置属性
     *
     * @param userId
     * @return
     */
    public SortUserConfigDTO findSortUserProperty(Integer userId, Byte type) {
        List<SortUserConfigPO> sortUserConfigPOList = sortUserConfigMapper.findSortUserProperty(userId, type);
        AssertUtils.notEmpty(sortUserConfigPOList, "当前分拣员还没有配置拣货策略");
        SortUserConfigDTO sortUserConfigDTO = getSortUserConfigDTO(sortUserConfigPOList);
        return sortUserConfigDTO;
    }

    /**
     * 根据分拣属性(货区，货位，类目)拿到分拣的信息
     *
     * @param sortPropertyByUserDTO
     * @return
     */
    public List<SortUserConfigDTO> findSortPropertyByUser(SortPropertyByUserDTO sortPropertyByUserDTO) {
        List<SortUserConfigDTO> list = new ArrayList<>();
        List<SortUserConfigPO> sortUserConfigPOList =
            sortUserConfigMapper.findSortPropertyByUser(sortPropertyByUserDTO);
        if (sortUserConfigPOList == null || sortUserConfigPOList.size() == 0) {
            return new ArrayList<>();
        }
        // 根据分拣员id分组
        Map<Integer, List<SortUserConfigPO>> map =
            sortUserConfigPOList.stream().collect(Collectors.groupingBy(SortUserConfigPO::getUserId));
        for (Integer key : map.keySet()) {
            List<SortUserConfigPO> poList = map.get(key);
            SortUserConfigDTO sortUserConfigDTO = getSortUserConfigDTO(poList);
            list.add(sortUserConfigDTO);
        }
        return list;
    }

    private SortUserConfigDTO getSortUserConfigDTO(List<SortUserConfigPO> sortUserConfigPOList) {
        SortUserConfigDTO sortUserConfigDTO = new SortUserConfigDTO();
        sortUserConfigDTO.setUserId(sortUserConfigPOList.get(0).getUserId());
        sortUserConfigDTO.setUserName(sortUserConfigPOList.get(0).getUserName());
        sortUserConfigDTO.setRemark(sortUserConfigPOList.get(0).getRemark());
        sortUserConfigDTO.setWarehouseId(sortUserConfigPOList.get(0).getWarehouseId());
        List<String> categoryList = sortUserConfigPOList.stream().filter(p -> SortType.SORT_CATEGORY == p.getSortType())
            .map(SortUserConfigPO::getSortName).collect(Collectors.toList());
        // 拿到关联货区和货位的list集合PO
        List<SortUserConfigPO> filterList = sortUserConfigPOList.stream()
            .filter(p -> SortType.SORT_AREA == p.getSortType() || SortType.SORT_LOCATION == p.getSortType())
            .collect(Collectors.toList());
        List<LocationDTO> locationDTOList = new ArrayList<>();
        List<LocationAreaDTO> locationAreaDTOList = new ArrayList<>();
        for (SortUserConfigPO po : filterList) {
            if (SortType.SORT_AREA == po.getSortType()) {
                // 配置货区
                LocationAreaDTO dto = new LocationAreaDTO();
                dto.setId(po.getSortId());
                dto.setArea(po.getSortName());
                locationAreaDTOList.add(dto);
            }
            if (SortType.SORT_LOCATION == po.getSortType()) {
                // 配置货位
                LocationDTO dto = new LocationDTO();
                dto.setId(po.getSortId());
                dto.setName(po.getSortName());
                locationDTOList.add(dto);
            }
        }
        sortUserConfigDTO.setCategoryList(categoryList);
        sortUserConfigDTO.setLocationAreaList(locationAreaDTOList);
        sortUserConfigDTO.setLocationList(locationDTOList);
        return sortUserConfigDTO;
    }

    /**
     * 删除分拣员属性
     *
     * @param id
     */
    public void deleteSortUserConfig(Integer id, Byte type) {
        sortUserConfigMapper.deleteSortUserConfig(id, type);
    }
}
