package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.StockAgeStrategyConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.StockAgeStrategyConfigMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.StockAgeStrategyMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.QueryConditionsEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.StockAgeStrategyStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.StockAgeStrategyTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;

@Service
public class StockAgeStrategyBL {

    private static final Logger LOG = LoggerFactory.getLogger(StockAgeStrategyBL.class);

    @Autowired
    private ProductRelationGroupQueryBL productRelationGroupQueryBL;

    @Autowired
    private StockAgeStrategyMapper stockAgeStrategyMapper;

    @Autowired
    private StockAgeStrategyConfigMapper stockAgeStrategyConfigMapper;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Reference
    private IProductSkuService iProductSkuService;

    /**
     * 新建库龄策略
     * 
     * @param stockAgeStrategyDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveStockAgeStrategy(StockAgeStrategyDTO stockAgeStrategyDTO) {
        StockAgeStrategyPO stockAgeStrategyPO =
            StockAgeStrategyConvertor.stockAgeStrategyDTO2StockAgeStrategyPO(stockAgeStrategyDTO);
        Long id = UUIDUtils.randonUUID();
        stockAgeStrategyPO.setId(id);

        if (CollectionUtils.isNotEmpty(stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS())) {
            // 重复数据校验
            checkRepeatConfig(stockAgeStrategyDTO);
            List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPOS =
                StockAgeStrategyConvertor.stockAgeStrategyConfigDTOS2StockAgeStrategyConfigPOS(
                    stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS());
            stockAgeStrategyConfigPOS.forEach(config -> {
                config.setId(UUIDUtils.randonUUID());
                config.setStockAgeStrategyId(id);
            });
            stockAgeStrategyConfigMapper.batchInsertOrUpdate(stockAgeStrategyConfigPOS);
        }
        stockAgeStrategyMapper.insertSelective(stockAgeStrategyPO);
    }

    /**
     * 修改库龄策略
     * 
     * @param stockAgeStrategyDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateStockAgeStrategy(StockAgeStrategyDTO stockAgeStrategyDTO) {
        stockAgeStrategyMapper.updateByPrimaryKeySelective(
            StockAgeStrategyConvertor.stockAgeStrategyDTO2StockAgeStrategyPO(stockAgeStrategyDTO));

        if (CollectionUtils.isNotEmpty(stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS())) {
            // 重复数据校验
            checkRepeatConfig(stockAgeStrategyDTO);
            List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPOS =
                StockAgeStrategyConvertor.stockAgeStrategyConfigDTOS2StockAgeStrategyConfigPOS(
                    stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS());
            stockAgeStrategyConfigMapper.batchInsertOrUpdate(stockAgeStrategyConfigPOS);
        }
    }

    /**
     * 修改库龄策略(全量修改)
     * 
     * @param stockAgeStrategyDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateStockAgeStrategyAll(StockAgeStrategyDTO stockAgeStrategyDTO) {
        // 重复数据校验
        checkRepeatConfig(stockAgeStrategyDTO);
        stockAgeStrategyMapper.updateByPrimaryKeySelective(
            StockAgeStrategyConvertor.stockAgeStrategyDTO2StockAgeStrategyPO(stockAgeStrategyDTO));
        List<StockAgeStrategyConfigPO> oldStockAgeStrategyConfigs =
            stockAgeStrategyConfigMapper.findByStrategyId(stockAgeStrategyDTO.getId(), stockAgeStrategyDTO.getOrgId());
        List<Long> delConfigIds =
            oldStockAgeStrategyConfigs.stream().map(StockAgeStrategyConfigPO::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS())) {
            List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPOS =
                StockAgeStrategyConvertor.stockAgeStrategyConfigDTOS2StockAgeStrategyConfigPOS(
                    stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS());
            List<Long> newConfigIds = stockAgeStrategyConfigPOS.stream().map(config -> {
                if (config.getId() == null) {
                    config.setId(UUIDUtils.randonUUID());
                    config.setStockAgeStrategyId(stockAgeStrategyDTO.getId());
                }
                return config.getId();
            }).collect(Collectors.toList());
            stockAgeStrategyConfigMapper.batchInsertOrUpdate(stockAgeStrategyConfigPOS);

            delConfigIds.removeAll(newConfigIds);
        }
        if (CollectionUtils.isNotEmpty(delConfigIds)) {
            stockAgeStrategyConfigMapper.deleteByIds(delConfigIds);
        }
    }

    /**
     * 分页条件查询策略详细信息
     * 
     * @param stockAgeStrategyQueryDTO
     * @return
     */
    public PageList<StockAgeStrategyDTO> pageListStockAgeStrategy(StockAgeStrategyQueryDTO stockAgeStrategyQueryDTO) {
        PageResult<StockAgeStrategyDTO> pageResult;
        if (stockAgeStrategyQueryDTO.getQueryCondition().equals(QueryConditionsEnum.查询全部.getType())) {
            pageResult = stockAgeStrategyMapper.pageListStockAgeStrategyAll(stockAgeStrategyQueryDTO,
                stockAgeStrategyQueryDTO.getPageNum(), stockAgeStrategyQueryDTO.getPageSize());
        } else {
            pageResult = stockAgeStrategyMapper.pageListStockAgeStrategy(stockAgeStrategyQueryDTO,
                stockAgeStrategyQueryDTO.getPageNum(), stockAgeStrategyQueryDTO.getPageSize());
        }
        return pageResult.toPageList();
    }

    /**
     * 重复数据校验
     * 
     * @param stockAgeStrategyDTO
     */
    private void checkRepeatConfig(StockAgeStrategyDTO stockAgeStrategyDTO) {
        // 同一个关联id不能重复
        List<Long> relatedIds =
            stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS().stream().filter(config -> config.getId() == null)
                .map(StockAgeStrategyConfigDTO::getRelatedId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relatedIds)) {
            return;
        }
        StockAgeStrategyQueryDTO stockAgeStrategyQueryDTO = new StockAgeStrategyQueryDTO();
        stockAgeStrategyQueryDTO.setOrgId(stockAgeStrategyDTO.getOrgId());
        stockAgeStrategyQueryDTO.setWarehouseId(stockAgeStrategyDTO.getWarehouseId());
        stockAgeStrategyQueryDTO.setRelatedIds(relatedIds);
        stockAgeStrategyQueryDTO.setStrategyType(stockAgeStrategyDTO.getStrategyType());
        stockAgeStrategyQueryDTO.setState(StockAgeStrategyStateEnum.启用.getType());
        PageResult<StockAgeStrategyDTO> pageResult =
            stockAgeStrategyMapper.pageListStockAgeStrategyAll(stockAgeStrategyQueryDTO, null, null);
        if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
            String existRelatedIds =
                pageResult.getResult().stream().flatMap(strategy -> strategy.getStockAgeStrategyConfigDTOS().stream())
                    .map(cofing -> cofing.getRelatedId().toString()).collect(Collectors.joining(","));
            throw new BusinessValidateException("请重新配置，下列数据在其它策略中已存在:" + existRelatedIds);

        }
    }

    /**
     * 分页条件查询策略配置
     */
    public PageList<StockAgeStrategyConfigDTO>
        pageListStockAgeStrategyConfig(StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO) {
        PageResult<StockAgeStrategyConfigDTO> pageResult =
            stockAgeStrategyConfigMapper.pageListStockAgeStrategyConfig(stockAgeStrategyConfigQueryDTO,
                stockAgeStrategyConfigQueryDTO.getPageNum(), stockAgeStrategyConfigQueryDTO.getPageSize());
        return pageResult.toPageList();
    }

    public PageList<StockAgeStrategyDTO>
        pageListStockAgeProductStrategy(StockAgeProductQueryDTO stockAgeProductQueryDTO) {
        PageResult<StockAgeStrategyDTO> pageResult = stockAgeStrategyMapper.pageListStockAgeProductStrategy(
            stockAgeProductQueryDTO, stockAgeProductQueryDTO.getPageNum(), stockAgeProductQueryDTO.getPageSize());
        return pageResult.toPageList();
    }

    /**
     * 根据城市ID、仓库ID、产品skuId判断产品是否库龄管控产品
     *
     * @param cityId 城市ID
     * @param warehouseId 仓库ID
     * @param productSkuIds 产品skuId
     * @return
     */
    public Map<Long, Boolean> isProductStockAgeControls(Integer cityId, Integer warehouseId, List<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "产品SkuId不能为空");
        StockAgeStrategyQueryDTO queryDTO = new StockAgeStrategyQueryDTO();
        queryDTO.setOrgId(cityId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setRelatedIds(productSkuIds);
        queryDTO.setQueryCondition(QueryConditionsEnum.查询全部.getType());
        queryDTO.setState(StockAgeStrategyStateEnum.启用.getType());
        queryDTO.setStrategyType(StockAgeStrategyTypeEnum.产品.getType());
        LOG.warn("isProductStockAgeControls 查询库龄配置参数：{}", JSON.toJSONString(queryDTO));
        PageList<StockAgeStrategyDTO> stockAgeStrategyList = pageListStockAgeStrategy(queryDTO);
        List<StockAgeStrategyDTO> stockAgeStrategyDataList = stockAgeStrategyList.getDataList();
        LOG.warn("isProductStockAgeControls 查询库龄配置结果：{}", JSON.toJSONString(stockAgeStrategyDataList));
        if (CollectionUtils.isEmpty(stockAgeStrategyDataList)) {
            return Collections.EMPTY_MAP;
        }
        List<StockAgeStrategyConfigDTO> configDTOList = stockAgeStrategyDataList.stream()
            .filter(e -> e != null && CollectionUtils.isNotEmpty(e.getStockAgeStrategyConfigDTOS()))
            .flatMap(e -> e.getStockAgeStrategyConfigDTOS().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configDTOList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, Boolean> result = new HashMap<>(16);
        for (Long skuId : productSkuIds) {
            if (skuId == null || result.containsKey(skuId)) {
                continue;
            }
            result.put(skuId, configDTOList.stream().anyMatch(e -> e != null && skuId.equals(e.getRelatedId())));
        }
        return result;
    }

    /**
     * 根据城市ID、仓库ID、产品skuId判断产品及其关联产品是否库龄管控产品
     *
     * @param cityId 城市ID
     * @param warehouseId 仓库ID
     * @param productSkuIds 产品skuId
     * @return
     */
    public Map<Long, Boolean> isRefProductStockAgeControls(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIds, "产品SkuId不能为空");
        Map<Long, List<ProductSkuDTO>> refProductMap =
            productRelationGroupQueryBL.findSameGroupProductBySkuIds(warehouseId, productSkuIds);
        Map<Long, List<Long>> skuRefMap = filterSimpleRefSku(refProductMap);
        List<Long> totalSkuIds = new ArrayList<>();
        totalSkuIds.addAll(productSkuIds);
        totalSkuIds.addAll(skuRefMap.values().stream().flatMap(e -> e.stream()).collect(Collectors.toList()));
        Map<Long, Boolean> productStockAgeControls = isProductStockAgeControls(cityId, warehouseId, totalSkuIds);
        if (productStockAgeControls == null) {
            return new HashMap<>(16);
        }
        Map<Long, Boolean> result = new HashMap<>(16);
        for (Long skuId : productSkuIds) {
            if (skuId == null || result.containsKey(skuId)) {
                continue;
            }
            boolean skuContrl = ObjectUtils.defaultIfNull(productStockAgeControls.get(skuId), Boolean.FALSE);
            boolean refContrl = false;
            List<Long> refSkuIds = skuRefMap.get(skuId);
            if (CollectionUtils.isNotEmpty(refSkuIds)) {
                // 有关联产品
                for (Long ref : refSkuIds) {
                    Boolean refCtr = ObjectUtils.defaultIfNull(productStockAgeControls.get(ref), Boolean.FALSE);
                    if (refCtr) {
                        refContrl = true;
                        break;
                    }
                }
            }
            // 产品与其关联产品有一个是库龄管控产品则都标记为库龄管控产品
            result.put(skuId, (skuContrl || refContrl) ? Boolean.TRUE : Boolean.FALSE);
        }
        return result;
    }

    /**
     * 根据规格货主查询产品是否库龄管控产品
     */
    public List<StockAgeControlProductBySpecDTO>
        isProductStockAgeControlsBySpec(ProductSkuBySpecificationSO productSkuBySpecSO) {
        AssertUtils.notNull(productSkuBySpecSO, "参数信息不能为空");
        AssertUtils.notNull(productSkuBySpecSO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(productSkuBySpecSO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuBySpecSO.getSpecList(), "规格信息不能为空");
        List<ProductSkuDTO> skuDTOS = productSkuQueryBL.findBySpec(productSkuBySpecSO);
        if (CollectionUtils.isEmpty(skuDTOS)) {
            return Collections.EMPTY_LIST;
        }
        // skuId 集合
        List<Long> skuIdList = skuDTOS.stream().filter(e -> e != null).map(e -> e.getProductSkuId()).distinct()
            .collect(Collectors.toList());
        // 查询产品库龄管控
        Map<Long, Boolean> productStockAgeControls =
            isProductStockAgeControls(productSkuBySpecSO.getCityId(), productSkuBySpecSO.getWarehouseId(), skuIdList);
        // 转化成
        return getSpecStockAgeControlList(productSkuBySpecSO.getCityId(), productSkuBySpecSO.getWarehouseId(),
            productStockAgeControls, skuDTOS);
    }

    // 根据规格货主转化产品是否库龄管控产品
    private List<StockAgeControlProductBySpecDTO> getSpecStockAgeControlList(Integer cityId, Integer warehouseId,
        Map<Long, Boolean> productStockAgeControls, List<ProductSkuDTO> skuDTOS) {
        if (productStockAgeControls == null || productStockAgeControls.isEmpty() || CollectionUtils.isEmpty(skuDTOS)) {
            return Collections.EMPTY_LIST;
        }
        // sku信息Map
        Map<Long, ProductSkuDTO> skuDTOMap = skuDTOS.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity(), (v1, v2) -> v1 != null ? v1 : v2));
        List<StockAgeControlProductBySpecDTO> result = new ArrayList<>();
        for (Map.Entry<Long, Boolean> entry : productStockAgeControls.entrySet()) {
            if (entry == null) {
                continue;
            }

            ProductSkuDTO skuDTO = skuDTOMap.get(entry.getKey());
            if (skuDTO == null) {
                continue;
            }
            StockAgeControlProductBySpecDTO specDTO = new StockAgeControlProductBySpecDTO();
            specDTO.setCityId(cityId);
            specDTO.setWarehouseId(warehouseId);
            specDTO.setProductSpecificationId(skuDTO.getProductSpecificationId());
            specDTO.setOwnerId(skuDTO.getCompany_Id());
            specDTO.setSecOwnerId(skuDTO.getSecOwnerId());
            specDTO.setStoreAgeControlProduct(entry.getValue());
            result.add(specDTO);
        }
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        List<StockAgeControlProductBySpecDTO> filterList = new ArrayList<>();
        Map<String, List<StockAgeControlProductBySpecDTO>> specGroup =
            result.stream().collect(Collectors.groupingBy(e -> e.getSpecAndOwnerIdentification()));
        for (Map.Entry<String, List<StockAgeControlProductBySpecDTO>> entry : specGroup.entrySet()) {
            if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            StockAgeControlProductBySpecDTO source = entry.getValue().get(0);
            StockAgeControlProductBySpecDTO filterDTO = new StockAgeControlProductBySpecDTO();
            // 去重：优先返回为true的数据
            BeanUtils.copyProperties(source, filterDTO);
            filterDTO.setStoreAgeControlProduct(entry.getValue().stream()
                .anyMatch(e -> e != null && ObjectUtils.defaultIfNull(e.getStoreAgeControlProduct(), Boolean.FALSE)));
            filterList.add(filterDTO);
        }
        return filterList;
    }

    /**
     * 过滤出关联产品
     * 
     * @param refProductMap
     * @return
     */
    private Map<Long, List<Long>> filterSimpleRefSku(Map<Long, List<ProductSkuDTO>> refProductMap) {
        if (refProductMap == null || refProductMap.isEmpty()) {
            return new HashMap<>(16);
        }
        Map<Long, List<Long>> skuRefMap = new HashMap<>(16);
        for (Map.Entry<Long, List<ProductSkuDTO>> entry : refProductMap.entrySet()) {
            if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            skuRefMap.put(entry.getKey(), entry.getValue().stream().filter(e -> e != null).map(e -> e.getProductSkuId())
                .distinct().collect(Collectors.toList()));
        }
        return skuRefMap;
    }

    /**
     * 分页条件查询策略配置信息
     */
    public PageList<StockAgeStrategyConfigInfoDTO>
        pageListStockAgeStrategyConfigInfo(StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO) {
        LOG.info("StockAgeStrategyBL.pageListStockAgeStrategyConfigInfo 库龄策略配置查询请求:{}",
            JSON.toJSONString(stockAgeStrategyConfigQueryDTO));
        PageList<StockAgeStrategyConfigInfoDTO> pageList = new PageList<>();
        PageResult<StockAgeStrategyConfigDTO> pageResult =
            stockAgeStrategyConfigMapper.pageListStockAgeStrategyConfig(stockAgeStrategyConfigQueryDTO,
                stockAgeStrategyConfigQueryDTO.getPageNum(), stockAgeStrategyConfigQueryDTO.getPageSize());
        if (CollectionUtils.isEmpty(pageResult) || null == pageResult.toPageList()
            || CollectionUtils.isEmpty(pageResult.toPageList().getDataList())) {
            return pageList;
        }

        List<StockAgeStrategyConfigDTO> configDTOS = pageResult.toPageList().getDataList();
        List<StockAgeStrategyConfigInfoDTO> configInfoDTOS = new ArrayList<>();
        List<Long> skuIds =
            configDTOS.stream().map(StockAgeStrategyConfigDTO::getRelatedId).collect(Collectors.toList());
        Map<Long, List<ProductSkuDTO>> productSkuMap = new HashMap<>(16);
        List<ProductSkuDTO> productSkuDTOS = iProductSkuQueryService.findBySku(skuIds);
        if (CollectionUtils.isNotEmpty(productSkuDTOS)) {
            productSkuMap = productSkuDTOS.stream().collect(Collectors.groupingBy(ProductSkuDTO::getProductSkuId));
        }
        Map<Long, ProductCodeDTO> codeMap =
            iProductSkuService.getPackageAndUnitCode(new HashSet<>(skuIds), configDTOS.get(0).getOrgId());
        for (StockAgeStrategyConfigDTO config : configDTOS) {
            StockAgeStrategyConfigInfoDTO configInfo = new StockAgeStrategyConfigInfoDTO();
            BeanUtils.copyProperties(config, configInfo);
            if (CollectionUtils.isNotEmpty(productSkuMap.get(config.getRelatedId()))) {
                ProductSkuDTO sku = productSkuMap.get(config.getRelatedId()).get(0);
                configInfo.setOwnerId(sku.getCompany_Id());
                configInfo.setOwnerName(sku.getSourceName());
                configInfo.setProductSkuName(sku.getName());
                configInfo.setSpecificationName(sku.getSpecificationName());
                configInfo.setFirstCategoryName(sku.getStatisticsClass());
                configInfo.setStorageType(sku.getStorageType());
            }
            if (codeMap.get(config.getRelatedId()) != null) {
                List<String> barCodes = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(codeMap.get(config.getRelatedId()).getUnitCode())) {
                    barCodes.addAll(codeMap.get(config.getRelatedId()).getUnitCode());
                }
                if (CollectionUtils.isNotEmpty(codeMap.get(config.getRelatedId()).getPackageCode())) {
                    barCodes.addAll(codeMap.get(config.getRelatedId()).getPackageCode());
                }
                configInfo.setProductCodes(barCodes);
            }

            configInfoDTOS.add(configInfo);
        }

        pageList.setPager(pageResult.getPager());
        pageList.setDataList(configInfoDTOS);
        return pageList;
    }

}
