package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductSkuConfigPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigBusinessTagEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigStorageAttributeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig.ProductSkuConfigHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSkuPOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductConfigStorageAttributeUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分仓属性服务
 * 作为独立服务解决循环依赖问题
 *
 * <AUTHOR>
 */
@Service
public class StorageAttributeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorageAttributeService.class);

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    @Autowired
    private ProductSkuConfigMapper productSkuConfigMapper;

    @Autowired
    private ProductSkuPOConvertor productSkuPOConvertor;

    @Resource
    private ProductSkuConfigHelper productSkuConfigHelper;

    @Reference
    private IContentConfigurationService iContentConfigurationService;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    private final Integer pageSize = 1000;

    /**
     * 获取产品配置分仓属性
     *
     * @param updateDTO
     */
    public Map<Long, Byte> queryStorageAttributeBySkuIds(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        LOGGER.info("获取产品配置分仓属性 入参:{}", JSON.toJSONString(updateDTO));
        // 通过仓库 id、skuId 计算其分仓属性
        Map<Long, Byte> storageAttributeMap =
                calcSkuStorageAttribute(updateDTO.getWarehouseId(), updateDTO.getProductSkuIdList());
        LOGGER.info("更新产品配置分仓属性 分仓属性计算结果:{}", JSON.toJSONString(storageAttributeMap));
        return storageAttributeMap;
    }

    /**
     * 计算SKU的存储属性
     *
     * @param warehouseId 仓库ID
     * @param skuIds      SKU ID列表
     * @return 存储属性映射，key是skuId，value是分仓属性
     */
    public Map<Long, Byte> calcSkuStorageAttribute(Integer warehouseId, List<Long> skuIds) {
        List<ProductSkuPO> skuPOList = productSkuPOMapper.findBySkuFull(warehouseId, skuIds);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(skuPOList)) {
            return null;
        }
        LOGGER.info("skuPOList查询结果:{}", JSON.toJSONString(skuPOList));

        // sku一级类目
        Map<Long, Long> skuStatisticsClassMap = skuPOList.stream().filter(p -> p.getStatisticsClassId() != null)
                .collect(Collectors.toMap(p -> p.getProductSku_Id(), p -> p.getStatisticsClassId(), (v1, v2) -> v1));
        LOGGER.info("skuStatisticsClassMap结果:{}", JSON.toJSONString(skuStatisticsClassMap));
        // 休食特征类目配置
        String restCategoryValue =
                iContentConfigurationService.getContentValue("FeatureProductDisplayCategory", null, "");
        LOGGER.info("restCategoryValue结果:{}", restCategoryValue);

        List<ProductSkuDTO> productSkuDTOS =
                productSkuConfigHelper.fillAdditionProp(productSkuPOConvertor.convert(skuPOList), warehouseId);
        LOGGER.info("productSkuDTOS结果:{}", JSON.toJSONString(productSkuDTOS));

        productSkuDTOS.stream().forEach(skuDTO -> {
            // 0:默认,1:酒饮,2:休食
            Byte storageAttribute = null;
            LOGGER.info("skuDTO处理LocationType:{}", skuDTO.getLocationType());
            if (Objects.equals(skuDTO.getLocationType(), LocationType.分拣位.getValue().intValue())) {
                storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
            } else if (Objects.equals(skuDTO.getLocationType(), LocationType.零拣位.getValue().intValue())) {
                storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
            }

            if (Objects.isNull(storageAttribute)) {
                LOGGER.info("skuDTO处理BusinessTag:{}", skuDTO.getBusinessTag());
                if (Objects.equals(skuDTO.getBusinessTag(), ProductConfigBusinessTagEnum.整件区.getValue().intValue())) {
                    storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
                } else if (Objects.equals(skuDTO.getBusinessTag(),
                        ProductConfigBusinessTagEnum.拆零区.getValue().intValue())) {
                    storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
                }
            }

            if (Objects.isNull(storageAttribute)) {
                String skuStatisticsClassId = String.valueOf(skuStatisticsClassMap.get(skuDTO.getProductSkuId()));
                LOGGER.info("skuDTO处理skuStatisticsClassId:{}", skuStatisticsClassId);
                if (restCategoryValue.contains(skuStatisticsClassId)) {
                    storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
                } else {
                    storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
                }
            }

            skuDTO.setStorageAttribute(storageAttribute);
        });

        return productSkuDTOS.stream()
                .collect(Collectors.toMap(p -> p.getProductSkuId(), p -> p.getStorageAttribute(), (v1, v2) -> v1));
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param userId             用户ID
     * @param productLocationPOS 产品位置PO列表
     */
    public void updateSkuConfigStorageAttribute(Integer userId, List<ProductLocationPO> productLocationPOS) {
        if (CollectionUtils.isEmpty(productLocationPOS)) {
            return;
        }

        try {
            List<Long> skuIds = productLocationPOS.stream()
                    .filter(p -> p.getProductSku_Id() != null)
                    .map(p -> p.getProductSku_Id()).distinct()
                    .collect(Collectors.toList());

            ProductConfigStorageAttributeUpdateDTO updateDTO = new ProductConfigStorageAttributeUpdateDTO();
            updateDTO.setWarehouseId(productLocationPOS.get(0).getWarehouse_Id());
            updateDTO.setProductSkuIdList(skuIds);
            updateDTO.setUserId(userId);
            updateStorageAttributeBySkuIds(updateDTO);
        } catch (Exception e) {
            LOGGER.error("更新产品配置分仓属性异常 ", e);
        }
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param userId      用户ID
     * @param warehouseId 仓库ID
     * @param skuIds      SKU ID列表
     */
    public void updateSkuConfigStorageAttribute(Integer userId, Integer warehouseId, List<Long> skuIds) {
        if (warehouseId == null || CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        try {
            ProductConfigStorageAttributeUpdateDTO updateDTO = new ProductConfigStorageAttributeUpdateDTO();
            updateDTO.setWarehouseId(warehouseId);
            updateDTO.setProductSkuIdList(skuIds);
            updateDTO.setUserId(userId);
            updateStorageAttributeBySkuIds(updateDTO);
        } catch (Exception e) {
            LOGGER.error("更新产品配置分仓属性异常 ", e);
        }
    }

    /**
     * 更新产品配置分仓属性
     *
     * @param updateDTO 更新DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStorageAttributeBySkuIds(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "参数不能为空");
        AssertUtils.notNull(updateDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(updateDTO.getProductSkuIdList(), "skuId不能为空");
        LOGGER.info("更新产品配置分仓属性 入参:{}", JSON.toJSONString(updateDTO));

        // 通过仓库 id、skuId 计算其分仓属性
        Map<Long, Byte> storageAttributeMap =
                calcSkuStorageAttribute(updateDTO.getWarehouseId(), updateDTO.getProductSkuIdList());
        LOGGER.info("更新产品配置分仓属性 分仓属性计算结果:{}", JSON.toJSONString(storageAttributeMap));
        if (storageAttributeMap == null || storageAttributeMap.size() <= 0) {
            return;
        }

        List<ProductSkuConfigDTO> configDTOS = new ArrayList<>();
        storageAttributeMap.forEach((sukId, storageAttribute) -> {
            ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
            configDTO.setWarehouseId(updateDTO.getWarehouseId());
            configDTO.setProductSkuId(sukId);
            configDTO.setStorageAttribute(storageAttributeMap.get(sukId));
            configDTO.setLastUpdateUserId(updateDTO.getUserId());
            configDTOS.add(configDTO);
        });
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(configDTOS)) {
            return;
        }
        LOGGER.info("更新产品配置分仓属性 更新参数:{}", JSON.toJSONString(configDTOS));
        productSkuConfigBL.saveProductSkuConfigBatch(configDTOS);
    }

    /**
     * 按仓库更新产品配置分仓属性
     *
     * @param storageAttributeUpdateDTO
     */
    @Async(value = "warehouseProductTaskExecutor")
    @DistributeLock(conditions = "#storageAttributeUpdateDTO.warehouseId", sleepMills = 120000, expireMills = 120000,
            key = "updateStorageAttributeByWarehouseId", lockType = DistributeLock.LockType.WAITLOCK)
    public void updateStorageAttributeByWarehouseId(ProductConfigStorageAttributeUpdateDTO storageAttributeUpdateDTO) {
        AssertUtils.notNull(storageAttributeUpdateDTO, "参数不能为空");
        AssertUtils.notNull(storageAttributeUpdateDTO.getWarehouseId(), "仓库id不能为空");
        LOGGER.info("按仓库更新产品配置分仓属性 入参:{}", JSON.toJSONString(storageAttributeUpdateDTO));
        if (!updateStorageAttributeFlag(storageAttributeUpdateDTO.getWarehouseId())) {
            return;
        }

        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(storageAttributeUpdateDTO.getWarehouseId());
        storageAttributeUpdateDTO.setCityId(warehouse.getCityId());

        ProductSkuInfoSO productSkuInfoSO = new ProductSkuInfoSO();
        productSkuInfoSO.setCityId(storageAttributeUpdateDTO.getCityId());
        productSkuInfoSO.setWarehouseId(storageAttributeUpdateDTO.getWarehouseId());
        productSkuInfoSO.setLimitSku((byte) 1);
        int pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            PageHelper.startPage(pageNum, pageSize);
            PageResult<ProductSkuInfoDTO> pageResult = productSkuPOMapper.listProductSkuInfo(productSkuInfoSO);
            if (pageNum == 1) {
                pageCount = pageResult.getPager().getTotalPage();
            }
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pageResult.getResult())) {
                return;
            }

            List<Long> skuIds = pageResult.getResult().stream().filter(p -> p != null && p.getProductSkuId() != null)
                    .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            ProductConfigStorageAttributeUpdateDTO updateDTO = new ProductConfigStorageAttributeUpdateDTO();
            updateDTO.setWarehouseId(storageAttributeUpdateDTO.getWarehouseId());
            updateDTO.setProductSkuIdList(skuIds);
            updateDTO.setUserId(storageAttributeUpdateDTO.getUserId());
            // 分批更新产品配置分仓属性
            updateStorageAttributeBySkuIds(updateDTO);
        }
        LOGGER.info("按仓库更新产品配置分仓属性完成");
    }

    private boolean updateStorageAttributeFlag(Integer warehouseId) {
        boolean isUpdateFlag = true;
        List<WarehouseAllocationConfigDTO> configs =
                warehouseAllocationConfigService.getConfigByWarehouseId(warehouseId);
        if (configs.isEmpty()) {
            isUpdateFlag = false;
        }
        return isUpdateFlag;
    }

    /**
     * 获取skuId所属的仓库
     *
     * @param skuIds      SKU ID列表
     * @param warehouseId 仓库ID
     * @return 匹配的SKU ID列表
     */
    public List<Long> getSkuConfigBySkuIdsAndWarehouseId(List<Long> skuIds, Integer warehouseId) {
        if (org.springframework.util.CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        // 根据skuId查询产品sku配置
        List<ProductSkuConfigPO> productSkuConfigPOS =
                productSkuConfigMapper.getSkuConfigBySkuIdsAndWarehouseId(warehouseId, skuIds);
        List<Long> resultMap =
                productSkuConfigPOS.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        return resultMap;
    }

    /**
     * 计算获取产品配置分仓属性
     *
     * @param updateDTO
     */
    public Map<Long, Integer> getSkuStorageAttributeMap(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        LOGGER.info("计算获取产品配置分仓属性 入参:{}", JSON.toJSONString(updateDTO));
        // 通过仓库 id、skuId 计算其分仓属性
        Map<Long, Byte> storageAttributeMap =
                calcSkuStorageAttribute(updateDTO.getWarehouseId(), updateDTO.getProductSkuIdList());
        LOGGER.info("计算获取产品配置分仓属性 结果:{}", JSON.toJSONString(storageAttributeMap));
        if (storageAttributeMap.isEmpty()) {
            return Collections.emptyMap();
        }
        return storageAttributeMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue() == null ? null : e.getValue().intValue()));
    }
}
