package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dubbop.constant.EnableState;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ISubOrgService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.WareHouseConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.CityWarehouseMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WareHouseMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.WareHouseCascadeQuery;
import com.yijiupi.supplychain.serviceutils.constant.org.OrgClass;

/**
 * <AUTHOR>
 * @Description:
 * @date 2018/3/9 10:45
 */
@Service
public class StoreWareHouseBL {
    private static final Logger logger = LoggerFactory.getLogger(StoreWareHouseBL.class);
    @Autowired
    private WareHouseMapper wareHouseMapper;
    @Autowired
    private CityWarehouseMapper cityWarehouseMapper;
    // @ReferDubbop(path = ServerPath.BASE_SERVICE)
    // private WarehouseForSupplychainService warehouseForSupplychainService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Reference
    private ISubOrgService iSubOrgService;

    @Reference
    private IOrgService iOrgService;

    /**
     * @param cityId
     * @return WareHouseDTO
     * @Description: 获取城市默认仓库
     * <AUTHOR>
     * @date 2018/3/13 14:00
     */
    public WareHouseDTO getWareHouseByCityId(Integer cityId) {
        // logger.info("查询默认仓库:{}", cityId);
        WareHouseDTO wareHouseDTO = new WareHouseDTO();
        WareHousePO wareHousePO = wareHouseMapper.selectWareHouseByCityId(cityId);
        if (wareHousePO != null) {
            BeanUtils.copyProperties(wareHousePO, wareHouseDTO);
        } else {
            logger.info("cityID：{} 查询默认仓库为空", cityId);
        }
        return wareHouseDTO;
    }

    /**
     * @param shopId
     * @return WareHouseDTO
     * @Description: 获取经销商仓库
     */
    public WareHouseDTO selectWareHouseByShopId(Long shopId) {
        // logger.info("查询经销商仓库:{}", shopId);
        WareHouseDTO wareHouseDTO = new WareHouseDTO();
        WareHousePO wareHousePO = wareHouseMapper.selectWareHouseByShopId(shopId);
        if (wareHousePO != null) {
            BeanUtils.copyProperties(wareHousePO, wareHouseDTO);
        } else {
            logger.info("cityID：{} 查询经销商仓库为空", shopId);
        }
        return wareHouseDTO;
    }

    /**
     * @param
     * @return
     * @Description: 全量同步仓库以及仓库城市对应关系数据
     * <AUTHOR>
     * @date 2018/3/13 13:59
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void listAllWarehouseForSupplychain() {
        // List<Warehouse> warehouseList = warehouseForSupplychainService.listAllWarehouseForSupplychain();
        // if (!CollectionUtils.isEmpty(warehouseList)) {
        // List<WareHousePO> wareHousePOList = WareHouseConvert.convertToListWareHousePO(warehouseList);
        // List<Integer> warehouseIds = new ArrayList<>(300);
        // List<CityWarehousePO> cityWarehousePOList = new ArrayList<>(150);
        // warehouseList.forEach(e -> {
        // warehouseIds.add(e.getId());
        // if (!CollectionUtils.isEmpty(e.getCityWarehouseMQList())) {
        // cityWarehousePOList.addAll(WareHouseConvert.convertToCityWareHousePO(e.getCityWarehouseMQList()));
        // }
        // });
        // // 更新仓库信息
        // wareHouseMapper.insertWarehousePOBatch(wareHousePOList);
        // logger.info("更新仓库数量:{}", wareHousePOList.size());
        // // 删除城市仓库关系数据
        // cityWarehouseMapper.deleteCityWarehouseByWareHouseId(warehouseIds);
        // logger.info("删除城市仓库关系数据数量:{}", warehouseIds.size());
        // // 新增城市仓库关系数据
        // cityWarehouseMapper.insertCityWarehousePOBatch(cityWarehousePOList);
        // logger.info("新增城市仓库对应关系数量:{}", cityWarehousePOList.size());
        // } else {
        // logger.info("同步仓库城市数据为空");
        // }
    }

    /**
     * @param cityId
     * @return List<Integer> cityId 集合
     * @Description: 通过城市id获取所有同时将这这个仓库设置为默认仓库的的城市
     * <AUTHOR>
     * @date 2018/3/13 13:59
     */
    public List<Integer> getAllCityDefaultWareHouseByCityId(Integer cityId) {
        List<Integer> allCityByCityDefaultWareHouse = Lists.newArrayList();
        WareHousePO wareHousePO = wareHouseMapper.selectWareHouseByCityId(cityId);
        if (wareHousePO != null) {
            logger.info("cityID：{} 默认仓库为:{}", cityId, wareHousePO.getId());
            allCityByCityDefaultWareHouse = wareHouseMapper.getAllCityByCityDefaultWareHouse(wareHousePO.getId());
        } else {
            logger.info("cityID：{} 查询默认仓库为空", cityId);
        }
        return allCityByCityDefaultWareHouse;
    }

    public PageList<WareHouseDTO> getListWareHouse(WareHouseDTO wareHouseDTO, PagerCondition pager) {
        PageResult<WareHouseDTO> pageResult =
            wareHouseMapper.findWareHouseList(wareHouseDTO, pager.getCurrentPage(), pager.getPageSize());
        PageList<WareHouseDTO> toPageList = pageResult.toPageList();
        return toPageList;
    }

    public PageList<WareHouseDTO> findWareHouseByParentOrgId(WareHouseCascadeQuery query, PagerCondition pager) {
        AssertUtils.notNull(query, "查询参数不能为空");
        AssertUtils.notNull(query.getParentOrgId(), "机构ID不能为空");
        PageResult<WareHouseDTO> pageResult =
            wareHouseMapper.findWareHouseByParentOrgId(query, pager.getCurrentPage(), pager.getPageSize());
        PageList<WareHouseDTO> toPageList = pageResult.toPageList();
        return toPageList;
    }

    /**
     * @param warehouseId
     * @return List<Integer> cityId 集合
     * @Description: 通过仓库id获取所有默认使用这个仓库的城市
     */
    public List<Integer> getAllCityByWarehouseId(Integer warehouseId) {
        List<Integer> lstCityIds = wareHouseMapper.getAllCityByCityDefaultWareHouse(warehouseId);
        return lstCityIds;
    }

    /**
     * @param warehouseId
     * @return List<Integer> cityId 集合
     * @Description: 通过仓库id获取仓库的主要城市
     */
    public Integer getMajorCityByWarehouseId(Integer warehouseId) {
        Integer cityId = wareHouseMapper.getMajorCityByWarehouseId(warehouseId);
        return cityId;
    }

    public WareHouseDTO findWareHouseById(Integer warehouseId) {
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse != null) {
            return WareHouseConvertor.warehouse2WarehouseDTO(warehouse);
        } else {
            return null;
        }

    }

    public Map<Integer, String> findWareHouseNamesByIds(List<Integer> warehouseIds) {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            // logger.info("findWareHouseNamesByIds - 查询参数 warehouseIds 为空");
            return Collections.EMPTY_MAP;
        }
        List<WareHouseDTO> wareHouseList = wareHouseMapper.findWareHousesByIds(warehouseIds);
        if (CollectionUtils.isEmpty(wareHouseList)) {
            // logger.info("findWareHouseNamesByIds - 查询参数 warehouseIds : {} 未找到", StringUtils.join(warehouseIds, ","));
            return Collections.EMPTY_MAP;
        }
        Map<Integer, String> result = new HashMap<Integer, String>(wareHouseList.size());
        Map<Integer, List<WareHouseDTO>> wareHouseMap =
            wareHouseList.stream().collect(Collectors.groupingBy(WareHouseDTO::getId));
        for (Map.Entry<Integer, List<WareHouseDTO>> entry : wareHouseMap.entrySet()) {
            WareHouseDTO dto = entry.getValue().stream().filter(d -> d.getName() != null).findAny().orElse(null);
            if (dto != null) {
                result.put(entry.getKey(), dto.getName());
            }
        }
        return result;
    }

    /**
     * 定时生成仓库每日库存量 每天23点57分50秒执行50 57 23 * * ?
     */
    // @Scheduled(cron="50 57 23 * * ?")
    // public void insertProductstoreDays(){
    // logger.info("定时生成仓库每日库存量:{}", new Date());
    // wareHouseMapper.insertProductstoreDays();
    // }

    public void updateWarehouseAreaById(Integer warehouseId, BigDecimal length, BigDecimal width) {
        wareHouseMapper.updateWarehouseAreaById(warehouseId, length, width);
    }

    /**
     * 查询仓库状态
     * 
     * @param warehouseId
     * @return
     */
    public Integer queryWarehouseStatusByWarehouseId(Integer warehouseId) {
        return wareHouseMapper.queryWarehouseStatusByWarehouseId(warehouseId);
    }

    /**
     * 通过城市id获取仓库列表
     * 
     * @param cityId
     * @return
     */
    public List<WareHouseDTO> findWarehouseListForOPAdmin(Integer cityId) {
        List<OrgDTO> list = findAllCityListWithParent(cityId);
        if (CollectionUtils.isEmpty(list)) {
            throw new DataValidateException("找不到关联的城市！");
        }
        List<Integer> cityIdList = list.stream().map(t -> t.getId()).distinct().collect(Collectors.toList());
        List<WareHouseDTO> warehouseList = wareHouseMapper.listWarehouseDTOByCityIdList(cityIdList);
        if (CollectionUtils.isEmpty(warehouseList)) {
            throw new DataValidateException("找不到关联的仓库！");
        }
        logger.info(cityId + "查询到的仓库结果" + JSON.toJSONString(warehouseList));
        return warehouseList.stream()
            .filter(p -> EnableState.启用.value.equals(p.getState()) && p.getWarehouseType() != null
                && (isNeedReturn(p.getWarehouseType())) && p.getWarehouseClass() != null
                && p.getWarehouseClass().intValue() == 0)
            .collect(Collectors.toList());
    }

    /**
     * 查询所有组织机构
     * 
     * @param orgId
     * @return
     * @return: List<String>
     */
    public List<OrgDTO> findAllCityListWithParent(Integer orgId) {
        OrgDTO dto = iSubOrgService.findOne(orgId);
        if (dto == null) {
            throw new DataValidateException("查询不到对应的组织机构信息!");
        } else {
            List<OrgDTO> list = new ArrayList<>();
            if (OrgClass.ORG_CLASS_FIRST.equals(dto.getOrgClass())) {
                list.add(dto);
                List<OrgDTO> items = iSubOrgService.findCityMsg(orgId);
                if (!org.springframework.util.CollectionUtils.isEmpty(items)) {
                    list.addAll(items);
                }
            } else {
                list = iSubOrgService.findCityMsg(dto.getParentOrgId());
            }
            return list;
        }
    }

    private Boolean isNeedReturn(Byte warehouseType) {
        if (warehouseType.byteValue() == WarehouseTypeEnum.店仓合一.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.集货点.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.城市仓库.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.总部仓库.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.前置仓.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.网格仓.getType()) {
            return true;
        }
        if (warehouseType.byteValue() == WarehouseTypeEnum.联合仓.getType()) {
            return true;
        }
        return false;
    }

    /**
     * 通过城市id获取仓库列表
     * 
     * @param wareHouseDTO
     * @return
     */
    public String getProxyCompanyName(WareHouseDTO wareHouseDTO) {
        boolean isCityEmpty =
            null == wareHouseDTO || (null == wareHouseDTO.getId() && null == wareHouseDTO.getCityId());
        if (isCityEmpty) {
            return "";
        }
        Warehouse warehouse = warehouseQueryService.findWarehouseById(wareHouseDTO.getId());
        if (warehouse != null && StringUtils.isNotEmpty(warehouse.getProxyCompanyName())) {
            return warehouse.getProxyCompanyName();
        }

        OrgDTO orgDTO = iOrgService.getOrg(wareHouseDTO.getCityId());
        return orgDTO != null ? orgDTO.getOperatorName() : "";
    }
}
