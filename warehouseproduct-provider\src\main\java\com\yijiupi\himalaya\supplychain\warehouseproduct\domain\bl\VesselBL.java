package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.TransferVesselToLocationDTO;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskItemManageService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.CheckBatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemQueryDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.ReplenishmentTaskEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IReplenishmentQueryService;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CodeGenerator;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ProductIdGenerator;
import com.yijiupi.himalaya.supplychain.wcs.dto.location.WCSBoxStateBatchModDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.location.WCSBoxStateModDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IWCSLocationModService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 *
 * <AUTHOR> 2022/10/20
 */
@Service
public class VesselBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(VesselBL.class);

    @Autowired
    private AgencyConnectLocationMapper agencyConnectLocationMapper;

    @Autowired
    private LocationPOMapper locationPOMapper;

    @Autowired
    private LocationAreaPOMapper locationAreaPOMapper;

    @Autowired
    private ProductLocationPOMapper productLocationPOMapper;

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private ProductIdGenerator productIdGenerator;

    @Autowired
    private VesselInfoPOMapper vesselInfoPOMapper;

    private static final String VESSEL_PREFIX = "WL-";

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;

    @Reference(timeout = 30000)
    private IStoreTransferOrderService iStoreTransferOrderService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference(timeout = 30000)
    private IWCSLocationModService iWCSLocationModService;

    @Reference(timeout = 30000)
    private IReplenishmentQueryService iReplenishmentQueryService;

    @Reference
    private IBatchTaskItemManageService iBatchTaskItemManageService;

    @Autowired
    private StoreWareHouseBL storeWareHouseBL;

    @Autowired
    private PassageMapper passageMapper;

    @Autowired
    private PassageItemMapper passageItemMapper;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private NotifyWCSBL notifyWCSBL;

    @Autowired
    private ProductStoreBatchPOMapper productStoreBatchPOMapper;

    @Autowired
    private ProductLocationBL productLocationBL;

    /**
     * 分页条件查询容器信息
     * 
     * @param vesselInfoQueryDTO
     * @return
     */
    public PageList<VesselDTO> pageListVessel(VesselInfoQueryDTO vesselInfoQueryDTO) {
        LOGGER.info("VesselBL.pageListVessel 入参：{}", JSON.toJSONString(vesselInfoQueryDTO));
        AssertUtils.notNull(vesselInfoQueryDTO, "查询参数不能为空");
        AssertUtils.notNull(vesselInfoQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(vesselInfoQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(vesselInfoQueryDTO.getCategory(), "货位类型不能为空");
        PageResult<VesselDTO> pageResult = locationPOMapper.pageListVesselLocation(vesselInfoQueryDTO,
            vesselInfoQueryDTO.getPageNum(), vesselInfoQueryDTO.getPageSize());
        List<VesselDTO> locationDTOS = pageResult.getResult();
        PageList<VesselDTO> pageList = new PageList<>();
        pageList.setDataList(locationDTOS);
        pageList.setPager(pageResult.getPager());
        return pageList;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void addVesselInfo(LoactionDTO dto) {
        LOGGER.info("VesselBL.addVesselInfo 入参：{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "新增参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getName(), "物料箱号不能为空");

        // 添加库位信息
        LocationPO record = new LocationPO(dto);
        record.setCreateTime(new Date());
        record.setCreateUserId(dto.getUserId());
        // String locationName = getName(dto, true);
        record.setName(dto.getName());
        LocationPO po = locationPOMapper.selectVesselByName(dto.getName(), dto.getWarehouseId(), dto.getCityId());
        if (po != null) {
            throw new BusinessValidateException("物料箱号重复，已有名称为" + record.getName() + "的物料箱号！");
        }

        // 检查容器号是否存在
        VesselInfoPO infoPO = vesselInfoPOMapper.selectByLocationNo(dto.getName(), record.getWarehouse_Id());
        if (infoPO != null) {
            throw new BusinessValidateException("物料箱号重复，已有名称为" + record.getName() + "的物料箱号！");
        }

        record.setId(UUIDGenerator.getUUID(LocationPO.class.getName()));
        record.setCategory(CategoryEnum.CARGO_VESSEL.getValue().byteValue());
        // 物料箱可混批不混放
        record.setIsChaosPut((byte)0);
        locationPOMapper.insertSelective(record);

        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(dto.getWarehouseId());

        // 添加容器信息表信息
        VesselInfoPO vesselInfoPO = new VesselInfoPO();
        vesselInfoPO.setId(UUIDGenerator.getUUID(VesselInfoPO.class.getName()));
        vesselInfoPO.setWarehouseId(record.getWarehouse_Id());
        vesselInfoPO.setLocationId(record.getId());
        vesselInfoPO.setLocationNo(dto.getName());
        vesselInfoPO.setIsFreeze(IsFreezeEnum.未冻结.getType());
        vesselInfoPO.setCreateUser(null != dto.getUserId() ? dto.getUserId().toString() : "");
        vesselInfoPO.setCreateTime(new Date());
        vesselInfoPOMapper.insertSelective(vesselInfoPO);
    }

    /**
     * 按货位创建物料箱
     *
     */
    public void addVesselByLocationIds(VesselDTO dto) {
        LOGGER.info("VesselBL.addVesselByLocationId 入参：{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "新增参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(dto.getLocationIds(), "货位id不能为空");
        // 检查并返回可新增物料箱的货位信息
        List<LoactionDTO> loactionDTOList = validateAddVesselData(dto);
        // 新增物料箱及物料箱信息数据
        List<LocationPO> insertLocationPOS = insertVessel(loactionDTOList, dto);
        // 组装移库数据进行移库
        updateStoreTranferNoOrder(insertLocationPOS, dto);
    }

    public List<LoactionDTO> validateAddVesselData(VesselDTO dto) {
        // 检查对应货位是否存在物料箱
        List<Long> locationIds = dto.getLocationIds().stream().collect(Collectors.toList());
        List<LocationPO> locationPOList = locationPOMapper.listVesselByAreaIds(locationIds, dto.getWarehouseId());
        LOGGER.info("VesselBL.addVesselByLocationId 已存在物料箱查询结果：{}", JSON.toJSONString(locationPOList));
        if (!CollectionUtils.isEmpty(locationPOList)) {
            List<Long> areaIds =
                locationPOList.stream().map(LocationPO::getArea_Id).distinct().collect(Collectors.toList());
            locationIds.removeIf(p -> p != null && areaIds.contains(p));
            if (CollectionUtils.isEmpty(locationIds)) {
                throw new BusinessValidateException("所选货位物料箱已存在！");
            }
        }

        // 查询对应货位
        List<LoactionDTO> loactionDTOList = locationPOMapper.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            throw new BusinessValidateException("所选货位信息不存在！");
        }

        // 检查容器号是否存在
        List<String> vesselLocationName = loactionDTOList.stream().filter(p -> !StringUtils.isEmpty(p.getName()))
            .map(p -> VESSEL_PREFIX + p.getName()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vesselLocationName)) {
            throw new BusinessValidateException("所选货位名称不存在！");
        }

        List<LocationPO> existWlNamePO = locationPOMapper.findLocationByNames(vesselLocationName, dto.getWarehouseId());
        LOGGER.info("VesselBL.addVesselByLocationId 检查容器号是否存在入参：{},结果：{}", JSON.toJSONString(vesselLocationName),
            JSON.toJSONString(existWlNamePO));
        if (!CollectionUtils.isEmpty(existWlNamePO)) {
            List<String> existWlName =
                existWlNamePO.stream().map(LocationPO::getName).distinct().collect(Collectors.toList());
            // loactionDTOList.removeIf(p -> existWlName.contains(VESSEL_PREFIX + p.getName()));
            List<String> errorNames =
                loactionDTOList.stream().filter(p -> existWlName.contains(VESSEL_PREFIX + p.getName()))
                    .map(LoactionDTO::getName).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(errorNames)) {
                throw new BusinessValidateException(
                    String.format("所选货位已存在对应物料箱！ Name:%s", JSON.toJSONString(errorNames)));
            }
        }

        return loactionDTOList;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<LocationPO> insertVessel(List<LoactionDTO> loactionDTOList, VesselDTO dto) {
        List<LocationPO> insertLocationPOS = new ArrayList<>();
        List<VesselInfoPO> insertVesselInfoPOS = new ArrayList<>();
        List<VesselPassageUpdateDTO> updateDTOList = new ArrayList<>();
        loactionDTOList.stream().forEach(p -> {
            LocationPO record = new LocationPO(p);
            Long locationId = p.getId();
            String locationName = p.getName();
            record.setArea_Id(locationId);
            record.setName(VESSEL_PREFIX + locationName);
            record.setCategory(CategoryEnum.CARGO_VESSEL.getValue().byteValue());
            record.setId(UUIDGenerator.getUUID(LocationPO.class.getName()));
            record.setCreateUserId(dto.getUserId());
            record.setCreateTime(new Date());
            record.setLastUpdateUserId(dto.getUserId());
            record.setLastUpdateTime(new Date());
            record.setState((byte)EnableStateEnum.ENABLE.getType());
            // 物料箱可混批不混放
            record.setIsChaosPut((byte)0);
            insertLocationPOS.add(record);

            VesselInfoPO vesselInfoPO = new VesselInfoPO();
            vesselInfoPO.setId(UUIDGenerator.getUUID(VesselInfoPO.class.getName()));
            vesselInfoPO.setWarehouseId(record.getWarehouse_Id());
            vesselInfoPO.setLocationId(record.getId());
            vesselInfoPO.setLocationNo(record.getName());
            vesselInfoPO.setCurrentLocationId(locationId);
            vesselInfoPO.setCurrentLocationName(locationName);
            vesselInfoPO.setIsFreeze(IsFreezeEnum.未冻结.getType());
            vesselInfoPO.setCreateUser(null != dto.getUserId() ? dto.getUserId().toString() : "");
            vesselInfoPO.setCreateTime(new Date());
            insertVesselInfoPOS.add(vesselInfoPO);

            VesselPassageUpdateDTO updateDTO = new VesselPassageUpdateDTO();
            updateDTO.setWarehouseId(p.getWarehouseId());
            updateDTO.setVesselId(record.getId());
            updateDTO.setVesselName(record.getName());
            updateDTO.setLocationId(locationId);
            updateDTO.setLocationNo(locationName);
            updateDTOList.add(updateDTO);
        });

        // 批量新增物料箱信息
        locationPOMapper.insertSelectiveBatch(insertLocationPOS);
        // 保存当前仓库最近修改货位操作时间
        codeGenerator.updateLocationDeleteTime(dto.getWarehouseId());
        // 批量新增容器信息
        vesselInfoPOMapper.insertSelectiveBatch(insertVesselInfoPOS);
        // 批量更新容器通道配置明细
        updatePassageItem(updateDTOList);

        return insertLocationPOS;
    }

    public void updateStoreTranferNoOrder(List<LocationPO> insertLocationPOS, VesselDTO dto) {
        LOGGER.info("VesselBL.updateStoreTranferNoOrder 移库入参：{}", JSON.toJSONString(insertLocationPOS));

        try {
            Thread.sleep(3000);

            Map<Long, List<LocationPO>> areaIdmap =
                insertLocationPOS.stream().collect(Collectors.groupingBy(LocationPO::getArea_Id));

            // 按货位查询批次库存
            List<Long> moveLocationIds =
                insertLocationPOS.stream().map(LocationPO::getArea_Id).collect(Collectors.toList());
            BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
            batchInventoryQueryDTO.setWarehouseId(dto.getWarehouseId());
            batchInventoryQueryDTO.setLocationIds(moveLocationIds);
            batchInventoryQueryDTO.setPageNum(1);
            batchInventoryQueryDTO.setPageSize(Integer.MAX_VALUE);
            PageList<BatchInventoryDTO> batchInventoryList =
                iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
            LOGGER.info("VesselBL.addVesselByLocationId 查询批次库存入参：{},结果：{}", JSON.toJSONString(batchInventoryQueryDTO),
                JSON.toJSONString(batchInventoryList));
            if (null == batchInventoryList || CollectionUtils.isEmpty(batchInventoryList.getDataList())) {
                return;
            }

            Map<Integer, String> warehosueMap = getWarehouseMap(Arrays.asList(dto.getWarehouseId()));
            // 无单移库
            List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
            StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
            storeTransferOrderDTO.setOrg_id(dto.getCityId());
            storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
            storeTransferOrderDTO.setStartTime(new Date());
            storeTransferOrderDTO.setWarehouse_Id(dto.getWarehouseId());
            storeTransferOrderDTO
                .setWarehouseName(warehosueMap != null ? warehosueMap.get(dto.getWarehouseId()) : null);
            storeTransferOrderDTO.setSorter_id(dto.getUserId());
            storeTransferOrderDTO.setSorterName(dto.getName());
            storeTransferOrderDTO.setRemark("");
            storeTransferOrderDTO.setCreateUser(dto.getUserName());
            storeTransferOrderDTO.setLastupdateuser(dto.getUserName());
            List<BatchInventoryDTO> batchInventoryDTOS = batchInventoryList.getDataList();
            batchInventoryDTOS.stream().forEach(p -> {

                List<LocationPO> vesselLocation = areaIdmap.get(p.getLocationId());
                if (CollectionUtils.isEmpty(vesselLocation)) {
                    throw new BusinessValidateException("所选货位没有对应物料箱信息！" + p.getLocationName());
                }
                Long toLocationId = vesselLocation.get(0).getId();
                String toLocationName = vesselLocation.get(0).getName();

                // 检查产品能否存放到某个货位上
                CheckBatchInventoryDTO checkBatchInventoryDTO = new CheckBatchInventoryDTO();
                checkBatchInventoryDTO.setLocationId(toLocationId);
                checkBatchInventoryDTO.setProductSkuId(p.getProductSkuId());
                checkBatchInventoryDTO.setLocationName(toLocationName);
                checkBatchInventoryDTO.setChannel(p.getChannel());
                checkBatchInventoryDTO.setBatchTime(p.getBatchTime());
                checkBatchInventoryDTO.setProductionDate(p.getProductionDate());
                checkBatchInventoryDTO.setWarehouseId(p.getWarehouseId());
                // checkBatchInventoryDTO.setChaosPut((byte) 0);
                // checkBatchInventoryDTO.setChaosBatch(vesselLocation.get(0).getIsChaosBatch());
                String msg = "";
                Boolean isCanIn = iBatchInventoryQueryService.checkIsCanPutIntoLocation(checkBatchInventoryDTO, msg);
                if (!isCanIn) {
                    throw new BusinessValidateException(msg + "移库目标货位：" + toLocationName);
                }

                StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
                storeTransferOrderItemDTO.setOrg_id(dto.getCityId());
                storeTransferOrderItemDTO.setState(StoreTransferStateEnum.待移库.getType());
                storeTransferOrderItemDTO.setOwnerName(p.getOwnerName());
                storeTransferOrderItemDTO.setSkuId(p.getProductSkuId());
                storeTransferOrderItemDTO.setProductName(p.getProductSkuName());
                storeTransferOrderItemDTO.setSpecName(p.getSpecificationName());
                storeTransferOrderItemDTO.setSpecQuantity(p.getPackageQuantity());
                storeTransferOrderItemDTO.setPackageName(p.getPackageName());
                storeTransferOrderItemDTO.setPackageCount(p.getStoreCountMax());
                storeTransferOrderItemDTO.setUnitName(p.getUnitName());
                storeTransferOrderItemDTO.setUnitCount(p.getStoreCountMin());
                storeTransferOrderItemDTO.setUnitTotalCount(p.getStoreTotalCount());
                storeTransferOrderItemDTO.setOverMovePackageCount(p.getStoreCountMax());
                storeTransferOrderItemDTO.setOverMoveUnitCount(p.getStoreCountMin());
                storeTransferOrderItemDTO.setFromLocation_id(p.getLocationId());
                storeTransferOrderItemDTO.setFromLocationName(p.getLocationName());
                storeTransferOrderItemDTO.setToLocation_id(toLocationId);
                storeTransferOrderItemDTO.setToLocationName(toLocationName);
                storeTransferOrderItemDTO.setToChannel(null != p.getChannel() ? p.getChannel().toString() : "");
                storeTransferOrderItemDTO.setCreateUser(dto.getUserName());
                storeTransferOrderItemDTO.setLastupdateuser(dto.getUserName());
                storeTransferOrderItemDTO.setProductionDate(p.getProductionDate());
                storeTransferOrderItemDTO.setBatchTime(p.getBatchTime());
                storeTransferOrderItemDTO.setProductSpecificationId(p.getProductSpecificationId());
                storeTransferOrderItemDTO.setSecOwnerId(p.getSecOwnerId());
                // "isStockAgeStrategy": false,
                storeTransferOrderItemDTO.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
                storeTransferOrderItemDTOS.add(storeTransferOrderItemDTO);
            });

            // 物料箱不能混放
            if (dto.getTransferToVessel() == null || dto.getTransferToVessel()) {
                Map<Long, String> noMoveMap = new HashMap<>(16);
                Map<Long, List<StoreTransferOrderItemDTO>> toLocationIdMap = storeTransferOrderItemDTOS.stream()
                    .collect(Collectors.groupingBy(StoreTransferOrderItemDTO::getToLocation_id));
                toLocationIdMap.forEach((toLocationId, itemList) -> {
                    List<Long> skuIds = itemList.stream().map(StoreTransferOrderItemDTO::getSkuId).distinct()
                        .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(skuIds) && skuIds.size() > 1) {
                        noMoveMap.put(toLocationId, itemList.get(0).getToLocationName());
                    }
                });
                LOGGER.info("VesselBL.updateStoreTranferNoOrder 物料箱不允许放不同产品：{}", JSON.toJSONString(noMoveMap));
                if (noMoveMap != null && noMoveMap.size() > 0) {
                    // // 过滤无法移库物料箱
                    // storeTransferOrderItemDTOS.removeIf(p ->
                    // !StringUtils.isEmpty(noMoveMap.get(p.getToLocation_id())));
                    throw new BusinessValidateException("物料箱不允许放不同产品，物料箱：{}！" + JSON.toJSONString(noMoveMap));
                }
            }

            storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
            LOGGER.info("VesselBL.updateStoreTranferNoOrder 移库参数组装：{}", JSON.toJSONString(storeTransferOrderDTO));

            checkPrice(storeTransferOrderDTO);
            iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);
        } catch (Exception e) {
            LOGGER.info("VesselBL.updateStoreTranferNoOrder 移库失败：", e);
            throw new BusinessValidateException("自动移库失败，需进行人工移库!");
        }
    }

    /**
     * 金额校验
     */
    public void checkPrice(StoreTransferOrderDTO storeTransferOrderDTO) {
        if (!Objects.equals(storeTransferOrderDTO.getTransferType(), StoreTransferEnum.处理品转入移库.getType())) {
            return;
        }

        Map<Long, BigDecimal> skuCount = new HashMap<>(16);
        storeTransferOrderDTO.getStoreTransferOrderItemDTOS().forEach(item -> {
            BigDecimal unitTotalCount = skuCount.get(item.getSkuId());
            BigDecimal totalCount =
                item.getOverMovePackageCount().multiply(item.getSpecQuantity()).add(item.getOverMoveUnitCount());
            if (unitTotalCount == null) {
                unitTotalCount = totalCount;
            } else {
                unitTotalCount = unitTotalCount.add(totalCount);
            }
            skuCount.put(item.getSkuId(), unitTotalCount);
        });

        DefectiveInventoryPriceDTO defectiveProductPrice = iBatchInventoryManageService.calCcpPriceBySkuCount(
            storeTransferOrderDTO.getOrg_id(), storeTransferOrderDTO.getWarehouse_Id(), skuCount);
        LOGGER.info("校验残次品金额，本次sku数量：{}，累加后：{}", skuCount, JSON.toJSONString(defectiveProductPrice));
        if (defectiveProductPrice != null && defectiveProductPrice.getRemainingAmount() != null
            && defectiveProductPrice.getRemainingAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessValidateException("残次品转入超出仓库残次品累计金额上限");
        }
    }

    private String getName(LoactionDTO dto, Boolean regex) {
        StringBuilder builder = new StringBuilder();
        if (regex) {
            LocationAreaPO locationAreaPO = locationAreaPOMapper.getLocationAreaById(dto.getArea_Id());
            if (locationAreaPO == null) {
                throw new RuntimeException("无法获取到货区信息");
            }
            builder.append(getString(locationAreaPO.getName(), false));
        } else {
            builder.append(getString(dto.getArea(), false));
        }
        builder.append(getString(dto.getRoadway(), false));
        builder.append(getString(dto.getProductlocation(), true));
        return builder.toString();
    }

    private String getString(String name, Boolean symble) {
        if (name == null || "".equals(name.trim())) {
            return "";
        }
        return (symble ? "-" : "") + name;
    }

    /**
     * 根据skuid查询容器信息
     * 
     * @param skuIdList
     * @return
     */
    public Map<Long, VesselDetailsDTO> listSKUBesselDetails(List<Long> skuIdList, Integer warehouseId) {
        LOGGER.info("VesselBL.listSKUBesselDetails 入参：{}", JSON.toJSONString(skuIdList));
        Map<Long, VesselDetailsDTO> resultMap = new HashMap<>(16);
        List<VesselDetailsDTO> list = locationPOMapper.listSKUBesselDetails(skuIdList, warehouseId);
        if (CollectionUtils.isEmpty(list)) {
            return resultMap;
        }

        resultMap = list.stream().filter(p -> p.getSkuId() != null)
            .collect(Collectors.toMap(VesselDetailsDTO::getSkuId, Function.identity(), (key1, key2) -> key2));
        return resultMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void vesselRelationLocation(LoactionDTO updateDTO) {
        LOGGER.info("VesselBL.vesselRelationLocation 入参：{}", JSON.toJSONString(updateDTO));

        // 检查物料箱是否存在
        LocationPO vesselLocationPO = locationPOMapper.findLocationById(updateDTO.getId());
        if (vesselLocationPO == null
            || !Objects.equals(vesselLocationPO.getCategory(), CategoryEnum.CARGO_VESSEL.getValue().byteValue())) {
            throw new BusinessValidateException("所选物料箱不存在！");
        }

        // 检查货位是否存在
        LocationPO locationPO = locationPOMapper.findLocationById(updateDTO.getArea_Id());
        if (locationPO == null
            || Objects.equals(locationPO.getCategory(), CategoryEnum.CARGO_VESSEL.getValue().byteValue())) {
            throw new BusinessValidateException("所选货位信息不存在！");
        }

        // 检查所选货位是否已关联物料箱
        List<LocationPO> locationPOList =
            locationPOMapper.listVesselByAreaIds(Arrays.asList(updateDTO.getArea_Id()), updateDTO.getWarehouseId());
        if (!CollectionUtils.isEmpty(locationPOList)) {
            throw new BusinessValidateException("所选货位已关联其他物料箱，请重新选择货位！");
        }

        LoactionDTO updateLoactionDTO = new LoactionDTO();
        // BeanUtils.copyProperties(locationPO, updateLoactionDTO);不更新物料箱其他信息
        updateLoactionDTO.setArea_Id(locationPO.getId());
        updateLoactionDTO.setId(vesselLocationPO.getId());
        updateLoactionDTO.setName(vesselLocationPO.getName());
        updateLoactionDTO.setCategory(CategoryEnum.CARGO_VESSEL.getValue().byteValue());
        updateLoactionDTO.setUserId(updateDTO.getUserId());

        // 修改物料箱信息
        locationPOMapper.updateSelectiveById(updateLoactionDTO);

        // 新增容器通道明细配置
        VesselPassageUpdateDTO vsselPassageUpdateDTO = new VesselPassageUpdateDTO();
        vsselPassageUpdateDTO.setWarehouseId(updateDTO.getWarehouseId());
        vsselPassageUpdateDTO.setVesselId(vesselLocationPO.getId());
        vsselPassageUpdateDTO.setVesselName(vesselLocationPO.getName());
        vsselPassageUpdateDTO.setLocationId(locationPO.getId());
        vsselPassageUpdateDTO.setLocationNo(locationPO.getName());
        updatePassageItem(Arrays.asList(vsselPassageUpdateDTO));

        // // 保存当前仓库最近修改货位操作时间
        // codeGenerator.updateLocationDeleteTime(updateDTO.getWarehouseId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateVesselInfoFreezeState(VesselInfoModifyDTO updateDTO) {
        LOGGER.info("VesselBL.updateVesselInfoFreezeState 入参：{}", JSON.toJSONString(updateDTO));

        // 检查容器号是否存在
        List<VesselInfoPO> infoPOS = vesselInfoPOMapper.findInfoByIds(updateDTO.getIds());
        if (CollectionUtils.isEmpty(infoPOS)) {
            throw new BusinessValidateException("物料箱信息不存在，请重新查询！");
        }

        Integer warehouseId = infoPOS.get(0).getWarehouseId();

        // 批量更新物料箱状态
        List<VesselInfoPO> updatePOList = new ArrayList<>();
        infoPOS.stream().forEach(p -> {
            VesselInfoPO updatePO = new VesselInfoPO();
            updatePO.setId(p.getId());
            updatePO.setLocationId(p.getLocationId());
            updatePO.setLocationNo(p.getLocationNo());
            updatePO.setRelationLocationId(p.getRelationLocationId());
            updatePO.setRelationLocationNo(p.getRelationLocationNo());
            byte updateIsFreeze = updateDTO.getIsFreeze() != null ? updateDTO.getIsFreeze()
                : Objects.equals(IsFreezeEnum.未冻结.getType(), p.getIsFreeze()) ? IsFreezeEnum.冻结.getType()
                    : IsFreezeEnum.未冻结.getType();
            updatePO.setIsFreeze(updateIsFreeze);
            updatePOList.add(updatePO);
        });

        // 检查物料箱是否存在未完成的补货任务
        Integer cityId = null;
        WareHouseDTO wareHouseDTO = storeWareHouseBL.findWareHouseById(warehouseId);
        if (wareHouseDTO == null) {
            throw new BusinessValidateException("仓库信息不存在！");
        }
        cityId = wareHouseDTO.getCityId();

        if (null == updateDTO.getNotCheckReplenishmentTaskState() || !updateDTO.getNotCheckReplenishmentTaskState()) {
            List<Long> vesselIds =
                updatePOList.stream().filter(p -> Objects.equals(IsFreezeEnum.未冻结.getType(), p.getIsFreeze()))
                    .map(VesselInfoPO::getLocationId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(vesselIds)) {
                ReplenishmentTaskItemQueryDTO itemQueryDTO = new ReplenishmentTaskItemQueryDTO();
                itemQueryDTO.setWarehouseId(warehouseId);
                itemQueryDTO.setOrgId(cityId);
                itemQueryDTO.setToLocationIds(vesselIds);
                itemQueryDTO.setTaskStates(Arrays.asList(ReplenishmentTaskEnum.待领取.getType(),
                    ReplenishmentTaskEnum.待补货.getType(), ReplenishmentTaskEnum.补货中.getType()));
                PageList<ReplenishmentTaskItemDTO> pageList =
                    iReplenishmentQueryService.pageListReplenishmentTaskItem(itemQueryDTO);
                if (pageList != null && !CollectionUtils.isEmpty(pageList.getDataList())) {
                    List<String> locationNames =
                        pageList.getDataList().stream().filter(p -> !StringUtils.isEmpty(p.getToLocationName()))
                            .map(ReplenishmentTaskItemDTO::getToLocationName).collect(Collectors.toList());
                    throw new BusinessValidateException(
                        String.format("物料箱对应补货任务未完成，无法解冻！物料箱:%s", JSON.toJSONString(locationNames)));
                }
            }
        }

        // 新增容器信息数据
        vesselInfoPOMapper.updateBatch(updatePOList);

        // 状态变更通知wcs参数
        WCSBoxStateBatchModDTO dto = new WCSBoxStateBatchModDTO();
        List<WCSBoxStateModDTO> boxList = new ArrayList<>();
        updatePOList.stream().forEach(p -> {
            WCSBoxStateModDTO wcsBoxStateModDTO = new WCSBoxStateModDTO();
            wcsBoxStateModDTO.setBoxId(p.getLocationId());
            wcsBoxStateModDTO.setBoxName(p.getLocationNo());
            wcsBoxStateModDTO.setAreaId(p.getRelationLocationId());
            wcsBoxStateModDTO.setAreaName(p.getRelationLocationNo());
            wcsBoxStateModDTO.setState(String.valueOf(p.getIsFreeze()));
            boxList.add(wcsBoxStateModDTO);
        });
        dto.setBoxList(boxList);
        dto.setOrgId(cityId);
        dto.setWarehouseId(warehouseId.toString());
        LOGGER.info("VesselBL.updateVesselInfoFreezeState 状态变更通知wcs参数：{}", JSON.toJSONString(dto));
        iWCSLocationModService.batchBoxStateMod(dto);

        // // 保存当前仓库最近修改货位操作时间
        // codeGenerator.updateLocationDeleteTime(updateDTO.getWarehouseId());
    }

    /**
     * 查询仓库名称
     */
    private Map<Integer, String> getWarehouseMap(List<Integer> warhouseIds) {
        if (CollectionUtils.isEmpty(warhouseIds)) {
            return null;
        }
        // 查询仓库名称
        List<Warehouse> warehouseList = iWarehouseQueryService.listWarehouseByIds(warhouseIds);
        if (CollectionUtils.isEmpty(warehouseList)) {
            return null;
        }
        Map<Integer, String> warehosueMap = new HashMap<>(16);
        warehouseList.forEach(warehouse -> {
            warehosueMap.put(warehouse.getId(), warehouse.getName());
        });
        return warehosueMap;
    }

    /**
     * 创建人工移库单
     */
    public void addStoreTransferOrder(List<VesselMoveDTO> dtoList) {
        LOGGER.info("VesselBL.addStoreTransferOrder 入参：{}", JSON.toJSONString(dtoList));

        Integer cityId = dtoList.get(0).getCityId();
        Integer warehouseId = dtoList.get(0).getWarehouseId();
        Map<Integer, String> warehosueMap = getWarehouseMap(Arrays.asList(warehouseId));
        List<Long> vesselIds = dtoList.stream().map(VesselMoveDTO::getVesselId).distinct().collect(Collectors.toList());
        Map<Long, VesselMoveDTO> vesselIdMap = dtoList.stream().filter(p -> p.getVesselId() != null)
            .collect(Collectors.toMap(VesselMoveDTO::getVesselId, Function.identity(), (key1, key2) -> key2));
        List<VesselDTO> moveVesselDTOList = new ArrayList<>();

        // 检查容器是否存在
        VesselInfoQueryDTO vesselInfoQueryDTO = new VesselInfoQueryDTO();
        vesselInfoQueryDTO.setCityId(cityId);
        vesselInfoQueryDTO.setWarehouseId(warehouseId);
        vesselInfoQueryDTO.setCategory(CategoryEnum.CARGO_VESSEL.getValue().byteValue());
        vesselInfoQueryDTO.setVesselLocationIds(vesselIds);
        PageResult<VesselDTO> pageResult = locationPOMapper.pageListVesselLocation(vesselInfoQueryDTO,
            vesselInfoQueryDTO.getPageNum(), vesselInfoQueryDTO.getPageSize());
        moveVesselDTOList = pageResult.getResult();
        if (CollectionUtils.isEmpty(moveVesselDTOList)) {
            throw new BusinessValidateException("人工移库容器信息不存在！，容器ids:" + JSON.toJSONString(vesselIds));
        }

        // 检查移库目标货位是否已绑定其他物料箱当前货位
        List<Long> toLocationIds =
            dtoList.stream().map(VesselMoveDTO::getToLocationId).distinct().collect(Collectors.toList());
        VesselInfoQueryDTO queryDTO = new VesselInfoQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setCurrentLocationIds(toLocationIds);
        List<VesselInfoPO> infoPOS = vesselInfoPOMapper.selectByConditions(queryDTO);
        if (!CollectionUtils.isEmpty(infoPOS)) {
            StringBuilder errorMessage = new StringBuilder("移库目标货位已绑定其他物料箱!\n");
            infoPOS.stream().forEach(p -> {
                errorMessage
                    .append(String.format("目标货位 :%s,对应物料箱:%s %n", p.getCurrentLocationName(), p.getLocationNo()));
            });
            throw new BusinessValidateException(errorMessage.toString());

            // 判断是否冻结
        }

        // 创建移库单
        List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
        StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
        storeTransferOrderDTO.setOrg_id(cityId);
        storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
        storeTransferOrderDTO.setTransferType(StoreTransferEnum.容器移库.getType());
        storeTransferOrderDTO.setStartTime(new Date());
        storeTransferOrderDTO.setWarehouse_Id(warehouseId);
        storeTransferOrderDTO.setWarehouseName(warehosueMap != null ? warehosueMap.get(warehouseId) : null);
        storeTransferOrderDTO.setSorter_id(dtoList.get(0).getUserId());
        storeTransferOrderDTO.setSorterName(dtoList.get(0).getUserName());
        storeTransferOrderDTO.setRemark("");
        storeTransferOrderDTO.setCreateUser(dtoList.get(0).getUserName());
        storeTransferOrderDTO.setLastupdateuser(dtoList.get(0).getUserName());
        moveVesselDTOList.stream().forEach(p -> {
            // 物料箱对应移库数据
            VesselMoveDTO vesselMoveDTO = vesselIdMap.get(p.getId());

            StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
            storeTransferOrderItemDTO.setOrg_id(p.getCityId());
            storeTransferOrderItemDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderItemDTO.setSkuId(p.getProductSkuId());
            storeTransferOrderItemDTO.setProductName(p.getProductName());
            storeTransferOrderItemDTO.setSpecName(p.getSpecificationName());
            storeTransferOrderItemDTO.setSpecQuantity(p.getPackageQuantity());
            storeTransferOrderItemDTO.setPackageName(p.getPackageName());
            storeTransferOrderItemDTO.setUnitName(p.getUnitName());
            storeTransferOrderItemDTO.setUnitTotalCount(p.getStoreTotalCount());
            storeTransferOrderItemDTO.setFromLocation_id(vesselMoveDTO.getFromLocationId());
            storeTransferOrderItemDTO.setFromLocationName(vesselMoveDTO.getFromLocationName());
            storeTransferOrderItemDTO.setToLocation_id(vesselMoveDTO.getToLocationId());
            storeTransferOrderItemDTO.setToLocationName(vesselMoveDTO.getToLocationName());
            storeTransferOrderItemDTO.setCreateUser(vesselMoveDTO.getUserName());
            storeTransferOrderItemDTO.setLastupdateuser(vesselMoveDTO.getUserName());
            // storeTransferOrderItemDTO.setOwnerName(p.getOwnerName());
            // storeTransferOrderItemDTO.setToChannel(null != p.getChannel() ? p.getChannel().toString() : "");
            // storeTransferOrderItemDTO.setProductionDate(p.getProductionDate());
            // storeTransferOrderItemDTO.setBatchTime(p.getBatchTime());
            // storeTransferOrderItemDTO.setProductSpecificationId(p.getProductSpecificationId());
            // storeTransferOrderItemDTO.setSecOwnerId(p.getSecOwnerId());
            // // "isStockAgeStrategy": false,
            // storeTransferOrderItemDTO.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
            if (p.getStoreTotalCount() != null && p.getPackageQuantity() != null) {
                BigDecimal[] counts = p.getStoreTotalCount().divideAndRemainder(p.getPackageQuantity());
                storeTransferOrderItemDTO.setPackageCount(counts[0]);
                storeTransferOrderItemDTO.setUnitCount(counts[1]);
            }
            storeTransferOrderItemDTO.setSkuId(p.getProductSkuId());
            // 标记容器
            storeTransferOrderItemDTO.setVesselId(vesselMoveDTO.getVesselId());
            storeTransferOrderItemDTO.setVesselName(vesselMoveDTO.getVesselName());
            storeTransferOrderItemDTOS.add(storeTransferOrderItemDTO);
        });
        storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);

        // 新增移库单
        iStoreTransferOrderService.addStoreTransferOrder(storeTransferOrderDTO);
    }

    /**
     * 更新容器信息当前货位
     * 
     * @param updateDTOS
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateVesselInfoCurrentLocation(List<VesselInfoModifyDTO> updateDTOS) {
        LOGGER.info("VesselBL.updateVesselInfoCurrentLocation 入参：{}", JSON.toJSONString(updateDTOS));

        // 检查容器号是否存在
        Map<Long, VesselInfoModifyDTO> locationIdMap = updateDTOS.stream().filter(item -> item.getLocationId() != null)
            .collect(Collectors.toMap(VesselInfoModifyDTO::getLocationId, Function.identity(), (key1, key2) -> key2));
        List<Long> locationIds =
            updateDTOS.stream().map(VesselInfoModifyDTO::getLocationId).distinct().collect(Collectors.toList());
        VesselInfoQueryDTO queryDTO = new VesselInfoQueryDTO();
        queryDTO.setWarehouseId(updateDTOS.get(0).getWarehouseId());
        queryDTO.setVesselLocationIds(locationIds);
        List<VesselInfoPO> infoPOS = vesselInfoPOMapper.selectByConditions(queryDTO);
        if (CollectionUtils.isEmpty(infoPOS)) {
            String vesselName =
                updateDTOS.stream().map(VesselInfoModifyDTO::getLocationNo).distinct().collect(Collectors.joining(","));
            throw new BusinessValidateException("物料箱信息:" + vesselName + "不存在,请重新查询");
        }

        // 批量更新物料箱状态
        List<VesselInfoPO> updatePOList = new ArrayList<>();
        infoPOS.stream().forEach(p -> {
            VesselInfoModifyDTO modifyDTO = locationIdMap.get(p.getLocationId());
            VesselInfoPO updatePO = new VesselInfoPO();
            updatePO.setId(p.getId());
            updatePO.setCurrentLocationId(modifyDTO != null ? modifyDTO.getCurrentLocationId() : null);
            updatePO.setCurrentLocationName(modifyDTO != null ? modifyDTO.getCurrentLocationName() : "");
            updatePO.setLastUpdateUser(updateDTOS.get(0).getLastUpdateUser());
            updatePOList.add(updatePO);
        });
        LOGGER.info("VesselBL.updateVesselInfoCurrentLocation 更新数据：{}", JSON.toJSONString(updatePOList));
        vesselInfoPOMapper.updateBatch(updatePOList);

        // // 保存当前仓库最近修改货位操作时间
        // codeGenerator.updateLocationDeleteTime(updateDTO.getWarehouseId());
    }

    /**
     * 更新通道信息
     * 
     * @param updateDTOList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePassageItem(List<VesselPassageUpdateDTO> updateDTOList) {
        LOGGER.info("VesselBL.updatePassageItem 入参：{}", JSON.toJSONString(updateDTOList));
        AssertUtils.notEmpty(updateDTOList, "更新通道信息参数不能为空");
        updateDTOList.stream().forEach(updateDTO -> {
            AssertUtils.notNull(updateDTO, "更新通道信息参数不能为空");
            AssertUtils.notNull(updateDTO.getWarehouseId(), "更新通道信息仓库id不能为空");
            AssertUtils.notNull(updateDTO.getVesselId(), "更新通道信息容器id不能为空");
            AssertUtils.notNull(updateDTO.getVesselName(), "更新通道信息容器名称不能为空");
            AssertUtils.notNull(updateDTO.getLocationId(), "更新通道信息货位id不能为空");
            AssertUtils.notNull(updateDTO.getLocationNo(), "更新通道信息货位名称不能为空");
        });

        Integer warehouseId = updateDTOList.get(0).getWarehouseId();
        List<String> vesselIds = updateDTOList.stream().filter(p -> p.getVesselId() != null)
            .map(VesselPassageUpdateDTO::getVesselId).distinct().map(String::valueOf).collect(Collectors.toList());
        List<String> locationIds = updateDTOList.stream().filter(p -> p.getLocationId() != null)
            .map(VesselPassageUpdateDTO::getLocationId).distinct().map(String::valueOf).collect(Collectors.toList());

        // 查询容器是否存在通道配置
        PassageItemSO vesselPassageItemSO = new PassageItemSO();
        vesselPassageItemSO.setWarehouseId(warehouseId);
        vesselPassageItemSO.setPassageType(PassageRelateTypeEnum.货位.getType());
        vesselPassageItemSO.setRelateType(PassageTypeEnum.货位.getType());
        vesselPassageItemSO.setRelateIdList(vesselIds);
        List<PassagePO> vesselPassagePOS = passageMapper.findAll(vesselPassageItemSO);
        LOGGER.info("VesselBL.updatePassageItem 查询容器通道配置 结果：{}", JSON.toJSONString(vesselPassagePOS));
        if (!CollectionUtils.isEmpty(vesselPassagePOS)) {
            List<PassageItemPO> vesselPassageItemPOS = vesselPassagePOS.stream().map(PassagePO::getPassageItemPOS)
                .flatMap(list -> list.stream()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(vesselPassageItemPOS)) {
                // 获取容器通道明细ids
                List<Long> vesselPassageItemIds =
                    vesselPassageItemPOS.stream().map(PassageItemPO::getId).distinct().collect(Collectors.toList());
                // 删除通道明细
                passageItemMapper.deleteByIds(vesselPassageItemIds);
            }
        }

        // 查询所选货位的通道配置
        PassageItemSO passageItemSO = new PassageItemSO();
        passageItemSO.setWarehouseId(warehouseId);
        passageItemSO.setPassageType(PassageRelateTypeEnum.货位.getType());
        passageItemSO.setRelateType(PassageTypeEnum.货位.getType());
        passageItemSO.setRelateIdList(locationIds);
        List<PassagePO> passagePOS = passageMapper.findAll(passageItemSO);
        LOGGER.info("VesselBL.updatePassageItem 查询所选货位的通道配置 结果：{}", JSON.toJSONString(passagePOS));
        if (CollectionUtils.isEmpty(passagePOS)) {
            return;
        }

        // 新增容器通道明细配置
        List<PassageItemPO> vesselPassageItemPOList = new ArrayList<>();
        List<PassageItemPO> passageItemPOS = passagePOS.stream().map(PassagePO::getPassageItemPOS)
            .flatMap(list -> list.stream()).collect(Collectors.toList());
        Map<Long, VesselPassageUpdateDTO> locationIdMap = updateDTOList.stream().collect(
            Collectors.toMap(VesselPassageUpdateDTO::getLocationId, Function.identity(), (key1, key2) -> key2));
        passageItemPOS.stream().forEach(item -> {
            Long relateId = Long.valueOf(item.getRelateId());
            if (locationIdMap.containsKey(relateId)) {
                VesselPassageUpdateDTO updateDTO = locationIdMap.get(relateId);

                PassageItemPO vesselPassageItemPO = new PassageItemPO();
                BeanUtils.copyProperties(item, vesselPassageItemPO);
                vesselPassageItemPO.setId(UUIDGenerator.getUUID(PassageItemPO.class.getName()));
                vesselPassageItemPO.setRelateId(updateDTO.getVesselId().toString());
                vesselPassageItemPO.setRelateName(updateDTO.getVesselName());
                vesselPassageItemPOList.add(vesselPassageItemPO);
            }
        });

        if (!CollectionUtils.isEmpty(vesselPassageItemPOList)) {
            Lists.partition(vesselPassageItemPOList, 50).forEach(list -> {
                passageItemMapper.insertBatch(list);
            });
        }

        LOGGER.info("VesselBL.updatePassageItem 容器更新通道明细，vesselPassageItemPOList：{}",
            JSON.toJSONString(vesselPassageItemPOList));
    }

    /**
     * 容器删除并自动移库到关联货位
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void vesselDeleteAndTransfer(VesselDTO dto) {
        LOGGER.info("VesselBL.vesselDeleteAndTransfer 入参：{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "入参不能为空！");
        AssertUtils.notNull(dto.getId(), "容器信息id不能为空！");
        AssertUtils.notNull(dto.getUserId(), "入参不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空！");
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空！");
        // 容器库存移库至容器关联货位
        tranferToRelatedLocation(dto);
        // 删除容器
        deleteVesselLocation(dto);
        // 删除容器信息
        deleteVesselInfoByVesselId(dto);
        // 删除容器通道配置
        deleteVesselPassageItemByVesselId(dto);
    }

    /**
     * 容器库存移库至容器关联货位
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void tranferToRelatedLocation(VesselDTO dto) {
        // 检查物料箱是否存在
        LocationPO vesselLocationPO = locationPOMapper.findLocationById(dto.getId());
        if (vesselLocationPO == null
            || !Objects.equals(vesselLocationPO.getCategory(), CategoryEnum.CARGO_VESSEL.getValue().byteValue())) {
            throw new BusinessValidateException("所选物料箱不存在！");
        }

        if (vesselLocationPO.getArea_Id() == null) {
            return;
        }

        // 检查关联货位是否存在
        LocationPO relatedLocationPO = locationPOMapper.findLocationById(vesselLocationPO.getArea_Id());
        if (relatedLocationPO == null) {
            return;
        }

        LocationPO tranferLocationPO = new LocationPO();
        tranferLocationPO.setId(relatedLocationPO.getId());
        tranferLocationPO.setName(relatedLocationPO.getName());
        tranferLocationPO.setArea_Id(vesselLocationPO.getId());
        dto.setTransferToVessel(false);
        // 组装移库数据进行移库
        updateStoreTranferNoOrder(Collections.singletonList(tranferLocationPO), dto);
    }

    /**
     * 根据容器id删除容器信息
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteVesselInfoByVesselId(VesselDTO dto) {
        // 检查容器信息是否存在
        VesselInfoQueryDTO queryDTO = new VesselInfoQueryDTO();
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setVesselLocationId(dto.getId());
        List<VesselInfoPO> infoPOS = vesselInfoPOMapper.selectByConditions(queryDTO);
        LOGGER.info("VesselBL.deleteVesselInfoByVesselId 查询容器信息 结果：{}", JSON.toJSONString(infoPOS));
        if (CollectionUtils.isEmpty(infoPOS)) {
            return;
        }

        List<Long> vesselInfoIds = infoPOS.stream().map(VesselInfoPO::getId).distinct().collect(Collectors.toList());
        int count = vesselInfoPOMapper.deleteByPrimaryKeyBatch(vesselInfoIds);
        if (count < 1) {
            throw new BusinessValidateException("删除容器信息失败");
        }
    }

    /**
     * 根据容器id删除通道配置
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteVesselPassageItemByVesselId(VesselDTO dto) {
        // 容器是否存在通道配置
        PassageItemSO vesselPassageItemSO = new PassageItemSO();
        vesselPassageItemSO.setWarehouseId(dto.getWarehouseId());
        vesselPassageItemSO.setPassageType(PassageRelateTypeEnum.货位.getType());
        vesselPassageItemSO.setRelateType(PassageTypeEnum.货位.getType());
        vesselPassageItemSO.setRelateIdList(Arrays.asList(dto.getId().toString()));
        List<PassagePO> vesselPassagePOS = passageMapper.findAll(vesselPassageItemSO);
        LOGGER.info("VesselBL.deleteVesselPassageItemByVesselId 查询容器通道配置 结果：{}", JSON.toJSONString(vesselPassagePOS));
        if (CollectionUtils.isEmpty(vesselPassagePOS)) {
            return;
        }

        List<PassageItemPO> vesselPassageItemPOS = vesselPassagePOS.stream().map(PassagePO::getPassageItemPOS)
            .flatMap(list -> list.stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vesselPassageItemPOS)) {
            return;
        }

        // 获取容器通道明细ids
        List<Long> vesselPassageItemIds =
            vesselPassageItemPOS.stream().map(PassageItemPO::getId).distinct().collect(Collectors.toList());
        // 删除通道明细
        passageItemMapper.deleteByIds(vesselPassageItemIds);
    }

    /**
     * 容器删除
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteVesselLocation(VesselDTO dto) {
        List<Long> vesselLocationIds = Arrays.asList(dto.getId());
        Integer ifCanDelete = productLocationPOMapper.countHavingSku(vesselLocationIds);
        if (ifCanDelete > 0) {
            throw new BusinessValidateException("存在货位存放有产品，请去掉有产品的货位再删除");
        }

        Integer warehouseId = dto.getWarehouseId();
        boolean isOpenLocationStock =
            warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (isOpenLocationStock) {
            ifCanDelete = productStoreBatchPOMapper.countHavingSku(vesselLocationIds);
            if (ifCanDelete > 0) {
                throw new BusinessValidateException("存在货位存放有库存，请先做移库操作再删除");
            }
        }
        // 删除容器货位
        locationPOMapper.deleteVesselByPrimaryKeyBatch(vesselLocationIds);
        // // 删除关联产品货位
        // productLocationPOMapper.deleteByLocationIds(vesselLocationIds);
        LOGGER.info("[删除容器货位]{}, 操作人：{}", JSON.toJSONString(vesselLocationIds), JSON.toJSONString(dto.getUserId()));
        try {
            // 通知WCS
            notifyWCSBL.deleteLocation(warehouseId, vesselLocationIds);
        } catch (Exception e) {
            LOGGER.info("删除容器货位通知WCS异常：{}", JSON.toJSONString(vesselLocationIds));

        }
        // 保存当前仓库最近删除容器货位操作时间
        codeGenerator.updateLocationDeleteTime(warehouseId);
    }
}
