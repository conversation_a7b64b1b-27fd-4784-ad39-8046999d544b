package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.*;
import com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.service.*;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUserAuth;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.dto.WarehouseDTO;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseManageService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseSyncService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.WareHouseConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.EnableStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.WarehouseConstant;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 仓库费用有关BL
 * 
 * @Description
 * <AUTHOR>
 * @Date 2018/11/21 15:26
 */
@Service
public class WareHouseChargeBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(WareHouseChargeBL.class);
    // 启用
    private static int ENABLESTATE_USE = 1;

    @Autowired
    private WarehousechargeconfigPOMapper warehousechargeconfigPOMapper;
    @Autowired
    private WareHouseMapper wareHouseMapper;
    @Autowired
    private PassageItemMapper passageItemMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;
    @Autowired
    private LocationAreaPOMapper locationAreaMapper;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private PassageItemBL passageItemBL;

    @Reference(timeout = 30000)
    private IOrgService iOrgService;
    @Reference(timeout = 30000)
    private IAdminUserQueryService iAdminUserQueryService;
    @Reference(timeout = 30000)
    private IAllotRegulationService iAllotRegulationService;
    @Reference(timeout = 30000)
    private IWarehouseSyncService warehouseSyncService;

    @Reference
    private IPassageService iPassageService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IDefaultLocationConfigService defaultLocationConfigService;

    @Reference
    private IInStockStrategyService inStockStrategyService;
    @Reference
    private IOutStockStrategyService outStockStrategyService;
    @Reference
    private IWavesStrategyService wavesStrategyService;

    @Reference(timeout = 30000)
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference(timeout = 30000)
    private IWarehouseManageService iWarehouseManageService;

    /**
     * 查询仓库信息
     *
     * @param wareHouseChargeQueryDTO
     * @return
     */
    public PageList<WareHouseChargeDTO>
        listWarehouseChargeConfigSelective(WareHouseChargeQueryDTO wareHouseChargeQueryDTO) {
        long old = System.currentTimeMillis();
        long now = 0;
        LOGGER.info("查询仓库信息 开始 old={}", old);
        Assert.notNull(wareHouseChargeQueryDTO.getPageNum(), "分页参数为空");
        Assert.notNull(wareHouseChargeQueryDTO.getPageSize(), "分页参数为空");
        if (StringUtils.isEmpty(wareHouseChargeQueryDTO.getName())) {
            wareHouseChargeQueryDTO.setName(null);
        }
        if (StringUtils.isEmpty(wareHouseChargeQueryDTO.getCity())) {
            wareHouseChargeQueryDTO.setCity(null);
        }
        PageList<OrgDTO> orgDTOPageList = new PageList<>();
        List<Integer> queryCityIdList = new ArrayList<Integer>();
        // 查询公司及子公司全部仓库
        if (wareHouseChargeQueryDTO.getCityId() == null) {
            orgDTOPageList = iOrgService.listSelfAndSubById(wareHouseChargeQueryDTO.getParentCityId());
            now = System.currentTimeMillis();
            LOGGER.info("查询仓库信息，当前耗时：now={}", now);
            if (orgDTOPageList != null) {
                if (!CollectionUtils.isEmpty(orgDTOPageList.getDataList())) {
                    orgDTOPageList.getDataList().stream().forEach(orgDTO -> {
                        queryCityIdList.add(orgDTO.getId());
                    });
                }
            }
        }
        // 只查询子公司的仓库
        if (wareHouseChargeQueryDTO.getCityId() != null) {
            queryCityIdList.add(wareHouseChargeQueryDTO.getCityId());
            orgDTOPageList = iOrgService.listSelfAndSubById(wareHouseChargeQueryDTO.getCityId());
            now = System.currentTimeMillis();
            LOGGER.info("查询仓库信息，当前耗时：now={}", now);

            wareHouseChargeQueryDTO.setCityId(null);
        }
        wareHouseChargeQueryDTO.setCityIdList(queryCityIdList);
        // 传入的cityId不存在
        if (queryCityIdList.size() == 0) {
            throw new BusinessValidateException("传入的parentCityId或cityId对应的服务商不存在");
        }
        // 查询仓库
        // LOGGER.info("获得的id列表是:" + queryCityIdList);
        PageList<WareHouseChargeDTO> pageList =
            warehousechargeconfigPOMapper.listWarehouseChargeConfigSelective(wareHouseChargeQueryDTO).toPageList();
        now = System.currentTimeMillis();
        LOGGER.info("查询仓库信息，当前耗时：now={}", now);
        // 补充仓库是否有库存的信息
        List<WareHouseChargeDTO> dataList = pageList.getDataList();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<Integer> warehouseIdList = dataList.stream().map(wareHouseChargeDTO -> {
                return wareHouseChargeDTO.getWarehouseId();
            }).collect(Collectors.toList());
            List<WareHouseChargeDTO> stockCountList = warehousechargeconfigPOMapper.listStockCount(warehouseIdList);
            now = System.currentTimeMillis();
            LOGGER.info("查询仓库信息，当前耗时：now={}", now);
            if (!CollectionUtils.isEmpty(stockCountList)) {
                dataList.stream().forEach(wareHouseChargeDTO -> {
                    for (WareHouseChargeDTO wareHouseChargeDTO1 : stockCountList) {
                        if (wareHouseChargeDTO1.getWarehouseId().equals(wareHouseChargeDTO.getWarehouseId())) {
                            wareHouseChargeDTO.setStockNumber(wareHouseChargeDTO1.getStockNumber());
                            break;
                        }
                    }
                });
            }
        }
        // 补充公司名字和是否开启货位库存
        if (!CollectionUtils.isEmpty(dataList)) {
            List<OrgDTO> lasrOrgDTOList = orgDTOPageList != null ? orgDTOPageList.getDataList() : new ArrayList<>();
            dataList.stream().forEach(wareHouseChargeDTO -> {
                // 获取是否开启货位库存
                Boolean isOpenLocationStock =
                    warehouseConfigService.isOpenLocationStock(wareHouseChargeDTO.getWarehouseId());
                long n = System.currentTimeMillis();
                LOGGER.info("查询仓库信息，当前耗时：n={}", n);
                wareHouseChargeDTO.setIsOpenCargoStock(isOpenLocationStock ? 1 : 0);
                for (OrgDTO orgDTO : lasrOrgDTOList) {
                    if (orgDTO.getId().equals(wareHouseChargeDTO.getCityId())) {
                        wareHouseChargeDTO.setCompanyName(orgDTO.getOrgName());
                        break;
                    }
                }
            });
        }
        // 查询调拨规则
        if (!CollectionUtils.isEmpty(dataList)) {
            dataList.forEach(t -> {
                AllotRegulationQueryDTO dto = new AllotRegulationQueryDTO();
                dto.setFromCityId(t.getCityId());
                dto.setFromWarehouseId(t.getWarehouseId());
                dto.setPageSize(1000);
                PageList<AllotRegulationDTO> allotList = iAllotRegulationService.pageList(dto);
                long n = System.currentTimeMillis();
                LOGGER.info("查询仓库信息，当前耗时：n={}", n);
                // LOGGER.info("allotList:" + GSON.toJson(allotList));
                t.setAllotList(convertToAllot(allotList.getDataList()));
            });
        }
        return pageList;
    }

    /**
     * 转换
     *
     * @param allots
     * @return
     * @return: List<com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO>
     */
    private List<com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO>
        convertToAllot(List<AllotRegulationDTO> allots) {
        if (CollectionUtils.isEmpty(allots)) {
            return null;
        }
        List<com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO> result = new ArrayList<>();
        allots.forEach(t -> {
            com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO dto =
                new com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO();
            BeanUtils.copyProperties(t, dto);
            result.add(dto);
        });
        return result;
    }

    /**
     * 仓库启用停用方法
     *
     * @param warehouseId
     * @Param status 0停用、1启用
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void disabled(Integer warehouseId, Byte status) {
        Assert.notNull(warehouseId, "仓库id为null");
        boolean isNotValidateState = status == null
            || ((EnableStateEnum.DISABLE.ordinal() != status && EnableStateEnum.ENABLE.ordinal() != status));
        if (isNotValidateState) {
            throw new DataValidateException("状态参数不合法");
        }
        if (WarehouseConstant.WAREHOUSE_STATUS_DISABLED.equals(status)) {
            checkWarehouseIfCanDisabled(warehouseId);
        }
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new WareHouseChargeQueryDTO();
        wareHouseChargeQueryDTO.setWarehouseId(warehouseId);
        PageList<WareHouseChargeDTO> wareHouseChargeDTOList =
            warehousechargeconfigPOMapper.listWarehouseChargeConfigSelective(wareHouseChargeQueryDTO).toPageList();
        if (CollectionUtils.isEmpty(wareHouseChargeDTOList.getDataList())) {
            throw new BusinessException("仓库不存在，无法停用");
        }
        WareHousePO wareHousePO = new WareHousePO();
        wareHousePO.setId(warehouseId);
        wareHousePO.setState(status.intValue());
        wareHouseMapper.updateSelectiveByPrimary(wareHousePO);
        WarehousechargeconfigPO warehousechargeconfigPO = new WarehousechargeconfigPO();
        warehousechargeconfigPO.setWarehouse_id(warehouseId);
        warehousechargeconfigPO.setStatus(status);
        warehousechargeconfigPOMapper.updateByWarehouseSelective(warehousechargeconfigPO);

        // 调用接口发送消息
        Warehouse warehouse = new Warehouse();
        BeanUtils.copyProperties(wareHousePO, warehouse);
        warehouse.setEnableState(wareHousePO.getState());
        warehouseSyncService.warehouseChangeSync(warehouse);
    }

    /**
     * 添加仓库
     *
     * @param wareHouseChargeDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addWarehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        WareHousePO wareHousePO = convert(wareHouseChargeDTO);
        wareHousePO.setCreateUserId(wareHouseChargeDTO.getCreateUser());
        wareHousePO.setLastUpdateUserId(wareHouseChargeDTO.getCreateUser());
        wareHousePO.setCreateTime(new Date());
        wareHousePO.setLastUpdateTime(new Date());
        Integer warehouseId = getWarehouseId(wareHousePO.getCityId(), wareHousePO.getWarehouseType());
        wareHousePO.setId(warehouseId);
        wareHouseMapper.insertSelective(wareHousePO);
        addWarehouseCharge(wareHouseChargeDTO, warehouseId);
        if (!CollectionUtils.isEmpty(wareHouseChargeDTO.getAllotList())) {
            wareHouseChargeDTO.getAllotList().forEach(t -> {
                com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO allot =
                    new com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO();
                BeanUtils.copyProperties(t, allot);
                allot.setFromWarehouseId(warehouseId);
                iAllotRegulationService.addOne(allot);
            });
        }

        // 初始化仓库货位
        warehouseSyncService.initLocation(warehouseId, wareHousePO.getCityId());

        // 调用接口发送消息
        Warehouse warehouse = new Warehouse();
        BeanUtils.copyProperties(wareHousePO, warehouse);
        warehouse.setEnableState(wareHousePO.getState());
        warehouseSyncService.warehouseChangeSync(warehouse);
    }

    /**
     * 新增仓库费率配置
     *
     * @param wareHouseChargeDTO
     * @param wareHousePO
     * @return: void
     */
    private void addWarehouseCharge(WareHouseChargeDTO wareHouseChargeDTO, Integer warehouseId) {
        WarehousechargeconfigPO warehousechargeconfigPO = new WarehousechargeconfigPO();
        warehousechargeconfigPO.setWarehouse_id(warehouseId);
        warehousechargeconfigPO.setId(UUIDUtils.randonUUID());
        warehousechargeconfigPO.setCreateTime(new Date());
        warehousechargeconfigPO.setLastUpdateTime(warehousechargeconfigPO.getCreateTime());
        warehousechargeconfigPO.setCreateUser(Long.valueOf(wareHouseChargeDTO.getCreateUser()));
        warehousechargeconfigPO.setLastUpdateUser(Long.valueOf(wareHouseChargeDTO.getCreateUser()));
        warehousechargeconfigPO.setUnloadingCharge(wareHouseChargeDTO.getUnloadingCharge());
        warehousechargeconfigPO.setSortingCharge(wareHouseChargeDTO.getSortingCharge());
        warehousechargeconfigPO.setCustodianCharge(wareHouseChargeDTO.getCustodianCharge());
        warehousechargeconfigPO.setLoadingCharge(wareHouseChargeDTO.getLoadingCharge());
        warehousechargeconfigPO.setTransportCharge(wareHouseChargeDTO.getTransportCharge());
        warehousechargeconfigPO.setLandingCharge(wareHouseChargeDTO.getLandingCharge());
        warehousechargeconfigPO.setStatus(warehousechargeconfigPO.getStatus());
        warehousechargeconfigPOMapper.insertSelective(warehousechargeconfigPO);
    }

    /**
     * 修改仓库
     *
     * @param wareHouseChargeDTO
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateWarehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new WareHouseChargeQueryDTO();
        wareHouseChargeQueryDTO.setWarehouseId(wareHouseChargeDTO.getWarehouseId());
        WareHouseChargeDTO oldwareHouseChargeDTO =
            warehousechargeconfigPOMapper.getWarehouseChargeConfigSelective(wareHouseChargeQueryDTO);
        if (oldwareHouseChargeDTO == null) {
            throw new RuntimeException("仓库不存在，无法更新");
        }
        WareHousePO wareHousePO = convert(wareHouseChargeDTO);
        Integer warehouseId = oldwareHouseChargeDTO.getWarehouseId();
        wareHousePO.setId(warehouseId);
        wareHousePO.setLastUpdateTime(new Date());
        wareHousePO.setLastUpdateUserId(wareHouseChargeDTO.getLastUpdateUser());
        wareHouseMapper.updateSelectiveByPrimary(wareHousePO);
        // warehousecharge可能为空,为空的话就新增一条warehousecharge
        if (oldwareHouseChargeDTO.getId() == null) {
            addWarehouseCharge(wareHouseChargeDTO, warehouseId);
        } else {
            WarehousechargeconfigPO warehousechargeconfigPO = convertCharge(wareHouseChargeDTO);
            warehousechargeconfigPO.setLastUpdateTime(new Date());
            warehousechargeconfigPO.setWarehouse_id(warehouseId);
            warehousechargeconfigPOMapper.updateByWarehouseSelective(warehousechargeconfigPO);
        }
        com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO deleteParams =
            new com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO();
        deleteParams.setFromCityId(wareHouseChargeDTO.getCityId());
        deleteParams.setFromWarehouseId(wareHouseChargeDTO.getWarehouseId());
        iAllotRegulationService.deleteByParams(deleteParams);
        if (!CollectionUtils.isEmpty(wareHouseChargeDTO.getAllotList())) {
            wareHouseChargeDTO.getAllotList().forEach(t -> {
                com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO allot =
                    new com.yijiupi.himalaya.supplychain.dto.AllotRegulationDTO();
                BeanUtils.copyProperties(t, allot);
                iAllotRegulationService.addOne(allot);
            });
        }

        // 调用接口发送消息
        Warehouse warehouse = new Warehouse();
        BeanUtils.copyProperties(wareHousePO, warehouse);
        warehouse.setEnableState(wareHousePO.getState());
        warehouseSyncService.warehouseChangeSync(warehouse);
    }

    /**
     * 查询仓库信息
     *
     * @param warehouseId 仓库主键
     * @return
     */
    public WareHouseChargeDTO getWarehouseChargeConfigSelective(Integer warehouseId) {
        // LOGGER.info("查询单个仓库的方法被调用了:" + warehouseId);
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new WareHouseChargeQueryDTO();
        wareHouseChargeQueryDTO.setWarehouseId(warehouseId);
        WareHouseChargeDTO wareHouseChargeDTO =
            warehousechargeconfigPOMapper.getWarehouseChargeConfigSelective(wareHouseChargeQueryDTO);
        if (wareHouseChargeDTO != null) {
            // 获取是否开启货位库存
            Boolean isOpenLocationStock = warehouseConfigService.isOpenLocationStock(warehouseId);
            wareHouseChargeDTO.setIsOpenCargoStock((null != isOpenLocationStock && isOpenLocationStock) ? 1 : 0);
        }
        return wareHouseChargeDTO;
    }

    private WareHousePO convert(WareHouseChargeDTO wareHouseChargeDTO) {
        WareHousePO wareHousePO = new WareHousePO();
        wareHousePO.setState(wareHouseChargeDTO.getStatus() == null ? null : wareHouseChargeDTO.getStatus().intValue());
        wareHousePO.setCity(wareHouseChargeDTO.getCity());
        wareHousePO.setCityId(wareHouseChargeDTO.getCityId());
        wareHousePO.setCounty(wareHouseChargeDTO.getCounty());
        wareHousePO.setDetailAddress(wareHouseChargeDTO.getProvince() + wareHouseChargeDTO.getCity()
            + wareHouseChargeDTO.getCounty() + wareHouseChargeDTO.getStreet());
        wareHousePO.setLatitude(wareHouseChargeDTO.getLatitude());
        wareHousePO.setLongitude(wareHouseChargeDTO.getLongitude());
        wareHousePO.setName(wareHouseChargeDTO.getName());
        wareHousePO.setProvince(wareHouseChargeDTO.getProvince());
        wareHousePO.setRemark(wareHouseChargeDTO.getRemark());
        wareHousePO.setShopId(wareHouseChargeDTO.getShopId());
        wareHousePO.setStreet(wareHouseChargeDTO.getStreet());
        wareHousePO.setWarehouseClass(wareHouseChargeDTO.getWarehouseClass());
        wareHousePO.setWarehouseType(wareHouseChargeDTO.getWarehouseType());
        wareHousePO.setAcreage(wareHouseChargeDTO.getAcreage());
        wareHousePO.setRent(wareHouseChargeDTO.getRent());
        wareHousePO.setRentUnit(wareHouseChargeDTO.getRentUnit());
        return wareHousePO;
    }

    private WarehousechargeconfigPO convertCharge(WareHouseChargeDTO wareHouseChargeDTO) {
        WarehousechargeconfigPO warehousechargeconfigPO = new WarehousechargeconfigPO();
        warehousechargeconfigPO.setStatus(wareHouseChargeDTO.getStatus());
        warehousechargeconfigPO.setCustodianCharge(wareHouseChargeDTO.getCustodianCharge());
        warehousechargeconfigPO.setLandingCharge(wareHouseChargeDTO.getLandingCharge());
        warehousechargeconfigPO.setLoadingCharge(wareHouseChargeDTO.getLoadingCharge());
        warehousechargeconfigPO.setSortingCharge(wareHouseChargeDTO.getSortingCharge());
        warehousechargeconfigPO.setTransportCharge(wareHouseChargeDTO.getTransportCharge());
        warehousechargeconfigPO.setUnloadingCharge(wareHouseChargeDTO.getUnloadingCharge());
        return warehousechargeconfigPO;
    }

    /**
     * 仅查询本级服务商仓库信息
     *
     * @param cityIdList
     * @return
     */
    public List<WareHouseDTO> listWarehouseDTOByCityIdList(List<Integer> cityIdList) {
        if (CollectionUtils.isEmpty(cityIdList)) {
            throw new DataValidateException("入参不能为空");
        }
        return wareHouseMapper.listWarehouseDTOByCityIdList(cityIdList);
    }

    /**
     * 仅查询本级服务商仓库信息
     *
     * @param cityId
     * @return
     */
    public List<WareHouseDTO> listWarehouseDTOByCityId(Integer cityId) {
        Assert.notNull(cityId, "入参不能为空");
        return wareHouseMapper.listWarehouseDTOByCityId(cityId);
    }

    /**
     * 检测仓库是否可被停用 只有服务商的所有仓库、所有子服务商及子服务商的仓库、所有账号、所有子服务商的账号都被停用了，服务商自己才能被停用
     *
     * @param warehouseId
     * @return true表示可被停用、false不能停用
     */
    private void checkWarehouseIfCanDisabled(Integer warehouseId) {
        List<Integer> warehouseIdList = new ArrayList<>();
        warehouseIdList.add(warehouseId);

        List<AdminUserAuth> adminUserAuthList = iAdminUserQueryService.listByOrgIdList(warehouseIdList);
        if (!CollectionUtils.isEmpty(adminUserAuthList)) {
            adminUserAuthList.stream().forEach(adminUserAuth -> {
                // tms EnableState 枚举访问权限发生变化暂用‘魔术’值替换
                if (Objects.equals(ENABLESTATE_USE, adminUserAuth.getState().intValue())) {
                    throw new BusinessValidateException("该仓库存在未停用的角色");
                }
            });
        }
    }

    /**
     * 通过城市ID生成仓库id
     */
    private Integer getWarehouseId(Integer cityId, Integer warehouseType) {
        LOGGER.info("新增仓库生成仓库id参数,cityId:{},warehouseType:{}", cityId, warehouseType);
        String cityIdString;
        if (warehouseType == 4) {
            Integer count = wareHouseMapper.getCountByWarehouseType(warehouseType);
            cityIdString = Integer.toString(9999) + Integer.toString(count + 1) + 9999;
        } else {
            Integer count = wareHouseMapper.getCountByCityId(cityId);
            cityIdString = Integer.toString(cityId) + Integer.toString(count + 1);
        }
        return Integer.parseInt(cityIdString);
    }

    /**
     * 查复制仓库信息
     *
     * @param dto 仓库
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void copyWarehouse(WarehouseCopyDTO dto) {
        Integer oldWarehouseId = dto.getWarehouseId();
        String user = dto.getUserName();
        // 复制仓库信息
        Integer newWarehouseId = copyWarehouseMain(oldWarehouseId, dto);
        LOGGER.info("新仓库生成仓库id参数,newWarehouseId:{}", newWarehouseId);
        if (newWarehouseId == null) {
            return;
        }
        // 复制库区
        List<LocationAreaPO> locationAreaPOS = copyLocationArea(oldWarehouseId, newWarehouseId, dto.getUserId());
        if (!CollectionUtils.isEmpty(locationAreaPOS)) {
            // 复制库位
            copyLocation(oldWarehouseId, newWarehouseId, dto.getUserId(), locationAreaPOS, dto.getCityId());
        }
        // 复制仓库作业方式配置
        copyWarehouseConfig(oldWarehouseId, newWarehouseId, user);
        // 仓库收货位配置
        copyWarehouseLocationConfig(oldWarehouseId, newWarehouseId, user, dto.getName());
        // 复制仓库拣货通道
        copyWarehousePassage(oldWarehouseId, newWarehouseId, user);
        // 复制仓库上架策略
        copyWarehouseStockStrategy(oldWarehouseId, newWarehouseId, user, dto.getCityId());
        // 复制仓库分配策略
        copyWarehouseOutStockStrategy(oldWarehouseId, newWarehouseId, user, dto.getCityId());
        // 复制仓库波次策略
        copyWarehouseWavesStrategy(oldWarehouseId, newWarehouseId, user);
    }

    /**
     * 复制仓库信息
     *
     * @param oldWarehouseId
     * @param dto
     * @return
     */
    private Integer copyWarehouseMain(Integer oldWarehouseId, WarehouseCopyDTO dto) {
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new WareHouseChargeQueryDTO();
        wareHouseChargeQueryDTO.setWarehouseId(oldWarehouseId);
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(oldWarehouseId);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在，请重新选择");
        }
        WarehouseDTO warehouseDTO = WareHouseConvertor.warehouse2DTO(warehouse);
        warehouseDTO.setId(null);
        warehouseDTO.setName(dto.getName());
        warehouseDTO.setCity(dto.getCity());
        warehouseDTO.setProvince(dto.getProvince());
        warehouseDTO.setCityId(dto.getCityId());
        warehouseDTO.setCounty(dto.getCounty());
        warehouseDTO.setStreet(dto.getStreet());
        warehouseDTO.setState(dto.getStatus());
        warehouseDTO.setCreateUserId(dto.getUserId());
        warehouseDTO.setLastUpdateUserId(dto.getUserId());
        Warehouse newWarehouse = iWarehouseManageService.addWarehouse(warehouseDTO);
        LOGGER.info("仓库新生成:{}", JSON.toJSONString(newWarehouse));
        return newWarehouse.getId();
    }

    /**
     * 复制仓库作业方式配置
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     * @param user
     */
    private void copyWarehouseConfig(Integer oldWarehouseId, Integer newWarehouseId, String user) {
        LOGGER.info("复制仓库作业方式配置参数,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        // 根据仓库id查询仓库作业方式配置
        WarehouseConfigDTO config = warehouseConfigService.getConfigByWareHouseId(oldWarehouseId);
        if (config == null || config.getWarehouse_Id() == null) {
            LOGGER.info("仓库作业方式不存在,oldWarehouseId:{}", oldWarehouseId);
            return;
        }
        config.setWarehouse_Id(newWarehouseId);
        config.setCreateUser(user);
        config.setLastUpdateUser(user);
        LOGGER.info("仓库新生成收货位:{}", JSON.toJSONString(config));
        warehouseConfigService.addWarehouseConfig(config);
    }

    /**
     * 复制仓库收货位配置
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     */
    private void copyWarehouseLocationConfig(Integer oldWarehouseId, Integer newWarehouseId, String user,
        String warehouseName) {
        LOGGER.info("复制仓库收货位配置,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        // 复制仓库收货位配置
        List<DefaultLocationConfigDTO> defaultLocationConfigs =
            defaultLocationConfigService.findDefaultLocationConfigByWarehouseId(oldWarehouseId);
        if (CollectionUtils.isEmpty(defaultLocationConfigs)) {
            LOGGER.info("仓库收货位配置不存在,oldWarehouseId:{}", oldWarehouseId);
        }
        // 获取对应库位
        Map<String, Long> locationMap = new HashMap<>(16);
        List<LoactionDTO> loactionDTOS = getLoactions(newWarehouseId);
        if (!CollectionUtils.isEmpty(loactionDTOS)) {
            List<String> nameList = new ArrayList<>();
            defaultLocationConfigs.forEach(it -> nameList.add(it.getTemporaryLocationName()));
            loactionDTOS.forEach(it -> {
                if (nameList.contains(it.getName())) {
                    locationMap.put(it.getName(), it.getId());
                }
            });
        }

        List<LocationAreaPO> locationAreas = getLocationAreas(newWarehouseId);
        Map<String, List<LocationAreaPO>> locationAreaMap =
            locationAreas.stream().collect(Collectors.groupingBy(LocationAreaPO::getName));

        LOGGER.info("已分配仓库库位:{}", JSON.toJSONString(locationMap));
        for (DefaultLocationConfigDTO defaultLocationConfig : defaultLocationConfigs) {
            defaultLocationConfig.setId(UUIDUtils.randonIntegerUUID());
            defaultLocationConfig.setWarehouseId(newWarehouseId);
            defaultLocationConfig.setWarehouseName(warehouseName);
            defaultLocationConfig.setCreateUser(user);
            // 货位
            if (defaultLocationConfig.getCategory() == 0) {
                Long locationId = locationMap.get(defaultLocationConfig.getTemporaryLocationName());
                if (locationId != null) {
                    defaultLocationConfig.setTemporaryLocationId(locationId);
                }
            } else {
                // 货区
                List<LocationAreaPO> locationAreaPOS =
                    locationAreaMap.get(defaultLocationConfig.getTemporaryLocationName());
                if (!CollectionUtils.isEmpty(locationAreaPOS)) {
                    defaultLocationConfig.setTemporaryLocationId(locationAreaPOS.get(0).getId());
                }
            }

            LOGGER.info("仓库新生成收货位:{}", JSON.toJSONString(defaultLocationConfig));
            defaultLocationConfigService.insertDefaultLocationConfig(defaultLocationConfig);
        }
    }

    /**
     * 复制仓库拣货通道
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     */
    private void copyWarehousePassage(Integer oldWarehouseId, Integer newWarehouseId, String user) {
        LOGGER.info("复制仓库拣货通道,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        PassageSO so = new PassageSO();
        so.setWarehouseId(oldWarehouseId);
        so.setPageNum(1);
        so.setPageSize(Integer.MAX_VALUE);
        PageList<PassageDTO> passagePageList = iPassageService.listPassage(so);
        if (passagePageList == null || CollectionUtils.isEmpty(passagePageList.getDataList())) {
            LOGGER.info("仓库拣货通道不存在,oldWarehouseId:{}", oldWarehouseId);
            return;
        }
        List<PassageDTO> passageList = passagePageList.getDataList();
        // 获取对应库位
        List<LoactionDTO> loactionDTOS = getLoactions(newWarehouseId);
        Map<String, List<LoactionDTO>> listMap =
            loactionDTOS.stream().collect(Collectors.groupingBy(LoactionDTO::getName));

        for (PassageDTO passageDTO : passageList) {
            List<PassageItemPO> passageItemPOList = passageItemBL.listPassageItemByPassageId(passageDTO.getId());
            List<PassageItemDTO> passageItemDTOList = passageItemPOList.stream().map(po -> {
                PassageItemDTO dto = new PassageItemDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());
            passageItemDTOList.forEach(item -> {
                if (!CollectionUtils.isEmpty(listMap)) {
                    List<LoactionDTO> loactionList = listMap.get(item.getRelateName());
                    if (!CollectionUtils.isEmpty(loactionList)) {
                        item.setRelateId(loactionList.get(0).getId().toString());
                    }
                }

            });

            passageDTO.setItemList(passageItemDTOList);
            passageDTO.setWarehouseId(newWarehouseId);
            passageDTO.setOperateUser(user);
            passageDTO.setId(UUIDUtils.randonUUID());
            LOGGER.info("仓库新生成拣货通道:{}", JSON.toJSONString(passageDTO));
            iPassageService.savePassage(passageDTO);
        }
    }

    /**
     * 复制仓库上架策略
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     */
    private void copyWarehouseStockStrategy(Integer oldWarehouseId, Integer newWarehouseId, String user,
        Integer cityId) {
        LOGGER.info("复制仓库上架策略,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        InStockStrategyDTO dto = new InStockStrategyDTO();
        dto.setWarehouseId(oldWarehouseId);
        Integer parentOrg = iOrgService.findParentOrgBy(cityId);
        dto.setParentOrgId(parentOrg);
        PagerCondition pagerCondition = new PagerCondition();
        pagerCondition.setPageSize(Integer.MAX_VALUE);
        pagerCondition.setCurrentPage(1);
        PageList<InStockStrategyDTO> inStockStrategyDTOPageList =
            inStockStrategyService.listInStockStrategy(dto, pagerCondition);
        if (inStockStrategyDTOPageList == null || CollectionUtils.isEmpty(inStockStrategyDTOPageList.getDataList())) {
            LOGGER.info("仓库上架策略不存在,parentOrg:{},oldWarehouseId", parentOrg, oldWarehouseId);
            return;
        }
        List<Integer> strategyIds = new ArrayList<>();
        inStockStrategyDTOPageList.getDataList().forEach(it -> strategyIds.add(it.getId()));

        List<LoactionDTO> loactions = getLoactions(newWarehouseId);
        List<LocationAreaPO> locationAreas = getLocationAreas(newWarehouseId);
        Map<String, List<LoactionDTO>> loactionMap =
            loactions.stream().collect(Collectors.groupingBy(LoactionDTO::getName));
        Map<String, List<LocationAreaPO>> locationAreaMap =
            locationAreas.stream().collect(Collectors.groupingBy(LocationAreaPO::getName));
        for (Integer strategyId : strategyIds) {
            InStockStrategyDTO it = inStockStrategyService.findInStockStrategyById(strategyId);
            it.setWarehouseId(newWarehouseId);
            it.setId(UUIDUtils.randonIntegerUUID());
            it.setCreateUser(user);
            it.setLastUpdateUser(user);
            if (!CollectionUtils.isEmpty(it.getInStockStrategyConfigDTOS())) {
                List<InStockStrategyConfigDTO> configDTOs = new ArrayList<>();
                it.getInStockStrategyConfigDTOS().forEach(item -> {
                    InStockStrategyConfigDTO configDTO = new InStockStrategyConfigDTO();
                    configDTO.setConfigId(item.getConfigId());
                    configDTO.setConfigName(item.getConfigName());
                    configDTO.setConfigType(item.getConfigType());
                    configDTOs.add(configDTO);
                });
                it.setInStockStrategyConfigDTOS(configDTOs);
            }
            resetWarehouseStockStrategyRule(it, loactionMap, locationAreaMap, user);
            // if (!CollectionUtils.isEmpty(it.getStrategyRuleList())) {
            // it.getStrategyRuleList().forEach(rule -> {
            // rule.setId(UUIDUtils.randonIntegerUUID());
            // rule.setCreateUser(user);
            // rule.setLastUpdateUser(user);
            // rule.setInStockStrategyId(it.getId());
            // });
            // }
            LOGGER.info("仓库新生成上架策略:{}", JSON.toJSONString(it));
            inStockStrategyService.insertInStockStrategy(it);
        }
    }

    private void resetWarehouseStockStrategyRule(InStockStrategyDTO it, Map<String, List<LoactionDTO>> loactionMap,
        Map<String, List<LocationAreaPO>> locationAreaMap, String user) {
        if (!CollectionUtils.isEmpty(it.getStrategyRuleList())) {
            it.getStrategyRuleList().forEach(rule -> {
                rule.setId(UUIDUtils.randonIntegerUUID());
                rule.setCreateUser(user);
                rule.setLastUpdateUser(user);
                rule.setInStockStrategyId(it.getId());
                if (!StringUtils.isEmpty(rule.getSourceAreaName())) {
                    List<LocationAreaPO> areaPOS = locationAreaMap.get(rule.getSourceAreaName());
                    if (!CollectionUtils.isEmpty(areaPOS)) {
                        rule.setSourceArea(areaPOS.get(0).getId());
                    }
                }
                if (!StringUtils.isEmpty(rule.getTargetAreaName())) {
                    List<LocationAreaPO> areaPOS = locationAreaMap.get(rule.getTargetAreaName());
                    if (!CollectionUtils.isEmpty(areaPOS)) {
                        rule.setTargetArea(areaPOS.get(0).getId());
                    }
                }
                if (!StringUtils.isEmpty(rule.getSourceRepositoryName())) {
                    List<LoactionDTO> loactionDTOS = loactionMap.get(rule.getSourceRepositoryName());
                    if (!CollectionUtils.isEmpty(loactionDTOS)) {
                        rule.setSourceRepository(loactionDTOS.get(0).getId());
                    }
                }
                if (!StringUtils.isEmpty(rule.getTargetRepositoryName())) {
                    List<LoactionDTO> loactionDTOS = loactionMap.get(rule.getTargetRepositoryName());
                    if (!CollectionUtils.isEmpty(loactionDTOS)) {
                        rule.setTargetRepository(loactionDTOS.get(0).getId());
                    }
                }
            });
        }

    }

    /**
     * 复制仓库分配策略
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     */
    private void copyWarehouseOutStockStrategy(Integer oldWarehouseId, Integer newWarehouseId, String user,
        Integer cityId) {
        LOGGER.info("复制仓库分配策略,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        OutStockStrategyDTO outStockStrategyDTO = new OutStockStrategyDTO();
        Integer parentOrg = iOrgService.findParentOrgBy(cityId);
        outStockStrategyDTO.setParentOrgId(parentOrg);
        outStockStrategyDTO.setWarehouseId(oldWarehouseId);
        PagerCondition pagerCondition = new PagerCondition();
        pagerCondition.setPageSize(Integer.MAX_VALUE);
        pagerCondition.setCurrentPage(1);
        PageList<OutStockStrategyDTO> outStockStrategyPageList =
            outStockStrategyService.listOutStockStrategy(outStockStrategyDTO, pagerCondition);
        if (outStockStrategyPageList == null || CollectionUtils.isEmpty(outStockStrategyPageList.getDataList())) {
            LOGGER.info("仓库分配策略不存在,parentOrg:{},oldWarehouseId", parentOrg, oldWarehouseId);
            return;
        }
        List<OutStockStrategyDTO> outStockStrategyDTOS = outStockStrategyPageList.getDataList();
        for (OutStockStrategyDTO stockStrategyDTO : outStockStrategyDTOS) {
            OutStockStrategyDTO outStockStrate =
                outStockStrategyService.findOutStockStrategyById(stockStrategyDTO.getId());
            outStockStrate.setWarehouseId(newWarehouseId);
            outStockStrate.setId(UUIDUtils.randonIntegerUUID());
            outStockStrate.setCreateUser(user);
            outStockStrate.setLastUpdateUser(user);
            if (!CollectionUtils.isEmpty(outStockStrate.getStrategyRuleList())) {
                outStockStrate.getStrategyRuleList().forEach(rule -> {
                    rule.setId(UUIDUtils.randonIntegerUUID());
                    rule.setOutStockStrategyId(outStockStrategyDTO.getId());
                    rule.setCreateUser(user);
                    rule.setLastUpdateUser(user);
                });
            }
            LOGGER.info("仓库新生成上架策略:{}", JSON.toJSONString(outStockStrate));
            outStockStrategyService.insertOutStockStrategy(outStockStrate);
        }
    }

    /**
     * 复制仓库波次策略
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     */
    private void copyWarehouseWavesStrategy(Integer oldWarehouseId, Integer newWarehouseId, String user) {
        LOGGER.info("复制仓库波次策略,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, user);
        WavesStrategyDTO dto = new WavesStrategyDTO();
        dto.setWarehouseId(oldWarehouseId);
        PagerCondition pagerCondition = new PagerCondition();
        pagerCondition.setPageSize(Integer.MAX_VALUE);
        pagerCondition.setCurrentPage(1);
        PageList<WavesStrategyDTO> wavesStrategyPageList = wavesStrategyService.listWavesStrategy(dto, pagerCondition);
        if (wavesStrategyPageList == null || CollectionUtils.isEmpty(wavesStrategyPageList.getDataList())) {
            LOGGER.info("仓库波次策略不存在,oldWarehouseId:{}", oldWarehouseId);
            return;
        }
        List<WavesStrategyDTO> wavesStrategyDTOS = wavesStrategyPageList.getDataList();
        for (WavesStrategyDTO wavesStrategyDTO : wavesStrategyDTOS) {
            wavesStrategyDTO.setWarehouseId(newWarehouseId);
            wavesStrategyDTO.setId(UUIDUtils.randonIntegerUUID());
            wavesStrategyDTO.setCreateUser(user);
            wavesStrategyDTO.setLastUpdateUser(user);
            wavesStrategyService.insertWavesStrategy(wavesStrategyDTO);
        }
    }

    /**
     * 复制库区
     * 
     * @param oldWarehouseId
     * @param newWarehouseId
     * @param userId
     */
    private List<LocationAreaPO> copyLocationArea(Integer oldWarehouseId, Integer newWarehouseId, Integer userId) {
        LOGGER.info("复制库区,oldWarehouseId:{},newWarehouseId:{},user:{}", oldWarehouseId, newWarehouseId, userId);
        LocationAreaListDTO locationAreaDTO = new LocationAreaListDTO();
        locationAreaDTO.setWarehouseId(oldWarehouseId);
        List<LocationAreaPO> locationAreaPOList = locationAreaMapper.listLocationAreaNoPage(locationAreaDTO);
        for (LocationAreaPO locationAreaPO : locationAreaPOList) {
            locationAreaPO.setWarehouse_Id(newWarehouseId);
            locationAreaPO.setName(locationAreaPO.getName());
            locationAreaPO.setCreateUserId(userId);
            locationAreaPO.setLastUpdateUserId(userId);
            locationAreaPO.setLastUpdateTime(new Date());
            locationAreaPO.setCreateTime(new Date());
            locationAreaPO.setId(UUIDGenerator.getUUID(LocationAreaPO.class.getName()));
            locationAreaMapper.addLocationArea(locationAreaPO);
        }
        return locationAreaPOList;
    }

    /**
     * 复制库位
     *
     * @param oldWarehouseId
     * @param newWarehouseId
     * @param userId
     */
    private void copyLocation(Integer oldWarehouseId, Integer newWarehouseId, Integer userId,
        List<LocationAreaPO> locationAreaPOS, Integer cityId) {
        LOGGER.info("复制库位,oldWarehouseId:{},newWarehouseId:{},user:{},locationAreaPOS:{}", oldWarehouseId,
            newWarehouseId, userId, locationAreaPOS);
        List<LoactionDTO> loactionDTOS = getLoactions(oldWarehouseId);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            LOGGER.info("仓库库位不存在,oldWarehouseId:{}", oldWarehouseId);
            return;
        }
        Map<String, List<LocationAreaPO>> areaMap =
            locationAreaPOS.stream().collect(Collectors.groupingBy(LocationAreaPO::getName));
        for (LoactionDTO loactionDTO : loactionDTOS) {
            List<LocationAreaPO> areaPOS = areaMap.get(loactionDTO.getArea());
            if (!CollectionUtils.isEmpty(areaPOS)) {
                loactionDTO.setArea_Id(areaPOS.get(0).getId());
                loactionDTO.setWarehouseId(newWarehouseId);
                loactionDTO.setUserId(userId);
                loactionDTO.setName(loactionDTO.getName());
            }
        }
        if (!CollectionUtils.isEmpty(loactionDTOS)) {
            productLocationBL.batchAdd(loactionDTOS, newWarehouseId, cityId, userId);
        }
    }

    /**
     * 获取仓库库位
     * 
     * @param warehouseId
     * @return
     */
    private List<LoactionDTO> getLoactions(Integer warehouseId) {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(warehouseId);
        dto.setPageSize(Integer.MAX_VALUE);
        dto.setCurrentPage(1);
        PageResult<LoactionDTO> modelList = locationPOMapper.findProductLocationPageListNew(dto);
        PageList<LoactionDTO> locationPageList = modelList.toPageList();
        List<LoactionDTO> loactionDTOS = locationPageList.getDataList();
        return loactionDTOS;
    }

    private List<LocationAreaPO> getLocationAreas(Integer warehouseId) {
        LocationAreaListDTO locationAreaDTO = new LocationAreaListDTO();
        locationAreaDTO.setWarehouseId(warehouseId);
        List<LocationAreaPO> locationAreaPOList = locationAreaMapper.listLocationAreaNoPage(locationAreaDTO);
        return locationAreaPOList;
    }

    /**
     * 修改仓库
     *
     * @param wareHouseChargeDTO
     */
    public void updateChargeWarehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new WareHouseChargeQueryDTO();
        wareHouseChargeQueryDTO.setWarehouseId(wareHouseChargeDTO.getWarehouseId());
        WareHouseChargeDTO oldwareHouseChargeDTO =
            warehousechargeconfigPOMapper.getWarehouseChargeConfigSelective(wareHouseChargeQueryDTO);
        if (oldwareHouseChargeDTO == null) {
            throw new BusinessException("仓库不存在，无法更新");
        }

        Integer warehouseId = oldwareHouseChargeDTO.getWarehouseId();
        // warehousecharge可能为空,为空的话就新增一条warehousecharge
        if (oldwareHouseChargeDTO.getId() == null) {
            addWarehouseCharge(wareHouseChargeDTO, warehouseId);
        } else {
            WarehousechargeconfigPO warehousechargeconfigPO = convertCharge(wareHouseChargeDTO);
            warehousechargeconfigPO.setLastUpdateTime(new Date());
            warehousechargeconfigPO.setWarehouse_id(warehouseId);
            warehousechargeconfigPO.setId(oldwareHouseChargeDTO.getId());
            warehousechargeconfigPOMapper.updateByPrimaryKeySelective(warehousechargeconfigPO);
        }
    }
}
