package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.EntranceConfigBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.LocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.OccupyElementBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.GridPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPointInfoPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.GridPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationParamDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointAndPickPointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PointInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseRoutePointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseStarirsDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.StrairsPointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DModelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.Warehouse2DModelType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/21 18:21
 */


@Component
public class Warehouse2DModelQueryBL {

    private static Cache<Integer, Map<String, PointInfoDTO>> LocationPointCache =
            Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(10, TimeUnit.MINUTES).build();

    @Resource
    private GridPOMapper gridPOMapper;

    @Resource
    private LocationPointInfoPOMapper locationPointInfoPOMapper;

    @Resource
    private LocationConfigPOMapper locationConfigPOMapper;

    public List<WarehouseRoutePointDTO> getWarehouseRoutePoints(Integer warehouesId) {
        List<GridPO> gridPOS = gridPOMapper.selectByWarehouseId(warehouesId);
        if (CollectionUtils.isEmpty(gridPOS)) {
            return Collections.emptyList();
        }
        return gridPOS.stream().map(this::convert).collect(Collectors.toList());
    }

    public List<LocationPointInfoDTO> findWarehouseLocatinPoints(Integer warehouesId) {
        List<LocationPointInfoPO> locationPointInfoPOS = locationPointInfoPOMapper.selectByWarehouseId(warehouesId);
        if (CollectionUtils.isEmpty(locationPointInfoPOS)) {
            return Collections.emptyList();
        }
        return locationPointInfoPOS.stream().map(this::convert).collect(Collectors.toList());
    }


    public LocationPointInfoDTO findFirstFloor(Integer warehouesId) {
        List<Integer> starPoint = findStarPoint(warehouesId, 1);
        if(CollectionUtils.isEmpty(starPoint)) {
            return null;
        }
        LocationPointInfoDTO locationPointInfoDTO = new LocationPointInfoDTO();
        locationPointInfoDTO.setWarehouseId(warehouesId);
        locationPointInfoDTO.setX(starPoint.get(0));
        locationPointInfoDTO.setY(starPoint.get(1));
        locationPointInfoDTO.setFloor(1);
        return locationPointInfoDTO;
    }

    public List<LocationPointInfoDTO> findAllStarPoint(Integer warehouesId) {
        Map<Integer, List<Integer>> starPointMap = findStarPointMap(warehouesId);
        if (MapUtils.isEmpty(starPointMap)) {
            return Collections.emptyList();
        }
        List<LocationPointInfoDTO> pointInfos = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> floorStarPoint : starPointMap.entrySet()) {
            Integer floor = floorStarPoint.getKey();
            List<Integer> starPoint = floorStarPoint.getValue();
            LocationPointInfoDTO locationPointInfoDTO = new LocationPointInfoDTO();
            locationPointInfoDTO.setWarehouseId(warehouesId);
            locationPointInfoDTO.setX(starPoint.get(0));
            locationPointInfoDTO.setY(starPoint.get(1));
            locationPointInfoDTO.setFloor(floor);
            pointInfos.add(locationPointInfoDTO);
        }
        return pointInfos;
    }



    public List<Integer> findStarPoint(Integer warehouesId, Integer floor) {
        Map<Integer, List<Integer>> starPointMap = findStarPointMap(warehouesId);
        if (MapUtils.isEmpty(starPointMap)) {
            return null;
        }
        return starPointMap.get(floor);
    }


    public Map<Integer, List<Integer>> findStarPointMap(Integer warehouesId) {
        List<LocationConfigPO> locationConfigs = locationConfigPOMapper.selectByWarehouseId(warehouesId);
        if (CollectionUtils.isEmpty(locationConfigs)) {
            return new HashMap<>();
        }
        LocationConfigPO locationConfigPO = locationConfigs.get(0);
        Warehouse2DModelDTO warehouse2DModel = JSON.parseObject(locationConfigPO.getLocationConfig(), Warehouse2DModelDTO.class);
        if (CollectionUtils.isEmpty(warehouse2DModel.getElements())) {
            return new HashMap<>();
        }

        Map<Integer, List<Integer>> floorStarPointMap = new HashMap<>();
        for (Warehouse2DModelDTO floorModel : warehouse2DModel.getElements()) {
            if (floorModel != null && floorModel.getConfig() != null) {
                Object entranceConfig = floorModel.getConfig().get("entranceConfig");
                EntranceConfigBO entranceConfigBO = JSON.parseObject(JSON.toJSONString(entranceConfig), EntranceConfigBO.class);
                if (entranceConfig != null) {
                    List<BigDecimal> coordinates = entranceConfigBO.getCoordinates();
                    floorStarPointMap.put(floorModel.getRefId().intValue(), Arrays.asList(coordinates.get(0).intValue(), coordinates.get(1).intValue()));
                }
            }
        }
        return floorStarPointMap;
    }


    public List<WarehouseStarirsDTO> findWarehouseStrairs(Integer warehouesId) {
        List<LocationConfigPO> locationConfigPOS = locationConfigPOMapper.selectByWarehouseId(warehouesId);
        List<StrairsPointDTO> stairsList = new ArrayList<>();
        for (LocationConfigPO locationConfigPO : locationConfigPOS) {
            Warehouse2DModelDTO warehouse2DModel = JSON.parseObject(locationConfigPO.getLocationConfig(), Warehouse2DModelDTO.class);
            for (Warehouse2DModelDTO floorModel : warehouse2DModel.getElements()) {
                if (floorModel == null) {
                    continue;
                }
                List<OccupyElementBO> occupyElements = getOccupyElements(floorModel.getConfig());
                if (CollectionUtils.isEmpty(occupyElements)) {
                    continue;
                }
                int floor = floorModel.getRefId().intValue();
                List<OccupyElementBO> upstairs = occupyElements.stream().filter(it -> BooleanUtils.isTrue(it.getMarkUpstairsPoint())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(upstairs)) {
                    List<StrairsPointDTO> strairsPoints = upstairs.stream().map(it -> convertStrairsPoint(it, floor)).collect(Collectors.toList());
                    stairsList.addAll(strairsPoints);
                }
            }
        }
        if (CollectionUtils.isEmpty(stairsList)) {
            return Collections.emptyList();
        }
        Map<String, List<StrairsPointDTO>> pointMap = stairsList.stream().collect(Collectors.groupingBy(StrairsPointDTO::getName));

        List<WarehouseStarirsDTO> warehouseStarirsList = new ArrayList<>();
        for (Map.Entry<String, List<StrairsPointDTO>> entry : pointMap.entrySet()) {
            List<StrairsPointDTO> pointDTOS = entry.getValue();
            if (pointDTOS.size() == 1) {
                continue;
            }
            WarehouseStarirsDTO starirsDTO = new WarehouseStarirsDTO();
            starirsDTO.setWarehouseId(warehouesId);
            starirsDTO.setName(entry.getKey());
            pointDTOS.sort(Comparator.comparing(StrairsPointDTO::getFloor));
            starirsDTO.setUpPoint(pointDTOS.get(0));
            starirsDTO.setDownPoint(pointDTOS.get(1));
            warehouseStarirsList.add(starirsDTO);
        }
        Map<String, List<WarehouseStarirsDTO>> map = warehouseStarirsList.stream().collect(Collectors.groupingBy(it -> it.getDownPoint().getX() + "-" + it.getDownPoint().getY() + "-" + it.getUpPoint().getX() + "-" + it.getUpPoint().getY()));

        List<WarehouseStarirsDTO> result = new ArrayList<>();
        for (List<WarehouseStarirsDTO> value : map.values()) {
            result.add(value.get(0));
        }
        return result;
    }


    public List<LocationPointAndPickPointDTO> getWarehousePointByLocations(LocationPointQueryDTO query) {
        Integer warehouseId = query.getWarehouseId();
        List<LocationParamDTO> locationParams = query.getLocations();

        List<LocationPointInfoPO> locationPointInfoPOS = locationPointInfoPOMapper.selectByWarehouseId(warehouseId);
        if (CollectionUtils.isEmpty(locationPointInfoPOS)) {
            return Collections.emptyList();
        }
        Map<Long, String> locationMap = locationParams.stream().collect(Collectors.toMap(LocationParamDTO::getLocationId, LocationParamDTO::getLocationName, (t1, t2) -> t1));
        Set<Long> locationIds = locationMap.keySet();
        List<LocationPointInfoPO> pickPoints = locationPointInfoPOS.stream().filter(it -> locationIds.contains(it.getLocationId())).collect(Collectors.toList());
        Map<Long, LocationPointInfoPO> pickPointMap = pickPoints.stream().collect(Collectors.toMap(LocationPointInfoPO::getLocationId, it -> it));
        List<LocationPointAndPickPointDTO> resultList = new ArrayList<>();
        Map<String, PointInfoDTO> locationPointMap = getLocationPointMapFromCache(warehouseId);
        for (Map.Entry<Long, String> entry : locationMap.entrySet()) {
            Long locationId = entry.getKey();
            String locationName = entry.getValue();
            LocationPointInfoPO pickPoint = pickPointMap.get(locationId);
            PointInfoDTO pointInfoDTO = locationPointMap.get(buildKey(locationName));
            LocationPointAndPickPointDTO locationPointAndPickPoint = new LocationPointAndPickPointDTO();
            locationPointAndPickPoint.setLocationId(locationId);
            locationPointAndPickPoint.setLocationName(locationName);
            locationPointAndPickPoint.setLocationPoint(pointInfoDTO);
            locationPointAndPickPoint.setPickPoint(build(pickPoint));
            resultList.add(locationPointAndPickPoint);
        }
        return resultList;
    }


    private Map<String, PointInfoDTO> getLocationPointByLocationConfig(List<LocationConfigPO> locationConfigPOS) {
        List<LocationBO> locationBOList = new ArrayList<>();
        for (LocationConfigPO locationConfigPO : locationConfigPOS) {
            Warehouse2DModelDTO warehouse2DModel = JSON.parseObject(locationConfigPO.getLocationConfig(), Warehouse2DModelDTO.class);
            for (Warehouse2DModelDTO floorModel : warehouse2DModel.getElements()) {
                Integer floor = floorModel.getRefId().intValue();
                for (Warehouse2DModelDTO locationArea2DModel : floorModel.getElements()) {
                    if (Objects.equals(Warehouse2DModelType.LOCATION_AREA.getValue(), locationArea2DModel.getType())) {
                        if (Objects.equals(locationArea2DModel.getType(), Warehouse2DModelType.LOCATION_AREA.getValue())) {
                            List<LocationBO> locationBOS = getAreaLocationBO(locationArea2DModel, floor);
                            locationBOList.addAll(locationBOS);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(locationBOList)) {
            return new HashMap<>();
        }
        return locationBOList.stream().collect(Collectors.toMap(LocationBO::getLocationName, this::build, (t1, t2) -> t1));
    }


    private List<LocationBO> getAreaLocationBO(Warehouse2DModelDTO locationArea2DModel, Integer floor) {
        List<BigDecimal> areaCoordinates = locationArea2DModel.getCoordinates();
        List<LocationBO> locationBOS = new ArrayList<>();
        for (Warehouse2DModelDTO shelveGroup : locationArea2DModel.getElements()) {
            if (!Warehouse2DModelType.SHELVE_GROUP.valueEquals(shelveGroup.getType())) {
                continue;
            }
            Map<String, Object> config = shelveGroup.getConfig();
            Integer layNumber = getLayNumber(config);
            List<BigDecimal> shelveGroupCoordinates = shelveGroup.getCoordinates();
            for (Warehouse2DModelDTO shelve : shelveGroup.getElements()) {
                if (!Warehouse2DModelType.SHELVE.valueEquals(shelve.getType())) {
                    continue;
                }
                List<BigDecimal> shelveCoordinates = shelve.getCoordinates();
                for (Warehouse2DModelDTO locationModel : shelve.getElements()) {
                    if (!Warehouse2DModelType.LOCATION.valueEquals(locationModel.getType())) {
                        continue;
                    }
                    List<BigDecimal> locationCoordinates = locationModel.getCoordinates();
                    BigDecimal x = areaCoordinates.get(0).add(shelveGroupCoordinates.get(0)).add(shelveCoordinates.get(0)).add(locationCoordinates.get(0));
                    BigDecimal y = areaCoordinates.get(1).add(shelveGroupCoordinates.get(1)).add(shelveCoordinates.get(1)).add(locationCoordinates.get(1));
                    locationCoordinates.set(0, x);
                    locationCoordinates.set(1, y);
                    List<String> locationNames = getLocationNames(locationModel.getRefName(), layNumber);
                    for (String name : locationNames) {
                        LocationBO locationBO = new LocationBO();
                        locationBO.setLocationName(buildKey(name));
                        locationBO.setCoordinates(locationCoordinates);
                        locationBO.setFloor(floor);
                        locationBOS.add(locationBO);
                    }
                }
            }
        }
        return locationBOS;
    }

    private PointInfoDTO build(LocationPointInfoPO locationPointInfoPO) {
        if (locationPointInfoPO == null) {
            return null;
        }
        PointInfoDTO pointInfoDTO = new PointInfoDTO();
        pointInfoDTO.setX(locationPointInfoPO.getX());
        pointInfoDTO.setY(locationPointInfoPO.getY());
        pointInfoDTO.setFloor(locationPointInfoPO.getFloor());
        return pointInfoDTO;
    }

    private PointInfoDTO build(LocationBO LocationBO){
        PointInfoDTO pointInfoDTO = new PointInfoDTO();
        pointInfoDTO.setX(LocationBO.getCoordinates().get(0).intValue());
        pointInfoDTO.setY(LocationBO.getCoordinates().get(1).intValue());
        pointInfoDTO.setFloor(LocationBO.getFloor());
        return pointInfoDTO;
    }

    public String buildKey(String locationName) {
        return locationName.replaceAll("(?<=[^0-9])0+(\\d+)", "$1");
    }

    private List<String> getLocationNames(String locationName, Integer layNumber) {
        List<String> locationNames = new ArrayList<>();
        locationNames.add(locationName);
        if (layNumber == null || layNumber == 1) {
            return locationNames;
        }
        String[] splits = locationName.split("-");
        if (splits.length < 3) {
            return locationNames;
        }
        for (int i = 2; i <= layNumber; i++) {
            splits[splits.length - 2] = "0" + i;
            locationNames.add(String.join("-", splits));
        }
        return locationNames;
    }

    private Integer getLayNumber(Map<String, Object> config){
        if(config == null) {
            return null;
        }
        Object layerNumber = config.get("singleShelfLayerNumber");
        if(Objects.isNull(layerNumber)){
            return 1;
        }
        return Integer.parseInt(layerNumber.toString());
    }


    private StrairsPointDTO convertStrairsPoint(OccupyElementBO  occupyElementBO,Integer floor){
        StrairsPointDTO strairsPointDTO = new StrairsPointDTO();
        strairsPointDTO.setName(occupyElementBO.getRefName());
        strairsPointDTO.setFloor(floor);
        strairsPointDTO.setX(occupyElementBO.getCoordinates().get(0).intValue());
        strairsPointDTO.setY(occupyElementBO.getCoordinates().get(1).intValue());
        return strairsPointDTO;
    }





    private List<OccupyElementBO> getOccupyElements(Map<String, Object> config){
        if(MapUtils.isEmpty(config)){
            return Collections.emptyList();
        }
        Object occupyElements = config.get("occupyElements");
        if (Objects.isNull(occupyElements)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(JSON.toJSONString(occupyElements), OccupyElementBO.class);
    }


    private WarehouseRoutePointDTO convert(GridPO gridPO) {
        WarehouseRoutePointDTO warehouseRoutePointDTO = new WarehouseRoutePointDTO();
        warehouseRoutePointDTO.setId(gridPO.getId());
        warehouseRoutePointDTO.setWarehouseId(gridPO.getWarehouseId());
        warehouseRoutePointDTO.setX(gridPO.getX());
        warehouseRoutePointDTO.setY(gridPO.getY());
        warehouseRoutePointDTO.setFloor(gridPO.getFloor());
        warehouseRoutePointDTO.setWalkable(gridPO.getIsWalkable());
        return warehouseRoutePointDTO;
    }

    private LocationPointInfoDTO convert(LocationPointInfoPO po) {
        LocationPointInfoDTO locationPointInfoDTO = new LocationPointInfoDTO();
        locationPointInfoDTO.setId(po.getId());
        locationPointInfoDTO.setWarehouseId(po.getWarehouseId());
        locationPointInfoDTO.setLocationId(po.getLocationId());
        locationPointInfoDTO.setX(po.getX());
        locationPointInfoDTO.setY(po.getY());
        locationPointInfoDTO.setZ(po.getZ());
        locationPointInfoDTO.setFloor(po.getFloor());
        locationPointInfoDTO.setRemark(po.getRemark());
        return locationPointInfoDTO;
    }


    public Map<String, PointInfoDTO> getLocationPointMapFromCache(Integer warehouseId) {
        Map<String, PointInfoDTO> locationPointMap = LocationPointCache.getIfPresent(warehouseId);
        if (MapUtils.isEmpty(locationPointMap)) {
            List<LocationConfigPO> locationConfigPOS = locationConfigPOMapper.selectByWarehouseId(warehouseId);
            locationPointMap = getLocationPointByLocationConfig(locationConfigPOS);
        }
        return locationPointMap;
    }
}
