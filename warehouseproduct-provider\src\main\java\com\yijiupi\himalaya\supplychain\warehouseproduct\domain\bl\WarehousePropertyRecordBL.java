package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.WarehousePropertyRecordConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarehousePropertyRecordMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehousePropertyRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> 固定资产同步
 */
@Service
public class WarehousePropertyRecordBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(WarehousePropertyRecordBL.class);
    @Autowired
    private WarehousePropertyRecordMapper warehousePropertyMapper;

    public void warehousePropertySync(WarehousePropertyRecordDTO warehousePropertyDTO) {
        // 校验当月是否同步过 如果同步过 则不新增数据
        Date applicantDate = warehousePropertyMapper.queryRecordDateByWarehouse(warehousePropertyDTO.getWarehouse());
        String applicantDateParam = warehousePropertyDTO.getApplicantDate();
        if (!ObjectUtils.isEmpty(applicantDate)
            && isSameMonthDate(applicantDate, DateUtil.parseDate(applicantDateParam))) {
            LOGGER.warn("WarehousePropertyRecordBL.warehousePropertySync 该仓库本月已上传");
            return;
        }
        warehousePropertyMapper.insert(WarehousePropertyRecordConvert.convert(warehousePropertyDTO));
    }

    public static boolean isSameMonthDate(Date date1, Date date2) {
        try {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(date1);

            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date2);

            boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
            boolean isSameMonth = isSameYear && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);

            return isSameMonth;
        } catch (Exception e) {
        }
        return false;

    }
}
