package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.distribution;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.DistributionMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.DistributionPercentRecordMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.DistributionPercentPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentSearchDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 配送系数
 * 
 * <AUTHOR> 2017/12/1
 */
@Service
public class DistributionBL {
    private static final Logger LOG = LoggerFactory.getLogger(DistributionBL.class);
    @Autowired
    private DistributionMapper distributionMapper;
    @Autowired
    private DistributionPercentRecordMapper distributionPercentRecordMapper;
    private Gson gson = new Gson();

    /**
     * 给skuId绑定配送系数
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addDistributionPercent(DistributionPercentSearchDTO dto) {
        ArrayList<DistributionPercentPO> distributionPercentPOList = new ArrayList<>();
        // 先查询,然后插入修改记录.
        List<DistributionPercentDTO> distributionPercentBySkuList =
            distributionMapper.getDistributionPercentBySkuList(dto.getSkuIdList());
        distributionMapper.addDistributionPercent(dto);
        for (DistributionPercentDTO distributionPercentDTO : distributionPercentBySkuList) {
            DistributionPercentPO po = new DistributionPercentPO();
            po.setCityId(distributionPercentDTO.getCityId());
            po.setDistributionPercentForAmount(dto.getDistributionPercentForAmount());
            po.setProductSkuId(distributionPercentDTO.getProductSkuId());
            po.setOldContext(gson.toJson(po));// 这里要求存修改之后的json
            po.setBusinessId(distributionPercentDTO.getProductSkuId());
            po.setBusinessType("修改配送系数");
            po.setDescription(dto.getTrueName() + "修改了配送系数.产品skuId:" + distributionPercentDTO.getProductSkuId()
                + ",配送系数-工资:" + distributionPercentDTO.getDistributionPercentForAmount() + "->"
                + dto.getDistributionPercentForAmount());
            po.setUserId(dto.getUserId());
            po.setId(UUIDGenerator.getUUID(DistributionPercentPO.class.getName()));
            distributionPercentPOList.add(po);
        }
        distributionMapper.addDistributionPercentRecord(distributionPercentPOList);
        LOG.info("skuId绑定配送系数:skuidList:{} 配送系数-件数:{} 配送系数-工资:{} 操作人id:{} 操作人姓名:{} 操作时间:{}", dto.getSkuIdList(),
            dto.getDistributionPercent(), dto.getDistributionPercentForAmount(), dto.getUserId(), dto.getTrueName(),
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
    }

    /**
     * 根据skuId获取配送系数
     */
    public DistributionPercentDTO getDistributionPercentBySku(Long productSkuId) {
        return distributionMapper.getDistributionPercentBySku(productSkuId);
    }

    /**
     * 根据skuIdList获取配送系数
     */
    public List<DistributionPercentDTO> getDistributionPercentBySkuList(List<Long> productSkuIdList) {
        return distributionMapper.getDistributionPercentBySkuList(productSkuIdList);
    }

    /**
     * 查询一个月内城市配送系数修改记录
     */
    public Integer findDistributionPercentRecord(DistributionPercentRecordDTO distributionRecordDTO) {
        return distributionPercentRecordMapper.findDistributionPercentRecord(distributionRecordDTO);
    }
}
