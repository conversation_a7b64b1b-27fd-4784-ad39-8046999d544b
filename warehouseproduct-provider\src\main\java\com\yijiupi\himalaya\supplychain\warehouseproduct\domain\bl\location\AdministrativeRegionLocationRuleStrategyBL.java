package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 行政区域出库位规则相关
 *
 * <AUTHOR>
 */
@Service("AdministrativeRegionLocationRuleStrategyBL")
public class AdministrativeRegionLocationRuleStrategyBL implements LocationRuleStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdministrativeRegionLocationRuleStrategyBL.class);

    @Autowired
    private LocationRuleMapper locationRuleMapper;

    @Autowired
    @Qualifier("CarLocationRuleStrategyBL")
    private LocationRuleStrategy locationRuleStrategy;

    private static final String SIGN = "-";

    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // 过滤未设置出库位集合 并存在行政区域
        List<LocationRuleDTO> locationRuleDTOS = locationRuleDTOList.stream()
            .filter(l -> StringUtils.isNotBlank(l.getProvince()) && StringUtils.isNotBlank(l.getCity())
                && StringUtils.isBlank(l.getLocationName()) && ObjectUtils.isEmpty(l.getLocationId()))
            .collect(Collectors.toList());
        // 查询车辆
        if (CollectionUtils.isEmpty(locationRuleDTOS)) {
            return locationRuleStrategy.getLocation(locationRuleDTOList);
        }
        StringBuffer stringBuffer = new StringBuffer();
        // 根据行政区域查询
        for (LocationRuleDTO locationRuleDTO : locationRuleDTOS) {
            String joinRegionName = stringBuffer.append(locationRuleDTO.getProvince()).append(SIGN)
                .append(locationRuleDTO.getCity()).append(SIGN).append(locationRuleDTO.getDistrict()).append(SIGN)
                .append(locationRuleDTO.getStreet()).toString();
            LocationRulePO locationRulePO = getLocationByRuleName(joinRegionName, locationRuleDTO.getWarehouseId());
            if (!ObjectUtils.isEmpty(locationRulePO)) {
                locationRuleDTO.setLocationId(locationRulePO.getLocationId());
                locationRuleDTO.setLocationName(locationRulePO.getLocationName());
            }
        }
        // 查询车辆
        return locationRuleStrategy.getLocation(locationRuleDTOList);
    }

    private LocationRulePO getLocationByRuleName(String joinRegionName, Integer warehouseId) {
        // 如果为空 返回null
        if (StringUtils.isBlank(joinRegionName)) {
            return null;
        }
        LocationRulePO locationRulePO = locationRuleMapper.getLocationByName(joinRegionName, warehouseId);
        // 查询到数据 返回数据
        if (!ObjectUtils.isEmpty(locationRulePO)) {
            return locationRulePO;
        }
        int indexOf = joinRegionName.lastIndexOf(SIGN);
        if (indexOf < 0) {
            return null;
        }
        String substring = joinRegionName.substring(0, indexOf);
        // 继续查询
        return getLocationByRuleName(substring, warehouseId);
    }
}
