package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 车辆出库位规则相关
 *
 * <AUTHOR>
 */
@Service("CarLocationRuleStrategyBL")
public class CarLocationRuleStrategyBL implements LocationRuleStrategy {

    @Autowired
    @Qualifier("DefaultLocationRuleStrategyBL")
    private LocationRuleStrategy locationRuleStrategy;

    @Autowired
    private LocationRuleMapper locationRuleMapper;

    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // 过滤未设置出库位集合
        List<
            LocationRuleDTO> collect =
                locationRuleDTOList
                    .stream().filter(l -> StringUtils.isNotBlank(l.getRuleId())
                        && StringUtils.isBlank(l.getLocationName()) && ObjectUtils.isEmpty(l.getLocationId()))
                    .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            // 查询默认方式
            return locationRuleStrategy.getLocation(locationRuleDTOList);
        }
        Map<Integer, List<LocationRuleDTO>> warehouseIdGroupLocationRule =
            collect.stream().collect(Collectors.groupingBy(LocationRuleDTO::getWarehouseId));
        Set<Integer> warehouseIdList = warehouseIdGroupLocationRule.keySet();
        for (Integer warehouseId : warehouseIdList) {
            List<LocationRuleDTO> locationRuleDTOS = warehouseIdGroupLocationRule.get(warehouseId);
            List<String> ruleList =
                locationRuleDTOS.stream().map(LocationRuleDTO::getRuleId).collect(Collectors.toList());
            // 根据车辆规则查询
            List<LocationRulePO> locationRulePOS = Optional
                .ofNullable(
                    locationRuleMapper.listLocationByRuleId(warehouseId, ruleList, LocationRuleEnum.CAR.getCode()))
                .orElse(new ArrayList<>());
            for (LocationRulePO locationRulePO : locationRulePOS) {
                LocationRuleDTO locationRuleDTO =
                    locationRuleDTOS.stream().filter(l -> l.getRuleId().equals(locationRulePO.getRuleId())).findFirst()
                        .orElse(new LocationRuleDTO());
                locationRuleDTO.setLocationName(locationRulePO.getLocationName());
                locationRuleDTO.setLocationId(locationRulePO.getLocationId());
            }
        }
        // 查询默认方式
        return locationRuleStrategy.getLocation(locationRuleDTOList);
    }
}
