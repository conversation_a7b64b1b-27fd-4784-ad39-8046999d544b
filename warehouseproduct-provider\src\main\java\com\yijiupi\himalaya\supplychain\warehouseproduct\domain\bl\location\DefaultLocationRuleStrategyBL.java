package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认出库位规则相关
 *
 * <AUTHOR>
 */
@Service("DefaultLocationRuleStrategyBL")
public class DefaultLocationRuleStrategyBL implements LocationRuleStrategy {
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // 过滤未设置出库位集合
        List<LocationRuleDTO> locationRuleDTOS = locationRuleDTOList.stream()
            .filter(l -> ObjectUtils.isEmpty(l.getLocationId()) && StringUtils.isBlank(l.getLocationName()))
            .collect(Collectors.toList());
        // 出库位全部已查询完
        if (CollectionUtils.isEmpty(locationRuleDTOS)) {
            return locationRuleDTOList;
        }
        List<Integer> warehouseIdList =
            locationRuleDTOS.stream().map(LocationRuleDTO::getWarehouseId).distinct().collect(Collectors.toList());
        // 查询默认出库位信息
        List<WarehouseConfigDTO> warehouseConfigDTOS =
            warehouseConfigService.listWarehouseDefaultLocation(warehouseIdList);
        if (ObjectUtils.isEmpty(warehouseConfigDTOS) || warehouseConfigDTOS.size() != warehouseIdList.size()) {
            throw new BusinessException("查询仓库信息失败 warehouse is null");
        }
        for (LocationRuleDTO locationRuleDTO : locationRuleDTOS) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigDTOS.stream()
                .filter(w -> w.getWarehouse_Id().compareTo(locationRuleDTO.getWarehouseId()) == 0).findFirst()
                .orElse(new WarehouseConfigDTO());
            locationRuleDTO.setLocationId(warehouseConfigDTO.getDefaultLocationId());
            locationRuleDTO.setLocationName(warehouseConfigDTO.getDefaultLocationName());
        }
        return locationRuleDTOList;
    }
}
