package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 快递物流相关规则
 *
 * <AUTHOR>
 */
@Service("ExpressServiceLocationRuleStrategyBL")
public class ExpressServiceLocationRuleStrategyBL implements LocationRuleStrategy {

    @Autowired
    @Qualifier("LineOrDistrictLocationRuleStrategyBL")
    private LocationRuleStrategy locationRuleStrategy;

    @Autowired
    private LocationRuleMapper locationRuleMapper;

    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // 获取ruleId 如果集合为空 则表示不走快递物流
        List<LocationRuleDTO> collect = locationRuleDTOList.stream().filter(l -> StringUtils.isNotBlank(l.getRuleId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            // 查询线路片区
            return locationRuleStrategy.getLocation(locationRuleDTOList);
        }
        // 查询快递物流信息
        Map<Integer, List<LocationRuleDTO>> warehouseGroupLocation =
            collect.stream().collect(Collectors.groupingBy(LocationRuleDTO::getWarehouseId));
        Set<Integer> warehouseIdList = warehouseGroupLocation.keySet();
        for (Integer warehouseId : warehouseIdList) {
            // 查询快递物流配置
            List<LocationRuleDTO> ruleDTOS = warehouseGroupLocation.get(warehouseId);
            List<LocationRulePO> locationRulePOS =
                Optional.ofNullable(locationRuleMapper.listLocationByRuleId(warehouseId,
                    ruleDTOS.stream().map(LocationRuleDTO::getRuleId).collect(Collectors.toList()),
                    LocationRuleEnum.EXPRESS_SERVICE.getCode())).orElse(new ArrayList<>());
            for (LocationRuleDTO locationRuleDTO : collect) {
                LocationRulePO locationRulePO =
                    locationRulePOS.stream().filter(l -> l.getRuleId().equals(locationRuleDTO.getRuleId())).findFirst()
                        .orElse(new LocationRulePO());
                locationRuleDTO.setLocationId(locationRulePO.getLocationId());
                locationRuleDTO.setLocationName(locationRulePO.getLocationName());
            }
        }
        // 查询线路片区
        return locationRuleStrategy.getLocation(locationRuleDTOList);
    }
}
