package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.RoutingDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IRoutingService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ConfigUtil;

/**
 * <AUTHOR> 线路/片区 规则
 */
@Service("LineOrDistrictLocationRuleStrategyBL")
public class LineOrDistrictLocationRuleStrategyBL implements LocationRuleStrategy {

    @Autowired
    @Qualifier("AdministrativeRegionLocationRuleStrategyBL")
    private LocationRuleStrategy locationRuleStrategy;

    @Autowired
    private LocationRuleMapper locationRuleMapper;

    @Reference
    private IRoutingService routingService;

    @Autowired
    private ConfigUtil configUtil;

    private String selectByIdsUrl = "apitmssetting/IDeliveryCenterService/selectByIds";

    @Autowired
    RestTemplate restTemplate;

    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // 过滤出未设置出库位 并ruleId存在值 或者addressId存在
        List<LocationRuleDTO> collect = locationRuleDTOList.stream()
            .filter(l -> (StringUtils.isNotBlank(l.getRuleId()) || !ObjectUtils.isEmpty(l.getAddressId()))
                && ObjectUtils.isEmpty(l.getLocationId()) && StringUtils.isBlank(l.getLocationName()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return locationRuleStrategy.getLocation(locationRuleDTOList);
        }
        // 根据过滤后的collect根据仓库进行分组
        Map<Integer, List<LocationRuleDTO>> warehouseGroupLocation =
            collect.stream().collect(Collectors.groupingBy(LocationRuleDTO::getWarehouseId));

        // 查询仓库默认集合方式

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        List<Warehouse> warehouses = restTemplate.exchange(configUtil.tmsGatewayUrl + selectByIdsUrl, HttpMethod.POST,
            new HttpEntity<>("[" + JSON.toJSONString(warehouseGroupLocation.keySet()) + "]", headers),
            new ParameterizedTypeReference<List<Warehouse>>() {}).getBody();
        if (CollectionUtils.isEmpty(warehouses)) {
            // 查询行政区域
            return locationRuleStrategy.getLocation(locationRuleDTOList);
        }
        Set<Integer> warehouseIdList = warehouseGroupLocation.keySet();
        for (Integer warehouseId : warehouseIdList) {
            Warehouse warehouse =
                warehouses.stream().filter(w -> w.getId().compareTo(warehouseId) == 0).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(warehouse)) {
                continue;
            }
            // 根据分组的仓库 获取LocationRuleDTO集合
            List<LocationRuleDTO> ruleDTOList = warehouseGroupLocation.get(warehouseId);
            if (ObjectUtils.isEmpty(warehouse.getDistributeCarType())) {
                // 没有集货方式
                continue;
            }
            // 0按线路
            if (warehouse.getDistributeCarType() == 0) {
                List<Integer> addressIdList = ruleDTOList.stream().filter(r -> !ObjectUtils.isEmpty(r.getAddressId()))
                    .map(LocationRuleDTO::getAddressId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(addressIdList)) {
                    continue;
                }
                // 根据addressId 查询线路
                Map<Integer, RoutingDTO> routingByAddressIds =
                    routingService.findRoutingByAddressIds(addressIdList, warehouseId);
                // 判断查询线路是否为空
                if (CollectionUtils.isEmpty(routingByAddressIds)) {
                    continue;
                }
                Set<Integer> keySet = routingByAddressIds.keySet();
                for (Integer key : keySet) {
                    // 获取当前addressId的线路信息 key=addressID
                    RoutingDTO routingDTO = routingByAddressIds.get(key);
                    // 过滤出未设置出库位 并且addressId等于key的LocationRuleDTO对象
                    List<LocationRuleDTO> filterAddressIdRuleDTOList = ruleDTOList.stream()
                        .filter(r -> Objects.nonNull(r.getAddressId()) && r.getAddressId().equals(key))
                        .collect(Collectors.toList());
                    List<LocationRulePO> locationRulePOS = Optional
                        .ofNullable(locationRuleMapper.listLocationByRuleId(warehouseId,
                            Arrays.asList(String.valueOf(routingDTO.getRouteId())), LocationRuleEnum.LINE.getCode()))
                        .orElse(new ArrayList<>());
                    if (CollectionUtils.isEmpty(locationRulePOS)) {
                        continue;
                    }
                    for (LocationRuleDTO locationRuleDTO : filterAddressIdRuleDTOList) {
                        LocationRulePO locationRulePO = locationRulePOS.stream()
                            .filter(ql -> ql.getRuleId().equals(String.valueOf(routingDTO.getRouteId())))
                            .findFirst().orElse(new LocationRulePO());
                        locationRuleDTO.setLocationId(locationRulePO.getLocationId());
                        locationRuleDTO.setLocationName(locationRulePO.getLocationName());
                    }
                }
            } else {
                // 片区
                List<String> areaIdList = ruleDTOList.stream().filter(r -> !ObjectUtils.isEmpty(r.getAreaId()))
                    .map(l -> String.valueOf(l.getAreaId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(areaIdList)) {
                    continue;
                }
                List<LocationRulePO> locationRulePOS =
                    Optional.ofNullable(locationRuleMapper.listLocationByRuleId(warehouseId, areaIdList,
                        LocationRuleEnum.DISTRICT.getCode())).orElse(new ArrayList<>());
                if (CollectionUtils.isEmpty(locationRulePOS)) {
                    continue;
                }
                // 当前仓库
                for (LocationRuleDTO locationRuleDTO : ruleDTOList) {
                    LocationRulePO locationRulePO =
                        locationRulePOS.stream().filter(l -> l.getRuleId().equals(locationRuleDTO.getRuleId()))
                            .findFirst().orElse(new LocationRulePO());
                    locationRuleDTO.setLocationId(locationRulePO.getLocationId());
                    locationRuleDTO.setLocationName(locationRulePO.getLocationName());
                }
            }
        }
        // 查询行政区域
        return locationRuleStrategy.getLocation(locationRuleDTOList);
    }
}
