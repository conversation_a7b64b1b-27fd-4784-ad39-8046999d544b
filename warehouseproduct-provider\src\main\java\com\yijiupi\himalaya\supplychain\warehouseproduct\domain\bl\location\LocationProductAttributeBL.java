package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.SqlReportDTO;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.warehouseproduct.VesselInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.warehouseproduct.VesselQueryParam;
import com.yijiupi.himalaya.supplychain.service.ISqlReportService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.service.warehouseproduct.IVesselInventoryInfo;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationVisualDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductShelfLifeEnum.*;
import static com.yijiupi.himalaya.supplychain.warehouseproduct.enums.StorageStateEnum.*;
import static com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ViewModeType.*;
import static java.util.stream.Collectors.*;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/5/8
 */
@Service
public class LocationProductAttributeBL {

    @Value("${sqlreport.productstorebatch.id}")
    private String productStoreBatchId;
    @Value("${sqlreport.productlocationstore.id}")
    private String productLocationStoreId;
    @Value("${sqlreport.productstorebatchsku.id}")
    private String productStoreBatchskuId;
    @Value("${sqlreport.productlocationsku.id}")
    private String productLocationskuId;
    @Value("${sqlreport.productdate.id}")
    private String productDateId;
    @Value("${sqlreport.productlocationdate.id}")
    private String productLocationDateId;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private ISqlReportService iSqlReportService;
    @Reference
    private IVesselInventoryInfo vesselInventoryInfo;

    /**
     * 为 货位、物料箱 填充额外属性, 并转换模型
     *
     * @param viewMode  视图模式
     * @param location  货位、物料箱数据
     * @param resultMap 额外属性 map
     * @return 填充额外属性并转换模型后的数据
     */
    public LocationVisualDTO convertToVisualDTO(Byte viewMode, LoactionDTO location, Map<String, Set<Long>> resultMap) {
        LocationVisualDTO locationVisualDTO = new LocationVisualDTO();
        BeanUtils.copyProperties(location, locationVisualDTO);
        if (viewMode.equals(INVENTORY_DISTRIBUTION.getType())) {
            // 设置库存状态
            setLocationStoreState(locationVisualDTO, resultMap);
        } else if (viewMode.equals(HOT_SPOT.getType())) {
            // 设置动销状态
            setAttributeInit(locationVisualDTO, resultMap);
        } else if (viewMode.equals(VALIDITY.getType())) {
            // 设置产品效期状态
            setProductShelfLifeState(locationVisualDTO, resultMap);
        }
        // 设置停用状态
        locationVisualDTO.setState(location.getState());
        return locationVisualDTO;
    }

    /**
     * 按照视图模式获取对应货位额外属性
     *
     * @param query    查询条件
     * @param viewMode 视图模式
     */
    public Map<String, Set<Long>> getResultMapByViewMode(LocationQueryDTO query, Byte viewMode) {
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(query.getWarehouseId());
        Boolean isOpenLocationStock = warehouseConfigDTO.getIsOpenLocationStock();
        if (viewMode.equals(INVENTORY_DISTRIBUTION.getType())) {
            String sqlReportId = isOpenLocationStock ? productStoreBatchId : productLocationStoreId;
            return sqlReportInit(query, sqlReportId).stream().collect(groupBy("status", "locationId"));
        } else if (viewMode.equals(HOT_SPOT.getType())) {
            String sqlReportId = isOpenLocationStock ? productStoreBatchskuId : productLocationskuId;
            return sqlReportInit(query, sqlReportId).stream().collect(groupBy("inventoryPinProperty", "locationId"));
        } else if (viewMode.equals(VALIDITY.getType())) {
            String sqlReportId = isOpenLocationStock ? productDateId : productLocationDateId;
            return sqlReportInit(query, sqlReportId).stream().collect(groupBy("status", "locationId"));
        }
        return Collections.emptyMap();
    }

    /**
     * 按照视图模式获取对应物料箱额外属性
     *
     * @param query    查询条件
     * @param vessels  物料箱数据
     * @param viewMode 视图模式
     */
    public Map<String, Set<Long>> getVesselResultMap(LocationQueryDTO query, Collection<List<LoactionDTO>> vessels, Byte viewMode) {
        Integer warehouseId = query.getWarehouseId();
        List<Long> vesselIds = vessels.stream().flatMap(Collection::stream)
                .map(LoactionDTO::getId).distinct().collect(toList());
        if (vesselIds.isEmpty()) {
            return Collections.emptyMap();
        }
        VesselQueryParam param = new VesselQueryParam();
        param.setViewMode(viewMode);
        param.setCityId(query.getCityId());
        param.setWarehouseId(warehouseId);
        param.setOpenLocationStock(warehouseConfigService.isOpenLocationStock(warehouseId));
        param.setLocationIds(vesselIds);
        List<VesselInfoDTO> vesselInfos = vesselInventoryInfo.getVesselInfo(param);
        if (vesselInfos.isEmpty()) {
            return Collections.emptyMap();
        }
        Function<VesselInfoDTO, String> keyMapper = it -> Optional.ofNullable(it.getStatus()).orElseGet(it::getInventoryPinProperty);
        return vesselInfos.stream().collect(groupingBy(keyMapper, mapping(VesselInfoDTO::getLocationId, toSet())));
    }

    @SuppressWarnings("SameParameterValue")
    private Collector<Map<String, Object>, ?, Map<String, Set<Long>>> groupBy(String key, String value) {
        return Collectors.groupingBy(item -> item.get(key).toString(),
                mapping(item -> Long.valueOf(item.get(value).toString()), toSet()));
    }

    /**
     * 动态 sql 查询初始化
     */
    private List<Map<String, Object>> sqlReportInit(LocationQueryDTO locationQueryDTO, String sqlReportId) {
        SqlReportDTO sqlReportDTO = new SqlReportDTO();
        Map<String, String> filteMap = new HashMap<>();
        filteMap.put("areaId", locationQueryDTO.getAreaId().toString());
        filteMap.put("warehouseId", locationQueryDTO.getWarehouseId().toString());
        filteMap.put("cityId", locationQueryDTO.getCityId().toString());
        filteMap.put("orgId", locationQueryDTO.getCityId().toString());
        sqlReportDTO.setId(sqlReportId);
        sqlReportDTO.setFilterMap(filteMap);
        return iSqlReportService.executeSql(sqlReportDTO);
    }

    private void setAttributeInit(LocationVisualDTO locationVisualDTO, Map<String, Set<Long>> basicAttributeMap) {
        Set<Long> Alist = basicAttributeMap.get("A");
        Set<Long> Blist = basicAttributeMap.get("B");
        Set<Long> Clist = basicAttributeMap.get("C");
        Long locationId = locationVisualDTO.getId();
        if (!CollectionUtils.isEmpty(basicAttributeMap)) {
            if (!CollectionUtils.isEmpty(Alist)) {
                if (Alist.contains(locationId)) {
                    locationVisualDTO.setAbcAttribute("A");
                    return;
                }
            }
            if (!CollectionUtils.isEmpty(Blist)) {
                if (Blist.contains(locationId)) {
                    locationVisualDTO.setAbcAttribute("B");
                    return;
                }
            }
            if (!CollectionUtils.isEmpty(Clist)) {
                if (Clist.contains(locationId)) {
                    locationVisualDTO.setAbcAttribute("C");
                }
            }
        }
    }

    /**
     * 设置库存状态
     */
    private void setLocationStoreState(LocationVisualDTO locationVisualDTO, Map<String, Set<Long>> productStoreStateMap) {
        Set<Long> normalList = productStoreStateMap.get(String.valueOf(正常.getType()));
        Set<Long> negativeList = productStoreStateMap.get(String.valueOf(负库存.getType()));
        if (!CollectionUtils.isEmpty(negativeList)) {
            if (negativeList.contains(locationVisualDTO.getId())) {
                locationVisualDTO.setStorageState(负库存.getType());
                return;
            }
        }
        if (!CollectionUtils.isEmpty(normalList)) {
            if (normalList.contains(locationVisualDTO.getId())) {
                locationVisualDTO.setStorageState(正常.getType());
                return;
            }
        }
        locationVisualDTO.setStorageState(空闲.getType());
    }

    /**
     * 设置货位效期状态
     */
    private void setProductShelfLifeState(LocationVisualDTO locationVisualDTO, Map<String, Set<Long>> productShelfLifeMap) {
        Set<Long> expireList = productShelfLifeMap.get(String.valueOf(过期.getType()));
        Set<Long> nearList = productShelfLifeMap.get(String.valueOf(临期.getType()));
        Set<Long> newnessList = productShelfLifeMap.get(String.valueOf(崭新.getType()));
        Long locationId = locationVisualDTO.getId();
        if (!CollectionUtils.isEmpty(expireList)) {
            if (expireList.contains(locationId)) {
                locationVisualDTO.setShelfLifeStatus(过期.getType());
                return;
            }
        }
        if (!CollectionUtils.isEmpty(nearList)) {
            if (nearList.contains(locationId)) {
                locationVisualDTO.setShelfLifeStatus(临期.getType());
                return;
            }
        }
        if (!CollectionUtils.isEmpty(newnessList)) {
            if (newnessList.contains(locationId)) {
                locationVisualDTO.setShelfLifeStatus(崭新.getType());
            }
        }
    }

}

