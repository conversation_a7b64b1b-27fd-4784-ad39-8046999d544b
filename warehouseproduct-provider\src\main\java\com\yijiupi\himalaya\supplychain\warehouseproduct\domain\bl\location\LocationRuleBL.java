package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation.AdministrativeRegionThirdPriorityBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation.OutStockLocationBaseBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation.RecommendLocationQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation.RouteOrAreaSecondBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationRuleConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.RecommendOutLocationQueryDTOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.OutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;

/**
 * 出库位规则相关
 *
 * <AUTHOR>
 */
@Service
public class LocationRuleBL {

    @Autowired
    private LocationRuleMapper locationRuleMapper;
    @Autowired
    @Qualifier("ExpressServiceLocationRuleStrategyBL")
    private LocationRuleStrategy locationRuleStrategy;
    @Autowired
    private List<OutStockLocationBaseBL> outStockLocationBlList;
    @Resource
    private RouteOrAreaSecondBL routeOrAreaSecondBL;
    @Resource
    private AdministrativeRegionThirdPriorityBL administrativeRegionThirdPriorityBL;
    @Resource
    private RecommendLocationQueryBL recommendLocationQueryBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IProductLocationService productLocationService;

    /**
     * 出库位规则查询
     */
    public List<LocationRuleDTO> listLocationRule(List<LocationRuleDTO> locationRuleDTOList) {
        Integer warehouseId =
            locationRuleDTOList.stream().map(LocationRuleDTO::getWarehouseId).findFirst().orElse(null);
        List<LocationRulePO> locationRulePOS =
            Optional.ofNullable(locationRuleMapper.listLocationRule(locationRuleDTOList, warehouseId))
                .orElse(new ArrayList<>());
        return LocationRuleConvert.convertQueryList(locationRulePOS);
    }

    /**
     * 保存出库位规则
     */
    @Transactional(rollbackFor = Throwable.class)
    public void saveRule(List<LocationRuleDTO> locationRuleDTOList) {
        // 删除历史
        locationRuleMapper.delete(locationRuleDTOList.get(0).getWarehouseId(),
            locationRuleDTOList.stream().map(LocationRuleDTO::getLocationId).collect(Collectors.toList()));
        List<LocationRuleDTO> collect = locationRuleDTOList.stream().filter(l -> StringUtils.isNotBlank(l.getRuleId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }

        List<LocationRulePO> locationRulePO = LocationRuleConvert.convertSave(collect);
        if (CollectionUtils.isEmpty(locationRulePO)) {
            return;
        }
        locationRuleMapper.insert(locationRulePO);
    }

    /**
     * 保存出库位规则
     */
    @Transactional(rollbackFor = Throwable.class)
    public void saveRuleIfAbsent(List<LocationRuleDTO> locationRuleDTOList) {
        Map<Integer,
            Map<Integer, List<Long>>> locationTypeWarehouseGroup = locationRuleDTOList.stream()
                .collect(Collectors.groupingBy(LocationRuleDTO::getRuleType,
                    Collectors.groupingBy(LocationRuleDTO::getWarehouseId,
                        Collectors.mapping(LocationRuleDTO::getLocationId, Collectors.toList()))));
        Set<String> existsLocationIds = locationTypeWarehouseGroup.entrySet().stream()
            .flatMap(it -> it.getValue().entrySet().stream()
                .map(item -> locationRuleMapper.queryByLocationIds(item.getValue(), item.getKey(), it.getKey())))
            .filter(it -> !CollectionUtils.isEmpty(it)).flatMap(Collection::stream)
            .map(it -> String.format("%s-%s", it.getRuleId(), it.getLocationId())).collect(Collectors.toSet());
        List<LocationRuleDTO> insertList = locationRuleDTOList.stream()
            .filter(it -> !existsLocationIds.contains(String.format("%s-%s", it.getRuleId(), it.getLocationId())))
            .filter(l -> StringUtils.isNotBlank(l.getRuleId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)) {
            List<LocationRulePO> locationRulePO = LocationRuleConvert.convertSave(insertList);
            locationRuleMapper.insert(locationRulePO);
        }
    }

    /**
     * 获取出库位信息
     */
    @Deprecated
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        return locationRuleStrategy.getLocation(locationRuleDTOList);
    }

    /**
     * 设置默认出库位
     *
     * @param locationQueryDTO
     */
    public void saveDefault(LocationRuleDTO locationQueryDTO) {
        WarehouseConfigDTO warehouseConfig =
            warehouseConfigService.getConfigByWareHouseId(locationQueryDTO.getWarehouseId());
        if (ObjectUtils.isEmpty(warehouseConfig)) {
            throw new BusinessException("未找到仓库配置，请先新建仓库配置");
        }
        WarehouseConfigDTO dto = new WarehouseConfigDTO();
        dto.setWarehouse_Id(locationQueryDTO.getWarehouseId());
        dto.setDefaultLocationId(locationQueryDTO.getLocationId());
        dto.setDefaultLocationName(locationQueryDTO.getLocationName());
        warehouseConfigService.updateWarehouseConfig(dto);
    }

    public List<LocationRuleDTO> listLocation(LocationRuleDTO locationRuleDTO) {
        WarehouseConfigDTO warehouseDto =
            warehouseConfigService.getConfigByWareHouseId(locationRuleDTO.getWarehouseId());
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(locationRuleDTO.getWarehouseId());
        dto.setSubcategory(LocationAreaEnum.周转区.getType().byteValue());
        dto.setLocSubcategory(LocationEnum.出库位.getType().byteValue());
        List<LoactionDTO> dtoList = productLocationService.findLocationListByWarehouseIdAndAreaType(dto);
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<Long> collect = dtoList.stream().map(LoactionDTO::getId).collect(Collectors.toList());
        // 根据id 查询规则
        List<LocationRulePO> locationRulePOS = locationRuleMapper.queryByLocationIds(collect,
            locationRuleDTO.getWarehouseId(), locationRuleDTO.getRuleType());
        return LocationRuleConvert.convertLocation(locationRulePOS, dtoList, warehouseDto, locationRuleDTO);
    }

    /**
     * 获取出库位信息 <br />
     * 这个抄的老代码，但感觉当时写的人没理解逻辑
     */
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryDTO> queryDTOList) {
        List<RecommendOutLocationQueryBO> boList = RecommendOutLocationQueryDTOConvertor.convert(queryDTOList);
        return outStockLocationBlList.stream().map(m -> m.getOutStockLocation(boList))
            .filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty).flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    public List<LocationRuleDTO> getOutStockLocationForNormalOrder(List<RecommendOutLocationQueryDTO> queryList) {
        List<RecommendOutLocationQueryBO> boList = RecommendOutLocationQueryDTOConvertor.convert(queryList);
        return routeOrAreaSecondBL.getOutStockLocation(boList);
    }

    public List<LocationRuleDTO> getOutStockLocationForAllotOrder(List<RecommendOutLocationQueryDTO> queryList) {
        List<RecommendOutLocationQueryBO> boList = RecommendOutLocationQueryDTOConvertor.convert(queryList);
        return administrativeRegionThirdPriorityBL.getOutStockLocation(boList);
    }

    public List<LocationRuleDTO> getOutLocation(OutLocationQueryDTO outLocationQueryDTO) {
        AssertUtils.notNull(outLocationQueryDTO.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notNull(outLocationQueryDTO.getCode(), "类型信息不能为空！");
        AssertUtils.notEmpty(outLocationQueryDTO.getRuleIdList(), "规则信息不能为空！");
        if (LocationRuleEnum.LINE.getCode().equals(outLocationQueryDTO.getCode())) {
            return recommendLocationQueryBL.getLocationByRoute(outLocationQueryDTO);
        }
        List<LocationRulePO> locationRulePOList = locationRuleMapper.listLocationByRuleId(
            outLocationQueryDTO.getWarehouseId(), outLocationQueryDTO.getRuleIdList(), outLocationQueryDTO.getCode());
        if (CollectionUtils.isEmpty(locationRulePOList)) {
            return Collections.emptyList();
        }

        return LocationRuleConvert.convertQueryList(locationRulePOList);
    }

    /**
     * 删除出库位规则
     *
     * @param ids 规则 id
     */
    public void deleteByIds(List<Long> ids) {
        AssertUtils.notEmpty(ids, "主键信息不能为空！");
        locationRuleMapper.deleteByIds(ids);
    }

    /**
     * 查找出库位
     *
     * @param locationRuleQueryDTO
     * @return
     */
    public List<LocationRuleDTO> findLocationRuleByCon(LocationRuleQueryDTO locationRuleQueryDTO) {
        List<LocationRulePO> locationRulePOS =
            locationRuleMapper.listLocationByRuleId(locationRuleQueryDTO.getWarehouseId(),
                locationRuleQueryDTO.getRuleIdList(), locationRuleQueryDTO.getRuleType());
        if (!CollectionUtils.isEmpty(locationRulePOS)) {
            return LocationRuleConvert.convertQueryList(locationRulePOS);
        }

        List<WarehouseConfigDTO> warehouseConfigDTOList = warehouseConfigService
            .listWarehouseDefaultLocation(Collections.singletonList(locationRuleQueryDTO.getWarehouseId()));
        if (CollectionUtils.isEmpty(warehouseConfigDTOList)) {
            return Collections.emptyList();
        }
        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigDTOList.stream().findFirst().get();

        LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
        locationRuleDTO.setLocationId(warehouseConfigDTO.getDefaultLocationId());
        locationRuleDTO.setLocationName(warehouseConfigDTO.getDefaultLocationName());
        return Collections.singletonList(locationRuleDTO);
    }

    /**
     * 删除出库位规则
     *
     * @param locationIds 货位id列表
     */
    public void deleteByLocationIds(List<Long> locationIds, Integer warehouseId) {
        AssertUtils.notEmpty(locationIds, "货位信息不能为空！");
        List<LocationRulePO> locationRulePOS =
            locationRuleMapper.listRuleInfoByLocationId(warehouseId, locationIds, null);
        if (CollectionUtils.isEmpty(locationRulePOS)) {
            return;
        }

        List<Long> ids = locationRulePOS.stream().map(LocationRulePO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        locationRuleMapper.deleteByIds(ids);
    }

}
