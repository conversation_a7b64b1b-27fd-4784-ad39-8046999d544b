package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationAreaBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.LocationAreaBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.LocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.OccupyElementBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.Warehouse2DModelingConverter;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.GridPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPointInfoPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.GridPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AddLocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AddLocationAreaResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.StrairsCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DModelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DSaveAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DSaveLocationGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.Warehouse2DModelType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-03-21 10:09
 **/
@Service
public class Warehouse2DModelingBL {

    public static final String MQ_SUPPLYCHAIN_WAREHOUSEPRODUCT_SAVE_LOCATION_CONFIG = "mq.supplychain.warehouseproduct.saveLocationConfig";
    @Resource
    private LocationConfigPOMapper locationConfigPOMapper;

    @Resource
    private GridPOMapper gridPOMapper;

    @Resource
    private LocationPointInfoPOMapper locationPointInfoPOMapper;

    @Resource
    private Warehouse2DModelingConverter warehouse2DModelingConverter;

    @Resource
    private LocationAreaBL locationAreaBL;

    @Resource
    private LocationBL locationBL;

    @Reference
    private IStoreWareHouseService storeWareHouseService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    private static final Logger logger = LoggerFactory.getLogger(Warehouse2DModelingBL.class);


    public Warehouse2DModelDTO queryLocationConfig(Integer warehouseId, Integer configType) {
        LocationConfigPO config = locationConfigPOMapper.selectByWarehouseIdAndConfigType(warehouseId, configType);
        return warehouse2DModelingConverter.toDTO(config);
    }


    public PageList<LocationAreaReturnDTO> getUnboundLocationArea(LocationAreaDTO locationAreaDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getWarehouseId(), "仓库id为null");
        if (locationAreaDTO.getPageNum() == null || locationAreaDTO.getPageNum() < 0) {
            locationAreaDTO.setPageNum(1);
        }
        if (locationAreaDTO.getPageSize() == null || locationAreaDTO.getPageNum() < 0) {
            locationAreaDTO.setPageSize(20);
        }
        StopWatch stopWatch = new StopWatch("查询货区");
        stopWatch.start();
        List<Long> areaIds = locationPointInfoPOMapper.findLocationAreaIds(locationAreaDTO.getWarehouseId());
        if (CollectionUtils.isNotEmpty(areaIds)) {
            locationAreaDTO.setExcludeIds(areaIds);
        }
        PageList<LocationAreaReturnDTO> result = locationAreaBL.getLocationArea(locationAreaDTO);
        stopWatch.stop();
        logger.info("查询货区耗时统计: {}", stopWatch.prettyPrint());
        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    public AddLocationAreaResultDTO createWarehouseLocationArea(AddLocationAreaDTO addLocationAreaDTO) {
        logger.info("新增货区,{}", JSON.toJSONString(addLocationAreaDTO));
        Byte locationType = getDefaultLocatonTypeByAreaType(addLocationAreaDTO.getAreaType());
        if (locationType == null) {
            throw new BusinessValidateException(String.format("不支持的货区类型, 货区名称=%s 货区类型=%s", addLocationAreaDTO.getAreaName(), addLocationAreaDTO.getAreaType()));
        }
        if (addLocationAreaDTO.getOptUserId() == null) {
            addLocationAreaDTO.setOptUserId(1);
        }
        String areaName = addLocationAreaDTO.getAreaName();
        Integer warehouseId = addLocationAreaDTO.getWarehouseId();
        WareHouseDTO warehouse = storeWareHouseService.findWareHouseById(warehouseId);
        Integer cityId = warehouse.getCityId();
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setWarehouseId(warehouseId);
        locationAreaDTO.setCityId(cityId);
        locationAreaDTO.setArea(addLocationAreaDTO.getAreaName());
        locationAreaDTO.setSubcategory(addLocationAreaDTO.getAreaType());
        locationAreaDTO.setRemo(LocationAreaEnum.getEnumStr(addLocationAreaDTO.getAreaType()));
        locationAreaDTO.setState(LocationStateEnum.启用.getType());
        locationAreaDTO.setLocationCapacity(9999);
        locationAreaDTO.setUserId(addLocationAreaDTO.getOptUserId());
        locationAreaBL.addLocationArea(locationAreaDTO);
        LocationAreaPO locationArea = locationAreaBL.getLocationAreaByName(cityId, warehouseId, areaName);
        if (locationArea == null) {
            throw new BusinessValidateException("库存新增异常");
        }
        List<LoactionDTO> loactions = buildLocations(addLocationAreaDTO, locationArea, locationType);
        locationBL.batchInsertLocation(loactions, warehouseId, cityId);
        List<LoactionDTO> locations = locationBL.findLocationListByAreaId(locationArea.getId());

        AddLocationAreaResultDTO resultDTO = new AddLocationAreaResultDTO();
        resultDTO.setCityId(cityId);
        resultDTO.setWarehouseId(warehouseId);
        resultDTO.setAreaId(locationArea.getId());
        resultDTO.setName(locationArea.getName());
        resultDTO.setLocations(locations);
        return resultDTO;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateLocationConfig(Warehouse2DModelDTO dto) {
        checkWarehouseModel(dto);
        //更新配置信息
        saveLocationConfig(dto);
        //更新货位信息
        if(BooleanUtils.isNotFalse(dto.getNeedSaveLocation())){
            Integer warehouseId = dto.getRefId().intValue();
            Integer warehouseConfigType = dto.getWarehouseConfigType();
            // 仓库其它所有分仓的楼层数据
            LocationConfigPO locationConfigPO = locationConfigPOMapper.selectByWarehouseIdAndConfigType(warehouseId, warehouseConfigType);
            Warehouse2DModelDTO warehouse2DModel = JSON.parseObject(locationConfigPO.getLocationConfig(), Warehouse2DModelDTO.class);
            rabbitTemplate.convertAndSend("", MQ_SUPPLYCHAIN_WAREHOUSEPRODUCT_SAVE_LOCATION_CONFIG, warehouse2DModel);
        }
    }


    public void checkWarehouseModel(Warehouse2DModelDTO dto) {
        List<LocationAreaBO> areaBOs = getAreasByWarehouseModel(dto, false, null);
        // 按areaName分组
        Map<String, List<LocationAreaBO>> nameCountMap = areaBOs.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(LocationAreaBO::getAreaName));
        // 找出出现次数大于1的areaName
        List<String> areaNames = nameCountMap.values().stream()
                .filter(locationAreaBOS -> locationAreaBOS.size() > 1)
                .map(areas -> areas.stream().map(area -> area.getFloor() + ":" + area.getAreaName()).collect(Collectors.joining(",")))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(areaNames)) {
            throw new BusinessValidateException("仓库存在重复货区，请检查异常货区: " + areaNames);
        }
    }


    @Transactional(rollbackFor = Throwable.class)
    public void updateLocationArea(Warehouse2DSaveAreaDTO areaDTO) {
        logger.info("修改货区: {}", JSON.toJSONString(areaDTO));
        String areaName = areaDTO.getAreaName();
        Integer warehouseId = areaDTO.getWarehouseId();
        WareHouseDTO warehouse = storeWareHouseService.findWareHouseById(warehouseId);
        Integer cityId = warehouse.getCityId();
        LocationAreaPO locationArea = locationAreaBL.getLocationAreaByName(cityId, warehouseId, areaName);
        if (locationArea == null) {
            throw new BusinessValidateException("库区不存在，" + areaName);
        }
        List<LoactionDTO> oldLocationList = locationBL.findLocationListByAreaId(locationArea.getId());
        if (BooleanUtils.isTrue(areaDTO.getClear())) {
            for (LoactionDTO oldLoaction : oldLocationList) {
                if (Objects.equals(oldLoaction.getState(), LocationStateEnum.停用.getType())) {
                    continue;
                }
                LoactionDTO loactionDTO = new LoactionDTO();
                loactionDTO.setId(oldLoaction.getId());
                loactionDTO.setState(LocationStateEnum.停用.getType());
                locationBL.updateLocation(loactionDTO);
            }
            LoactionDTO loactionDTO = new LoactionDTO();
            loactionDTO.setId(locationArea.getId());
            loactionDTO.setState(LocationStateEnum.停用.getType());
            locationBL.updateLocation(loactionDTO);
            return;
        }
        LoactionDTO loactionDTO = oldLocationList.get(0);
        String name = loactionDTO.getName();
        String[] split = name.split("-");
        if (split.length > 3) {
            return;
        }
        List<LocationBO> newLocationsBOs = buildLocationBOs(areaDTO, locationArea, oldLocationList);
        saveLocations(newLocationsBOs, oldLocationList, warehouseId);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void updateLocationShelve(Warehouse2DSaveLocationGroupDTO saveLocationGroupDTO) {
        logger.info("修改货架: {}", JSON.toJSONString(saveLocationGroupDTO));
        String areaName = saveLocationGroupDTO.getAreaName();
        Integer warehouseId = saveLocationGroupDTO.getWarehouseId();
        WareHouseDTO warehouse = storeWareHouseService.findWareHouseById(warehouseId);
        Integer cityId = warehouse.getCityId();
        LocationAreaPO locationArea = locationAreaBL.getLocationAreaByName(cityId, warehouseId, areaName);
        if (locationArea == null) {
            throw new BusinessValidateException("库区不存在，" + areaName);
        }
        List<LoactionDTO> locationList = locationBL.findLocationListByAreaId(locationArea.getId());
        List<LoactionDTO> oldShelveGroupLocations = locationList.stream().filter(it -> buildKey(it.getName()).contains(buildKey(saveLocationGroupDTO.getShelveGroupName()))).collect(Collectors.toList());
        if (BooleanUtils.isTrue(saveLocationGroupDTO.getClear())) {
            for (LoactionDTO oldLoaction : oldShelveGroupLocations) {
                if (Objects.equals(oldLoaction.getState(), LocationStateEnum.停用.getType())) {
                    continue;
                }
                LoactionDTO loactionDTO = new LoactionDTO();
                loactionDTO.setId(oldLoaction.getId());
                loactionDTO.setState(LocationStateEnum.停用.getType());
                locationBL.updateLocation(loactionDTO);
            }
            return;
        }
        LoactionDTO loactionDTO = locationList.get(0);
        String name = loactionDTO.getName();
        String[] split = name.split("-");
        if (split.length > 3) {
            return;
        }
        if (BooleanUtils.isTrue(saveLocationGroupDTO.getApplyAll())) {
            Warehouse2DSaveAreaDTO areaDTO = new Warehouse2DSaveAreaDTO();
            areaDTO.setWarehouseId(warehouseId);
            areaDTO.setAreaType(locationArea.getSubcategory().intValue());
            areaDTO.setAreaName(locationArea.getName());
            areaDTO.setShelveGroupNumber(saveLocationGroupDTO.getShelveGroupNumber());
            areaDTO.setLayerNumber(saveLocationGroupDTO.getLayerNumber());
            areaDTO.setShelveCount(saveLocationGroupDTO.getShelveCount());
            areaDTO.setShelveLocationCount(saveLocationGroupDTO.getShelveLocationCount());
            areaDTO.setClear(false);
            updateLocationArea(areaDTO);
            return;
        }
        List<LocationBO> newLocationsBOs = buildLocationBOs(saveLocationGroupDTO, locationArea, oldShelveGroupLocations);
        saveLocations(newLocationsBOs, oldShelveGroupLocations, warehouseId);
    }

    public Boolean checkStairsExists(StrairsCheckDTO strairsPoint) {
        logger.info("校验楼梯是否重复: {}", JSON.toJSONString(strairsPoint));
        LocationConfigPO locationConfigPO = locationConfigPOMapper.selectByWarehouseIdAndConfigType(strairsPoint.getWarehouseId(), strairsPoint.getConfigType());
        if (locationConfigPO == null || locationConfigPO.getLocationConfig() == null) {
            return true;
        }
        Warehouse2DModelDTO warehouse2DModel = JSON.parseObject(locationConfigPO.getLocationConfig(), Warehouse2DModelDTO.class);
        Warehouse2DModelDTO floorModel = warehouse2DModel.getElements().stream().filter(it -> Objects.equals(it.getRefId().intValue(), strairsPoint.getFloor())).findFirst().orElse(null);
        if (floorModel == null) {
            return true;
        }
        List<OccupyElementBO> occupyElements = getOccupyElements(floorModel.getConfig());
        if (CollectionUtils.isEmpty(occupyElements)) {
            return true;
        }
        List<OccupyElementBO> upstairs = occupyElements.stream().filter(it -> BooleanUtils.isTrue(it.getMarkUpstairsPoint()) && Objects.equals(it.getRefName(), strairsPoint.getName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(upstairs)) {
            throw new BusinessValidateException(String.format("%s楼存在重复上楼点【%s】", strairsPoint.getFloor(), strairsPoint.getName()));
        }
        return true;
    }


    private List<OccupyElementBO> getOccupyElements(Map<String, Object> config){
        if(MapUtils.isEmpty(config)){
            return Collections.emptyList();
        }
        Object occupyElements = config.get("occupyElements");
        if (Objects.isNull(occupyElements)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(JSON.toJSONString(occupyElements), OccupyElementBO.class);
    }


    private void saveLocationConfig(Warehouse2DModelDTO dto) {
        LocationConfigPO oldLocationConfig = locationConfigPOMapper.selectByWarehouseIdAndConfigType(dto.getRefId().intValue(), dto.getWarehouseConfigType());
        LocationConfigPO newLocationConfig = warehouse2DModelingConverter.toPO(dto);
        if (oldLocationConfig != null) {
            newLocationConfig.setId(oldLocationConfig.getId());
            locationConfigPOMapper.updateByPrimaryKeySelective(newLocationConfig);
        } else {
            newLocationConfig.setId(UUIDGenerator.getUUID(LocationConfigPO.class.getName()));
            locationConfigPOMapper.insertSelective(newLocationConfig);
        }
    }


    @Transactional(rollbackFor = Throwable.class)
    @RabbitListener(queues = MQ_SUPPLYCHAIN_WAREHOUSEPRODUCT_SAVE_LOCATION_CONFIG)
    public void saveWarehouseLocationInfo(Warehouse2DModelDTO warehouse2DModel) {
        if (warehouse2DModel.getUserId() == null) {
            warehouse2DModel.setUserId(-1);
        }
        // 仓库其它所有分仓的楼层数据
        saveGridInfo(warehouse2DModel);
        //保存货位坐标点
        saveLocationInfo(warehouse2DModel);
    }


    private void saveLocationInfo(Warehouse2DModelDTO warehouse2DModel) {
        Integer warehouseId = warehouse2DModel.getRefId().intValue();
        BigDecimal gridScale = getGridScale(warehouse2DModel);
        // 获取平面图所有货区信息
        List<LocationAreaBO> areaBOs = getAreasByWarehouseModel(warehouse2DModel, true, gridScale);
        List<LocationBO> locationBOs = new ArrayList<>();
        // 获取平面图货位对应仓库货位id，如果平面图存在，货位数据不存在则新增。平面图不存在，货位数据存在，则停用
        if (CollectionUtils.isNotEmpty(areaBOs)) {
            locationBOs = getAreaLocations(areaBOs, warehouseId);
        }
        // 保存货主坐标点信息
        if (CollectionUtils.isNotEmpty(locationBOs)) {
            saveLocationPoints(locationBOs, warehouseId);
        }
        deleteNotBindArea(warehouseId, areaBOs);
    }

    private void deleteNotBindArea(Integer warehouseId, List<LocationAreaBO> areaBOs) {
        List<LocationPO> bindArea = locationPointInfoPOMapper.findBindLocationAreaByWarehouseId(warehouseId);
        if (CollectionUtils.isEmpty(bindArea)) {
            return;
        }
        List<Long> deleteAreaIds = new ArrayList<>();
        List<String> areaNames = areaBOs.stream().map(LocationAreaBO::getAreaName).collect(Collectors.toList());
        for (LocationPO location : bindArea) {
            if (!areaNames.contains(location.getName())) {
                deleteAreaIds.add(location.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(deleteAreaIds)) {
            List<LocationPointInfoPO> locationsPoints = locationPointInfoPOMapper.findBindLocationIdByAreaId(deleteAreaIds, warehouseId);
            Lists.partition(locationsPoints, 1000).forEach(lists -> {
                List<Long> ids = lists.stream().map(LocationPointInfoPO::getId).collect(Collectors.toList());
                locationPointInfoPOMapper.deleteByPrimaryKeyIn(ids);
            });
        }
    }

    private void saveGridInfo(Warehouse2DModelDTO warehouseModel) {
        Integer warehouseId = warehouseModel.getRefId().intValue();
        List<LocationConfigPO> locationConfigPOs = locationConfigPOMapper.selectByWarehouseId(warehouseId);
        Map<Long, List<Warehouse2DModelDTO>> floorMap = locationConfigPOs
                .stream().map(LocationConfigPO::getLocationConfig)
                .map(it -> JSON.parseObject(it, Warehouse2DModelDTO.class))
                .filter(it -> !Objects.equals(warehouseModel.getWarehouseConfigType(), it.getWarehouseConfigType()))
                .map(Warehouse2DModelDTO::getElements).filter(Objects::nonNull).flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Warehouse2DModelDTO::getRefId));

        List<GridPO> saveGrids = new ArrayList<>();
        //遍历楼层
        for (Warehouse2DModelDTO floorModel : warehouseModel.getElements()) {
            // 保存网格数据
            List<GridPO> grids = saveGrid(warehouseModel, floorModel, floorMap.get(floorModel.getRefId()));
            if (CollectionUtils.isNotEmpty(grids)) {
                saveGrids.addAll(grids);
            }
        }
        gridPOMapper.deleteByWarehouseId(warehouseId);
        if (CollectionUtils.isNotEmpty(saveGrids)) {
            Lists.partition(saveGrids, 1000).forEach(gridPOMapper::batchInsert);
        }
    }


    private List<LocationAreaBO> getAreasByWarehouseModel(Warehouse2DModelDTO warehouse2DModel, Boolean needLocationInfo, BigDecimal gridScale) {
        List<LocationAreaBO> areaBOs = new ArrayList<>();
        for (Warehouse2DModelDTO floorModel : warehouse2DModel.getElements()) {
            for (Warehouse2DModelDTO locationArea2DModel : floorModel.getElements()) {
                if (Objects.equals(Warehouse2DModelType.LOCATION_AREA.getValue(), locationArea2DModel.getType())) {
                    if (Objects.equals(locationArea2DModel.getType(), Warehouse2DModelType.LOCATION_AREA.getValue())) {
                        LocationAreaBO areaBO = new LocationAreaBO();
                        areaBO.setAreaName(locationArea2DModel.getRefName());
                        areaBO.setFloor(floorModel.getRefId().intValue());
                        if (BooleanUtils.isTrue(needLocationInfo)) {
                            List<LocationBO> locationBOS = getAreaLocationBO(locationArea2DModel, gridScale);
                            areaBO.setLocations(locationBOS);
                        }
                        areaBOs.add(areaBO);
                    }
                }
            }
        }
        return areaBOs;
    }

    /**
     * 保存网格数据
     *
     * @param warehouseModel   仓库配置
     * @param floorModel       楼层配置
     * @param otherFloorModels 其它分仓的当前楼层的数据
     */
    private List<GridPO> saveGrid(Warehouse2DModelDTO warehouseModel, Warehouse2DModelDTO floorModel, List<Warehouse2DModelDTO> otherFloorModels) {
        // 仓库坐标
        List<BigDecimal> coordinates = floorModel.getCoordinates();
        BigDecimal gridScale = getGridScale(warehouseModel);
        Integer floor = floorModel.getRefId().intValue();
        Integer userId = warehouseModel.getUserId();
        Integer xs = coordinates.get(0).intValue();
        Integer ys = coordinates.get(1).intValue();
        BigDecimal xe = coordinates.get(2).multiply(gridScale).add(coordinates.get(0));
        BigDecimal ye = coordinates.get(3).multiply(gridScale).add(coordinates.get(1));
        // 遍历出当前楼层所有的元素
        List<List<BigDecimal>> shelveCoordinates = getShelveCoordinatesByFloorModel(floorModel);
        // 遍历当前楼层其它封藏的所有元素
        if (CollectionUtils.isNotEmpty(otherFloorModels)) {
            for (Warehouse2DModelDTO otherFloorModel : otherFloorModels) {
                List<List<BigDecimal>> otherShelveCoordinates = getShelveCoordinatesByFloorModel(otherFloorModel);
                if (CollectionUtils.isNotEmpty(otherShelveCoordinates)) {
                    shelveCoordinates.addAll(otherShelveCoordinates);
                }
            }
        }
        List<GridPO> grids = new ArrayList<>();
        // 生成仓库网格
        for (int i = xs; i <= xe.intValue(); i++) {
            for (int j = ys; j <= ye.intValue(); j++) {
                Warehouse2DModelDTO grid = new Warehouse2DModelDTO();
                grid.setCoordinates(Arrays.asList(BigDecimal.valueOf(i), BigDecimal.valueOf(j)));
                grid.setRefId(warehouseModel.getRefId());
                // 若当前网格与当前楼层内其它物品有交集则认为 不可行走
                boolean walkable = !isIntersecting(BigDecimal.valueOf(i), BigDecimal.valueOf(j), shelveCoordinates, gridScale);
                grids.add(warehouse2DModelingConverter.toGridPO(grid, floor, userId, walkable));
            }
        }
        return grids;
    }


    private List<List<BigDecimal>> getOccupyCoordinates(Warehouse2DModelDTO floorModel) {
        List<List<BigDecimal>> coordinates = new ArrayList<>();
        if (floorModel.getConfig() == null) {
            return coordinates;
        }
        Object occupyElements = floorModel.getConfig().get("occupyElements");
        if (Objects.isNull(occupyElements)) {
            return coordinates;
        }
        List<OccupyElementBO> occupyElementBOS = JSON.parseArray(JSON.toJSONString(occupyElements), OccupyElementBO.class);
        if (CollectionUtils.isEmpty(occupyElementBOS)) {
            return coordinates;
        }
        return occupyElementBOS.stream().filter(it -> BooleanUtils.isNotTrue(it.getMarkUpstairsPoint()) && Objects.equals(it.getType(), Warehouse2DModelType.OCCUPY_ELEMENT.getValue()) && CollectionUtils.isNotEmpty(it.getCoordinates()))
                .map(OccupyElementBO::getCoordinates).collect(Collectors.toList());
    }

    private List<List<BigDecimal>> getShelveCoordinatesByFloorModel(Warehouse2DModelDTO floorModel) {
        List<List<BigDecimal>> occupyCoordinates = getOccupyCoordinates(floorModel);
        for (Warehouse2DModelDTO areaModel : floorModel.getElements()) {
            if (areaModel == null || !Objects.equals(Warehouse2DModelType.LOCATION_AREA.getValue(), areaModel.getType())) {
                continue;
            }
            Map<String, Object> areaConfig = areaModel.getConfig();
            if (areaConfig != null) {
                Object areaTypeObj = areaConfig.get("areaType");
                if (areaTypeObj != null) {
                    Integer areaType = Integer.valueOf(areaTypeObj.toString());
                    if (Objects.equals(areaType, LocationAreaEnum.周转区.getType())
                            || Objects.equals(areaType, LocationAreaEnum.集货区.getType())) {
                        continue;
                    }
                }
            }
            List<BigDecimal> areaCoordinates = areaModel.getCoordinates();
            for (Warehouse2DModelDTO shelveGroup : areaModel.getElements()) {
                if (shelveGroup == null || CollectionUtils.isEmpty(shelveGroup.getElements())
                        || !Objects.equals(Warehouse2DModelType.SHELVE_GROUP.getValue(), shelveGroup.getType())) {
                    continue;
                }
                List<BigDecimal> shelveGroupCoordinates = shelveGroup.getCoordinates();
                for (Warehouse2DModelDTO shelve : shelveGroup.getElements()) {
                    if (shelve == null || !Objects.equals(Warehouse2DModelType.SHELVE.getValue(), shelve.getType())) {
                        continue;
                    }
                    List<BigDecimal> shelveCoordinates = shelve.getCoordinates();
                    BigDecimal x = areaCoordinates.get(0).add(shelveGroupCoordinates.get(0)).add(shelveCoordinates.get(0));
                    BigDecimal y = areaCoordinates.get(1).add(shelveGroupCoordinates.get(1)).add(shelveCoordinates.get(1));
                    occupyCoordinates.add(Arrays.asList(x, y, shelveCoordinates.get(2), shelveCoordinates.get(3)));
                }
            }
        }
        return occupyCoordinates;
    }


    private void saveLocationPoints(List<LocationBO> locationBOs, Integer warehouseId) {
        Map<Integer, List<LocationBO>> floorLocationMap = locationBOs.stream().collect(Collectors.groupingBy(LocationBO::getFloor));
        for (Map.Entry<Integer, List<LocationBO>> floorEntry : floorLocationMap.entrySet()) {
            Integer floor = floorEntry.getKey();
            List<GridPO> floorGrid = gridPOMapper.selectByWarehouseIdAndFloor(warehouseId, floor);
            Map<String, Boolean> gridMap = floorGrid.stream().collect(Collectors.toMap(it -> it.getX() + "_" + it.getY(), GridPO::getIsWalkable, (t1, t2) -> t1));

            List<LocationBO> floorLocations = floorEntry.getValue();
            Map<Long, List<LocationBO>> areaLocationMap = floorLocations.stream().collect(Collectors.groupingBy(LocationBO::getAreaId));
            for (Map.Entry<Long, List<LocationBO>> entry : areaLocationMap.entrySet()) {
                Long areaId = entry.getKey();
                List<LocationBO> newLocationBOs = entry.getValue();
                List<LocationPointInfoPO> newPointList = newLocationBOs.stream().map(locationBO -> {
                    List<BigDecimal> locationCoordinates = locationBO.getCoordinates();
                    String[] split = locationBO.getLocationName().split("-");
                    LocationPointInfoPO locationPointInfoPO = new LocationPointInfoPO();
                    locationPointInfoPO.setWarehouseId(warehouseId);
                    locationPointInfoPO.setLocationId(locationBO.getLocationId());
                    BigDecimal x = locationCoordinates.get(0);
                    BigDecimal y = locationCoordinates.get(1);
                    // 出库区坐标 取中间点
                    if (Objects.equals(locationBO.getAreaType(), LocationAreaEnum.周转区.getType().byteValue())) {
                        locationPointInfoPO.setX(x.intValue());
                        locationPointInfoPO.setY(y.intValue());
                    } else {
                        List<Integer> locationPoint = getLocationPoint(x.intValue(), y.intValue(), locationBO.getEndLength().intValue(), gridMap, locationBO.getHorizontalLonger());
                        if(CollectionUtils.isEmpty(locationPoint)){
                            logger.warn("异常货位={}, 仓库id={}", JSON.toJSONString(locationBO), warehouseId);
                            return null;
                        }
                        locationPointInfoPO.setX(locationPoint.get(0));
                        locationPointInfoPO.setY(locationPoint.get(1));
                    }
                    locationPointInfoPO.setFloor(floor);
                    locationPointInfoPO.setCreateTime(new Date());
                    locationPointInfoPO.setCreateUser(1);
                    locationPointInfoPO.setLastUpdateTime(new Date());
                    locationPointInfoPO.setLastUpdateUser(1);
                    return locationPointInfoPO;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                //填充例外点
                fullExtPoint(warehouseId, areaId, newPointList);
                // 仓库所有的货位坐标信息
                List<LocationPointInfoPO> oldLocationPointInfos = locationPointInfoPOMapper.findBindLocationIdByAreaId(Collections.singletonList(areaId), warehouseId);
                Map<Long, List<LocationPointInfoPO>> oldLocationMap = oldLocationPointInfos.stream().collect(Collectors.groupingBy(LocationPointInfoPO::getLocationId));

                Map<Long, LocationPointInfoPO> oldPointMap = oldLocationPointInfos.stream()
                        .collect(Collectors.toMap(LocationPointInfoPO::getLocationId, it -> it, (t1, t2) -> t1));
                List<LocationPointInfoPO> insertPointList = new ArrayList<>();
                List<LocationPointInfoPO> updatePointList = new ArrayList<>();
                // 删除无效绑定坐标点
                List<Long> deleteLocationPointIds = new ArrayList<>();
                for (LocationPointInfoPO newPointInfoPO : newPointList) {
                    if (newPointInfoPO == null) {
                        continue;
                    }
                    List<LocationPointInfoPO> oldPoints = oldLocationMap.get(newPointInfoPO.getLocationId());
                    if (CollectionUtils.isNotEmpty(oldPoints)) {
                        LocationPointInfoPO oldPoint = oldPoints.get(0);
                        if(oldPoints.size() > 1){
                            List<Long> errorIds = oldPoints.stream().map(LocationPointInfoPO::getId).filter(id -> !Objects.equals(id, oldPoint.getId())).collect(Collectors.toList());
                            deleteLocationPointIds.addAll(errorIds);
                        }
                        if (!Objects.equals(oldPoint.getX(), newPointInfoPO.getX()) || !Objects.equals(oldPoint.getY(), newPointInfoPO.getY())
                                || !Objects.equals(oldPoint.getFloor(), newPointInfoPO.getFloor())) {
                            newPointInfoPO.setId(oldPoint.getId());
                            updatePointList.add(newPointInfoPO);
                        }
                    } else {
                        newPointInfoPO.setId(UUIDGenerator.getUUID(LocationPointInfoPO.class.getName()));
                        insertPointList.add(newPointInfoPO);
                    }
                }


                Map<Long, LocationPointInfoPO> newPointMap = newPointList.stream().collect(Collectors.toMap(LocationPointInfoPO::getLocationId, it -> it, (t1, t2) -> t1));
                for (LocationPointInfoPO oldLocationPoint : oldLocationPointInfos) {
                    if (newPointMap.get(oldLocationPoint.getLocationId()) == null) {
                        deleteLocationPointIds.add(oldLocationPoint.getId());
                    }
                }
                List<Long> otherFloorLocationIds = oldLocationPointInfos.stream().filter(it -> !Objects.equals(it.getFloor(), floor)).map(it -> it.getId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherFloorLocationIds)) {
                    deleteLocationPointIds.addAll(otherFloorLocationIds);
                }
                if (CollectionUtils.isNotEmpty(deleteLocationPointIds)) {
                    Lists.partition(deleteLocationPointIds, 1000).forEach(ids -> locationPointInfoPOMapper.deleteByPrimaryKeyIn(ids));
                }
                if (CollectionUtils.isNotEmpty(insertPointList)) {
                    Lists.partition(insertPointList, 1000).forEach(insertList -> locationPointInfoPOMapper.batchInsert(insertList));
                }
                if (CollectionUtils.isNotEmpty(updatePointList)) {
                    for (LocationPointInfoPO locationPointInfoPO : updatePointList) {
                        locationPointInfoPOMapper.updateByPrimaryKeySelective(locationPointInfoPO);
                    }
                }
            }
        }
    }

    private void fullExtPoint(Integer warehoueId, Long areaId, List<LocationPointInfoPO> newPointList) {
        if (!Objects.equals(warehoueId, 1081)) {
            return;
        }
        if (Objects.equals(areaId, 5395046881334472391L)) {
            //5433367855855649894	H1-02-119	60,9
            //5433367855855649895	H1-02-120	60,10
            //5443566889058158157	H1-02-121	60,11
            //5443566889058158158	H1-02-122	60,12
            //5443566889058158159	H1-02-123	60,13
            //5443566889058158161	H1-02-125	60,15
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5433367855855649894L, 60, 9, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5433367855855649895L, 60, 10, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5443566889058158157L, 60, 11, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5443566889058158158L, 60, 12, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5443566889058158159L, 60, 13, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5443566889058158161L, 60, 15, 1));
        }
        if (Objects.equals(areaId, 5308061742528082667L)) {
            //5439524308443750765	NA1-02-35F  38,0
            //5439524308443750766	NA1-03-35F	38,0
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5439524308443750765L, 38, 0, 1));
            newPointList.add(buildLocationPointInfoPO(warehoueId, 5439524308443750766L, 38, 0, 1));
        }
    }

    private LocationPointInfoPO buildLocationPointInfoPO(Integer warehouseId, Long locationId, Integer x, Integer y, Integer floor) {
        LocationPointInfoPO point = new LocationPointInfoPO();
        point.setWarehouseId(warehouseId);
        point.setLocationId(locationId);
        point.setX(x);
        point.setY(y);
        point.setFloor(floor);
        point.setCreateTime(new Date());
        point.setCreateUser(1);
        point.setLastUpdateTime(new Date());
        point.setLastUpdateUser(1);
        return point;
    }

    private List<Integer> getLocationPoint(Integer x, Integer y, Integer endLength, Map<String, Boolean> gridMap, boolean isHorizontalLonger) {
        List<Integer> accessPoints = new ArrayList<>();
        List<Integer> params = Arrays.asList(-1, endLength);
        if (!isHorizontalLonger) {
            // 只检查左右方向
            for (Integer param : params) {
                if (checkIsWalk(x + param, y, gridMap)) {
                    accessPoints.add(x + param);
                    accessPoints.add(y);
                }
            }
        } else {
            // 只检查上下方向
            for (Integer param : params) {
                if (checkIsWalk(x, y + param, gridMap)) {
                    accessPoints.add(x);
                    accessPoints.add(y + param);
                }
            }
        }
        return accessPoints;
    }

    /**
     * 校验是否可以通行
     * @param x
     * @param y
     * @param gridMap
     * @return
     */
    private Boolean checkIsWalk(Integer x, Integer y, Map<String, Boolean> gridMap) {
        if (x < 0 || y < 0) {
            return false;
        }
        String key = x + "_" + y;
        return gridMap.containsKey(key) && gridMap.get(key);
    }


    private List<LocationBO> getAreaLocationBO(Warehouse2DModelDTO locationArea2DModel, BigDecimal gridScale) {

        List<BigDecimal> areaCoordinates = locationArea2DModel.getCoordinates();
        List<LocationBO> locationBOS = new ArrayList<>();
        for (Warehouse2DModelDTO shelveGroup : locationArea2DModel.getElements()) {
            if (!Warehouse2DModelType.SHELVE_GROUP.valueEquals(shelveGroup.getType())) {
                continue;
            }
            Map<String, Object> config = shelveGroup.getConfig();
            Integer layNumber = getLayNumber(config);
            //是否左右方向
            Boolean horizontalLonger = getShelveDirection(config);
            List<BigDecimal> shelveGroupCoordinates = shelveGroup.getCoordinates();
            for (Warehouse2DModelDTO shelve : shelveGroup.getElements()) {
                if (!Warehouse2DModelType.SHELVE.valueEquals(shelve.getType())) {
                    continue;
                }
                List<BigDecimal> shelveCoordinates = shelve.getCoordinates();
                for (Warehouse2DModelDTO locationModel : shelve.getElements()) {
                    if (!Warehouse2DModelType.LOCATION.valueEquals(locationModel.getType())) {
                        continue;
                    }
                    List<BigDecimal> locationCoordinates = locationModel.getCoordinates();
                    BigDecimal x = areaCoordinates.get(0).add(shelveGroupCoordinates.get(0)).add(shelveCoordinates.get(0)).add(locationCoordinates.get(0));
                    BigDecimal y = areaCoordinates.get(1).add(shelveGroupCoordinates.get(1)).add(shelveCoordinates.get(1)).add(locationCoordinates.get(1));
                    locationCoordinates.set(0, x);
                    locationCoordinates.set(1, y);
                    String locationName = locationModel.getRefName();
                    List<String> locationNames = getLocationNames(locationName, layNumber);
                    for (String name : locationNames) {
                        LocationBO locationBO = new LocationBO();
                        locationBO.setLocationName(name);
                        locationBO.setCoordinates(locationCoordinates);
                        locationBO.setHorizontalLonger(horizontalLonger);
                        locationBO.setEndLength(horizontalLonger ? shelveCoordinates.get(3).multiply(gridScale) : shelveCoordinates.get(2).multiply(gridScale));
                        locationBOS.add(locationBO);
                    }
                }
            }
        }
        return locationBOS;
    }

    private List<String> getLocationNames(String locationName, Integer layNumber) {
        List<String> locationNames = new ArrayList<>();
        locationNames.add(locationName);
        if (layNumber == null || layNumber == 1) {
            return locationNames;
        }
        String[] splits = locationName.split("-");
        if (splits.length < 3) {
            return locationNames;
        }
        for (int i = 2; i <= layNumber; i++) {
            splits[splits.length - 2] = "0" + i;
            locationNames.add(String.join("-", splits));
        }
        return locationNames;
    }

    private Boolean getShelveDirection(Map<String, Object> config){
        if(config == null) {
            return null;
        }
        Object shelfDirection = config.get("singleShelfDirection");
        Integer shelveDirection = Integer.parseInt(shelfDirection.toString());
        //  {value: 1, label: '从左到右'},
        //  {value: 2, label: '从右到左'},
        //  {value: 3, label: '从上到下'},
        //  {value: 4, label: '从下到上'},
        return Objects.equals(shelveDirection, 1) || Objects.equals(shelveDirection, 2);
    }

    private Integer getLayNumber(Map<String, Object> config){
        if(config == null) {
            return null;
        }
        Object layerNumber = config.get("singleShelfLayerNumber");
        if(Objects.isNull(layerNumber)){
            return 1;
        }
        return Integer.parseInt(layerNumber.toString());
    }


    private List<LocationBO> getAreaLocations(List<LocationAreaBO> areaBOs, Integer warehouseId) {
        List<LocationBO> saveLocactionBOs = new ArrayList<>();
        List<String> areaNames = areaBOs.stream().map(LocationAreaBO::getAreaName).collect(Collectors.toList());
        List<LoactionDTO> locationAreas = locationBL.getLocationByNames(warehouseId, areaNames);
        Map<String, LoactionDTO> areaMap = locationAreas.stream().collect(Collectors.toMap(LoactionDTO::getName, it -> it));
        for (LocationAreaBO areaBO : areaBOs) {
            LoactionDTO area = areaMap.get(areaBO.getAreaName());
            areaBO.setAreaId(area.getId());
            areaBO.getLocations().forEach(it -> {
                it.setAreaId(area.getId());
                it.setAreaName(area.getName());
                it.setFloor(areaBO.getFloor());
                it.setAreaType(area.getSubcategory());
                it.setAreaType(areaBO.getAreaType() == null ? area.getSubcategory() : areaBO.getAreaType().byteValue());
            });
            List<LoactionDTO> oldLocations = locationBL.findLocationListByAreaId(area.getId());
            List<LocationBO> locationBOS = saveLocations(areaBO.getLocations(), oldLocations, warehouseId);
            saveLocactionBOs.addAll(locationBOS);
        }
        return saveLocactionBOs;
    }

    public String buildKey(String locationName) {
        return locationName.replaceAll("(?<=[^0-9])0+(\\d+)", "$1");
    }


    private List<LocationBO> saveLocations(List<LocationBO> newLocationsBOs, List<LoactionDTO> oldLoactions, Integer warehouseId) {
        Map<String, LocationBO> newLocationMap = newLocationsBOs.stream().collect(Collectors.toMap(it -> buildKey(it.getLocationName()), it -> it, (t1, t2) -> t1));
        Map<String, LoactionDTO> oldLocationMap = oldLoactions.stream().collect(Collectors.toMap(it -> buildKey(it.getName()), it -> it, (t1, t2) -> t1));
        List<LocationBO> addLocations = new ArrayList<>();
        List<Long> stopLocationIds = new ArrayList<>();
        List<Long> startLocationIds = new ArrayList<>();
        for (LocationBO newLocation : newLocationsBOs) {
            LoactionDTO oldLocation = oldLocationMap.get(buildKey(newLocation.getLocationName()));
            if (oldLocation != null) {
                newLocation.setLocationId(oldLocation.getId());
                if (Objects.equals(oldLocation.getState(), LocationStateEnum.停用.getType())) {
                    startLocationIds.add(oldLocation.getId());
                }
            } else {
                addLocations.add(newLocation);
            }
        }
        for (LoactionDTO oldLoaction : oldLoactions) {
            LocationBO newLocation = newLocationMap.get(buildKey(oldLoaction.getName()));
            if (newLocation == null) {
                stopLocationIds.add(oldLoaction.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(startLocationIds)) {
            for (Long startLocationId : startLocationIds) {
                LoactionDTO loactionDTO = new LoactionDTO();
                loactionDTO.setId(startLocationId);
                loactionDTO.setState(LocationStateEnum.启用.getType());
                locationBL.updateLocation(loactionDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(stopLocationIds)) {
            for (Long stopId : stopLocationIds) {
                LoactionDTO loactionDTO = new LoactionDTO();
                loactionDTO.setId(stopId);
                loactionDTO.setState(LocationStateEnum.停用.getType());
                locationBL.updateLocation(loactionDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(addLocations)) {
            WareHouseDTO warehouse = storeWareHouseService.findWareHouseById(warehouseId);
            List<LoactionDTO> loactions = new ArrayList<>();
            for (LocationBO addLocation : addLocations) {
                String[] split = addLocation.getLocationName().split("-");
                int length = split.length;
                String productlocation = null;
                if (split.length > 2) {
                    productlocation = split[length - 2] + "-" + split[length - 1];
                } else {
                    productlocation = split[length - 1];
                }
                String roadWay = split[0].replace(addLocation.getAreaName(), "");
                Byte locationType = getDefaultLocatonTypeByAreaType(addLocation.getAreaType());
                LoactionDTO loactionDTO = new LoactionDTO();
                loactionDTO.setArea_Id(addLocation.getAreaId());
                loactionDTO.setName(addLocation.getLocationName());
                loactionDTO.setArea(addLocation.getAreaName());
                loactionDTO.setRoadWay(roadWay);
                loactionDTO.setRoadway(roadWay);
                loactionDTO.setProductlocation(productlocation);
                loactionDTO.setCityId(warehouse.getCityId());
                loactionDTO.setWarehouseId(warehouseId);
                loactionDTO.setUserId(1);
                loactionDTO.setCategory(CategoryEnum.CARGO_LOCATION.getValue().byteValue());
                loactionDTO.setSubcategory(locationType);
                loactionDTO.setSubcategoryName(LocationEnum.getEnumStr(locationType.intValue()));
                loactionDTO.setLocationCapacity(9999);
                loactionDTO.setState(LocationStateEnum.启用.getType());
                loactionDTO.setPalletCount(1);
                loactions.add(loactionDTO);
            }
            locationBL.batchInsertLocation(loactions, warehouseId, warehouse.getCityId());
            List<String> locationnNames = addLocations.stream().map(it -> it.getLocationName()).collect(Collectors.toList());
            List<LoactionDTO> locations = locationBL.getLocationByNames(warehouseId, locationnNames);
            Map<String, LoactionDTO> locationMap = locations.stream().collect(Collectors.toMap(it -> it.getName(), it -> it));
            for (LocationBO newLocationsBO : newLocationsBOs) {
                LoactionDTO loactionDTO = locationMap.get(newLocationsBO.getLocationName());
                if (loactionDTO != null) {
                    newLocationsBO.setLocationId(loactionDTO.getId());
                }
            }
        }
        return newLocationsBOs;
    }


    /**
     * 1米代表几个格子
     *
     * @param warehouseModel
     * @return
     */
    private BigDecimal getGridScale(Warehouse2DModelDTO warehouseModel) {
        Map<String, Object> config = warehouseModel.getConfig();
        if (MapUtils.isEmpty(config)) {
            return BigDecimal.valueOf(2);
        }
        Object gridConfig = config.get("gridConfig");
        if (gridConfig == null) {
            return BigDecimal.valueOf(2);
        }
        Map<String, Object> gridConfigMap = (Map<String, Object>) config.get("gridConfig");
        Object gridValue = gridConfigMap.get("gridValue");
        if (gridValue != null) {
            return Objects.equals(Integer.parseInt(gridValue.toString()), 1) ? BigDecimal.valueOf(2) : BigDecimal.valueOf(1);
        }
        return BigDecimal.valueOf(2);
    }

    private boolean isIntersecting(BigDecimal x, BigDecimal y, List<List<BigDecimal>> planes, BigDecimal gridScale) {
        //规格1 ，0.5米一格。默认0.5一
        //规格2 ，1米一格
        //xEnd，和yEnd，单位是米
        for (List<BigDecimal> plane : planes) {
            BigDecimal xStart = plane.get(0);
            BigDecimal yStart = plane.get(1);
            BigDecimal xEnd = plane.get(2).multiply(gridScale).add(xStart);
            BigDecimal yEnd = plane.get(3).multiply(gridScale).add(yStart);
            if (x.compareTo(xStart) >= 0 && x.compareTo(xEnd) < 0 && y.compareTo(yStart) >= 0 && y.compareTo(yEnd) < 0) {
                return true;
            }
        }
        return false;
    }


    private List<LoactionDTO> buildLocations(AddLocationAreaDTO addLocationAreaDTO, LocationAreaPO locationArea, Byte locationType) {
        Integer shelveCount = addLocationAreaDTO.getShelveCount();
        Integer shelveLocationCount = addLocationAreaDTO.getShelveLocationCount();
        Integer layerNumber = addLocationAreaDTO.getLayerNumber();
        Integer shelveGroupNumber = addLocationAreaDTO.getShelveGroupNumber();
        String areaName = addLocationAreaDTO.getAreaName();
        List<LoactionDTO> loactions = new ArrayList<>();
        for (int roadWayCount = 1; roadWayCount <= shelveGroupNumber; roadWayCount++) {
            for (int layerCount = 1; layerCount <= layerNumber; layerCount++) {
                int locationTotalCount = shelveCount * shelveLocationCount;
                for (int locationCount = 1; locationCount <= locationTotalCount; locationCount++) {
                    String roadWay = String.valueOf(roadWayCount);
                    String productlocation = null;
                    if (Objects.equals(LocationEnum.出库位.getType().byteValue(), locationType)
                            || Objects.equals(LocationEnum.集货位.getType().byteValue(), locationType)) {
                        productlocation = String.valueOf(locationCount);
                    } else {
                        String layer = "0" + layerCount;
                        productlocation = layer + "-" + locationCount;
                    }
                    String locationName = areaName + roadWay + "-" + productlocation;
                    LoactionDTO loactionDTO = new LoactionDTO();
                    loactionDTO.setArea_Id(locationArea.getId());
                    loactionDTO.setName(locationName);
                    loactionDTO.setArea(areaName);
                    loactionDTO.setRoadWay(roadWay);
                    loactionDTO.setRoadway(roadWay);
                    loactionDTO.setProductlocation(productlocation);
                    loactionDTO.setCityId(locationArea.getCity_Id());
                    loactionDTO.setWarehouseId(locationArea.getWarehouse_Id());
                    loactionDTO.setUserId(addLocationAreaDTO.getOptUserId());
                    loactionDTO.setCategory(CategoryEnum.CARGO_LOCATION.getValue().byteValue());
                    loactionDTO.setSubcategory(locationType);
                    loactionDTO.setSubcategoryName(LocationEnum.getEnumStr(locationType.intValue()));
                    loactionDTO.setLocationCapacity(9999);
                    loactionDTO.setState(LocationStateEnum.启用.getType());
                    loactionDTO.setPalletCount(1);
                    loactions.add(loactionDTO);
                }
            }
        }
        return loactions;
    }


    private List<LocationBO> buildLocationBOs(Warehouse2DSaveAreaDTO areaDTO, LocationAreaPO locationArea, List<LoactionDTO> oldLocationList) {
        Integer shelveCount = areaDTO.getShelveCount();
        Integer shelveLocationCount = areaDTO.getShelveLocationCount();
        Integer layerNumber = areaDTO.getLayerNumber();
        Integer shelveGroupNumber = areaDTO.getShelveGroupNumber();
        String areaName = areaDTO.getAreaName();
        List<LocationBO> loactions = new ArrayList<>();
        Byte areaType = locationArea.getSubcategory();
        for (int roadWayCount = 1; roadWayCount <= shelveGroupNumber; roadWayCount++) {
            for (int layerCount = 1; layerCount <= layerNumber; layerCount++) {

                int locationTotalCount = shelveCount * shelveLocationCount;
                String name = areaName + roadWayCount;
                if (!Objects.equals(areaType, LocationAreaEnum.周转区.getType().byteValue())
                        && !Objects.equals(areaType, LocationAreaEnum.集货区.getType().byteValue())) {
                    name = name + "-0" + layerCount;
                }
                String finalName = name;
                List<LoactionDTO> sortLocations = oldLocationList.stream().filter(it -> buildKey(it.getName()).contains(buildKey(finalName)))
                        .sorted((a, b) -> compareLocationNames(a.getName(), b.getName()))
                        .collect(Collectors.toList());
                int oldLocationSize = sortLocations.size();
                String lastLocationName = null;
                for (int i = 0; i < locationTotalCount; i++) {
                    LoactionDTO oldLocation = null;
                    if (i < oldLocationSize) {
                        oldLocation = sortLocations.get(i);

                    }
                    if(oldLocation != null){
                        String locationName = oldLocation.getName();
                        lastLocationName = locationName;
                        LocationBO loactionDTO = new LocationBO();
                        loactionDTO.setAreaType(oldLocation.getSubcategory());
                        loactionDTO.setAreaId(oldLocation.getAreaId());
                        loactionDTO.setAreaName(areaDTO.getAreaName());
                        loactionDTO.setLocationName(locationName);
                        loactions.add(loactionDTO);
                    }else {
                        String locationName = incrementLastNumber(lastLocationName, finalName);
                        LocationBO loactionDTO = new LocationBO();
                        loactionDTO.setAreaType(areaType);
                        loactionDTO.setAreaId(locationArea.getId());
                        loactionDTO.setAreaName(locationArea.getName());
                        loactionDTO.setLocationName(locationName);
                        loactions.add(loactionDTO);
                        lastLocationName = locationName;
                    }
                }
            }
        }
        return loactions;
    }

    private List<LocationBO> buildLocationBOs(Warehouse2DSaveLocationGroupDTO saveLocationGroupDTO, LocationAreaPO locationArea ,List<LoactionDTO> oldShelveGroupLocations) {
        Integer shelveCount = saveLocationGroupDTO.getShelveCount();
        Integer shelveLocationCount = saveLocationGroupDTO.getShelveLocationCount();
        Integer layerNumber = saveLocationGroupDTO.getLayerNumber();
        String shelveGroupName = saveLocationGroupDTO.getShelveGroupName();
        List<LocationBO> loactions = new ArrayList<>();
        Byte areaType = locationArea.getSubcategory();

        for (int layerCount = 1; layerCount <= layerNumber; layerCount++) {
            String name = shelveGroupName;
            if (!Objects.equals(areaType, LocationAreaEnum.周转区.getType().byteValue())
                    && !Objects.equals(areaType, LocationAreaEnum.集货区.getType().byteValue())) {
                name = name + "-0" + layerCount;
            }
            String finalName = name;
            List<LoactionDTO> sortLocations = oldShelveGroupLocations.stream().filter(it -> buildKey(it.getName()).contains(buildKey(finalName) + "-"))
                    .sorted((a, b) -> compareLocationNames(a.getName(), b.getName()))
                    .collect(Collectors.toList());
            int oldLocationSize = sortLocations.size();
            int locationTotalCount = shelveCount * shelveLocationCount;
            String lastLocationName = null;
            for (int i = 0; i < locationTotalCount; i++) {
                LoactionDTO oldLocation = null;
                if (i < oldLocationSize) {
                    oldLocation = sortLocations.get(i);
                }
                if (oldLocation != null) {
                    String locationName = oldLocation.getName();
                    lastLocationName = locationName;
                    LocationBO loactionDTO = new LocationBO();
                    loactionDTO.setAreaType(oldLocation.getSubcategory());
                    loactionDTO.setAreaId(locationArea.getId());
                    loactionDTO.setAreaName(locationArea.getName());
                    loactionDTO.setLocationName(locationName);
                    loactions.add(loactionDTO);
                } else {
                    String locationName = incrementLastNumber(lastLocationName, finalName);
                    LocationBO loactionDTO = new LocationBO();
                    loactionDTO.setAreaType(areaType);
                    loactionDTO.setAreaId(locationArea.getId());
                    loactionDTO.setAreaName(locationArea.getName());
                    loactionDTO.setLocationName(locationName);
                    loactions.add(loactionDTO);
                    lastLocationName = locationName;
                }
            }
        }
        return loactions;
    }


    private Byte getDefaultLocatonTypeByAreaType(Byte areaType) {
        switch (areaType) {
            case 50:
                return LocationEnum.存储位.getType().byteValue();
            case 51:
                return LocationEnum.分拣位.getType().byteValue();
            case 52:
                return LocationEnum.零拣位.getType().byteValue();
            case 53:
                return LocationEnum.出库位.getType().byteValue();
            case 61:
                return LocationEnum.集货位.getType().byteValue();
        }
        return null;
    }


    /**
     * 将货位编号最后一段数字加1,如果不存在则初始化
     * 例如：
     * NA1-01-1 -> NA1-01-2
     * NA1-01-5A -> NA1-01-6
     *
     * @param locationName 原货位编号
     * @return 新的货位编号，如果格式不正确则返回原字符串
     */
    public static String incrementLastNumber(String locationName, String prefixName) {
        if (StringUtils.isEmpty(locationName)) {
            return prefixName + "-1";
        }

        // 使用正则表达式匹配最后一段的数字和可能的字母
        Pattern pattern = Pattern.compile("(.*-)(\\d+)([A-Za-z]*)$");
        Matcher matcher = pattern.matcher(locationName);

        if (matcher.find()) {
            String prefix = matcher.group(1);     // 前缀部分（包括最后一个横杠）
            String number = matcher.group(2);     // 数字部分
            // 数字加1
            int newNumber = Integer.parseInt(number) + 1;
            // 返回新的货位编号
            return prefix + newNumber;
        }
        return locationName;
    }

    // 封装的比较方法
    public static int compareLocationNames(String s1, String s2) {
        Pattern pattern = Pattern.compile("(\\d+)([A-Z]?)");
        String[] parts1 = s1.split("-");
        String[] parts2 = s2.split("-");

        for (int i = 0; i < Math.min(parts1.length, parts2.length); i++) {
            Matcher m1 = pattern.matcher(parts1[i]);
            Matcher m2 = pattern.matcher(parts2[i]);

            if (m1.matches() && m2.matches()) {
                int num1 = Integer.parseInt(m1.group(1));
                int num2 = Integer.parseInt(m2.group(1));
                int cmp = Integer.compare(num1, num2);
                if (cmp != 0) return cmp;

                String suffix1 = m1.group(2);
                String suffix2 = m2.group(2);
                int suffixCmp = suffix1.compareTo(suffix2);
                if (suffixCmp != 0) return suffixCmp;
            } else {
                int cmp = parts1[i].compareTo(parts2[i]);
                if (cmp != 0) return cmp;
            }
        }

        return Integer.compare(parts1.length, parts2.length);
    }

}
