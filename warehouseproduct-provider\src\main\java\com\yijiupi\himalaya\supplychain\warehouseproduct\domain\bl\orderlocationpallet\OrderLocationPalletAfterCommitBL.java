package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.orderlocationpallet;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.postcommit.CommitAfterTransactionAnn;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskManageService;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OrderLocationInfoToTmsDTO;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/24
 */
@Service
public class OrderLocationPalletAfterCommitBL {

    @Reference
    private IBatchTaskManageService iBatchTaskManageService;

    // TODO 不推荐使用，一致性无保障
    @CommitAfterTransactionAnn
    public void sendLocationChange(OrderLocationPalletDTO dto) {
        OrderLocationInfoToTmsDTO orderLocationInfoToTmsDTO = new OrderLocationInfoToTmsDTO();
        orderLocationInfoToTmsDTO.setOrderNoList(dto.getOrderPalletInfoDTOS().stream()
            .map(OrderPalletInfoDTO::getOrderNo).distinct().collect(Collectors.toList()));
        orderLocationInfoToTmsDTO.setOrgId(dto.getOrgId());
        orderLocationInfoToTmsDTO.setWarehouseId(dto.getWarehouseId());
        if (Objects.nonNull(dto.getLastUpdateUser())) {
            orderLocationInfoToTmsDTO.setOptUserId(Integer.valueOf(dto.getLastUpdateUser()));
        }

        iBatchTaskManageService.sendOrderLocationInfoToTms(orderLocationInfoToTmsDTO);
    }

}
