package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.orderlocationpallet;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OrderLocationPalletMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OrderLocationPalletPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.BatchOrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderPalletInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.GlobalCache;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderInfoQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoRelatedOrderResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.MergePickOrderInfoResultDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockOrderDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单货位托盘关系
 *
 * <AUTHOR>
 * @since 2024年8月14日
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class OrderLocationPalletBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderLocationPalletBL.class);

    @Autowired
    private OrderLocationPalletMapper orderLocationPalletMapper;
    @Autowired
    private LocationBL locationBL;
    @Autowired
    private OrderLocationPalletAfterCommitBL orderLocationPalletAfterCommitBL;
    @Autowired
    private GlobalCache globalCache;

    @Reference
    private IBatchManageService batchManageService;
    @Reference
    private IBatchOrderInfoQueryService iBatchOrderInfoQueryService;
    @Reference
    private IVariableValueService iVariableValueService;

    /**
     * 订单货位托盘关系新增
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addPallet(OrderLocationPalletDTO dto) {
        LOGGER.info("添加订单货位托盘关系信息 入参：{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "新增参数不能为空");
        AssertUtils.notNull(dto.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getLocationId(), "货位id不能为空");
        AssertUtils.notNull(dto.getBatchTaskId(), "波次任务id不能为空");
        AssertUtils.notEmpty(dto.getOrderPalletInfoDTOS(), "订单托盘信息不能为空");
        dto.getOrderPalletInfoDTOS().stream().forEach(p -> {
            AssertUtils.notNull(p.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(p.getOrderNo(), "订单号不能为空");
            AssertUtils.notEmpty(p.getPalletNoList(), "托盘号集合不能为空");
        });

        List<Long> deleteIds = new ArrayList<>();
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setBatchTaskId(dto.getBatchTaskId());
        List<OrderLocationPalletDTO> dtoList = orderLocationPalletMapper.selectByConditions(queryDTO);
        if (!CollectionUtils.isEmpty(dtoList)) {
            List<Long> existIdList = dtoList.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
                .collect(Collectors.toList());
            deleteIds.addAll(existIdList);
        }

        // 兼容接力分拣按订单id临时保存的托盘关系数据
        List<Long> orderIdList =
            dto.getOrderPalletInfoDTOS().stream().map(p -> p.getOrderId()).distinct().collect(Collectors.toList());
        queryDTO.setOrderIdList(orderIdList);
        queryDTO.setBatchTaskId(null);
        List<OrderLocationPalletDTO> existDTOList = orderLocationPalletMapper.selectByConditions(queryDTO);
        if (!CollectionUtils.isEmpty(existDTOList)) {
            List<Long> existIdList = existDTOList.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
                .collect(Collectors.toList());
            deleteIds.addAll(existIdList);
        }

        List<OrderLocationPalletPO> addPOList = new ArrayList<>();
        dto.getOrderPalletInfoDTOS().stream().forEach(info -> {
            info.getPalletNoList().stream().distinct().forEach(palletNo -> {
                OrderLocationPalletPO orderLocationPalletPO = new OrderLocationPalletPO();
                orderLocationPalletPO.setId(UUIDGenerator.getUUID(OrderLocationPalletPO.class.getName()));
                orderLocationPalletPO.setOrgId(dto.getOrgId());
                orderLocationPalletPO.setWarehouseId(dto.getWarehouseId());
                orderLocationPalletPO.setPalletNo(palletNo);
                orderLocationPalletPO.setLocationId(dto.getLocationId());
                orderLocationPalletPO.setLocationName(dto.getLocationName());
                orderLocationPalletPO.setOrderId(info.getOrderId());
                orderLocationPalletPO.setOrderNo(info.getOrderNo());
                orderLocationPalletPO.setBatchTaskId(dto.getBatchTaskId());
                orderLocationPalletPO.setRemark(dto.getRemark());
                orderLocationPalletPO.setCreateUser(dto.getLastUpdateUser());
                orderLocationPalletPO.setCreateTime(new Date());
                orderLocationPalletPO.setLastUpdateUser(dto.getLastUpdateUser());
                orderLocationPalletPO.setLastUpdateTime(new Date());
                addPOList.add(orderLocationPalletPO);
            });
        });

        if (!CollectionUtils.isEmpty(deleteIds)) {
            deleteIds = deleteIds.stream().distinct().collect(Collectors.toList());
            orderLocationPalletMapper.deleteByPrimaryKeyBatch(deleteIds);
            LOGGER.info("添加订单货位托盘关系信息 删除已存在托盘关系数据：{}", JSON.toJSONString(deleteIds));
        }

        orderLocationPalletMapper.insertSelectiveBatch(addPOList);
        LOGGER.info("添加订单货位托盘关系信息 新增数据：{}", JSON.toJSONString(addPOList));
    }

    /**
     * 更新订单货位托盘关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void editPallet(OrderLocationPalletDTO dto) {
        // 只有已拣货未出库的才能改
        String batchTaskId = dto.getBatchTaskId();
        Integer orgId = dto.getOrgId();
        List<OutStockOrderDTO> orders = batchManageService.selectByBatchTaskId(batchTaskId, orgId);
        boolean allPicked = orders.stream().allMatch(it -> OutStockOrderStateEnum.已拣货.valueEquals(it.getState()));
        AssertUtils.isTrue(allPicked, "只允许修改 已拣货 状态订单的托盘信息");
        // 先删, 再新增
        orderLocationPalletMapper.deleteByBatchTaskId(batchTaskId);
        List<String> palletNos = dto.getPalletNos();
        // 清空托盘号
        if (CollectionUtils.isEmpty(palletNos)) {
            batchManageService.updateBatchTaskPallet(batchTaskId, "");
            return;
        }
        // 修改托盘号
        List<OrderPalletInfoDTO> palletInfos = orders.stream()
            .map(it -> OrderPalletInfoDTO.of(it.getId(), it.getRefOrderNo(), palletNos)).collect(Collectors.toList());
        dto.setOrderPalletInfoDTOS(palletInfos);
        addPallet(dto);
        // #1#2#3
        batchManageService.updateBatchTaskPallet(batchTaskId, "#" + String.join("#", palletNos));
    }

    /**
     * 订单货位托盘关系查询
     */
    @Transactional
    public List<OrderLocationPalletDTO> findPalletByCondition(OrderLocationPalletQueryDTO queryDTO) {
        LOGGER.info("订单货位托盘关系查询 入参：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");

        List<OrderLocationPalletDTO> palletDTOS = orderLocationPalletMapper.selectByConditions(queryDTO);
        LOGGER.info("添加订单货位托盘关系信息 查询结果：{}", JSON.toJSONString(palletDTOS));
        return palletDTOS;
    }

    /**
     * 订单货位托盘关系数据删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void deletePalletByCondition(OrderLocationPalletQueryDTO deleteDTO) {
        LOGGER.info("删除订单货位托盘关系信息 入参：{}", JSON.toJSONString(deleteDTO));
        AssertUtils.notNull(deleteDTO, "查询参数不能为空");
        AssertUtils.notNull(deleteDTO.getWarehouseId(), "仓库id不能为空");

        List<OrderLocationPalletDTO> dtoList = orderLocationPalletMapper.selectByConditions(deleteDTO);
        LOGGER.info("删除订单货位托盘关系信息 查询结果：{}", JSON.toJSONString(dtoList));
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        List<Long> palletIds = dtoList.stream().filter(p -> p.getId() != null).map(OrderLocationPalletDTO::getId)
            .distinct().collect(Collectors.toList());
        int count = orderLocationPalletMapper.deleteByPrimaryKeyBatch(palletIds);
        if (count < 1) {
            throw new BusinessValidateException("删除订单货位托盘关系信息失败");
        }
        LOGGER.info("删除订单货位托盘关系信息 删除数据：{}", JSON.toJSONString(palletIds));
    }

    /**
     * 根据订单id新增订单货位托盘关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addPalletByOrderId(OrderLocationPalletDTO dto) {
        LOGGER.info("根据订单id新增订单货位托盘关系 入参：{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "新增参数不能为空");
        AssertUtils.notNull(dto.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(dto.getLocationId(), "货位id不能为空");
        AssertUtils.notEmpty(dto.getOrderPalletInfoDTOS(), "订单托盘信息不能为空");
        dto.getOrderPalletInfoDTOS().stream().forEach(p -> {
            AssertUtils.notNull(p.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(p.getOrderNo(), "订单号不能为空");
            AssertUtils.notEmpty(p.getPalletNoList(), "托盘号集合不能为空");
        });

        List<Long> orderIds =
            dto.getOrderPalletInfoDTOS().stream().map(p -> p.getOrderId()).distinct().collect(Collectors.toList());

        resetMergePickOrder(dto, orderIds);
        List<Long> deleteIds = new ArrayList<>();
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setOrderIdList(orderIds);
        List<OrderLocationPalletDTO> dtoList = orderLocationPalletMapper.selectByConditions(queryDTO);
        if (!CollectionUtils.isEmpty(dtoList)) {
            List<Long> existIdList = dtoList.stream().filter(p -> p.getId() != null).map(p -> p.getId()).distinct()
                .collect(Collectors.toList());
            deleteIds.addAll(existIdList);
        }

        List<OrderLocationPalletPO> addPOList = new ArrayList<>();
        dto.getOrderPalletInfoDTOS().stream().forEach(info -> {
            info.getPalletNoList().stream().distinct().forEach(palletNo -> {
                OrderLocationPalletPO orderLocationPalletPO = new OrderLocationPalletPO();
                orderLocationPalletPO.setId(UUIDGenerator.getUUID(OrderLocationPalletPO.class.getName()));
                orderLocationPalletPO.setOrgId(dto.getOrgId());
                orderLocationPalletPO.setWarehouseId(dto.getWarehouseId());
                orderLocationPalletPO.setPalletNo(palletNo);
                orderLocationPalletPO.setLocationId(dto.getLocationId());
                orderLocationPalletPO.setLocationName(dto.getLocationName());
                orderLocationPalletPO.setOrderId(info.getOrderId());
                orderLocationPalletPO.setOrderNo(info.getOrderNo());
                orderLocationPalletPO.setBatchTaskId(dto.getBatchTaskId());
                orderLocationPalletPO.setRemark(dto.getRemark());
                orderLocationPalletPO.setCreateUser(dto.getLastUpdateUser());
                orderLocationPalletPO.setCreateTime(new Date());
                orderLocationPalletPO.setLastUpdateUser(dto.getLastUpdateUser());
                orderLocationPalletPO.setLastUpdateTime(new Date());
                addPOList.add(orderLocationPalletPO);
            });
        });

        if (!CollectionUtils.isEmpty(deleteIds)) {
            orderLocationPalletMapper.deleteByPrimaryKeyBatch(deleteIds);
            LOGGER.info("根据订单id新增订单货位托盘关系 删除已存在托盘关系数据：{}", JSON.toJSONString(deleteIds));
        }

        orderLocationPalletMapper.insertSelectiveBatch(addPOList);
        addMaxPalletIfNotExist(dto);
        LOGGER.info("根据订单id新增订单货位托盘关系 新增数据：{}", JSON.toJSONString(addPOList));

        orderLocationPalletAfterCommitBL.sendLocationChange(dto);
    }

    /**
     * 如果有合并拣货的订单，
     *
     * @param dto
     * @param orderIds
     */
    private void resetMergePickOrder(OrderLocationPalletDTO dto, List<Long> orderIds) {
        if (BooleanUtils.isFalse(globalCache.getOpenTrayPositionLocation(dto.getWarehouseId()))) {
            return;
        }

        if (BooleanUtils.isFalse(globalCache.getPickByCustomerFromCache(dto.getWarehouseId()))) {
            return;
        }

        MergePickOrderInfoQueryDTO mergeQueryDTO = new MergePickOrderInfoQueryDTO();
        mergeQueryDTO.setOrderIdList(orderIds);
        mergeQueryDTO.setWarehouseId(dto.getWarehouseId());

        List<MergePickOrderInfoResultDTO> mergeResultList =
            iBatchOrderInfoQueryService.queryMergePickOrderInfo(mergeQueryDTO);

        if (CollectionUtils.isEmpty(mergeResultList)) {
            return;
        }

        List<MergePickOrderInfoRelatedOrderResultDTO> otherOrderInfos =
            mergeResultList.stream().filter(MergePickOrderInfoResultDTO::isIsMergePick)
                .filter(m -> !CollectionUtils.isEmpty(m.getOtherOrderList()))
                .flatMap(m -> m.getOtherOrderList().stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(otherOrderInfos)) {
            return;
        }

        mergeResultList = mergeResultList.stream().filter(MergePickOrderInfoResultDTO::isIsMergePick)
            .filter(m -> !CollectionUtils.isEmpty(m.getOtherOrderList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mergeResultList)) {
            return;
        }

        List<OrderPalletInfoDTO> palletInfoDTOList = dto.getOrderPalletInfoDTOS();
        Map<Long, Long> insertOrderIdMap = palletInfoDTOList.stream()
            .collect(Collectors.toMap(OrderPalletInfoDTO::getOrderId, OrderPalletInfoDTO::getOrderId));

        Map<Long, List<OrderPalletInfoDTO>> orderGroupMap =
            dto.getOrderPalletInfoDTOS().stream().collect(Collectors.groupingBy(OrderPalletInfoDTO::getOrderId));

        for (MergePickOrderInfoResultDTO resultDTO : mergeResultList) {
            if (CollectionUtils.isEmpty(resultDTO.getOtherOrderList())) {
                continue;
            }
            List<OrderPalletInfoDTO> orderPalletInfoDTOS = orderGroupMap.get(resultDTO.getOrderId());
            if (CollectionUtils.isEmpty(orderPalletInfoDTOS)) {
                continue;
            }

            // 过滤出没在入参中的数据
            List<MergePickOrderInfoRelatedOrderResultDTO> otherOrderList = resultDTO.getOtherOrderList().stream()
                .filter(m -> CollectionUtils.isEmpty(orderGroupMap.get(m.getOrderId()))).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(otherOrderList)) {
                continue;
            }

            OrderPalletInfoDTO orderPalletInfoDTO = orderPalletInfoDTOS.stream().findFirst().get();
            List<OrderPalletInfoDTO> otherPalletInfoList = otherOrderList.stream().map(m -> {
                OrderPalletInfoDTO otherPalletInfo = new OrderPalletInfoDTO();
                otherPalletInfo.setOrderId(m.getOrderId());
                otherPalletInfo.setOrderNo(m.getOrderNo());
                otherPalletInfo.setPalletNoList(orderPalletInfoDTO.getPalletNoList());

                return otherPalletInfo;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(otherPalletInfoList)) {
                continue;
            }

            palletInfoDTOList.addAll(otherPalletInfoList);
        }

        dto.setOrderPalletInfoDTOS(palletInfoDTOList);
    }

    /**
     * 批量根据订单id新增订单货位托盘关系
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPalletByOrderIdBatch(BatchOrderLocationPalletDTO dto) {
        dto.getPalletDTOList().forEach(item -> {
            item.setLastUpdateUser(String.valueOf(dto.getOptUserId()));
            addPalletByOrderId(item);
        });
    }

    // 如果添加绑定关系的托盘号大于最大托盘号，更新最大托盘号为新的
    private void addMaxPalletIfNotExist(OrderLocationPalletDTO dto) {
        if (CollectionUtils.isEmpty(dto.getOrderPalletInfoDTOS())) {
            return;
        }

        LoactionDTO loactionDTO = locationBL.findLocationById(dto.getLocationId());
        if (Objects.isNull(loactionDTO)) {
            return;
        }

        List<Integer> palletNoList = dto.getOrderPalletInfoDTOS().stream()
            .filter(m -> !CollectionUtils.isEmpty(m.getPalletNoList())).flatMap(m -> m.getPalletNoList().stream())
            .distinct().map(Integer::valueOf).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(palletNoList)) {
            return;
        }

        // 前面已判空了
        Integer maxPalletNo = palletNoList.stream().max(Comparator.naturalOrder()).get();

        if (BooleanUtils.isFalse(needUpdate(loactionDTO, maxPalletNo))) {
            return;
        }

        LoactionDTO updateLocationDTO = new LoactionDTO();
        updateLocationDTO.setId(loactionDTO.getId());
        updateLocationDTO.setPalletCount(maxPalletNo);
        LOGGER.info("绑定订单托盘关系，更新托盘号：{}", JSON.toJSONString(updateLocationDTO));
        locationBL.updatePalletCount(updateLocationDTO);
    }

    private boolean needUpdate(LoactionDTO loactionDTO, Integer maxPalletNo) {
        if (Objects.isNull(loactionDTO.getPalletCount())) {
            return Boolean.TRUE;
        }

        if (loactionDTO.getPalletCount().compareTo(maxPalletNo) >= 0) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}
