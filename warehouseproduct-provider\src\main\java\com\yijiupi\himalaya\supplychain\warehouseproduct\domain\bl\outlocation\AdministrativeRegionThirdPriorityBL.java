package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.AreaDTO;
import com.yijiupi.himalaya.supplychain.service.IAreaService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.LocationRuleRegionMatchBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Service
public class AdministrativeRegionThirdPriorityBL extends OutStockLocationBaseBL implements Ordered {

    @Reference
    private IAreaService iAreaService;

    private static final String SIGN = "-";

    private static final Logger logger = LoggerFactory.getLogger(AdministrativeRegionThirdPriorityBL.class);

    @Override
    public int getOrder() {
        return 20;
    }

    @Override
    List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList) {
        return boList.stream().filter(m -> Objects.isNull(m.getLocationId()))
            .filter(m -> StringUtils.isNotBlank(m.getQueryDTO().getProvince())
                && StringUtils.isNotBlank(m.getQueryDTO().getCity()))
            .collect(Collectors.toList());
    }

    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> shouldHandleList = shouldHandleList(boList);
        // 查询车辆
        if (CollectionUtils.isEmpty(shouldHandleList)) {
            return Collections.emptyList();
        }

        List<LocationRuleDTO> locationRuleDTOList = new ArrayList<>();

        // 根据行政区域查询
        for (RecommendOutLocationQueryBO queryBO : shouldHandleList) {
            RecommendOutLocationQueryDTO queryDTO = queryBO.getQueryDTO();
            String joinRegionName = queryDTO.getProvince() + SIGN + queryDTO.getCity() + SIGN + queryDTO.getDistrict()
                + SIGN + queryDTO.getStreet();

            LocationRulePO locationRulePO = matchRegionLocationRule(queryDTO.getWarehouseId(), joinRegionName);
            // getLocationByRuleName(joinRegionName, queryDTO.getWarehouseId());

            if (!ObjectUtils.isEmpty(locationRulePO)) {
                queryBO.setLocationId(locationRulePO.getLocationId());
                queryBO.setLocationName(locationRulePO.getLocationName());
                LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
                BeanUtils.copyProperties(queryDTO, locationRuleDTO);
                locationRuleDTO.setLocationId(locationRulePO.getLocationId());
                locationRuleDTO.setLocationName(locationRulePO.getLocationName());
                locationRuleDTO.setRuleName(locationRulePO.getRuleName());
                // TODO: 新的地址 id 可能为 long
                locationRuleDTO.setAddressId(queryBO.getQueryDTO().getAddressId().intValue());
                locationRuleDTOList.add(locationRuleDTO);
            }
        }
        // 查询车辆
        return locationRuleDTOList;
    }

    private LocationRulePO matchRegionLocationRule(Integer warehouseId, String joinRegionName) {
        List<LocationRuleRegionMatchBO> regionLocationRuleBOList = getRegionLocationRuleList(warehouseId);
        if (CollectionUtils.isEmpty(regionLocationRuleBOList)) {
            return null;
        }
        LocationRuleRegionMatchBO bo = matchRegionName(joinRegionName, regionLocationRuleBOList);

        if (Objects.nonNull(bo)) {
            return bo.getLocationRulePO();
        }
        return null;
    }

    private LocationRuleRegionMatchBO matchRegionName(String joinRegionName, List<LocationRuleRegionMatchBO> boList) {
        // 如果为空 返回null
        if (StringUtils.isBlank(joinRegionName)) {
            return null;
        }
        // 查询到数据 返回数据
        Optional<LocationRuleRegionMatchBO> matchOptional =
            boList.stream().filter(m -> StringUtils.isNotBlank(m.getRuleName()))
                .filter(m -> m.getRuleName().equals(joinRegionName)).findAny();
        if (matchOptional.isPresent()) {
            return matchOptional.get();
        }
        int indexOf = joinRegionName.lastIndexOf(SIGN);
        if (indexOf < 0) {
            return null;
        }
        String substring = joinRegionName.substring(0, indexOf);
        // 继续查询
        return matchRegionName(substring, boList);
    }

    private List<LocationRuleRegionMatchBO> getRegionLocationRuleList(Integer warehouseId) {
        List<LocationRulePO> regionLocationRuleList = locationRuleMapper.getLocationsByWarehouseAndRuleType(warehouseId,
            LocationRuleEnum.ADMINISTRATIVE_REGION.getCode());
        if (CollectionUtils.isEmpty(regionLocationRuleList)) {
            return Collections.emptyList();
        }

        List<LocationRuleRegionMatchBO> boList = regionLocationRuleList.stream().filter(this::nonNull)
            .map(m -> LocationRuleRegionMatchBO.newInstance(m, Long.valueOf(m.getRuleId())))
            .collect(Collectors.toList());

        if (org.springframework.util.CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        List<Long> areaIds =
            boList.stream().map(LocationRuleRegionMatchBO::getRuleId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(areaIds)) {
            return Collections.emptyList();
        }

        List<AreaDTO> areaDTOList = iAreaService.queryBatch(areaIds);

        Map<Long, AreaDTO> areaDTOMap = areaDTOList.stream().collect(Collectors.toMap(AreaDTO::getId, v -> v));

        boList.forEach(bo -> {
            bo.setAreaInfo(areaDTOMap.get(bo.getRuleId()));
        });

        return boList;
    }

    private boolean nonNull(LocationRulePO m) {
        if (Objects.isNull(m.getRuleId())) {
            return Boolean.FALSE;
        }
        if ("null".equals(m.getRuleId())) {
            return Boolean.FALSE;
        }

        if (!StringUtils.isNumeric(m.getRuleId())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private LocationRulePO getLocationByRuleName(String joinRegionName, Integer warehouseId) {
        // 如果为空 返回null
        if (StringUtils.isBlank(joinRegionName)) {
            return null;
        }
        LocationRulePO locationRulePO = locationRuleMapper.getLocationByName(joinRegionName, warehouseId);
        // 查询到数据 返回数据
        if (!ObjectUtils.isEmpty(locationRulePO)) {
            return locationRulePO;
        }
        int indexOf = joinRegionName.lastIndexOf(SIGN);
        if (indexOf < 0) {
            return null;
        }
        String substring = joinRegionName.substring(0, indexOf);
        // 继续查询
        return getLocationByRuleName(substring, warehouseId);
    }

}
