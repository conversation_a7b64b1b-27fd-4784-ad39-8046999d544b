package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class CarFourthPriorityBL extends OutStockLocationBaseBL implements Ordered {

    @Override
    public int getOrder() {
        return 30;
    }

    @Override
    List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList) {
        return boList.stream().filter(m -> Objects.isNull(m.getLocationId()))
                .filter(m -> Objects.nonNull(m.getQueryDTO().getCarId())).collect(Collectors.toList());
    }

    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList) {
        // 过滤未设置出库位集合
        List<RecommendOutLocationQueryBO> collect = shouldHandleList(boList);
        if (CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }
        Map<Integer, List<RecommendOutLocationQueryBO>> warehouseIdGroupLocationRule =
                collect.stream().collect(Collectors.groupingBy(RecommendOutLocationQueryBO::getWarehouseId));
        Set<Integer> warehouseIdList = warehouseIdGroupLocationRule.keySet();

        List<LocationRulePO> ruleTotalList = new ArrayList<>();
        for (Integer warehouseId : warehouseIdList) {
            List<RecommendOutLocationQueryBO> locationRuleDTOS = warehouseIdGroupLocationRule.get(warehouseId);
            List<String> carIdList =
                    locationRuleDTOS.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getCarId())).map(m -> m.getQueryDTO().getCarId()).map(String ::valueOf).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(carIdList)) {
                continue;
            }
            // 根据车辆规则查询
            List<LocationRulePO> locationRulePOS = locationRuleMapper.listLocationByRuleId(warehouseId, carIdList, LocationRuleEnum.CAR.getCode());
            if (!CollectionUtils.isEmpty(locationRulePOS)) {
                ruleTotalList.addAll(locationRulePOS);
            }
        }

        Map<String, LocationRulePO> locationRulePOMap = ruleTotalList.stream().collect(Collectors.toMap(LocationRulePO :: getRuleId, v -> v));

        collect = collect.stream().filter(m -> Objects.nonNull(locationRulePOMap.get(m.getQueryDTO().getCarId().toString()))).collect(Collectors.toList());;

        if (CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }

        return initDTO(boList, locationRulePOMap, bo -> bo.getQueryDTO().getCarId().toString());
    }
}
