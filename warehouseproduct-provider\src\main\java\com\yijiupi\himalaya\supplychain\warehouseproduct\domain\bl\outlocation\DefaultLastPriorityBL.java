package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class DefaultLastPriorityBL extends OutStockLocationBaseBL implements Ordered {

    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }

    @Override
    List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList) {
        return boList.stream().filter(m -> Objects.isNull(m.getLocationId())).collect(Collectors.toList());
    }

    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> shouldHandleList = shouldHandleList(boList);

        if (CollectionUtils.isEmpty(shouldHandleList)) {
            return Collections.emptyList();
        }
        List<Integer> warehouseIdList =
                shouldHandleList.stream().map(RecommendOutLocationQueryBO::getWarehouseId).distinct().collect(Collectors.toList());
        // 查询默认出库位信息
        List<WarehouseConfigDTO> warehouseConfigDTOS =
                warehouseConfigService.listWarehouseDefaultLocation(warehouseIdList);
        if (ObjectUtils.isEmpty(warehouseConfigDTOS) || warehouseConfigDTOS.size() != warehouseIdList.size()) {
            throw new BusinessException("查询仓库信息失败 warehouse is null");
        }
        List<LocationRuleDTO> locationRuleDTOList = new ArrayList<>();
        for (RecommendOutLocationQueryBO queryBO : shouldHandleList) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigDTOS.stream()
                    .filter(w -> w.getWarehouse_Id().compareTo(queryBO.getWarehouseId()) == 0).findFirst()
                    .orElse(new WarehouseConfigDTO());
            queryBO.setLocationId(warehouseConfigDTO.getDefaultLocationId());
            queryBO.setLocationName(warehouseConfigDTO.getDefaultLocationName());

            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            BeanUtils.copyProperties(queryBO.getQueryDTO(), locationRuleDTO);
            locationRuleDTO.setLocationId(warehouseConfigDTO.getDefaultLocationId());
            locationRuleDTO.setLocationName(warehouseConfigDTO.getDefaultLocationName());
            locationRuleDTOList.add(locationRuleDTO);
        }
        return locationRuleDTOList;
    }
}
