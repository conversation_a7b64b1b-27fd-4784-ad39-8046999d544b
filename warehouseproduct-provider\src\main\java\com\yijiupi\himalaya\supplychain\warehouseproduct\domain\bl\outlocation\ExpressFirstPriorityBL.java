package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class ExpressFirstPriorityBL extends OutStockLocationBaseBL implements Ordered {

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList) {
        return boList.stream().filter(l -> StringUtils.isNotBlank(l.getQueryDTO().getLogisticsCompanyId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> shouldHandleList = shouldHandleList(boList);
        if (CollectionUtils.isEmpty(shouldHandleList)) {
            return Collections.emptyList();
        }

        Map<Integer, List<RecommendOutLocationQueryBO>> warehouseGroupLocation =
                boList.stream().collect(Collectors.groupingBy(RecommendOutLocationQueryBO::getWarehouseId));
        Set<Integer> warehouseIdList = warehouseGroupLocation.keySet();
        List<LocationRulePO> locationRulePOS = new ArrayList<>();
        for (Integer warehouseId : warehouseIdList) {
            // 查询快递物流配置
            List<RecommendOutLocationQueryBO> ruleDTOS = warehouseGroupLocation.get(warehouseId);
            List<LocationRulePO> ruleList = locationRuleMapper.listLocationByRuleId(warehouseId,
                    ruleDTOS.stream().map(m -> m.getQueryDTO().getLogisticsCompanyId()).collect(Collectors.toList()),
                    LocationRuleEnum.EXPRESS_SERVICE.getCode());
            if (!CollectionUtils.isEmpty(ruleList)) {
                locationRulePOS.addAll(ruleList);
            }
        }

        if (CollectionUtils.isEmpty(locationRulePOS)) {
            return Collections.emptyList();
        }

        // TODO,用不用合并
        Map<String, LocationRulePO> locationRulePOMap = locationRulePOS.stream().collect(Collectors.toMap(LocationRulePO :: getRuleId, v -> v));

        shouldHandleList = shouldHandleList.stream().filter(m -> Objects.nonNull(locationRulePOMap.get(m.getQueryDTO().getLogisticsCompanyId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shouldHandleList)) {
            return Collections.emptyList();
        }

        return initDTO(boList, locationRulePOMap, bo -> bo.getQueryDTO().getLogisticsCompanyId());
    }
}
