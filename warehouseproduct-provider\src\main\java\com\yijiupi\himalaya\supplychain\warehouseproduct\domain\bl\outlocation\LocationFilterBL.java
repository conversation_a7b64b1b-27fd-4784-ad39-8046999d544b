package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/29
 */
@Service
public class LocationFilterBL {

    @Autowired
    private LocationPOMapper locationPOMapper;

    public List<LocationRuleDTO> filterExistLocationRule(List<LocationRuleDTO> locationRuleDTOList) {
        if (CollectionUtils.isEmpty(locationRuleDTOList)) {
            return Collections.emptyList();
        }

        List<Long> locationIds = locationRuleDTOList.stream().map(LocationRuleDTO :: getLocationId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationIds)) {
            return locationRuleDTOList;
        }

        List<LoactionDTO> locationList = locationPOMapper.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(locationList)) {
            return Collections.emptyList();
        }

        if (locationList.size() == locationIds.size()) {
            return locationRuleDTOList;
        }

        Map<Long, LoactionDTO> loactionDTOMap = locationList.stream().collect(Collectors.toMap(LoactionDTO :: getId, v -> v));

        return locationRuleDTOList.stream().filter(m -> Objects.nonNull(loactionDTOMap.get(m.getLocationId()))).collect(Collectors.toList());
    }

}
