package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public abstract class OutStockLocationBaseBL {

    @Autowired
    protected LocationRuleMapper locationRuleMapper;

    abstract List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList);

    public abstract List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList);

    List<LocationRuleDTO> initDTO(List<RecommendOutLocationQueryBO> shouldHandleList, Map<String, LocationRulePO> locationRulePOMap, Function<RecommendOutLocationQueryBO, String> function) {
        List<LocationRuleDTO> locationRuleDTOList = new ArrayList<>();
        for (RecommendOutLocationQueryBO queryBO : shouldHandleList) {
            LocationRulePO locationRulePO = locationRulePOMap.get(function.apply(queryBO));
            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            BeanUtils.copyProperties(queryBO.getQueryDTO(), locationRuleDTO);
            // TODO: 新的地址 id 可能为 long
            if (Objects.nonNull(queryBO.getQueryDTO().getAddressId())) {
                locationRuleDTO.setAddressId(queryBO.getQueryDTO().getAddressId().intValue());
            }
            locationRuleDTO.setLocationId(locationRulePO.getLocationId());
            locationRuleDTO.setLocationName(locationRulePO.getLocationName());
            //TODO 创建波次的时候用这个字段做了业务（当做key分组），而这个字段数据库有重复的，有时间要改。
            locationRuleDTO.setRuleName(locationRulePO.getRuleName());
            locationRuleDTO.setRuleId(locationRulePO.getRuleId());
            queryBO.setLocationId(locationRulePO.getLocationId());
            queryBO.setLocationName(locationRulePO.getLocationName());

            locationRuleDTOList.add(locationRuleDTO);
        }

        return locationRuleDTOList;
    }

}
