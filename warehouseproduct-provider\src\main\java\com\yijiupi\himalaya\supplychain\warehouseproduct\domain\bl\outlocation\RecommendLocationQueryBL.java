package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationRuleConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.OutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Service
public class RecommendLocationQueryBL {

    @Autowired
    private LocationRuleMapper locationRuleMapper;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    public List<LocationRuleDTO> getLocationByRoute(OutLocationQueryDTO outLocationQueryDTO) {
        if (!LocationRuleEnum.LINE.getCode().equals(outLocationQueryDTO.getCode())) {
            return Collections.emptyList();
        }
        List<LocationRulePO> locationRulePOList = locationRuleMapper.listLocationByRuleId(
            outLocationQueryDTO.getWarehouseId(), outLocationQueryDTO.getRuleIdList(), outLocationQueryDTO.getCode());

        List<String> notExistRouteIdList = getNotExistRouteList(outLocationQueryDTO, locationRulePOList);
        if (CollectionUtils.isEmpty(notExistRouteIdList)) {
            return LocationRuleConvert.convertQueryList(locationRulePOList);
        }

        List<WarehouseConfigDTO> warehouseConfigDTOS = warehouseConfigService
            .listWarehouseDefaultLocation(Collections.singletonList(outLocationQueryDTO.getWarehouseId()));

        if (CollectionUtils.isEmpty(warehouseConfigDTOS)) {
            return Collections.emptyList();
        }

        WarehouseConfigDTO warehouseConfigDTO = warehouseConfigDTOS.get(0);

        List<LocationRuleDTO> locationRuleDTOList = notExistRouteIdList.stream().map(id -> {
            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            locationRuleDTO.setLocationId(warehouseConfigDTO.getDefaultLocationId());
            locationRuleDTO.setLocationName(warehouseConfigDTO.getDefaultLocationName());
            locationRuleDTO.setRuleId(id);

            return locationRuleDTO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(locationRulePOList)) {
            List<LocationRuleDTO> routeLocationRuleDTOList = LocationRuleConvert.convertQueryList(locationRulePOList);
            locationRuleDTOList.addAll(routeLocationRuleDTOList);
        }

        return locationRuleDTOList;
    }

    private List<String> getNotExistRouteList(OutLocationQueryDTO outLocationQueryDTO,
        List<LocationRulePO> locationRulePOList) {
        if (CollectionUtils.isEmpty(locationRulePOList)) {
            return outLocationQueryDTO.getRuleIdList();
        }
        Map<String, String> existRuleIdMap = locationRulePOList.stream().map(LocationRulePO::getRuleId).distinct()
            .collect(Collectors.toMap(k -> k, v -> v));
        List<String> notExsitRuleIdList = outLocationQueryDTO.getRuleIdList().stream()
            .filter(id -> StringUtils.isBlank(existRuleIdMap.get(id))).collect(Collectors.toList());

        return notExsitRuleIdList;
    }

}
