package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.CityAreaDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ICityAreaService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.TmsApiManager;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.dto.RoutingInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Service
public class RouteOrAreaSecondBL extends OutStockLocationBaseBL implements Ordered {

    @Resource
    private TmsApiManager tmsApiManager;

    @Reference
    private ICityAreaService iCityAreaService;

    private static final int ROUTE = 0;
    private static final int AREA = 1;

    private static final int ROUTE_AND_AREA = 2;

    @Override
    public int getOrder() {
        return 10;
    }

    @Override
    List<RecommendOutLocationQueryBO> shouldHandleList(List<RecommendOutLocationQueryBO> boList) {
        return boList.stream().filter(m -> Objects.isNull(m.getLocationId())).filter(this::support)
            .collect(Collectors.toList());
    }

    private boolean support(RecommendOutLocationQueryBO bo) {
        if (Objects.nonNull(bo.getQueryDTO().getAreaId())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(bo.getQueryDTO().getRouteId())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(bo.getQueryDTO().getAddressId())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> shouldHandleList = shouldHandleList(boList);
        if (CollectionUtils.isEmpty(shouldHandleList)) {
            return Collections.emptyList();
        }

        // 根据过滤后的collect根据仓库进行分组
        Map<Integer, List<RecommendOutLocationQueryBO>> warehouseGroupLocation =
            boList.stream().collect(Collectors.groupingBy(RecommendOutLocationQueryBO::getWarehouseId));

        // 查询仓库默认集合方式
        List<Warehouse> warehouses = tmsApiManager.getWarehouseList(warehouseGroupLocation.keySet());
        if (CollectionUtils.isEmpty(warehouses)) {
            return Collections.emptyList();
        }

        Map<Integer, Warehouse> warehouseMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));

        List<LocationRuleDTO> locationList = new ArrayList<>();

        for (Map.Entry<Integer, Warehouse> warehouseEntry : warehouseMap.entrySet()) {
            List<LocationRuleDTO> routeLocationList =
                handleByRoute(warehouseEntry.getValue(), warehouseGroupLocation.get(warehouseEntry.getKey()));
            List<LocationRuleDTO> areaLocationList =
                handleByArea(warehouseEntry.getValue(), warehouseGroupLocation.get(warehouseEntry.getKey()));
            if (!CollectionUtils.isEmpty(routeLocationList)) {
                locationList.addAll(routeLocationList);
            }
            if (!CollectionUtils.isEmpty(areaLocationList)) {
                locationList.addAll(areaLocationList);
            }
        }

        return locationList;
    }

    private List<LocationRuleDTO> handleByRoute(Warehouse warehouse, List<RecommendOutLocationQueryBO> boList) {
        // 0按线路；2 是既按线路，又可以按片区；片区现在tms变更没通知wms，暂时只支持按线路
        if (Objects.isNull(warehouse.getDistributeCarType())
            || (warehouse.getDistributeCarType() != ROUTE && warehouse.getDistributeCarType() != ROUTE_AND_AREA)) {
            return Collections.emptyList();
        }

        List<RecommendOutLocationQueryBO> routeBoList = initRouteInfo(boList);
        if (CollectionUtils.isEmpty(routeBoList)) {
            return Collections.emptyList();
        }

        Map<Integer, List<RecommendOutLocationQueryBO>> boGroupMap =
            routeBoList.stream().collect(Collectors.groupingBy(m -> m.getQueryDTO().getWarehouseId()));
        List<LocationRulePO> locationRuleList = new ArrayList<>();
        for (Map.Entry<Integer, List<RecommendOutLocationQueryBO>> entry : boGroupMap.entrySet()) {
            List<String> routeIds = entry.getValue().stream().map(m -> m.getQueryDTO().getRouteId().toString())
                .collect(Collectors.toList());
            List<LocationRulePO> tmpList =
                locationRuleMapper.listLocationByRuleId(entry.getKey(), routeIds, LocationRuleEnum.LINE.getCode());
            if (!CollectionUtils.isEmpty(tmpList)) {
                locationRuleList.addAll(tmpList);
            }
        }

        locationRuleList = locationRuleList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(locationRuleList)) {
            return Collections.emptyList();
        }

        Map<String, LocationRulePO> locationRuleMap =
            locationRuleList.stream().collect(Collectors.toMap(LocationRulePO::getRuleId, v -> v, (v1, v2) -> v1));

        boList = boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getRouteId()))
            .filter(m -> Objects.nonNull(locationRuleMap.get(m.getQueryDTO().getRouteId().toString())))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        return initDTO(boList, locationRuleMap, bo -> bo.getQueryDTO().getRouteId().toString());
    }

    private List<LocationRuleDTO> handleByArea(Warehouse warehouse, List<RecommendOutLocationQueryBO> boList) {
        if (Objects.isNull(warehouse.getDistributeCarType()) || warehouse.getDistributeCarType() != AREA) {
            return Collections.emptyList();
        }
        List<RecommendOutLocationQueryBO> routeBoList = initAreaInfo(boList);
        if (CollectionUtils.isEmpty(routeBoList)) {
            return Collections.emptyList();
        }

        Map<Integer, List<RecommendOutLocationQueryBO>> boGroupMap =
            routeBoList.stream().collect(Collectors.groupingBy(m -> m.getQueryDTO().getWarehouseId()));
        List<LocationRulePO> locationRuleList = new ArrayList<>();
        for (Map.Entry<Integer, List<RecommendOutLocationQueryBO>> entry : boGroupMap.entrySet()) {
            List<String> routeIds =
                entry.getValue().stream().map(m -> m.getQueryDTO().getAreaId().toString()).collect(Collectors.toList());
            List<LocationRulePO> tmpList =
                locationRuleMapper.listLocationByRuleId(entry.getKey(), routeIds, LocationRuleEnum.DISTRICT.getCode());
            if (!CollectionUtils.isEmpty(tmpList)) {
                locationRuleList.addAll(tmpList);
            }
        }

        Map<String, LocationRulePO> locationRuleMap =
            locationRuleList.stream().collect(Collectors.toMap(LocationRulePO::getRuleId, v -> v, (v1, v2) -> v1));

        boList = boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getAreaId()))
            .filter(m -> Objects.nonNull(locationRuleMap.get(m.getQueryDTO().getAreaId().toString())))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        return initDTO(boList, locationRuleMap, bo -> bo.getQueryDTO().getAreaId().toString());
    }

    private List<RecommendOutLocationQueryBO> initRouteInfo(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> haveRouteList =
                boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getRouteId())).collect(Collectors.toList());
        // 没有routeId的先过滤出来
        List<RecommendOutLocationQueryBO> haveNotRouteInfoList =
                boList.stream().filter(m -> Objects.isNull(m.getQueryDTO().getRouteId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(haveNotRouteInfoList)) {
            return haveRouteList;
        }
        Map<Integer, List<Long>> addressWarehouseIdMap = haveNotRouteInfoList.stream()
                .map(RecommendOutLocationQueryBO::getQueryDTO).filter(queryDTO -> Objects.nonNull(queryDTO.getAddressId()))
                .collect(Collectors.groupingBy(RecommendOutLocationQueryDTO::getWarehouseId,
                        Collectors.mapping(RecommendOutLocationQueryDTO::getAddressId, Collectors.toList())));
        if (CollectionUtils.isEmpty(addressWarehouseIdMap)) {
            return haveRouteList;
        }
        Map<Long, RoutingInfoDTO> routingByAddressIds = addressWarehouseIdMap.entrySet()
                .stream().map(it -> tmsApiManager.findRoutingByAddressIds(it.getValue(), it.getKey()))
                .filter(Objects::nonNull).map(Map::entrySet)
                .flatMap(Collection::stream).filter(it -> it.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        haveNotRouteInfoList.forEach(m -> {
            RoutingInfoDTO routingDTO = routingByAddressIds.get(m.getQueryDTO().getAddressId());
            if (Objects.nonNull(routingDTO)) {
                m.getQueryDTO().setRouteId(routingDTO.getId());
            }
        });
        return boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getRouteId())).collect(Collectors.toList());
    }

    private List<RecommendOutLocationQueryBO> initAreaInfo(List<RecommendOutLocationQueryBO> boList) {
        List<RecommendOutLocationQueryBO> haveAreaList =
            boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getAreaId())).collect(Collectors.toList());
        // 没有routeId的先过滤出来
        List<RecommendOutLocationQueryBO> haveNotAreaInfoList = boList.stream()
                .filter(m -> Objects.isNull(m.getQueryDTO().getAreaId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(haveNotAreaInfoList)) {
            return haveAreaList;
        }
        Map<Integer, List<Integer>> addressWarehouseIdMap = haveNotAreaInfoList.stream()
                .map(RecommendOutLocationQueryBO::getQueryDTO)
                .filter(queryDTO -> Objects.nonNull(queryDTO.getAddressId()))
                .collect(Collectors.groupingBy(RecommendOutLocationQueryDTO::getWarehouseId,
                        Collectors.mapping(it -> it.getAddressId().intValue(), Collectors.toList())));
        if (CollectionUtils.isEmpty(addressWarehouseIdMap)) {
            return haveAreaList;
        }
        Map<Integer, CityAreaDTO> areaMap = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : addressWarehouseIdMap.entrySet()) {
            Map<Integer, CityAreaDTO> tmpMap = iCityAreaService.findAreasByAddressIds(entry.getValue(), entry.getKey());
            if (!CollectionUtils.isEmpty(tmpMap)) {
                areaMap.putAll(tmpMap);
            }
        }
        for (RecommendOutLocationQueryBO bo : haveNotAreaInfoList) {
            RecommendOutLocationQueryDTO queryDTO = bo.getQueryDTO();
            Long addressId = queryDTO.getAddressId();
            if (addressId == null) {
                continue;
            }
            Optional.ofNullable(areaMap.get(addressId.intValue()))
                    .map(CityAreaDTO::getId)
                    .ifPresent(queryDTO::setAreaId);
        }
        return boList.stream().filter(m -> Objects.nonNull(m.getQueryDTO().getAreaId())).collect(Collectors.toList());

    }

}
