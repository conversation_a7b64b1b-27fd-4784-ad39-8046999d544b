package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productinfo;

import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductInfoPOMapper;
import com.yijiupi.himalaya.supplychain.productsync.domain.po.ProductInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productinfo.WarehouseProductInfoDTO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
public class ProductInfoQueryBL {

    @Autowired
    private ProductInfoPOMapper productInfoPOMapper;

    public List<WarehouseProductInfoDTO> findByIds(List<Long> productInfoIds) {
        List<ProductInfoPO> productInfoPOS = productInfoPOMapper.selectProductByIdList(productInfoIds);
        return getProductInfoDTOS(productInfoPOS);
    }

    public List<WarehouseProductInfoDTO> findBySkuIds(List<Long> lstSkuId) {
        List<ProductInfoPO> productInfoPOS = productInfoPOMapper.selectProductBySkuIdList(lstSkuId);
        return getProductInfoDTOS(productInfoPOS);
    }

    private static @NotNull List<WarehouseProductInfoDTO> getProductInfoDTOS(List<ProductInfoPO> productInfoPOS) {
        if (CollectionUtils.isEmpty(productInfoPOS)) {
            return Collections.emptyList();
        }
        return productInfoPOS.stream().map(infoPO -> {
            WarehouseProductInfoDTO dto = new WarehouseProductInfoDTO();
            BeanUtils.copyProperties(infoPO, dto);
            return dto;
        }).collect(Collectors.toList());
    }

}
