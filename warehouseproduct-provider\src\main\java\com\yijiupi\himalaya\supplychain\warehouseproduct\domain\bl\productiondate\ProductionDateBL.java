package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productiondate;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductLocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.SkuInventoryAndProductionDateHelperBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.SkuInventoryAndProductionDateQO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDetailDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductionDateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productiondate.ProductionDateQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productiondate.ProductionDateQueryItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-25 17:23
 **/
@Service
public class ProductionDateBL {

    @Resource
    private ProductLocationBL productLocationBL;

    @Resource
    private ProductSkuPOMapper productSkuPOMapper;

    @Reference
    private WarehouseConfigService warehouseConfigService;

    private static final Logger logger = LoggerFactory.getLogger(ProductionDateBL.class);

    public List<ProductionDateDTO> queryProductionDate(List<SkuInventoryAndProductionDateHelperBO> boList, ProductionDateQueryDTO queryDTO) {
        List<Long> locationIds = boList.stream().map(SkuInventoryAndProductionDateHelperBO::getAreaId).distinct()
                .collect(Collectors.toList());
        List<Long> skuIds = queryDTO.getProductList().stream().map(ProductionDateQueryItemDTO::getSkuId).distinct()
                .collect(Collectors.toList());
        SkuInventoryAndProductionDateQO qo = new SkuInventoryAndProductionDateQO();
        qo.setSkuIds(skuIds);
        qo.setLocationIds(locationIds);
        qo.setWarehouseId(queryDTO.getWarehouseId());
        return productSkuPOMapper.listProductionDate(qo);
    }

    public List<SkuInventoryAndProductionDateHelperBO> getHelperInfo(ProductionDateQueryDTO query) {
        Integer warehouseId = query.getWarehouseId();
        List<SkuInventoryAndProductionDateHelperBO> boList = query.getProductList().stream().map(m -> {
            SkuInventoryAndProductionDateHelperBO bo = new SkuInventoryAndProductionDateHelperBO();
            bo.setSkuId(m.getSkuId());
            bo.setLocationId(m.getLocationId());
            return bo;
        }).collect(Collectors.toList());
        if (BooleanUtils.isFalse(warehouseConfigService.getConfigByWareHouseId(warehouseId).isOpen2p5plus())) {
            boList.forEach(m -> m.setAreaId(m.getLocationId()));
            return boList;
        }
        List<Long> skuIds = query.getProductList().stream().map(ProductionDateQueryItemDTO::getSkuId).distinct()
                .collect(Collectors.toList());
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setSkuIdList(skuIds);
        dto.setWarehouseId(warehouseId);
        List<ProductLocationDetailDTO> productLocationDetails = productLocationBL.findProductLocationBySkuIds(dto);
        if (CollectionUtils.isEmpty(productLocationDetails)) {
            return boList;
        }
        Map<String, ProductLocationDetailDTO> detailMap = productLocationDetails.stream()
                .collect(Collectors.toMap(m -> m.getProductSkuId() + "-" + m.getId(), Function.identity(), this::mergeIt));
        for (SkuInventoryAndProductionDateHelperBO m : boList) {
            ProductLocationDetailDTO productLocationDetailDTO = detailMap.get(m.getSkuId() + "-" + m.getLocationId());
            if (Objects.isNull(productLocationDetailDTO)) {
                continue;
            }
            m.setLocationName(productLocationDetailDTO.getName());
            if (CategoryEnum.CARGO_AREA.getByteValue().equals(productLocationDetailDTO.getCategory())) {
                m.setAreaId(productLocationDetailDTO.getId());
            } else {
                m.setAreaId(productLocationDetailDTO.getAreaId());
            }
        }
        return boList;
    }

    private ProductLocationDetailDTO mergeIt(ProductLocationDetailDTO a, ProductLocationDetailDTO b) {
        logger.info("存在一个产品关联多次同一个货位: {}", JSON.toJSONString(a, SerializerFeature.WriteMapNullValue));
        return a;
    }

}
