package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productlocation;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.common.dto.PageListUtil;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.LocationConvetor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productlocation.ProductLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-28 11:01
 **/
@Service
public class ProductLocationHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductLocationHelper.class);

    @Resource
    private ProductLocationPOMapper productLocationPOMapper;

    @Resource
    private LocationPOMapper locationPOMapper;

    @Reference
    private IBatchInventoryQueryService batchInventoryQueryService;

    private static final Logger logger = LoggerFactory.getLogger(ProductLocationHelper.class);

    public void insertSelective(ProductLocationPO relPO) {
        List<ProductLocationPO> newLocations = getNotExistsSkuInfo(Collections.singletonList(relPO));
        if (newLocations.isEmpty()) {
            logger.warn("单个新增: 已经存在产品关联货位, 不再新增, {}", JSON.toJSONString(relPO));
            return;
        }

        logger.warn("单个新增产品关联货位, {}", JSON.toJSONString(relPO));
        productLocationPOMapper.insertSelective(relPO);
    }

    public void insertBatch(List<ProductLocationPO> productLocationPOList) {
        List<ProductLocationPO> notExistsSkuInfo = getNotExistsSkuInfo(productLocationPOList);
        if (notExistsSkuInfo.isEmpty()) {
            logger.warn("批量新增: 已经存在产品关联货位, 不再新增, {}", JSON.toJSONString(productLocationPOList));
            return;
        }
        logger.warn("批量新增产品关联货位, {}", JSON.toJSONString(productLocationPOList));
        productLocationPOMapper.insertBatch(productLocationPOList);
    }

    /**
     * SCM-15228 2.5+实施优化，易点货收货入库交互调整
     *
     * @param param 查询条件
     * @return 查询结果
     */
    public PageList<LoactionDTO> pageListLocation(ProductLocationQueryDTO param) {
        switch (param.getQueryType()) {
            case 1:
                return queryLocationArea(param);
            case 2:
                PageHelper.startPage(param.getCurrentPage(), param.getPageSize());
                return locationPOMapper.pageListRoadway(param).toPageList(LocationConvetor::convertDTOS);
            case 3:
                return queryLocation(param);
            case 4:
                return queryLocationInfo(param);
            default:
                return PageListUtil.getDefault(param);
        }
    }

    /**
     * 分页查询货区
     *
     * @param param 查询参数
     * @return 查询结果
     */
    private PageList<LoactionDTO> queryLocationArea(ProductLocationQueryDTO param) {
        LocationQueryDTO query = new LocationQueryDTO();
        query.setCityId(param.getCityId());
        query.setWarehouseId(param.getWarehouseId());
        query.setCategoryList(Collections.singletonList(CategoryEnum.CARGO_AREA.getByteValue()));
        query.setSubcategoryList(param.getSubCategoryList());
        query.setHasLocation(param.getHasLocation());
        query.setLocSubCategoryList(param.getLocSubCategoryList());
        query.setState(param.getStatus());
        query.setOnlyEmptyLocation(param.getOnlyEmptyLocation());
        PageHelper.startPage(param.getCurrentPage(), param.getPageSize());
        return locationPOMapper.pageListLocationByCond(query).toPageList(LocationConvetor::convertDTOS);
    }

    /**
     * 通过货位名或货位 id 查货区及巷道
     *
     * @param param 查询参数
     * @return 查询结果
     */
    private PageList<LoactionDTO> queryLocationInfo(ProductLocationQueryDTO param) {
        LocationQueryDTO query = new LocationQueryDTO();
        query.setCityId(param.getCityId());
        query.setWarehouseId(param.getWarehouseId());
        query.setCategoryList(Collections.singletonList(CategoryEnum.CARGO_LOCATION.getByteValue()));
        query.setSubcategoryList(param.getSubCategoryList());
        query.setName(param.getLocationName());
        query.setState(param.getStatus());
        Optional.ofNullable(param.getLocationId()).map(Collections::singletonList).ifPresent(query::setLocationIdList);
        PageHelper.startPage(param.getCurrentPage(), param.getPageSize());
        return locationPOMapper.listLocationByCondition(query).toPageList(LocationConvetor::convertDTOS);
    }

    /**
     * 分页查询货位
     *
     * @param param 查询参数
     * @return 查询结果
     */
    private PageList<LoactionDTO> queryLocation(ProductLocationQueryDTO param) {
        PageHelper.startPage(param.getCurrentPage(), param.getPageSize());
        PageList<LoactionDTO> result = locationPOMapper.pageListLocationByAreaAndRoad(param)
                .toPageList(LocationConvetor::convertDTOS);
        Set<Long> locationIds = result.getDataList().stream().map(LoactionDTO::getId).collect(Collectors.toSet());
        // 查询货位库存
        Map<String, List<BatchInventoryDTO>> locationInventoryMap = queryLocationInventory(locationIds, param.getWarehouseId());
        List<BatchInventoryDTO> warnInventories = locationInventoryMap.get("null");
        if (warnInventories != null) {
            logger.warn("存在货位 id 为 null 的库存数据: {}", JSON.toJSONString(warnInventories));
        }
        for (LoactionDTO location : result.getDataList()) {
            List<BatchInventoryDTO> inventories = locationInventoryMap.get(String.valueOf(location.getId()));
            if (CollectionUtils.isEmpty(inventories)) {
                location.setEmptyLocation(true);
                continue;
            }
            location.setEmptyLocation(false);
        }
        return result;
    }

    private Map<String, List<BatchInventoryDTO>> queryLocationInventory(Set<Long> locationIds, Integer warehouseId) {
        BatchInventoryQueryDTO query = new BatchInventoryQueryDTO();
        query.setLocationIds(new ArrayList<>(locationIds));
        query.setWarehouseId(warehouseId);
        // 只查有库存的数据
        query.setShowAll(null);
        List<BatchInventoryDTO> result = batchInventoryQueryService.findBatchInventoryList(query).getDataList();
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyMap();
        }
        return result.stream().collect(Collectors.groupingBy(it -> String.valueOf(it.getLocationId())));
    }

    private List<ProductLocationPO> getNotExistsSkuInfo(List<ProductLocationPO> pos) {
        try {
            if (CollectionUtils.isEmpty(pos)) {
                return Collections.emptyList();
            }
            Map<String, ProductLocationPO> newMap = pos.stream()
                    .collect(Collectors.toMap(this::getMapKey, Function.identity(), (a, b) -> a));
            Set<Long> skuIds = pos.stream().map(ProductLocationPO::getProductSku_Id).collect(Collectors.toSet());
            Map<String, ProductLocationPO> resultMap = productLocationPOMapper.selectBySkuIds(skuIds).stream()
                    .collect(Collectors.toMap(this::getMapKey, Function.identity(), (a, b) -> a));
            return newMap.entrySet().stream().filter(it -> filterNotExists(it, resultMap)).map(Map.Entry::getValue)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("校验产品关联货位是否已经存在失败: ", e);
        }
        return pos;
    }

    private boolean filterNotExists(Map.Entry<String, ProductLocationPO> _new, Map<String, ProductLocationPO> oldMap) {
        // skuId 不存在或 skuId 存在但 locationId 不同
        if (!oldMap.containsKey(_new.getKey())) {
            return true;
        }
        return !oldMap.get(_new.getKey()).getLocation_Id().equals(_new.getValue().getLocation_Id());
    }

    private String getMapKey(ProductLocationPO it) {
        return String.format("%s-%s", it.getProductSku_Id(), it.getLocation_Id());
    }

    private Collector<ProductLocationPO, ?, Set<Long>> collectToSkuIdSet() {
        return Collectors.mapping(ProductLocationPO::getProductSku_Id, Collectors.toSet());
    }

}
