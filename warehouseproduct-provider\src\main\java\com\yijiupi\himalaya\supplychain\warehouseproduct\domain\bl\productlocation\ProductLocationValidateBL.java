package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productlocation;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.config.UserWarehouseAllocation;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationItemBelongDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationItemBelongQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationQuery;
import com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUserAuth;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUserQueryDTO;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.ProductLocationValidateOperationLegalBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/2/25
 */
@Service
public class ProductLocationValidateBL {

    @Autowired
    private LocationAreaPOMapper locationAreaPOMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;

    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;
    @Reference
    private IAdminUserQueryService iAdminUserQueryService;

    private static final List<String> LEGAL_ROLE_LIST = Arrays.asList(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程,
            AdminUserAuthRoleConstant.ROLE_CODE_拣货员, AdminUserAuthRoleConstant.ROLE_CODE_播种人,
            AdminUserAuthRoleConstant.ROLE_CODE_打包员, AdminUserAuthRoleConstant.ROLE_CODE_补货人);

    // 1、判断是否开启分仓；2、判断用户角色；3、查询用户分区；4、查询货区分区
    public void validateOperationLegal(ProductLocationValidateOperationLegalBO bo) {
        boolean enabledWarehouseSplit = iWarehouseAllocationConfigService.isEnabledWarehouseSplit(bo.getWarehouseId());
        if (BooleanUtils.isFalse(enabledWarehouseSplit)) {
            return;
        }

        if (BooleanUtils.isFalse(shouldValidateAdminUser(bo))) {
            return;
        }
        // 填充货区信息
        initAreaInfo(bo);

        WarehouseAllocationQuery query = new WarehouseAllocationQuery();
        query.setUserId(bo.getUserId());
        query.setWarehouseIds(Collections.singleton(bo.getWarehouseId()));
        List<UserWarehouseAllocation> userWarehouseAllocationList =
                iWarehouseAllocationConfigService.queryConfigByUser(query);
        // 没判断角色，直接判断用户是否有分仓权限
        if (CollectionUtils.isEmpty(userWarehouseAllocationList)) {
            return;
        }

        // SCM-21611 分仓逻辑支持一个人同时属于多个分仓
        if (userWarehouseAllocationList.size() > 1) {
            return;
        }

        Optional<Integer> userWarehouseConfigTypeOptional = userWarehouseAllocationList.stream()
                .filter(m -> m.getWarehouseId().equals(bo.getWarehouseId())).filter(m -> Objects.nonNull(m.getConfigType()))
                .map(UserWarehouseAllocation::getConfigType).findFirst();
        if (BooleanUtils.isFalse(userWarehouseConfigTypeOptional.isPresent())) {
            throw new BusinessValidateException("您没有分区操作权限，请为当前用户配置分仓权限后操作！");
        }

        Integer userWarehouseConfigType = userWarehouseConfigTypeOptional.get();

        List<String> areaIds = getAreaIds(bo.getLocationIds());

        if (CollectionUtils.isEmpty(areaIds)) {
            return;
        }

        WarehouseAllocationItemBelongQueryDTO queryDTO = new WarehouseAllocationItemBelongQueryDTO();
        queryDTO.setWarehouseId(bo.getWarehouseId());
        queryDTO.setRelateType(WarehouseAllocationConfigRelateType.LOCATION_AREA.getValue());
        queryDTO.setRelateIds(getAreaIds(bo.getLocationIds()));
        List<WarehouseAllocationItemBelongDTO> itemBelongDTOList =
                iWarehouseAllocationConfigService.queryItemBelongWarehouse(queryDTO);

        List<WarehouseAllocationItemBelongDTO> notHaveRightsAreaList = itemBelongDTOList.stream()
                .filter(m -> !m.getConfigType().equals(userWarehouseConfigType)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(notHaveRightsAreaList)) {
            return;
        }

        List<Long> notHaveRightsAreaIdList =
                notHaveRightsAreaList.stream().map(WarehouseAllocationItemBelongDTO::getRelateId).map(Long::valueOf)
                        .distinct().collect(Collectors.toList());

        List<LoactionDTO> areaList = locationPOMapper.findLocationByIds(notHaveRightsAreaIdList);
        String areaNames = areaList.stream().map(LoactionDTO::getName).distinct().collect(Collectors.joining(","));

        throw new BusinessValidateException(String.format("您没有%s分区操作权限，请检查关联货位是否错误", areaNames));
    }

    private List<String> getAreaIds(List<Long> locationIds) {
        List<LoactionDTO> locationDTOList = locationPOMapper.findLocationByIds(locationIds);

        List<Long> areaIds =
                locationDTOList.stream().map(LoactionDTO::getAreaId).distinct().collect(Collectors.toList());

        List<LoactionDTO> areaDTOList = locationPOMapper.findLocationByIds(areaIds);

        List<String> trueAreaIds =
                areaDTOList.stream().filter(m -> CategoryEnum.CARGO_AREA.getValue().intValue() == m.getCategory())
                        .map(LoactionDTO::getId).map(String::valueOf).collect(Collectors.toList());

        return trueAreaIds;
    }

    private boolean shouldValidateAdminUser(ProductLocationValidateOperationLegalBO bo) {
        AdminUserQueryDTO adminUserQueryDTO = new AdminUserQueryDTO();
        adminUserQueryDTO.setUserId(bo.getUserId());
        List<AdminUser> adminUserList = iAdminUserQueryService.listAdminUserWithAuth(adminUserQueryDTO);

        if (CollectionUtils.isEmpty(adminUserList)) {
            return Boolean.FALSE;
        }

        Optional<AdminUser> optional = adminUserList.stream().findFirst();
        if (BooleanUtils.isFalse(optional.isPresent())) {
            return Boolean.FALSE;
        }
        AdminUser adminUser = optional.get();
        List<AdminUserAuth> authList = adminUser.getAuthList();
        if (CollectionUtils.isEmpty(authList)) {
            return Boolean.FALSE;
        }

        List<AdminUserAuth> warehouseUserRoleList = authList.stream().filter(m -> Objects.nonNull(m.getOrg_Id()))
                .filter(m -> m.getOrg_Id().equals(bo.getWarehouseId()))
                .filter(m -> LEGAL_ROLE_LIST.contains(m.getUserRole())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(warehouseUserRoleList)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 填充货区信息
     *
     * @param bo
     */
    private void initAreaInfo(ProductLocationValidateOperationLegalBO bo) {
        if (CollectionUtils.isNotEmpty(bo.getAreaIds())) {
            return;
        }

        if (CollectionUtils.isEmpty(bo.getLocationIds())) {
            return;
        }

        List<LoactionDTO> loactionDTOList = locationPOMapper.findLocationByIds(bo.getLocationIds());
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return;
        }

        List<Long> areaIds = new ArrayList<>();

        List<LoactionDTO> areaList =
                loactionDTOList.stream().filter(m -> m.getCategory().byteValue() == CategoryEnum.CARGO_AREA.getValue())
                        .collect(Collectors.toList());
        List<LoactionDTO> locationList =
                loactionDTOList.stream().filter(m -> m.getCategory().byteValue() == CategoryEnum.CARGO_LOCATION.getValue())
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(areaList)) {
            areaIds.addAll(areaList.stream().map(LoactionDTO::getAreaId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(locationList)) {
            areaIds
                    .addAll(loactionDTOList.stream().map(LoactionDTO::getAreaId).distinct().collect(Collectors.toList()));
        }

        bo.setAreaIds(areaIds);
    }

}
