package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig;

public class ProductSkuAdditionProp {


    /**
     * 业务属性, 整件区=0, 拆零区=1
     */
    private Integer businessTag;
    /**
     * 货位类型, -1=未设置关联货位, 0=零捡位, 1=分拣位
     */
    private Integer locationType;

    public ProductSkuAdditionProp() {
    }

    public ProductSkuAdditionProp(Integer businessTag, Integer locationType) {
        this.businessTag = businessTag;
        this.locationType = locationType;
    }

    /**
     * 获取 业务属性 整件区=0 拆零区=1
     *
     * @return businessTag 业务属性 整件区=0 拆零区=1
     */
    public Integer getBusinessTag() {
        return this.businessTag;
    }

    /**
     * 设置 业务属性 整件区=0 拆零区=1
     *
     * @param businessTag 业务属性 整件区=0 拆零区=1
     */
    public void setBusinessTag(Integer businessTag) {
        this.businessTag = businessTag;
    }

    /**
     * 获取 货位类型 -1=未设置关联货位 0=零捡位 1=分拣位
     *
     * @return locationType 货位类型 -1=未设置关联货位 0=零捡位 1=分拣位
     */
    public Integer getLocationType() {
        return this.locationType;
    }

    /**
     * 设置 货位类型 -1=未设置关联货位 0=零捡位 1=分拣位
     *
     * @param locationType 货位类型 -1=未设置关联货位 0=零捡位 1=分拣位
     */
    public void setLocationType(Integer locationType) {
        this.locationType = locationType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"businessTag\":")
                .append(businessTag);

        sb.append(",\"locationType\":")
                .append(locationType);

        sb.append('}');
        return sb.toString();
    }
}
