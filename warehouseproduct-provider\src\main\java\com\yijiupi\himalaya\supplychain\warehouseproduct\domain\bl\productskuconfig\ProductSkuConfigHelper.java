package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigItemDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.productsync.domain.dao.ProductSkuConfigMapper;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigBusinessTagEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductConfigStorageAttributeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentSkuInfo;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentUpdateParam;
import com.yijiupi.himalaya.supplychain.productsync.dto.productskuconfig.ReplenishmentValueInfo;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductLocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ListSkuDetailParam;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductSkuPOConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageItemMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationType;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductFeatureEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.QuerySkuDetailsParamDTO;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.LOCATION_AREA;
import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.WORKING_PASSAGE;
import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType.DRINKING;
import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigType.REST;

/**
 * <AUTHOR>
 * @since 2024-08-09 09:34
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class ProductSkuConfigHelper {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSkuConfigHelper.class);

    @Resource
    private ProductSkuPOMapper productSkuPOMapper;

    @Resource
    private ProductSkuConfigMapper productSkuConfigMapper;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    @Resource
    private PassageItemMapper passageItemMapper;

    @Resource
    private ProductSkuPOConvertor productSkuPOConvertor;

    @Resource
    private ProductLocationBL productLocationBL;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;

    @Reference
    private IContentConfigurationService iContentConfigurationService;

    private static final Logger logger = LoggerFactory.getLogger(ProductSkuConfigHelper.class);

    public List<ProductSkuDTO> listSkuDetails(QuerySkuDetailsParamDTO queryDTO) {
        // 根据仓库id查询城市id
        Integer warehouseId = queryDTO.getWarehouseId();
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse == null) {
            logger.warn("ProductSkuQueryBL.listSkuDetails 根据仓findByProductSpecificationIds库id查询 未查询到仓库信息");
            return Collections.emptyList();
        }
        Byte isDelete = queryDTO.getIsDelete();
        Integer cityId = warehouse.getCityId();
        ListSkuDetailParam param = ListSkuDetailParam.of(cityId, queryDTO.getQuerySkuParamList(), isDelete);
        List<ProductSkuDTO> skus = productSkuPOConvertor.convert(productSkuPOMapper.listSkuDetails(param));
        return fillAdditionProp(skus, warehouseId);
    }


    /**
     * 填充额外属性<br>
     * <ul>
     *     <li>BusinessTag</li>
     *     <li>LocationType</li>
     * </ul>
     *
     * @param skus        sku 数据
     * @param warehouseId 仓库 id
     * @return 入参的 sku 数据
     */
    public List<ProductSkuDTO> fillAdditionProp(List<ProductSkuDTO> skus, Integer warehouseId) {
        if (CollectionUtils.isEmpty(skus)) {
            return skus;
        }
        // 没开分仓, 直接返回
        boolean enabledWarehouseSplit = warehouseAllocationConfigService.isEnabledWarehouseSplit(warehouseId);

        List<Long> skuIds = skus.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return skus;
        }

        //产品货位关联数据
        Map<Long, List<LoactionDTO>> skuLocationMap = productLocationBL.findLocationDTOBySkuId(warehouseId, skuIds);

        //没配置关联货位的情况，根据SKU配置的整件拆零属性来判断
        Map<Long, String> skuTagMap = productSkuConfigBL.findSkuConfigBySkuIdsAndWarehouseId(skuIds, warehouseId).stream()
                .collect(Collectors.toMap(
                        ProductSkuConfigDTO::getProductSkuId, it -> String.valueOf(it.getBusinessTag()),
                        (a, b) -> a
                ));

        // sku一级类目
        Map<Long, Long> skuStatisticsClassMap = skus.stream().filter(p -> p.getStatisticsClassId() != null)
                .collect(Collectors.toMap(p -> p.getProductSkuId(), p -> p.getStatisticsClassId(), (v1, v2) -> v1));
        logger.info("skuStatisticsClassMap结果:{}", JSON.toJSONString(skuStatisticsClassMap));

        // 休食特征类目配置
        String restCategoryValue =
                iContentConfigurationService.getContentValue("FeatureProductDisplayCategory", null, "");
        logger.info("restCategoryValue结果:{}", restCategoryValue);

        Map<Byte, Set<Long>> typeAreaMap ;
        Map<Byte, Set<Long>> typePassageMap;
        //没开分仓，不需要查询分仓数据，只查询货位关联数据
        if(enabledWarehouseSplit) {
            List<WarehouseAllocationConfigDTO> configs = warehouseAllocationConfigService.getConfigByWarehouseId(warehouseId);
            typeAreaMap = configs.stream()
                    .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToAreaId()));
            typePassageMap = configs.stream()
                    .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToPassageId()));
        }else {
            typeAreaMap = new HashMap<>();
            typePassageMap = new HashMap<>();
        }

        for (ProductSkuDTO sku : skus) {
            Long productSkuId = sku.getProductSkuId();
            //优先根据关联货位判断拆单
            Integer locationType = Optional.ofNullable(skuLocationMap.get(productSkuId))
                    .map(it -> getAreaTag(it, warehouseId, typeAreaMap, typePassageMap, enabledWarehouseSplit))
                    .orElse(LocationType.NONE.getValue());
            //如果没有关联货位，根据SKU配置的整件拆零属性来判断
            Optional.ofNullable(skuTagMap.get(productSkuId)).filter(NumberUtils::isDigits)
                    .map(Integer::valueOf)
                    .ifPresent(sku::setBusinessTag);
            //如果再没有，走到OMS默认逻辑，根据类目来拆单
            sku.setLocationType(locationType);
            // 根据计算填充分仓属性
            fillStorageAttributeByCal(sku, skuStatisticsClassMap, restCategoryValue);
        }
        return skus;
    }

    /**
     * 根据关联货位获取 sku 分仓类型
     *
     * @param warehouseId 仓库 id
     * @param locations   sku 关联货位
     * @param configs     分仓配置
     * @return sku 所属分仓类型
     */
    @Nonnull
    public Optional<Byte> getSkuSplitWarehouseType(Integer warehouseId, List<LoactionDTO> locations, List<WarehouseAllocationConfigDTO> configs) {
        Map<Byte, Set<Long>> typeAreaMap = configs.stream()
                .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToAreaId()));
        Map<Byte, Set<Long>> typePassageMap = configs.stream()
                .collect(Collectors.groupingBy(WarehouseAllocationConfigDTO::getConfigType, mappingToPassageId()));
        return getSkuSplitWarehouseType(warehouseId, locations, typeAreaMap, typePassageMap, true);
    }

    /**
     * 根据关联货位获取 sku 分仓类型
     *
     * @param warehouseId 仓库 id
     * @param locations   sku 关联货位
     * @return sku 所属分仓类型
     */
    @Nonnull
    public Optional<Byte> getSkuSplitWarehouseType(Integer warehouseId, List<LoactionDTO> locations, Map<Byte, Set<Long>> typeAreaMap
            , Map<Byte, Set<Long>> typePassageMap, boolean enabledWarehouseSplit) {
        // 没开分仓, 直接返回
        if (!enabledWarehouseSplit) {
            return Optional.empty();
        }
        // 拣货区和货位
        List<Byte> pickSubcategoryList =
                Arrays.asList(LocationEnum.分拣位.getType().byteValue(), LocationEnum.零拣位.getType().byteValue(),
                        LocationAreaEnum.拣货区.getType().byteValue(),LocationAreaEnum.零拣区.getType().byteValue());

        // 1、判断 sku 关联货位是否在指定货区
        Set<Long> skuAreaIds = locations.stream().map(LoactionDTO::getArea_Id).collect(Collectors.toSet());
        Set<Long> skuPickAreaIds = locations.stream().filter(it -> pickSubcategoryList.contains(it.getSubcategory()))
                .map(LoactionDTO::getArea_Id).collect(Collectors.toSet());
        // 优先使用拣货区和零拣区计算
        Optional<Byte> result = matchConfigType(typeAreaMap, skuPickAreaIds);
        if (result.isPresent()) {
            return result;
        }
        result = matchConfigType(typeAreaMap, skuAreaIds);
        if (result.isPresent()) {
            return result;
        }

        // 2、判断 sku 关联货位是否在指定通道中
        Set<String> locationIds = locations.stream().map(LoactionDTO::getId).map(String::valueOf).collect(Collectors.toSet());
        Set<String> skuPickLocationIds = locations.stream().filter(it -> pickSubcategoryList.contains(it.getSubcategory()))
                .map(p -> String.valueOf(p.getId())).collect(Collectors.toSet());
        // 优先使用分拣位和零拣位计算
        result = matchConfigTypeForPassage(typePassageMap, warehouseId, skuPickLocationIds);
        if (result.isPresent()) {
            return result;
        }
        result = matchConfigTypeForPassage(typePassageMap, warehouseId, locationIds);
        if (result.isPresent()) {
            return result;
        }

        // 如果没有命中分仓规则, 直接返回 -1
        return Optional.of(LocationType.NONE.getValue().byteValue());
    }

    private Optional<Byte> matchConfigType(Map<Byte, Set<Long>> configMap, Set<Long> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return Optional.empty();
        }
        for (Map.Entry<Byte, Set<Long>> entry : configMap.entrySet()) {
            Byte configType = entry.getKey();
            Set<Long> configIds = entry.getValue();
            if (CollectionUtils.isEmpty(configIds)) {
                continue;
            }
            if (!Sets.intersection(configIds, targetIds).isEmpty()) {
                return Optional.of(configType);
            }
        }
        return Optional.empty();
    }

    private Optional<Byte> matchConfigTypeForPassage(Map<Byte, Set<Long>> configMap, Integer warehouseId, Set<String> targetLocationIds) {
        if (CollectionUtils.isEmpty(targetLocationIds)) {
            return Optional.empty();
        }
        for (Map.Entry<Byte, Set<Long>> entry : configMap.entrySet()) {
            Byte configType = entry.getKey();
            Set<Long> passageIds = entry.getValue();
            if (CollectionUtils.isEmpty(passageIds)) {
                continue;
            }
            if (!passageItemMapper.selectByPassageIdsAndLocationIds(warehouseId, passageIds, targetLocationIds).isEmpty()) {
                return Optional.of(configType);
            }
        }
        return Optional.empty();
    }

    /**
     * 获取 sku 分区属性<br/>
     * <ul>
     *     <li>如果仓库开了分仓, 则通过分仓配置判断 sku 分区属性, sku 关联货位在分仓配置里的货区里或在分仓配置里的通道里, 那这个 sku 就是属于这个分仓</li>
     *     <li>如果没开分仓, 则通过关联货位是分拣位还是零拣位判断 sku 属于酒饮还是休食, 分拣位是酒饮, 零拣位是休食</li>
     * </ul>
     *
     * @param locations   sku 关联货位
     * @param warehouseId 仓库 id
     * @return sku 分区属性
     */
    private Integer getAreaTag(List<LoactionDTO> locations, Integer warehouseId, Map<Byte, Set<Long>> typeAreaMap, Map<Byte, Set<Long>> typePassageMap, boolean enabledWarehouseSplit) {
        if (CollectionUtils.isEmpty(locations)) {
            return LocationType.NONE.getValue();
        }
        // 优先尝试通过仓库分仓配置获取货位属性
        Integer areaTag = getSkuSplitWarehouseType(warehouseId, locations, typeAreaMap, typePassageMap, enabledWarehouseSplit)
                .map(it -> getAreaTag(locations, DRINKING.valueEquals(it), REST.valueEquals(it)))
                .orElse(null);
        if (areaTag != null) {
            logger.info("根据分仓配置计算得到货位属性为: {}", areaTag);
            return areaTag;
        }
        logger.info("开始根据关联货位计算货位属性");
        Set<LocationEnum> locationCategories = locations.stream().map(LoactionDTO::getSubcategory).map(Number::intValue)
                .map(LocationEnum::getEnum).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        boolean fenJian = locationCategories.contains(LocationEnum.分拣位);
        boolean lingJian = locationCategories.contains(LocationEnum.零拣位);
        return getAreaTag(locations, fenJian, lingJian);
    }

    /**
     * 提取出货区 id
     */
    private Collector<WarehouseAllocationConfigDTO, ?, Set<Long>> mappingToAreaId() {
        return StreamUtils.flatMapping(it -> it.getItems().stream()
                .filter(item -> LOCATION_AREA.valueEquals(item.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId)
                .filter(NumberUtils::isDigits).map(Long::valueOf), Collectors.toSet()
        );
    }

    /**
     * 提取出通道 id
     */
    private Collector<WarehouseAllocationConfigDTO, ?, Set<Long>> mappingToPassageId() {
        return StreamUtils.flatMapping(it -> it.getItems().stream()
                .filter(item -> WORKING_PASSAGE.valueEquals(item.getRelateType()))
                .map(WarehouseAllocationConfigItemDTO::getRelateId)
                .filter(NumberUtils::isDigits).map(Long::valueOf), Collectors.toSet()
        );
    }

    @Nonnull
    private Integer getAreaTag(List<LoactionDTO> locations, boolean fenJian, boolean lingJian) {
        if (fenJian && lingJian) {
            logger.info("sku 同时关联了分拣位和零捡位: {}", JSON.toJSONString(locations));
            return LocationType.NONE.getValue();
        }
        if (!fenJian && !lingJian) {
            logger.info("sku 既没有关联分拣位又没有关联零拣位: {}", JSON.toJSONString(locations));
            return LocationType.NONE.getValue();
        }
        return (lingJian ? LocationType.零拣位 : LocationType.分拣位).getValue();
    }

    private void fillStorageAttributeByCal(ProductSkuDTO skuDTO, Map<Long, Long> skuStatisticsClassMap,
                                           String restCategoryValue){
        // 1:酒饮,2:休食
        Byte storageAttribute = skuDTO.getStorageAttribute();
//        if(Objects.nonNull(storageAttribute)){
//            return;
//        }

        logger.info("skuDTO处理LocationType:{}", skuDTO.getLocationType());
        if (Objects.equals(skuDTO.getLocationType(), LocationType.分拣位.getValue().intValue())) {
            storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
        } else if (Objects.equals(skuDTO.getLocationType(), LocationType.零拣位.getValue().intValue())) {
            storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
        }

        if (Objects.isNull(storageAttribute)) {
            logger.info("skuDTO处理BusinessTag:{}", skuDTO.getBusinessTag());
            if (Objects.equals(skuDTO.getBusinessTag(), ProductConfigBusinessTagEnum.整件区.getValue().intValue())) {
                storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
            } else if (Objects.equals(skuDTO.getBusinessTag(),
                    ProductConfigBusinessTagEnum.拆零区.getValue().intValue())) {
                storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
            }
        }

        if (Objects.isNull(storageAttribute)) {
            if(skuStatisticsClassMap != null && skuStatisticsClassMap.size() > 0 && skuStatisticsClassMap.get(skuDTO.getProductSkuId()) != null){
                String skuStatisticsClassId = String.valueOf(skuStatisticsClassMap.get(skuDTO.getProductSkuId()));
                logger.info("skuDTO处理skuStatisticsClassId:{}", skuStatisticsClassId);
                if (restCategoryValue.contains(skuStatisticsClassId)) {
                    storageAttribute = ProductConfigStorageAttributeEnum.REST.getValue();
                } else {
                    storageAttribute = ProductConfigStorageAttributeEnum.DRINKING.getValue();
                }
            }
        }

        if(Objects.isNull(storageAttribute)){
            return;
        }

        skuDTO.setStorageAttribute(storageAttribute);
    }

    /**
     * 批量更新补货上下限
     *
     * @param param 更新入参
     * @param skus  sku 信息
     */
    public void batchUpdate(ReplenishmentUpdateParam param, List<ProductSkuDTO> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            logger.info("sku 信息为空, 跳过处理: {}", JSON.toJSONString(param));
            return;
        }
        ReplenishmentUpdateParam copied = param.copy();
        copied.setSkuInfos(skus.stream().map(ReplenishmentSkuInfo::of).collect(Collectors.toList()));
        batchUpdateReplenishmentInfo(copied);
    }

    /**
     * 批量更新补货上下限
     *
     * @param param 更新入参
     */
    public void batchUpdateReplenishmentInfo(ReplenishmentUpdateParam param) {
        logger.info("启动事务");
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            batchUpdate(param);
            logger.info("提交事务");
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            logger.error("批量更新数据失败, 回滚事务: ", e);
            platformTransactionManager.rollback(transactionStatus);
        }
    }

    private void batchUpdate(ReplenishmentUpdateParam param) {
        AssertUtils.notEmpty(param.getSkuInfos(), "sku 信息不能为空");
        Map<Byte, List<ReplenishmentSkuInfo>> skuFeatureMap = param.getSkuInfos().stream()
                .filter(it -> it.getProductFeature() != null)
                .collect(Collectors.groupingBy(ReplenishmentSkuInfo::getProductFeature));
        List<ReplenishmentSkuInfo> errorList = param.getSkuInfos().stream()
                .filter(it -> it.getProductFeature() == null).collect(Collectors.toList());
        if (!errorList.isEmpty()) {
            logger.warn("以下产品没有商品特征, 跳过处理: {}", JSON.toJSONString(errorList, SerializerFeature.WriteMapNullValue));
        }
        for (Map.Entry<Byte, List<ReplenishmentSkuInfo>> entry : skuFeatureMap.entrySet()) {
            handleForLarge(param, entry.getKey(), entry.getValue());
            handleForLittle(param, entry.getKey(), entry.getValue());
        }
    }

    /**
     * 处理 大件产品设置小件补货上下限的情况
     *
     * @param param 设置参数
     * @param key   商品特征
     * @param value 要设置的产品信息
     */
    private void handleForLarge(ReplenishmentUpdateParam param, Byte key, List<ReplenishmentSkuInfo> value) {
        ReplenishmentValueInfo maxReplenishment = param.getMaxReplenishment();
        ReplenishmentValueInfo minReplenishment = param.getMinReplenishment();
        // 若产品是 大件
        if (!(ProductFeatureEnum.大件.getType() == key)) {
            return;
        }
        for (ReplenishmentSkuInfo skuInfo : value) {
            ReplenishmentUpdateParam itemParam = param.copy(skuInfo.getSkuId());
            if (maxReplenishment != null) {
                BigDecimal maxValue = maxReplenishment.getValue();
                itemParam.setMax(maxValue);
                // 要设置的补货上限类型是 小件, 那就将其转换为 大件
                if (ProductFeatureEnum.小件.getType() == maxReplenishment.getType()) {
                    BigDecimal max = maxValue.divide(skuInfo.getPackageQuantity(), RoundingMode.UP)
                            .setScale(0, RoundingMode.UP);
                    itemParam.setMax(max);
                }
            }
            if (minReplenishment != null) {
                BigDecimal minValue = minReplenishment.getValue();
                itemParam.setMin(minValue);
                // 要设置的补货下限类型是 小件, 那就将其转换为 大件
                if (ProductFeatureEnum.小件.getType() == minReplenishment.getType()) {
                    BigDecimal min = minValue.divide(skuInfo.getPackageQuantity(), RoundingMode.UP)
                            .setScale(0, RoundingMode.UP);
                    itemParam.setMin(min);
                }
            }
            if (itemParam.needUpdate()) {
                productSkuConfigMapper.updateReplenishmentInfo(itemParam);
            }
        }
    }

    /**
     * 处理 小件产品设置大件补货上下限的情况
     *
     * @param param 设置参数
     * @param key   商品特征
     * @param value 要设置的产品信息
     */
    private void handleForLittle(ReplenishmentUpdateParam param, Byte key, List<ReplenishmentSkuInfo> value) {
        ReplenishmentValueInfo maxReplenishment = param.getMaxReplenishment();
        ReplenishmentValueInfo minReplenishment = param.getMinReplenishment();
        // 若产品是 小件
        if (!(ProductFeatureEnum.小件.getType() == key)) {
            return;
        }
        for (ReplenishmentSkuInfo skuInfo : value) {
            ReplenishmentUpdateParam itemParam = param.copy(skuInfo.getSkuId());
            if (maxReplenishment != null) {
                BigDecimal maxValue = maxReplenishment.getValue();
                itemParam.setMax(maxValue);
                // 要设置的补货上限类型是 大件, 那就将其转换为 小件
                if (ProductFeatureEnum.大件.getType() == maxReplenishment.getType()) {
                    itemParam.setMax(maxValue.multiply(skuInfo.getPackageQuantity()));
                }
            }
            if (minReplenishment != null) {
                BigDecimal minValue = minReplenishment.getValue();
                itemParam.setMin(minValue);
                // 要设置的补货下限类型是 大件, 那就将其转换为 小件
                if (ProductFeatureEnum.大件.getType() == minReplenishment.getType()) {
                    itemParam.setMin(minReplenishment.getValue().multiply(skuInfo.getPackageQuantity()));
                }
            }
            if (itemParam.needUpdate()) {
                productSkuConfigMapper.updateReplenishmentInfo(itemParam);
            }
        }
    }
}
