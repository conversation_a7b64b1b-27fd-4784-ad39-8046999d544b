package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.assignment.service.ITodoTaskCallbackService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductCodeWhitelistPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductCodeWhitelistDTO;
import com.yijiupi.himalaya.supplychain.waves.utils.StreamUtils;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-12-05 11:56
 **/
@Service
public class ProductCodeWhitelistBL {

    @Resource
    private ProductCodeWhitelistPOMapper productCodeWhitelistPOMapper;

    @Resource
    private ProductSkuPOMapper productSkuPOMapper;

    @Reference
    private ITodoTaskCallbackService todoTaskCallbackService;

    public void saveOrUpdateProductCodeWhitelist(ProductCodeWhitelistDTO dto) {
        Integer warehouseId = dto.getWarehouseId();
        Integer userId = dto.getUserId();
        Long skuId = dto.getSkuId();
        // 通过 skuId 查一下 sku, 填充数据
        ProductSkuPO sku = productSkuPOMapper.selectBySkuId(skuId);
        Long specId = sku.getProductSpecification_Id();
        ProductCodeWhitelistPO exists = productCodeWhitelistPOMapper.selectByWarehouseIdAndSkuId(warehouseId, skuId);
        if (exists == null) {
            exists = new ProductCodeWhitelistPO();
            exists.setId(UUIDGenerator.getUUID(ProductCodeWhitelistPO.class.getName()));
            exists.setWarehouseId(warehouseId);
            exists.setProductName(sku.getName());
            exists.setSkuId(skuId);
            exists.setSpecificationId(specId);
            exists.setCreateTime(null);
            exists.setCreateUserId(userId);
            exists.setLastUpdateTime(null);
            exists.setLastUpdateUserId(userId);
            productCodeWhitelistPOMapper.insertSelective(exists);
        } else {
            // 若已存在, 则更新最后更新时间和操作人
            ProductCodeWhitelistPO po = new ProductCodeWhitelistPO();
            po.setId(exists.getId());
            po.setLastUpdateUserId(userId);
            po.setLastUpdateTime(new Date());
            productCodeWhitelistPOMapper.updateByPrimaryKeySelective(po);
        }
        if (specId > Integer.MAX_VALUE) {
            return;
        }
        todoTaskCallbackService.forceCompleteTodoTaskBySpecId(Collections.singletonList(specId.intValue()));
    }

    public List<ProductCodeWhitelistDTO> selectValidWhitelist(Integer warehouseId, Collection<Long> skuId) {
        if (CollectionUtils.isEmpty(skuId)) {
            return Collections.emptyList();
        }
        return productCodeWhitelistPOMapper.selectValidWhitelist(warehouseId, skuId).stream()
                .map(it -> StreamUtils.copy(it, ProductCodeWhitelistDTO::new))
                .collect(Collectors.toList());
    }

}
