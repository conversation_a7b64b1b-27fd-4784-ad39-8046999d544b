package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskAssignDTO;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskCreateParam;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskTypeDTO;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskTypeQueryDTO;
import com.yijiupi.himalaya.assignment.enums.todo.AssignType;
import com.yijiupi.himalaya.assignment.enums.todo.TodoTaskRank;
import com.yijiupi.himalaya.assignment.service.ITodoTaskService;
import com.yijiupi.himalaya.assignment.service.ITodoTaskTypeService;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationConfigItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productskuconfig.ProductSkuConfigHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.variable.VariableManager;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationType;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.enums.config.WarehouseAllocationConfigRelateType.PRODUCT_FEATURE;

/**
 * <AUTHOR>
 * @since 2024-10-23 13:59
 **/
@Service
public class TodoTaskBL {

    @Resource
    private VariableManager variableManager;

    @Resource
    private ProductLocationPOMapper productLocationPOMapper;

    @Resource
    private ProductSkuPOMapper productSkuPOMapper;

    @Resource
    private ProductSkuConfigHelper productSkuConfigHelper;

    @Reference
    private ITodoTaskService todoTaskService;

    @Reference
    private ITodoTaskTypeService todoTaskTypeService;

    @Reference
    private IWarehouseAllocationConfigService warehouseAllocationConfigService;

    private static final String TASK_JOB_RUN_OPT = "TaskJobRunOpt";

    /**
     * 任务类型编码 -> 任务类型的 cache
     */
    private static final Cache<String, TodoTaskTypeDTO> taskTypeCache = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    private static final Logger logger = LoggerFactory.getLogger(TodoTaskBL.class);

    public Builder newBuilder() {
        return new Builder();
    }

    /**
     * 创建任务, 会根据配置决定是否需要创建任务
     *
     * @param tasks 任务信息
     */
    public void createTask(List<TodoTaskCreateParam> tasks) {
        Map<String, List<TodoTaskCreateParam>> taskMap = tasks.stream()
                .collect(Collectors.groupingBy(TodoTaskCreateParam::getTaskTypeCode));
        Map<String, String> variableMap = variableManager.getObjectVariable(TASK_JOB_RUN_OPT, null);
        logger.info("准备保存 {} 条任务数据, 配置信息: {}", tasks.size(), variableMap);
        for (Map.Entry<String, List<TodoTaskCreateParam>> entry : taskMap.entrySet()) {
            String type = entry.getKey();
            List<TodoTaskCreateParam> typeTask = entry.getValue();
            if (!Boolean.parseBoolean(variableMap.get(type))) {
                logger.info("根据配置, 不生成 {} 类型任务", type);
                continue;
            }
            if (typeTask.isEmpty()) {
                logger.info("没有需要创建任务的数据, 跳过后续处理");
                return;
            }
            todoTaskService.createTodoTaskBatch(typeTask);
            logger.info("{} 任务创建完毕", type);
        }
    }

    public TodoTaskTypeDTO getTaskTypeByCode(String taskTypeCode) {
        return taskTypeCache.get(taskTypeCode, key -> {
            TodoTaskTypeQueryDTO query = new TodoTaskTypeQueryDTO();
            query.setIsDeleted(YesOrNoEnum.NO.getValue().byteValue());
            query.setTaskTypeNo(key);
            List<TodoTaskTypeDTO> types = todoTaskTypeService.pageListTodoTaskType(query).getDataList();
            if (CollectionUtils.isEmpty(types)) {
                return null;
            }
            return types.get(0);
        });
    }

    @SuppressWarnings("unused")
    public class Builder {
        private Set<Integer> assignIds = new HashSet<>();
        private Set<String> assignRoles = new HashSet<>();
        private String taskTypeCode;
        private AssignType assignType;
        private int overdueDays;
        private String businessNo;
        private TodoTaskRank taskRank;
        private String taskDetail;
        private Date startTime;
        /**
         * 任务属性, 0=未指定、1=酒饮、2=休食
         */
        private Integer taskProperty;

        public Builder setAssignIds(Set<Integer> assignIds) {
            this.assignIds = assignIds;
            return this;
        }

        public Builder setAssignRoles(Set<String> assignRoles) {
            this.assignRoles = assignRoles;
            return this;
        }

        public Builder addAssignId(Integer assignId) {
            this.assignIds.add(assignId);
            return this;
        }

        public Builder addAssignRole(String assignRole) {
            this.assignRoles.add(assignRole);
            return this;
        }

        public Builder setTaskTypeCode(String taskTypeCode) {
            this.taskTypeCode = taskTypeCode;
            return this;
        }

        public Builder setAssignType(AssignType assignType) {
            this.assignType = assignType;
            return this;
        }

        public Builder setOverdueDays(int overdueDays) {
            this.overdueDays = overdueDays;
            return this;
        }

        public Builder setBusinessNo(String businessNo) {
            this.businessNo = businessNo;
            return this;
        }

        public Builder setTaskRank(TodoTaskRank taskRank) {
            this.taskRank = taskRank;
            return this;
        }

        public Builder setTaskDetail(String taskDetail) {
            this.taskDetail = taskDetail;
            return this;
        }

        public Builder setStartTime(Date startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder setTaskProperty(Integer taskProperty) {
            this.taskProperty = taskProperty;
            return this;
        }

        @Nullable
        public TodoTaskCreateParam build() {
            AssertUtils.notNull(taskTypeCode, "任务类型编码不能为空");
            AssertUtils.notNull(taskRank, "任务优先级不能为空");
            AssertUtils.notNull(assignType, "分配类型不能为空");
            AssertUtils.notNull(startTime, "开始时间不能为空");
            AssertUtils.notEmpty(assignIds, "分配对象不能为空");
            if (assignType.equals(AssignType.WAREHOUSE)) {
                AssertUtils.notEmpty(assignRoles, "分配角色不能为空");
            }
            TodoTaskTypeDTO taskType = getTaskTypeByCode(taskTypeCode);
            if (taskType == null) {
                logger.info("没有 {} 的任务类型, 跳过后续处理", taskTypeCode);
                return null;
            }
            List<TodoTaskAssignDTO> assignInfos = assignIds.stream()
                    .flatMap(it -> assignRoles.stream().map(role -> TodoTaskAssignDTO.of(it, role)))
                    .collect(Collectors.toList());
            TodoTaskCreateParam create = new TodoTaskCreateParam();
            create.setTaskTypeId(taskType.getId());
            create.setTaskTypeCode(taskTypeCode);
            create.setTaskRank(taskRank.getValue().intValue());
            create.setBusinessNo(businessNo);
            create.setBeginTime(startTime);
            create.setOverdueTime(DateUtils.getAddHour(startTime, overdueDays * 24));
            create.setAssignInfo(assignInfos);
            create.setAssignType(assignType.getValue().intValue());
            create.setTaskDetail(taskDetail);
            // 0 表示没有分仓属性
            create.setTaskProperty(taskProperty == null ? 0 : taskProperty);
            return create;
        }

        public void buildAndCreate() {
            TodoTaskCreateParam create = build();
            if (create == null) {
                return;
            }
            todoTaskService.createTodoTask(create);
        }

    }

    /**
     * 通过仓库 id、skuId 计算其分仓属性
     *
     * @param warehouseId 仓库 id
     * @param skuIds      skuId
     * @return 分仓属性, key 是 skuId, value 是 分仓属性
     */
    public Map<Long, Integer> calcSkuTodoTaskProperty(Integer warehouseId, Collection<Long> skuIds) {
        if (!warehouseAllocationConfigService.isEnabledWarehouseSplit(warehouseId) || CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        List<WarehouseAllocationConfigDTO> configs = warehouseAllocationConfigService.getConfigByWarehouseId(warehouseId);
        if (configs.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, List<LoactionDTO>> skuLocationMap = productLocationPOMapper.findLocationBySkuId(skuIds, warehouseId).stream()
                .collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId, mappingToLocationDTO()));
        Map<Long, ProductSkuPO> skuMap = productSkuPOMapper.findBySku(skuIds).stream()
                .collect(Collectors.toMap(ProductSkuPO::getProductSku_Id, Function.identity(), (a, b) -> a));
        return skuIds.stream().collect(Collectors.toMap(Function.identity(),
                it -> calcSkuSplitProperty(skuMap.get(it), skuLocationMap.get(it), configs, warehouseId))
        );
    }

    private static Collector<ProductLoactionItemDTO, ?, List<LoactionDTO>> mappingToLocationDTO() {
        return Collectors.mapping(it -> {
            LoactionDTO dto = new LoactionDTO();
            dto.setId(it.getLocationId());
            dto.setArea_Id(it.getAreaId());
            return dto;
        }, Collectors.toList());
    }

    @Nonnull
    private Integer calcSkuSplitProperty(ProductSkuPO sku, List<LoactionDTO> locations, List<WarehouseAllocationConfigDTO> configs, Integer warehouseId) {
        if (sku == null) {
            return 0;
        }
        // 优先根据关联货位确定分仓
        if (!CollectionUtils.isEmpty(locations)) {
            // 上边已经判断过了, 走到这里来必定是开启分仓了的
            Byte splitProperty = productSkuConfigHelper.getSkuSplitWarehouseType(warehouseId, locations, configs)
                    .orElse(LocationType.NONE.getValue().byteValue());
            // 如果根据分仓配置成功确定分仓, 那就直接返回
            if (!LocationType.NONE.valueEquals(splitProperty)) {
                return splitProperty.intValue();
            }
        }
        // 如果无法根据货位确定分仓, 那就按照产品特征确定分仓
        for (WarehouseAllocationConfigDTO config : configs) {
            Set<Byte> productFeatures = config.getItems().stream()
                    .filter(it -> PRODUCT_FEATURE.valueEquals(it.getRelateType()))
                    .map(WarehouseAllocationConfigItemDTO::getRelateId).filter(NumberUtils::isDigits)
                    .map(Byte::valueOf).collect(Collectors.toSet());
            if (productFeatures.contains(sku.getProductFeature())) {
                return config.getConfigType().intValue();
            }
        }
        return 0;
    }

}

