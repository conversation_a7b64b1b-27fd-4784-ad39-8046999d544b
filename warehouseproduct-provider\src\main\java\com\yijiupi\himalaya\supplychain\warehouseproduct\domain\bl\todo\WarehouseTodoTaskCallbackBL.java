package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskDTO;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskOperateParam;
import com.yijiupi.himalaya.assignment.enums.todo.TodoTaskState;
import com.yijiupi.himalaya.assignment.service.ITodoTaskService;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductCodeListDTO;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuServiceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.cache.ScmProductCache;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ErrorBoxCodeBL.TMJZ_CODE;

/**
 * <AUTHOR>
 * @since 2024-11-19 16:26
 **/
@Service
public class WarehouseTodoTaskCallbackBL {

    @Resource
    private TodoTaskBL todoTaskBL;

    @Resource
    private ScmProductCache scmProductCache;

    @Resource
    private ProductSkuServiceBL productSkuServiceBL;

    @Reference
    private ITodoTaskService todoTaskService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    private static final Pattern pattern = Pattern.compile("\\(系统条码:\\s*([\\d,、]+)\\)");

    private static final Logger logger = LoggerFactory.getLogger(WarehouseTodoTaskCallbackBL.class);

    public void checkProductCode(Integer warehouseId, Long specId, Long skuId) {
        if (specId > Integer.MAX_VALUE) {
            return;
        }
        TodoTaskOperateParam param = new TodoTaskOperateParam();
        param.setTaskStatusList(new ArrayList<>(TodoTaskState.unCompletedStates));
        param.setTaskTypeId(todoTaskBL.getTaskTypeByCode(TMJZ_CODE).getId());
        param.setBusinessNo(String.format("%s,%s,%s", warehouseId, specId, skuId));
        List<TodoTaskDTO> tasks = todoTaskService.pageListTodoTask(param).getDataList();
        if (CollectionUtils.isEmpty(tasks)) {
            logger.info("找不到待办任务, 跳过处理: {}", JSON.toJSONString(param));
            return;
        }
        Set<Integer> specIds = Collections.singleton(specId.intValue());
        ScmProductCodeListDTO code = scmProductCache.get(specIds).get(specId.intValue());
        if (code == null) {
            logger.info("没有从缓存中查询到条码, 跳过后续处理");
            return;
        }
        Integer cityId = warehouseQueryService.findWarehouseById(warehouseId).getCityId();
        ProductCodeDTO codeDTO = productSkuServiceBL.getProductCodeDTO(code, cityId);
        for (TodoTaskDTO task : tasks) {
            Matcher matcher = pattern.matcher(task.getTaskDetail());
            if (!matcher.find()) {
                continue;
            }
            List<String> oldBarCode = Stream.of(matcher.group(1).split("、")).map(String::trim).collect(Collectors.toList());
            // 断言条码发生变化
            AssertUtils.isTrue(!oldBarCode.equals(codeDTO.getUnitCode()), "条码矫正尚未完成");
        }
    }

    public Set<String> checkCanBeCompleted(Set<String> businessNos) {
        // businessNo 是由 仓库 id,规格 id,skuId 组成的
        return businessNos.stream().filter(it -> {
            try {
                String[] split = it.split(",");
                int warehouseId = Integer.parseInt(split[0].trim());
                long specId = Long.parseLong(split[1].trim());
                long skuId = Long.parseLong(split[2].trim());
                checkProductCode(warehouseId, specId, skuId);
                return true;
            } catch (DataValidateException e) {
                logger.info("businessNo: {}, {}", it, e.getMessage());
            } catch (Exception e) {
                logger.warn("businessNo: {}, {}", it, e.getMessage(), e);
            }
            return false;
        }).collect(Collectors.toSet());
    }

}
