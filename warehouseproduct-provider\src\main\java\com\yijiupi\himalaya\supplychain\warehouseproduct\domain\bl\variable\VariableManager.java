package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.variable;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024-10-25 17:43
 **/
@Service
public class VariableManager {

    @Reference
    private IVariableValueService variableValueService;

    private static final Collector<Map.Entry<?, ?>, ?, Map<String, String>> STRING_MAP_COLLECTOR = Collectors.toMap(
            it -> String.valueOf(it.getKey()), it -> String.valueOf(it.getValue())
    );

    public Map<String, String> getObjectVariable(String key, Integer warehouseId) {
        return getObjectVariable(key, warehouseId, null);
    }

    public Set<Long> getListVariable(String key, Integer warehouseId) {
        return getListVariable(key, warehouseId, null);
    }

    /**
     * 获取 wms 网站上配置的 对象 类型参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @param orgId       城市 id
     * @return 参数内容
     */
    public Map<String, String> getObjectVariable(String key, Integer warehouseId, Integer orgId) {
        return getVariableByKey(key, warehouseId, orgId).map(VariableDefAndValueDTO::getVariableData)
                .filter(StringUtils::hasText).map(JSONObject::parseObject)
                .map(it -> it.entrySet().stream().collect(STRING_MAP_COLLECTOR))
                .orElseGet(Collections::emptyMap);
    }

    /**
     * 获取 wms 网站上配置的 以英文逗号分割的数组 参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @param orgId       城市 id
     * @return 参数内容
     */
    public Set<Long> getListVariable(String key, Integer warehouseId, Integer orgId) {
        return getVariableByKey(key, warehouseId, orgId).map(VariableDefAndValueDTO::getVariableData)
                .filter(StringUtils::hasText).map(it -> it.split(",")).map(Stream::of)
                .map(it -> it.filter(NumberUtils::isNumber).map(Long::valueOf).collect(Collectors.toSet()))
                .orElseGet(Collections::emptySet);

    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @param orgId       城市 id
     * @return 查询结果, 可能为空
     */
    @NotNull
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key, Integer warehouseId, Integer orgId) {
        VariableValueQueryDTO queryDTO = new VariableValueQueryDTO();
        queryDTO.setVariableKey(key);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        return Optional.ofNullable(variableValueService.detailVariable(queryDTO));
    }

}
