package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.vessel;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.TransferVesselStoreBO;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
@Service
public class VesselTransferAsyncBL {

    @Autowired
    private VesselTransferBL vesselTransferBL;
    private static final Logger LOGGER = LoggerFactory.getLogger(VesselTransferAsyncBL.class);



    public static final ExecutorService EXECUTOR =
            new ThreadPoolExecutor(10, 20, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(),
                    new DefaultThreadFactory("VesselTransferAsyncBL"), new ThreadPoolExecutor.CallerRunsPolicy());

    public void transferToRelatedLocation(TransferVesselStoreBO transferVesselStoreBO) {
        CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(
                () -> vesselTransferBL.transferToRelatedLocation(transferVesselStoreBO), EXECUTOR);
        try {
            boolean result = completableFuture.get();
            if (Boolean.TRUE.equals(result)) {
                LOGGER.warn("同步成功，{}", JSON.toJSONString(transferVesselStoreBO));
            }
        } catch (Exception e) {
            LOGGER.warn("出库批次同步报错，入参 : " + JSON.toJSONString(transferVesselStoreBO), e);
        }
    }
}
