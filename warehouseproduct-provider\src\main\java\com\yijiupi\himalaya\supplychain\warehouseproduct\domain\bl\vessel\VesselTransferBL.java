package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.vessel;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.CheckBatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.StoreTransferOrderItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.StoreTransferStateEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IReplenishmentQueryService;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IStoreTransferOrderService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductLocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StoreWareHouseBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.NotifyWCSBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.TransferVesselBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.TransferVesselStoreBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.TransferVesselToLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskItemManageService;
import com.yijiupi.himalaya.supplychain.waves.dto.batchitem.UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IWCSLocationModService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Service
public class VesselTransferBL {

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    @Reference
    private IBatchInventoryManageService iBatchInventoryManageService;
    @Reference(timeout = 30000)
    private IStoreTransferOrderService iStoreTransferOrderService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference(timeout = 30000)
    private IWCSLocationModService iWCSLocationModService;
    @Reference(timeout = 30000)
    private IReplenishmentQueryService iReplenishmentQueryService;
    @Reference
    private IBatchTaskItemManageService iBatchTaskItemManageService;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private StoreWareHouseBL storeWareHouseBL;
    @Autowired
    private PassageMapper passageMapper;
    @Autowired
    private PassageItemMapper passageItemMapper;
    @Autowired
    private LocationPOMapper locationPOMapper;
    @Autowired
    private NotifyWCSBL notifyWCSBL;
    @Autowired
    private ProductStoreBatchPOMapper productStoreBatchPOMapper;
    @Autowired
    private ProductLocationBL productLocationBL;
    @Autowired
    private VesselInfoPOMapper vesselInfoPOMapper;
    @Autowired
    private ProductLocationPOMapper productLocationPOMapper;

    private static final String VESSEL_PREFIX = "WL-";
    private static final Logger LOGGER = LoggerFactory.getLogger(VesselTransferBL.class);


    /**
     * 容器库存移库至容器关联货位
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean transferToRelatedLocation(TransferVesselStoreBO transferVesselStoreBO) {

        TransferVesselBO bo = getTransferVesselBO(transferVesselStoreBO);
        if (Objects.isNull(bo) || Objects.isNull(bo.getFromLocationPO()) || Objects.isNull(bo.getToLocationPO())) {
            return Boolean.TRUE;
        }
        bo.setWarehouseId(transferVesselStoreBO.getWarehouseId());
        bo.setCityId(transferVesselStoreBO.getCityId());
        // 组装移库数据进行移库
        updateStoreTransferNoOrder(bo);

        return Boolean.TRUE;
    }

    private TransferVesselBO getTransferVesselBO(TransferVesselStoreBO transferVesselStoreBO) {
        if (TransferVesselToLocationDTO.TRANSFER_FROM_VESSEL.equals(transferVesselStoreBO.getTransferFromTo())) {
            LocationPO fromLocationPO = locationPOMapper.findLocationById(transferVesselStoreBO.getVesselId());
            LocationPO toLocationPO = locationPOMapper.findLocationById(fromLocationPO.getArea_Id());
            if (Objects.nonNull(toLocationPO) && toLocationPO.getId().equals(fromLocationPO.getId())) {
                return null;
            }

            return new TransferVesselBO(fromLocationPO, toLocationPO, transferVesselStoreBO.getUserId());
        }

        LocationPO toLocationPO = locationPOMapper.findLocationById(transferVesselStoreBO.getVesselId());
        LocationPO fromLocationPO = locationPOMapper.findLocationById(toLocationPO.getArea_Id());

        return new TransferVesselBO(fromLocationPO, toLocationPO, transferVesselStoreBO.getUserId());
    }


    private LocationPO getLocationPO(LocationPO vesselLocationPO, Byte transferType) {
        if (Objects.isNull(transferType)) {
            return locationPOMapper.findLocationById(vesselLocationPO.getArea_Id());
        }
        if (TransferVesselToLocationDTO.TRANSFER_TYPE_LOCATION.equals(transferType)) {
            return locationPOMapper.findLocationById(vesselLocationPO.getArea_Id());
        }
        if (TransferVesselToLocationDTO.TRANSFER_TYPE_NAME.equals(transferType)) {
            String locationName = vesselLocationPO.getName().replace("WL-", "");
            return locationPOMapper.findLocationByName(locationName, vesselLocationPO.getWarehouse_Id());
        }

        return locationPOMapper.findLocationById(vesselLocationPO.getArea_Id());
    }

    public void updateStoreTransferNoOrder(TransferVesselBO bo) {
        LOGGER.info("VesselBL.updateStoreTransferNoOrder 移库入参：{}", JSON.toJSONString(bo));
        if (Objects.isNull(bo.getFromLocationPO()) || Objects.isNull(bo.getToLocationPO())) {
            return;
        }
        try {
            LocationPO fromLocation = bo.getFromLocationPO();
            LocationPO toLocation = bo.getToLocationPO();
            Integer warehouseId = fromLocation.getWarehouse_Id();

            // 按货位查询批次库存
            BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
            batchInventoryQueryDTO.setWarehouseId(fromLocation.getWarehouse_Id());
            batchInventoryQueryDTO.setLocationIds(Collections.singletonList(fromLocation.getId()));
            batchInventoryQueryDTO.setPageNum(1);
            batchInventoryQueryDTO.setPageSize(Integer.MAX_VALUE);
            PageList<BatchInventoryDTO> batchInventoryPageList =
                    iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);
            LOGGER.info("VesselBL.addVesselByLocationId 查询批次库存入参：{},结果：{}", JSON.toJSONString(batchInventoryQueryDTO),
                    JSON.toJSONString(batchInventoryPageList));
            if (null == batchInventoryPageList || CollectionUtils.isEmpty(batchInventoryPageList.getDataList())) {
                return;
            }

            List<BatchInventoryDTO> batchInventoryDTOList = batchInventoryPageList.getDataList();
//            List<BatchInventoryDTO> batchInventoryDTOList = getLegalBatchInventoryList(batchInventoryPageList, toLocation);
//            if (CollectionUtils.isEmpty(batchInventoryDTOList)) {
//                return;
//            }

            Map<Integer, String> warehosueMap = getWarehouseMap(Collections.singletonList(warehouseId));
            // 无单移库
            List<StoreTransferOrderItemDTO> storeTransferOrderItemDTOS = new ArrayList<>();
            StoreTransferOrderDTO storeTransferOrderDTO = new StoreTransferOrderDTO();
            storeTransferOrderDTO.setOrg_id(fromLocation.getCity_Id());
            storeTransferOrderDTO.setState(StoreTransferStateEnum.待移库.getType());
            storeTransferOrderDTO.setTransferType(StoreTransferEnum.人工移库.getType());
            storeTransferOrderDTO.setStartTime(new Date());
            storeTransferOrderDTO.setWarehouse_Id(warehouseId);
            storeTransferOrderDTO
                    .setWarehouseName(warehosueMap != null ? warehosueMap.get(warehouseId) : null);
            storeTransferOrderDTO.setSorter_id(bo.getUserId());
            storeTransferOrderDTO.setSorterName(bo.getUserName());
            storeTransferOrderDTO.setRemark("");
            storeTransferOrderDTO.setCreateUser(bo.getUserName());
            storeTransferOrderDTO.setLastupdateuser(bo.getUserName());

            batchInventoryDTOList.stream().forEach(p -> {
                Long toLocationId = toLocation.getId();
                String toLocationName = toLocation.getName();

                // 检查产品能否存放到某个货位上
                CheckBatchInventoryDTO checkBatchInventoryDTO = new CheckBatchInventoryDTO();
                checkBatchInventoryDTO.setLocationId(toLocationId);
                checkBatchInventoryDTO.setProductSkuId(p.getProductSkuId());
                checkBatchInventoryDTO.setLocationName(toLocationName);
                checkBatchInventoryDTO.setChannel(p.getChannel());
                checkBatchInventoryDTO.setBatchTime(p.getBatchTime());
                checkBatchInventoryDTO.setProductionDate(p.getProductionDate());
                checkBatchInventoryDTO.setWarehouseId(p.getWarehouseId());
                // checkBatchInventoryDTO.setChaosPut((byte) 0);
                // checkBatchInventoryDTO.setChaosBatch(vesselLocation.get(0).getIsChaosBatch());
                String msg = "";
                Boolean isCanIn = iBatchInventoryQueryService.checkIsCanPutIntoLocation(checkBatchInventoryDTO, msg);
                if (!isCanIn) {
                    throw new BusinessValidateException(msg + "移库目标货位：" + toLocationName);
                }

                StoreTransferOrderItemDTO storeTransferOrderItemDTO = new StoreTransferOrderItemDTO();
                storeTransferOrderItemDTO.setOrg_id(fromLocation.getCity_Id());
                storeTransferOrderItemDTO.setState(StoreTransferStateEnum.待移库.getType());
                storeTransferOrderItemDTO.setOwnerName(p.getOwnerName());
                storeTransferOrderItemDTO.setSkuId(p.getProductSkuId());
                storeTransferOrderItemDTO.setProductName(p.getProductSkuName());
                storeTransferOrderItemDTO.setSpecName(p.getSpecificationName());
                storeTransferOrderItemDTO.setSpecQuantity(p.getPackageQuantity());
                storeTransferOrderItemDTO.setPackageName(p.getPackageName());
                storeTransferOrderItemDTO.setPackageCount(p.getStoreCountMax());
                storeTransferOrderItemDTO.setUnitName(p.getUnitName());
                storeTransferOrderItemDTO.setUnitCount(p.getStoreCountMin());
                storeTransferOrderItemDTO.setUnitTotalCount(p.getStoreTotalCount());
                storeTransferOrderItemDTO.setOverMovePackageCount(p.getStoreCountMax());
                storeTransferOrderItemDTO.setOverMoveUnitCount(p.getStoreCountMin());
                storeTransferOrderItemDTO.setFromLocation_id(p.getLocationId());
                storeTransferOrderItemDTO.setFromLocationName(p.getLocationName());
                storeTransferOrderItemDTO.setToLocation_id(toLocationId);
                storeTransferOrderItemDTO.setToLocationName(toLocationName);
                storeTransferOrderItemDTO.setToChannel(null != p.getChannel() ? p.getChannel().toString() : "");
                storeTransferOrderItemDTO.setCreateUser(bo.getUserName());
                storeTransferOrderItemDTO.setLastupdateuser(bo.getUserName());
                storeTransferOrderItemDTO.setProductionDate(p.getProductionDate());
                storeTransferOrderItemDTO.setBatchTime(p.getBatchTime());
                storeTransferOrderItemDTO.setProductSpecificationId(p.getProductSpecificationId());
                storeTransferOrderItemDTO.setSecOwnerId(p.getSecOwnerId());
                // "isStockAgeStrategy": false,
                storeTransferOrderItemDTO.setBatchAttributeInfoNo(p.getBatchAttributeInfoNo());
                storeTransferOrderItemDTOS.add(storeTransferOrderItemDTO);
            });

            // 物料箱不能混放
//            if (dto.getTransferToVessel() == null || dto.getTransferToVessel()) {
//                Map<Long, String> noMoveMap = new HashMap<>(16);
//                Map<Long, List<StoreTransferOrderItemDTO>> toLocationIdMap = storeTransferOrderItemDTOS.stream()
//                        .collect(Collectors.groupingBy(StoreTransferOrderItemDTO::getToLocation_id));
//                toLocationIdMap.forEach((toLocationId, itemList) -> {
//                    List<Long> skuIds = itemList.stream().map(StoreTransferOrderItemDTO::getSkuId).distinct()
//                            .collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(skuIds) && skuIds.size() > 1) {
//                        noMoveMap.put(toLocationId, itemList.get(0).getToLocationName());
//                    }
//                });
//                LOGGER.info("VesselBL.updateStoreTranferNoOrder 物料箱不允许放不同产品：{}", JSON.toJSONString(noMoveMap));
//                if (noMoveMap != null && noMoveMap.size() > 0) {
//                    // // 过滤无法移库物料箱
//                    // storeTransferOrderItemDTOS.removeIf(p ->
//                    // !StringUtils.isEmpty(noMoveMap.get(p.getToLocation_id())));
//                    throw new BusinessValidateException("物料箱不允许放不同产品，物料箱：{}！" + JSON.toJSONString(noMoveMap));
//                }
//            }

            storeTransferOrderDTO.setStoreTransferOrderItemDTOS(storeTransferOrderItemDTOS);
            LOGGER.info("VesselBL.updateStoreTransferNoOrder 移库参数组装：{}", JSON.toJSONString(storeTransferOrderDTO));

            checkPrice(storeTransferOrderDTO);
            iStoreTransferOrderService.updateStoreTranferNoOrder(storeTransferOrderDTO);

            UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO batchDTO = new UpdateNotPickedBatchTaskItemLocationFromOldToNewDTO();
            batchDTO.setNewLocationId(bo.getToLocationPO().getId());
            batchDTO.setNewLocationName(bo.getToLocationPO().getName());
            batchDTO.setWarehouseId(bo.getWarehouseId());
            batchDTO.setOrgId(bo.getCityId());
            batchDTO.setOldLocationId(bo.getFromLocationPO().getId());
            iBatchTaskItemManageService.updateNotPickedBatchTaskItemLocationFromOldToNew(batchDTO);
        } catch (Exception e) {
            LOGGER.info("VesselBL.updateStoreTransferNoOrder 移库失败：", e);
//            throw new BusinessValidateException("自动移库失败，需进行人工移库!");
        }
    }

    private List<BatchInventoryDTO> getLegalBatchInventoryList(PageList<BatchInventoryDTO> batchInventoryList, LocationPO toLocation) {
        if (Objects.isNull(toLocation) || CategoryEnum.CARGO_VESSEL.getByteValue().equals(toLocation.getCategory())) {
            return batchInventoryList.getDataList();
        }
        List<ProductLocationPO> productLocationPOList = productLocationPOMapper.findByLocationIds(Collections.singletonList(toLocation.getId()));
        if (CollectionUtils.isEmpty(productLocationPOList)) {
            return batchInventoryList.getDataList();
        }

        Map<Long, Long> productSkuIdMap = productLocationPOList.stream().collect(Collectors.toMap(ProductLocationPO :: getProductSku_Id, ProductLocationPO :: getProductSku_Id));

        List<BatchInventoryDTO> batchInventoryDTOList = batchInventoryList.getDataList();
        batchInventoryDTOList = batchInventoryDTOList.stream().filter(m -> Objects.nonNull(productSkuIdMap.get(m.getProductSkuId()))).collect(Collectors.toList());

       return batchInventoryDTOList;
    }

    /**
     * 查询仓库名称
     */
    private Map<Integer, String> getWarehouseMap(List<Integer> warhouseIds) {
        if (CollectionUtils.isEmpty(warhouseIds)) {
            return null;
        }
        // 查询仓库名称
        List<Warehouse> warehouseList = iWarehouseQueryService.listWarehouseByIds(warhouseIds);
        if (CollectionUtils.isEmpty(warehouseList)) {
            return null;
        }
        Map<Integer, String> warehosueMap = new HashMap<>(16);
        warehouseList.forEach(warehouse -> {
            warehosueMap.put(warehouse.getId(), warehouse.getName());
        });
        return warehosueMap;
    }

    /**
     * 金额校验
     */
    public void checkPrice(StoreTransferOrderDTO storeTransferOrderDTO) {
        if (!Objects.equals(storeTransferOrderDTO.getTransferType(), StoreTransferEnum.处理品转入移库.getType())) {
            return;
        }

        Map<Long, BigDecimal> skuCount = new HashMap<>(16);
        storeTransferOrderDTO.getStoreTransferOrderItemDTOS().forEach(item -> {
            BigDecimal unitTotalCount = skuCount.get(item.getSkuId());
            BigDecimal totalCount =
                    item.getOverMovePackageCount().multiply(item.getSpecQuantity()).add(item.getOverMoveUnitCount());
            if (unitTotalCount == null) {
                unitTotalCount = totalCount;
            } else {
                unitTotalCount = unitTotalCount.add(totalCount);
            }
            skuCount.put(item.getSkuId(), unitTotalCount);
        });

        DefectiveInventoryPriceDTO defectiveProductPrice = iBatchInventoryManageService.calCcpPriceBySkuCount(
                storeTransferOrderDTO.getOrg_id(), storeTransferOrderDTO.getWarehouse_Id(), skuCount);
        LOGGER.info("校验残次品金额，本次sku数量：{}，累加后：{}", skuCount, JSON.toJSONString(defectiveProductPrice));
        if (defectiveProductPrice != null && defectiveProductPrice.getRemainingAmount() != null
                && defectiveProductPrice.getRemainingAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessValidateException("残次品转入超出仓库残次品累计金额上限");
        }
    }

}
