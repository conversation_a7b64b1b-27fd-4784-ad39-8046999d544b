package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.vessel;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.HistoryDataDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDTO;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Service
public class VesselTransferFutureHandleBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(VesselTransferBL.class);

    @Autowired
    private VesselTransferBL vesselTransferBL;
    public static final ExecutorService EXECUTOR =
            new ThreadPoolExecutor(10, 20, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(),
                    new DefaultThreadFactory("batchSyncTask"), new ThreadPoolExecutor.CallerRunsPolicy());

    public void updateOutStockOrderList(VesselDTO vesselDTO, Byte transferType) {
//        CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(
//                () -> vesselTransferBL.transferToRelatedLocation(vesselDTO, transferType), EXECUTOR);
//        try {
//            boolean result = completableFuture.get();
//        } catch (Exception e) {
//            LOGGER.warn("同步出错 :" + JSON.toJSONString(vesselDTO), e);
//        }
    }
}
