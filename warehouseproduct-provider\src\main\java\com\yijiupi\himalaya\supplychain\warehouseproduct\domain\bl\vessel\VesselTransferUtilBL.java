package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.vessel;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.TransferVesselBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.TransferVesselStoreBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.TransferVesselToLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/7
 */
@Service
public class VesselTransferUtilBL {

    @Autowired
    private LocationPOMapper locationPOMapper;
    @Autowired
    private VesselTransferBL vesselTransferBL;
    @Autowired
    private VesselTransferAsyncBL vesselTransferAsyncBL;

    private static final Logger LOGGER = LoggerFactory.getLogger(VesselTransferUtilBL.class);



    /**
     * 批量转移容器库存到关联货位
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferVesselToLocation(TransferVesselToLocationDTO dto) {
        StopWatch stopWatch = new StopWatch("transferVesselToLocation");
        LOGGER.info("VesselBL.transferVesselToLocation 开始执行 入参：{}", JSON.toJSONString(dto));
        stopWatch.start("transferVesselToLocation");
        resetLocationInfo(dto);

        dto.getVesselIds().forEach(id -> {
            TransferVesselStoreBO transferVesselStoreBO = new TransferVesselStoreBO();
            transferVesselStoreBO.setTransferType(dto.getTransferType());
            transferVesselStoreBO.setTransferFromTo(dto.getTransferFromTo());
            transferVesselStoreBO.setVesselId(id);
            transferVesselStoreBO.setCityId(dto.getOrgId());
            transferVesselStoreBO.setWarehouseId(dto.getWarehouseId());
            transferVesselStoreBO.setUserId(dto.getUserId());

            // 容器库存移库至容器关联货位
            vesselTransferAsyncBL.transferToRelatedLocation(transferVesselStoreBO);
        });
        stopWatch.stop();
        LOGGER.info("VesselBL.transferVesselToLocation 执行完毕：{}；执行时间： {}", JSON.toJSONString(dto), stopWatch.prettyPrint());
    }

    private void resetLocationInfo(TransferVesselToLocationDTO dto) {
        if (!CollectionUtils.isEmpty(dto.getVesselIds())) {
            return;
        }

        List<Long> vesselIds = locationPOMapper.selectLocationIds(dto.getWarehouseId(), CategoryEnum.CARGO_VESSEL.getByteValue());
        LOGGER.info("VesselBL.transferVesselToLocation 查询出物料箱信息为 ：{}", JSON.toJSONString(vesselIds));
        dto.setVesselIds(vesselIds);
    }

}
