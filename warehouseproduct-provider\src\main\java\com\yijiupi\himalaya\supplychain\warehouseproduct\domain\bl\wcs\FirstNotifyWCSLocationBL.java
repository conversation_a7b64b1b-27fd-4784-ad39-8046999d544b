package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.bo.NotifyWCSLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRelateTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageTypeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: FirstNotifyWCSLocationBL
 * @description: 之前是货位，改成类目
 * @date 2022-11-01 10:09
 */
@Service
public class FirstNotifyWCSLocationBL extends NotifyWCSLocationBaseBL {

    @Override
    public boolean support(NotifyWCSLocationBO bo) {
        if (!Objects.equals(bo.getOriPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())
            && Objects.equals(bo.getPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<String> getLocationIdList(NotifyWCSLocationBO bo) {
        return bo.getOriPassageItemPOList().stream()
            .filter(m -> PassageRelateTypeEnum.货位.getType() == m.getRelateType()).map(PassageItemPO::getRelateId)
            .collect(Collectors.toList());
    }
}
