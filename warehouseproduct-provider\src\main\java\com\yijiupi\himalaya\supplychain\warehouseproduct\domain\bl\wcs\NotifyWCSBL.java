package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.bo.NotifyWCSLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.WCSLocationModConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRobotPickEnum;
import com.yijiupi.himalaya.supplychain.wcs.dto.location.WCSLocationModDTO;
import com.yijiupi.himalaya.supplychain.wcs.service.task.IWCSLocationModService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: NotifyWCSBL
 * @description:
 * @date 2022-11-01 09:55
 */
@Service
public class NotifyWCSBL {

    @Reference
    private IWCSLocationModService iLocationModService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private List<NotifyWCSLocationBaseBL> locationBLList;
    private static final Logger LOGGER = LoggerFactory.getLogger(NotifyWCSBL.class);

    @Async
    public void notifyWCSByPassage(PassageDTO passageDTO, PassagePO oriPassageDTO,
        List<PassageItemPO> oriPassageItemPOList) {
        if (!PassageRobotPickEnum.开启.getType().equals(passageDTO.getIsRobotPick())) {
            return;
        }

        NotifyWCSLocationBO bo = new NotifyWCSLocationBO();
        bo.setPassageDTO(passageDTO);
        bo.setOriPassageDTO(oriPassageDTO);
        bo.setOriPassageItemPOList(oriPassageItemPOList);

        Optional<NotifyWCSLocationBaseBL> baseOptional = locationBLList.stream().filter(m -> m.support(bo)).findFirst();
        if (!baseOptional.isPresent()) {
            return;
        }
        NotifyWCSLocationBaseBL baseBL = baseOptional.get();

        List<String> locationIds = baseBL.getLocationIdList(bo);
        if (CollectionUtils.isEmpty(locationIds)) {
            return;
        }

        // 原来是类目，变成货位；原来是货位，变成类目；都是货位，发生变更
        // 通知WCS
        iLocationModService.passageLocationMod(WCSLocationModConvertor.convert(passageDTO, locationIds));
    }

    /**
     * 删除货位
     * 
     * @param warehouseId
     * @param locationIds
     */
    public void deleteLocation(Integer warehouseId, List<Long> locationIds) {
        Boolean isRobotPicking = warehouseConfigService.checkWarehouseIsRobotPicking(warehouseId);

        if (BooleanUtils.isFalse(isRobotPicking)) {
            return;
        }
        WCSLocationModDTO wcsLocationModDTO = new WCSLocationModDTO();
        wcsLocationModDTO.setWarehouseId(warehouseId);
        wcsLocationModDTO.setLocationIds(locationIds.stream().map(String::valueOf).collect(Collectors.toList()));

        try {
            iLocationModService.passageLocationMod(wcsLocationModDTO);
        } catch (Exception e) {
            LOGGER.warn("通知机器人删除货位异常, 参数" + JSON.toJSONString(locationIds) + "; 仓库Id:" + warehouseId, e);
        }
    }

}
