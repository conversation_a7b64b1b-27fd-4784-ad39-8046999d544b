package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.bo.NotifyWCSLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRelateTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageTypeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SecondNotifyWCSLocationBL
 * @description: 之前是类目改成货位
 * @date 2022-11-01 10:09
 */
@Service
public class SecondNotifyWCSLocationBL extends NotifyWCSLocationBaseBL {
    @Override
    public boolean support(NotifyWCSLocationBO bo) {
        if (Objects.equals(bo.getOriPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())
            && !Objects.equals(bo.getPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<String> getLocationIdList(NotifyWCSLocationBO bo) {
        return bo.getPassageDTO().getItemList().stream()
            .filter(m -> PassageRelateTypeEnum.货位.getType() == m.getRelateType()).map(PassageItemDTO::getRelateId)
            .collect(Collectors.toList());
    }
}
