package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.bo.NotifyWCSLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageRelateTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageTypeEnum;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ThirdNotifyWCSLocationBL
 * @description: 都是货位
 * @date 2022-11-01 10:10
 */
@Service
public class ThirdNotifyWCSLocationBL extends NotifyWCSLocationBaseBL {
    @Override
    public boolean support(NotifyWCSLocationBO bo) {
        if (!Objects.equals(bo.getOriPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())
            && !Objects.equals(bo.getPassageDTO().getPassageType(), PassageTypeEnum.类目.getType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<String> getLocationIdList(NotifyWCSLocationBO bo) {
        if (!needNotifyWCS(bo.getPassageDTO(), bo.getOriPassageItemPOList())) {
            return Collections.emptyList();
        }

        List<String> paramLocationIdList =
            bo.getPassageDTO().getItemList().stream().map(PassageItemDTO::getRelateId).collect(Collectors.toList());
        List<String> existLocationIdList =
            bo.getOriPassageItemPOList().stream().filter(m -> PassageRelateTypeEnum.货位.getType() == m.getRelateType())
                .map(PassageItemPO::getRelateId).collect(Collectors.toList());

        List<String> intersectionLocationList = new ArrayList<>(
            org.apache.commons.collections4.CollectionUtils.intersection(paramLocationIdList, existLocationIdList));

        Set<String> locationIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(paramLocationIdList)) {
            paramLocationIdList.removeAll(intersectionLocationList);
            locationIds.addAll(paramLocationIdList);
        }

        if (CollectionUtils.isNotEmpty(existLocationIdList)) {
            existLocationIdList.removeAll(intersectionLocationList);
            locationIds.addAll(existLocationIdList);
        }
        return new ArrayList<>(locationIds);
    }

    private boolean needNotifyWCS(PassageDTO passageDTO, List<PassageItemPO> passageItemPOList) {
        if (passageDTO.getItemList().size() != passageItemPOList.size()) {
            return Boolean.TRUE;
        }
        List<String> paramLocationIdList =
            passageDTO.getItemList().stream().map(PassageItemDTO::getRelateId).collect(Collectors.toList());
        List<String> existLocationIdList =
            passageItemPOList.stream().filter(m -> PassageRelateTypeEnum.货位.getType() == m.getRelateType())
                .map(PassageItemPO::getRelateId).collect(Collectors.toList());

        List<String> intersectionList = new ArrayList<>(
            org.apache.commons.collections4.CollectionUtils.intersection(paramLocationIdList, existLocationIdList));
        if (CollectionUtils.isEmpty(intersectionList)) {
            return Boolean.TRUE;
        }
        paramLocationIdList.removeAll(intersectionList);
        existLocationIdList.removeAll(intersectionList);
        if (CollectionUtils.isNotEmpty(paramLocationIdList)) {
            return Boolean.TRUE;
        }

        if (CollectionUtils.isNotEmpty(existLocationIdList)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
