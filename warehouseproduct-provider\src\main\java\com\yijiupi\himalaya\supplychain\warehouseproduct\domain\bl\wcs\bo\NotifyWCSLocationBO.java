package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.wcs.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: NotifyWCSLocationBO
 * @description:
 * @date 2022-11-01 10:07
 */
public class NotifyWCSLocationBO {

    private PassageDTO passageDTO;

    private PassagePO oriPassageDTO;

    private List<PassageItemPO> oriPassageItemPOList;

    /**
     * 获取
     *
     * @return passageDTO
     */
    public PassageDTO getPassageDTO() {
        return this.passageDTO;
    }

    /**
     * 设置
     *
     * @param passageDTO
     */
    public void setPassageDTO(PassageDTO passageDTO) {
        this.passageDTO = passageDTO;
    }

    /**
     * 获取
     *
     * @return oriPassageDTO
     */
    public PassagePO getOriPassageDTO() {
        return this.oriPassageDTO;
    }

    /**
     * 设置
     *
     * @param oriPassageDTO
     */
    public void setOriPassageDTO(PassagePO oriPassageDTO) {
        this.oriPassageDTO = oriPassageDTO;
    }

    /**
     * 获取
     *
     * @return oriPassageItemPOList
     */
    public List<PassageItemPO> getOriPassageItemPOList() {
        return this.oriPassageItemPOList;
    }

    /**
     * 设置
     *
     * @param oriPassageItemPOList
     */
    public void setOriPassageItemPOList(List<PassageItemPO> oriPassageItemPOList) {
        this.oriPassageItemPOList = oriPassageItemPOList;
    }
}
