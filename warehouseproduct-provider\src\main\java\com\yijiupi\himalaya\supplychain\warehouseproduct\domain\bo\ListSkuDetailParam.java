package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.query.QuerySkuDetailsParamDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-04 11:49
 **/
public class ListSkuDetailParam {

    private Integer cityId;

    private List<QuerySkuDetailsParamDTO.QuerySkuParam> querySkuParamList;

    private Byte isDelete = 0;

    public static ListSkuDetailParam of(Integer cityId, List<QuerySkuDetailsParamDTO.QuerySkuParam> param, Byte isDelete) {
        ListSkuDetailParam result = new ListSkuDetailParam();
        result.setCityId(cityId);
        result.setQuerySkuParamList(param);
        result.setIsDelete(isDelete);
        return result;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<QuerySkuDetailsParamDTO.QuerySkuParam> getQuerySkuParamList() {
        return querySkuParamList;
    }

    public void setQuerySkuParamList(List<QuerySkuDetailsParamDTO.QuerySkuParam> querySkuParamList) {
        this.querySkuParamList = querySkuParamList;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}
