package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/5
 */
public class LocationAreaInfoQueryBO {


    /**
     * 片区id猎豹
     */
    private List<Long> areaIds;
    /**
     * 0 货位，1 货区
     */
    private Byte category;
    /**
     * 仓库id
     */
    private Integer warehouseId;



    /**
     * 获取 0 货位，1 货区
     *
     * @return category 0 货位，1 货区
     */
    public Byte getCategory() {
        return this.category;
    }

    /**
     * 设置 0 货位，1 货区
     *
     * @param category 0 货位，1 货区
     */
    public void setCategory(Byte category) {
        this.category = category;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 片区id猎豹
     *
     * @return areaIds 片区id猎豹
     */
    public List<Long> getAreaIds() {
        return this.areaIds;
    }

    /**
     * 设置 片区id猎豹
     *
     * @param areaIds 片区id猎豹
     */
    public void setAreaIds(List<Long> areaIds) {
        this.areaIds = areaIds;
    }
}

