package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/2 16:55
 */
public class LocationBO {

    private Byte areaType;

    private Long areaId;

    private String areaName;

    private Integer floor;

    private Long locationId;


    private String locationName;
    /**
     * 坐标
     */
    private List<BigDecimal> coordinates;

    private Boolean isHorizontalLonger;

    private BigDecimal endLength;

    public BigDecimal getEndLength() {
        return endLength;
    }

    public void setEndLength(BigDecimal endLength) {
        this.endLength = endLength;
    }

    public Boolean getHorizontalLonger() {
        return isHorizontalLonger;
    }

    public void setHorizontalLonger(Boolean horizontalLonger) {
        isHorizontalLonger = horizontalLonger;
    }

    public Integer getFloor() {
        return floor;
    }

    public void setFloor(Integer floor) {
        this.floor = floor;
    }

    public Byte getAreaType() {
        return areaType;
    }

    public void setAreaType(Byte areaType) {
        this.areaType = areaType;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public List<BigDecimal> getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(List<BigDecimal> coordinates) {
        this.coordinates = coordinates;
    }
}
