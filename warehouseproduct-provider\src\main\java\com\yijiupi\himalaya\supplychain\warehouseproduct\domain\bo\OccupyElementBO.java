package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 下午7:17
 */
public class OccupyElementBO {
    private String elementId;

    private List<BigDecimal> coordinates;

    /**
     * 是否上楼点
     */
    private Boolean markUpstairsPoint;

    private Integer type;

    private String refName;

    public String getElementId() {
        return elementId;
    }

    public void setElementId(String elementId) {
        this.elementId = elementId;
    }

    public List<BigDecimal> getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(List<BigDecimal> coordinates) {
        this.coordinates = coordinates;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRefName() {
        return refName;
    }

    public void setRefName(String refName) {
        this.refName = refName;
    }

    public Boolean getMarkUpstairsPoint() {
        return markUpstairsPoint;
    }

    public void setMarkUpstairsPoint(Boolean markUpstairsPoint) {
        this.markUpstairsPoint = markUpstairsPoint;
    }
}
