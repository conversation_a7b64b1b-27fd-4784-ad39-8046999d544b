package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
public class ProductLocationAssociateBO {

     private ProductLocationDTO dto;

     private boolean create = Boolean.FALSE;

    public ProductLocationAssociateBO() {
    }

    public ProductLocationAssociateBO(ProductLocationDTO dto, boolean create) {
        this.dto = dto;
        this.create = create;
    }

    /**
     * 获取
     *
     * @return dto
     */
    public ProductLocationDTO getDto() {
        return this.dto;
    }

    /**
     * 设置
     *
     * @param dto
     */
    public void setDto(ProductLocationDTO dto) {
        this.dto = dto;
    }

    /**
     * 获取
     *
     * @return create
     */
    public boolean isCreate() {
        return this.create;
    }

    /**
     * 设置
     *
     * @param create
     */
    public void setCreate(boolean create) {
        this.create = create;
    }
}
