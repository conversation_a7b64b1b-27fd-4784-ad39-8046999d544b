package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 * 获取出库位
 * <AUTHOR>
 * @since 2023/10/9
 */
public class RecommendOutLocationQueryBO {

    private RecommendOutLocationQueryDTO queryDTO;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取
     *
     * @return queryDTO
     */
    public RecommendOutLocationQueryDTO getQueryDTO() {
        return this.queryDTO;
    }

    /**
     * 设置
     *
     * @param queryDTO
     */
    public void setQueryDTO(RecommendOutLocationQueryDTO queryDTO) {
        this.queryDTO = queryDTO;
    }

    /**
     * 获取 出库位id
     *
     * @return locationId 出库位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 出库位id
     *
     * @param locationId 出库位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 出库位名称
     *
     * @return locationName 出库位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 出库位名称
     *
     * @param locationName 出库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
