package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
public class SkuInventoryAndProductionDateHelperBO {

    private Long skuId;

    private Long locationId;

    private String locationName;

    private Long areaId;

    /**
     * 获取
     *
     * @return skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置
     *
     * @param skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    /**
     * 获取
     *
     * @return locationId
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置
     *
     * @param locationId
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取
     *
     * @return areaId
     */
    public Long getAreaId() {
        return this.areaId;
    }

    /**
     * 设置
     *
     * @param areaId
     */
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * 获取
     *
     * @return locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置
     *
     * @param locationName
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
