package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
public class SkuInventoryAndProductionDateQO implements Serializable {
    /**
     * skuId列表
     */
    private List<Long> skuIds;
    /**
     * 货位id列表
     */
    private List<Long> locationIds;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 获取 skuId列表
     *
     * @return skuIds skuId列表
     */
    public List<Long> getSkuIds() {
        return this.skuIds;
    }

    /**
     * 设置 skuId列表
     *
     * @param skuIds skuId列表
     */
    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    /**
     * 获取 货位id列表
     *
     * @return locationIds 货位id列表
     */
    public List<Long> getLocationIds() {
        return this.locationIds;
    }

    /**
     * 设置 货位id列表
     *
     * @param locationIds 货位id列表
     */
    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
