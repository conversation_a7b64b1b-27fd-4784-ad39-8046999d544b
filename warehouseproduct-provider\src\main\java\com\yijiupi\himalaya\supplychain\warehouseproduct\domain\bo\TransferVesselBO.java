package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/7
 */
public class TransferVesselBO {
    /**
     * 来源货位
     */
    private LocationPO fromLocationPO;

    /**
     * 目标货位
     */
    private LocationPO toLocationPO;
    /**
     * 操作人
     */
    private Integer userId;

    private String userName = "人工";

    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;

    public TransferVesselBO() {
    }

    public TransferVesselBO(LocationPO fromLocationPO, LocationPO toLocationPO, Integer userId) {
        this.fromLocationPO = fromLocationPO;
        this.toLocationPO = toLocationPO;
        this.userId = userId;
    }

    /**
     * 获取 来源货位
     *
     * @return fromLocationPO 来源货位
     */
    public LocationPO getFromLocationPO() {
        return this.fromLocationPO;
    }

    /**
     * 设置 来源货位
     *
     * @param fromLocationPO 来源货位
     */
    public void setFromLocationPO(LocationPO fromLocationPO) {
        this.fromLocationPO = fromLocationPO;
    }

    /**
     * 获取 目标货位
     *
     * @return toLocationPO 目标货位
     */
    public LocationPO getToLocationPO() {
        return this.toLocationPO;
    }

    /**
     * 设置 目标货位
     *
     * @param toLocationPO 目标货位
     */
    public void setToLocationPO(LocationPO toLocationPO) {
        this.toLocationPO = toLocationPO;
    }

    /**
     * 获取 操作人
     *
     * @return userId 操作人
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置 操作人
     *
     * @param userId 操作人
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取
     *
     * @return userName
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     * 设置
     *
     * @param userName
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取 城市Id
     *
     * @return cityId 城市Id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市Id
     *
     * @param cityId 城市Id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}
