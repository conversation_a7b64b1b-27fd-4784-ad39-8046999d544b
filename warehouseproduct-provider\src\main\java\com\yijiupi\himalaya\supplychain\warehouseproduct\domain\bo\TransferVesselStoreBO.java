package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/8
 */
public class TransferVesselStoreBO {
    /**
     * 货位id
     */
    private Long vesselId;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 操作人Id
     */
    private Integer userId;

    private Byte transferType;

    /**
     * 从货位到物料箱/从物料箱到货位
     */
    private Byte transferFromTo;

    public TransferVesselStoreBO() {
    }

    /**
     * 获取
     *
     * @return transferType
     */
    public Byte getTransferType() {
        return this.transferType;
    }

    /**
     * 设置
     *
     * @param transferType
     */
    public void setTransferType(Byte transferType) {
        this.transferType = transferType;
    }

    /**
     * 获取 从货位到物料箱从物料箱到货位
     *
     * @return transferFromTo 从货位到物料箱从物料箱到货位
     */
    public Byte getTransferFromTo() {
        return this.transferFromTo;
    }

    /**
     * 设置 从货位到物料箱从物料箱到货位
     *
     * @param transferFromTo 从货位到物料箱从物料箱到货位
     */
    public void setTransferFromTo(Byte transferFromTo) {
        this.transferFromTo = transferFromTo;
    }


    /**
     * 获取 城市Id
     *
     * @return cityId 城市Id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市Id
     *
     * @param cityId 城市Id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 操作人Id
     *
     * @return userId 操作人Id
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置 操作人Id
     *
     * @param userId 操作人Id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取 货位id
     *
     * @return vesselId 货位id
     */
    public Long getVesselId() {
        return this.vesselId;
    }

    /**
     * 设置 货位id
     *
     * @param vesselId 货位id
     */
    public void setVesselId(Long vesselId) {
        this.vesselId = vesselId;
    }
}
