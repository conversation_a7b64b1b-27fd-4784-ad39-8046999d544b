package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location;

import com.yijiupi.himalaya.supplychain.dto.AreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/6
 */
public class LocationRuleRegionMatchBO {

    /**
     * 
     */
    private LocationRulePO locationRulePO;
    /**
     * 区域id
     */
    private Long ruleId;
    /**
     * 省
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 街道
     */
    private String street;
    /**
     * 拼接的区域名称，用 - 分割
     */
    private String ruleName;

    public LocationRuleRegionMatchBO() {}

    public LocationRuleRegionMatchBO(LocationRulePO locationRulePO, Long ruleId) {
        this.locationRulePO = locationRulePO;
        this.ruleId = ruleId;
    }

    /**
     * 获取
     *
     * @return locationRulePO
     */
    public LocationRulePO getLocationRulePO() {
        return this.locationRulePO;
    }

    /**
     * 设置
     *
     * @param locationRulePO
     */
    public void setLocationRulePO(LocationRulePO locationRulePO) {
        this.locationRulePO = locationRulePO;
    }

    /**
     * 获取 区域id
     *
     * @return ruleId 区域id
     */
    public Long getRuleId() {
        return this.ruleId;
    }

    /**
     * 设置 区域id
     *
     * @param ruleId 区域id
     */
    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * 获取 省
     *
     * @return province 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置 省
     *
     * @param province 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取 城市
     *
     * @return city 城市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置 城市
     *
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取 区
     *
     * @return district 区
     */
    public String getDistrict() {
        return this.district;
    }

    /**
     * 设置 区
     *
     * @param district 区
     */
    public void setDistrict(String district) {
        this.district = district;
    }

    /**
     * 获取 街道
     *
     * @return street 街道
     */
    public String getStreet() {
        return this.street;
    }

    /**
     * 设置 街道
     *
     * @param street 街道
     */
    public void setStreet(String street) {
        this.street = street;
    }

    /**
     * 获取 拼接的区域名称，用 - 分割
     *
     * @return ruleName 拼接的区域名称，用 - 分割
     */
    public String getRuleName() {
        return this.ruleName;
    }

    /**
     * 设置 拼接的区域名称，用 - 分割
     *
     * @param ruleName 拼接的区域名称，用 - 分割
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public void setAreaInfo(AreaDTO areaInfo) {
        if (Objects.isNull(areaInfo)) {
            return;
        }
        this.province = areaInfo.getProvince();
        this.city = areaInfo.getCity();
        this.district = areaInfo.getCounty();
        this.street = areaInfo.getStreet();
        StringBuffer stringBuffer = new StringBuffer("");
        boolean hasContent = false;

        if (StringUtils.isNotBlank(province)) {
            stringBuffer.append(province);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(city)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(city);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(district)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(district);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(street)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(street);
        }
        this.ruleName = stringBuffer.toString();
    }

    public static LocationRuleRegionMatchBO newInstance(LocationRulePO locationRulePO, Long ruleId) {
        return new LocationRuleRegionMatchBO(locationRulePO, ruleId);
    }

}
