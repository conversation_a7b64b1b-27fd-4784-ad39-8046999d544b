package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location;

import java.util.List;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/2/25
 */
public class ProductLocationValidateOperationLegalBO {
    /**
     * 操作人id
     */
    private Integer userId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 货区id列表
     */
    private List<Long> areaIds;
    /**
     * 货位id列表，用于查找货区id
     */
    private List<Long> locationIds;

    public ProductLocationValidateOperationLegalBO() {}

    public ProductLocationValidateOperationLegalBO(Integer userId, Integer warehouseId, List<Long> areaIds) {
        this.userId = userId;
        this.warehouseId = warehouseId;
        this.areaIds = areaIds;
    }

    /**
     * 获取 操作人id
     *
     * @return userId 操作人id
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置 操作人id
     *
     * @param userId 操作人id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货区id列表
     *
     * @return areaIds 货区id列表
     */
    public List<Long> getAreaIds() {
        return this.areaIds;
    }

    /**
     * 设置 货区id列表
     *
     * @param areaIds 货区id列表
     */
    public void setAreaIds(List<Long> areaIds) {
        this.areaIds = areaIds;
    }

    /**
     * 获取 货位id列表，用于查找货区id
     *
     * @return locationIds 货位id列表，用于查找货区id
     */
    public List<Long> getLocationIds() {
        return this.locationIds;
    }

    /**
     * 设置 货位id列表，用于查找货区id
     *
     * @param locationIds 货位id列表，用于查找货区id
     */
    public void setLocationIds(List<Long> locationIds) {
        this.locationIds = locationIds;
    }

    public static ProductLocationValidateOperationLegalBO ofAreaIds(Integer userId, Integer warehouseId,
        List<Long> areaIds) {
        ProductLocationValidateOperationLegalBO bo = new ProductLocationValidateOperationLegalBO();
        bo.setAreaIds(areaIds);
        bo.setUserId(userId);
        bo.setWarehouseId(warehouseId);

        return bo;
    }

    public static ProductLocationValidateOperationLegalBO ofLocationIds(Integer userId, Integer warehouseId,
        List<Long> locationIds) {
        ProductLocationValidateOperationLegalBO bo = new ProductLocationValidateOperationLegalBO();
        bo.setLocationIds(locationIds);
        bo.setUserId(userId);
        bo.setWarehouseId(warehouseId);

        return bo;
    }

}
