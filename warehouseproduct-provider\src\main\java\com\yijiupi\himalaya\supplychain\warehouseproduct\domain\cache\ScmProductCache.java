package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.cache;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.assignment.service.ITodoTaskCallbackService;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.product.ScmProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductBoxCode;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfo;
import com.yijiupi.himalaya.supplychain.dubbop.dto.ProductInfoSpecification;
import com.yijiupi.himalaya.supplychain.dubbop.dto.product.ScmProductCodeListDTO;
import com.yijiupi.himalaya.supplychain.productsync.convertor.ScmProductCodeConvert;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuServiceBL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 商品码缓存
 */
@Component
public class ScmProductCache {

    private static final Logger logger = LoggerFactory.getLogger(ScmProductCache.class);

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private ScmProductInfoQueryService scmProductInfoQueryService;

    /**
     * 条码缓存key前缀
     */
    private static final String PREFIX = "sup:scm:product:code:";

    @Reference
    private ITodoTaskCallbackService todoTaskCallbackService;
    /**
     * 获取缓存中信息 如果不存在 则查询DB
     */
    public void set(ProductInfo productInfo) {
        // 根据id分组
        Map<Integer, List<ProductInfoSpecification>> groupMap = productInfo.getSpecificationList()
                .stream().collect(Collectors.groupingBy(ProductInfoSpecification::getId));
        Set<Integer> specIds = groupMap.keySet();
        for (Integer groupKey : specIds) {
            // 获取分组的对象
            List<ProductInfoSpecification> productInfoSpecifications = groupMap.get(groupKey);
            Map<String, ScmProductCodeListDTO> value = new HashMap<>(10);
            ScmProductCodeListDTO scmProductCodeListDTO = new ScmProductCodeListDTO();
            List<List<ProductBoxCode>> productBoxCodeList = productInfoSpecifications.stream()
                    .map(ProductInfoSpecification::getProductBoxCodeList).collect(Collectors.toList());
            // 交易产品条码
            scmProductCodeListDTO.setJiupiBottleCodeList(
                    ScmProductCodeConvert.convertJiuPiBottleCodeList(productInfo.getBottleCodeList()));
            // 产品箱码集合
            scmProductCodeListDTO.setBoxCodeList(ScmProductCodeConvert.convertBoxCode(productBoxCodeList));
            value.put(String.valueOf(groupKey), scmProductCodeListDTO);
            logger.info("supplychain.productsync.cache.ScmProductCache.set 缓存条码信息 value={}", JSON.toJSONString(value));
            setAndSetTimeout(groupKey, value);
        }
        todoTaskCallbackService.completeTodoTaskBySpecId(specIds);
    }

    /**
     * 获取缓存中信息 如果不存在 则查询DB
     *
     * @param list 规格 id
     * @return 规格对应的条码信息
     */
    public Map<Integer, ScmProductCodeListDTO> get(Set<Integer> list) {
        Map<Integer, ScmProductCodeListDTO> returnProductCodeMap = new HashMap<>(list.size());
        if (CollectionUtils.isEmpty(list)) {
            return returnProductCodeMap;
        }
        // 从缓存中未查询到数据集合
        Set<Integer> keys = new HashSet<>(list.size());
        for (Integer key : list) {
            try {
                // 获取缓存中的信息
                Map<String, ScmProductCodeListDTO> cacheMap = get(key);
                // 如果缓存中为空 则查DB 再缓存起来
                if (CollectionUtils.isEmpty(cacheMap)) {
                    keys.add(key);
                    continue;
                }
                returnProductCodeMap.put(key, cacheMap.get(String.valueOf(key)));
            } catch (Exception e) {
                keys.add(key);
            }
        }
        List<Set<Integer>> splitList = ProductSkuServiceBL.splitList(keys, 50);
        for (Set<Integer> integers : splitList) {
            Map<Integer, ScmProductCodeListDTO> productCodeListMap =
                    scmProductInfoQueryService.getScmProductCodeList(integers);
            if (CollectionUtils.isEmpty(productCodeListMap)) {
                continue;
            }
            for (Integer key : productCodeListMap.keySet()) {
                ScmProductCodeListDTO codeDto = productCodeListMap.get(key);
                returnProductCodeMap.put(key, codeDto);
                Map<String, ScmProductCodeListDTO> cacheMap = new HashMap<>(16);
                cacheMap.put(String.valueOf(key), codeDto);
                // 缓存数据
                setAndSetTimeout(key, cacheMap);
            }
        }
        logger.info("supplychain.warehouseproduct.domain.cache.ScmProductCache.get 查询缓存 param={},result={}",
                JSON.toJSONString(list), JSON.toJSONString(returnProductCodeMap));
        return returnProductCodeMap;
    }

    /**
     * 插入缓存 并设置过期时间为7天
     *
     * @param key   redis key
     * @param value redis 存放内容
     */
    private void setAndSetTimeout(Integer key, Map<String, ScmProductCodeListDTO> value) {
        redisTemplate.opsForValue().set(PREFIX + key, value);
        redisTemplate.expire(PREFIX + key, 1, TimeUnit.DAYS);
    }

    /**
     * 获取缓存信息
     *
     * @param key redis key
     * @return redis 存放内容
     */
    @SuppressWarnings("unchecked")
    private Map<String, ScmProductCodeListDTO> get(Integer key) {
        try {
            Object o = redisTemplate.opsForValue().get(PREFIX + key);
            if (ObjectUtils.isEmpty(o)) {
                return null;
            }
            return (Map<String, ScmProductCodeListDTO>) o;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 清理条码缓存
     *
     * @param productSpecificationId 规格 id
     */
    public void clearProductCodeCache(Integer productSpecificationId) {
        redisTemplate.delete(PREFIX + productSpecificationId);
    }
}
