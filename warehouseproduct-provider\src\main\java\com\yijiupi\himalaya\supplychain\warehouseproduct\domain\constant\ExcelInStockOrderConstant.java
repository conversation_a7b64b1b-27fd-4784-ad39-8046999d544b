package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品信息excel表单字段
 *
 * <AUTHOR>
 * @date 2019-08-30 15:29
 */
public class ExcelInStockOrderConstant {

    /** private static final String DATE = "DATE"; */
    private static final String SPRIT = "/";
    private static final String INTEGER = "INTEGER";
    private static final String COLON = ":";
    private static final String COMMA = ",";
    public static final String OWNERNO = "货主编码";
    public static final String OWNERNAME = "货主名称";
    public static final String SUPPLIERID = "供应商编码";
    public static final String SUPPLIERNAME = "供应商名称";
    /** public static final String WAREHOUSETNAME = "仓库名称"; */
    public static final String ORDERTYPE = "单据类型";

    private static final String CGSQ = "采购申请单";
    private static final String DBSQ = "调拨申请单";

    /** public static final String ORDERNO = "订单号"; */
    public static final String PRODUCTBARCODE = "产品条码";
    public static final String PRODUCTNAME = "产品名称";
    public static final String SPECNAME = "包装规格";
    public static final String PACKAGECOUNT = "大单位数量";
    public static final String PACKAGENAME = "大单位";
    public static final String UNITTOTALCOUNT = "小单位数量";
    public static final String UNITNAME = "小单位";
    /** public static final String PRODUCTIONDATE = "生产日期"; */
    public static final String DAYOFSHELFLIFE = "保质期(天)";
    public static final String REMARK = "备注";

    /**
     * 单据类型枚举MAP
     */
    protected static final Map<String, Integer> ORDERTYPE_MAP = new HashMap<>(16);

    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {
        /**
         * "货主编码,货主名称,供应商编码,供应商名称,仓库名称,单据类型:采购入库/调拨入库/退货入库/第三方入库,订单号,产品条码,产品名称,包装规格,大单位数量:INTEGER,大单位,小单位数量:INTEGER,小单位,生产日期,保质期(天),备注"
         */
        EXCEL_TABLE_NAME.append(OWNERNO).append(COMMA).append(OWNERNAME).append(COMMA).append(SUPPLIERID).append(COMMA)
            .append(SUPPLIERNAME).append(COMMA)
            /** .append(WAREHOUSETNAME).append(COMMA) */
            .append(ORDERTYPE).append(COLON).append(CGSQ).append(SPRIT).append(DBSQ).append(COMMA)
            /** .append(ORDERNO).append(COMMA) */
            .append(PRODUCTBARCODE).append(COMMA).append(PRODUCTNAME).append(COMMA).append(SPECNAME).append(COMMA)
            .append(PACKAGECOUNT).append(COLON).append(INTEGER).append(COMMA).append(PACKAGENAME).append(COMMA)
            .append(UNITTOTALCOUNT).append(COLON).append(INTEGER).append(COMMA).append(UNITNAME).append(COMMA)
            /** .append(PRODUCTIONDATE).append(COLON).append(DATE).append(COMMA) */
            .append(DAYOFSHELFLIFE).append(COLON).append(INTEGER).append(COMMA).append(REMARK);
        /** 是否加工 */
        ORDERTYPE_MAP.put(CGSQ, 1);
        ORDERTYPE_MAP.put(DBSQ, 2);
    }

    public static Map<String, Integer> getOrderTypeMap() {
        return ORDERTYPE_MAP;
    }

}
