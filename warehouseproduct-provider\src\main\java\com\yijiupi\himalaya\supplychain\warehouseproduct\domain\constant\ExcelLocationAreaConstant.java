package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 库区excel表单字段
 *
 * <AUTHOR>
 * @date 2019-10-16
 */
public class ExcelLocationAreaConstant {

    private static final String SPRIT = "/";
    private static final String COLON = ":";
    private static final String COMMA = ",";
    public static final String AREA = "库区编码";
    public static final String SUBCATEGORY = "库区类型";
    public static final String LOCATIONCAPACITY = "库区容量";
    public static final String REMO = "备注";

    /**
     * 状态
     */
    public static final String CATEGORY_STORAGE = "存储区";
    public static final String CATEGORY_PICK = "拣货区";
    public static final String CATEGORY_ZEROPICK = "零拣区";
    public static final String CATEGORY_TURNOVER = "周转区";
    public static final String CATEGORY_RETURN = "退货区";
    public static final String CATEGORY_TEMPORARY = "暂存区";
    public static final String CATEGORY_PEND = "待检区";
    public static final String CATEGORY_DEFECTIVE = "残次品区";
    public static final String CATEGORY_COLLECT = "集货区";
    public static final String CATEGORY_DISPLAY = "陈列品区";

    protected static final Map<String, Byte> SUBCATEGORY_MAP = new HashMap<>(16);

    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {
        // "库区编码,库区类型:,库区容量,备注,仓库名称
        EXCEL_TABLE_NAME.append(AREA).append(COMMA).append(SUBCATEGORY).append(COLON).append(CATEGORY_STORAGE)
            .append(SPRIT).append(CATEGORY_PICK).append(SPRIT).append(CATEGORY_ZEROPICK).append(SPRIT)
            .append(CATEGORY_TURNOVER).append(SPRIT).append(CATEGORY_RETURN).append(SPRIT).append(CATEGORY_TEMPORARY)
            .append(SPRIT).append(CATEGORY_PEND).append(SPRIT).append(CATEGORY_DEFECTIVE).append(SPRIT)
            .append(CATEGORY_COLLECT).append(SPRIT).append(CATEGORY_DISPLAY).append(COMMA).append(LOCATIONCAPACITY)
            .append(COMMA).append(REMO);

        SUBCATEGORY_MAP.put(CATEGORY_STORAGE, (byte)50);
        SUBCATEGORY_MAP.put(CATEGORY_PICK, (byte)51);
        SUBCATEGORY_MAP.put(CATEGORY_ZEROPICK, (byte)52);
        SUBCATEGORY_MAP.put(CATEGORY_TURNOVER, (byte)53);
        SUBCATEGORY_MAP.put(CATEGORY_RETURN, (byte)54);
        SUBCATEGORY_MAP.put(CATEGORY_TEMPORARY, (byte)55);
        SUBCATEGORY_MAP.put(CATEGORY_PEND, (byte)56);
        SUBCATEGORY_MAP.put(CATEGORY_DEFECTIVE, (byte)60);
        SUBCATEGORY_MAP.put(CATEGORY_COLLECT, (byte)61);
        SUBCATEGORY_MAP.put(CATEGORY_DISPLAY, (byte)62);
    }

    public static Map<String, Byte> getSubcategoryMap() {
        return SUBCATEGORY_MAP;
    }
}
