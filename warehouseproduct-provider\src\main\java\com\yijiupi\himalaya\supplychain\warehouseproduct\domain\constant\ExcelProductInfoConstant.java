package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品信息excel表单字段
 *
 * <AUTHOR>
 * @date 2019-08-30 15:29
 */
public class ExcelProductInfoConstant {

    public static final String PRODUC_NAME = "产品名称*";
    public static final String PRODUCT_CODE = "产品编码";
    public static final String CATEGORY_NAME = "类目";
    public static final String BRAND = "品牌";
    public static final String OWNER = "货主";
    public static final String STORAGETYPE = "保存条件";
    public static final String SHELFLIFELONGTIME = "是否有保质期";
    public static final String MONTHOFSHELFLIFE = "保质期";
    public static final String SHELFLIFEUNIT = "保质期单位";
    public static final String PROCESS = "是否加工";
    public static final String BOTTLECODE = "最小单位条码";
    public static final String SPECNAME = "规格名称*(如6瓶/件)";
    public static final String PACKAGEQUANTITY = "包装数量*(如6)";
    public static final String UNITNAME = "内包装单位*(如瓶)";
    public static final String PACKAGENAME = "外包装单位*(如件)";
    public static final String BARCODE = "条码";
    public static final String LENGTH = "长(CM)";
    public static final String WIDTH = "宽(CM)";
    public static final String HEIGHT = "高(CM)";
    public static final String VOLUME = "体积(CM3)";
    public static final String WEIGHT = "重量(KG)";
    public static final String THIRDSKU = "外部SKUID";
    public static final String THIRDSYSNAME = "外部系统名称";
    public static final String THIRDSYSCODE = "外部系统编码";

    /**
     * 保存条件
     */
    public static final String STORAGETYPE_NORMAL = "常温";
    public static final String STORAGETYPE_COLD = "冷藏";
    public static final String STORAGETYPE_FREEZE = "冷冻";
    static final Map<String, Byte> STORAGETYPE_MAP = new HashMap<>(16);

    /**
     * 是否有保质期
     */
    public static final String SHELFLIFELONGTIME_YES = "有";
    public static final String SHELFLIFELONGTIME_NO = "无";
    static final Map<String, Byte> SHELFLIFELONGTIME_MAP = new HashMap<>(16);

    /**
     * 保质期单位
     */
    public static final String SHELFLIFEUNIT_YEAR = "年";
    public static final String SHELFLIFEUNIT_MONTH = "月";
    public static final String SHELFLIFEUNIT_DAY = "日";
    static final Map<String, Byte> SHELFLIFEUNIT_MAP = new HashMap<>(16);

    /**
     * 是否加工
     */
    public static final String PROCESS_YES = "是";
    public static final String PROCESS_NO = "否";
    static final Map<String, Byte> PROCESS_MAP = new HashMap<>(16);

    /**
     * 状态
     */
    public static final String STATUS_YES = "启用";
    public static final String STATUS_NO = "停用";
    static final Map<String, Byte> STATUS__MAP = new HashMap<>(16);

    /**
     * 规格状态
     */
    public static final String STATE_YES = "启用";
    public static final String STATE_NO = "停用";
    static final Map<String, Byte> STATE_MAP = new HashMap<>(16);

    /**
     * 保存条件
     */
    public static final String PDD = "拼多多";
    public static final String TB = "淘宝";
    public static final String JD = "京东";
    public static final String YSYX = "云深优选";
    public static final String WDT = "旺店通";

    static final Map<String, Byte> SYSTEM_TYPE = new HashMap<>(16);
    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {

        // 表头显示
        // 产品名称,产品编码,类目,品牌,保存条件:常温/冷藏/冷冻,是否有保质期:有/无,保质期:INTEGER,保质期单位:年/月/日,是否加工:是/否,最小单位条码,状态:启用/停用,
        // 规格名称,包装数量:INTEGER,内包装单位,外包装单位,条码,长(CM):INTEGER,宽(CM):INTEGER,高(CM):INTEGER,体积(CM3):INTEGER,重量(KG):INTEGER,规格状态:启用/停用
        EXCEL_TABLE_NAME.append(PRODUC_NAME).append(",").append(PRODUCT_CODE).append(",").append(CATEGORY_NAME)
            .append(",").append(BRAND).append(",").append(OWNER).append(",").append(STORAGETYPE).append(":")
            .append(STORAGETYPE_NORMAL).append("/").append(STORAGETYPE_COLD).append("/").append(STORAGETYPE_FREEZE)
            .append(",").append(SHELFLIFELONGTIME).append(":").append(SHELFLIFELONGTIME_YES).append("/")
            .append(SHELFLIFELONGTIME_NO).append(",").append(MONTHOFSHELFLIFE).append(":INTEGER").append(",")
            .append(SHELFLIFEUNIT).append(":").append(SHELFLIFEUNIT_YEAR).append("/").append(SHELFLIFEUNIT_MONTH)
            .append("/").append(SHELFLIFEUNIT_DAY).append(",").append(PROCESS).append(":").append(PROCESS_YES)
            .append("/").append(PROCESS_NO).append(",").append(BOTTLECODE).append(",").append(SPECNAME).append(",")
            .append(PACKAGEQUANTITY).append(":INTEGER").append(",").append(UNITNAME).append(",").append(PACKAGENAME)
            .append(",").append(BARCODE).append(",").append(LENGTH).append(":INTEGER").append(",").append(WIDTH)
            .append(":INTEGER").append(",").append(HEIGHT).append(":INTEGER").append(",").append(VOLUME)
            .append(":INTEGER").append(",").append(WEIGHT).append(":INTEGER").append(",").append(THIRDSYSNAME)
            .append(":").append(PDD).append("/").append(TB).append("/").append(JD).append("/").append(YSYX).append("/")
            .append(WDT).append(",").append(THIRDSKU);

        /** 保存条件map */
        STORAGETYPE_MAP.put(STORAGETYPE_NORMAL, (byte)0);
        STORAGETYPE_MAP.put(STORAGETYPE_COLD, (byte)1);
        STORAGETYPE_MAP.put(STORAGETYPE_FREEZE, (byte)2);

        /** 是否有保质期map */
        SHELFLIFELONGTIME_MAP.put(SHELFLIFELONGTIME_YES, (byte)0);
        SHELFLIFELONGTIME_MAP.put(SHELFLIFELONGTIME_NO, (byte)1);

        /** 保质期单位map */
        SHELFLIFEUNIT_MAP.put(SHELFLIFEUNIT_YEAR, (byte)1);
        SHELFLIFEUNIT_MAP.put(SHELFLIFEUNIT_MONTH, (byte)2);
        SHELFLIFEUNIT_MAP.put(SHELFLIFEUNIT_DAY, (byte)3);

        /** 是否加工 */
        PROCESS_MAP.put(PROCESS_YES, (byte)1);
        PROCESS_MAP.put(PROCESS_NO, (byte)0);

        /** 状态 */
        STATUS__MAP.put(STATUS_YES, (byte)1);
        STATUS__MAP.put(STATUS_NO, (byte)0);

        /** 规格状态 */
        STATE_MAP.put(STATE_YES, (byte)0);
        STATE_MAP.put(STATE_NO, (byte)1);

        SYSTEM_TYPE.put(PDD, (byte)1);
        SYSTEM_TYPE.put(TB, (byte)2);
        SYSTEM_TYPE.put(JD, (byte)3);
        SYSTEM_TYPE.put(YSYX, (byte)4);
        SYSTEM_TYPE.put(WDT, (byte)5);

    }

    /** 保存条件map */
    public static Map<String, Byte> getStorageTypeMap() {
        return STORAGETYPE_MAP;
    }

    /** 是否有保质期map */
    public static Map<String, Byte> getShelfLifeLongTimeMap() {
        return SHELFLIFELONGTIME_MAP;
    }

    /** 保质期单位map */
    public static Map<String, Byte> getShelfLifeUnitMap() {
        return SHELFLIFEUNIT_MAP;
    }

    /** 是否加工 */
    public static Map<String, Byte> getProcessMap() {
        return PROCESS_MAP;
    }

    /** 状态 */
    public static Map<String, Byte> getStatusMap() {
        return STATUS__MAP;
    }

    /** 规格状态 */
    public static Map<String, Byte> getStateMap() {
        return STATE_MAP;
    }

    public static Map<String, Byte> getSystemType() {
        return SYSTEM_TYPE;
    }

}
