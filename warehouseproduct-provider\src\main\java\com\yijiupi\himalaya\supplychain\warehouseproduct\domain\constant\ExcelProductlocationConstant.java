package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

/**
 * 产品货位设置
 * 
 * <AUTHOR>
 * @date 2020/6/16 14:25
 */
public class ExcelProductlocationConstant {
    private ExcelProductlocationConstant() {}

    public static final String SKU = "产品SKUID*";
    public static final String PRODUCTNAME = "产品名称";
    public static final String OWNER = "货主";
    public static final String SPECNAME = "包装规格";
    public static final String SALEMODE = "销售模式";
    public static final String LOCATION = "货位*";
    private static final String COLON = ":";
    private static final String COMMA = ",";
    private static final String SPRIT = "/";

    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {
        /** 表头显示 */
        EXCEL_TABLE_NAME.append(SKU).append(COMMA).append(PRODUCTNAME).append(COMMA).append(OWNER).append(COMMA)
            .append(SPECNAME).append(COMMA).append(LOCATION);
    }
}
