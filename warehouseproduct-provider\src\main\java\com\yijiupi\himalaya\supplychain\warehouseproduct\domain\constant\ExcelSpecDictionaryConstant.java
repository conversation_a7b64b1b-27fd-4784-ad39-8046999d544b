package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 包装规格excel表单字段
 *
 * <AUTHOR>
 * @date 2019-08-30 15:29
 */
public class ExcelSpecDictionaryConstant {

    public static final String UNITNAME = "内包装单位";
    public static final String PACKAGEQUANTITY = "包装数量";
    public static final String PACKAGENAME = "外包装单位";
    public static final String STATE = "状态";

    /**
     * 规格状态
     */
    public static final String STATE_YES = "启用";
    public static final String STATE_NO = "停用";
    static final Map<String, Byte> STATE_MAP = new HashMap<>(16);

    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {
        /** 表头显示 */
        /** 内包装单位,包装数量:INTEGER,外包装单位,状态:启用/停用 */
        EXCEL_TABLE_NAME.append(UNITNAME).append(",").append(PACKAGEQUANTITY).append(":INTEGER").append(",")
            .append(PACKAGENAME).append(",").append(STATE).append(":").append(STATE_YES).append("/").append(STATE_NO);

        /** 规格状态 */
        STATE_MAP.put(STATE_YES, (byte)0);
        STATE_MAP.put(STATE_NO, (byte)1);
    }

    public static Map<String, Byte> getStateMap() {
        return STATE_MAP;
    }
}
