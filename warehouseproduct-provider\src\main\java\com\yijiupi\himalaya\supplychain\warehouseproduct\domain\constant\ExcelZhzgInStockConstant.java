package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品信息excel表单字段
 *
 * <AUTHOR>
 * @date 2019-08-30 15:29
 */
public class ExcelZhzgInStockConstant {

    private ExcelZhzgInStockConstant() {}

    private static final String COLON = ":";
    private static final String COMMA = ",";

    public static final String SPECID = "产品信息规格id";
    public static final String PRODUCTNAME = "产品名称";
    public static final String QUANTITY = "数量";
    public static final String SPECNAME = "规格名称";
    public static final String SUPPLIERNAME = "供应商";
    public static final String PRODUCTBARCODE = "商品条码";
    public static final String BUYER = "采购人员";
    public static final String PRICE = "供货价（参考）";

    /**
     * 单据类型枚举MAP
     */
    static final Map<String, Integer> ORDERTYPE_MAP = new HashMap<>(16);

    /**
     * 表格表头
     */
    public static final StringBuilder EXCEL_TABLE_NAME = new StringBuilder();

    static {
        // "货主编码,货主名称,供应商编码,供应商名称,仓库名称,单据类型:采购入库/调拨入库/退货入库/第三方入库,订单号,产品条码,产品名称,包装规格,大单位数量:INTEGER,大单位,小单位数量:INTEGER,小单位,生产日期,保质期(天),备注"
        EXCEL_TABLE_NAME.append(SPECID).append(COMMA).append(PRODUCTNAME).append(COMMA).append(QUANTITY).append(COMMA)
            .append(SPECNAME).append(COMMA).append(SUPPLIERNAME).append(COLON).append(PRODUCTBARCODE).append(COMMA)
            .append(BUYER).append(COMMA).append(PRICE);
    }

}
