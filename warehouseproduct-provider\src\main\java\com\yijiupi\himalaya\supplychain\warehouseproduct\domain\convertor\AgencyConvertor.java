package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AgencyInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2017/11/24
 */
public class AgencyConvertor {

    public static List<AgencyInfoDTO> agencyInfoPOList2DTO(List<AgencyInfoPO> poList) {
        ArrayList<AgencyInfoDTO> dtoList = new ArrayList<>();
        poList.forEach(n -> {
            dtoList.add(agencyInfoPO2DTO(n));
        });
        return dtoList;
    }

    public static AgencyInfoDTO agencyInfoPO2DTO(AgencyInfoPO agencyInfoPO) {
        AgencyInfoDTO dto = new AgencyInfoDTO();
        dto.setAgencyId(agencyInfoPO.getAgencyId());
        dto.setAgencyName(agencyInfoPO.getAgencyName());
        dto.setRemark(agencyInfoPO.getRemark());
        return dto;
    }
}
