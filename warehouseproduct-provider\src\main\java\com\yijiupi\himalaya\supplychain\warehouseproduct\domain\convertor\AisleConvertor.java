package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleDTO;

/**
 * 巷道
 *
 * <AUTHOR>
 * @date 2024/11/2
 */
public class AisleConvertor {

    /**
     * AislePO转换成AisleDTO
     * 
     * @return
     */
    public static AisleDTO convertorToAisleDTO(AislePO po) {
        if (null == po) {
            return null;
        }
        AisleDTO dto = new AisleDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * List<AislePO>转换成List<AisleDTO>
     * 
     * @return
     */
    public static List<AisleDTO> convertorToAisleDTOList(List<AislePO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<AisleDTO> dtoList = poList.stream().map(AisleConvertor::convertorToAisleDTO).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * AisleDTO转换成AislePO
     * 
     * @return
     */
    public static AislePO convertorToAislePO(AisleDTO dto) {
        if (null == dto) {
            return null;
        }
        AislePO po = new AislePO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * List<AisleDTO>转换成List<AislePO>
     * 
     * @return
     */
    public static List<AislePO> convertorToAislePOList(List<AisleDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<AislePO> poList = dtoList.stream().map(AisleConvertor::convertorToAislePO).collect(Collectors.toList());
        return poList;
    }

}
