package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AssetsChangeRecordDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class AssetsChangeRecordConvertor extends Convertor<AssetsChangeRecordPO, AssetsChangeRecordDTO> {

    @Override
    public AssetsChangeRecordDTO convert(AssetsChangeRecordPO po) {
        if (po == null) {
            return null;
        }
        AssetsChangeRecordDTO dto = new AssetsChangeRecordDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public AssetsChangeRecordPO reverseConvert(AssetsChangeRecordDTO dto) {
        if (dto == null) {
            return null;
        }
        AssetsChangeRecordPO po = new AssetsChangeRecordPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }
}
