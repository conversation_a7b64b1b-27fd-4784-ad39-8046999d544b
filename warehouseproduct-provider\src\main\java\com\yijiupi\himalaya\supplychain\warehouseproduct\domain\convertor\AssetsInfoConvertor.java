package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AssetsInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AssetsTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class AssetsInfoConvertor extends Convertor<AssetsInfoPO, AssetsInfoDTO> {

    @Override
    public AssetsInfoDTO convert(AssetsInfoPO po) {
        if (po == null) {
            return null;
        }
        if (po.getVolume() == null) {
            // 计算体积
            BigDecimal volume = getVolume(po.getLength(), po.getWidth(), po.getHeight());
            po.setVolume(volume);
        }
        AssetsInfoDTO dto = new AssetsInfoDTO();
        BeanUtils.copyProperties(po, dto);
        // 设置资产名称
        dto.setAssetsTypeName(AssetsTypeEnum.getEnmuName(po.getAssetsType()));
        return dto;
    }

    @Override
    public AssetsInfoPO reverseConvert(AssetsInfoDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.getVolume() == null) {
            // 计算体积
            BigDecimal volume = getVolume(dto.getLength(), dto.getWidth(), dto.getHeight());
            dto.setVolume(volume);
        }
        AssetsInfoPO po = new AssetsInfoPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * 根据长宽高计算体积
     */
    public BigDecimal getVolume(BigDecimal length, BigDecimal width, BigDecimal height) {
        if (length == null || width == null || height == null) {
            return null;
        }
        return length.multiply(width).multiply(height);
    }
}
