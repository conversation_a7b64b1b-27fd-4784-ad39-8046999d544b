package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;

/**
 * <AUTHOR> 2018/1/19
 */
public class LocationAreaConvetor {
    /**
     * 拼装
     *
     * @return
     */
    public static LocationAreaPO getLocationAreaPO(LocationAreaPO locationAreaPO, LocationAreaDTO locationAreaDTO) {
        locationAreaPO.setLastUpdateUserId(locationAreaDTO.getUserId());
        locationAreaPO.setLastUpdateTime(new Date());
        locationAreaPO.setCreateTime(new Date());
        locationAreaPO.setSubcategory(locationAreaDTO.getSubcategory());
        locationAreaPO.setCreateUserId(locationAreaDTO.getUserId());
        locationAreaPO.setLocationCapacity(locationAreaDTO.getLocationCapacity());
        return locationAreaPO;
    }

    /**
     * 转换DTO
     */
    public static LocationAreaPO locationAreaDTO2PO(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaPO = new LocationAreaPO();
        locationAreaPO.setWarehouse_Id(locationAreaDTO.getWarehouseId());
        locationAreaPO.setCity_Id(locationAreaDTO.getCityId());
        locationAreaPO.setName(locationAreaDTO.getArea());
        locationAreaPO.setRemo(locationAreaDTO.getRemo());
        locationAreaPO.setSubcategory(locationAreaDTO.getSubcategory());

        locationAreaPO.setWidth(locationAreaDTO.getWidth());
        locationAreaPO.setHeight(locationAreaDTO.getHeight());
        locationAreaPO.setCoordinateX(locationAreaDTO.getCoordinateX());
        locationAreaPO.setCoordinateY(locationAreaDTO.getCoordinateY());
        locationAreaPO.setLayer(locationAreaDTO.getLayer());
        locationAreaPO.setSubCategories(locationAreaDTO.getSubCategories());
        locationAreaPO.setState(locationAreaDTO.getState());
        locationAreaPO.setBusinessType(locationAreaDTO.getBusinessType());
        locationAreaPO.setExcludeIds(locationAreaDTO.getExcludeIds());
        return locationAreaPO;
    }

    /**
     * 转换list
     */
    public static List<LocationAreaReturnDTO> locationAreaPOList2DTO(List<LocationAreaPO> ls, Map<Long, Integer> map) {
        return ls.stream().map(n -> toDTO(n, map)).collect(Collectors.toList());
    }

    private static LocationAreaReturnDTO toDTO(LocationAreaPO n, Map<Long, Integer> map) {
        LocationAreaReturnDTO locationAreaReturnDTO = new LocationAreaReturnDTO();
        BeanUtils.copyProperties(n, locationAreaReturnDTO);
        locationAreaReturnDTO.setAreaState(n.getState());
        if (map != null) {
            locationAreaReturnDTO.setState(map.get(n.getId()));
            locationAreaReturnDTO
                .setSubcategoryName(LocationAreaEnum.getEnumStr(locationAreaReturnDTO.getSubcategory()));
        }
        return locationAreaReturnDTO;
    }

    public static LocationAreaPO getLocationAreaPO(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaPO = new LocationAreaPO();
        locationAreaPO.setId(locationAreaDTO.getId());
        locationAreaPO.setRemo(locationAreaDTO.getRemo());
        locationAreaPO.setLastUpdateUserId(locationAreaDTO.getUserId());
        locationAreaPO.setLocationCapacity(locationAreaDTO.getLocationCapacity());
        return locationAreaPO;
    }

    public static LocationAreaPO getUpdateLocationAreaPO(LocationAreaDTO locationAreaDTO) {
        LocationAreaPO locationAreaPO = new LocationAreaPO();
        locationAreaPO.setId(locationAreaDTO.getId());
        locationAreaPO.setName(locationAreaDTO.getArea());
        locationAreaPO.setRemo(locationAreaDTO.getRemo());
        locationAreaPO.setLastUpdateUserId(locationAreaDTO.getUserId());
        locationAreaPO.setLastUpdateTime(new Date());
        locationAreaPO.setSubcategory(locationAreaDTO.getSubcategory());
        locationAreaPO.setLocationCapacity(locationAreaDTO.getLocationCapacity());
        locationAreaPO.setState(locationAreaDTO.getState());

        locationAreaPO.setWidth(locationAreaDTO.getWidth());
        locationAreaPO.setHeight(locationAreaDTO.getHeight());
        locationAreaPO.setCoordinateX(locationAreaDTO.getCoordinateX());
        locationAreaPO.setCoordinateY(locationAreaDTO.getCoordinateY());
        locationAreaPO.setLayer(locationAreaDTO.getLayer());
        locationAreaPO.setBusinessType(locationAreaDTO.getBusinessType());
        return locationAreaPO;
    }
}
