package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;

import java.util.Objects;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public class LocationConvertor {

    public static LoactionDTO convertToDTO(LocationPO locationPO) {
        if (Objects.isNull(locationPO)) {
            return null;
        }

        LoactionDTO loactionDTO = new LoactionDTO();
        loactionDTO.setLastUpdateTime(locationPO.getLastUpdateTime());
        loactionDTO.setExpress(locationPO.getExpress());
        loactionDTO.setLocationGrade(locationPO.getLocationGrade());
        loactionDTO.setArea_Id(locationPO.getArea_Id());
        loactionDTO.setSequence(locationPO.getSequence());
        loactionDTO.setId(locationPO.getId());
        loactionDTO.setName(locationPO.getName());
        loactionDTO.setArea(locationPO.getArea());
        loactionDTO.setRoadway(locationPO.getRoadWay());
        loactionDTO.setProductlocation(locationPO.getProductLocation());
        loactionDTO.setPallets(locationPO.getPallets());
        loactionDTO.setCityId(locationPO.getCity_Id());
        loactionDTO.setWarehouseId(locationPO.getWarehouse_Id());
        loactionDTO.setCategory(locationPO.getCategory());
        loactionDTO.setSubcategory(locationPO.getSubcategory());
        loactionDTO.setIsChaosPut(locationPO.getIsChaosPut());
        loactionDTO.setIsChaosBatch(locationPO.getIsChaosBatch());
        loactionDTO.setLocationCapacity(locationPO.getLocationCapacity());
        loactionDTO.setState(locationPO.getState());
        loactionDTO.setWidth(locationPO.getWidth());
        loactionDTO.setHeight(locationPO.getHeight());
        loactionDTO.setCoordinateX(locationPO.getCoordinateX());
        loactionDTO.setCoordinateY(locationPO.getCoordinateY());
        loactionDTO.setLayer(locationPO.getLayer());
        loactionDTO.setAreaId(locationPO.getArea_Id());
        loactionDTO.setRoadWay(locationPO.getRoadWay());
        loactionDTO.setPalletCount(locationPO.getPalletCount());
        loactionDTO.setAisleId(locationPO.getAisleId());
        loactionDTO.setAisleNo(locationPO.getAisleNo());

        return loactionDTO;
    }

}
