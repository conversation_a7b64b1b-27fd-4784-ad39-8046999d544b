package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.ReportLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 货位dto转换类
 *
 * <AUTHOR>
 * @since 2020年6月5日 上午11:18:39
 */
public class LocationConvetor {

    private LocationConvetor() {
    }

    /**
     * 转换DTO
     */
    public static List<LoactionDTO> loactionListPO2DTO(List<LocationPO> loactionPOs) {
        List<LoactionDTO> dtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loactionPOs)) {
            loactionPOs.forEach(loactionPO -> {
                dtos.add(loactionPO2DTO(loactionPO));
            });
        }
        return dtos;
    }

    public static LoactionDTO loactionPO2DTO(LocationPO loactionPO) {
        LoactionDTO loactionDTO = new LoactionDTO();
        BeanUtils.copyProperties(loactionPO, loactionDTO);
        loactionDTO.setCityId(loactionPO.getCity_Id());
        loactionDTO.setWarehouseId(loactionPO.getWarehouse_Id());
        return loactionDTO;
    }

    /**
     * 转换DTO
     */
    public static List<LocationPO> loactionListDTO2PO(List<LoactionDTO> loactionDTOs) {
        List<LocationPO> pos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loactionDTOs)) {
            loactionDTOs.forEach(loactionDTO -> {
                pos.add(loactionDTO2PO(loactionDTO));
            });
        }
        return pos;
    }

    public static LocationPO loactionDTO2PO(LoactionDTO loactionDTO) {
        LocationPO locationPO = new LocationPO();
        BeanUtils.copyProperties(loactionDTO, locationPO);
        locationPO.setCity_Id(loactionDTO.getCityId());
        locationPO.setWarehouse_Id(loactionDTO.getWarehouseId());
        locationPO.setCreateUserId(loactionDTO.getUserId());
        locationPO.setLastUpdateUserId(loactionDTO.getUserId());
        locationPO.setRoadWay(loactionDTO.getRoadway());
        locationPO.setProductLocation(loactionDTO.getProductlocation());
        return locationPO;
    }

    public static List<LocationPO> reportToLocationPO(List<ReportLocationDTO> reportList) {
        if (CollectionUtils.isEmpty(reportList)) {
            return new ArrayList<>();
        }
        List<LocationPO> toList = new ArrayList<>();
        reportList.forEach(report -> {
            LocationPO to = new LocationPO();
            BeanUtils.copyProperties(report, to);
            toList.add(to);
        });
        return toList;
    }

    /**
     * 转换 DTO
     * @return 转换结果, 永不为 null, 如果入参为 null, 那就返回空 ArrayList
     */
    public static List<LoactionDTO> convertDTOS(List<LocationPO> locationPOS) {
        List<LoactionDTO> dtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(locationPOS)) {
            locationPOS.forEach(locationPO -> dtoList.add(loactionPO2DTO(locationPO)));
        }
        return dtoList;
    }

    public static PageList<LoactionDTO> convertDTOS(PageResult<LocationPO> ids, List<LocationPO> result) {
        Map<Long, LocationPO> resultMap = result.stream()
                .collect(Collectors.toMap(LocationPO::getId, Function.identity()));
        return ids.toPageList(it -> it.stream()
                .map(item -> resultMap.get(item.getId())).map(LocationConvetor::loactionPO2DTO)
                .collect(Collectors.toList())
        );
    }

}
