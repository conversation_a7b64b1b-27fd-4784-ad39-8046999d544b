package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSONObject;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AdministrativeRegionExtInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationRuleEnum;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * <AUTHOR>
 */
public class LocationRuleConvert {
    public static List<LocationRuleDTO> convertQueryList(List<LocationRulePO> locationRulePOS) {
        List<LocationRuleDTO> locationRuleDTOS = new ArrayList<>();
        for (LocationRulePO locationRulePO : locationRulePOS) {
            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            BeanUtils.copyProperties(locationRulePO, locationRuleDTO);
            String extInfo = locationRulePO.getExtInfo();
            if (StringUtils.isNotBlank(extInfo)) {
                AdministrativeRegionExtInfoDTO administrativeRegionExtInfoDTO =
                    JSONObject.parseObject(extInfo, AdministrativeRegionExtInfoDTO.class);
                locationRuleDTO
                    .setCityParentId(String.valueOf(ObjectUtils.isEmpty(administrativeRegionExtInfoDTO.getCityId())
                        ? StringUtils.EMPTY : administrativeRegionExtInfoDTO.getCityId()));
                locationRuleDTO.setProvinceParentId(
                    String.valueOf(ObjectUtils.isEmpty(administrativeRegionExtInfoDTO.getProvinceId())
                        ? StringUtils.EMPTY : administrativeRegionExtInfoDTO.getProvinceId()));
                if (StringUtils.isNotBlank(administrativeRegionExtInfoDTO.getStreet())) {
                    locationRuleDTO.setParentId(administrativeRegionExtInfoDTO.getDistrictId());
                } else if (StringUtils.isNotBlank(administrativeRegionExtInfoDTO.getDistrict())) {
                    locationRuleDTO.setParentId(administrativeRegionExtInfoDTO.getCityId());
                } else if (StringUtils.isNotBlank(administrativeRegionExtInfoDTO.getCity())) {
                    locationRuleDTO.setParentId(administrativeRegionExtInfoDTO.getProvinceId());
                }
            }
            locationRuleDTOS.add(locationRuleDTO);
        }
        return locationRuleDTOS;
    }

    public static List<LocationRulePO> convertSave(List<LocationRuleDTO> locationRuleDTOList) {
        List<LocationRulePO> locationRulePOList = new ArrayList<>();
        for (LocationRuleDTO locationRuleDTO : locationRuleDTOList) {
            if (isNullRuleInfo(locationRuleDTO)) {
                continue;
            }
            LocationRulePO locationRulePO = new LocationRulePO();
            BeanUtils.copyProperties(locationRuleDTO, locationRulePO);
            locationRulePO.setId(UUIDGenerator.getUUID("locationRule"));
            locationRulePOList.add(locationRulePO);
        }
        return locationRulePOList;
    }

    private static boolean isNullRuleInfo(LocationRuleDTO locationRuleDTO) {
        if (StringUtils.isBlank(locationRuleDTO.getRuleId())) {
            return Boolean.TRUE;
        }
        if (locationRuleDTO.getRuleId().equals("null")) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static void convert(LocationRulePO locationRulePO, LocationRuleDTO locationRuleDTO) {
        locationRuleDTO.setLocationId(locationRulePO.getLocationId());
        locationRuleDTO.setLocationName(locationRulePO.getLocationName());
        locationRuleDTO.setRuleId(locationRulePO.getRuleId());
        locationRuleDTO.setRuleName(locationRulePO.getRuleName());
        locationRuleDTO.setRuleType(locationRulePO.getRuleType());
    }

    public static List<LocationRuleDTO> convertLocation(List<LocationRulePO> locationRulePOS, List<LoactionDTO> dtoList,
        WarehouseConfigDTO warehouseDto, LocationRuleDTO queryDTO) {
        List<LocationRuleDTO> locationRuleDTOS = new ArrayList<>();
        for (LoactionDTO loactionDTO : dtoList) {
            LocationRuleDTO locationRuleDTO = new LocationRuleDTO();
            locationRuleDTO.setLocationName(loactionDTO.getName());
            locationRuleDTO.setLocationId(loactionDTO.getId());
            List<LocationRulePO> collect = locationRulePOS.stream()
                .filter(l -> l.getLocationId().compareTo(loactionDTO.getId()) == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                locationRuleDTO.setRuleName(getRuleName(collect, queryDTO));
            }
            if (!CollectionUtils.isEmpty(collect) && collect.stream()
                .allMatch(p -> p.getRuleType().compareTo(LocationRuleEnum.ADMINISTRATIVE_REGION.getCode()) == 0)) {
                LocationRulePO locationRulePO = collect.get(0);
                String extInfo = locationRulePO.getExtInfo();
                if (StringUtils.isNotBlank(extInfo)) {
                    AdministrativeRegionExtInfoDTO administrativeRegionExtInfoDTO =
                        JSONObject.parseObject(extInfo, AdministrativeRegionExtInfoDTO.class);
                    locationRuleDTO.setCityParentId(String.valueOf(administrativeRegionExtInfoDTO.getCityId()));
                    locationRuleDTO.setProvinceParentId(String.valueOf(administrativeRegionExtInfoDTO.getProvinceId()));
                }
            }
            if (!ObjectUtils.isEmpty(warehouseDto.getDefaultLocationId())
                && warehouseDto.getDefaultLocationId().compareTo(loactionDTO.getId()) == 0) {
                String ruleName = locationRuleDTO.getRuleName();
                if (StringUtils.isNotBlank(ruleName)) {
                    locationRuleDTO.setRuleName(ruleName + "、默认出库位");
                } else {
                    locationRuleDTO.setRuleName("默认出库位");
                }
            }
            locationRuleDTO.setConsolidationGoodsType(loactionDTO.getSubcategory().toString());
            locationRuleDTO.setPalletCount(loactionDTO.getPalletCount());
            locationRuleDTOS.add(locationRuleDTO);
        }
        return locationRuleDTOS;
    }

    private static String getRuleName(List<LocationRulePO> collect, LocationRuleDTO queryDTO) {
        if (!LocationRuleEnum.ADMINISTRATIVE_REGION.getCode().equals(queryDTO.getRuleType())) {
            return String.join("、", collect.stream().map(LocationRulePO::getRuleName).collect(Collectors.toList()));
        }

        return collect.stream().map(locationRulePO -> {
            return buildAreaInfo(locationRulePO);
        }).collect(Collectors.joining(","));
    }

    private static String buildAreaInfo(LocationRulePO locationRulePO) {
        String extInfo = locationRulePO.getExtInfo();
        if (StringUtils.isBlank(extInfo)) {
            return locationRulePO.getRuleName();
        }
        JSONObject jsonObject = JSONObject.parseObject(extInfo);
        String province = jsonObject.getString("province");
        String city = jsonObject.getString("city");
        String district = jsonObject.getString("district");
        String street = jsonObject.getString("street");

        StringBuffer stringBuffer = new StringBuffer();
        boolean hasContent = false;
        if (StringUtils.isNotBlank(province)) {
            stringBuffer.append(province);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(city)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(city);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(district)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(district);
            hasContent = true;
        }
        if (StringUtils.isNotBlank(street)) {
            if (hasContent) {
                stringBuffer.append("-");
            }
            stringBuffer.append(street);
        }

        String areaInfo = stringBuffer.toString();
        if (StringUtils.isNotBlank(areaInfo)) {
            return areaInfo;
        }

        return locationRulePO.getRuleName();
    }
}
