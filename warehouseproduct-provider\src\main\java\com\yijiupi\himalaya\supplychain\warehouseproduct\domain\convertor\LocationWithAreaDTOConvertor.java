package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationWithAreaDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
public class LocationWithAreaDTOConvertor {

    public static List<LocationWithAreaDTO> convertList(List<LoactionDTO> locationList, List<LoactionDTO> areaList) {
        Map<Long, LoactionDTO> areaMap = areaList.stream().collect(Collectors.toMap(LoactionDTO::getId, v -> v));

        return locationList.stream().map(location -> {
            LocationWithAreaDTO locationWithAreaDTO = new LocationWithAreaDTO();
            BeanUtils.copyProperties(location, locationWithAreaDTO);
            LoactionDTO areaDTO = areaMap.get(location.getArea_Id());
            LocationWithAreaDTO area = new LocationWithAreaDTO();
            BeanUtils.copyProperties(areaDTO, area);
            locationWithAreaDTO.setAreaDTO(area);
            return locationWithAreaDTO;
        }).collect(Collectors.toList());
    }

}
