package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.github.pagehelper.StringUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PartnertStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.CommonUtils;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerStateEnum;

/**
 * 货主转化类
 *
 * <AUTHOR>
 * @date 2018/11/15 17:56
 */
public class OwnerConvertor {

    /**
     * OwnerDTO转化成OwnerPO
     *
     * @param ownerDTO
     * @return
     */
    public static OwnerPO converToOwnerPO(OwnerDTO ownerDTO) {
        if (null == ownerDTO) {
            return null;
        }
        OwnerPO ownerPO = new OwnerPO();
        BeanUtils.copyProperties(ownerDTO, ownerPO);
        ownerPO.setAddress(CommonUtils.getMaxLen(ownerPO.getAddress(), 50));
        return ownerPO;
    }

    /**
     * List<OwnerDTO>转化成List<OwnerPO>
     *
     * @param dtoList
     * @return
     */
    public static List<OwnerPO> converToOwnerPOList(List<OwnerDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<OwnerPO> poList = dtoList.stream().map(OwnerConvertor::converToOwnerPO).collect(Collectors.toList());
        return poList;
    }

    /**
     * OwnerPO转化成OwnerDTO
     *
     * @param ownerPO
     * @return
     */
    public static OwnerDTO converToOwnerDTO(OwnerPO ownerPO) {
        if (null == ownerPO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        BeanUtils.copyProperties(ownerPO, ownerDTO);
        return ownerDTO;
    }

    /**
     * List<OwnerPO>转化成List<OwnerDTO>
     *
     * @param poList
     * @return
     */
    public static List<OwnerDTO> converToOwnerDTOList(List<OwnerPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<OwnerDTO> dtoList = poList.stream().map(OwnerConvertor::converToOwnerDTO).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * supplierMessageDTO转化成OwnerDTO
     *
     * @param supplierMessageDTO
     * @return
     */
    public static OwnerDTO converToOwnerDTO(SupplierMessageDTO supplierMessageDTO) {
        if (null == supplierMessageDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setId(supplierMessageDTO.getSupplierId());
        ownerDTO.setCityId(
            supplierMessageDTO.getSystemOrgId() == null ? null : supplierMessageDTO.getSystemOrgId().intValue());
        ownerDTO.setOwnerName(supplierMessageDTO.getSupplierName());
        ownerDTO.setState(supplierMessageDTO.getState() == null ? null
            : (supplierMessageDTO.getState() == 0 ? OwnerStateEnum.启用.getType() : OwnerStateEnum.停用.getType()));
        ownerDTO.setProvince(supplierMessageDTO.getProvince());
        ownerDTO.setCity(supplierMessageDTO.getCity());
        ownerDTO.setCounty(supplierMessageDTO.getCounty());
        ownerDTO.setStreet(supplierMessageDTO.getStreet());
        ownerDTO.setAddress(supplierMessageDTO.getAddress());
        ownerDTO.setMobileNo(supplierMessageDTO.getMobile());
        ownerDTO.setUserName(supplierMessageDTO.getLinkName());
        return ownerDTO;
    }

    /**
     * supplierMessageDTO转化成OwnerDTO
     *
     * @param regionMessageDTO
     * @return
     */
    public static OwnerDTO converToOwnerDTO(RegionMessageDTO regionMessageDTO) {
        if (null == regionMessageDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setId(regionMessageDTO.getRegionId());
        ownerDTO
            .setCityId(regionMessageDTO.getWmsRegionId() == null ? null : regionMessageDTO.getWmsRegionId().intValue());
        ownerDTO.setOwnerName(regionMessageDTO.getRegionName());
        ownerDTO.setProvince(regionMessageDTO.getProvince());
        ownerDTO
            .setRefPartnerId(regionMessageDTO.getRegionId() != null ? regionMessageDTO.getRegionId().toString() : null);
        return ownerDTO;
    }

    /**
     * syncRegionChangeDTO转化成OwnerDTO
     *
     * @param syncRegionChangeDTO
     * @return
     */
    public static OwnerDTO converToOwnerDTO(SyncRegionChangeDTO syncRegionChangeDTO) {
        if (null == syncRegionChangeDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setId(syncRegionChangeDTO.getOrgId());
        ownerDTO.setOwnerName(syncRegionChangeDTO.getOrgName());
        ownerDTO.setProvince(syncRegionChangeDTO.getProvince());
        ownerDTO.setMobileNo(syncRegionChangeDTO.getConsumerHotline());
        return ownerDTO;
    }

    /**
     * shopErpDTO转化成OwnerDTO
     *
     * @param shopErpDTO
     * @return
     */
    public static OwnerDTO converToOwnerDTO(ShopErpDTO shopErpDTO) {
        if (null == shopErpDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setId(shopErpDTO.getId() == null ? null : Long.valueOf(shopErpDTO.getId()));
        ownerDTO.setCityId(shopErpDTO.getCityId() == null ? null : Integer.valueOf(shopErpDTO.getCityId()));
        ownerDTO.setOwnerName(
            StringUtil.isEmpty(shopErpDTO.getOwnerName()) ? shopErpDTO.getName() : shopErpDTO.getOwnerName());
        ownerDTO.setProvince(shopErpDTO.getProvince());
        ownerDTO.setCity(shopErpDTO.getCity());
        ownerDTO.setCounty(shopErpDTO.getCountry());
        ownerDTO.setStreet(shopErpDTO.getStreet());
        ownerDTO.setAddress(shopErpDTO.getAddress());
        ownerDTO.setMobileNo(shopErpDTO.getLeadUserMobileNo());
        ownerDTO.setUserName(shopErpDTO.getLeadUserName());
        ownerDTO.setState(shopErpDTO.getState() == null ? null : Byte.valueOf(shopErpDTO.getState()));
        ownerDTO.setRefPartnerId(shopErpDTO.getInfoId());
        ownerDTO.setCreateUserId(-1L);
        ownerDTO.setLastUpdateUserId(-1L);
        return ownerDTO;
    }

    /**
     * PartnerSyncByErpDTO转化成OwnerDTO
     *
     * @return
     */
    public static OwnerDTO converToOwnerDTO(PartnerSyncByErpDTO partnerSyncByErpDTO) {
        if (null == partnerSyncByErpDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setCompanyId(1L);
        ownerDTO.setRefPartnerId(partnerSyncByErpDTO.getInfoId());
        ownerDTO.setOwnerName(partnerSyncByErpDTO.getName());
        ownerDTO
            .setAddress((partnerSyncByErpDTO.getAddress() != null && partnerSyncByErpDTO.getAddress().length() > 100)
                ? partnerSyncByErpDTO.getAddress().substring(0, 100) : partnerSyncByErpDTO.getAddress());
        ownerDTO.setState(partnerSyncByErpDTO.getEnableState() == null ? PartnertStateEnum.启用.getType()
            : partnerSyncByErpDTO.getEnableState());
        ownerDTO.setUserName(partnerSyncByErpDTO.getLeadUserName());
        ownerDTO.setMobileNo(partnerSyncByErpDTO.getLeadUserMobileNo());
        return ownerDTO;
    }

    /**
     * SupplierRegisterDTO转化成OwnerDTO
     *
     * @return
     */
    public static OwnerDTO converToOwnerDTO(SupplierRegisterDTO supplierRegisterDTO) {
        if (null == supplierRegisterDTO) {
            return null;
        }
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setCompanyId(1L);
        ownerDTO.setRefPartnerId(supplierRegisterDTO.getInfoId());
        ownerDTO.setOwnerName(supplierRegisterDTO.getBusinessLicense() != null
            ? supplierRegisterDTO.getBusinessLicense().getCompanyName() : null);
        ownerDTO.setAddress(
            (supplierRegisterDTO.getDetailAddress() != null && supplierRegisterDTO.getDetailAddress().length() > 100)
                ? supplierRegisterDTO.getDetailAddress().substring(0, 100) : supplierRegisterDTO.getDetailAddress());
        ownerDTO.setState(supplierRegisterDTO.getEnableState() == null ? PartnertStateEnum.启用.getType()
            : supplierRegisterDTO.getEnableState().byteValue());
        ownerDTO.setUserName(supplierRegisterDTO.getContactName());
        ownerDTO.setMobileNo(supplierRegisterDTO.getContactPhone());
        return ownerDTO;
    }
}
