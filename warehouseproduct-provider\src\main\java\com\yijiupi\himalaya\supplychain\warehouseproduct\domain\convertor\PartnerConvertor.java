package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PartnerManagerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 货主转化类
 *
 * <AUTHOR>
 * @date 2018/11/15 17:56
 */
public class PartnerConvertor {

    /**
     * OwnerManagerDTO转化成OwnerManagerPO
     * 
     * @param dto
     * @return
     */
    public static PartnerManagerPO converToPO(PartnerManagerDTO dto) {
        if (null == dto) {
            return null;
        }
        PartnerManagerPO po = new PartnerManagerPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * List<OwnerManagerDTO>转化成List<OwnerManagerPO>
     * 
     * @param dtoList
     * @return
     */
    public static List<PartnerManagerPO> converToPOList(List<PartnerManagerDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<PartnerManagerPO> poList = dtoList.stream().map(PartnerConvertor::converToPO).collect(Collectors.toList());
        return poList;
    }

    /**
     * OwnerManagerPO转化成OwnerManagerDTO
     * 
     * @param po
     * @return
     */
    public static PartnerManagerDTO converToDTO(PartnerManagerPO po) {
        if (null == po) {
            return null;
        }
        PartnerManagerDTO dto = new PartnerManagerDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * List<OwnerManagerPO>转化成List<OwnerManagerDTO>
     * 
     * @param poList
     * @return
     */
    public static List<PartnerManagerDTO> converToDTOList(List<PartnerManagerPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<PartnerManagerDTO> dtoList =
            poList.stream().map(PartnerConvertor::converToDTO).collect(Collectors.toList());
        return dtoList;
    }

}
