package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class PassageConverter {

    public static List<PassageDTO> POS2DTOS(List<PassagePO> passagePOS) {
        if (CollectionUtils.isEmpty(passagePOS)) {
            return new ArrayList<>();
        }

        List<PassageDTO> passageDTOS = new ArrayList<>();
        passagePOS.forEach(po -> {
            PassageDTO dto = new PassageDTO();
            BeanUtils.copyProperties(po, dto);

            dto.setItemList(itemPOS2ItemDTOS(po.getPassageItemPOS()));

            passageDTOS.add(dto);
        });
        return passageDTOS;
    }

    public static List<PassageItemDTO> itemPOS2ItemDTOS(List<PassageItemPO> passageItemPOS) {
        if (CollectionUtils.isEmpty(passageItemPOS)) {
            return new ArrayList<>();
        }
        List<PassageItemDTO> passageItemDTOS = new ArrayList<>();
        passageItemPOS.forEach(po -> {
            PassageItemDTO dto = new PassageItemDTO();
            BeanUtils.copyProperties(po, dto);

            passageItemDTOS.add(dto);
        });
        return passageItemDTOS;
    }
}
