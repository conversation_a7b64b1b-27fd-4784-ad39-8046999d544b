package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.Collections;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ProductLocationAssociateBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickLocationModToBatchTaskDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.PickLocationModToBatchTaskItemDTO;
import org.apache.commons.lang3.BooleanUtils;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/3
 */
public class PickLocationModToBatchTaskDTOConvertor {

    public static PickLocationModToBatchTaskDTO convert(ProductLocationPO oriPO, ProductLocationAssociateBO bo,
                                                        LocationPO toLocation) {
        if (LocationEnum.分拣位.getType().byteValue() != toLocation.getSubcategory()) {
            return null;
        }
        ProductLocationDTO dto = bo.getDto();
        PickLocationModToBatchTaskDTO modDTO = new PickLocationModToBatchTaskDTO();
        modDTO.setWarehouseId(oriPO.getWarehouse_Id());
        modDTO.setOptUserId(dto.getUserId());

        PickLocationModToBatchTaskItemDTO modItemDTO = new PickLocationModToBatchTaskItemDTO();
        modItemDTO.setSkuId(oriPO.getProductSku_Id());
        if (BooleanUtils.isFalse(bo.isCreate())) {
            modItemDTO.setFromLocationId(oriPO.getLocation_Id());
        }
        modItemDTO.setToLocationId(dto.getLocationId());

        modDTO.setLocationInfoList(Collections.singletonList(modItemDTO));

        return modDTO;
    }

}
