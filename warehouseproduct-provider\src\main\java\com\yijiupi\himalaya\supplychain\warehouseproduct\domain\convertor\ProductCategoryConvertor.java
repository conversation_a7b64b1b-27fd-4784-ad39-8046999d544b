package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductStatisCategoryDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ProductCategoryConvertor {

    public static List<ProductCategoryDTO>
        productInfoCategoryDTOS2ProductCategoryDTOS(List<ProductInfoCategoryDTO> productInfoCategoryDTOS) {
        if (CollectionUtils.isEmpty(productInfoCategoryDTOS)) {
            return new ArrayList<>();
        }
        List<ProductCategoryDTO> productCategoryDTOS = new ArrayList<>();
        productInfoCategoryDTOS.forEach(infoCategory -> {
            ProductCategoryDTO productCategoryDTO = new ProductCategoryDTO();
            BeanUtils.copyProperties(infoCategory, productCategoryDTO);
            productCategoryDTO.setId(infoCategory.getProductInfoId());

            productCategoryDTOS.add(productCategoryDTO);
        });
        return productCategoryDTOS;
    }

    /**
     * 类目树转换
     * 
     * @param productCategoryGroupTree
     * @return
     */
    public static List<ProductStatisCategoryDTO>
        productCategoryGroupTree2ProductStatisCategoryDTOS(List<ProductCategoryGroupDTO> productCategoryGroupTree) {
        if (CollectionUtils.isEmpty(productCategoryGroupTree)) {
            return new ArrayList<>();
        }
        List<ProductStatisCategoryDTO> productStatisCategoryDTOS = new ArrayList<>();
        productCategoryGroupTree.forEach(categoryBranch -> {
            ProductStatisCategoryDTO productStatisCategoryDTO = new ProductStatisCategoryDTO();
            productStatisCategoryDTO.setId(categoryBranch.getId());
            productStatisCategoryDTO.setName(categoryBranch.getName());
            productStatisCategoryDTO.setChilds(
                productCategoryGroupTree2ProductStatisCategoryDTOS(categoryBranch.getChildProductCategoryGroupDTOS()));

            productStatisCategoryDTOS.add(productStatisCategoryDTO);
        });
        return productStatisCategoryDTOS;
    }
}
