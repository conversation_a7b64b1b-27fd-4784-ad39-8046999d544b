package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.springframework.beans.BeanUtils;

/**
 * 控货策略
 *
 * <AUTHOR>
 * @date 2019-10-12 18:00
 */
public class ProductControlConfigConvertor {

    /**
     * 转化为productControlConfigPO
     * 
     * @return
     */
    public static ProductControlConfigPO convertToProductControlConfigPO(ProductControlConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductControlConfigPO po = new ProductControlConfigPO();
        BeanUtils.copyProperties(dto, po);
        if (po.getId() == null) {
            po.setId(UUIDGenerator.getUUID(ProductControlConfigDTO.class.getName()));
        }
        return po;
    }

    /**
     * 转化为productControlConfigDTO
     * 
     * @return
     */
    public static ProductControlConfigDTO convertToProductControlConfigDTO(ProductControlConfigPO po) {
        if (po == null) {
            return null;
        }
        ProductControlConfigDTO dto = new ProductControlConfigDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 转化为productControlConfigItemPO
     * 
     * @return
     */
    public static ProductControlConfigItemPO convertToProductControlConfigItemPO(ProductControlConfigItemDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductControlConfigItemPO po = new ProductControlConfigItemPO();
        BeanUtils.copyProperties(dto, po);
        if (po.getId() == null) {
            po.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_CONTROL_CONFIG_ITEM));
        }
        return po;
    }

    /**
     * 转化为productControlConfigItemDTO
     * 
     * @return
     */
    public static ProductControlConfigItemDTO convertToProductControlConfigItemDTO(ProductControlConfigItemPO po) {
        if (po == null) {
            return null;
        }
        ProductControlConfigItemDTO dto = new ProductControlConfigItemDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

}
