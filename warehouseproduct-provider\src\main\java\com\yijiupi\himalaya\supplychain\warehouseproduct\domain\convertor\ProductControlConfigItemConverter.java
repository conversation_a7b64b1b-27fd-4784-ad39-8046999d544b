package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigItemDTO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductControlConfigItemConverter {
    public static List<ProductControlConfigItemDTO> productControlConfigItemPOS2productControlConfigItemDTOS(
        List<ProductControlConfigItemPO> productControlConfigItemPOS) {
        if (CollectionUtils.isEmpty(productControlConfigItemPOS)) {
            return null;
        }
        List<ProductControlConfigItemDTO> controlConfigItemDTOS = new ArrayList<>();
        productControlConfigItemPOS.forEach(po -> {
            ProductControlConfigItemDTO dto = new ProductControlConfigItemDTO();
            BeanUtils.copyProperties(po, dto);

            controlConfigItemDTOS.add(dto);
        });
        return controlConfigItemDTOS;
    }
}
