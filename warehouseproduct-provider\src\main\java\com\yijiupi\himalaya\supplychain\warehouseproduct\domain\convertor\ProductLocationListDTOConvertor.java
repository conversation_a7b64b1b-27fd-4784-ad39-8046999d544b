package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationListDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationListSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/7/17
 */
public class ProductLocationListDTOConvertor {


    public static PageList<ProductLocationListDTO> convert(PageResult<ProductLocationListPO> poPageResult, ProductLocationListSO so) {
        if (Objects.isNull(poPageResult)) {
            // 封装分页列表对象
            PageList<ProductLocationListDTO> dtoPageList = new PageList<>();
            dtoPageList.setPager(new Pager(so.getPageNum(), so.getPageSize(), 0));
            List<ProductLocationListDTO> dtoList = new ArrayList<>();
            dtoPageList.setDataList(dtoList);
            return dtoPageList;
        }
        List<ProductLocationListPO> poList = poPageResult.toPageList().getDataList();
        if (CollectionUtils.isEmpty(poList)) {
            // 封装分页列表对象
            PageList<ProductLocationListDTO> dtoPageList = new PageList<>();
            dtoPageList.setPager(poPageResult.toPageList().getPager());
            List<ProductLocationListDTO> dtoList = new ArrayList<>();
            dtoPageList.setDataList(dtoList);
            return dtoPageList;
        }

        // 对象转换
        List<ProductLocationListDTO> dtoList = poList.stream().map(po -> {
            if (null == po) {
                return null;
            }
            ProductLocationListDTO dto = new ProductLocationListDTO();
            BeanUtils.copyProperties(po, dto);
            if (null != po.getSubcategory()) {
                dto.setSubcategoryName(LocationEnum.getEnumStr(Integer.valueOf(po.getSubcategory())));
            }
            // dto.setOwnerName(ownerNameMap.get(po.getProductSkuId()));
            if (null != po.getSaleModel()) {
                dto.setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(po.getSaleModel())));
            }
            return dto;
        }).collect(Collectors.toList());

        // 封装分页列表对象
        PageList<ProductLocationListDTO> dtoPageList = new PageList<>();
        dtoPageList.setPager(poPageResult.toPageList().getPager());
        dtoPageList.setDataList(dtoList);
        return dtoPageList;
    }

}
