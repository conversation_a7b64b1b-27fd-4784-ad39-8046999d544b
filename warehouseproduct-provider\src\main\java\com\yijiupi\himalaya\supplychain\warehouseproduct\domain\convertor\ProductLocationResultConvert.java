package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto.LocationProductInfoResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.controller.dto.ProductLocationInfoResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuServiceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerInfoDTO;

@Component
public class ProductLocationResultConvert {

    @Autowired
    private ProductSkuServiceBL bl;

    public List<ProductLocationInfoResult> convert(List<LoactionDTO> list) {
        List<ProductLocationInfoResult> modelList = new ArrayList<>();
        List<Long> skuIdList = new ArrayList<>();
        for (LoactionDTO dto : list) {
            if (dto.getProductSkuIdList() != null && !dto.getProductSkuIdList().isEmpty()) {
                skuIdList.addAll(dto.getProductSkuIdList());
            }
        }
        // 分页
        Map<Long, OwnerInfoDTO> skuNameParams = new HashMap<>();
        if (!skuIdList.isEmpty()) {
            List<OwnerInfoDTO> ownerInfoBySkuId = bl.getOwnerInfoBySkuId(skuIdList);
            skuNameParams =
                ownerInfoBySkuId.stream().collect(Collectors.toMap(OwnerInfoDTO::getProductSkuId, Function.identity()));
        } else {
            skuNameParams = new HashMap<Long, OwnerInfoDTO>();
        }
        for (LoactionDTO dto : list) {
            ProductLocationInfoResult model = new ProductLocationInfoResult();
            model.setArea(dto.getArea());
            model.setArea_Id(dto.getArea_Id());
            model.setSequence(dto.getSequence());
            model.setCityId(dto.getCityId());
            model.setId(dto.getId());
            model.setName(dto.getName());
            model.setPallets(dto.getPallets());
            model.setProductlocation(dto.getProductlocation());
            model.setRoadway(dto.getRoadway());
            model.setWarehouseId(dto.getWarehouseId());
            model.setSubcategory(dto.getSubcategory());
            model.setSubcategoryName(dto.getSubcategoryName());
            model.setIsChaosBatch(dto.getIsChaosBatch());
            model.setIsChaosPut(dto.getIsChaosPut());
            model.setProductSkuList(convertProductSku(dto.getProductSkuIdList(), skuNameParams));
            model.setLocationCapacity(dto.getLocationCapacity());
            model.setLocationGrade(dto.getLocationGrade());
            model.setExpress(dto.getExpress());
            model.setState(dto.getState());
            // 巷道
            model.setAisleId(dto.getAisleId());
            model.setAisleNo(dto.getAisleNo());
            // 业务类型
            model.setBusinessType(dto.getBusinessType());
            model.setPassageId(dto.getPassageId());
            model.setPassageName(dto.getPassageName());
            model.setStorageAttribute(dto.getStorageAttribute());
            modelList.add(model);
        }
        return modelList;
    }

    private List<LocationProductInfoResult> convertProductSku(List<Long> productSkuIdList,
        Map<Long, OwnerInfoDTO> skuNameParams) {
        List<LocationProductInfoResult> productSkuList = new ArrayList<>();
        if (productSkuIdList != null && !productSkuIdList.isEmpty()) {
            for (Long id : productSkuIdList) {
                LocationProductInfoResult productSku = new LocationProductInfoResult();
                productSku.setSkuId(id);
                productSku
                    .setProductSkuName(skuNameParams.get(id) == null ? null : skuNameParams.get(id).getProductName());
                productSku.setOwnerName(skuNameParams.get(id) == null ? null : skuNameParams.get(id).getOwnerName());
                productSkuList.add(productSku);
            }
        }
        return productSkuList;
    }
}
