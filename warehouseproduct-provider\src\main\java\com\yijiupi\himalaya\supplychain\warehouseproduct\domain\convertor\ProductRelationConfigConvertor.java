package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductRelationConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductRelationConfigSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 产品关联关系配置转换类
 *
 * <AUTHOR>
 * @date 2019-09-25 14:10
 */
public class ProductRelationConfigConvertor {

    /**
     * 获取ProductRelationConfigPO
     *
     * @return
     */
    public static ProductRelationConfigPO connvertToProductRelationConfigPO(ProductSkuDTO skuDTO,
        ProductSkuDTO refSkuDTO) {
        ProductRelationConfigPO configPO = new ProductRelationConfigPO();
        configPO.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_RELATION));
        configPO.setProductSpecificationId(skuDTO.getProductSpecificationId());
        configPO.setSource(skuDTO.getSource());
        // 主产品 - 易款产品不需要保存ownerId
        if (!Objects.equals(skuDTO.getSource(), (byte)ProductSourceType.易款连锁)) {
            configPO.setOwnerId(skuDTO.getCompany_Id());
        }
        configPO.setRefProductSpecificationId(refSkuDTO.getProductSpecificationId());
        configPO.setRefSource(refSkuDTO.getSource());
        // 副产品 - 易款产品不需要保存ownerId
        if (!Objects.equals(refSkuDTO.getSource(), (byte)ProductSourceType.易款连锁)) {
            configPO.setRefOwnerId(refSkuDTO.getCompany_Id());
        }
        // 设置 - 产品识别编码, 设置 null 时会自动根据：ProductSpecification_Id+Owner_Id+Source生成产品识别码
        configPO.setProductIdentityCode(null);
        // 设置 - 关联产品识别编码, 设置 null 时会自动根据：Ref_ProductSpecification_Id+Ref_Owner_Id+Ref_Source生成关联产品识别编码
        configPO.setRefProductIdentityCode(null);
        return configPO;
    }

    /**
     * 获取ProductRelationConfigSO
     *
     * @return
     */
    public static ProductRelationConfigSO connvertToProductRelationConfigSO(ProductSkuDTO skuDTO) {
        if (skuDTO == null) {
            return null;
        }
        ProductRelationConfigSO so = new ProductRelationConfigSO();
        so.setProductSpecificationId(skuDTO.getProductSpecificationId());
        so.setSource(skuDTO.getSource());
        // 易款产品不传ownerId
        if (!Objects.equals(skuDTO.getSource(), (byte)ProductSourceType.易款连锁)) {
            so.setOwnerId(skuDTO.getCompany_Id());
        }
        // 设置 - 产品识别编码, 设置 null 时会自动根据：ProductSpecification_Id + Owner_Id + Source 生成产品识别码
        so.setProductIdentityCode(null);
        return so;
    }

    /**
     * 获取ProductRelationConfigSO
     *
     * @return
     */
    public static ProductRelationConfigSO connvertToProductRelationConfigSO(ProductRelationConfigPO configPO) {
        if (configPO == null) {
            return null;
        }
        ProductRelationConfigSO so = new ProductRelationConfigSO();
        so.setProductSpecificationId(configPO.getProductSpecificationId());
        so.setSource(configPO.getSource());
        so.setOwnerId(configPO.getOwnerId());
        return so;
    }

    public static ProductRelationConfigPO toConfigPO(ProductRelationConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductRelationConfigPO po = new ProductRelationConfigPO();
        BeanUtils.copyProperties(dto, po);
        if (po.getId() == null) {
            // dto 转 po 时如果id为空这自动生成 id
            po.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_RELATION));
        }
        return po;
    }

    public static List<ProductRelationConfigPO> toConfigPO(List<ProductRelationConfigDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<ProductRelationConfigPO> poList = new ArrayList<>();
        for (int i = 0; i < dtoList.size(); i++) {
            ProductRelationConfigPO configPO = toConfigPO(dtoList.get(i));
            if (configPO == null) {
                continue;
            }
            poList.add(configPO);
        }
        return poList;
    }

    public static ProductRelationConfigDTO toConfigDTO(ProductRelationConfigPO po) {
        if (po == null) {
            return null;
        }
        ProductRelationConfigDTO dto = new ProductRelationConfigDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public static List<ProductRelationConfigDTO> toConfigDTO(List<ProductRelationConfigPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<ProductRelationConfigDTO> dtoList = new ArrayList<>();
        for (int i = 0; i < poList.size(); i++) {
            ProductRelationConfigDTO configDTO = toConfigDTO(poList.get(i));
            if (configDTO == null) {
                continue;
            }
            dtoList.add(configDTO);
        }
        return dtoList;
    }
}
