package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductRelationGroupConvertor {

    public static ProductRelationGroupDTO toGroupDTO(ProductRelationGroupPO po) {
        if (po == null) {
            return null;
        }
        ProductRelationGroupDTO dto = new ProductRelationGroupDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public static List<ProductRelationGroupDTO> toGroupDTO(List<ProductRelationGroupPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<ProductRelationGroupDTO> rs = new ArrayList<>(poList.size());
        for (ProductRelationGroupPO po : poList) {
            ProductRelationGroupDTO dto = toGroupDTO(po);
            if (dto != null) {
                rs.add(dto);
            }
        }
        return rs;
    }

    public static ProductRelationGroupPO toGroupPO(ProductRelationGroupDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.getId() == null) {
            dto.setId(UUIDUtils.randonUUID());
        }
        ProductRelationGroupPO po = new ProductRelationGroupPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    public static List<ProductRelationGroupPO> toGroupPO(List<ProductRelationGroupDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<ProductRelationGroupPO> rs = new ArrayList<>(dtoList.size());
        for (ProductRelationGroupDTO dto : dtoList) {
            ProductRelationGroupPO po = toGroupPO(dto);
            if (po != null) {
                rs.add(po);
            }
        }
        return rs;
    }

}
