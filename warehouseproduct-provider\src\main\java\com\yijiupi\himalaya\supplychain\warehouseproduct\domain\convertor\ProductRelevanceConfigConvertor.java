package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelevanceConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductRelevanceConfigDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class ProductRelevanceConfigConvertor extends Convertor<ProductRelevanceConfigPO, ProductRelevanceConfigDTO> {

    @Override
    public ProductRelevanceConfigDTO convert(ProductRelevanceConfigPO po) {
        if (po == null) {
            return null;
        }
        ProductRelevanceConfigDTO dto = new ProductRelevanceConfigDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public ProductRelevanceConfigPO reverseConvert(ProductRelevanceConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProductRelevanceConfigPO po = new ProductRelevanceConfigPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }
}
