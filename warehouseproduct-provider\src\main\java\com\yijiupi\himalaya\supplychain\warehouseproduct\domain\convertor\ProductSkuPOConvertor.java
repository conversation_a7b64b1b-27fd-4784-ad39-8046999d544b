/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuByInventoryRatioDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2018年10月31日 下午5:10:42
 */
@Component
public class ProductSkuPOConvertor extends Convertor<ProductSkuPO, ProductSkuDTO> {

    @Override
    public ProductSkuDTO convert(ProductSkuPO m) {
        ProductSkuDTO dto = new ProductSkuDTO();
        BeanUtils.copyProperties(m, dto);
        dto.setCityId(m.getCity_Id());
        dto.setProductSpecificationId(m.getProductSpecification_Id());
        dto.setProductSkuId(m.getProductSku_Id());
        // 格式化库存占比
        if (!StringUtils.isEmpty(m.getInventoryRatio())) {
            List<ProductSkuByInventoryRatioDTO> inventoryRatioList =
                JSON.parseObject(m.getInventoryRatio(), new TypeReference<List<ProductSkuByInventoryRatioDTO>>() {});
            dto.setInventoryRatioList(inventoryRatioList);
        }
        return dto;
    }

    @Override
    public ProductSkuPO reverseConvert(ProductSkuDTO m) {
        ProductSkuPO po = new ProductSkuPO();
        BeanUtils.copyProperties(m, po);
        po.setCity_Id(m.getCityId());
        po.setProductSpecification_Id(m.getProductSpecificationId());
        po.setProductSku_Id(m.getProductSkuId());
        // 库存占比转成JSON存储
        if (CollectionUtils.isNotEmpty(m.getInventoryRatioList())) {
            po.setInventoryRatio(JSON.toJSONString(m.getInventoryRatioList()));
        } else {
            po.setInventoryRatio("");
        }
        return po;
    }
}
