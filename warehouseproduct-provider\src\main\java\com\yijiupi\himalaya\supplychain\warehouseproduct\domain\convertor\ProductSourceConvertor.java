package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeBindDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordBusinessTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ProductSourceConvertor {

    public static List<ProductSourceCodePO>
        productSourceCodeDTOS2NewProductSourceCodePOS(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        if (CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            return null;
        }

        List<ProductSourceCodePO> productSourceCodePOS = new ArrayList<>();
        productSourceCodeDTOS.forEach(dto -> {
            ProductSourceCodePO po = new ProductSourceCodePO();
            if (dto.getId() == null) {
                dto.setId(UUIDGeneratorUtil.getUUID(UUIDGeneratorUtil.PRODUCT_SOURCE_CODE));
            }
            BeanUtils.copyProperties(dto, po);

            productSourceCodePOS.add(po);
        });

        return productSourceCodePOS;
    }

    public static List<ProductSourceCodeDTO>
        productSourceCodePOS2ProductSourceCodeDTOS(List<ProductSourceCodePO> productSourceCodePOS) {
        if (CollectionUtils.isEmpty(productSourceCodePOS)) {
            return new ArrayList<>();
        }

        List<ProductSourceCodeDTO> productSourceCodeDTOS = new ArrayList<>();
        productSourceCodePOS.forEach(po -> {
            ProductSourceCodeDTO dto = new ProductSourceCodeDTO();
            BeanUtils.copyProperties(po, dto);

            dto.setProductControlConfigItemDTOS(ProductControlConfigItemConverter
                .productControlConfigItemPOS2productControlConfigItemDTOS(po.getProductControlConfigItemPOS()));
            dto.setRecordList(
                productSourceCodeRecordPOS2ProductSourceCodeRecordDTOS(po.getProductSourceCodeRecordPOS()));

            productSourceCodeDTOS.add(dto);
        });

        return productSourceCodeDTOS;
    }

    public static List<ProductSourceCodeRecordDTO> productSourceCodeRecordPOS2ProductSourceCodeRecordDTOS(
        List<ProductSourceCodeRecordPO> productSourceCodeRecordPOS) {
        if (CollectionUtils.isEmpty(productSourceCodeRecordPOS)) {
            return new ArrayList<>();
        }
        List<ProductSourceCodeRecordDTO> productSourceCodeRecordDTOS = new ArrayList<>();
        productSourceCodeRecordPOS.forEach(po -> {
            ProductSourceCodeRecordDTO dto = new ProductSourceCodeRecordDTO();
            BeanUtils.copyProperties(po, dto);

            productSourceCodeRecordDTOS.add(dto);
        });
        return productSourceCodeRecordDTOS;
    }

    public static List<ProductSourceCodeRecordDTO>
        productSourceCodeDTOS2ProductSourceCodeRecordDTOS(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        if (CollectionUtils.isEmpty(productSourceCodeDTOS)) {
            return new ArrayList<>();
        }
        List<ProductSourceCodeRecordDTO> recordList = new ArrayList<>();
        productSourceCodeDTOS.forEach(sourceCode -> {
            ProductSourceCodeRecordDTO record = new ProductSourceCodeRecordDTO();
            record.setProductSourceCodeId(sourceCode.getId());
            record.setWarehouseId(sourceCode.getWarehouseId());
            record.setCode(sourceCode.getCode());
            record.setBusinessId(sourceCode.getBusinessId() == null ? null : sourceCode.getBusinessId().toString());
            record.setBusinessNo(sourceCode.getBusinessNo());
            if (Objects.equals(sourceCode.getProductSourceCodeRecordBusinessType(),
                ProductSourceCodeRecordBusinessTypeEnum.采集订单.getType())) {
                record.setBusinessId(sourceCode.getSourceBusinessId());
                record.setBusinessNo(sourceCode.getSourceBusinessNo());
            }
            record.setBusinessType(sourceCode.getProductSourceCodeRecordBusinessType());
            record.setAfterState(sourceCode.getProductSourceCodeRecordState());
            record.setCreateUser(StringUtils.isNotEmpty(sourceCode.getCreateUser()) ? sourceCode.getCreateUser()
                : sourceCode.getLastUpdateUser());
            record.setConfigId(sourceCode.getConfigId());

            recordList.add(record);
        });
        return recordList;
    }

    public static List<ProductSourceCodeCheckDTO>
        ProductSourceCodeBindDTOS2ProductSourceCodeCheckDTOS(List<ProductSourceCodeBindDTO> productSourceCodeBindDTOS) {
        List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(productSourceCodeBindDTOS)) {
            return productSourceCodeCheckDTOS;
        }
        productSourceCodeBindDTOS.forEach(bindDTO -> {
            ProductSourceCodeCheckDTO checkDTO = new ProductSourceCodeCheckDTO();
            BeanUtils.copyProperties(bindDTO, checkDTO);

            productSourceCodeCheckDTOS.add(checkDTO);
        });
        return productSourceCodeCheckDTOS;
    }

    public static List<ProductSourceCodeRecordDTO>
        productSourceCodeRecordSyncDTO2ProductSourceCodeRecordDTOS(ProductSourceCodeRecordSyncDTO recordSyncDTO) {
        List<ProductSourceCodeRecordDTO> productSourceCodeRecordDTOS = new ArrayList<>();

        if (recordSyncDTO == null || CollectionUtils.isEmpty(recordSyncDTO.getProductSourceCodeIds())) {
            return productSourceCodeRecordDTOS;
        }

        List<ProductSourceCodeRecordDTO> recordDTOS =
            recordSyncDTO.getProductSourceCodeIds().stream().filter(Objects::nonNull).map(codeId -> {
                ProductSourceCodeRecordDTO record = new ProductSourceCodeRecordDTO();
                record.setProductSourceCodeId(codeId);
                record.setAfterState(recordSyncDTO.getAfterState());
                record.setBusinessType(recordSyncDTO.getBusinessType());
                record.setBusinessId(recordSyncDTO.getBusinessId());
                record.setBusinessNo(recordSyncDTO.getBusinessNo());
                record.setDescribe(recordSyncDTO.getDescribe());
                record.setRemark(recordSyncDTO.getRemark());
                record.setCreateTime(recordSyncDTO.getCreateTime());
                record.setCreateUser(recordSyncDTO.getCreateUserName());
                record.setLastUpdateUser(recordSyncDTO.getLastUpdateUserName());
                return record;
            }).collect(Collectors.toList());

        return recordDTOS;
    }
}
