package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ProductSupplierConvertor {

    public static ProductSupplierDTO toProductSupplierDTO(ProductSupplierPO po) {
        if (po == null) {
            return null;
        }
        ProductSupplierDTO dto = new ProductSupplierDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public static List<ProductSupplierDTO> toProductSupplierDTO(List<ProductSupplierPO> poList) {
        if (poList == null || poList.isEmpty()) {
            return new ArrayList<>();
        }
        return poList.stream().filter(e -> e != null).map(e -> toProductSupplierDTO(e)).filter(e -> e != null)
            .collect(Collectors.toList());
    }

    public static ProductSupplierPO toProductSupplierPO(ProductSupplierDTO dto) {
        if (dto == null) {
            return null;
        }
        if (dto.getId() == null) {
            dto.setId(UUIDUtils.randonUUID());
        }
        if (dto.getIsDefault() == null) {
            dto.setIsDefault(ConditionStateEnum.否.getType());
        }
        ProductSupplierPO po = new ProductSupplierPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    public static List<ProductSupplierPO> toProductSupplierPO(List<ProductSupplierDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return new ArrayList<>();
        }
        return dtoList.stream().filter(e -> e != null).map(e -> toProductSupplierPO(e)).filter(e -> e != null)
            .collect(Collectors.toList());
    }
}
