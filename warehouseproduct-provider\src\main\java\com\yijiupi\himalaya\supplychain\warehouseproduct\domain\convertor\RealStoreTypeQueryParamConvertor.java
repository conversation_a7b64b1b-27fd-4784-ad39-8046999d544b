package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Objects;

/**
 * <AUTHOR>
 * @title: RealStoreTypeQuertParamConvertor
 * @description:
 * @date 2022-09-15 17:38
 */
public class RealStoreTypeQueryParamConvertor {

    public static final Byte BIG = 1;

    public static final Byte EQUAL = 0;

    public static final Byte SMALL = -1;

    public static Byte convert(Boolean hasRealStore) {
        if (Objects.isNull(hasRealStore)) {
            return null;
        }

        if (BooleanUtils.isTrue(hasRealStore)) {
            return BIG;
        }
        if (BooleanUtils.isFalse(hasRealStore)) {
            return EQUAL;
        }

        return null;
    }

}
