package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.RecommendOutLocationQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public class RecommendOutLocationQueryDTOConvertor {

    public static List<RecommendOutLocationQueryBO> convert(List<RecommendOutLocationQueryDTO> dtoList) {
        return dtoList.stream().map(m -> {
            RecommendOutLocationQueryBO bo = new RecommendOutLocationQueryBO();
            bo.setQueryDTO(m);
            bo.setWarehouseId(m.getWarehouseId());
            return bo;
        }).collect(Collectors.toList());
    }

}
