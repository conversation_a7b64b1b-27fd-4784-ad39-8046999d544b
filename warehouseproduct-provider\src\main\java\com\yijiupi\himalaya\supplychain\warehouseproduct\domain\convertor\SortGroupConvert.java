package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.DateUtil;
import org.springframework.stereotype.Component;

/**
 * 分区转化类
 *
 * <AUTHOR>
 * @date 2018/5/16 18:16
 */
@Component
public class SortGroupConvert {

    /**
     * 分区Param转化为DTO
     *
     * @param param
     * @return
     */
    public SortGroupDTO convertToGroupDTO(SortGroupParam param) {
        if (null == param) {
            return null;
        }
        SortGroupDTO dto = new SortGroupDTO();
        dto.setId(param.getId());
        dto.setName(param.getName());
        dto.setSortGroupType(param.getSortGroupType());
        dto.setWarehouseId(param.getWarehouseId());
        dto.setRemark(param.getRemark());
        dto.setCreateUser(param.getExecuteUser());
        dto.setLastUpdateUser(param.getExecuteUser());
        // 分区类别
        List<SortGroupSettingParam> settingParamList = param.getGroupSettingList();
        if (null != settingParamList) {
            List<SortGroupSettingDTO> settingDTOList =
                settingParamList.stream().map(this::convertToGroupSettingDTO).collect(Collectors.toList());
            dto.setGroupSettingList(settingDTOList);
        }

        // 分区人员
        List<SortGroupUserParam> userParamList = param.getGroupUserList();
        if (null != userParamList) {
            List<SortGroupUserDTO> userDTOList =
                userParamList.stream().map(this::convertToGroupUserDTO).collect(Collectors.toList());
            dto.setGroupUserList(userDTOList);
        }
        dto.setFlag(param.getFlag());
        dto.setSortGroupPickWay(param.getSortGroupPickWay());
        dto.setCallNum(param.getCallNum());
        return dto;
    }

    /**
     * 分区类别Param转化为DTO
     *
     * @param param
     * @return
     */
    public SortGroupSettingDTO convertToGroupSettingDTO(SortGroupSettingParam param) {
        if (null == param) {
            return null;
        }
        SortGroupSettingDTO dto = new SortGroupSettingDTO();
        dto.setSortType(param.getSortType());
        dto.setSortId(param.getSortId());
        dto.setSortName(param.getSortName());
        return dto;
    }

    /**
     * 分区人员Param转化为DTO
     *
     * @param param
     * @return
     */
    public SortGroupUserDTO convertToGroupUserDTO(SortGroupUserParam param) {
        if (null == param) {
            return null;
        }
        SortGroupUserDTO dto = new SortGroupUserDTO();
        dto.setId(param.getId());
        dto.setSortGroupId(param.getSortGroupId());
        dto.setUserId(param.getUserId());
        dto.setUserName(param.getUserName());
        dto.setWorkStartDate(DateUtil.parseDate(param.getWorkStartDate()));
        dto.setWorkEndDate(DateUtil.parseDate(param.getWorkEndDate()));
        dto.setWorkDayType(param.getWorkDayType());
        if (null != param.getWorkDayDetailList()) {
            StringBuilder sb = new StringBuilder("");
            for (String s : param.getWorkDayDetailList()) {
                sb.append(s + ",");
            }
            dto.setWorkDayDetail(sb.substring(0, sb.length() - 1));
        }
        dto.setWorkStartTime(DateUtil.parseTime(param.getWorkStartTime()));
        dto.setWorkEndTime(DateUtil.parseTime(param.getWorkEndTime()));
        dto.setState(param.getState());
        dto.setCreateUser(param.getExecuteUser());
        dto.setLastUpdateUser(param.getExecuteUser());
        return dto;
    }
}
