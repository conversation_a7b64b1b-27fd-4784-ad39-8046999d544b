package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupListPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupSettingPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupUserPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupListDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSettingDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupUserDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * 分区转换类
 *
 * <AUTHOR>
 * @date 2018/5/15 19:43
 */
public class SortGroupConvertor {

    /**
     * 分区列表PO转换为DTO
     *
     * @param po
     * @return
     */
    public static SortGroupListDTO convertToSortGroupListDTO(SortGroupListPO po) {
        if (null == po) {
            return null;
        }
        SortGroupListDTO dto = new SortGroupListDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 分区DTO转换为PO
     *
     * @param dto
     * @return
     */
    public static SortGroupPO convertToSortGroupPO(SortGroupDTO dto) {
        if (null == dto) {
            return null;
        }
        SortGroupPO po = new SortGroupPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * 分区PO转换为DTO
     *
     * @param po
     * @return
     */
    public static SortGroupDTO convertToSortGroupDTO(SortGroupPO po) {
        if (null == po) {
            return null;
        }
        SortGroupDTO dto = new SortGroupDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 分区人员DTO转换为PO
     *
     * @param dto
     * @return
     */
    public static SortGroupUserPO convertToSortGroupUserPO(SortGroupUserDTO dto) {
        if (null == dto) {
            return null;
        }
        SortGroupUserPO po = new SortGroupUserPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * 分区人员PO转换为DTO
     *
     * @param po
     * @return
     */
    public static SortGroupUserDTO convertToSortGroupUserDTO(SortGroupUserPO po) {
        if (null == po) {
            return null;
        }
        SortGroupUserDTO dto = new SortGroupUserDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 分区类型DTO转换为PO
     *
     * @param dto
     * @return
     */
    public static SortGroupSettingPO convertToSortGroupSettingPO(SortGroupSettingDTO dto) {
        if (null == dto) {
            return null;
        }
        SortGroupSettingPO po = new SortGroupSettingPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * 分区类型PO转换为DTO
     *
     * @param po
     * @return
     */
    public static SortGroupSettingDTO convertToSortGroupSettingDTO(SortGroupSettingPO po) {
        if (null == po) {
            return null;
        }
        SortGroupSettingDTO dto = new SortGroupSettingDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

}
