package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.dto.ControlInfoResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupRfidPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSettingDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.EnableStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDUtils;

/**
 * 控制器电子标签
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
public class SortGroupRfidConvertor {

    /**
     * SortGroupRfidPO转换成SortGroupRfidDTO
     * 
     * @return
     */
    public static SortGroupRfidDTO convertorToSortGroupRfidDTO(SortGroupRfidPO po) {
        if (null == po) {
            return null;
        }
        SortGroupRfidDTO dto = new SortGroupRfidDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * List<SortGroupRfidPO>转换成List<SortGroupRfidDTO>
     * 
     * @return
     */
    public static List<SortGroupRfidDTO> convertorToSortGroupRfidDTOList(List<SortGroupRfidPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<SortGroupRfidDTO> dtoList =
            poList.stream().map(SortGroupRfidConvertor::convertorToSortGroupRfidDTO).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * SortGroupRfidDTO转换成SortGroupRfidPO
     * 
     * @return
     */
    public static SortGroupRfidPO convertorToSortGroupRfidPO(SortGroupRfidDTO dto) {
        if (null == dto) {
            return null;
        }
        SortGroupRfidPO po = new SortGroupRfidPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * List<SortGroupRfidDTO>转换成List<SortGroupRfidPO>
     * 
     * @return
     */
    public static List<SortGroupRfidPO> convertorToSortGroupRfidPOList(List<SortGroupRfidDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<SortGroupRfidPO> poList =
            dtoList.stream().map(SortGroupRfidConvertor::convertorToSortGroupRfidPO).collect(Collectors.toList());
        return poList;
    }

    /**
     * List<SortGroupRfidDTO>转换成List<SortGroupRfidPO>
     *
     * @return
     */
    public static List<SortGroupRfidPO> convertorToAddPOList(List<SortGroupRfidDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }

        List<SortGroupRfidPO> poList = new ArrayList<>();
        dtoList.forEach(dto -> {
            if (null == dto) {
                return;
            }

            SortGroupRfidPO po = new SortGroupRfidPO();
            BeanUtils.copyProperties(dto, po);
            po.setId(UUIDUtils.randonUUID());
            po.setEnable((byte)EnableStateEnum.ENABLE.getType());
            po.setCreateTime(new Date());
            poList.add(po);
        });

        return poList;
    }

    /**
     * 组装待生成标签数据List<SortGroupRfidDTO>
     *
     * @return
     */
    public static List<SortGroupRfidDTO> convertorToSortGroupRfidDTOList(Map<String, List<SortGroupRfidDTO>> rfidMap,
        Map<String, ControlInfoResultDTO> controlMap, Map<String, SortGroupSettingDTO> settingMap) {
        if (rfidMap == null || rfidMap.size() <= 0) {
            return Collections.EMPTY_LIST;
        }

        if (controlMap == null || controlMap.size() <= 0 || settingMap == null || settingMap.size() <= 0) {
            return Collections.EMPTY_LIST;
        }

        List<SortGroupRfidDTO> addDTOList = new ArrayList<>();
        rfidMap.forEach((rfidKey, rfidDTOS) -> {
            ControlInfoResultDTO controlInfo = controlMap.get(rfidKey);
            if (controlInfo == null) {
                throw new BusinessValidateException("所选控制器数据不存在,控制器：" + rfidKey);
            }

            rfidDTOS.stream().forEach(dto -> {
                SortGroupSettingDTO settingDTO = settingMap.get(dto.getSortName());
                if (settingDTO == null) {
                    throw new BusinessValidateException("所选分区货位数据不存在,货位名称：" + dto.getSortName());
                }

                SortGroupRfidDTO addDTO = new SortGroupRfidDTO();
                addDTO.setWarehouseId(dto.getWarehouseId());
                addDTO.setRfidType(dto.getRfidType());
                addDTO.setControlId(controlInfo.getControlId());
                addDTO.setControlName(controlInfo.getControlName());
                addDTO.setBusId(controlInfo.getBusId());
                addDTO.setBusTagNo(controlInfo.getBusTagNo());
                addDTO.setBusTypeName(controlInfo.getBusTypeName());
                addDTO.setDeviceId(controlInfo.getDeviceId());
                addDTO.setDeviceTagNo(controlInfo.getDeviceTagNo());
                addDTO.setDeviceTypeName(controlInfo.getDeviceTypeName());
                addDTO.setSortId(Long.valueOf(settingDTO.getSortId()));
                addDTO.setSortName(settingDTO.getSortName());
                addDTO.setCreateUser(dto.getCreateUser());
                addDTO.setAisleNo(dto.getAisleNo());
                addDTO.setAreaId(settingDTO.getAreaId());
                addDTOList.add(addDTO);
            });
        });

        return addDTOList;
    }

}
