package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class StockAgeStrategyConvertor {

    public static StockAgeStrategyPO stockAgeStrategyDTO2StockAgeStrategyPO(StockAgeStrategyDTO stockAgeStrategyDTO) {
        if (stockAgeStrategyDTO == null) {
            return null;
        }
        StockAgeStrategyPO stockAgeStrategyPO = new StockAgeStrategyPO();
        BeanUtils.copyProperties(stockAgeStrategyDTO, stockAgeStrategyPO);
        return stockAgeStrategyPO;
    }

    public static List<StockAgeStrategyConfigPO> stockAgeStrategyConfigDTOS2StockAgeStrategyConfigPOS(
        List<StockAgeStrategyConfigDTO> stockAgeStrategyConfigDTOS) {
        List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(stockAgeStrategyConfigDTOS)) {
            return stockAgeStrategyConfigPOS;
        }
        stockAgeStrategyConfigDTOS.forEach(dto -> {
            StockAgeStrategyConfigPO po = new StockAgeStrategyConfigPO();
            BeanUtils.copyProperties(dto, po);

            stockAgeStrategyConfigPOS.add(po);
        });
        return stockAgeStrategyConfigPOS;
    }

    public static List<StockAgeStrategyConfigDTO>
        stockAgeStrategyConfigPOS2StockAgeStrategyConfigDTOS(List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPOS) {
        if (CollectionUtils.isEmpty(stockAgeStrategyConfigPOS)) {
            return null;
        }
        List<StockAgeStrategyConfigDTO> stockAgeStrategyConfigDTOS = new ArrayList<>();
        stockAgeStrategyConfigPOS.forEach(po -> {
            StockAgeStrategyConfigDTO dto = new StockAgeStrategyConfigDTO();
            BeanUtils.copyProperties(po, dto);

            stockAgeStrategyConfigDTOS.add(dto);
        });
        return stockAgeStrategyConfigDTOS;
    }
}
