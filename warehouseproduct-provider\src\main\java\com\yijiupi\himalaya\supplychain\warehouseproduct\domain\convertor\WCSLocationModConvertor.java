package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.wcs.dto.location.WCSLocationModDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: WCSLocationModConvertor
 * @description:
 * @date 2022-11-01 09:43
 */
public class WCSLocationModConvertor {

    public static WCSLocationModDTO convert(PassageDTO passageDTO, List<String> locationIds) {
        WCSLocationModDTO modDTO = new WCSLocationModDTO();
        modDTO.setPassageId(passageDTO.getId());
        modDTO.setWarehouseId(passageDTO.getWarehouseId());
        modDTO.setLocationIds(locationIds);
        return modDTO;
    }

}
