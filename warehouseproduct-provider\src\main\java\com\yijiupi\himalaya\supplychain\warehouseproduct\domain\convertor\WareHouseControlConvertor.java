package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehouseWineConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseWineConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseWineConfigItemDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2019/2/27
 */
public class WareHouseControlConvertor {

    public static List<WarehouseWineConfigDTO> wareHousePOList2DTO(List<WarehouseWineConfigPO> poList) {
        ArrayList<WarehouseWineConfigDTO> dtoList = new ArrayList<>();
        poList.forEach(n -> {
            dtoList.add(wareHousePO2DTO(n));
        });
        return dtoList;
    }

    public static WarehouseWineConfigDTO wareHousePO2DTO(WarehouseWineConfigPO warehouseWineConfigPO) {
        WarehouseWineConfigDTO dto = new WarehouseWineConfigDTO();
        dto.setCompany(warehouseWineConfigPO.getCompany());
        dto.setCompulsoryCodeCopying(warehouseWineConfigPO.getCompulsoryCodeCopying());
        dto.setConfigName(warehouseWineConfigPO.getConfigName());
        dto.setCreateTime(warehouseWineConfigPO.getCreateTime());
        dto.setCreateUser(warehouseWineConfigPO.getCreateUser());
        dto.setDefaultPolicy(warehouseWineConfigPO.getDefaultPolicy());
        dto.setId(warehouseWineConfigPO.getId());
        dto.setStatus(warehouseWineConfigPO.getStatus());
        dto.setLastUpdateUser(warehouseWineConfigPO.getLastUpdateUser());
        dto.setWarehouseIds(warehouseWineConfigPO.getWarehouseIds());
        return dto;
    }

    public static WareHouseConfigDTO wareHouseItemPO2DTO(WarehouseWineConfigPO warehouseWineConfigPO,
        List<WarehouseWineConfigItemDTO> list) {
        WareHouseConfigDTO dto = new WareHouseConfigDTO();
        dto.setCompany(warehouseWineConfigPO.getCompany());
        dto.setCompulsoryCodeCopying(warehouseWineConfigPO.getCompulsoryCodeCopying());
        dto.setConfigName(warehouseWineConfigPO.getConfigName());
        dto.setDefaultPolicy(warehouseWineConfigPO.getDefaultPolicy());
        dto.setWarehouseIds(warehouseWineConfigPO.getWarehouseIds());
        dto.setId(warehouseWineConfigPO.getId());
        dto.setWarehouseWineConfigItemList(list);
        return dto;
    }

    public static WarehouseWineConfigPO wareHousetoPO(WareHouseConfigDTO wareHouseConfigPO) {
        WarehouseWineConfigPO record = new WarehouseWineConfigPO();
        record.setConfigName(wareHouseConfigPO.getConfigName());
        record.setCompany(wareHouseConfigPO.getCompany());
        List<Integer> warehouseIdList = wareHouseConfigPO.getWarehouseIdList();
        StringBuilder warehouseIds = new StringBuilder();
        warehouseIdList.forEach(i -> {
            warehouseIds.append(i + ",");
        });
        record.setWarehouseIds(warehouseIds.toString());
        record.setCompulsoryCodeCopying(wareHouseConfigPO.getCompulsoryCodeCopying());
        record.setDefaultPolicy(wareHouseConfigPO.getDefaultPolicy());
        record.setCreateUser(record.getCreateUser());
        record.setCreateTime(new Date());
        Byte status = 0;// 启用
        record.setStatus(status);
        return record;
    }

    public static WarehouseWineConfigDTO wareHousetoDTO(WareHouseConfigDTO wareHouseConfigPO) {
        WarehouseWineConfigDTO record = new WarehouseWineConfigDTO();
        record.setId(wareHouseConfigPO.getId());
        record.setConfigName(wareHouseConfigPO.getConfigName());
        record.setCompany(wareHouseConfigPO.getCompany());
        List<Integer> warehouseIdList = wareHouseConfigPO.getWarehouseIdList();
        if (null != warehouseIdList && warehouseIdList.size() > 0) {
            StringBuilder warehouseIds = new StringBuilder();
            warehouseIdList.forEach(i -> {
                warehouseIds.append(i + ",");
            });
            record.setWarehouseIds(warehouseIds.toString());
        }
        record.setCompulsoryCodeCopying(wareHouseConfigPO.getCompulsoryCodeCopying());
        record.setDefaultPolicy(wareHouseConfigPO.getDefaultPolicy());
        record.setLastUpdateTime(new Date());
        record.setLastUpdateUser(record.getLastUpdateUser());
        record.setStatus(wareHouseConfigPO.getStatus());
        return record;
    }

}
