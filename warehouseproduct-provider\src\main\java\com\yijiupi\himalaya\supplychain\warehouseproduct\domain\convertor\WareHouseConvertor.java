package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import org.springframework.beans.BeanUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.WarehouseDTO;

public class WareHouseConvertor {

    public static WareHouseDTO warehouse2WarehouseDTO(Warehouse warehouse) {
        if (warehouse == null) {
            return null;
        }
        WareHouseDTO wareHouseDTO = new WareHouseDTO();
        BeanUtils.copyProperties(warehouse, wareHouseDTO);
        wareHouseDTO.setState(warehouse.getEnableState());
        wareHouseDTO
            .setWarehouseType(warehouse.getWarehouseType() == null ? null : warehouse.getWarehouseType().byteValue());
        wareHouseDTO.setWarehouseClass(
            warehouse.getWarehouseClass() == null ? null : warehouse.getWarehouseClass().byteValue());
        return wareHouseDTO;
    }

    public static WarehouseDTO warehouse2DTO(Warehouse warehouse) {
        if (warehouse == null) {
            return null;
        }
        WarehouseDTO wareHouseDTO = new WarehouseDTO();
        BeanUtils.copyProperties(warehouse, wareHouseDTO);
        wareHouseDTO.setState(warehouse.getEnableState());
        wareHouseDTO
            .setWarehouseType(warehouse.getWarehouseType() == null ? null : warehouse.getWarehouseType().byteValue());
        wareHouseDTO.setWarehouseClass(
            warehouse.getWarehouseClass() == null ? null : warehouse.getWarehouseClass().byteValue());
        return wareHouseDTO;
    }
}
