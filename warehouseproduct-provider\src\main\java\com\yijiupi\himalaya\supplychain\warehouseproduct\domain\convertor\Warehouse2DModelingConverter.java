package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.GridPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DModelDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-21 10:25
 **/
@Service
public class Warehouse2DModelingConverter {

    public LocationConfigPO toPO(Warehouse2DModelDTO dto) {
        LocationConfigPO po = new LocationConfigPO();
        po.setWarehouseId(dto.getRefId().intValue());
        po.setConfigType(dto.getWarehouseConfigType() == null ? 0 : dto.getWarehouseConfigType());
        po.setLocationConfig(JSON.toJSONString(dto));
        po.setCreateTime(new Date());
        po.setCreateUser(dto.getUserId());
        po.setLastUpdateTime(new Date());
        po.setLastUpdateUser(dto.getUserId());
        return po;
    }

    public GridPO toGridPO(Warehouse2DModelDTO dto, int floor, Integer userId, boolean walkable) {
        GridPO gridPO = new GridPO();
        gridPO.setId(UUIDGenerator.getUUID(GridPO.class.getName()));
        gridPO.setWarehouseId(dto.getRefId().intValue());
        List<BigDecimal> coordinates = dto.getCoordinates();
        gridPO.setX(coordinates.get(0).intValue());
        gridPO.setY(coordinates.get(1).intValue());
        gridPO.setFloor(floor);
        gridPO.setIsWalkable(walkable);
        gridPO.setCreateTime(new Date());
        gridPO.setCreateUser(userId);
        gridPO.setLastUpdateTime(new Date());
        gridPO.setLastUpdateUser(userId);
        return gridPO;
    }


    public Warehouse2DModelDTO toDTO(LocationConfigPO po) {
        if (po == null) {
            return null;
        }
        return JSON.parseObject(po.getLocationConfig(), Warehouse2DModelDTO.class);
    }



}
