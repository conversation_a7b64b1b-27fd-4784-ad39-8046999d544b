package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousePropertyRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehousePropertyRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.DateUtil;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UUIDGeneratorUtil;
import org.springframework.beans.BeanUtils;

public class WarehousePropertyRecordConvert {
    public static WarehousePropertyRecordPO convert(WarehousePropertyRecordDTO warehousePropertyDTO) {
        WarehousePropertyRecordPO warehousePropertyRecordPO = new WarehousePropertyRecordPO();
        BeanUtils.copyProperties(warehousePropertyDTO, warehousePropertyRecordPO);
        warehousePropertyRecordPO.setId(UUIDGeneratorUtil.getUUID("warehousePropertyRecord"));
        warehousePropertyRecordPO.setApplicantDate(DateUtil.parseDate(warehousePropertyDTO.getApplicantDate()));
        warehousePropertyRecordPO.setWarehouseId(Integer.valueOf(warehousePropertyDTO.getWarehouse()));
        warehousePropertyRecordPO.setCreateUser(warehousePropertyDTO.getOperator());
        return warehousePropertyRecordPO;

    }
}
