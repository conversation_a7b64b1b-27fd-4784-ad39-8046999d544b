package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyConnectLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.GetUnusedLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO;

/**
 * <AUTHOR> 2017/11/24
 */
public interface AgencyConnectLocationMapper {
    /**
     * 关联经销商和货位,插入经销商货位表.
     */
    void insertAgencyLocation(AgencyConnectLocationDTO agencyConnectLocationDTO);

    /**
     * 根据经销商id和仓库id查询货位信息
     */
    List<LocationInfoDTO> getLocation(@Param("agencyId") Long agencyId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据经销商Id，仓库Id和SKUID获取对应的货位信息
     */
    List<LocationInfoDTO> getLocationBySku(@Param("agencyId") Long agencyId, @Param("warehouseId") Integer warehouseId,
        @Param("productSkuId") Long productSkuId);

    /**
     * 查询未被关联的货位,供经销商选择.
     */
    PageResult<LocationInfoDTO> getUnusedLocation(
        @Param("getUnusedLocationDTO") GetUnusedLocationDTO getUnusedLocationDTO, @Param("pageNum") int pageNum,
        @Param("pageSize") int pageSize);

    /**
     * 删除经销商与货位关系
     */
    void removeAgencyLocation(AgencyConnectLocationDTO agencyConnectLocationDTO);

    /**
     * 查询出locationId
     */
    List<Long> getAgencyLocationId(AgencyConnectLocationDTO dto);

    /**
     * 根据经销商查询该经销商所有的货位详细信息
     */
    List<LocationInfoDTO> getLocationInfoByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 删除该经销商货位关联信息.
     */
    void removeAgencyLocationByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 查询出已经有货位关联的经销商的id
     */
    List<Long> getAgencyIdByLocation();
}
