package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AgencyInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 经销商信息查询
 *
 * <AUTHOR> 2017/11/26
 */
public interface AgencyInfoMapper {
    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    PageResult<AgencyInfoPO> getAgencyInfoPO(@Param("agencyInfoQueryDTO") AgencyInfoQueryDTO agencyInfoQueryDTO,
        @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);
}
