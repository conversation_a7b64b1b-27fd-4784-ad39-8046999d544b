package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleQueryDTO;

public interface AislePOMapper {

    /**
     * 根据id查询巷道
     *
     */
    AislePO selectByPrimaryKey(Long id);

    /**
     * 分页查询巷道
     */
    PageResult<AislePO> pageListAisle(AisleQueryDTO queryDTO);

    /**
     * 新增巷道
     */
    int insertSelective(AislePO aislePO);

    /**
     * 查询巷道不分页
     */
    List<AislePO> listAisleNoPage(AisleQueryDTO queryDTO);

    /**
     * 编辑巷道
     */
    void updateAisle(AislePO aislePO);

    /**
     * 删除巷道
     *
     * @param id
     */
    void deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 通过仓库id、巷道和货区查询
     */
    AislePO getAisleByAisleNoAndAreaId(AislePO po);

}
