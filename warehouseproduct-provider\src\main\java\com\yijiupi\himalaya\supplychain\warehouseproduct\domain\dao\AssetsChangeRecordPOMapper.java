/*
 * @ClassName AssetsChangeRecordPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-04-16 13:41:54
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsChangeRecordListQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssetsChangeRecordPOMapper {
    /**
     * @Title deleteByIds
     * @param id
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @Title insertSelective
     * @param record
     * @return int
     */
    int insertSelective(AssetsChangeRecordPO record);

    /**
     * @Title selectByPrimaryKey
     * @param id
     * @return AssetsChangeRecordPO
     */
    AssetsChangeRecordPO selectByPrimaryKey(Long id);

    /**
     * @Title updateByPrimaryKeySelective
     * @param record
     * @return int
     */
    int updateByPrimaryKeySelective(AssetsChangeRecordPO record);

    int insertList(@Param("list") List<AssetsChangeRecordPO> recordPOList);

    List<AssetsChangeRecordPO>
        findChangeRecordByParentIdAndCondition(AssetsChangeRecordListQueryDTO changeRecordListQueryDTO);
}