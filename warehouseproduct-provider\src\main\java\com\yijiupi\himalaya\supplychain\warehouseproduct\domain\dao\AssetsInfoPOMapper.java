/*
 * @ClassName AssetsInfoPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-04-16 12:02:28
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsListQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssetsInfoPOMapper {
    /**
     * @param id
     * @return int
     * @Title deleteByIds
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @param record
     * @return int
     * @Title insertSelective
     */
    int insertSelective(AssetsInfoPO record);

    /**
     * @param id
     * @return AssetsInfoPO
     * @Title selectByPrimaryKey
     */
    AssetsInfoPO selectByPrimaryKey(Long id);

    AssetsInfoPO selectByCode(String code);

    /**
     * @param record
     * @return int
     * @Title updateByPrimaryKeySelective
     */
    int updateByPrimaryKeySelective(AssetsInfoPO record);

    int updateAssetsInfoByEdit(AssetsInfoPO record);

    int insertList(@Param("list") List<AssetsInfoPO> assetsInfoPOList);

    /**
     * 根据资产编码查询
     *
     * @param codes
     * @return
     */
    List<AssetsInfoPO> selectByCodeAndState(@Param("list") List<String> codes,
        @Param("stateList") List<Integer> stateList);

    int batchUpdateAssetsInfo(@Param("list") List<AssetsInfoPO> poList);

    PageResult<AssetsInfoPO> findAssetsInfoByPage(AssetsListQueryDTO queryDTO);
}