package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.CityWarehousePO;
import org.apache.ibatis.annotations.Param;

/**
 * @Title: CityWarehouseMapper.java
 * @Package
 * <AUTHOR>
 * @date 2018/3/8 17:10
 */
public interface CityWarehouseMapper {

    int insertCityWarehousePOBatch(@Param("poList") List<CityWarehousePO> poList);

    int deleteCityWarehouseByWareHouseId(@Param("warehouseIds") List<Integer> warehouseIds);

}