package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.DistributionPercentPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2017/12/1
 */
public interface DistributionMapper {

    /**
     * 给skuId绑定配送系数
     */
    void addDistributionPercent(DistributionPercentSearchDTO distributionPercentSearchDTO);

    /**
     * 根据skuId获取配送系数
     */
    DistributionPercentDTO getDistributionPercentBySku(Long productSkuId);

    /**
     * 根据skuIdList获取配送系数
     */
    List<DistributionPercentDTO>
        getDistributionPercentBySkuList(@Param("productSkuIdList") List<Long> productSkuIdList);

    /**
     * 配送系数修改记录
     */
    void addDistributionPercentRecord(@Param("poList") List<DistributionPercentPO> poList);
}
