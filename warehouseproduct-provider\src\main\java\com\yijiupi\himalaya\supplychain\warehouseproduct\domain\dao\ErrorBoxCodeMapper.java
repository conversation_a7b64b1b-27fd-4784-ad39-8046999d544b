package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ErrorBoxCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ErrorBoxCodeSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 错误条码结果记录
 */
public interface ErrorBoxCodeMapper {

    /**
     * 批量新增记录
     */
    int insertList(@Param("list") List<ErrorBoxCodePO> record);

    /**
     * 获取记录列表
     */
    PageResult<ErrorBoxCodePO> listRecord(ErrorBoxCodeSO so);

}