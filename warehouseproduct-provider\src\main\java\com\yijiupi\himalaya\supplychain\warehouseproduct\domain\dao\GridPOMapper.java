package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.GridPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-21 11:24
 **/
@Mapper
public interface GridPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(GridPO record);

    int insertOrUpdate(GridPO record);

    int insertOrUpdateSelective(GridPO record);

    int insertSelective(GridPO record);

    GridPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GridPO record);

    int updateByPrimaryKey(GridPO record);

    int updateBatch(@Param("list") List<GridPO> list);

    int updateBatchSelective(@Param("list") List<GridPO> list);

    int batchInsert(@Param("list") List<GridPO> list);

    int batchInsertOrUpdate(@Param("list") List<GridPO> list);

    /**
     * 按仓库删除网格配置
     *
     * @param warehouseId 仓库 id
     * @return 影响行数
     */
    int deleteByWarehouseId(Integer warehouseId);

    /**
     * 通过仓库 id 查询网格配置
     *
     * @param warehouseId 仓库 id
     * @return 网格配置
     */
    List<GridPO> selectByWarehouseId(@Param("warehouseId") Integer warehouseId);

    List<GridPO> selectByWarehouseIdAndFloor(@Param("warehouseId") Integer warehouseId, @Param("floor") Integer floor);

}