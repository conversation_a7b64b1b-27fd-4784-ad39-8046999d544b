package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LocationAreaPOMapper {

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table locationarea
     *
     * @mbg.generated Sun Feb 11 10:07:28 CST 2018
     */
    LocationAreaPO selectByPrimaryKey(Long id);

    /**
     * 根据城市id和仓库id和货区名称查询货区详情
     *
     * @param areaList
     * @param warehouse_Id
     * @param city_Id
     * @return
     * @return: List<LocationAreaPO>
     */
    List<LocationAreaPO> findAreaIdByAreaList(@Param("areaList") List<String> areaList,
        @Param("warehouse_Id") Integer warehouse_Id, @Param("city_Id") Integer city_Id);

    /**
     * 新增货区
     */
    void addLocationArea(LocationAreaPO locationAreaPO);

    /**
     * 通过仓库id，城市id和货区获得货区
     */
    LocationAreaPO getLocationArea(LocationAreaPO locationAreaPO);

    /**
     * 查询货区
     */
    PageResult<LocationAreaPO> listLocationArea(@Param("locationAreaPO") LocationAreaPO locationAreaPO, @Param("hasLocation") Boolean hasLocation);

    /**
     * 查询货区不分页
     */
    List<LocationAreaPO> listLocationAreaNoPage(@Param("locationAreaDTO") LocationAreaListDTO locationAreaDTO);

    /**
     * 编辑货区,只改变备注
     */
    void updateLocationArea(LocationAreaPO locationAreaPO);

    /**
     * 编辑货区
     */
    void updateLocationAreaByPO(LocationAreaPO locationAreaPO);

    /**
     * 通过id获取货区
     *
     * @param id
     * @return
     */
    LocationAreaPO getLocationAreaById(@Param("id") Long id);

    /**
     * 根据area和warehouseId查找库区
     *
     * @param warehouseId
     * @param area
     * @return
     */
    String getLocationAreaByArea(@Param("warehouseId") Integer warehouseId, @Param("area") String area,
        @Param("id") Long id);

    /**
     * 删除货区
     *
     * @param id
     */
    void deleteLocation(@Param("id") Long id);
}
