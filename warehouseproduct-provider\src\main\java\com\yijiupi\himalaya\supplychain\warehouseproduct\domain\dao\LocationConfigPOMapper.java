package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-26 09:37
 **/
@Mapper
public interface LocationConfigPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(LocationConfigPO record);

    int insertOrUpdate(LocationConfigPO record);

    int insertOrUpdateSelective(LocationConfigPO record);

    int insertSelective(LocationConfigPO record);

    LocationConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LocationConfigPO record);

    int updateByPrimaryKey(LocationConfigPO record);

    int updateBatch(@Param("list") List<LocationConfigPO> list);

    int updateBatchSelective(@Param("list") List<LocationConfigPO> list);

    int batchInsert(@Param("list") List<LocationConfigPO> list);

    int batchInsertOrUpdate(@Param("list") List<LocationConfigPO> list);

    /**
     * 通过仓库 id 查询 货区画布布局配置信息
     *
     * @param warehouseId 仓库 id
     * @param configType 分仓配置
     * @return 查询结果
     */
    LocationConfigPO selectByWarehouseIdAndConfigType(Integer warehouseId, Integer configType);

    /**
     * 通过仓库 id 查询配置
     *
     * @param warehouseId 仓库 id
     * @return 配置信息
     */
    List<LocationConfigPO> selectByWarehouseId(Integer warehouseId);
}