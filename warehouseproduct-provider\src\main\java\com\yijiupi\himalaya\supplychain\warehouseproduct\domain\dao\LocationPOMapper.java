package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.LocationAreaInfoQueryBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.LocationPageQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productlocation.ProductLocationQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
public interface LocationPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyBatch(@Param("list") List<Long> ids);

    int insertSelective(LocationPO record);

    LocationPO selectByName(@Param("name") String name, @Param("warehouseId") Integer warehouseId,
                            @Param("cityId") Integer cityId);

    int selectBySequence(@Param("id") Long id, @Param("sequence") Integer sequence,
                         @Param("warehouseId") Integer warehouseId, @Param("cityId") Integer cityId);

    int updateByPrimaryKeySelective(LocationPO record);

    /**
     * 根据仓库id获取所有货位
     */
    PageResult<LocationPO> listLocation(@Param("warehouseId") Integer warehouseId,
                                        @Param("locationName") String locationName);

    /**
     * 根据条件查询货位
     */
    PageResult<LocationPO> listLocationByCondition(@Param("dto") LocationQueryDTO dto);

    PageResult<LoactionDTO> findProductLocationPageList(LocationQueryDTO dto);

    List<LoactionDTO> findLocationListByWarehouseIdAndAreaType(Map<String, Object> params);

    Integer findProductLocationCount(Map<String, Object> params);

    ProductLocationDetailDTO getProductLocationBySkuId(@Param("dto") LocationQueryDTO dto);

    List<ProductLocationDetailDTO> findProductLocationBySkuIds(@Param("dto") LocationQueryDTO dto);

    /**
     * 根据货位模糊查询商品skuid
     */
    List<Long> findProductSkuByProductLocation(LocationQueryDTO dto);

    /**
     * 根据货区id查找商品skuid数量
     *
     * @param id
     * @return
     */
    Integer listProductCount(@Param("area_Id") Long id);

    /**
     * 拿到货区的所有货位
     *
     * @param list
     * @return
     */
    List<LocationPO> listProductCountList(@Param("list") List<Long> list);

    /**
     * 根据货区id查找货位
     *
     * @param areaId
     */
    List<LocationPO> listLocationDTO(@Param("area_Id") Long areaId);

    /**
     * 修改货位名称
     *
     * @param poList
     */
    void updateName(@Param("poList") List<LocationPO> poList);

    /**
     * 删除货位
     *
     * @param id
     */
    void deleteByAreaId(@Param("area_Id") Long id);

    /**
     * 根据货区ID查询货位
     *
     * @param id
     */
    List<LocationPO> selectByAreaId(@Param("area_Id") Long id);

    /**
     * 批量新增
     */
    void batchInsert(@Param("locationList") List<LocationPO> locationPoList,
                     @Param("warehouse_Id") Integer warehouse_Id, @Param("city_Id") Integer city_Id);

    List<LocationPO> findLocationListById(@Param("idList") List<String> idList);

    List<LocationPO> findLocationAreaListById(@Param("idList") List<String> idList);

    /**
     * @param dto
     * @return
     */
    List<LocationPO> findLocationList(@Param("dto") LocationQueryDTO dto);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    LocationPO findLocationById(@Param("id") Long id);

    /**
     * TODO
     *
     * @param names
     * @param warehouse_Id
     * @param city_Id
     * @return
     * @return: Integer
     */
    List<LocationPO> selectByNames(@Param("names") List<String> names, @Param("warehouseId") Integer warehouse_Id,
                                   @Param("cityId") Integer city_Id);

    /**
     * 根据货位名称和仓库查询货位或者货区信息
     *
     * @param name
     * @param warehouse_Id
     * @return
     */
    LocationPO findLocationByName(@Param("name") String name, @Param("warehouseId") Integer warehouse_Id);

    /**
     * 根据货位名称和仓库查询货位或者货区信息
     *
     * @return
     */
    List<LocationPO> findLocationByNames(@Param("list") List<String> nameList,
                                         @Param("warehouseId") Integer warehouseId);

    /**
     * 根据货位名称和仓库查询货位或者货区信息
     *
     * @return
     */
    List<LocationCategoryDTO> findLocationListByName(LocationCategoryQuery locationCategoryQuery);

    /**
     * 根据货位id删除所有没有放产品的货位
     *
     * @param idList
     * @return
     */
    Integer deleteLocationByPrimaryKeyList(List<Long> idList);

    /**
     * 根据货位id查询所有没有放产品的货位
     *
     * @param idList
     * @return
     */
    List<LocationPO> getLocationByPrimaryKeyList(List<Long> idList);

    /**
     * 根据id修改
     *
     * @param modifyDTO
     */
    void updateSelectiveByIdList(LocationModifyDTO modifyDTO);

    /**
     * 根据name修改
     *
     * @param modifyDTO
     */
    void updateSelectiveByNameWithWarehouseIdAndCityId(LocationModifyDTO modifyDTO);

    /**
     * 统计货区数目
     *
     * @param locationIdList
     * @return
     */
    Integer countLocationArea(@Param("locationIdList") List<Long> locationIdList);

    /**
     * 按subcategory分组根据仓库ID、类型查询货位信息, 每种subcategory只返回一条记录
     *
     * @param warehouseId
     * @param category
     * @return
     */
    List<LocationPO> selectCargoAreaSingleByWarehouseIdAndCategory(@Param("warehouseId") Integer warehouseId,
                                                                   @Param("category") Integer category);

    /**
     * 根据货位id查询货位和货区信息
     */
    List<LoactionDTO> findLocationAndAreaInfoById(@Param("idList") List<Long> idList);

    /**
     * 根据货位id查询对应货区下指定货位类型的所有货位信息
     */
    List<LoactionDTO> findLocationListByIdAndCategory(@Param("queryDTO") LocationQueryDTO dto);

    /**
     * 根据货位id查询货位信息
     */
    List<LoactionDTO> findLocationByIds(@Param("locationIds") Collection<Long> locationIds);

    /**
     * 根据货位id查询货位信息
     */
    List<Long> findLocationAreaIdsByIds(@Param("locationIds") Collection<Long> locationIds);

    PageResult<LoactionDTO> pageListLocation(@Param("locationQuery") LocationInfoQueryDTO locationQueryDTO,
                                             @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询货位信息列表
     */
    PageResult<LoactionDTO> findProductLocationPageListNew(LocationQueryDTO dto);

    /**
     * 查询货区和货位信息列表
     *
     * @param dto
     * @return
     */
    PageResult<LoactionDTO> findProductLocationOrAreaPageListNew(LocationQueryDTO dto);

    /**
     * 根据条件查询货位范围
     */
    LocationRangeSequenceDTO findLocationRangeByCondition(@Param("locationRange") LocationRangeQueryDTO rangeQueryDTO);

    /**
     * 根据id修改
     *
     * @param modifyDTO
     */
    void updateSelectiveById(LoactionDTO modifyDTO);

    PageResult<VesselDTO> pageListVesselLocation(@Param("locationQuery") VesselInfoQueryDTO vesselInfoQueryDTO,
                                                 @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    LocationPO selectVesselByName(@Param("name") String name, @Param("warehouseId") Integer warehouseId,
                                  @Param("cityId") Integer cityId);

    /**
     * 根据货区id查找货位
     *
     * @param areaIds
     */
    List<LocationPO> listVesselByAreaIds(@Param("areaIds") List<Long> areaIds,
                                         @Param("warehouseId") Integer warehouseId);

    int insertSelectiveBatch(@Param("list") List<LocationPO> recordList);

    List<VesselDetailsDTO> listSKUBesselDetails(@Param("skuIds") List<Long> skuIdList,
                                                @Param("warehouseId") Integer warehouseId);

    /**
     * 分页查询
     */
    PageResult<LocationPO> pageListProductCountList(@Param("locationQueryDTO") LocationPageQueryDTO locationQueryDTO,
                                                    @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    PageResult<ProductLocationDetailDTO> pageListProductLocationBySkuIds(@Param("dto") LocationQueryDTO dto,
                                                                         @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据货位ids查询货位信息
     */
    List<LoactionDTO> findVesselByCondition(@Param("queryDTO") LocationQueryDTO dto);

    /**
     * 根据货位 id 查询关联的物料箱
     */
    List<LocationPO> selectLocationByAreaIds(@Param("areaIds") List<Long> id);

    PageResult<LocationPO> findLocationPageList(@Param("dto") LocationQueryDTO locationQueryDTO);

    int deleteVesselByPrimaryKeyBatch(@Param("list") List<Long> ids);

    List<LocationPO> findLocationAreaListExcludeDefective(@Param("idList") List<String> idList);

    List<LocationPO> pageListLocationByCondition(@Param("dto") LocationScrollQueryDTO dto);

    /**
     * 通过货区查询巷道
     */
    PageResult<LocationPO> pageListRoadway(ProductLocationQueryDTO param);

    /**
     * 通过货区+巷道查询货位
     */
    PageResult<LocationPO> pageListLocationByAreaAndRoad(ProductLocationQueryDTO param);

    /**
     * 根据条件查询货位
     */
    PageResult<LocationPO> pageListLocationByCond(@Param("dto") LocationQueryDTO dto);

    PageResult<LocationPO> selectByIds(Collection<Long> ids);

    int updateLocationBatchById(LocationQueryDTO modifyDTO);

    List<LocationPO> selectByAreaInfo(LocationAreaInfoQueryBO queryBO);

    List<Long> selectLocationIds(@Param("warehouseId") Integer warehouseId, @Param("category") Byte category);

    int clearAisleNoBatchById(LocationModifyDTO modifyDTO);

    /**
     * 通过货区 id 查询货位 id
     *
     * @param areaIds 货区 id
     * @return 货位 id
     */
    List<LocationPO> findLocationByAreaIds(@Param("areaIds") List<Long> areaIds);

    int updateLocationBusinessTypeById(LocationModifyDTO modifyDTO);
}
