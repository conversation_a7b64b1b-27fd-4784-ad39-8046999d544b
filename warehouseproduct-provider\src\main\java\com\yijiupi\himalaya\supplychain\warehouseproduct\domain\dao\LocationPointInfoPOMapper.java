package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-21 11:23
 **/
@Mapper
public interface LocationPointInfoPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(LocationPointInfoPO record);

    int insertOrUpdate(LocationPointInfoPO record);

    int insertOrUpdateSelective(LocationPointInfoPO record);

    int insertSelective(LocationPointInfoPO record);

    LocationPointInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LocationPointInfoPO record);

    int updateByPrimaryKey(LocationPointInfoPO record);

    int updateBatch(@Param("list") List<LocationPointInfoPO> list);

    int updateBatchSelective(@Param("list") List<LocationPointInfoPO> list);

    int batchInsert(@Param("list") List<LocationPointInfoPO> list);

    int batchInsertOrUpdate(@Param("list") List<LocationPointInfoPO> list);

    /**
     * 按仓库删除货位坐标配置
     *
     * @param warehouseId 仓库 id
     * @return 影响行数
     */
    int deleteByWarehouseId(Integer warehouseId);

    /**
     * 通过仓库 id 查询货位坐标配置
     *
     * @param warehouseId 仓库 id
     * @return 货位坐标配置
     */
    List<LocationPointInfoPO> selectByWarehouseId(Integer warehouseId);


    List<Long> findDisableLocationPoint(@Param("areaIds") List<Long> areaIds,@Param("warehouseId") Integer warehouseId);

    List<Long> findLocationAreaIds(@Param("warehouseId") Integer warehouseId);

    List<LocationPointInfoPO> findBindLocationIdByAreaId(@Param("areaIds") List<Long> areaIds, @Param("warehouseId") Integer warehouseId);

    List<LocationPO> findBindLocationAreaByWarehouseId(@Param("warehouseId") Integer warehouseId);


}