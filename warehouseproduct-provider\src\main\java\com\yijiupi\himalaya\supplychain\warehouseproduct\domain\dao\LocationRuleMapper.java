package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;

public interface LocationRuleMapper {
    /**
     * 查询出库位规则
     *
     * @param locationRuleDTOList
     * @param warehouseId
     * @return
     */
    List<LocationRulePO> listLocationRule(@Param("locationRuleDTOList") List<LocationRuleDTO> locationRuleDTOList,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 保存
     *
     * @param locationRulePOList
     */
    void insert(@Param("locationRulePOList") List<LocationRulePO> locationRulePOList);

    /**
     * 根据 线路名称 查询出库位信息<br/>
     * 按行政区域设置的出库位
     */
    LocationRulePO getLocationByName(@Param("joinRegionName") String region, @Param("warehouseId") Integer warehouseId);

    /**
     * 删除历史配置
     *
     * @param warehouseId
     * @param collect
     */
    void delete(@Param("warehouseId") Integer warehouseId, @Param("collect") List<Long> collect);

    /**
     * 根据出库位id 查询规则信息
     */
    List<LocationRulePO> queryByLocationIds(@Param("collect") List<Long> collect,
        @Param("warehouseId") Integer warehouseId, @Param("ruleType") Integer ruleType);

    /**
     * 根据仓库 规则id查询出库位
     *
     * @param warehouseId
     * @param collect
     * @return
     */
    List<LocationRulePO> listLocationByRuleId(@Param("warehouseId") Integer warehouseId,
        @Param("collect") List<String> collect, @Param("ruleType") Integer ruleType);

    void deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据 线路名称 查询出库位信息
     */
    List<LocationRulePO> getLocationsByWarehouseAndRuleType(@Param("warehouseId") Integer warehouseId,
        @Param("ruleType") Integer ruleType);

    List<LocationRulePO> listRuleInfoByLocationId(@Param("warehouseId") Integer warehouseId,
        @Param("locationIds") List<Long> locationIds, @Param("ruleType") Integer ruleType);

}
