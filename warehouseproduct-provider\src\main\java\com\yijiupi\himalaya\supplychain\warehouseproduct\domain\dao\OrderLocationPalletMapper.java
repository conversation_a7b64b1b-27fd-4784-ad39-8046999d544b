package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OrderLocationPalletPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderLocationPalletMapper {

    int insertSelectiveBatch(@Param("list") List<OrderLocationPalletPO> poList);

    List<OrderLocationPalletDTO> selectByIds(@Param("ids") List<Long> Ids);

    List<OrderLocationPalletDTO> selectByConditions(@Param("query") OrderLocationPalletQueryDTO query);

    int deleteByPrimaryKeyBatch(@Param("list") List<Long> ids);

    List<OrderLocationPalletDTO> selectByOrderIds(@Param("orderIds") Collection<Long> orderIds);

    int updateByPrimaryKeySelective(OrderLocationPalletDTO record);

    int updateBatchSelective(List<OrderLocationPalletDTO> list);

    List<OrderLocationPalletDTO> selectByBatchTaskId(String batchTaskId);

    void deleteByBatchTaskId(String batchTaskId);
}
