package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 货主信息查询
 */
public interface OwnerMapper {
    /**
     * 根据仓库Id获取所有在本仓库存放货物的货主
     */
    List<OwnerDTO> getOwnerInfoDTO(@Param("dto") OwnerQueryDTO dto);

    List<OwnerDTO> getOwnerIdByErpOwnerId(@Param("erpOwnerId") List<String> erpOwnerId);

    /**
     * 获取指定货主信息
     *
     * @return
     */
    OwnerPO selectByPrimaryKey(Long id);

    /**
     * 批量获取指定货主信息
     *
     * @return
     */
    List<OwnerPO> listOwnerByIds(@Param("list") List<Long> ids);

    /**
     * 批量插入或更新货主
     *
     * @param ownerPOList
     * @return
     */
    int insertOrUpdateBatch(@Param("list") List<OwnerPO> ownerPOList);

    /**
     * 新增货主
     *
     * @return
     */
    int insertSelective(OwnerPO record);

    /**
     * 更新货主
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OwnerPO record);

    /**
     * 删除货主
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    List<OwnerPO> findOwnerByOrgId(@Param("cityId") Integer orgId);

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    List<OwnerPO> findOwnerByOrgIdAndType(@Param("cityId") Integer orgId, @Param("ownerType") Integer ownerType);

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    List<OwnerPO> findOwnerByOrgIdAndTypeList(@Param("cityId") Integer orgId,
        @Param("list") List<Integer> ownerTypeList);

    /**
     * 根据仓库查询所有有库存的货主
     *
     * @param warehouseId
     * @return
     */
    List<OwnerPO> findOwnerByWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("ownerType") Integer ownerType);

    /**
     * 更新业务伙伴状态
     *
     * @param id
     * @param status
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("state") Byte status, @Param("userId") Long userId);

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    PageResult<OwnerDTO> findOwnerByCondition(OwnerQueryDTO dto);

    /**
     * 校验编号唯一性业务伙伴
     *
     * @param ownerNo
     * @param id
     * @return
     */
    int checkOwnerNoUniq(@Param("ownerNo") String ownerNo, @Param("id") String id);

    /**
     * 根据货主编码list获取货主实体列表
     *
     * @param ownerNos
     * @return
     */
    @MapKey("ownerNo")
    Map<String, OwnerDTO> findOwnerByOwnerNoIn(List<String> ownerNos);

    /**
     * 根据货主编码list获取货主
     *
     * @param ownerNos
     * @return
     */
    List<OwnerDTO> findOwnerByNo(List<String> ownerNos);

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    List<OwnerPO> listOwnerByCondition(OwnerQueryDTO dto);

    /**
     * 根据外部关联id获取批量获取指定货主信息
     *
     * @return
     */
    List<OwnerPO> listOwnerByRefPartnerIds(@Param("list") List<String> refPartnerIds,
        @Param("ownerType") Integer ownerType);

    /**
     * 根据外部关联id或货主id获取批量获取指定货主信息
     *
     * @return
     */
    List<OwnerPO> listOwnerByRefPartnerIdsOrOwnerIds(@Param("list") List<String> refPartnerIdOrOwnerIds,
        @Param("ownerType") Integer ownerType);

    void batchInsert(@Param("list") List<OwnerPO> ownerPOList);

    /**
     * 根据仓库查询所有产品的货主
     *
     * @param warehouseId
     * @return
     */
    List<OwnerPO> findOwnerByWarehouseIdWithSKU(@Param("warehouseId") Integer warehouseId,
        @Param("ownerType") Integer ownerType);
}
