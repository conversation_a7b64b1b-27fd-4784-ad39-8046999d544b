package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PartnerManagerPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Author xialei Date 2019-08-20
 */
public interface PartnerManagerMapper {

    /**
     * 根据业务伙伴Id获取货主详情
     *
     * @param id
     * @return
     */
    PartnerManagerPO getPartnerManagerDetailById(String id);

    /**
     * 根据关联外部Id获取货主详情
     * 
     * @return
     */
    PartnerManagerPO getPartnerManagerDetailByRefId(String refPartnerId);

    /**
     * 根据关联外部Id批量查货主详情
     * 
     * @return
     */
    List<PartnerManagerPO> listPartnerManagerDetailByRefId(@Param("list") List<String> refPartnerIds);

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    PageResult<PartnerManagerDTO> findPartnerManagerByCondition(PartnerManagerDTO dto);

    /**
     * 新增业务伙伴
     *
     * @param dto
     * @return
     */
    int insert(PartnerManagerDTO dto);

    /**
     * 修改业务伙伴
     *
     * @param dto
     * @return
     */
    int update(PartnerManagerDTO dto);

    /**
     * 更新业务伙伴状态
     *
     * @param id
     * @param status
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("status") Byte status, @Param("userId") Long userId);

    /**
     * 批量插入业务伙伴
     *
     * @param partnerManagers
     * @return
     */
    int insertBatch(List<PartnerManagerDTO> partnerManagers);

    /**
     * 校验编号唯一性业务伙伴
     *
     * @param partnerNo
     * @return
     */
    int checkPartnerManagerNoUniq(@Param("partnerNo") String partnerNo, @Param("id") String id);

    /**
     * 获取合作商列表
     * 
     * @param partnerNos
     * @return
     */
    @MapKey("partnerNo")
    Map<String, PartnerManagerDTO> findPartnerByPartnerNoIn(List<String> partnerNos);

    /**
     * 根据编码list获取贸易伙伴
     * 
     * @param partnerNos
     * @return
     */
    List<PartnerManagerDTO> findPartnerByNo(List<String> partnerNos);

    /**
     * 根据idlist获取贸易伙伴
     * 
     * @param ids
     * @return
     */
    List<PartnerManagerDTO> listPartnerByIds(@Param("ids") List<Long> ids);

}