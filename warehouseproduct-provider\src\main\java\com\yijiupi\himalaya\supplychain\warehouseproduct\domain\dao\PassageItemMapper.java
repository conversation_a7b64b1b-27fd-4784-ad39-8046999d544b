package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 通道详情
 *
 * <AUTHOR>
 * @date 2018/8/8 15:03
 */
public interface PassageItemMapper {

    /**
     * 根据通道ID查询相关的通道详情
     * 
     * @param passageSO
     * @return
     */
    List<PassageItemPO> listPassageItemByPassageId(@Param("passageId") Long passageId);

    List<PassageItemPO> listPassageItemByPassageIdAndBiggerId(@Param("passageId") Long passageId, @Param("id") Long id);


    Integer listPassageItemByPassageIdCount(@Param("passageId") Long passageId);

    List<PassageItemPO> listPassageItemByPassageIdPage(@Param("passageId") Long passageId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 查询相关的通道子项详情
     * 
     * @param passageSO
     * @return
     */
    List<PassageItemPO> listPassageItem(@Param("passageId") Long passageId, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据子项查询相关的通道子项详情
     * 
     * @param passageSO
     * @return
     */
    List<PassageItemPO> listPassageItemByRelate(@Param("so") PassageItemSO so);

    /**
     * 批量新增通道详情
     * 
     * @param po
     * @return
     */
    Integer insertBatch(@Param("list") List<PassageItemPO> list);

    /**
     * 根据通道ID删除相关的通道详情
     */
    Integer deleteByPassageId(@Param("passageId") Long passageId);

    /**
     * 查询相关的通道子项详情
     */
    PageResult<PassageItemPO> pageListPassageItem(@Param("passageDTO") PassageDTO passageDTO);

    /**
     * 根据通道明细ID删除
     */
    Integer deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 通过通道 id 查询通道明细
     *
     * @param passageIds 通道 id
     * @return 查询结果
     */
    PageResult<PassageItemPO> selectByPassageIds(Collection<Long> passageIds);

    /**
     * 通过通道 id 和货位 id 查询在该通道内的货位
     *
     * @param passageIds  通道 id
     * @param locationIds 货位 id
     * @return 和货位有交集的通道明细数据
     */
    PageResult<PassageItemPO> selectByPassageIdsAndLocationIds(@Param("warehouseId") Integer warehouseId, @Param("passageIds") Set<Long> passageIds, @Param("locationIds")  Set<String> locationIds);
}
