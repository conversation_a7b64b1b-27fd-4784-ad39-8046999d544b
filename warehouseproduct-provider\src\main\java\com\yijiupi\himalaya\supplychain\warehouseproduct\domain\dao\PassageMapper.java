package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemScrollSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通道
 *
 * <AUTHOR>
 * @since 2018/8/8 15:03
 */
public interface PassageMapper {

    /**
     * 条件查询通道列表
     */
    PageResult<PassagePO> listPassageByCondition(PassageSO passageSO);

    /**
     * 根据id集合查询通道列表
     */
    List<PassagePO> listPassageByIds(@Param("list") List<Long> ids);

    /**
     * 查询通道
     */
    PassagePO selectByPrimaryKey(Long id);

    /**
     * 根据仓库id查询通道类型集合
     */
    List<Byte> listPassageTypeByWarehouseId(@Param("warehouseId") Integer warehouseId);

    /**
     * 根据仓库id查询通道类型
     */
    Byte selectPassageTypeByWarehouseId(@Param("warehouseId") Integer warehouseId);

    /**
     * 新增通道
     */
    Integer insert(PassagePO po);

    /**
     * 修改通道
     */
    Integer updateByPrimaryKey(PassagePO po);

    /**
     * 删除通道
     * 
     * @param id
     * @return
     */
    Integer deleteByPrimaryKey(Long id);

    List<PassagePO> findAll(@Param("so") PassageItemSO passageItemSO);

    PageResult<PassagePO> listPassageAllByCondition(PassageSO so);

    List<PassagePO> pageListPassageItem(@Param("so") PassageItemScrollSO passageItemSO);
}
