package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-05 13:47
 **/
@Mapper
public interface ProductCodeWhitelistPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(ProductCodeWhitelistPO record);

    int insertOrUpdate(ProductCodeWhitelistPO record);

    int insertOrUpdateSelective(ProductCodeWhitelistPO record);

    int insertSelective(ProductCodeWhitelistPO record);

    ProductCodeWhitelistPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductCodeWhitelistPO record);

    int updateByPrimaryKey(ProductCodeWhitelistPO record);

    int updateBatch(@Param("list") List<ProductCodeWhitelistPO> list);

    int updateBatchSelective(@Param("list") List<ProductCodeWhitelistPO> list);

    int batchInsert(@Param("list") List<ProductCodeWhitelistPO> list);

    /**
     * 查询有效的白名单数据
     *
     * @param warehouseId 仓库 id
     * @param skuId      sku id
     * @return 3 个月内的白名单数据
     */
    List<ProductCodeWhitelistPO> selectValidWhitelist(@Param("warehouseId") Integer warehouseId, @Param("skuId") Collection<Long> skuId);

    /**
     * 通过仓库 id 和 skuId 查询已经存在的条码白名单
     *
     * @param warehouseId 仓库 id
     * @param skuId       skuId
     * @return 查询结果
     */
    ProductCodeWhitelistPO selectByWarehouseIdAndSkuId(@Param("warehouseId") Integer warehouseId, @Param("skuId") Long skuId);
}