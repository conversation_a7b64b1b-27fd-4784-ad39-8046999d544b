package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 控货策略明细
 */
public interface ProductControlConfigItemPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductControlConfigItemPO record);

    int insertSelective(ProductControlConfigItemPO record);

    ProductControlConfigItemPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductControlConfigItemPO record);

    int updateByPrimaryKey(ProductControlConfigItemPO record);

    /**
     * 批量新增
     */
    int insertBatch(@Param("list") List<ProductControlConfigItemPO> list);

    /**
     * 根据控货策略ID删除明细
     */
    void deleteByConfigId(Long configId);

    /**
     * 根据控货策略ID查询明细
     * 
     * @return
     */
    List<ProductControlConfigItemPO> listProductControlConfigItemByConfigId(@Param("configIds") List<Long> configIds);
}