package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 控货策略
 */
public interface ProductControlConfigPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductControlConfigPO record);

    int insertSelective(ProductControlConfigPO record);

    ProductControlConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductControlConfigPO record);

    int updateByPrimaryKey(ProductControlConfigPO record);

    /**
     * 根据id查询控货策略
     * 
     * @return
     */
    ProductControlConfigDTO getProductControlConfig(Long id);

    /**
     * 获取控货策略列表
     * 
     * @return
     */
    PageResult<ProductControlConfigDTO> listProductControlConfig(ProductControlConfigSO productControlConfigSO);

    /**
     * 根据用户地址，查询控货策略
     * 
     * @return
     */
    List<ProductControlConfigDTO>
        listProductControlConfigByAddress(ProductControlConfigQuery productControlConfigQuery);

    /**
     * 根据控货策略ID查询控货策略
     * 
     * @return
     */
    List<ProductControlConfigPO> listProductControlConfigByIds(@Param("list") List<Long> ids);

    /**
     * 统计同一供应商、同一产品的控货策略记录数
     * 
     * @return
     */
    Long countProductControlConfig(@Param("provider") String provider, @Param("productSkuId") Long productSkuId,
        @Param("id") Long id);

}