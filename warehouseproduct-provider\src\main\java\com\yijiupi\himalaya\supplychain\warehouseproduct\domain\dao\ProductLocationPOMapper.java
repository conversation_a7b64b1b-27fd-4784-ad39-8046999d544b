package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.AbnormalLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationListSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuBaseInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ProductLocationPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteBatch(@Param("list") List<Long> ids);

    int insert(ProductLocationPO record);

    int insertSelective(ProductLocationPO record);

    int insertBatch(@Param("list") List<ProductLocationPO> recordList);

    ProductLocationPO selectByPrimaryKey(Long id);

    List<ProductLocationPO> selectByIds(@Param("list") List<Long> ids);

    int updateByPrimaryKeySelective(ProductLocationPO record);

    int updateByPrimaryKey(ProductLocationPO record);

    int deleteByLocationId(@Param("locationId") Long productLocationId);

    int deleteByLocationIds(@Param("list") List<Long> productLocationIds);

    /**
     * 查询该条记录是否存在.
     */
    Long getProductLocationCount(@Param("productSkuId") Long productSkuId, @Param("locationId") Long locationId,
                                 @Param("warehouseId") Integer warehouseId);

    /**
     * 查找不允许混放的货位关联产品记录数
     */
    Long getProductLocationByMixUpCount(@Param("productSkuId") Long productSkuId, @Param("locationId") Long locationId,
                                        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据locationid查找locationarea
     */
    List<ProductLocationPO> findByLocationId(Long locationId);

    /**
     * 根据locationid查找locationarea
     */
    List<ProductLocationPO> findByLocationIds(@Param("list") Collection<Long> locationIds);

    /**
     * 根据SKUID查询关联的货位信息
     */
    List<ProductLoactionItemDTO> findLocationBySkuId(@Param("list") Collection<Long> productSkuIds, @Param("warehouseId") Integer warehouseId);

    /**
     * 根据货位的查询关联的货位信息
     */
    List<ProductLoactionItemDTO> findLocationByLocationIds(@Param("list") List<Long> locationIds,
                                                           @Param("warehouseId") Integer warehouseId);

    /**
     * 根据仓库ID和产品skuId删除产品货位关系
     */
    int deleteBySkuId(@Param("list") List<Long> productSkuIds, @Param("warehouseId") Integer warehouseId);

    /**
     * 获取产品货位配置列表
     */
    PageResult<ProductLocationListPO> listProductLocation(ProductLocationListSO so);

    /**
     * 获取产品货位配置列表
     */
    PageResult<ProductLocationListPO> listProductLocationWithInventory(ProductLocationListSO so);

    /**
     * 获取产品货位配置列表-没关联库存
     */
    PageResult<ProductLocationListPO> listProductLocationWithoutInventory(ProductLocationListSO so);

    /**
     * 获取产品货位配置（不分页）
     */
    List<ProductLocationListPO> listProductLocationNoPage(ProductLocationListSO so);

    /**
     * 获取所有产品货位记录
     */
    List<ProductLocationPO> listProductLocationSelective(ProductLocationSO so);

    /**
     * 根据skuId列表查询所有货位产品关联信息
     */
    List<ProductLocationPO> selectBySkuIds(@Param("skuIdList") Collection<Long> skuIdList);

    /**
     * 统计货位列表总的存放的产品条目数
     */
    Integer countHavingSku(@Param("locationIdList") List<Long> locationIdList);

    /**
     * 根据产品货位关联关系查询产品信息
     */
    List<ProductSkuPO> findLocationPruductsByRelation(LocationProductQuery productQuery);

    /**
     * 根据产品配置货位信息查询相关产品SKU基础信息
     */
    PageResult<ProductSkuBaseInfoDTO> findProductBaseInfoByProductLocation(ProductLocationListSO so);

    /**
     * 根据产品SKU信息查询仓库产品配置货位信息
     */
    List<ProductLocationListPO> findProductLocationBySkuInfo(ProductLocationListSO so);

    /**
     * 根据货位id查找对应skuId
     */
    List<ProductLocationPO> listProductByLocationIds(@Param("cityId") Integer cityId,
                                                     @Param("warehouseId") Integer warehouseId, @Param("list") List<Long> locationIds);

    List<ProductLoactionItemDTO> findLocationItemByCon(@Param("dto") ProductLocationItemQueryDTO queryDTO);

    /**
     * 根据产品货位关联关系查询产品信息
     */
    List<ProductLoactionSkuInfoDTO> findProductByRelationLocation(LocationProductQuery productQuery);

    /**
     * 根据货区查找关联产品信息
     */
    List<ProductLoactionSkuInfoDTO> findProductByLocationGroup(LocationProductQuery productQuery);

    /**
     * 根据条件查找关联产品信息
     */
    List<ProductLoactionSkuInfoDTO> findProductByCondition(LocationProductQuery productQuery);

    /**
     * 查询没有关联产品的货位
     *
     * @param warehouseIds 仓库 id
     * @return 货位和仓库的关系
     */
    List<AbnormalLocationBO> findNoneRefProductLocation(Collection<Integer> warehouseIds);

    /**
     * 查询有库存, 没有关联货位的 sku
     *
     * @param warehouseIds 仓库 id
     * @return skuId 和仓库 id
     */
    List<AbnormalLocationBO> findNoLocationSku(Collection<Integer> warehouseIds);

    /**
     * 查询没有关联货位的, 酒饮类目的 skuId, 如果不传 restCategoryIds 将会查询所有没有关联货位的 skuId
     *
     * @param skuIds          skuId
     * @param restCategoryIds 休食类目 id
     * @return 没有关联货位的 skuId
     */
    List<AbnormalLocationBO> findNoLocationSkuBySkuIds(Collection<Long> skuIds, Set<Long> restCategoryIds);

    /**
     * 根据条件查找产品关联货位信息
     */
    List<ProductLoactionSkuInfoDTO> findProductLocationByCondition(LocationProductQuery productQuery);
}
