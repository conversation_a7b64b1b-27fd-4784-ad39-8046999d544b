/*
 * @ClassName ProductRelationGroupPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2020-06-18 16:23:55
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductGroupInitDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductGroupInitQueryResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductTotalGroupQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface ProductRelationGroupPOMapper {
    /**
     * @param id
     * @return int
     * @Title deleteByPrimaryKey
     */
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyList(@Param("idList") List<Long> idList);

    /**
     * @param record
     * @return int
     * @Title insertSelective
     */
    int insertSelective(ProductRelationGroupPO record);

    int insertOrUpdateBatchList(List<ProductRelationGroupPO> poList);

    /**
     * @param id
     * @return ProductRelationGroupPO
     * @Title selectByPrimaryKey
     */
    ProductRelationGroupPO selectByPrimaryKey(Long id);

    /**
     * 根据仓库ID和规格ID查询分组信息
     */
    List<ProductRelationGroupPO> selectExistProductGroupInfo(@Param("skuIdList") Collection<Long> skuIdList,
        @Param("warehouseIdList") Collection<Integer> warehouseIdList);

    /**
     * 根据sku查询完整的分组信息
     */
    List<ProductRelationGroupPO> selectTotalGroupConfig(ProductTotalGroupQueryDTO queryDTO);

    /**
     * 初始化产品关系查询
     */
    List<ProductGroupInitQueryResultDTO> findYkAndJpSameSpecProducts(ProductGroupInitDTO initDTO);

    /**
     * @param record
     * @return int
     * @Title updateByPrimaryKeySelective
     */
    int updateByPrimaryKeySelective(ProductRelationGroupPO record);

    /**
     * 统计指定仓库有多少关联关系
     */
    int countRelationByWarehouseId(@Param("warehouseId") Integer warehouseId);
}