package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品sku配置表
 *
 * <AUTHOR>
 * @since 2020-01-08 10:34
 */
public interface ProductSkuConfigPOMapper {

    List<ProductSkuConfigPO> findSkuConfigById(@Param("ids") List<Long> ids);

    /**
     * 根据skuId查询产品sku配置
     */
    List<ProductSkuConfigPO> findDisableSkuConfigBySkuIdsAndWarehouseId(@Param("warehouseId") Integer warehouseId,
                                                                        @Param("productSkuIds") List<Long> productSkuIds);

    /**
     * 启用产品sku
     */
    void enableProductSku(@Param("configIdList") List<Long> configIdList);

    /**
     * 停用产品sku
     */
    void disableProductSku(@Param("configIdList") List<Long> configIdList);

    /**
     * 三个月前已下架库存为0的SKU
     */
    Long getSkuOffShelfAndStockZeroThreeMonthsAgoMaxId(@Param("warehouseId") Integer warehouseId);
    List<Long> findSkuOffShelfAndStockZeroThreeMonthsAgo(@Param("warehouseId") Integer warehouseId, @Param("lastMinId") Long lastMinId);
}
