package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductSkuLabelMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ProductSkuLabelPO record);

    int insertSelective(ProductSkuLabelPO record);

    ProductSkuLabelPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSkuLabelPO record);

    int updateByPrimaryKey(ProductSkuLabelPO record);

    List<ProductSkuLabelPO> listProductSkuLabel(ProductSkuLabelSO productSkuLabelSO);

    ProductSkuLabelPO getProductSkuLabelByType(@Param("orgId") Integer orgId, @Param("warehouseId") Integer warehouseId,
        @Param("productSkuId") Long productSkuId, @Param("labelType") Byte labelType);

}