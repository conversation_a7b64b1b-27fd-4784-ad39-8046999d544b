package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ListSkuDetailParam;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ProductWarehouseAllocationTypeVerifyBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.SkuInventoryAndProductionDateQO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuDeliveryPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.UnifySkuSimplePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ProductSkuPOMapper {
    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    int insert(ProductSkuPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    int insertSelective(ProductSkuPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    ProductSkuPO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    int updateByPrimaryKeySelective(ProductSkuPO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table productsku
     *
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    int updateByPrimaryKey(ProductSkuPO record);

    /**
     * 城市产品数量
     *
     * @param cityId
     * @return
     * @return: int
     */
    int selectCountByCityId(@Param("cityId") Integer cityId);

    /**
     * 城市产品
     *
     * @return
     * @return: int
     */
    ProductSkuPO selectByCityIdAndProductSkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 包含序号插入
     *
     * @param record
     * @return
     * @return: int
     */
    int insertByCity(ProductSkuPO record);

    /**
     * 根据规格 ID 查找对应的 SKUID
     */
    List<Long> selectSkuIdsWithDifferenceName(@Param("name") String name,
        @Param("productSpecificationIds") List<Long> productSpecificationIds);

    /**
     * 修改名称
     *
     * @param name
     * @return
     * @return: int
     */
    int updateSkuName(@Param("name") String name, @Param("skuIds") List<Long> skuIds);

    /**
     * 获取SKU序号
     *
     * @param cityId
     * @return
     * @return: List<ProductSkuPO>
     */
    List<ProductSkuPO> getSequenceByCityAndProductSkuIds(@Param("cityId") Integer cityId,
        @Param("list") List<Long> productSkuIds);

    /**
     * 根据城市id,产品名称查询产品列表.
     */
    PageResult<ProductSkuInfoReturnDTO> getProductSkuInfo(@Param("dto") ProductSkuInfoSearchDTO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据skuid批量查询info
     *
     * @param productSkuIdList
     * @return
     * @return: List<ProductSkuPO>
     */
    List<ProductSkuPO> getProductSkuInfoBySkuList(List<Long> productSkuIdList);

    /**
     * 根据时间段查询SKU的数量
     *
     * @param params
     * @return
     */
    int countProductInPeriod(Map<String, Object> params);

    List<OwnerInfoPO> getOwnerInfoBySkuId(@Param("list") List<Long> productSkuList);

    /**
     * 根据skuId查询产品信息
     *
     * @param productSkuList
     */
    List<ProductSkuPO> getProductInfoBySkuId(@Param("productSkuList") List<Long> productSkuList);

    /**
     * 根据规格参数id查询产品信息
     *
     * @param productSpecificationList
     */
    List<ProductSkuPO> getProductInfoBySpecificationId(@Param("cityId") Integer cityId,
        @Param("productSpecificationList") List<Long> productSpecificationList, @Param("ownerId") Long ownerId,
        @Param("source") Integer source);

    /**
     * 查询所有prouductSkuId
     *
     * @return
     */
    List<Long> listProductSkuId();

    /**
     * 更新产品信息id
     *
     * @param productSkuPOList
     */
    void updateProductInfoId(@Param("list") List<ProductSkuPO> productSkuPOList);

    /**
     * 获取商品详情
     */
    PageResult<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 获取商品详情（有库存的商品）
     *
     * @param productSkuInfoSO
     * @return
     */
    PageResult<ProductSkuInfoDTO> listProductSkuInfoIncludeStore(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 根据产品skuId查询产品信息
     */
    List<ProductSkuInfoDTO> listProductSkuInfoBySkuId(@Param("cityId") Integer cityId,
        @Param("list") List<Long> productSkuIds);

    /**
     * 根据产品skuId查询产品信息
     */
    List<ProductSkuInfoDTO> findProductSkuInfo(@Param("cityId") Integer cityId,
        @Param("list") List<Long> productSkuIds);

    /**
     * 根据产品skuId查询产品生产日期详情
     */
    List<ProductionDateDTO> listProductionDate(SkuInventoryAndProductionDateQO qo);

    /**
     * 查询城市所有SKU数量
     *
     * @return
     */
    int countProductByCity(@Param("cityId") Integer cityId);

    /**
     * 分页查询产品列表
     */
    PageResult<ProductSkuPO> listProductSku(@Param("so") ProductSkuQueryDTO so, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 分页查询产品列表(完整的sku仓库配置)
     *
     * @return
     */
    PageResult<ProductSkuPO> listProductSkuFull(ProductSkuSO so);

    /**
     * 根据skuid修改
     *
     * @param reverseConvert
     * @return: void
     */
    void updateByProductSkuIdSelective(ProductSkuPO reverseConvert);

    /**
     * 根据skuId查找(完整的sku仓库配置)
     */
    List<ProductSkuPO> findBySkuFull(@Param("warehouseId") Integer warehouseId,
        @Param("list") Collection<Long> productSkuIds);

    /**
     * 根据skuId查找(完整的sku仓库配置)
     */
    List<ProductSkuPO> findBySkuFullNew(@Param("warehouseId") Integer warehouseId,
                                        @Param("list") Collection<Long> productSkuIds,
                                        @Param("warehouseAllocationType") Integer warehouseAllocationType);

    /**
     * 查询产品相关价格
     */
    List<ProductPriceDTO> listProuductPrice(@Param("list") List<ProductPriceQueryDTO> productPriceQueryDTOS);

    /**
     * 根据sku查找
     */
    List<ProductSkuPO> findBySku(@Param("list") Collection<Long> productSkuIds);

    /**
     * @param productSkuIds
     * @return
     */
    List<ProductSkuPO> findBySkuAndRef(@Param("skuList") List<Long> productSkuIds,
        @Param("refSkuList") List<String> refSkuIds);

    /**
     * 根据规格查询产品
     *
     * @return
     */
    List<ProductSkuPO> findBySpec(ProductSkuBySpecificationSO productSkuBySpecSO);

    /**
     * 根据产品规格ID + ownerId + source查找sku
     *
     * @return
     */
    List<ProductSkuPO> listProductSkuBySpec(@Param("cityId") Integer cityId,
        @Param("list") List<ProductSkuBySpecSO> specSOList);

    /**
     * 查询加工商品列表
     */
    PageResult<ProcessProductSkuDTO> pageListProcessProductSku(
        @Param("queryDTO") ProcessProductSkuQueryDTO processProductSkuQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 通过infoId、规格id、货主id查询对应的可加工产品
     */
    List<ProcessProductSkuDTO>
        listProcessProductSkuByInfo(@Param("queryDTO") ProcessProductInfoQueryDTO processProductInfoQueryDTO);

    /**
     * 通过infoId查询对应的可加工产品
     */
    List<ProcessProductSkuDTO>
        listProcessProductSkuByInfoId(@Param("queryDTO") ProcessProductInfoQueryDTO processProductInfoQueryDTO);

    /**
     * 根据城市、仓库、货位id查询产品信息
     *
     * @param productQuery
     * @return
     */
    List<ProductSkuPO> findLocationPruductsByBatchInfo(LocationProductQuery productQuery);

    /**
     * 根据产品skuId查询产品信息
     *
     * @return
     */
    ProductSkuPO findProductSkuInfoBySkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 批量更新sku
     */
    void updateBatch(@Param("list") List<ProductSkuPO> productSkuPOList);

    List<ProductSkuPO> findByProductSpecificationIds(
        @Param("productSpecificationIds") List<Long> productSpecificationIds, @Param("ownerId") Long ownerId,
        @Param("orgId") Integer orgId);

    /**
     * 批量更新sku属性
     */
    void updateBatchAttr(@Param("list") List<ProductSkuPO> productSkuPOList);

    /**
     * 批量更新sku属性（是否独家、是否允许混放、关联产品状态）
     */
    void updateBatchRelate(@Param("list") List<ProductSkuPO> productSkuPOList);

    /**
     * 批量更新sku属性（是否独家、是否允许混放）
     */
    void updateBatchByUniqueAndFleeGoods(@Param("list") List<ProductSkuPO> productSkuPOList);

    /**
     * 批量设置库存占用比例
     */
    void updateBatchInventoryRatio(ProductInventoryRatioUpdateDTO productInventoryRatioUpdateDTO);

    /**
     * 根据规格参数id查询产品信息
     *
     * @param productSpecificationList
     */
    List<ProductSkuPO> findProductInfoBySpecificationIdAndOwners(@Param("cityId") Integer cityId,
        @Param("productSpecificationList") List<Long> productSpecificationList,
        @Param("ownerIdList") List<Long> ownerIdList);

    /**
     * 根据skuId查询产品（规格id、货主id、source）
     *
     * @param ids
     * @return
     */
    List<ProductSkuPO> listProductSkuByIds(@Param("list") List<Long> skuIds);

    /**
     * 根据产品规格ID + ownerId + source查找sku(适用于产品关联查询)
     *
     * @return
     */
    List<ProductSkuPO> findProductSkuByProductRelation(@Param("cityId") Integer cityId,
        @Param("list") List<ProductSkuBySpecSO> specSOList);

    /**
     * 根据大区ID+规格Id+source查找sku
     *
     * @return
     */
    ProductSkuPO getProductSkuBySpecIdAndSource(@Param("cityId") Integer cityId, @Param("specId") Long productSpecId,
        @Param("source") Integer productSource);

    /**
     * 查询产品实际发货城市skuId
     *
     * @return
     */
    List<ProductSkuDeliveryPO> listProductSkuDelivery(
        @Param("specList") List<ProductSkuBySpecificationQueryDTO> specList, @Param("cityId") Integer cityId,
        @Param("deliveryCityId") Integer deliveryCityId);

    /**
     * 查询易款店仓仓配进货单实际发货城市skuId
     *
     * @return
     */
    List<ProductSkuDeliveryPO> listProductSkuDeliveryByCPJH(@Param("specList") List<ProductSkuDeliveryItemSO> specList,
        @Param("cityId") Integer cityId, @Param("deliveryCityId") Integer deliveryCityId);

    /**
     * 根据关联的外部产品skuId查询仓库实际的SKUID
     *
     * @return
     */
    List<Long> getSkuIdByRefSkuId(@Param("cityId") Integer cityId, @Param("list") List<String> refSkuIds);

    /**
     * 根据大区ID+规格Id集合+source查找skuId
     *
     * @return
     */
    List<Long> getSkuIdByCityIdAndSpecId(@Param("cityId") Integer cityId, @Param("list") List<Long> specIds,
        @Param("source") Integer productSource);

    /**
     * 根据条件查询产品信息及类目信息
     */
    List<ProductSkuInfoDTO> findProductInfoAndCategory(ProductInfoCategoryQuery query);

    /**
     * 查询有仓库库存[包含负库存]产品信息及类目
     */
    PageResult<ProductSkuInfoDTO> findHaveInventoryProductInfo(ProductInfoCategoryQuery query);

    Long listProductSkuInfoCount(@Param("query") ProductSkuInfoSO productSkuInfoSO);

    /**
     * 根据sku查询产品信息及顶级类目信息
     */
    List<ProductSkuInfoDTO> findProductAndTopCategory(ProductInfoCategoryQuery query);

    /**
     * 从货位中查找产品
     *
     * @return
     */
    List<ProductSkuPO> findProductInLocation(@Param("locationId") Long locationId,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据产品skuId查询产品品牌、一级类目ID
     *
     * @return
     */
    ProductSkuPO findProductBrandCategoryBySku(@Param("productSkuId") Long productSkuId);

    /**
     * 查询中台skuId
     */
    List<UnifySkuSimplePO> findUnifySkuBySpecIdAndOwnerId(
        @Param("productSpecIdAndOwnerIds") List<ProductSpecIdAndOwnerIdDTO> productSpecIdAndOwnerIds);

    List<UnifySkuSimplePO> findUnifySkuBySkuIds(@Param("skuIds") List<Long> skuIds);

    List<UnifySkuSimplePO> findUnifySkuByUnifySkuIds(@Param("unifySkuIds") List<Long> unifySkuIds,
        @Param("cityId") Integer cityId);

    /**
     * 根据城市id、规格id、货主id查询sku信息
     */
    ProductSkuPO getProductSkuBySpecId(@Param("cityId") Integer cityId, @Param("specId") Long specId,
        @Param("ownerId") Long ownerId, @Param("source") Integer source);

    /**
     * 根据skuId修改转移目标skuId
     */
    void updateReplaceToSkuId(@Param("skuIdList") List<Long> skuIdList, @Param("replaceToSkuId") Long replaceToSkuId);

    ProductSkuPO selectBySkuId(@Param("skuId") Long skuId);

    Long isHaveReplaceSku(@Param("skuList") List<ProductSkuDTO> skuList);

    /**
     * 查询相同产品info的sku
     */
    List<ProductSkuPO> listSameUnifyProductInfoSku(@Param("cityId") Integer cityId,
        @Param("skuIdList") List<Long> skuIdList);

    /**
     * 根据货主 规格 仓库 查询sku信息
     */
    List<ProductSkuPO> listSkuDetails(ListSkuDetailParam param);

    /**
     * 查询sku特征
     *
     * @param productSkuList
     * @return
     */
    List<ProductSkuPO> getProductCharacteristicBySkuIds(@Param("productSkuList") List<Long> productSkuList,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 查询瓶码信息
     *
     * @param skuList
     * @return
     */
    List<ProductSkuDTO> listSkuBottleCode(@Param("skuList") List<Long> skuList, @Param("cityId") Integer cityId);

    /**
     * 查询箱码信息
     *
     * @param skuList
     * @return
     */
    List<ProductSkuDTO> listSkuPackagingCode(@Param("skuList") List<Long> skuList, @Param("cityId") Integer cityId);

    /**
     * 根据skuId查找(完整的sku仓库配置)
     */
    List<ProductSkuPO> findProductBySkuFull(@Param("warehouseId") Integer warehouseId,
        @Param("list") List<Long> productSkuIds);

    List<Map<String, Object>> queryInventoryProperty(@Param("warehouseId") Integer warehouseId);

    List<ProductSkuPO> getProductSkuByCon(@Param("cityId") Integer cityId, @Param("dealerId") Long dealerId,
        @Param("companyId") Long companyId, @Param("productSpecificationIds") List<Integer> productSpecificationIds);

    /**
     * 根据外部编码查询产品列表
     */
    List<ProductSkuPO> findBySkuOuterCode(@Param("cityId") Integer cityId, @Param("list") List<String> outerCodeList);

    /**
     * 根据城市id查询产品列表.
     */
    PageResult<ProductSkuPO> pageListProductSkuInfo(@Param("dto") ProductSkuQueryDTO dto);

    /**
     * 批量更新sku是否删除
     */
    void updateIsDeleteBatch(@Param("skuIdList") Collection<Long> skuIdList, @Param("isDelete") Byte isDelete);

    /**
     * 批量更新 sku 状态
     *
     * @param skuIds skuId
     * @param state  sku 状态
     */
    void updateProductStateBatch(@Param("skuIds") Collection<Long> skuIds, @Param("state") Integer state);

    /**
     * 获取SKU分仓属性
     */
    List<ProductSkuPO> getSkuConfigStorageAttribute(ProductSkuInfoSO productSkuInfoSO);

    List<ProductWarehouseAllocationTypeVerifyBO> findProductWarehouseAllocationTypeVerifyBO(@Param("query") ProductWarehouseAllocationTypeVerifyQuery query);
}
