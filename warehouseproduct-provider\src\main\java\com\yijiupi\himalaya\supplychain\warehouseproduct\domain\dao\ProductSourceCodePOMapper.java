package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSourceCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 溯源码
 */
public interface ProductSourceCodePOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductSourceCodePO record);

    int insertSelective(ProductSourceCodePO record);

    ProductSourceCodePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSourceCodePO record);

    void batchInsertOrUpdate(@Param("list") List<ProductSourceCodePO> productSourceCodePOS);

    void productSourceCodesAssociateOrder(@Param("list") List<ProductSourceCodeDTO> productSourceCodeDTOS);

    ProductSourceCodePO getByConfigIdAndCode(@Param("configId") Long configId, @Param("code") String code);

    PageResult<ProductSourceCodePO> pageListProductSourceCode(
        @Param("query") ProductSourceCodeQueryDTO productSourceCodeQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    ProductSourceCodePO getProductSourceCode(@Param("code") String code);

    void collectProductSourceCode(@Param("configId") Long configId,
        @Param("list") List<ProductSourceCodeDTO> productSourceCodeDTOS);

    List<ProductSourceCodePO> findBySourceCodesAndState(@Param("productSourceCodes") List<String> productSourceCodes,
        @Param("sourceCodeState") Byte sourceCodeState);

    void clearByBusinessNos(@Param("businessNos") List<String> businessNos, @Param("operator") String operator);

    void deleteBySourceBusinessNos(@Param("sourceBusinessNos") List<String> sourceBusinessNos);

    int getCodesCountByConfigIdAndCode(@Param("configId") Long configId, @Param("code") String code,
        @Param("sourceCodeState") Byte sourceCodeState);

    /**
     * 根据溯源码查详情
     * 
     * @return
     */
    List<ProductSourceCodePO> getProductSourceByCodeList(@Param("list") List<String> code,
        @Param("sourceCodeState") Byte sourceCodeState);

    void processSourceCodes(@Param("sourceCodes") List<ProductSourceCodeDTO> productSourceCodeDTOS);

    /**
     * 根据单号查详情
     * 
     * @return
     */
    List<ProductSourceCodePO> findByBusinessNos(@Param("businessNos") List<String> businessNos);

    void clearRelationByCodes(@Param("codes") List<String> codes, @Param("operator") String operator);

    void bindProductSourceCodes(@Param("list") List<ProductSourceCodePO> list);

    /**
     * 查询溯源码和溯源码记录
     */
    List<ProductSourceCodePO> getProductSourceCodeAndRecord(@Param("code") String code);

    /**
     * 根据溯源码id查详情
     * 
     * @return
     */
    List<ProductSourceCodePO> getProductSourceByIdList(@Param("productSourceCodeIds") List<Long> productSourceCodeIds,
        @Param("sourceCodeState") Byte sourceCodeState);

    /**
     * 批量更新溯源码信息
     */
    int batchUpdateProductSourceCode(@Param("list") List<ProductSourceCodePO> list);

    /**
     * 产品sku codes 根据溯源码查详情
     * 
     * @return
     */
    List<ProductSourceCodeInfoDTO> getProductSourceByCodeInfoList(
        @Param("codeSkuList") List<ProductSourceCodeCheckDTO> codeSkuList,
        @Param("sourceCodeState") Byte sourceCodeState);

    /**
     * 修改溯源码状态
     */
    void updateProductSourceState(@Param("id") Long id, @Param("state") Byte state, @Param("operator") String operator);

    /**
     * 根据溯源码查详情
     * 
     * @return
     */
    List<ProductSourceCodePO> getProductSourceByCodeIds(@Param("list") List<Long> codeId);
}