package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 溯源码记录
 */
public interface ProductSourceCodeRecordPOMapper {

    ProductSourceCodeRecordPO selectByPrimaryKey(Long id);

    int deleteByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSourceCodeRecordPO record);

    /**
     * 批量删除溯源码记录
     * 
     * @return
     */
    int deleteBatch(@Param("list") List<Long> ids);

    /**
     * 获取溯源码记录列表
     * 
     * @return
     */
    PageResult<ProductSourceCodeRecordPO>
        listProductSourceCodeRecord(ProductSourceCodeRecordSO productSourceCodeRecordSO);

    /**
     * 根据溯源码id查询记录
     * 
     * @return
     */
    List<ProductSourceCodeRecordPO> listByCodeId(Long productSourceCodeId);

    /**
     * 新增溯源码记录
     */
    int insertSelective(ProductSourceCodeRecordPO record);

    /**
     * 批量新增溯源码记录
     */
    int insertBatch(@Param("list") List<ProductSourceCodeRecordPO> recordList);

    /**
     * 查询溯源码记录id
     * 
     * @return
     */
    List<Long> listProductSourceCodeRecordIds(@Param("list") List<ProductSourceCodeRecordPO> recordList);

    /**
     * 获取溯源码最近状态
     * 
     * @return
     */
    Byte getProductSourceCodeState(@Param("productSourceCodeId") Long productSourceCodeId);

}