package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationStorageNumDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2018/12/14 10:03
 */
public interface ProductStoreBatchPOMapper {
    /**
     * 统计存放的产品条目数
     *
     * @param locationIdList
     * @return
     */
    Integer countHavingSku(@Param("locationIdList") List<Long> locationIdList);

    /***
     *
     * 统计每个货位所占用的产品条目数
     *
     * @return
     */
    List<LocationStorageNumDTO> countHavingGroupByLocationId(@Param("locationIdList") List<Long> locationIdList);

    /**
     * 查询批次库存各状态
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<Map<String, Object>> invertoryBatchStateList(@Param("dto") BatchInventoryQueryDTO batchInventoryQueryDTO);

    /**
     * 查询产品关联货位库存各状态
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    List<Map<String, Object>> invertoryStateList(@Param("dto") BatchInventoryQueryDTO batchInventoryQueryDTO);

    List<Map<String, Object>> productShelfLifeList(@Param("dto") BatchInventoryQueryDTO batchInventoryQueryDTO);

    int insertIntoNewWarehouseStoreFromOtherWarehouse();

    int insertIntoNewWarehouseStoreBatch();

    /***
     * 批量更新批次库存关联的货位
     * @return
     */
    int updateProductStoreBatchLocation(@Param("lstId") List<String> lstId, @Param("locationId") Long locationId, @Param("locationName") String locationName);

}
