/*
 * @ClassName ProductSupplierPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2020-07-15 17:47:07
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierBySpecIdDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductSupplierPOMapper {
    /**
     * @param id
     * @return int
     * @Title deleteByPrimaryKey
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @param record
     * @return int
     * @Title insertSelective
     */
    int insertSelective(ProductSupplierPO record);

    /**
     * @param id
     * @return ProductSupplierPO
     * @Title selectByPrimaryKey
     */
    ProductSupplierPO selectByPrimaryKey(Long id);

    /**
     * @param record
     * @return int
     * @Title updateByPrimaryKeySelective
     */
    int updateByPrimaryKeySelective(ProductSupplierPO record);

    /**
     * 查询产品仓库供应商关系
     */
    List<ProductSupplierPO> selectProductSupplierBySpecInfo(ProductSupplierBySpecIdDTO querySpecDTO);

    /**
     * 批量新增产品供应商
     */
    int insertOrUpdateBatchSupplierList(@Param("poList") List<ProductSupplierPO> poList);

}