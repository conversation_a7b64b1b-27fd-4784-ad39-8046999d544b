package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesAreaConfigMapper {
    int deleteByIds(@Param("list") List<Long> ids);

    Long insert(@Param("dto") SalesAreaConfigDTO record);

    List<SalesAreaConfigDTO> selectByIds(@Param("list") List<Long> ids);

    PageResult<SalesAreaConfigDTO> selectByWarehouseId(@Param("dto") SalesAreaQueryDTO query,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    int update(SalesAreaConfigDTO record);

    int updateState(@Param("list") List<Long> ids, @Param("state") Byte state);

    SalesAreaConfigDTO selectById(@Param("id") Long configId);

    SalesAreaConfigDTO selectAllById(Long parentId);
}