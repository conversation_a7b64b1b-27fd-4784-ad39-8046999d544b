package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesAreaItemMapper {
    int deleteByIds(@Param("list") List<Long> ids);

    int deleteParentIds(@Param("list") List<Long> parentIds);

    int insertBatch(@Param("list") List<SalesAreaItemDTO> record);

    int insertUpdateBatch(@Param("list") List<SalesAreaItemDTO> record);

    SalesAreaItemDTO selectByPrimaryKey(Long id);

    List<SalesAreaItemDTO> selectByParentIds(@Param("list") List<Long> parentId);

    int updateByPrimaryKeySelective(SalesAreaItemDTO record);

    int updateStateByParentId(@Param("list") List<Long> parentId, @Param("state") Byte state);

    int updateStateByIds(@Param("list") List<Long> parentId, @Param("state") Byte state);

    PageResult<SalesAreaItemDTO> pageListSalesAreaItems(@Param("query") SalesAreaItemQueryDTO query,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);
}