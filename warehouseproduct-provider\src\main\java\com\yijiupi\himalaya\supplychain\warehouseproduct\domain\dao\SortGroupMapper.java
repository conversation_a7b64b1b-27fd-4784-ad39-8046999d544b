package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupListPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分区
 *
 * <AUTHOR>
 * @since 2018/5/15 18:16
 */
public interface SortGroupMapper {

    /**
     * 条件查询分区列表
     */
    PageResult<SortGroupListPO> listGroupByCondition(SortGroupSO so);

    /**
     * 根据分区id查询分区
     */
    SortGroupPO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 条件查询分区集合
     */
    List<SortGroupPO> selectBySortGroupDTOList(SortGroupSO so);

    /**
     * 新增分区
     */
    Integer insert(SortGroupPO po);

    /**
     * 修改分区
     */
    Integer updateByPrimaryKey(SortGroupPO po);

    /**
     * 删除分区
     */
    Integer deleteByPrimaryKey(Long id);

    /**
     * 通过分区 id 查询关联的货位名
     *
     * @param groupIds 分区 id
     * @return 关联的货位名
     */
    List<String> selectSortNameByIds(@Param("groupIds") List<Long> groupIds);
}
