package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupRfidPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidSO;

/**
 * 电子标签
 */
public interface SortGroupRfidMapper {

    /**
     * 全量获取分区标签列表
     *
     * @param so
     * @return
     */
    PageResult<SortGroupRfidDTO> listSortGroupRfid(SortGroupRfidSO so);

    /**
     * 分页获取分区标签列表
     *
     * @return
     */
    int insertBatch(@Param("list") List<SortGroupRfidPO> list);

    /**
     * 批量新增分区标签列
     *
     * @return
     */
    int deleteByPrimaryKeyBatch(@Param("list") List<Long> ids);
}
