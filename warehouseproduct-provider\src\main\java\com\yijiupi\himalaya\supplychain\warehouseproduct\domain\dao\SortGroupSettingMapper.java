package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupSettingPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 分区类别
 *
 * <AUTHOR>
 * @date 2018/5/15 18:16
 */
public interface SortGroupSettingMapper {

    /**
     * 根据分区id查询分区下所有类别
     * 
     * @param groupId
     * @return
     */
    List<SortGroupSettingPO> listByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据分区id集合查询分区下所有类别
     * 
     * @param groupIds
     * @return
     */
    List<SortGroupSettingPO> listByGroupIds(@Param("list") List<Long> groupIds);

    /**
     * 批量新增分区类别
     * 
     * @param po
     * @return
     */
    Integer insertBatch(@Param("list") List<SortGroupSettingPO> po);

    /**
     * 根据分区id删除分区下所有类别
     * 
     * @param groupId
     * @return
     */
    Integer deleteByGroupId(@Param("groupId") Long groupId);

    Integer countByGroupId(@Param("groupId") Long groupId);

    /**
     * 分页查询 处理大结果集问题
     * 
     * @param groupId
     * @param index
     * @param totalCount
     * @return
     */
    List<SortGroupSettingPO> pageListByGroupId(@Param("groupId") Long groupId, @Param("index") int index,
        @Param("totalCount") Integer totalCount);

    /**
     * 根据分区id查询分区下所有类别
     * @param groupIds 分区 id
     * @return 查询结果
     */
    PageResult<SortGroupSettingPO> newPageListByGroupId(@Param("groupIds") Collection<Long> groupIds);

    Integer findListByGroupIdCount(@Param("groupId") Long groupId);

    List<SortGroupSettingPO> findListByGroupIdAndBiggerId(@Param("groupId") Long groupId, @Param("id") Long id);

    List<SortGroupSettingPO> pageListByGroupIdPage(@Param("groupId") Long groupId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<SortGroupSettingPO> findListByGroupId(@Param("groupId") Long groupId);
}
