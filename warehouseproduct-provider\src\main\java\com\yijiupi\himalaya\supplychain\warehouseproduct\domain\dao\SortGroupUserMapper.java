package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupUserPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupUserSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupUserSelectSO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分区用户
 *
 * <AUTHOR>
 * @date 2018/5/15 18:16
 */
public interface SortGroupUserMapper {

    /**
     * 根据分区属性获取分区人员列表
     * 
     * @param so
     * @return
     */
    List<SortGroupUserPO> listGroupUserByWarehouseId(@Param("so") SortGroupUserSelectSO so);

    /**
     * 根据分区id查询分区下所有人员
     * 
     * @param so
     * @return
     */
    PageResult<SortGroupUserPO> listGroupUserByGroupId(SortGroupUserSO so);

    /**
     * 根据主键查询分区人员记录
     * 
     * @param id
     * @return
     */
    SortGroupUserPO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 新增分区人员记录
     * 
     * @param po
     * @return
     */
    Integer insert(SortGroupUserPO po);

    /**
     * 批量新增分区人员记录
     * 
     * @param po
     * @return
     */
    Integer insertBatch(@Param("list") List<SortGroupUserPO> po);

    /**
     * 修改分区人员记录
     * 
     * @param po
     * @return
     */
    Integer updateByPrimaryKey(SortGroupUserPO po);

    /**
     * 删除分区人员记录
     * 
     * @param id
     * @return
     */
    Integer deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 根绝分区Id删除所有分区人员记录
     * 
     * @param groupId
     * @return
     */
    Integer deleteByGroupId(@Param("groupId") Long groupId);

    /**
     * 获取分区人员所属的拣货组id集合
     * 
     * @param flag 分区标识 0：分区拣货 1：分区补货
     * @param userId
     * @return
     */
    List<Long> listGroupIdByUserId(@Param("userId") Integer userId, @Param("flag") Byte flag);

}
