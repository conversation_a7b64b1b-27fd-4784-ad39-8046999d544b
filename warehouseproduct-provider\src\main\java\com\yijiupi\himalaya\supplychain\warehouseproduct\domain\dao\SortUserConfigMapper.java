package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortUserConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.SelectSortUser;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.SortPropertyByUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分拣员策略
 *
 * <AUTHOR> 2018/3/19
 */
public interface SortUserConfigMapper {
    /**
     * 添加分拣员策略
     *
     * @param sortUserConfigPOList
     */
    void addSortUserConfigList(@Param("sortUserConfigPOList") List<SortUserConfigPO> sortUserConfigPOList);

    /**
     * 删除分拣员策略
     *
     * @param userId
     */
    void deleteSortUserConfig(@Param("userId") Integer userId, @Param("type") Byte type);

    /**
     * 寻找分拣员已经配置的策略
     *
     * @param userId
     * @return
     */
    List<SortUserConfigPO> findSortUserProperty(@Param("userId") Integer userId, @Param("type") Byte type);

    /**
     * 根据分拣属性(货区，货位，类目)拿到分拣的信息
     *
     * @param sortPropertyByUserDTO
     * @return
     */
    List<SortUserConfigPO>
        findSortPropertyByUser(@Param("sortPropertyByUserDTO") SortPropertyByUserDTO sortPropertyByUserDTO);

    /**
     * 根据查询条件获取已经配置策略的分拣员
     *
     * @param selectSortUser
     * @return
     */
    PageResult<SortUserConfigPO> selectSortUser(@Param("selectSortUser") SelectSortUser selectSortUser,
        @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

}