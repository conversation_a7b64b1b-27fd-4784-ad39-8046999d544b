package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockAgeStrategyConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(StockAgeStrategyConfigPO record);

    int insertSelective(StockAgeStrategyConfigPO record);

    StockAgeStrategyConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockAgeStrategyConfigPO record);

    int updateByPrimaryKey(StockAgeStrategyConfigPO record);

    void batchInsertOrUpdate(@Param("list") List<StockAgeStrategyConfigPO> stockAgeStrategyConfigPO);

    List<StockAgeStrategyConfigPO>
        listStockAgeStrategyConfig(@Param("query") StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO);

    PageResult<StockAgeStrategyConfigDTO> pageListStockAgeStrategyConfig(
        @Param("query") StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    List<StockAgeStrategyConfigPO> findByStrategyId(@Param("strategyId") Long strategyId,
        @Param("orgId") Integer orgId);

    void deleteByIds(@Param("configIds") List<Long> configIds);
}