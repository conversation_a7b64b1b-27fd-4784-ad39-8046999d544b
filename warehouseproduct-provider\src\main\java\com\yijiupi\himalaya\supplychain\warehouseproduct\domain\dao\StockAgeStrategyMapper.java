package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeProductQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyQueryDTO;
import org.apache.ibatis.annotations.Param;

public interface StockAgeStrategyMapper {

    int deleteByPrimaryKey(Long id);

    int insert(StockAgeStrategyPO record);

    int insertSelective(StockAgeStrategyPO record);

    StockAgeStrategyPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockAgeStrategyPO record);

    int updateByPrimaryKey(StockAgeStrategyPO record);

    PageResult<StockAgeStrategyDTO> pageListStockAgeStrategyAll(
        @Param("query") StockAgeStrategyQueryDTO stockAgeStrategyQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    PageResult<StockAgeStrategyDTO> pageListStockAgeStrategy(
        @Param("query") StockAgeStrategyQueryDTO stockAgeStrategyQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    PageResult<StockAgeStrategyDTO> pageListStockAgeProductStrategy(
        @Param("query") StockAgeProductQueryDTO stockAgeProductQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);
}