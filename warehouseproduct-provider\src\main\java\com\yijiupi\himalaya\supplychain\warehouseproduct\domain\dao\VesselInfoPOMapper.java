package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.VesselInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselInfoModifyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselInfoQueryDTO;

public interface VesselInfoPOMapper {

    VesselInfoPO selectByLocationNo(@Param("locationNo") String LocationNo, @Param("warehouseId") Integer warehouseId);

    int insertSelective(VesselInfoPO record);

    int insertSelectiveBatch(List<VesselInfoPO> recordList);

    VesselInfoPO selectById(@Param("id") Long Id);

    List<VesselInfoPO> selectByIds(@Param("ids") List<Long> Ids);

    int updateFreezeStateByIdList(VesselInfoModifyDTO updateDTO);

    int updateBatch(@Param("list") List<VesselInfoPO> record);

    List<VesselInfoPO> selectByConditions(@Param("query") VesselInfoQueryDTO query);

    List<VesselInfoPO> findInfoByIds(@Param("ids") List<Long> Ids);

    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyBatch(@Param("list") List<Long> ids);
}
