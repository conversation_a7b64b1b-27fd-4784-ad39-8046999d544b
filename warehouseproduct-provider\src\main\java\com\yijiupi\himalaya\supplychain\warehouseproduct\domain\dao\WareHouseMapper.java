package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.WareHouseCascadeQuery;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: WareHouseMapper.java
 * @Package
 * <AUTHOR>
 * @date 2018/3/8 17:10
 */
public interface WareHouseMapper {

    WareHousePO selectWareHouseByCityId(@Param("cityId") Integer cityId);

    WareHousePO selectWareHouseByShopId(@Param("shopId") Long shopId);

    int insertWarehousePOBatch(@Param("poList") List<WareHousePO> poList);

    List<Integer> getAllCityByCityDefaultWareHouse(@Param("warehouseId") Integer warehouseId);

    /**
     * @param warehouseId
     * @return List<Integer> cityId 集合
     * @Description: 通过仓库id获取仓库的主要城市
     */
    Integer getMajorCityByWarehouseId(@Param("warehouseId") Integer warehouseId);

    PageResult<WareHouseDTO> findWareHouseList(@Param("dto") WareHouseDTO dto, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    WareHouseDTO findWareHouseById(@Param("warehouseId") Integer warehouseId);

    List<WareHouseDTO> findWareHousesByIds(@Param("list") List<Integer> warehouseIds);

    PageResult<WareHouseDTO> findWareHouseByParentOrgId(@Param("dto") WareHouseCascadeQuery query,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据主键更新
     *
     * @param wareHousePO
     */
    void updateSelectiveByPrimary(WareHousePO wareHousePO);

    /**
     * 获得当前最大主键
     *
     * @return
     */
    Integer getMaxPrimaryKey();

    /**
     * 添加仓库
     *
     * @param wareHousePO
     */
    void insertSelective(WareHousePO wareHousePO);

    /**
     * 仅查询本级服务商仓库信息 weiyangjun
     *
     * @param cityIdList
     * @return
     */
    List<WareHouseDTO> listWarehouseDTOByCityIdList(@Param("cityIdList") List<Integer> cityIdList);

    /**
     * 仅查询本级服务商仓库信息 weiyangjun
     *
     * @param cityIdList
     * @return
     */
    List<WareHouseDTO> listWarehouseDTOByCityId(@Param("cityId") Integer cityId);

    Integer getCountByWarehouseType(@Param("warehouseType") Integer warehouseType);

    Integer getCountByCityId(@Param("cityId") Integer cityId);

    // /**
    // * 定时生成仓库每日库存量
    // */
    // void insertProductstoreDays();

    /**
     * 根据主键更新仓库长和宽
     *
     * @param wareHousePO
     */
    void updateWarehouseAreaById(Integer warehouseId, BigDecimal length, BigDecimal width);

    /**
     * 查询店仓仓库
     */
    List<WareHouseDTO> findDianCangWareHouseList();

    /**
     * 查询仓库状态
     * 
     * @param warehouseId
     * @return
     */
    Integer queryWarehouseStatusByWarehouseId(@Param("warehouseId") Integer warehouseId);
}
