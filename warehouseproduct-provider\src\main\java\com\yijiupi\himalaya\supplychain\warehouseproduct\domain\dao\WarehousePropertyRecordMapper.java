package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousePropertyRecordPO;

import java.util.Date;

/**
 * <AUTHOR> 固定资产
 */
public interface WarehousePropertyRecordMapper {

    /**
     * 根据warehouseId查询日期
     * 
     * @param warehouse
     * @return
     */
    Date queryRecordDateByWarehouse(String warehouse);

    /**
     * 新增
     * 
     * @param convert
     */
    void insert(WarehousePropertyRecordPO convert);
}