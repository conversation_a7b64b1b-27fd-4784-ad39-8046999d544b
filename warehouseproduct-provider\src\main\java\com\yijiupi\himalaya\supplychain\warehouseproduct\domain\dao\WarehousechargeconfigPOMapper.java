package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WarehousechargeconfigPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarehousechargeconfigPO record);

    int insertSelective(WarehousechargeconfigPO record);

    WarehousechargeconfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarehousechargeconfigPO record);

    int updateByPrimaryKey(WarehousechargeconfigPO record);

    /**
     * 多条件查询
     *
     * @param wareHouseChargeQueryDTO
     * @return
     */
    PageResult<WareHouseChargeDTO> listWarehouseChargeConfigSelective(WareHouseChargeQueryDTO wareHouseChargeQueryDTO);

    /**
     * 多条件查询
     *
     * @param wareHouseChargeQueryDTO
     * @return
     */
    Integer countListWarehouseChargeConfigSelective(WareHouseChargeQueryDTO wareHouseChargeQueryDTO);

    /**
     * 多条件查询
     *
     * @param wareHouseChargeQueryDTO
     * @return
     */
    WareHouseChargeDTO getWarehouseChargeConfigSelective(WareHouseChargeQueryDTO wareHouseChargeQueryDTO);

    /**
     * 通过warehouseId更新
     *
     * @param record
     */
    void updateByWarehouseSelective(WarehousechargeconfigPO record);

    /**
     * 获得当前最大主键
     *
     * @return
     */
    Integer getMaxPrimaryKey();

    /**
     * 根据仓库id查询仓库现在是否有库存，结果是库存记录的条数，不是货物数量
     *
     * @param warehouseIdList
     * @return
     */
    List<WareHouseChargeDTO> listStockCount(@Param("warehouseIdList") List<Integer> warehouseIdList);
}
