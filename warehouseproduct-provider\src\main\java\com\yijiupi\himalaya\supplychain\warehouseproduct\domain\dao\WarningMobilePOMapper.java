package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarningStockMobileSO;

public interface WarningMobilePOMapper {
    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    int insert(WarningMobilePO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    int insertSelective(WarningMobilePO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    WarningMobilePO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    int updateByPrimaryKeySelective(WarningMobilePO record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds to the database table warningmobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    int updateByPrimaryKey(WarningMobilePO record);

    /**
     * 查询
     * 
     * @param dto
     * @return: void
     */
    List<WarningMobilePO> findWarningMobileList(@Param("so") WarningStockMobileSO so);

    /**
     * 预警信息查询
     * 
     * @param so
     * @return
     * @return: List<WarningMobileDTO>
     */
    List<WarningMobilePO> findWarningMobileListCategory(@Param("so") WarningStockMobileSO so);

    /**
     * 预警信息查询
     * 
     * @param so
     * @return
     * @return: List<WarningMobileDTO>
     */
    List<WarningMobilePO> findWarningMobileListMobile(@Param("so") WarningStockMobileSO so);

    /**
     * 保存
     * 
     * @param dto
     * @return: void
     */
    void insertBatch(@Param("list") List<WarningMobilePO> list);

    /**
     * 删除
     * 
     * @param dto
     * @return: void
     */
    void deleteByGroupId(@Param("groupId") String groupId);

    /**
     * 获取手机
     * 
     * @param list
     * @return
     * @return: List<String>
     */
    List<String> findMobileByCategoryIds(@Param("so") List<Long> list, @Param("cityId") Integer cityId);

    /**
     * 获取预警设置列表
     * 
     * @return
     */
    List<WarningMobilePO> listWarningMobile(@Param("so") WarningStockMobileSO so);
}
