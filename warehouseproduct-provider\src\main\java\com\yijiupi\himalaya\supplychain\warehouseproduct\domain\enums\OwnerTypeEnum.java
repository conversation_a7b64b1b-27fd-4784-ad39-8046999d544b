package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.enums;

/**
 * <AUTHOR>
 * @date 2019/8/22 14:26
 */
public enum OwnerTypeEnum {
    /**
     * 货主
     */
    货主("货主", 0),
    /**
     * 经销商
     */
    经销商("经销商", 2),
    /**
     * 供应商
     */
    供应商("供应商", 10);

    private Integer type;
    private String typeName;

    OwnerTypeEnum(String typeName, Integer type) {
        this.typeName = typeName;
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

    public static Integer getTypeByName(String typeName) {
        for (OwnerTypeEnum bt : values()) {
            if (bt.typeName.equals(typeName)) {
                return bt.type;
            }
        }
        return null;
    }
}
