package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.enums;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant.ExcelConstant;

/**
 * <AUTHOR>
 * @date 2019/8/22 14:26
 */
public enum PartnerTypeEnum {

    /**
     * 供应商
     */
    SUPPLIER(ExcelConstant.SUPPLIER, "1"),
    /**
     * 经销商
     */
    AGENCY(ExcelConstant.AGENCY, "2"),
    /**
     * 快递公司
     */
    EXPRESS(ExcelConstant.EXPRESS, "3"),
    /**
     * 货主
     */
    OWNER(ExcelConstant.OWNER, "4");

    private String type;
    private String typeName;

    PartnerTypeEnum(String typeName, String type) {
        this.typeName = typeName;
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

    public static String getTypeByName(String typeName) {
        for (PartnerTypeEnum bt : values()) {
            if (bt.typeName.equals(typeName)) {
                return bt.type;
            }
        }
        return null;
    }
}
