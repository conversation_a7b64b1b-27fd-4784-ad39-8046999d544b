package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.listener;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.OwnerConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ShopErpDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SupplierRegisterDTO;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerStateEnum;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;

/**
 * 同步货主信息
 *
 * <AUTHOR>
 * @date 2018/11/16 11:56
 */
@Service
public class OwnerSyncListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OwnerSyncListener.class);

    @Autowired
    private OwnerBL ownerBL;

    @Reference
    private IOrgService iOrgService;

    // /**
    // * 同步知花知果大区
    // *
    // * @param message
    // * @throws Exception
    // */
    // @RabbitListener(queues = "${mq.supplychain.owner.zhzgSync}")
    // public void syncZHZG(Message message){
    // try {
    // String json = new String(message.getBody(), "UTF-8");
    // json = hidePassword(json);
    // LOGGER.info("同步（知花知果）大区信息>>>【mq.supplychain.owner.zhzgSync】" + json);
    // if (StringUtils.isEmpty(json)) {
    // throw new BusinessException("同步（知花知果）大区信息为空！");
    // }
    // SyncRegionChangeDTO supplierMessageDTO = JSON.parseObject(json, SyncRegionChangeDTO.class);
    // OwnerDTO ownerDTO = OwnerConvertor.converToOwnerDTO(supplierMessageDTO);
    // setDefaultAttr(ownerDTO, OwnerTypeConst.知花知果);
    // // 知花知果大区id转换成供应链区域id
    // ownerDTO.setCityId(iOrgService.getOrgIdByFromInfo(supplierMessageDTO.getOrgId(), OrgConstant.ORG_TYPE_EASYGO));
    // ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
    // } catch (Exception e) {
    // LOGGER.error("同步（知花知果）大区失败", e);
    // }
    // }

    /**
     * 供应商同步（易经销）
     */
    @RabbitListener(queues = "${mq.supplychain.provider.easysaleSync}")
    public void syncEasySale(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOGGER.info("[易经销]供应商同步>>>【mq.supplychain.provider.easysaleSync】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new BusinessException("[ERP]供应商同步为空！");
            }
            SupplierRegisterDTO supplierRegisterDTO = JSON.parseObject(json, SupplierRegisterDTO.class);
            OwnerDTO ownerDTO = OwnerConvertor.converToOwnerDTO(supplierRegisterDTO);
            setDefaultAttr(ownerDTO, OwnerTypeConst.供应商);
            ownerBL.insertOrUpdateOwnerByRefPartner(Arrays.asList(ownerDTO));
        } catch (Exception e) {
            LOGGER.error("[易经销]供应商同步失败", e);
        }
    }

    // /**
    // * 供应商同步（ERP）
    // */
    // @RabbitListener(queues = "${mq.supplychain.partner.erpSync}")
    // public void syncERP(Message message) {
    // try {
    // String json = new String(message.getBody(), "UTF-8");
    // LOGGER.info("[ERP]供应商同步>>>【mq.supplychain.partner.erpSync】 {}",
    // MyObjectWriter.toJson(JSON.parse(json)));
    // if (StringUtils.isEmpty(json)) {
    // throw new BusinessException("[ERP]供应商同步为空！");
    // }
    // PartnerSyncByErpDTO partnerSyncByErpDTO = JSON.parseObject(json, PartnerSyncByErpDTO.class);
    // OwnerDTO ownerDTO = OwnerConvertor.converToOwnerDTO(partnerSyncByErpDTO);
    // setDefaultAttr(ownerDTO, OwnerTypeConst.供应商);
    // ownerBL.insertOrUpdateOwnerByRefPartner(Arrays.asList(ownerDTO));
    // } catch (Exception e) {
    // LOGGER.error("[ERP]供应商同步失败", e);
    // }
    // }

    /**
     * 同步易款连锁大区
     */
//    @RabbitListener(queues = "${mq.supplychain.region.easychainSync}")
    public void syncEasychain(Message message) {
//        try {
//            String json = new String(message.getBody(), "UTF-8");
//            json = hidePassword(json);
//            LOGGER.info("同步（易款连锁）大区信息>>>【mq.supplychain.region.easychainSync】" + json);
//            if (StringUtils.isEmpty(json)) {
//                throw new BusinessException("同步（易款连锁）大区信息为空！");
//            }
//            RegionMessageDTO regionMessageDTO = JSON.parseObject(json, RegionMessageDTO.class);
//            OwnerDTO ownerDTO = OwnerConvertor.converToOwnerDTO(regionMessageDTO);
//            setDefaultAttr(ownerDTO, OwnerTypeConst.易款连锁);
//            ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
//        } catch (Exception e) {
//            LOGGER.error("同步（易款连锁）大区失败", e);
//        }
    }

    /**
     * 同步易经销的经销商信息
     *
     * @param message
     * @throws Exception
     */
    @RabbitListener(queues = "${mq.supplychain.owner.easysaleSync}")
    public void syncAgency(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            this.syncAgency(json);
        } catch (Exception e) {
            LOGGER.error("同步（易经销）经销商失败", e);
        }
    }

    public void syncAgency(String json) {
        json = hidePassword(json);
        LOGGER.info("同步（易经销）经销商信息>>>【mq.supplychain.owner.easysaleSync】" + json);
        if (StringUtils.isEmpty(json)) {
            throw new BusinessException("同步（易经销）经销商信息为空！");
        }
        ShopErpDTO shopErpDTO = JSON.parseObject(json, ShopErpDTO.class);
        OwnerDTO ownerDTO = OwnerConvertor.converToOwnerDTO(shopErpDTO);
        setDefaultAttr(ownerDTO, OwnerTypeConst.入驻商);
        ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
        // 融销业务，货主与ERP打通，需要用经销商ID
        if (shopErpDTO.getDealerBaseId() != null && !Objects.equals(shopErpDTO.getId(), shopErpDTO.getDealerBaseId())) {
            ownerDTO.setId(Long.valueOf(shopErpDTO.getDealerBaseId()));
            ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
        }
    }

    /**
     * 设置货主默认属性
     */
    private void setDefaultAttr(OwnerDTO ownerDTO, Integer ownerType) {
        AssertUtils.notNull(ownerDTO, "货主参数不能为空");
        AssertUtils.notNull(ownerDTO.getRefPartnerId(), "货主id不能为空");
        AssertUtils.notNull(ownerDTO.getOwnerName(), "货主名称不能为空");
        if (ownerDTO.getCityId() == null) {
            ownerDTO.setCityId(0);
        }
        if (ownerDTO.getState() == null) {
            ownerDTO.setState(OwnerStateEnum.启用.getType());
        }
        ownerDTO.setOwnerType(ownerType);
    }

    /**
     * 货主信息新增同步（合作商）
     *
     * @param message
     * @throws Exception
     */
    @RabbitListener(queues = "${mq.supplychain.partner.AddPartner}")
    public void add(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            json = hidePassword(json);
            LOGGER.info("货主信息同步新增>>>【mq.supplychain.partner.AddPartner】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new BusinessException("货主信息同步新增为空！");
            }
            OwnerDTO ownerDTO = process(json);
            ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
        } catch (Exception e) {
            LOGGER.error("货主信息同步新增失败", e);
        }
    }

    /**
     * 货主信息修改同步（合作商）
     *
     * @param message
     * @throws Exception
     */
    @RabbitListener(queues = "${mq.supplychain.partner.UpdatePartner}")
    public void update(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            json = hidePassword(json);
            LOGGER.info("货主信息同步修改>>>【mq.supplychain.partner.UpdatePartner】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new BusinessException("货主信息同步修改为空！");
            }
            OwnerDTO ownerDTO = process(json);
            ownerBL.insertOrUpdateOwner(Arrays.asList(ownerDTO));
        } catch (Exception e) {
            LOGGER.error("货主信息同步修改失败", e);
        }
    }

    /**
     * 货主信息启用同步（合作商）
     *
     * @param message
     * @throws Exception
     */
    @RabbitListener(queues = "${mq.supplychain.partner.EnablePartner}")
    public void enable(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            json = hidePassword(json);
            LOGGER.info("货主信息同步启用>>>【mq.supplychain.partner.EnablePartner】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new BusinessException("货主信息同步启用为空！");
            }
            ownerBL.updateStateOwner(Long.valueOf(json), OwnerStateEnum.启用.getType());
        } catch (Exception e) {
            LOGGER.error("货主信息同步启用失败", e);
        }
    }

    /**
     * 货主信息停用同步（合作商）
     *
     * @param message
     * @throws Exception
     */
    @RabbitListener(queues = "${mq.supplychain.partner.DisablePartner}")
    public void disable(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            json = hidePassword(json);
            LOGGER.info("货主信息同步停用>>>【mq.supplychain.partner.DisablePartner】" + json);
            if (StringUtils.isEmpty(json)) {
                throw new BusinessException("货主信息同步停用为空！");
            }
            ownerBL.updateStateOwner(Long.valueOf(json), OwnerStateEnum.停用.getType());
        } catch (Exception e) {
            LOGGER.error("货主信息同步停用失败", e);
        }
    }

    /**
     * 处理json
     *
     * @param json
     * @throws Exception
     */
    private OwnerDTO process(String json) {
        PartnerSyncDTO partnerSyncDTO = JSON.parseObject(json, PartnerSyncDTO.class);
        LOGGER.info("partnerSyncDTO:{}", JSON.toJSONString(partnerSyncDTO));
        OwnerDTO ownerDTO = new OwnerDTO();
        ownerDTO.setId(partnerSyncDTO.getId().longValue());
        ownerDTO.setCityId(partnerSyncDTO.getCityId());
        ownerDTO.setOwnerType(OwnerTypeConst.合作商);
        ownerDTO.setOwnerName(partnerSyncDTO.getName());
        ownerDTO.setState(convertState(partnerSyncDTO.getState()));
        ownerDTO.setAddress(partnerSyncDTO.getDetailAddress());
        ownerDTO.setRefPartnerId(ownerDTO.getId().toString());
        LOGGER.info("ownerDTO:{}", JSON.toJSONString(ownerDTO));
        return ownerDTO;
    }

    private Byte convertState(String state) {
        if (null == state) {
            return null;
        }
        if (Objects.equals(state, OwnerStateEnum.停用.name())) {
            return OwnerStateEnum.停用.getType();
        } else if (Objects.equals(state, OwnerStateEnum.启用.name())) {
            return OwnerStateEnum.启用.getType();
        } else {
            return null;
        }
    }

    private String hidePassword(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return json.replaceAll("password", "yjp");
    }
}
