package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.listener;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductRelationGroupBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductRelationGroupQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * 产品分组关系同步
 */
@Component
public class ProductRelationGroupListener {

    private static final Logger LOG = LoggerFactory.getLogger(ProductRelationGroupListener.class);

    @Autowired
    private ProductRelationGroupBL productRelationGroupBL;

    @Autowired
    private ProductRelationGroupQueryBL productRelationGroupQueryBL;

    @RabbitListener(queues = "${mq.supplychain.productSku.relateGroupAdd}")
    public void relationGroupAdd(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("ProductRelationGroupListener - 产品分组关系新增同步参数:{}", json);
            if (StringUtils.isBlank(json)) {
                return;
            }
            List<ProductRelationGroupAddDTO> groupList = JSON.parseArray(json, ProductRelationGroupAddDTO.class);
            if (CollectionUtils.isEmpty(groupList)) {
                return;
            }
            preconditioning();
            productRelationGroupBL.saveProductRelation(groupList, false);
        } catch (Exception e) {
            LOG.error("产品分组关系同步发生异常，错误信息：" + e.getMessage(), e);
        }
    }

    /**
     * 仓库信息变更初始化产品关联关系
     */
    @RabbitListener(queues = "${mq.supplychain.productSku.warehouseRelateGroupInit}")
    public void relationGroupAddByWarehouseChange(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("relationGroupAddByWarehouseChange - 仓库信息变更触发产品分组关系初始化参数:{}", json);
            if (StringUtils.isBlank(json)) {
                return;
            }
            Warehouse warehouse = JSON.parseObject(json, Warehouse.class);
            if (warehouse == null || warehouse.getId() == null
                || !Objects.equals((int)WarehouseTypeEnum.店仓合一.getType(), warehouse.getWarehouseType())) {
                return;
            }
            // 检查仓库是否需要初始化：
            // 两种方案：
            // 1、仓库消息变更时将[变更前仓库类型]传递过来，对比变[更前仓库类型]与[变更后仓库类型]，[仓库类型]变更前不是[店仓合一]变更后是[店仓合一]则直接初始化
            // 2、仓库类型变更为[店仓合一]根据仓库ID查询产品分组表时候有分组关系存在，如果有分组关系则不用初始化，如果没有则进行初始化
            // 此处使用第 2 种方案
            int countNum = productRelationGroupQueryBL.countRelationByWarehouseId(warehouse.getId());
            LOG.info("relationGroupAddByWarehouseChange - 仓库[{}] 变更后仓库类型[{}] 已经存在 {} 条关联关系！", warehouse.getId(),
                warehouse.getWarehouseType(), countNum);
            if (countNum > 0) {
                return;
            }
            // 仓库切换成[店仓合一]时需要初始化产品分组关系
            // 审计合规化后不需要建立关联关系
            // productRelationGroupBL.initProductGroup(warehouse.getId());
        } catch (Exception e) {
            LOG.error("仓库信息变更触发产品分组关系初始化发生异常，错误信息：" + e.getMessage(), e);
        }
    }

    private void preconditioning() {
        long sleepTime = ThreadLocalRandom.current().nextLong(300, 1500);
        try {
            TimeUnit.MILLISECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
