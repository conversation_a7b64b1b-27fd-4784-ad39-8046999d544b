package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.listener;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceNewBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 溯源码同步
 *
 * <AUTHOR>
 * @Date 2021/8/2 13:59
 */
@Service
public class ProductSourceCodeRecordListener {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSourceCodeRecordListener.class);

    @Autowired
    private ProductSourceNewBL productSourceNewBL;

    /**
     * 同步TMS的溯源码变更记录
     */
    @RabbitListener(queues = "${mq.supplychain.productSourceCode.codeRecordSync}")
    public void codeRecordSync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            String messageId = message.getMessageProperties().getMessageId();
            LOG.info("同步TMS的溯源码变更记录>>>【mq.supplychain.productSourceCode.codeRecordSync】[{}]{}", messageId, json);
            ProductSourceCodeRecordSyncDTO codeRecordSyncDTO =
                JSON.parseObject(json, ProductSourceCodeRecordSyncDTO.class);
            productSourceNewBL.addProductSourceCodeRecordFromTms(codeRecordSyncDTO);
        } catch (Exception e) {
            LOG.error("同步TMS的溯源码变更记录发生异常，错误信息：" + e.getMessage(), e);
        }
    }
}
