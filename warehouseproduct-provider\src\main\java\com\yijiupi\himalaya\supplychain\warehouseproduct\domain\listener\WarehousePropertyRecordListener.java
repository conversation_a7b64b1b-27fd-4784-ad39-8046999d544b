package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.listener;

import com.alibaba.fastjson.JSONObject;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.WarehousePropertyRecordBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehousePropertyRecordDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> 固定资产统计
 */
@Service
public class WarehousePropertyRecordListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehousePropertyRecordListener.class);

    @Autowired
    private WarehousePropertyRecordBL warehousePropertyBL;

    @RabbitListener(queues = "${mq.supplychain.warehouseproduct.warehousePropertySync}")
    public void warehousePropertySync(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOGGER.info("WarehousePropertyListener.relationGroupAdd 固定资产统计 json={}", json);
            if (StringUtils.isBlank(json)) {
                LOGGER.warn("WarehousePropertyListener.relationGroupAdd 固定资产统计 json is null");
                return;
            }
            WarehousePropertyRecordDTO warehousePropertyDTO =
                JSONObject.parseObject(json, WarehousePropertyRecordDTO.class);
            if (ObjectUtils.isEmpty(warehousePropertyDTO)) {
                LOGGER.warn("WarehousePropertyListener.relationGroupAdd 固定资产统计 warehousePropertyDTO is null");
                return;
            }
            if (!"0".equals(warehousePropertyDTO.getType())) {
                warehousePropertyDTO.setType("0");
            }
            if (StringUtils.isBlank(warehousePropertyDTO.getWarehouse())) {
                LOGGER.warn("WarehousePropertyListener.relationGroupAdd 仓库信息不能为空");
                return;
            }
            warehousePropertyBL.warehousePropertySync(warehousePropertyDTO);
        } catch (Exception e) {
            LOGGER.error("WarehousePropertyListener.relationGroupAdd 固定资产统计异常", e);
        }
    }

}
