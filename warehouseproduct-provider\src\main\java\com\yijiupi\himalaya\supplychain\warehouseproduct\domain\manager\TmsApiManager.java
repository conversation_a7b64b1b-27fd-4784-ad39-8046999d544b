package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.dto.RoutingInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.dto.RoutingQueryTraceParam;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.ConfigUtil;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-05-22 11:55
 **/
@Service
public class TmsApiManager {

    @Resource
    private ConfigUtil configUtil;

    @Resource
    private RestTemplate restTemplate;

    private static final String selectByIdsUrl = "apitmssetting/IDeliveryCenterService/selectByIds";

    private static final String routingQueryUrl = "apitmssetting/IRoutingQueryService/queryRoutingInfoByTradeAddressIds";

    public List<Warehouse> getWarehouseList(Set<Integer> warehouseIds) {
        String url = configUtil.tmsGatewayUrl + selectByIdsUrl;
        ParameterizedTypeReference<List<Warehouse>> type = new ParameterizedTypeReference<List<Warehouse>>() {
        };
        String payload = "[" + JSON.toJSONString(warehouseIds) + "]";
        return post(url, payload, type);
    }

    public Map<Long, RoutingInfoDTO> findRoutingByAddressIds(List<Long> addressIds, Integer warehouseId) {
        String url = configUtil.tmsGatewayUrl + routingQueryUrl;
        ParameterizedTypeReference<Map<Long, RoutingInfoDTO>> type = new ParameterizedTypeReference<Map<Long, RoutingInfoDTO>>() {
        };
        return post(url, JSON.toJSONString(RoutingQueryTraceParam.of(addressIds, warehouseId)), type);
    }

    private <T> T post(String url, String payload, ParameterizedTypeReference<T> type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        return restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(payload, headers), type).getBody();
    }

}
