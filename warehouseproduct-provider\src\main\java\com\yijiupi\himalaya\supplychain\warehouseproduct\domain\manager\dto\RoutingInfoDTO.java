package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.dto;

import java.io.Serializable;

public class RoutingInfoDTO implements Serializable {

    private Long id;

    /**
     * 配送站id
     */
    private Integer deliveryCenterId;
    /**
     * 名称
     */
    private String name;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 序号
     */
    private Integer sequence;
    /**
     * 配送司机
     */
    private Integer deliveryUserId;
    /**
     * 配送车辆
     */
    private Long deliveryCarId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDeliveryCenterId() {
        return deliveryCenterId;
    }

    public void setDeliveryCenterId(Integer deliveryCenterId) {
        this.deliveryCenterId = deliveryCenterId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getDeliveryUserId() {
        return deliveryUserId;
    }

    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    public Long getDeliveryCarId() {
        return deliveryCarId;
    }

    public void setDeliveryCarId(Long deliveryCarId) {
        this.deliveryCarId = deliveryCarId;
    }
}
