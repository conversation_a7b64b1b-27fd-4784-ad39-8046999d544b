package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.manager.dto;

import java.io.Serializable;
import java.util.List;

public class RoutingQueryTraceParam implements Serializable {

    /**
     * 配送站id
     */
    private Integer deliveryCenterId;
    /**
     * 商城用户ids
     */
    private List<Long> fromAddressIds;

    public static RoutingQueryTraceParam of(List<Long> fromAddressIds, Integer deliveryCenterId) {
        RoutingQueryTraceParam param = new RoutingQueryTraceParam();
        param.setFromAddressIds(fromAddressIds);
        param.setDeliveryCenterId(deliveryCenterId);
        return param;
    }

    public List<Long> getFromAddressIds() {
        return fromAddressIds;
    }

    public void setFromAddressIds(List<Long> fromAddressIds) {
        this.fromAddressIds = fromAddressIds;
    }

    public Integer getDeliveryCenterId() {
        return deliveryCenterId;
    }

    public void setDeliveryCenterId(Integer deliveryCenterId) {
        this.deliveryCenterId = deliveryCenterId;
    }
}
