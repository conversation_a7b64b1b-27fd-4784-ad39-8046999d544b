package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.mq;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ExceptionOrderSyncDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 异常订单同步
 *
 * <AUTHOR>
 * @date 2019/8/15 11:36
 */
@Component
public class ExceptionOrderSyncMQ {

    private static final Logger LOG = LoggerFactory.getLogger(ExceptionOrderSyncMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.exceptionOrder.orderSync}")
    private String exceptionOrderSyncExchange;

    /**
     * 发送消息
     */
    public void send(ExceptionOrderSyncDTO exceptionOrderSyncDTO) {
        LOG.info("易款异常订单SKU处理成功后同步给OMS:{}", JSON.toJSONString(exceptionOrderSyncDTO));
        rabbitTemplate.convertAndSend(exceptionOrderSyncExchange, null, exceptionOrderSyncDTO);
    }
}
