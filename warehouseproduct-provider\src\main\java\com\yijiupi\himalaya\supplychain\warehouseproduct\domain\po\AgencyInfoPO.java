package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;

/**
 * 经销商信息po
 * 
 * <AUTHOR> 2017/11/26
 */
public class AgencyInfoPO implements Serializable {
    /**
     * 经销商id
     */
    private Long agencyId;
    /**
     * 经销商名称
     */
    private String agencyName;
    /**
     * 经销商库位配置
     */
    private String remark;

    /**
     * 获取 经销商id
     */
    public Long getAgencyId() {
        return this.agencyId;
    }

    /**
     * 设置 经销商id
     */
    public void setAgencyId(Long agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * 获取 经销商名称
     */
    public String getAgencyName() {
        return this.agencyName;
    }

    /**
     * 设置 经销商名称
     */
    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    /**
     * 获取 经销商库位配置
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 经销商库位配置
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
