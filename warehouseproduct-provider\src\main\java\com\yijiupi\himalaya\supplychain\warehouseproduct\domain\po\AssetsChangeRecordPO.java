/*
 * @ClassName AssetsChangeRecordPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-04-16 13:41:54
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

public class AssetsChangeRecordPO implements Serializable {
    /**
     * @Fields id 主键ID
     */
    private Long id;
    /**
     * @Fields assetsinfoId 资产ID（assetsinfo主键）
     */
    private Long assetsinfoId;
    /**
     * @Fields assetsinfoCode 资产编码
     */
    private String assetsinfoCode;
    /**
     * @Fields changePerson 变更人
     */
    private String changePerson;
    /**
     * @Fields changeTime 变更时间
     */
    private Date changeTime;
    /**
     * @Fields changeEvent 变更事件(0:归还, 1: 领用, 2:报废)
     */
    private Integer changeEvent;
    /**
     * @Fields remark 备注
     */
    private String remark;

    /**
     * 获取 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 资产ID（assetsinfo主键）
     */
    public Long getAssetsinfoId() {
        return assetsinfoId;
    }

    /**
     * 设置 资产ID（assetsinfo主键）
     */
    public void setAssetsinfoId(Long assetsinfoId) {
        this.assetsinfoId = assetsinfoId;
    }

    /**
     * 获取 资产编码
     */
    public String getAssetsinfoCode() {
        return assetsinfoCode;
    }

    /**
     * 设置 资产编码
     */
    public void setAssetsinfoCode(String assetsinfoCode) {
        this.assetsinfoCode = assetsinfoCode == null ? null : assetsinfoCode.trim();
    }

    /**
     * 获取 变更人
     */
    public String getChangePerson() {
        return changePerson;
    }

    /**
     * 设置 变更人
     */
    public void setChangePerson(String changePerson) {
        this.changePerson = changePerson == null ? null : changePerson.trim();
    }

    /**
     * 获取 变更时间
     */
    public Date getChangeTime() {
        return changeTime;
    }

    /**
     * 设置 变更时间
     */
    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    /**
     * 获取 变更事件(0:归还, 1: 领用, 2:报废)
     */
    public Integer getChangeEvent() {
        return changeEvent;
    }

    /**
     * 设置 变更事件(0:归还, 1: 领用, 2:报废)
     */
    public void setChangeEvent(Integer changeEvent) {
        this.changeEvent = changeEvent;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}