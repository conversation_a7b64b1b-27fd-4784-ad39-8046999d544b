/*
 * @ClassName AssetsInfoPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-04-16 12:02:28
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class AssetsInfoPO implements Serializable {
    /**
     * @Fields id 主键ID
     */
    private Long id;
    /**
     * @Fields warehouseId 资产所属仓库ID
     */
    private Integer warehouseId;
    /**
     * @Fields code 资产编码
     */
    private String code;
    /**
     * @Fields name 资产名称
     */
    private String name;
    /**
     * @Fields state 状态(0:在库, 1: 领用, 2:报废)
     */
    private Integer state;
    /**
     * @Fields retrieve 是否回收(0:回收, 1:不回收)
     */
    private Integer retrieve;
    /**
     * @Fields specifications 资产规格
     */
    private String specifications;
    /**
     * @Fields assetsType 资产类型
     */
    private String assetsType;
    /**
     * @Fields description 描述
     */
    private String description;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 容器长,单位CM
     */
    private BigDecimal length;

    /**
     * 容器宽,单位CM
     */
    private BigDecimal width;

    /**
     * 容器高,单位CM
     */
    private BigDecimal height;

    /**
     * 容器体积(按长方体体积),单位立方CM
     */
    private BigDecimal volume;

    /**
     * 获取 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 资产所属仓库ID
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 资产所属仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 资产编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置 资产编码
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * 获取 资产名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置 资产名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取 状态(0:在库, 1: 领用, 2:报废)
     */
    public Integer getState() {
        return state;
    }

    /**
     * 设置 状态(0:在库, 1: 领用, 2:报废)
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取 是否回收(0:回收, 1:不回收)
     */
    public Integer getRetrieve() {
        return retrieve;
    }

    /**
     * 设置 是否回收(0:回收, 1:不回收)
     */
    public void setRetrieve(Integer retrieve) {
        this.retrieve = retrieve;
    }

    /**
     * 获取 资产规格
     */
    public String getSpecifications() {
        return specifications;
    }

    /**
     * 设置 资产规格
     */
    public void setSpecifications(String specifications) {
        this.specifications = specifications == null ? null : specifications.trim();
    }

    /**
     * 获取 资产类型
     */
    public String getAssetsType() {
        return assetsType;
    }

    /**
     * 设置 资产类型
     */
    public void setAssetsType(String assetsType) {
        this.assetsType = assetsType;
    }

    /**
     * 获取 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置 描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
}