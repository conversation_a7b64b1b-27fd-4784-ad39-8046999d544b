package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.math.BigDecimal;

/**
 * 配送系数po
 * 
 * <AUTHOR> 2018/1/2
 */
public class DistributionPercentPO {
    private Long id;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 业务Id，这里是产品SKUID
     */
    private Long businessId;
    /**
     * 业务类型,这里是:修改配送系数
     */
    private String businessType;
    /**
     * 描述
     */
    private String description;
    /**
     * json格式
     */
    private String oldContext;
    /**
     * 操作人
     */
    private Integer userId;
    /**
     * skuid
     */
    private Long productSkuId;
    /**
     * 配送系数-工资
     */
    private BigDecimal distributionPercentForAmount;

    /**
     * 获取 业务Id，这里是产品SKUID
     *
     * @return businessId 业务Id，这里是产品SKUID
     */
    public Long getBusinessId() {
        return this.businessId;
    }

    /**
     * 设置 业务Id，这里是产品SKUID
     *
     * @param businessId 业务Id，这里是产品SKUID
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * 获取 业务类型这里是:修改配送系数
     *
     * @return businessType 业务类型这里是:修改配送系数
     */
    public String getBusinessType() {
        return this.businessType;
    }

    /**
     * 设置 业务类型这里是:修改配送系数
     *
     * @param businessType 业务类型这里是:修改配送系数
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 描述
     *
     * @return description 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * 设置 描述
     *
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取 json格式
     *
     * @return oldContext json格式
     */
    public String getOldContext() {
        return this.oldContext;
    }

    /**
     * 设置 json格式
     *
     * @param oldContext json格式
     */
    public void setOldContext(String oldContext) {
        this.oldContext = oldContext;
    }

    /**
     * 获取 操作人
     *
     * @return userId 操作人
     */
    public Integer getUserId() {
        return this.userId;
    }

    /**
     * 设置 操作人
     *
     * @param userId 操作人
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取 配送系数-工资
     *
     * @return distributionPercentForAmount 配送系数-工资
     */
    public BigDecimal getDistributionPercentForAmount() {
        return this.distributionPercentForAmount;
    }

    /**
     * 设置 配送系数-工资
     *
     * @param distributionPercentForAmount 配送系数-工资
     */
    public void setDistributionPercentForAmount(BigDecimal distributionPercentForAmount) {
        this.distributionPercentForAmount = distributionPercentForAmount;
    }

    /**
     * 获取 skuid
     *
     * @return productSkuId skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuid
     *
     * @param productSkuId skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
