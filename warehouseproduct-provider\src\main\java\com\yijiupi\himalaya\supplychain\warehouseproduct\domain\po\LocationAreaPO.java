package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class LocationAreaPO implements Serializable {
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column locationarea.Id
     *
     * @mbggenerated
     */
    private Long id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * locationarea.Warehouse_Id
     *
     * @mbggenerated
     */
    private Integer warehouse_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column locationarea.City_Id
     *
     * @mbggenerated
     */
    private Integer city_Id;

    private String name;

    /** 宽 */
    private BigDecimal width;
    /** 高 */
    private BigDecimal height;
    /** 坐标：X */
    private BigDecimal coordinateX;
    /** 坐标：Y */
    private BigDecimal coordinateY;
    /** 货架层数 */
    private Integer layer;

    private List<Long> excludeIds;

    public List<Long> getExcludeIds() {
        return excludeIds;
    }

    public void setExcludeIds(List<Long> excludeIds) {
        this.excludeIds = excludeIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column locationarea.Area
     *
     * @mbggenerated
     */
    private String area;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column locationarea.Remo
     *
     * @mbggenerated
     */
    private String remo;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * locationarea.CreateTime
     *
     * @mbggenerated
     */
    private Date createTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * locationarea.CreateUserId
     *
     * @mbggenerated
     */
    private Integer createUserId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * locationarea.LastUpdateTime
     *
     * @mbggenerated
     */
    private Date lastUpdateTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * locationarea.LastUpdateUserId
     *
     * @mbggenerated
     */
    private Integer lastUpdateUserId;
    /**
     * 类型，0：货位，1：货区',
     */
    private Byte category;
    /**
     * 货区/货区类型，
     */
    private Byte subcategory;

    private Integer locationCapacity;

    /**
     * 状态
     */
    private Byte state;

    /**
     * 货位子类型, 非标准模型
     */
    private List<Integer> subCategories;

    /**
     * 业务类型，1：赠品区
     */
    private Byte businessType;

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * locationarea.Id
     *
     * @return the value of locationarea.Id
     * @mbggenerated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column locationarea.Id
     *
     * @param id the value for locationarea.Id
     * @mbggenerated
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouse_Id() {
        return warehouse_Id;
    }

    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    public Integer getCity_Id() {
        return city_Id;
    }

    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    /**
     * 获取 类型，0：货位，1：货区',
     */
    public Byte getCategory() {
        return this.category;
    }

    /**
     * 设置 类型，0：货位，1：货区',
     */
    public void setCategory(Byte category) {
        this.category = category;
    }

    /**
     * 获取 货区/货区类型，
     */
    public Byte getSubcategory() {
        return this.subcategory;
    }

    /**
     * 设置 货区/货区类型，
     */
    public void setSubcategory(Byte subcategory) {
        this.subcategory = subcategory;
    }

    public Integer getLocationCapacity() {
        return this.locationCapacity;
    }

    public void setLocationCapacity(Integer locationCapacity) {
        this.locationCapacity = locationCapacity;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getCoordinateX() {
        return coordinateX;
    }

    public void setCoordinateX(BigDecimal coordinateX) {
        this.coordinateX = coordinateX;
    }

    public BigDecimal getCoordinateY() {
        return coordinateY;
    }

    public void setCoordinateY(BigDecimal coordinateY) {
        this.coordinateY = coordinateY;
    }

    public Integer getLayer() {
        return layer;
    }

    public void setLayer(Integer layer) {
        this.layer = layer;
    }

    public List<Integer> getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(List<Integer> subCategories) {
        this.subCategories = subCategories;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }
}
