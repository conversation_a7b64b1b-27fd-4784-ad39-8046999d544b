package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LocationPO implements Serializable {
    private Long id;
    private Integer warehouse_Id;
    private Integer city_Id;
    private String name;
    private String area;
    private String pallets;
    private String productLocation;
    private String roadWay;
    private String remo;
    private Date createTime;
    private Integer createUserId;
    private Date lastUpdateTime;
    private Integer lastUpdateUserId;
    private Long area_Id;
    private Integer sequence;
    private Byte category;
    private Byte subcategory;
    private Integer locationCapacity;
    /**
     * 是否混放
     */
    private Byte isChaosPut;
    /**
     * 是否混批
     */
    private Byte isChaosBatch;

    /**
     * 货位分级属性，0：未设置，1：A，2：B，3：C
     */
    private Integer locationGrade;

    /**
     * 状态：0.停用 1.启用
     */
    private Byte state;

    /** 宽 */
    private BigDecimal width;
    /** 高 */
    private BigDecimal height;
    /** 坐标：X */
    private BigDecimal coordinateX;
    /** 坐标：Y */
    private BigDecimal coordinateY;
    /** 货架层数 */
    private Integer layer;
    /**
     * 快递直发集货位 0：否 1：是
     */
    private Byte express;

    /**
     * 货位类型
     */
    private Byte locationSubcategory;

    /**
     * 托盘数量
     */
    private Integer palletCount;

    /**
     * 巷道id
     */
    private Long aisleId;

    /**
     * 巷道编号
     */
    private String aisleNo;

    /**
     * 业务类型，1：赠品区
     */
    private Byte businessType;

    public Byte getExpress() {
        return express;
    }

    public void setExpress(Byte express) {
        this.express = express;
    }

    public Integer getLocationGrade() {
        return locationGrade;
    }

    public void setLocationGrade(Integer locationGrade) {
        this.locationGrade = locationGrade;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }

    public LocationPO(LoactionDTO dto) {
        this.area = dto.getArea();
        this.area_Id = dto.getArea_Id();
        this.sequence = dto.getSequence();
        this.city_Id = dto.getCityId();
        this.id = dto.getId();
        this.name = dto.getName();
        this.pallets = dto.getPallets();
        this.productLocation = dto.getProductlocation();
        this.roadWay = dto.getRoadway();
        this.warehouse_Id = dto.getWarehouseId();
        this.subcategory = dto.getSubcategory();
        this.category = dto.getCategory();
        this.isChaosBatch = dto.getIsChaosBatch();
        this.isChaosPut = dto.getIsChaosPut();
        this.locationCapacity = dto.getLocationCapacity();
        this.express = dto.getExpress();
        this.aisleId = dto.getAisleId();
        this.aisleNo = dto.getAisleNo();
        this.businessType = dto.getBusinessType();
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public LoactionDTO convertToModel() {
        LoactionDTO dto = new LoactionDTO();
        dto.setId(this.id);
        dto.setCityId(this.city_Id);
        dto.setName(this.name);
        dto.setPallets(this.pallets);
        dto.setProductlocation(this.productLocation);
        dto.setRoadway(this.roadWay);
        dto.setWarehouseId(this.warehouse_Id);
        dto.setArea(this.area);
        dto.setArea_Id(this.area_Id);
        dto.setSequence(this.sequence);
        return dto;
    }

    public LocationPO() {
        super();
    }

    public static List<LoactionDTO> convertToModelList(List<LocationPO> poList) {
        List<LoactionDTO> modelList = new ArrayList<>();
        if (poList == null || poList.isEmpty()) {
            return new ArrayList<>();
        }
        for (LocationPO po : poList) {
            modelList.add(po.convertToModel());
        }
        return modelList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouse_Id() {
        return warehouse_Id;
    }

    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    public Integer getCity_Id() {
        return city_Id;
    }

    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getPallets() {
        return pallets;
    }

    public void setPallets(String pallets) {
        this.pallets = pallets;
    }

    public String getProductLocation() {
        return productLocation;
    }

    public void setProductLocation(String productLocation) {
        this.productLocation = productLocation;
    }

    public String getRoadWay() {
        return roadWay;
    }

    public void setRoadWay(String roadWay) {
        this.roadWay = roadWay;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getArea_Id() {
        return area_Id;
    }

    public void setArea_Id(Long area_Id) {
        this.area_Id = area_Id;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public Byte getSubcategory() {
        return this.subcategory;
    }

    public void setSubcategory(Byte subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 是否混放
     */
    public Byte getIsChaosPut() {
        return this.isChaosPut;
    }

    /**
     * 设置 是否混放
     */
    public void setIsChaosPut(Byte isChaosPut) {
        this.isChaosPut = isChaosPut;
    }

    /**
     * 获取 是否混批
     */
    public Byte getIsChaosBatch() {
        return this.isChaosBatch;
    }

    /**
     * 设置 是否混批
     */
    public void setIsChaosBatch(Byte isChaosBatch) {
        this.isChaosBatch = isChaosBatch;
    }

    public Integer getLocationCapacity() {
        return this.locationCapacity;
    }

    public void setLocationCapacity(Integer locationCapacity) {
        this.locationCapacity = locationCapacity;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getCoordinateX() {
        return coordinateX;
    }

    public void setCoordinateX(BigDecimal coordinateX) {
        this.coordinateX = coordinateX;
    }

    public BigDecimal getCoordinateY() {
        return coordinateY;
    }

    public void setCoordinateY(BigDecimal coordinateY) {
        this.coordinateY = coordinateY;
    }

    public Integer getLayer() {
        return layer;
    }

    public void setLayer(Integer layer) {
        this.layer = layer;
    }

    public Byte getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Byte locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public Integer getPalletCount() {
        return palletCount;
    }

    public void setPalletCount(Integer palletCount) {
        this.palletCount = palletCount;
    }

    public Long getAisleId() {
        return aisleId;
    }

    public void setAisleId(Long aisleId) {
        this.aisleId = aisleId;
    }

    public String getAisleNo() {
        return aisleNo;
    }

    public void setAisleNo(String aisleNo) {
        this.aisleNo = aisleNo;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }
}
