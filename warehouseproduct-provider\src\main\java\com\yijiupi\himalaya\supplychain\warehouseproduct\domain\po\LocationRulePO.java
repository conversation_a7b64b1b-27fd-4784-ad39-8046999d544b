package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;

/**
 * <AUTHOR> 出库位规则
 */
public class LocationRulePO implements Serializable {
    private static final long serialVersionUID = -2632249286159350827L;

    private Long id;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 规则id
     */
    private Integer ruleType;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则id
     */
    private String ruleId;
    /**
     * 出库位id
     */
    private Long locationId;
    /**
     * 出库位名称
     */
    private String locationName;
    /**
     * 扩展字段
     */
    private String extInfo;

    private Integer createUserId;
    private Integer lastUpdateUserId;

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
