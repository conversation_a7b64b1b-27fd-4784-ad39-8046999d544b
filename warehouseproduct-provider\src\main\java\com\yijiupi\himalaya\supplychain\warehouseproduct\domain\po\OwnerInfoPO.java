package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

/**
 * 货主信息po
 * 
 * <AUTHOR> 2018/3/2
 */
public class OwnerInfoPO {
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 销售模式
     */
    private Integer saleModel;
    /**
     * 公司名称
     */
    private Long CompanyId;
    /**
     * skuId
     */
    private Long productSkuId;

    private String ownerName;

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取 产品名称
     *
     * @return productName 产品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 产品名称
     *
     * @param productName 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 销售模式
     *
     * @return saleModel 销售模式
     */
    public Integer getSaleModel() {
        return this.saleModel;
    }

    /**
     * 设置 销售模式
     *
     * @param saleModel 销售模式
     */
    public void setSaleModel(Integer saleModel) {
        this.saleModel = saleModel;
    }

    /**
     * 获取 公司名称
     *
     * @return CompanyId 公司名称
     */
    public Long getCompanyId() {
        return this.CompanyId;
    }

    /**
     * 设置 公司名称
     *
     * @param CompanyId 公司名称
     */
    public void setCompanyId(Long CompanyId) {
        this.CompanyId = CompanyId;
    }

    /**
     * 获取 skuId
     *
     * @return productSkuId skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuId
     *
     * @param productSkuId skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }
}
