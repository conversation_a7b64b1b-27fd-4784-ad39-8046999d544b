package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

/**
 * 货主表
 */
public class OwnerPO {

    /**
     * 货主id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 货主类型 0：酒批 1：合作商 2：入驻商
     */
    private Integer ownerType;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 状态 0停用, 1启用
     */
    private Byte state;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 货主详细地址
     */
    private String address;

    /**
     * 负责人手机号
     */
    private String mobileNo;

    /**
     * 负责人名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后变更时间
     */
    private Date lastUpdateTime;

    /**
     * 编码
     */
    private String ownerNo;

    /**
     * 状态 0-非默认货主, 1-默认货主
     */
    private Byte isDefault;

    /** 创建人id */
    private Long createUserId;

    /** 创建人id */
    private Long lastUpdateUserId;

    /** 关联外部供应商id */
    private String refPartnerId;

    /**
     * 服务商id
     */
    private Long companyId;

    /**
     * 是否可退货 0：否 1：是
     */
    private Byte isReturn;

    public Byte getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Byte isReturn) {
        this.isReturn = isReturn;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Byte getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Byte isDefault) {
        this.isDefault = isDefault;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getOwnerNo() {
        return ownerNo;
    }

    public void setOwnerNo(String ownerNo) {
        this.ownerNo = ownerNo;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Long lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public String getRefPartnerId() {
        return refPartnerId;
    }

    public void setRefPartnerId(String refPartnerId) {
        this.refPartnerId = refPartnerId;
    }
}