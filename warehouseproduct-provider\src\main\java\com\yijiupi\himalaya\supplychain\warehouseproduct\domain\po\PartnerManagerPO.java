package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-08-20
 */
public class PartnerManagerPO {
    /**
     * 伙伴ID
     */
    private Long id;

    /**
     * 名称
     */
    private String partnerName;
    /**
     * 中文缩写
     */
    private String shortCn;

    /**
     * 英文缩写
     */
    private String shortEn;

    /**
     * 编码
     */
    private String partnerNo;

    /**
     * 地址
     */
    private String address;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态 0停用、1启用
     */
    private Byte status;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 注册日期
     */
    private Date createTime;

    /**
     * 最后更新人
     */
    private Long lastUpdateUser;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 所属公司
     */
    private Long companyId;

    /**
     * 所属仓库
     */
    private Long warehouseId;

    /**
     * 路线
     */
    private String line;

    /**
     * 类型 1供应商，2经销商，3快递公司，4货主
     */
    private Byte type;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 关联外部供应商id
     */
    private String refPartnerId;

    public String getRefPartnerId() {
        return refPartnerId;
    }

    public void setRefPartnerId(String refPartnerId) {
        this.refPartnerId = refPartnerId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取id
     *
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     *
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取partnerName
     *
     * @return partnerName
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     * 设置partnerName
     *
     * @param partnerName
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * 获取shortCn
     *
     * @return shortCn
     */
    public String getShortCn() {
        return shortCn;
    }

    /**
     * 设置shortCn
     *
     * @param shortCn
     */
    public void setShortCn(String shortCn) {
        this.shortCn = shortCn;
    }

    /**
     * 获取shortEn
     *
     * @return shortEn
     */
    public String getShortEn() {
        return shortEn;
    }

    /**
     * 设置shortEn
     *
     * @param shortEn
     */
    public void setShortEn(String shortEn) {
        this.shortEn = shortEn;
    }

    /**
     * 获取partnerNo
     *
     * @return partnerNo
     */
    public String getPartnerNo() {
        return partnerNo;
    }

    /**
     * 设置partnerNo
     *
     * @param partnerNo
     */
    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    /**
     * 获取address
     *
     * @return address
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置address
     *
     * @param address
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 获取linkMan
     *
     * @return linkMan
     */
    public String getLinkMan() {
        return linkMan;
    }

    /**
     * 设置linkMan
     *
     * @param linkMan
     */
    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    /**
     * 获取phone
     *
     * @return phone
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 设置phone
     *
     * @param phone
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 获取email
     *
     * @return email
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置email
     *
     * @param email
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 获取status
     *
     * @return status
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置status
     *
     * @param status
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取createUser
     *
     * @return createUser
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置createUser
     *
     * @param createUser
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取createTime
     *
     * @return createTime
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置createTime
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取lastUpdateUser
     *
     * @return lastUpdateUser
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置lastUpdateUser
     *
     * @param lastUpdateUser
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取lastUpdateTime
     *
     * @return lastUpdateTime
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置lastUpdateTime
     *
     * @param lastUpdateTime
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取companyId
     *
     * @return companyId
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * 设置companyId
     *
     * @param companyId
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取warehouseId
     *
     * @return warehouseId
     */
    public Long getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置warehouseId
     *
     * @param warehouseId
     */
    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取line
     *
     * @return line
     */
    public String getLine() {
        return line;
    }

    /**
     * 设置line
     *
     * @param line
     */
    public void setLine(String line) {
        this.line = line;
    }

    /**
     * 获取type
     *
     * @return type
     */
    public Byte getType() {
        return type;
    }

    /**
     * 设置type
     *
     * @param type
     */
    public void setType(Byte type) {
        this.type = type;
    }
}