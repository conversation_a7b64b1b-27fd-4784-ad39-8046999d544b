package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 通道关联详情
 *
 * <AUTHOR>
 * @date 2018/8/8 14:59
 */
public class PassageItemPO implements Serializable {

    /**
     * 通道详情Id
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 通道ID
     */
    private Long passageId;

    /**
     * 关联项类型（1 类目，2 货位）
     */
    private Byte relateType;

    /**
     * 关联项编码
     */
    private String relateId;

    /**
     * 关联项名称
     */
    private String relateName;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getPassageId() {
        return passageId;
    }

    public void setPassageId(Long passageId) {
        this.passageId = passageId;
    }

    public Byte getRelateType() {
        return relateType;
    }

    public void setRelateType(Byte relateType) {
        this.relateType = relateType;
    }

    public String getRelateId() {
        return relateId;
    }

    public void setRelateId(String relateId) {
        this.relateId = relateId;
    }

    public String getRelateName() {
        return relateName;
    }

    public void setRelateName(String relateName) {
        this.relateName = relateName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
