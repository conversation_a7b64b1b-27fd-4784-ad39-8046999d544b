package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.AgvPickConfigTypeConstants;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PassageConstants;

public class PassagePO implements Serializable {

    private static final long serialVersionUID = -8784968451735904044L;

    /**
     * 通道Id
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 通道名称
     */
    private String passageName;

    /**
     * 通道类型（1 类目通道，2 货位通道）
     */
    private Byte passageType;

    /**
     * 通道编码
     */
    private String passageCode;

    /**
     * 拣货方式（1 按订单拣货， 2 按产品拣货）
     */
    private Byte pickingType;

    /**
     * 拣货分组策略（1 货区， 2 货位， 3 类目）
     */
    private Byte pickingGroupStrategy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 更新人
     */
    private String lastUpdateUser;

    /**
     * 通道配置(0:不开启，1:播种墙播种，2:分区分单播种)
     */
    private Byte sowType;

    /**
     * 大件、小件通道
     *
     * @see PassageConstants
     */
    private Byte packageType;
    /**
     * 状态：
     *
     * @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    private Byte state;

    /**
     * 通道明细
     */
    private List<PassageItemPO> passageItemPOS;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column passage.isRobotPick
     *
     * @mbggenerated Mon Oct 17 10:34:00 CST 2022
     */
    private Byte isRobotPick;
    /**
     * 开启agv拣货：0 关闭；1 开启大华agv
     *
     * @see AgvPickConfigTypeConstants
     */
    private Byte agvPickConfigType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getPassageName() {
        return passageName;
    }

    public void setPassageName(String passageName) {
        this.passageName = passageName;
    }

    public Byte getPassageType() {
        return passageType;
    }

    public void setPassageType(Byte passageType) {
        this.passageType = passageType;
    }

    public Byte getPickingType() {
        return pickingType;
    }

    public void setPickingType(Byte pickingType) {
        this.pickingType = pickingType;
    }

    public Byte getPickingGroupStrategy() {
        return pickingGroupStrategy;
    }

    public void setPickingGroupStrategy(Byte pickingGroupStrategy) {
        this.pickingGroupStrategy = pickingGroupStrategy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Byte getSowType() {
        return sowType;
    }

    public void setSowType(Byte sowType) {
        this.sowType = sowType;
    }

    public List<PassageItemPO> getPassageItemPOS() {
        return passageItemPOS;
    }

    public void setPassageItemPOS(List<PassageItemPO> passageItemPOS) {
        this.passageItemPOS = passageItemPOS;
    }

    /**
     * 获取 This field was generated by MyBatis Generator. This field corresponds to the database column
     * passage.isRobotPick @mbggenerated Mon Oct 17 10:34:00 CST 2022
     *
     * @return isRobotPick This field was generated by MyBatis Generator. This field corresponds to the database column
     * passage.isRobotPick @mbggenerated Mon Oct 17 10:34:00 CST 2022
     */
    public Byte getIsRobotPick() {
        return this.isRobotPick;
    }

    /**
     * 设置 This field was generated by MyBatis Generator. This field corresponds to the database column
     * passage.isRobotPick @mbggenerated Mon Oct 17 10:34:00 CST 2022
     *
     * @param isRobotPick This field was generated by MyBatis Generator. This field corresponds to the database column
     *            passage.isRobotPick @mbggenerated Mon Oct 17 10:34:00 CST 2022
     */
    public void setIsRobotPick(Byte isRobotPick) {
        this.isRobotPick = isRobotPick;
    }

    public String getPassageCode() {
        return passageCode;
    }

    public void setPassageCode(String passageCode) {
        this.passageCode = passageCode;
    }

    /**
     * 获取 大件、小件通道 @see PassageConstants
     *
     * @return packageType 大件、小件通道 @see PassageConstants
     */
    public Byte getPackageType() {
        return this.packageType;
    }

    /**
     * 设置 大件、小件通道 @see PassageConstants
     *
     * @param packageType 大件、小件通道 @see PassageConstants
     */
    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }


    /**
     * 获取 开启agv拣货：0 关闭；1 开启大华agv           @see AgvPickConfigTypeConstants
     *
     * @return agvPickConfigType 开启agv拣货：0 关闭；1 开启大华agv           @see AgvPickConfigTypeConstants
     */
    public Byte getAgvPickConfigType() {
        return this.agvPickConfigType;
    }

    /**
     * 设置 开启agv拣货：0 关闭；1 开启大华agv           @see AgvPickConfigTypeConstants
     *
     * @param agvPickConfigType 开启agv拣货：0 关闭；1 开启大华agv           @see AgvPickConfigTypeConstants
     */
    public void setAgvPickConfigType(Byte agvPickConfigType) {
        this.agvPickConfigType = agvPickConfigType;
    }

    /**
     * 获取 状态： @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @return state 状态： @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态： @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @param state 状态： @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public void setState(Byte state) {
        this.state = state;
    }
}