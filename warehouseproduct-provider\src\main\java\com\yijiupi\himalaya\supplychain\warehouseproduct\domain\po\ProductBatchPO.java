/*
 * @ClassName ProductBatchPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-03-05 14:22:11
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

public class ProductBatchPO {
    /**
     * @Fields id
     */
    private Long id;
    /**
     * @Fields orderNo 订单号
     */
    private Long orderNo;
    /**
     * @Fields brandName 品牌名称
     */
    private String brandName;
    /**
     * @Fields productName 商品名称
     */
    private String productName;
    /**
     * @Fields specName 规格名称
     */
    private String specName;
    /**
     * @Fields batcheId 批次号
     */
    private String batcheId;
    /**
     * @Fields manufactureTime 生产日期
     */
    private Date manufactureTime;
    /**
     * @Fields productSkuId 产品skuid
     */
    private Long productSkuId;
    /**
     * @Fields productSpecificationId 产品规格id
     */
    private Long productSpecificationId;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后修改人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后修改时间
     */
    private Date lastUpdateTime;
    /**
     * @Fields status 是否删除 0=不删除 1=删除
     */
    private Byte status;

    /**
     * 获取
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 订单号
     */
    public Long getOrderNo() {
        return orderNo;
    }

    /**
     * 设置 订单号
     */
    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 品牌名称
     */
    public String getBrandName() {
        return brandName;
    }

    /**
     * 设置 品牌名称
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName == null ? null : brandName.trim();
    }

    /**
     * 获取 商品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 商品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取 规格名称
     */
    public String getSpecName() {
        return specName;
    }

    /**
     * 设置 规格名称
     */
    public void setSpecName(String specName) {
        this.specName = specName == null ? null : specName.trim();
    }

    /**
     * 获取 批次号
     */
    public String getBatcheId() {
        return batcheId;
    }

    /**
     * 设置 批次号
     */
    public void setBatcheId(String batcheId) {
        this.batcheId = batcheId == null ? null : batcheId.trim();
    }

    /**
     * 获取 生产日期
     */
    public Date getManufactureTime() {
        return manufactureTime;
    }

    /**
     * 设置 生产日期
     */
    public void setManufactureTime(Date manufactureTime) {
        this.manufactureTime = manufactureTime;
    }

    /**
     * 获取 产品skuid
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品规格id
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后修改人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后修改人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后修改时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后修改时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 是否删除 0=不删除 1=删除
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 是否删除 0=不删除 1=删除
     */
    public void setStatus(Byte status) {
        this.status = status;
    }
}
