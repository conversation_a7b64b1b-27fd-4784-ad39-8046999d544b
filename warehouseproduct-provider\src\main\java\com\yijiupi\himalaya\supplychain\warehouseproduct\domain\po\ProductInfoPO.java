package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

public class ProductInfoPO {

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productinfo.Id
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Long id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productinfo.Brand
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String brand;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.StatisticsCategoryName
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String statisticsCategoryName;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.SeriesName
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String seriesName;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ProductName
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String productName;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.GeneralName
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String generalName;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.DefaultImageFile_Id
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Integer defaultImageFile_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.StorageMethod
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String storageMethod;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.OriginalPlace
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String originalPlace;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productinfo.Status
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte status;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.BottleCode
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String bottleCode;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ProductCode
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String productCode;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.packagingCode
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private String packagingCode;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ProductInfoType
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte productInfoType;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ProductStatisticsClass
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Long productStatisticsClass;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.SecondStatisticsClass
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Long secondStatisticsClass;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.MonthOfShelfLife
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Integer monthOfShelfLife;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ShelfLifeUnit
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte shelfLifeUnit;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.hasBottleCode
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte hasBottleCode;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.hasBoxCode
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte hasBoxCode;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productinfo.ShopId
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Long shopId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.ShelfLifeLongTime
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte shelfLifeLongTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.CreateTime
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Date createTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.CreateUser_Id
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Integer createUser_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.LastUpdateTime
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Date lastUpdateTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.LastUpdateUser_Id
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Integer lastUpdateUser_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productinfo.PackageType
     * 
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    private Byte packageType;

    /**
     * 是否可以加工（0：不可以 1：可以）
     */
    private Byte process;

    public Byte getProcess() {
        return process;
    }

    public void setProcess(Byte process) {
        this.process = process;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.Id
     * 
     * @return the value of productinfo.Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column productinfo.Id
     * 
     * @param id the value for productinfo.Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.Brand
     * 
     * @return the value of productinfo.Brand
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getBrand() {
        return brand;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.Brand
     * 
     * @param brand the value for productinfo.Brand
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.StatisticsCategoryName
     * 
     * @return the value of productinfo.StatisticsCategoryName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getStatisticsCategoryName() {
        return statisticsCategoryName;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.StatisticsCategoryName
     * 
     * @param statisticsCategoryName the value for productinfo.StatisticsCategoryName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setStatisticsCategoryName(String statisticsCategoryName) {
        this.statisticsCategoryName = statisticsCategoryName == null ? null : statisticsCategoryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.SeriesName
     * 
     * @return the value of productinfo.SeriesName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getSeriesName() {
        return seriesName;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.SeriesName
     * 
     * @param seriesName the value for productinfo.SeriesName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName == null ? null : seriesName.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ProductName
     * 
     * @return the value of productinfo.ProductName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getProductName() {
        return productName;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ProductName
     * 
     * @param productName the value for productinfo.ProductName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.GeneralName
     * 
     * @return the value of productinfo.GeneralName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getGeneralName() {
        return generalName;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.GeneralName
     * 
     * @param generalName the value for productinfo.GeneralName
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setGeneralName(String generalName) {
        this.generalName = generalName == null ? null : generalName.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.DefaultImageFile_Id
     * 
     * @return the value of productinfo.DefaultImageFile_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Integer getDefaultImageFile_Id() {
        return defaultImageFile_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.DefaultImageFile_Id
     * 
     * @param defaultImageFile_Id the value for productinfo.DefaultImageFile_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setDefaultImageFile_Id(Integer defaultImageFile_Id) {
        this.defaultImageFile_Id = defaultImageFile_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.StorageMethod
     * 
     * @return the value of productinfo.StorageMethod
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getStorageMethod() {
        return storageMethod;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.StorageMethod
     * 
     * @param storageMethod the value for productinfo.StorageMethod
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod == null ? null : storageMethod.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.OriginalPlace
     * 
     * @return the value of productinfo.OriginalPlace
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getOriginalPlace() {
        return originalPlace;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.OriginalPlace
     * 
     * @param originalPlace the value for productinfo.OriginalPlace
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setOriginalPlace(String originalPlace) {
        this.originalPlace = originalPlace == null ? null : originalPlace.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.Status
     * 
     * @return the value of productinfo.Status
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.Status
     * 
     * @param status the value for productinfo.Status
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.BottleCode
     * 
     * @return the value of productinfo.BottleCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getBottleCode() {
        return bottleCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.BottleCode
     * 
     * @param bottleCode the value for productinfo.BottleCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setBottleCode(String bottleCode) {
        this.bottleCode = bottleCode == null ? null : bottleCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ProductCode
     * 
     * @return the value of productinfo.ProductCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ProductCode
     * 
     * @param productCode the value for productinfo.ProductCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.packagingCode
     * 
     * @return the value of productinfo.packagingCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public String getPackagingCode() {
        return packagingCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.packagingCode
     * 
     * @param packagingCode the value for productinfo.packagingCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setPackagingCode(String packagingCode) {
        this.packagingCode = packagingCode == null ? null : packagingCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ProductInfoType
     * 
     * @return the value of productinfo.ProductInfoType
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getProductInfoType() {
        return productInfoType;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ProductInfoType
     * 
     * @param productInfoType the value for productinfo.ProductInfoType
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setProductInfoType(Byte productInfoType) {
        this.productInfoType = productInfoType;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ProductStatisticsClass
     * 
     * @return the value of productinfo.ProductStatisticsClass
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Long getProductStatisticsClass() {
        return productStatisticsClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ProductStatisticsClass
     * 
     * @param productStatisticsClass the value for productinfo.ProductStatisticsClass
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setProductStatisticsClass(Long productStatisticsClass) {
        this.productStatisticsClass = productStatisticsClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.SecondStatisticsClass
     * 
     * @return the value of productinfo.SecondStatisticsClass
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Long getSecondStatisticsClass() {
        return secondStatisticsClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.SecondStatisticsClass
     * 
     * @param secondStatisticsClass the value for productinfo.SecondStatisticsClass
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setSecondStatisticsClass(Long secondStatisticsClass) {
        this.secondStatisticsClass = secondStatisticsClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.MonthOfShelfLife
     * 
     * @return the value of productinfo.MonthOfShelfLife
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.MonthOfShelfLife
     * 
     * @param monthOfShelfLife the value for productinfo.MonthOfShelfLife
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ShelfLifeUnit
     * 
     * @return the value of productinfo.ShelfLifeUnit
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ShelfLifeUnit
     * 
     * @param shelfLifeUnit the value for productinfo.ShelfLifeUnit
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setShelfLifeUnit(Byte shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.hasBottleCode
     * 
     * @return the value of productinfo.hasBottleCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getHasBottleCode() {
        return hasBottleCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.hasBottleCode
     * 
     * @param hasBottleCode the value for productinfo.hasBottleCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setHasBottleCode(Byte hasBottleCode) {
        this.hasBottleCode = hasBottleCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.hasBoxCode
     * 
     * @return the value of productinfo.hasBoxCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getHasBoxCode() {
        return hasBoxCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.hasBoxCode
     * 
     * @param hasBoxCode the value for productinfo.hasBoxCode
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setHasBoxCode(Byte hasBoxCode) {
        this.hasBoxCode = hasBoxCode;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ShopId
     * 
     * @return the value of productinfo.ShopId
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Long getShopId() {
        return shopId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ShopId
     * 
     * @param shopId the value for productinfo.ShopId
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.ShelfLifeLongTime
     * 
     * @return the value of productinfo.ShelfLifeLongTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getShelfLifeLongTime() {
        return shelfLifeLongTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.ShelfLifeLongTime
     * 
     * @param shelfLifeLongTime the value for productinfo.ShelfLifeLongTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setShelfLifeLongTime(Byte shelfLifeLongTime) {
        this.shelfLifeLongTime = shelfLifeLongTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.CreateTime
     * 
     * @return the value of productinfo.CreateTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.CreateTime
     * 
     * @param createTime the value for productinfo.CreateTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.CreateUser_Id
     * 
     * @return the value of productinfo.CreateUser_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Integer getCreateUser_Id() {
        return createUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.CreateUser_Id
     * 
     * @param createUser_Id the value for productinfo.CreateUser_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setCreateUser_Id(Integer createUser_Id) {
        this.createUser_Id = createUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.LastUpdateTime
     * 
     * @return the value of productinfo.LastUpdateTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.LastUpdateTime
     * 
     * @param lastUpdateTime the value for productinfo.LastUpdateTime
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.LastUpdateUser_Id
     * 
     * @return the value of productinfo.LastUpdateUser_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Integer getLastUpdateUser_Id() {
        return lastUpdateUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.LastUpdateUser_Id
     * 
     * @param lastUpdateUser_Id the value for productinfo.LastUpdateUser_Id
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setLastUpdateUser_Id(Integer lastUpdateUser_Id) {
        this.lastUpdateUser_Id = lastUpdateUser_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productinfo.PackageType
     * 
     * @return the value of productinfo.PackageType
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public Byte getPackageType() {
        return packageType;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productinfo.PackageType
     * 
     * @param packageType the value for productinfo.PackageType
     * @mbg.generated Thu Dec 06 16:37:00 CST 2018
     */
    public void setPackageType(Byte packageType) {
        this.packageType = packageType;
    }
}