package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品货位列表
 *
 * <AUTHOR>
 * @date 2018/8/1 14:16
 */
public class ProductLocationListPO implements Serializable {

    private static final long serialVersionUID = -1003080554266290419L;
    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 包装规格
     */
    private String specificationName;

    /**
     * 销售模式
     */
    private Byte saleModel;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 货位顺序
     */
    private Integer locationSequence;

    /**
     * 货位类型
     */
    private Byte subcategory;

    /**
     * 类型，0：货位，1：货区
     */
    private Byte category;

    /**
     * 产品货位Id
     */
    private Long productLocationId;

    /**
     * 货主供应商
     */
    private String ownerName;

    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 大单位名称
     */
    private String packageName;

    /**
     * 小单位名称
     */
    private String unitName;

    /**
     * 小件总数量
     */
    private BigDecimal unitTotolCount;

    /**
     * 产品的保质期
     */
    private Integer monthOfShelfLife;

    /**
     * 保质期单位(1：年 2：月 3：日）
     */
    private Integer shelfLifeUnit;

    /**
     * 产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    /**
     * 一级类目
     */
    private String statisticsClassName;

    /**
     * 二级类目
     */
    private String secondStatisticsClassName;

    /**
     * 货位分级属性，0：未设置，1：A，2：B，3：C
     */
    private Integer locationGrade;

    /**
     * 货区类型
     */
    private Byte areaSubcategory;

    /**
     * 货区id
     */
    private Long areaId;

    /**
     * 货区名称
     */
    private String areaName;

    /**
     * 业务类型，1：赠品 2:促销
     *
     */
    private Byte businessType;

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Byte getAreaSubcategory() {
        return areaSubcategory;
    }

    public void setAreaSubcategory(Byte areaSubcategory) {
        this.areaSubcategory = areaSubcategory;
    }

    public String getStatisticsClassName() {
        return statisticsClassName;
    }

    public void setStatisticsClassName(String statisticsClassName) {
        this.statisticsClassName = statisticsClassName;
    }

    public String getSecondStatisticsClassName() {
        return secondStatisticsClassName;
    }

    public void setSecondStatisticsClassName(String secondStatisticsClassName) {
        this.secondStatisticsClassName = secondStatisticsClassName;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitTotolCount() {
        return unitTotolCount;
    }

    public void setUnitTotolCount(BigDecimal unitTotolCount) {
        this.unitTotolCount = unitTotolCount;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public Byte getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getProductLocationId() {
        return productLocationId;
    }

    public void setProductLocationId(Long productLocationId) {
        this.productLocationId = productLocationId;
    }

    public Byte getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(Byte subcategory) {
        this.subcategory = subcategory;
    }

    public Integer getLocationSequence() {
        return locationSequence;
    }

    public void setLocationSequence(Integer locationSequence) {
        this.locationSequence = locationSequence;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }

    public Integer getLocationGrade() {
        return locationGrade;
    }

    public void setLocationGrade(Integer locationGrade) {
        this.locationGrade = locationGrade;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }
}
