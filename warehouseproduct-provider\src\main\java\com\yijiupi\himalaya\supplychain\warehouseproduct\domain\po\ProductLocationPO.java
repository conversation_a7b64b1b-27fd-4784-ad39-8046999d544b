package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;

public class ProductLocationPO {
    private Long id;

    private Integer city_Id;

    private Integer warehouse_Id;

    private Long productSku_Id;

    private Long location_Id;

    private String remo;

    private Date createTime;

    private Integer createUserId;

    private Date lastUpdateTime;

    private Integer lastUpdateUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCity_Id() {
        return city_Id;
    }

    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    public Integer getWarehouse_Id() {
        return warehouse_Id;
    }

    public void setWarehouse_Id(Integer warehouse_Id) {
        this.warehouse_Id = warehouse_Id;
    }

    public Long getProductSku_Id() {
        return productSku_Id;
    }

    public void setProductSku_Id(Long productSku_Id) {
        this.productSku_Id = productSku_Id;
    }

    public Long getLocation_Id() {
        return location_Id;
    }

    public void setLocation_Id(Long location_Id) {
        this.location_Id = location_Id;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public ProductLocationPO() {
        super();
    }

    public ProductLocationPO(LoactionDTO dto, Long locationId, Long productSkuId) {
        super();
        this.city_Id = dto.getCityId();
        this.location_Id = locationId;
        this.productSku_Id = productSkuId;
        this.warehouse_Id = dto.getWarehouseId();
    }

}