/*
 * @ClassName ProductRelationConfigPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-09-23 15:19:54
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class ProductRelationConfigPO {
    /**
     * @Fields id 主键
     */
    private Long id;
    /**
     * @Fields productSpecificationId 产品规格ID
     */
    private Long productSpecificationId;
    /**
     * @Fields ownerId 货主ID
     */
    private Long ownerId;
    /**
     * @Fields source 产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    private Byte source;
    /**
     * @Fields refProductSpecificationId 关联产品规格ID
     */
    private Long refProductSpecificationId;
    /**
     * @Fields refOwnerId 关联货主ID
     */
    private Long refOwnerId;
    /**
     * @Fields refSource 关联产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    private Byte refSource;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 产品识别编码(ProductSpecification_Id+Owner_Id+Source组成)
     */
    private String productIdentityCode;

    /**
     * 关联产品识别编码Ref_ProductSpecification_Id+Ref_Owner_Id+Ref_Source组成)
     */
    private String refProductIdentityCode;

    /**
     * 获取 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 产品规格ID
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 货主ID
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     * 设置 货主ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    public Byte getSource() {
        return source;
    }

    /**
     * 设置 产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    public void setSource(Byte source) {
        this.source = source;
    }

    /**
     * 获取 关联产品规格ID
     */
    public Long getRefProductSpecificationId() {
        return refProductSpecificationId;
    }

    /**
     * 设置 关联产品规格ID
     */
    public void setRefProductSpecificationId(Long refProductSpecificationId) {
        this.refProductSpecificationId = refProductSpecificationId;
    }

    /**
     * 获取 关联货主ID
     */
    public Long getRefOwnerId() {
        return refOwnerId;
    }

    /**
     * 设置 关联货主ID
     */
    public void setRefOwnerId(Long refOwnerId) {
        this.refOwnerId = refOwnerId;
    }

    /**
     * 获取 关联产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    public Byte getRefSource() {
        return refSource;
    }

    /**
     * 设置 关联产品来源,易酒批 = 0,微酒 = 1,知花知果 = 2,易款连锁 = 3,易经商 = 4
     */
    public void setRefSource(Byte refSource) {
        this.refSource = refSource;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getProductIdentityCode() {
        if (StringUtils.isBlank(productIdentityCode)) {
            return genIdentityCode(getProductSpecificationId(), getOwnerId(), getSource());
        }
        return productIdentityCode;
    }

    public void setProductIdentityCode(String productIdentityCode) {
        if (StringUtils.isBlank(productIdentityCode)) {
            productIdentityCode = genIdentityCode(getProductSpecificationId(), getOwnerId(), getSource());
        }
        this.productIdentityCode = productIdentityCode;
    }

    public String getRefProductIdentityCode() {
        if (StringUtils.isBlank(refProductIdentityCode)) {
            return genIdentityCode(getRefProductSpecificationId(), getRefOwnerId(), getRefSource());
        }
        return refProductIdentityCode;
    }

    public void setRefProductIdentityCode(String refProductIdentityCode) {
        if (StringUtils.isBlank(refProductIdentityCode)) {
            refProductIdentityCode = genIdentityCode(getRefProductSpecificationId(), getRefOwnerId(), getRefSource());
        }
        this.refProductIdentityCode = refProductIdentityCode;
    }

    /**
     * 生成产品/关联产品识别编码
     * 
     * @param productSpecificationId
     * @param ownerId
     * @param source
     * @return
     */
    private String genIdentityCode(Long productSpecificationId, Long ownerId, Byte source) {
        return String.format("%s%s%s", productSpecificationId == null ? "" : productSpecificationId,
            ObjectUtils.compare(ObjectUtils.defaultIfNull(source, ProductSourceType.易酒批).intValue(),
                ProductSourceType.易款连锁) == 0 ? "" : (ownerId == null ? "" : ownerId),
            source == null ? ProductSourceType.易酒批 : source);
    }
}