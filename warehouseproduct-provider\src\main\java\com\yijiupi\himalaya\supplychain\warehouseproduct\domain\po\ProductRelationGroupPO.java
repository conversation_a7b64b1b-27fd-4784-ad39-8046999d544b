/*
 * @ClassName ProductRelationGroupPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2020-06-18 16:23:55
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

public class ProductRelationGroupPO implements Serializable {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields cityId 城市ID
     */
    private Integer cityId;
    /**
     * @Fields warehouseId 仓库Id
     */
    private Integer warehouseId;
    /**
     * @Fields productSkuId 产品SKUID
     */
    private Long productSkuId;

    /**
     * 规格系数id
     */
    private Long productSpecificationId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * @Fields groupId 分组ID
     */
    private Long groupId;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库Id
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品SKUID
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品SKUID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 分组ID
     */
    public Long getGroupId() {
        return groupId;
    }

    /**
     * 设置 分组ID
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}