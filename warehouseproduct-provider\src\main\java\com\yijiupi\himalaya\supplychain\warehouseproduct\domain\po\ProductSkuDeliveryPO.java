package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

/**
 * 实际发货sku
 *
 * <AUTHOR>
 * @date 2019-12-30 11:13
 */
public class ProductSkuDeliveryPO {

    /**
     * 下单城市-skuid
     */
    private Long skuId;

    /**
     * 下单城市-规格ID
     */
    private Long specId;

    /**
     * 下单城市-货主ID
     */
    private Long ownerId;

    /**
     * 下单城市-销售模式
     */
    private Byte saleModel;

    /**
     * 发货城市-skuid
     */
    private Long deliverySkuId;

    /**
     * 发货城市-产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Byte getSaleModel() {
        return saleModel;
    }

    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    public Long getDeliverySkuId() {
        return deliverySkuId;
    }

    public void setDeliverySkuId(Long deliverySkuId) {
        this.deliverySkuId = deliverySkuId;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public String getIdentityKey() {
        return String.format("%s-%s", specId, ownerId);
    }
}
