package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductSkuPO implements Serializable {
    private static final long serialVersionUID = 8548638398872375571L;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.Id
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Long id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.City_Id
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Integer city_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.ProductSpecification_Id
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Long productSpecification_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.ProductSku_Id
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Long productSku_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.Name
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private String name;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.Sequence
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Integer sequence;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.SaleModel
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Byte saleModel;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.Company_Id
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Long company_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column productsku.Remo
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private String remo;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.CreateTime
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Date createTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.CreateUserId
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Integer createUserId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.LastUpdateTime
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Date lastUpdateTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * productsku.LastUpdateUserId
     * 
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    private Integer lastUpdateUserId;
    /**
     * 配送系数-工资
     */
    private BigDecimal distributionPercentForAmount;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押
     */
    private Byte source;
    /**
     * 仓库托管费
     */
    private BigDecimal warehouseCustodyFee;
    /**
     * 配送费（单件配送费）
     */
    private BigDecimal deliveryFee;
    /**
     * 配送费支付方式（0：固定价格，1：百分比）
     */
    private Integer deliveryPayType;
    /**
     * 分拣费
     */
    private BigDecimal sortingFee;
    /**
     * 产品信息id
     */
    private Long productInfoId;
    /**
     * 产品状态
     */
    private Integer productState;
    /**
     * 品牌
     */
    private String productBrand;
    /**
     * 货主名称
     */
    private String ownerName;
    /**
     * 是否拆包,不拆包(0), 拆包(1)
     */
    private Boolean unpackage;
    /**
     * 产品的保质期
     */
    private Integer monthOfShelfLife;
    /**
     * 保质期单位(1：年 2：月 3：日）
     */
    private Integer shelfLifeUnit;
    /**
     * 保质期是否为长期
     */
    private Boolean shelfLifeLongTime;

    private Long productInfoCategoryId;

    public Boolean getShelfLifeLongTime() {
        return shelfLifeLongTime;
    }

    public void setShelfLifeLongTime(Boolean shelfLifeLongTime) {
        this.shelfLifeLongTime = shelfLifeLongTime;
    }

    /**
     * 商品特征:1:大件,2:小件
     */
    private Byte productFeature;
    /**
     * 库存上限
     */
    private BigDecimal maxInventory;
    /**
     * 库存下限
     */
    private BigDecimal minInventory;
    /**
     * 补货上限
     */
    private BigDecimal maxReplenishment;
    /**
     * 补货下限
     */
    private BigDecimal minReplenishment;
    /**
     * 是否补全
     */
    private Byte isComplete;

    private String boxCode;
    private String packageCode;
    /**
     * 长/cm
     */
    private Double length;
    /**
     * 宽/cm
     */
    private Double width;
    /**
     * 高/cm
     */
    private Double height;
    /**
     * 重量/KG
     */
    private Double weight;
    /**
     * 体积(长*宽*高的字符串)
     */
    private String volume;
    private String sourceName;

    /**
     * 是否可以加工 0：不可以 1：可以
     */
    private Byte process;

    /**
     * 保存条件 0:标品 1:冷藏 2:冷冻 3:常温食品 4:鲜花绿植 5:电子券
     */
    private Byte storageType;

    /**
     * 是否拣货，默认否 0:否 1:是
     */
    private Byte pick;

    /**
     * 是否播种，默认是 0:否 1:是
     */
    private Byte sow;

    /**
     * 转换规格id
     */
    private Long convertProductInfoSpecId;

    /**
     * 转换规格系数
     */
    private BigDecimal convertSpecQuantity;

    /**
     * 库存占比
     */
    private String inventoryRatio;

    /**
     * 是否独家产品 0:否 1:是
     */
    private Byte unique;

    /**
     * 能否窜货（是否允许货主混放） 0:不能 1:能
     */
    private Byte fleeGoods;

    /**
     * 产品关联状态，0:未关联 1:已关联
     */
    private Byte productRelevantState;

    /**
     * 产品分级属性:0：未设置，1:A,2:B,3:C
     */
    private Integer productGrade;

    /**
     * 产品信息状态 上架(1), 下架(0), 废弃(-3), 不能设置（4）
     */
    private Byte productInfoStatus;

    /**
     * 图片ID
     */
    private String defaultImageFileId;

    /**
     * 系列名
     */
    private String seriesName;

    /**
     * 一级类目id
     */
    private Long statisticsClassId;

    /**
     * 一级类目名称
     */
    private String statisticsClass;

    /**
     * 二级类目id
     */
    private Long secondStatisticsClassId;

    /**
     * 二级类目名称
     */
    private String secondStatisticsClass;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 二级货主ID
     */
    private Long secOwnerId;

    /**
     * 成本价（最小单位）
     */
    private BigDecimal costPrice;

    /**
     * 关联外部的产品SKUID
     */
    private String refProductSkuId;

    /**
     * 销售价
     */
    private BigDecimal sellingPrice;

    /**
     * 销售价单位
     */
    private String sellingPriceUnit;

    /**
     * 替换的目标skuId
     */
    private Long replaceToSkuId;
    /**
     * 产品条码
     */
    private String productCode;

    /**
     * 大单位的中台packageId
     */
    private Long packageUnifyPackageId;

    /**
     * 小单位的中台packageId
     */
    private Long unitUnifyPackageId;
    /**
     * 产品外部编码
     */
    private String outerCode;

    /**
     * 产品类型
     */
    private Byte productType;

    /**
     * 托盘规格系数
     */
    private BigDecimal palletQuantity;

    /**
     * 是否已删除 0删除 1未删除
     */
    private Byte isDelete;

    /**
     * 业务属性, 整件区=0, 拆零区=1, 非标准模型, 这个是在 productSkuConfig 里的字段
     */
    private Integer businessTag;

    /**
     * 库存动销属性
     */
    private String inventoryPinProperty;

    /**
     * 分仓属性：0、默认；1、酒饮，2、休百
     */
    private Byte storageAttribute;

    public String getOuterCode() {
        return outerCode;
    }

    public void setOuterCode(String outerCode) {
        this.outerCode = outerCode;
    }

    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getSellingPriceUnit() {
        return sellingPriceUnit;
    }

    public void setSellingPriceUnit(String sellingPriceUnit) {
        this.sellingPriceUnit = sellingPriceUnit;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getStatisticsClassId() {
        return statisticsClassId;
    }

    public void setStatisticsClassId(Long statisticsClassId) {
        this.statisticsClassId = statisticsClassId;
    }

    public String getStatisticsClass() {
        return statisticsClass;
    }

    public void setStatisticsClass(String statisticsClass) {
        this.statisticsClass = statisticsClass;
    }

    public Long getSecondStatisticsClassId() {
        return secondStatisticsClassId;
    }

    public void setSecondStatisticsClassId(Long secondStatisticsClassId) {
        this.secondStatisticsClassId = secondStatisticsClassId;
    }

    public String getSecondStatisticsClass() {
        return secondStatisticsClass;
    }

    public void setSecondStatisticsClass(String secondStatisticsClass) {
        this.secondStatisticsClass = secondStatisticsClass;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getDefaultImageFileId() {
        return defaultImageFileId;
    }

    public void setDefaultImageFileId(String defaultImageFileId) {
        this.defaultImageFileId = defaultImageFileId;
    }

    public Byte getProductInfoStatus() {
        return productInfoStatus;
    }

    public void setProductInfoStatus(Byte productInfoStatus) {
        this.productInfoStatus = productInfoStatus;
    }

    public Integer getProductGrade() {
        return productGrade;
    }

    public void setProductGrade(Integer productGrade) {
        this.productGrade = productGrade;
    }

    public Byte getUnique() {
        return unique;
    }

    public void setUnique(Byte unique) {
        this.unique = unique;
    }

    public Byte getFleeGoods() {
        return fleeGoods;
    }

    public void setFleeGoods(Byte fleeGoods) {
        this.fleeGoods = fleeGoods;
    }

    public Byte getProductRelevantState() {
        return productRelevantState;
    }

    public void setProductRelevantState(Byte productRelevantState) {
        this.productRelevantState = productRelevantState;
    }

    public String getInventoryRatio() {
        return inventoryRatio;
    }

    public void setInventoryRatio(String inventoryRatio) {
        this.inventoryRatio = inventoryRatio;
    }

    public Byte getProcess() {
        return process;
    }

    public void setProcess(Byte process) {
        this.process = process;
    }

    public Byte getStorageType() {
        return storageType;
    }

    public void setStorageType(Byte storageType) {
        this.storageType = storageType;
    }

    public Byte getPick() {
        return pick;
    }

    public void setPick(Byte pick) {
        this.pick = pick;
    }

    public Byte getSow() {
        return sow;
    }

    public void setSow(Byte sow) {
        this.sow = sow;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public Boolean getUnpackage() {
        return unpackage;
    }

    public void setUnpackage(Boolean unpackage) {
        this.unpackage = unpackage;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Long getProductInfoId() {
        return productInfoId;
    }

    public void setProductInfoId(Long productInfoId) {
        this.productInfoId = productInfoId;
    }

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.Id
     * 
     * @return the value of productsku.Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column productsku.Id
     * 
     * @param id the value for productsku.Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.City_Id
     * 
     * @return the value of productsku.City_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Integer getCity_Id() {
        return city_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.City_Id
     * 
     * @param city_Id the value for productsku.City_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.ProductSpecification_Id
     * 
     * @return the value of productsku.ProductSpecification_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Long getProductSpecification_Id() {
        return productSpecification_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.ProductSpecification_Id
     * 
     * @param productSpecification_Id the value for productsku.ProductSpecification_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setProductSpecification_Id(Long productSpecification_Id) {
        this.productSpecification_Id = productSpecification_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.ProductSku_Id
     * 
     * @return the value of productsku.ProductSku_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Long getProductSku_Id() {
        return productSku_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.ProductSku_Id
     * 
     * @param productSku_Id the value for productsku.ProductSku_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setProductSku_Id(Long productSku_Id) {
        this.productSku_Id = productSku_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.Name
     * 
     * @return the value of productsku.Name
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column productsku.Name
     * 
     * @param name the value for productsku.Name
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.Sequence
     * 
     * @return the value of productsku.Sequence
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Integer getSequence() {
        return sequence;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.Sequence
     * 
     * @param sequence the value for productsku.Sequence
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.SaleModel
     * 
     * @return the value of productsku.SaleModel
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Byte getSaleModel() {
        return saleModel;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.SaleModel
     * 
     * @param saleModel the value for productsku.SaleModel
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setSaleModel(Byte saleModel) {
        this.saleModel = saleModel;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.Company_Id
     * 
     * @return the value of productsku.Company_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Long getCompany_Id() {
        return company_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.Company_Id
     * 
     * @param company_Id the value for productsku.Company_Id
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setCompany_Id(Long company_Id) {
        this.company_Id = company_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.Remo
     * 
     * @return the value of productsku.Remo
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public String getRemo() {
        return remo;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column productsku.Remo
     * 
     * @param remo the value for productsku.Remo
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setRemo(String remo) {
        this.remo = remo == null ? null : remo.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.CreateTime
     * 
     * @return the value of productsku.CreateTime
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.CreateTime
     * 
     * @param createTime the value for productsku.CreateTime
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.CreateUserId
     * 
     * @return the value of productsku.CreateUserId
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.CreateUserId
     * 
     * @param createUserId the value for productsku.CreateUserId
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.LastUpdateTime
     * 
     * @return the value of productsku.LastUpdateTime
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.LastUpdateTime
     * 
     * @param lastUpdateTime the value for productsku.LastUpdateTime
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * productsku.LastUpdateUserId
     * 
     * @return the value of productsku.LastUpdateUserId
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * productsku.LastUpdateUserId
     * 
     * @param lastUpdateUserId the value for productsku.LastUpdateUserId
     * @mbggenerated Wed Sep 06 09:48:37 CST 2017
     */
    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    /**
     * 获取 配送系数-工资
     * 
     * @return distributionPercentForAmount 配送系数-工资
     */
    public BigDecimal getDistributionPercentForAmount() {
        return this.distributionPercentForAmount;
    }

    /**
     * 设置 配送系数-工资
     * 
     * @param distributionPercentForAmount 配送系数-工资
     */
    public void setDistributionPercentForAmount(BigDecimal distributionPercentForAmount) {
        this.distributionPercentForAmount = distributionPercentForAmount;
    }

    /**
     * 获取 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押
     */
    public Byte getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押
     */
    public void setSource(Byte source) {
        this.source = source;
    }

    /**
     * 获取 仓库托管费
     */
    public BigDecimal getWarehouseCustodyFee() {
        return this.warehouseCustodyFee;
    }

    /**
     * 设置 仓库托管费
     */
    public void setWarehouseCustodyFee(BigDecimal warehouseCustodyFee) {
        this.warehouseCustodyFee = warehouseCustodyFee;
    }

    /**
     * 获取 配送费（单件配送费）
     */
    public BigDecimal getDeliveryFee() {
        return this.deliveryFee;
    }

    /**
     * 设置 配送费（单件配送费）
     */
    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    /**
     * 获取 配送费支付方式（0：固定价格，1：百分比）
     */
    public Integer getDeliveryPayType() {
        return this.deliveryPayType;
    }

    /**
     * 设置 配送费支付方式（0：固定价格，1：百分比）
     */
    public void setDeliveryPayType(Integer deliveryPayType) {
        this.deliveryPayType = deliveryPayType;
    }

    /**
     * 获取 分拣费
     */
    public BigDecimal getSortingFee() {
        return this.sortingFee;
    }

    /**
     * 设置 分拣费
     */
    public void setSortingFee(BigDecimal sortingFee) {
        this.sortingFee = sortingFee;
    }

    public Byte getProductFeature() {
        return productFeature;
    }

    public void setProductFeature(Byte productFeature) {
        this.productFeature = productFeature;
    }

    public BigDecimal getMaxInventory() {
        return maxInventory;
    }

    public void setMaxInventory(BigDecimal maxInventory) {
        this.maxInventory = maxInventory;
    }

    public BigDecimal getMinInventory() {
        return minInventory;
    }

    public void setMinInventory(BigDecimal minInventory) {
        this.minInventory = minInventory;
    }

    public BigDecimal getMaxReplenishment() {
        return maxReplenishment;
    }

    public void setMaxReplenishment(BigDecimal maxReplenishment) {
        this.maxReplenishment = maxReplenishment;
    }

    public BigDecimal getMinReplenishment() {
        return minReplenishment;
    }

    public void setMinReplenishment(BigDecimal minReplenishment) {
        this.minReplenishment = minReplenishment;
    }

    /**
     * 获取isComplete
     * 
     * @return isComplete isComplete
     */
    public Byte getIsComplete() {
        return isComplete;
    }

    /**
     * 设置isComplete
     * 
     * @param isComplete isComplete
     */
    public void setIsComplete(Byte isComplete) {
        this.isComplete = isComplete;
    }

    /**
     * 获取sourceName
     * 
     * @return sourceName sourceName
     */
    public String getSourceName() {
        return sourceName;
    }

    /**
     * 设置sourceName
     * 
     * @param sourceName sourceName
     */
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Long getConvertProductInfoSpecId() {
        return convertProductInfoSpecId;
    }

    public void setConvertProductInfoSpecId(Long convertProductInfoSpecId) {
        this.convertProductInfoSpecId = convertProductInfoSpecId;
    }

    public BigDecimal getConvertSpecQuantity() {
        return convertSpecQuantity;
    }

    public void setConvertSpecQuantity(BigDecimal convertSpecQuantity) {
        this.convertSpecQuantity = convertSpecQuantity;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getRefProductSkuId() {
        return refProductSkuId;
    }

    public void setRefProductSkuId(String refProductSkuId) {
        this.refProductSkuId = refProductSkuId;
    }

    public Long getUnitUnifyPackageId() {
        return unitUnifyPackageId;
    }

    public void setUnitUnifyPackageId(Long unitUnifyPackageId) {
        this.unitUnifyPackageId = unitUnifyPackageId;
    }

    public Long getPackageUnifyPackageId() {
        return packageUnifyPackageId;
    }

    public void setPackageUnifyPackageId(Long packageUnifyPackageId) {
        this.packageUnifyPackageId = packageUnifyPackageId;
    }

    public Long getReplaceToSkuId() {
        return replaceToSkuId;
    }

    public void setReplaceToSkuId(Long replaceToSkuId) {
        this.replaceToSkuId = replaceToSkuId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Byte getProductType() {
        return productType;
    }

    public void setProductType(Byte productType) {
        this.productType = productType;
    }

    public BigDecimal getPalletQuantity() {
        return palletQuantity;
    }

    public void setPalletQuantity(BigDecimal palletQuantity) {
        this.palletQuantity = palletQuantity;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getBusinessTag() {
        return businessTag;
    }

    public void setBusinessTag(Integer businessTag) {
        this.businessTag = businessTag;
    }

    /**
     * 获取
     *
     * @return productInfoCategoryId
     */
    public Long getProductInfoCategoryId() {
        return this.productInfoCategoryId;
    }

    /**
     * 设置
     *
     * @param productInfoCategoryId
     */
    public void setProductInfoCategoryId(Long productInfoCategoryId) {
        this.productInfoCategoryId = productInfoCategoryId;
    }

    public String getInventoryPinProperty() {
        return inventoryPinProperty;
    }

    public void setInventoryPinProperty(String inventoryPinProperty) {
        this.inventoryPinProperty = inventoryPinProperty;
    }

    public Byte getStorageAttribute() {
        return storageAttribute;
    }

    public void setStorageAttribute(Byte storageAttribute) {
        this.storageAttribute = storageAttribute;
    }
}
