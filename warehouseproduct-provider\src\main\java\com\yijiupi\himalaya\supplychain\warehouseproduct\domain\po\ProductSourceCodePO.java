package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 溯源码
 */
public class ProductSourceCodePO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 关联控货策略id
     */
    private Long configId;

    /**
     * 溯源码
     */
    private String code;

    /**
     * 来源：0-系统创建 1-扫码采集
     */
    private Byte source;

    /**
     * 状态：0-未使用 1-已使用
     */
    private Byte state;

    /**
     * 来源单据id
     */
    private String sourceBusinessId;

    /**
     * 来源单据编号
     */
    private String sourceBusinessNo;

    /**
     * 订单ID
     */
    private Long businessId;

    /**
     * 订单编号
     */
    private String businessNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 来源单据项id
     */
    private String sourceBusinessItemId;

    /**
     * 批次号
     */
    private String remark;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 批次号
     */
    private String specName;

    /**
     * 供应商名称
     */
    private Long providerId;

    /**
     * 供应商名称
     */
    private String provider;

    /**
     * 控货策略明细项
     */
    private List<ProductControlConfigItemPO> productControlConfigItemPOS;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String lastUpdateUser;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 溯源码记录
     */
    private List<ProductSourceCodeRecordPO> productSourceCodeRecordPOS;

    /**
     * skuid
     */
    private Long productSkuId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getSourceBusinessId() {
        return sourceBusinessId;
    }

    public void setSourceBusinessId(String sourceBusinessId) {
        this.sourceBusinessId = sourceBusinessId;
    }

    public String getSourceBusinessNo() {
        return sourceBusinessNo;
    }

    public void setSourceBusinessNo(String sourceBusinessNo) {
        this.sourceBusinessNo = sourceBusinessNo;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSourceBusinessItemId() {
        return sourceBusinessItemId;
    }

    public void setSourceBusinessItemId(String sourceBusinessItemId) {
        this.sourceBusinessItemId = sourceBusinessItemId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public List<ProductControlConfigItemPO> getProductControlConfigItemPOS() {
        return productControlConfigItemPOS;
    }

    public void setProductControlConfigItemPOS(List<ProductControlConfigItemPO> productControlConfigItemPOS) {
        this.productControlConfigItemPOS = productControlConfigItemPOS;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<ProductSourceCodeRecordPO> getProductSourceCodeRecordPOS() {
        return productSourceCodeRecordPOS;
    }

    public void setProductSourceCodeRecordPOS(List<ProductSourceCodeRecordPO> productSourceCodeRecordPOS) {
        this.productSourceCodeRecordPOS = productSourceCodeRecordPOS;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }
}