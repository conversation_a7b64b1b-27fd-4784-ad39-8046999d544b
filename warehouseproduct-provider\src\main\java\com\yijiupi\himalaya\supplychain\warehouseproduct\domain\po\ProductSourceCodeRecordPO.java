package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 溯源码记录
 */
public class ProductSourceCodeRecordPO implements Serializable {

    private static final long serialVersionUID = -4047049915239922980L;

    /**
     * 记录id
     */
    private Long id;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 溯源码记录id
     */
    private Long productSourceCodeId;

    /**
     * 溯源码
     */
    private String code;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 供应商
     */
    private String provider;

    /**
     * 变更前状态 0:在库 1:在途 2:出库
     */
    private Byte beforeState;

    /**
     * 变更后状态 0:在库 1:在途 2:出库
     */
    private Byte afterState;

    /**
     * 单据类型 0: 手动修改 1: 采集订单 2: 销售订单
     */
    private Byte businessType;

    /**
     * 单据id
     */
    private String businessId;

    /**
     * 单据编号
     */
    private String businessNo;

    /**
     * 描述
     */
    private String describe;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 最后修改人
     */
    private String lastUpdateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSourceCodeId() {
        return productSourceCodeId;
    }

    public void setProductSourceCodeId(Long productSourceCodeId) {
        this.productSourceCodeId = productSourceCodeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public Byte getBeforeState() {
        return beforeState;
    }

    public void setBeforeState(Byte beforeState) {
        this.beforeState = beforeState;
    }

    public Byte getAfterState() {
        return afterState;
    }

    public void setAfterState(Byte afterState) {
        this.afterState = afterState;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}