/*
 * @ClassName ProductSupplierPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2020-07-15 17:47:07
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

public class ProductSupplierPO implements Serializable {
    /**
     * @Fields id 主键
     */
    private Long id;
    /**
     * @Fields cityId 城市ID
     */
    private Integer cityId;
    /**
     * @Fields warehouseId 仓库ID
     */
    private Integer warehouseId;
    /**
     * @Fields productSpecificationId 产品规格ID
     */
    private Long productSpecificationId;
    /**
     * @Fields ownerId 货主ID
     */
    private Long ownerId;
    /**
     * @Fields secOwnerId 二级货主Id
     */
    private Long secOwnerId;
    /**
     * @Fields supplierType 供应商类型，1-仓库，2-供应商
     */
    private Byte supplierType;
    /**
     * @Fields supplierId 供应商ID
     */
    private String supplierId;
    /**
     * @Fields supplierName 供应商名称
     */
    private String supplierName;
    /**
     * @Fields isDefault 是否默认供应商,0-否，1-是
     */
    private Byte isDefault;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private String lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 获取 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 设置 城市ID
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品规格ID
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 货主ID
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     * 设置 货主ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 二级货主Id
     */
    public Long getSecOwnerId() {
        return secOwnerId;
    }

    /**
     * 设置 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 供应商类型，1-仓库，2-供应商
     */
    public Byte getSupplierType() {
        return supplierType;
    }

    /**
     * 设置 供应商类型，1-仓库，2-供应商
     */
    public void setSupplierType(Byte supplierType) {
        this.supplierType = supplierType;
    }

    /**
     * 获取 供应商ID
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     * 设置 供应商ID
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId == null ? null : supplierId.trim();
    }

    /**
     * 获取 供应商名称
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     * 设置 供应商名称
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    /**
     * 获取 是否默认供应商,0-否，1-是
     */
    public Byte getIsDefault() {
        return isDefault;
    }

    /**
     * 设置 是否默认供应商,0-否，1-是
     */
    public void setIsDefault(Byte isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser == null ? null : lastUpdateUser.trim();
    }

    /**
     * 获取 最后修改时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后修改时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getUniqueIdentification() {
        return String.format("%s-%s-%s-%s-%s-%s", getWarehouseId(), getProductSpecificationId(), getOwnerId(),
            getSecOwnerId(), getSupplierType(), getSupplierId());
    }
}