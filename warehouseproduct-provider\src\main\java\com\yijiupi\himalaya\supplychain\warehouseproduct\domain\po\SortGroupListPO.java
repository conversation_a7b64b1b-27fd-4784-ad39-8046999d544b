package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupPickWayConstants;

import java.io.Serializable;

/**
 * 分区列表数据
 *
 * <AUTHOR>
 * @date 2018/5/16 14:43
 */
public class SortGroupListPO implements Serializable {
    private static final long serialVersionUID = 1253074156294400610L;

    /**
     * 分区ID
     */
    private Long id;

    /**
     * 分区名称
     */
    private String name;

    /**
     * 分区类型（1：货区分区，2：货位分区 3：类目分区）
     */
    private Byte sortGroupType;

    /**
     * 类目
     */
    private String type;

    /**
     * 货区
     */
    private String area;

    /**
     * 货位
     */
    private String location;

    /**
     * 拣货员
     */
    private String sortUser;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分区标识 0：分区拣货 1：分区补货
     */
    private Byte flag;
    /**
     * 分拣拣货方式：:1、默认；2、电子标签拣货
     *
     * @see SortGroupPickWayConstants
     */
    private Byte sortGroupPickWay;
    /**
     * 状态
     *
     * @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    private Byte state;
    /**
     * 控制器按键编号
     */
    private String callNum;

    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSortUser() {
        return sortUser;
    }

    public void setSortUser(String sortUser) {
        this.sortUser = sortUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getSortGroupType() {
        return sortGroupType;
    }

    public void setSortGroupType(Byte sortGroupType) {
        this.sortGroupType = sortGroupType;
    }

    /**
     * 获取 分拣拣货方式：:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @return sortGroupPickWay 分拣拣货方式：:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public Byte getSortGroupPickWay() {
        return this.sortGroupPickWay;
    }

    /**
     * 设置 分拣拣货方式：:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @param sortGroupPickWay 分拣拣货方式：:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public void setSortGroupPickWay(Byte sortGroupPickWay) {
        this.sortGroupPickWay = sortGroupPickWay;
    }

    /**
     * 获取 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @return state 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @param state 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 控制器按键编号
     *
     * @return callNum 控制器按键编号
     */
    public String getCallNum() {
        return this.callNum;
    }

    /**
     * 设置 控制器按键编号
     *
     * @param callNum 控制器按键编号
     */
    public void setCallNum(String callNum) {
        this.callNum = callNum;
    }
}
