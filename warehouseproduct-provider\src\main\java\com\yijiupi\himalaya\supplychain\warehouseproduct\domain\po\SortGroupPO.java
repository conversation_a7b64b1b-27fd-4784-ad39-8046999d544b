package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupPickWayConstants;

import java.util.Date;

/**
 * 分区
 *
 * <AUTHOR>
 * @date 2018/5/15 17:46
 */
public class SortGroupPO {
    /**
     * 分区ID
     */
    private Long id;

    /**
     * 分区名称
     */
    private String name;

    /**
     * 分区类型（1：货区分区，2：货位分区 3：类目分区）
     */
    private Byte sortGroupType;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 更新人
     */
    private String lastUpdateUser;

    /**
     * 分区标识 0：分区拣货 1：分区补货
     */
    private Byte flag;
    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see SortGroupPickWayConstants
     */
    private Byte sortGroupPickWay;
    /**
     * 状态
     *
     * @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    private Byte state;
    /**
     * 控制器按键编号
     */
    private String callNum;

    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Byte getSortGroupType() {
        return sortGroupType;
    }

    public void setSortGroupType(Byte sortGroupType) {
        this.sortGroupType = sortGroupType;
    }

    /**
     * 获取 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @return sortGroupPickWay 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public Byte getSortGroupPickWay() {
        return this.sortGroupPickWay;
    }

    /**
     * 设置 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @param sortGroupPickWay 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public void setSortGroupPickWay(Byte sortGroupPickWay) {
        this.sortGroupPickWay = sortGroupPickWay;
    }

    /**
     * 获取 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @return state 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     *
     * @param state 状态 @see com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ConditionStateEnum
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 控制器按键编号
     *
     * @return callNum 控制器按键编号
     */
    public String getCallNum() {
        return this.callNum;
    }

    /**
     * 设置 控制器按键编号
     *
     * @param callNum 控制器按键编号
     */
    public void setCallNum(String callNum) {
        this.callNum = callNum;
    }
}
