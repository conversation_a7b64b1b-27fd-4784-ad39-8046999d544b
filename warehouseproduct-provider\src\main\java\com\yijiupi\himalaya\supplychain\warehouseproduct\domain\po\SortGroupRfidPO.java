package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 控制器电子标签
 */
public class SortGroupRfidPO implements Serializable {

    private static final long serialVersionUID = -979420228723614777L;

    /**
     * RFID主键id
     */
    private Long id;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 电子标签类型 1，分区, 2，巷道, 3，货位, 4.播种墙
     */
    private Byte rfidType;

    /**
     * 控制器id
     */
    private Long controlId;

    /**
     * 控制器名称
     */
    private String controlName;

    /**
     * 关联设备id
     */
    private Long deviceId;

    /**
     * 设备标签号
     */
    private String deviceTagNo;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 总线id
     */
    private Long busId;

    /**
     * 总线标签号
     */
    private String busTagNo;

    /**
     * 总线类型名称
     */
    private String busTypeName;

    /**
     * 关联项id
     */
    private Long sortId;

    /**
     * 关联项名称
     */
    private String sortName;

    /**
     * 电子标签是否启用：0:否，1:是
     */
    private Byte enable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后更新人
     */
    private String lastUpdateUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Byte getRfidType() {
        return rfidType;
    }

    public void setRfidType(Byte rfidType) {
        this.rfidType = rfidType;
    }

    public Long getControlId() {
        return controlId;
    }

    public void setControlId(Long controlId) {
        this.controlId = controlId;
    }

    public String getControlName() {
        return controlName;
    }

    public void setControlName(String controlName) {
        this.controlName = controlName;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceTagNo() {
        return deviceTagNo;
    }

    public void setDeviceTagNo(String deviceTagNo) {
        this.deviceTagNo = deviceTagNo;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public Long getBusId() {
        return busId;
    }

    public void setBusId(Long busId) {
        this.busId = busId;
    }

    public String getBusTagNo() {
        return busTagNo;
    }

    public void setBusTagNo(String busTagNo) {
        this.busTagNo = busTagNo;
    }

    public String getBusTypeName() {
        return busTypeName;
    }

    public void setBusTypeName(String busTypeName) {
        this.busTypeName = busTypeName;
    }

    public Long getSortId() {
        return sortId;
    }

    public void setSortId(Long sortId) {
        this.sortId = sortId;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public Byte getEnable() {
        return enable;
    }

    public void setEnable(Byte enable) {
        this.enable = enable;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}