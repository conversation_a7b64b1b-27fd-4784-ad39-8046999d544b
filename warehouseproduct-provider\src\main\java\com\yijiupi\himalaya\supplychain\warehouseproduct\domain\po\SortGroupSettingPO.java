package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 分区类别表
 *
 * <AUTHOR>
 * @date 2018/5/15 18:04
 */
public class SortGroupSettingPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分区ID
     */
    private Long sortGroupId;

    /**
     * 类别（1：货区，2：货位 3：类目）
     */
    private Integer sortType;

    /**
     * 关联项编码
     */
    private String sortId;

    /**
     * 关联项名称
     */
    private String sortName;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getSortType() {
        return sortType;
    }

    public void setSortType(Integer sortType) {
        this.sortType = sortType;
    }

    public String getSortId() {
        return sortId;
    }

    public void setSortId(String sortId) {
        this.sortId = sortId;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
