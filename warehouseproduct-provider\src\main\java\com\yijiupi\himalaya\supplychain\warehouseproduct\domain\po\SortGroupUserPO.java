package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * 分区成员
 *
 * <AUTHOR>
 * @date 2018/5/15 17:57
 */
public class SortGroupUserPO implements Serializable {
    private static final long serialVersionUID = -4091054030295895608L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分区ID
     */
    private Long sortGroupId;

    /**
     * 拣货员Id
     */
    private Integer userId;

    /**
     * 拣货员名称
     */
    private String userName;

    /**
     * 工作开始日期
     */
    private Date workStartDate;

    /**
     * 工作结束日期
     */
    private Date workEndDate;

    /**
     * 工作日类型（1=每周，2=每月）
     */
    private Integer workDayType;

    /**
     * 工作日详情
     */
    private String workDayDetail;

    /**
     * 工作开始时段
     */
    private Date workStartTime;

    /**
     * 工作结束时段
     */
    private Date workEndTime;

    /**
     * 状态（0=禁用，1=启用）
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date lastUpdateTime;

    /**
     * 更新人
     */
    private String lastUpdateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getWorkStartDate() {
        return workStartDate;
    }

    public void setWorkStartDate(Date workStartDate) {
        this.workStartDate = workStartDate;
    }

    public Date getWorkEndDate() {
        return workEndDate;
    }

    public void setWorkEndDate(Date workEndDate) {
        this.workEndDate = workEndDate;
    }

    public Integer getWorkDayType() {
        return workDayType;
    }

    public void setWorkDayType(Integer workDayType) {
        this.workDayType = workDayType;
    }

    public String getWorkDayDetail() {
        return workDayDetail;
    }

    public void setWorkDayDetail(String workDayDetail) {
        this.workDayDetail = workDayDetail;
    }

    public Date getWorkStartTime() {
        return workStartTime;
    }

    public void setWorkStartTime(Date workStartTime) {
        this.workStartTime = workStartTime;
    }

    public Date getWorkEndTime() {
        return workEndTime;
    }

    public void setWorkEndTime(Date workEndTime) {
        this.workEndTime = workEndTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}
