package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.math.BigDecimal;
import java.util.Date;

public class StockAgeStrategyPO {
    /**
     * id
     */
    private Long id;

    /**
     * 城市id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略类型: 0.产品 1.类目
     */
    private Byte strategyType;

    /**
     * 库龄下限
     */
    private BigDecimal minStockAge;

    /**
     * 库龄上限
     */
    private BigDecimal maxStockAge;

    /**
     * 状态: 0.启用 1.停用
     */
    private Byte state;

    /**
     * 策略配置数量
     */
    private Integer strategyConfigCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改时间
     */
    private Date lastUpdateTime;

    /**
     * 修改人
     */
    private String lastUpdateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(Byte strategyType) {
        this.strategyType = strategyType;
    }

    public BigDecimal getMinStockAge() {
        return minStockAge;
    }

    public void setMinStockAge(BigDecimal minStockAge) {
        this.minStockAge = minStockAge;
    }

    public BigDecimal getMaxStockAge() {
        return maxStockAge;
    }

    public void setMaxStockAge(BigDecimal maxStockAge) {
        this.maxStockAge = maxStockAge;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getStrategyConfigCount() {
        return strategyConfigCount;
    }

    public void setStrategyConfigCount(Integer strategyConfigCount) {
        this.strategyConfigCount = strategyConfigCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}