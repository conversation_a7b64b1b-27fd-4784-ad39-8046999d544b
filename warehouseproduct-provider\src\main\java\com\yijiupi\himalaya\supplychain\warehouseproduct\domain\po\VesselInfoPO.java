package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

public class VesselInfoPO {

    private Long id;

    private Integer warehouseId;

    /**
     * 容器id
     */
    private Long locationId;

    /**
     * 容器箱号
     */
    private String locationNo;

    /**
     * 当前货位
     */
    private Long currentLocationId;

    /**
     * 当前货位名称
     */
    private String currentLocationName;

    /**
     * 是否冻结 0未冻结，1已冻结
     */
    private Byte isFreeze;

    private String createUser;

    private Date createTime;

    private Date lastUpdateTime;

    private String lastUpdateUser;

    private String remark;

    /**
     * 关联货位
     */
    private Long relationLocationId;

    /**
     * 关联货位名称
     */
    private String relationLocationNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationNo() {
        return locationNo;
    }

    public void setLocationNo(String locationNo) {
        this.locationNo = locationNo;
    }

    public Long getCurrentLocationId() {
        return currentLocationId;
    }

    public void setCurrentLocationId(Long currentLocationId) {
        this.currentLocationId = currentLocationId;
    }

    public String getCurrentLocationName() {
        return currentLocationName;
    }

    public void setCurrentLocationName(String currentLocationName) {
        this.currentLocationName = currentLocationName;
    }

    public Byte getIsFreeze() {
        return isFreeze;
    }

    public void setIsFreeze(Byte isFreeze) {
        this.isFreeze = isFreeze;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getRelationLocationId() {
        return relationLocationId;
    }

    public void setRelationLocationId(Long relationLocationId) {
        this.relationLocationId = relationLocationId;
    }

    public String getRelationLocationNo() {
        return relationLocationNo;
    }

    public void setRelationLocationNo(String relationLocationNo) {
        this.relationLocationNo = relationLocationNo;
    }
}
