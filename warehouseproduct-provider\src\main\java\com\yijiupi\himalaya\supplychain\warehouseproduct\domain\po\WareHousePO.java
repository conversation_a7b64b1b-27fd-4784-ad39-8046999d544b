package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: WareHousePO.java
 * @Package
 * <AUTHOR>
 * @date 2018/3/8 17:10
 */
public class WareHousePO {

    private Integer id;

    private String name;

    private String remark;

    private Integer state;

    private String province;

    private String city;

    private String county;

    private String street;

    private String detailAddress;

    private Date createTime;

    private Integer createUserId;

    private Date lastUpdateTime;

    private Integer lastUpdateUserId;

    private Integer warehouseType;

    private Integer warehouseClass;

    private Long shopId;
    private Integer cityId;

    /**
     * 下单经度
     */
    private BigDecimal longitude;
    /**
     * 下单纬度
     */
    private BigDecimal latitude;

    /**
     * 月租价格
     */
    private BigDecimal rent;

    /**
     * 租金单位: 1：年 2：月 3：日
     */
    private Byte rentUnit;

    /**
     * 面积
     */
    private BigDecimal acreage;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    public Integer getWarehouseClass() {
        return warehouseClass;
    }

    public void setWarehouseClass(Integer warehouseClass) {
        this.warehouseClass = warehouseClass;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    /**
     * 获取 下单经度
     */
    public BigDecimal getLongitude() {
        return this.longitude;
    }

    /**
     * 设置 下单经度
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取 下单纬度
     */
    public BigDecimal getLatitude() {
        return this.latitude;
    }

    /**
     * 设置 下单纬度
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getRent() {
        return rent;
    }

    public void setRent(BigDecimal rent) {
        this.rent = rent;
    }

    public Byte getRentUnit() {
        return rentUnit;
    }

    public void setRentUnit(Byte rentUnit) {
        this.rentUnit = rentUnit;
    }

    public BigDecimal getAcreage() {
        return acreage;
    }

    public void setAcreage(BigDecimal acreage) {
        this.acreage = acreage;
    }
}