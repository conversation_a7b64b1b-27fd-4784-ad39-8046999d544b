package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 固定资产统计
 */
public class WarehousePropertyRecordPO implements Serializable {
    private static final long serialVersionUID = 3314646923652511334L;
    private Long id;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 请求id
     */
    private String requestId;

    /**
     * 描述
     */
    private String detailsDescription;
    /**
     * 请求时间
     */
    private Date applicantDate;
    private String lastUpdateUser;
    private Date lastUpdateTime;
    private Date createTime;
    private String createUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getDetailsDescription() {
        return detailsDescription;
    }

    public void setDetailsDescription(String detailsDescription) {
        this.detailsDescription = detailsDescription;
    }

    public Date getApplicantDate() {
        return applicantDate;
    }

    public void setApplicantDate(Date applicantDate) {
        this.applicantDate = applicantDate;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}
