/*
 * @ClassName WarehouseWineConfigItemPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-02-27 15:51:58
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

public class WarehouseWineConfigItemPO {
    /**
     * @Fields id
     */
    private Long id;
    /**
     * @Fields configId 名酒策略配置id
     */
    private Long configId;
    /**
     * @Fields productSpecificationId 产品规格id
     */
    private Long productSpecificationId;
    /**
     * @Fields productSkuId 产品skuId
     */
    private Long productSkuId;
    /**
     * @Fields productName 产品名称
     */
    private String productName;
    /**
     * @Fields productBrand 产品品牌
     */
    private String productBrand;
    /**
     * @Fields productCategory 产品类目
     */
    private String productCategory;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后修改人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后修改时间
     */
    private Date lastUpdateTime;
    /**
     * @Fields status 是否删除 0=不删除 1=删除
     */
    private Byte status;

    /**
     * 获取
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 名酒策略配置id
     */
    public Long getConfigId() {
        return configId;
    }

    /**
     * 设置 名酒策略配置id
     */
    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    /**
     * 获取 产品规格id
     */
    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    /**
     * 设置 产品规格id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 产品skuId
     */
    public Long getProductSkuId() {
        return productSkuId;
    }

    /**
     * 设置 产品skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取 产品品牌
     */
    public String getProductBrand() {
        return productBrand;
    }

    /**
     * 设置 产品品牌
     */
    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    /**
     * 获取 产品类目
     */
    public String getProductCategory() {
        return productCategory;
    }

    /**
     * 设置 产品类目
     */
    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory == null ? null : productCategory.trim();
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后修改人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后修改人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后修改时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后修改时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 是否删除 0=不删除 1=删除
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 是否删除 0=不删除 1=删除
     */
    public void setStatus(Byte status) {
        this.status = status;
    }
}
