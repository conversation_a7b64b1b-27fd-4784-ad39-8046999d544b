/*
 * @ClassName WarehouseWineConfigPO
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2019-02-27 15:51:15
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.io.Serializable;
import java.util.Date;

public class WarehouseWineConfigPO implements Serializable {
    /**
     * @Fields id 编号
     */
    private Long id;
    /**
     * @Fields configName 策略名称
     */
    private String configName;
    /**
     * @Fields company 所属公司
     */
    private String company;
    /**
     * @Fields warehouseIds 仓库id集合
     */
    private String warehouseIds;
    /**
     * @Fields compulsoryCodeCopying 强制抄码 0=不抄码 1=抄码
     */
    private Byte compulsoryCodeCopying;
    /**
     * @Fields defaultPolicy 默认策略 0=未设置 1=已设置
     */
    private Byte defaultPolicy;
    /**
     * @Fields createUser 创建人
     */
    private Long createUser;
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    /**
     * @Fields lastUpdateUser 最后更新人
     */
    private Long lastUpdateUser;
    /**
     * @Fields lastUpdateTime 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * @Fields status 是否删除 0= 不删除，1=删除
     */
    private Byte status;

    /**
     * 获取 编号
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置 编号
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 策略名称
     */
    public String getConfigName() {
        return configName;
    }

    /**
     * 设置 策略名称
     */
    public void setConfigName(String configName) {
        this.configName = configName == null ? null : configName.trim();
    }

    /**
     * 获取 所属公司
     */
    public String getCompany() {
        return company;
    }

    /**
     * 设置 所属公司
     */
    public void setCompany(String company) {
        this.company = company == null ? null : company.trim();
    }

    /**
     * 获取 仓库id集合
     */
    public String getWarehouseIds() {
        return warehouseIds;
    }

    /**
     * 设置 仓库id集合
     */
    public void setWarehouseIds(String warehouseIds) {
        this.warehouseIds = warehouseIds == null ? null : warehouseIds.trim();
    }

    /**
     * 获取 强制抄码 0=不抄码 1=抄码
     */
    public Byte getCompulsoryCodeCopying() {
        return compulsoryCodeCopying;
    }

    /**
     * 设置 强制抄码 0=不抄码 1=抄码
     */
    public void setCompulsoryCodeCopying(Byte compulsoryCodeCopying) {
        this.compulsoryCodeCopying = compulsoryCodeCopying;
    }

    /**
     * 获取 默认策略 0=未设置 1=已设置
     */
    public Byte getDefaultPolicy() {
        return defaultPolicy;
    }

    /**
     * 设置 默认策略 0=未设置 1=已设置
     */
    public void setDefaultPolicy(Byte defaultPolicy) {
        this.defaultPolicy = defaultPolicy;
    }

    /**
     * 获取 创建人
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 最后更新人
     */
    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    /**
     * 设置 最后更新人
     */
    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    /**
     * 获取 最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置 最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取 是否删除 0= 不删除，1=删除
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置 是否删除 0= 不删除，1=删除
     */
    public void setStatus(Byte status) {
        this.status = status;
    }
}
