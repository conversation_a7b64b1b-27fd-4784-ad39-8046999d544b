package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.math.BigDecimal;
import java.util.Date;

public class WarehousechargeconfigPO {
    private Long id;

    private Integer warehouse_id;

    private BigDecimal unloadingCharge;

    private BigDecimal sortingCharge;

    private BigDecimal custodianCharge;

    private BigDecimal loadingCharge;

    private BigDecimal transportCharge;

    private BigDecimal landingCharge;

    private Byte status;

    private Long createUser;

    private Date createTime;

    private Long lastUpdateUser;

    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouse_id() {
        return warehouse_id;
    }

    public void setWarehouse_id(Integer warehouse_id) {
        this.warehouse_id = warehouse_id;
    }

    public BigDecimal getUnloadingCharge() {
        return unloadingCharge;
    }

    public void setUnloadingCharge(BigDecimal unloadingCharge) {
        this.unloadingCharge = unloadingCharge;
    }

    public BigDecimal getSortingCharge() {
        return sortingCharge;
    }

    public void setSortingCharge(BigDecimal sortingCharge) {
        this.sortingCharge = sortingCharge;
    }

    public BigDecimal getCustodianCharge() {
        return custodianCharge;
    }

    public void setCustodianCharge(BigDecimal custodianCharge) {
        this.custodianCharge = custodianCharge;
    }

    public BigDecimal getLoadingCharge() {
        return loadingCharge;
    }

    public void setLoadingCharge(BigDecimal loadingCharge) {
        this.loadingCharge = loadingCharge;
    }

    public BigDecimal getTransportCharge() {
        return transportCharge;
    }

    public void setTransportCharge(BigDecimal transportCharge) {
        this.transportCharge = transportCharge;
    }

    public BigDecimal getLandingCharge() {
        return landingCharge;
    }

    public void setLandingCharge(BigDecimal landingCharge) {
        this.landingCharge = landingCharge;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(Long lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}
