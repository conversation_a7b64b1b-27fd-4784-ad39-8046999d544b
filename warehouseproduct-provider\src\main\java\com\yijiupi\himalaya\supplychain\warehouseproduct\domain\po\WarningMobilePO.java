package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po;

import java.util.Date;

public class WarningMobilePO {

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column warningmobile.Id
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Long id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.City_Id
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Integer city_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.GroupId
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private String groupId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.ProductFirstCategory_Id
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Long productFirstCategory_Id;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.CategoryClass
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Long categoryClass;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column warningmobile.Mobile
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private String mobile;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column warningmobile.Remo
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private String remo;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.CreateUserId
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Integer createUserId;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.CreateTime
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Date createTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.LastUpdateTime
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Date lastUpdateTime;
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database column
     * warningmobile.LastUpdateUserId
     * 
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    private Integer lastUpdateUserId;

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.Id
     * 
     * @return the value of warningmobile.Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.Id
     * 
     * @param id the value for warningmobile.Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.City_Id
     * 
     * @return the value of warningmobile.City_Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Integer getCity_Id() {
        return city_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.City_Id
     * 
     * @param city_Id the value for warningmobile.City_Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setCity_Id(Integer city_Id) {
        this.city_Id = city_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.GroupId
     * 
     * @return the value of warningmobile.GroupId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public String getGroupId() {
        return groupId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.GroupId
     * 
     * @param groupId the value for warningmobile.GroupId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.ProductFirstCategory_Id
     * 
     * @return the value of warningmobile.ProductFirstCategory_Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Long getProductFirstCategory_Id() {
        return productFirstCategory_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.ProductFirstCategory_Id
     * 
     * @param productFirstCategory_Id the value for warningmobile.ProductFirstCategory_Id
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setProductFirstCategory_Id(Long productFirstCategory_Id) {
        this.productFirstCategory_Id = productFirstCategory_Id;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.CategoryClass
     * 
     * @return the value of warningmobile.CategoryClass
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Long getCategoryClass() {
        return categoryClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.CategoryClass
     * 
     * @param categoryClass the value for warningmobile.CategoryClass
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setCategoryClass(Long categoryClass) {
        this.categoryClass = categoryClass;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.Mobile
     * 
     * @return the value of warningmobile.Mobile
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.Mobile
     * 
     * @param mobile the value for warningmobile.Mobile
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.Remo
     * 
     * @return the value of warningmobile.Remo
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public String getRemo() {
        return remo;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.Remo
     * 
     * @param remo the value for warningmobile.Remo
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setRemo(String remo) {
        this.remo = remo == null ? null : remo.trim();
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.CreateUserId
     * 
     * @return the value of warningmobile.CreateUserId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.CreateUserId
     * 
     * @param createUserId the value for warningmobile.CreateUserId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.CreateTime
     * 
     * @return the value of warningmobile.CreateTime
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.CreateTime
     * 
     * @param createTime the value for warningmobile.CreateTime
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.LastUpdateTime
     * 
     * @return the value of warningmobile.LastUpdateTime
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.LastUpdateTime
     * 
     * @param lastUpdateTime the value for warningmobile.LastUpdateTime
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator. This method returns the value of the database column
     * warningmobile.LastUpdateUserId
     * 
     * @return the value of warningmobile.LastUpdateUserId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public Integer getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator. This method sets the value of the database column
     * warningmobile.LastUpdateUserId
     * 
     * @param lastUpdateUserId the value for warningmobile.LastUpdateUserId
     * @mbggenerated Mon Jul 17 15:00:10 CST 2017
     */
    public void setLastUpdateUserId(Integer lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }
}