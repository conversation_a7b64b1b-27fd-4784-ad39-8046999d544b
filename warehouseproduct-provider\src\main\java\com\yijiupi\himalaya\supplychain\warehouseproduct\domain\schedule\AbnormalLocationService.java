package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskCreateParam;
import com.yijiupi.himalaya.assignment.enums.todo.AssignType;
import com.yijiupi.himalaya.assignment.enums.todo.TodoTaskRank;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.query.WarehouseForWMSQueryDTO;
import com.yijiupi.himalaya.supplychain.service.IWarehouseQueryForWMSService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.EnableState;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo.TodoTaskBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.variable.VariableManager;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.AbnormalLocationBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static java.util.concurrent.CompletableFuture.runAsync;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @since 2024-10-23 12:37
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class AbnormalLocationService {

    @Resource
    private ProductLocationPOMapper productLocationPOMapper;

    @Resource
    private TodoTaskBL todoTaskBL;

    @Resource
    @Qualifier("warehouseProductTaskExecutor")
    private Executor executor;

    @Resource
    private VariableManager variableManager;

    @Reference
    private IInStockQueryService inStockQueryService;

    @Reference
    private IWarehouseQueryForWMSService warehouseQueryForWMSService;

    private static final int LIMIT_TASK = 10;

    private static final String SPILT_CATEGORY = "SplitCategory";

    private static final String LOCATION_TEMPLATE01 = "%s 超过 1 个月未关联产品, 存在货位浪费, 请关联产品";

    private static final String LOCATION_TEMPLATE02 = "%s 需要重新关联货位, 请立即处理!";

    private static final Logger logger = LoggerFactory.getLogger(AbnormalLocationService.class);

    /**
     * 异常货位定时生成待办任务
     */
    @XxlJob("abnormalLocationTodoTask")
    public void abnormalLocationTodoTask() {
        logger.info("开始自动生成异常货位待办任务");
        List<Integer> warehouseIds = new ArrayList<>(queryWarehouseIds());
        if (warehouseIds.isEmpty()) {
            logger.info("没有启用的仓库, 不做后续处理");
            return;
        }
        // 货位无关联产品生成任务
        CompletableFuture<Void> f1 = runAsync(() -> noneRefProductLocationTask(warehouseIds), executor);
        // 入库产品关联货位任务
        CompletableFuture<Void> f2 = runAsync(() -> inStockRefLocationTask(warehouseIds), executor);
        CompletableFuture.allOf(f1, f2).exceptionally(it -> {
            logger.error("出现异常", it);
            return null;
        }).join();
        logger.info("待办任务生成结束");
    }

    /**
     * 货位无关联产品生成任务
     *
     * @param warehouseIds 仓库 id
     */
    private void noneRefProductLocationTask(List<Integer> warehouseIds) {
        // 仓库有无关联产品的分拣零拣货位, 且仓库有上架的sku有库存且无关联货位
        Map<Integer, Set<Long>> skuWarehouseMap = productLocationPOMapper.findNoLocationSku(warehouseIds).stream()
                .collect(groupingBy(AbnormalLocationBO::getWarehouseId, mapping(AbnormalLocationBO::getSkuId, toSet())));
        List<TodoTaskCreateParam> tasks = productLocationPOMapper.findNoneRefProductLocation(warehouseIds).stream()
                .collect(groupingBy(AbnormalLocationBO::getWarehouseId)).entrySet().stream()
                .filter(it -> !skuWarehouseMap.getOrDefault(it.getKey(), Collections.emptySet()).isEmpty())
                // 按仓库分组后, 限制一次推送的数量
                .flatMap(it -> it.getValue().stream().limit(LIMIT_TASK).map(item -> todoTaskBL.newBuilder()
                        .setTaskTypeCode("HWZL001")
                        .setTaskRank(TodoTaskRank.HIGH)
                        // businessNo 设置成仓库 id, 货位名称
                        .setBusinessNo(String.format("%s,%s", it.getKey(), item.getLocationName()))
                        .setStartTime(new Date())
                        .setOverdueDays(1)
                        .setAssignType(AssignType.WAREHOUSE)
                        .addAssignId(it.getKey())
                        .addAssignRole(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程)
                        .setTaskDetail(String.format(LOCATION_TEMPLATE01, item.getLocationName()))
                )).map(TodoTaskBL.Builder::build).filter(Objects::nonNull)
                .collect(Collectors.toList());
        todoTaskBL.createTask(tasks);
    }

    /**
     * 入库产品关联货位任务
     *
     * @param warehouseIds 仓库 id
     */
    private void inStockRefLocationTask(List<Integer> warehouseIds) {
        Map<Integer, Set<Long>> skuIdByPassDays = inStockQueryService.findSkuIdByPassDays(warehouseIds, 1);
        Set<Long> skuIds = skuIdByPassDays.values().stream().flatMap(Collection::stream).collect(toSet());
        if (skuIds.isEmpty()) {
            logger.info("没有找到 1 天前入库的 sku, 跳过后续处理");
            return;
        }
        // 根据【SplitCategory】这个wms参数判断是酒饮还是休食，上架任务产品类目在这个参数中，就是休食，否则就是酒饮。
        Set<Long> restCategoryIds = variableManager.getListVariable(SPILT_CATEGORY, warehouseIds.get(0));
        logger.info("休食类目 id: {}", restCategoryIds);
        Map<Long, String> noLocationSkuMap = productLocationPOMapper.findNoLocationSkuBySkuIds(skuIds, restCategoryIds)
                .stream().collect(toMap(AbnormalLocationBO::getSkuId, AbnormalLocationBO::getProductName));
        List<TodoTaskCreateParam> tasks = skuIdByPassDays.entrySet().stream().flatMap(it ->
                // 按仓库分组后, 限制一次推送的数量
                it.getValue().stream().filter(noLocationSkuMap::containsKey).limit(LIMIT_TASK).map(item -> todoTaskBL.newBuilder()
                        .setTaskTypeCode("HWZL002")
                        .setTaskRank(TodoTaskRank.HIGH)
                        // businessNo 设置成仓库 id, skuId
                        .setBusinessNo(String.format("%s,%s", it.getKey(), item))
                        .setStartTime(new Date())
                        .setOverdueDays(1)
                        .setAssignType(AssignType.WAREHOUSE)
                        .addAssignId(it.getKey())
                        .addAssignRole(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程)
                        .setTaskDetail(String.format(LOCATION_TEMPLATE02, noLocationSkuMap.get(item)))
                        .build()
                )).filter(Objects::nonNull).collect(toList());
        todoTaskBL.createTask(tasks);
    }

    /**
     * 获取仓库 id, 如果参数指定了仓库 id 则优先使用参数的
     *
     * @return 要生成任务的仓库 id
     */
    private Set<Integer> queryWarehouseIds() {
        String param = XxlJobContext.getXxlJobContext().getJobParam();
        if (StringUtils.hasText(param)) {
            logger.info("指定灰度仓库调度: {}", param);
            List<Integer> array = JSONArray.parseArray(param, Integer.class);
            if (CollectionUtils.isEmpty(array)) {
                return Collections.emptySet();
            }
            return new HashSet<>(array);
        }
        WarehouseForWMSQueryDTO queryDTO = new WarehouseForWMSQueryDTO();
        queryDTO.setStatus(EnableState.启用.value);
        List<Warehouse> warehouseList = warehouseQueryForWMSService.listForWms(queryDTO).getDataList();
        if (CollectionUtils.isEmpty(warehouseList)) {
            return Collections.emptySet();
        }
        return warehouseList.stream().map(Warehouse::getId).collect(toSet());
    }

}
