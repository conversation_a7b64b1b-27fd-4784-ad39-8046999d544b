package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 产品类目同步服务
 */
@Service
public class ProductCategorySyncService {

    @Reference
    private IProductCategoryService iProductCategoryService;

    private static final Logger logger = LoggerFactory.getLogger(ProductCategorySyncService.class);

    // 指定每晚0点
    // @Scheduled(cron = "0 0 0 * * ?")
    @XxlJob("wms_syncProductCategory")
    public void syncProductCategory() {
        logger.info("开始同步产品类目");
        try {
            iProductCategoryService.initProductCategoryByProductInfo();
        } catch (Exception e) {
            logger.info("同步产品类目出错！{}", e.getMessage());
        }
        logger.info("结束同步产品类目");
    }

}