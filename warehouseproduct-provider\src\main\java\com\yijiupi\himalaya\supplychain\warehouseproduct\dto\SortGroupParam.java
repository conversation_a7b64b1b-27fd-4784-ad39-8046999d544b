package com.yijiupi.himalaya.supplychain.warehouseproduct.dto;

import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SortGroupPickWayConstants;

import java.util.List;

/**
 * 分区
 *
 * <AUTHOR>
 * @date 2018/5/17 10:56
 */
public class SortGroupParam {

    /**
     * 分区ID
     */
    private Long id;

    /**
     * 分区名称
     */
    private String name;

    /**
     * 分区类型（1：货区分区，2：货位分区 3：类目分区）
     */
    private Byte sortGroupType;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String executeUser;

    /**
     * 分区人员列表
     */
    private List<SortGroupUserParam> groupUserList;

    /**
     * 分区类别列表
     */
    private List<SortGroupSettingParam> groupSettingList;

    /**
     * 分区标识 0：分区拣货 1：分区补货
     */
    private Byte flag;
    /**
     * 拣货方式:1、默认；2、电子标签拣货
     *
     * @see SortGroupPickWayConstants
     */
    private Byte sortGroupPickWay;
    /**
     * 控制器编号
     */
    private String callNum;

    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExecuteUser() {
        return executeUser;
    }

    public void setExecuteUser(String executeUser) {
        this.executeUser = executeUser;
    }

    public List<SortGroupUserParam> getGroupUserList() {
        return groupUserList;
    }

    public void setGroupUserList(List<SortGroupUserParam> groupUserList) {
        this.groupUserList = groupUserList;
    }

    public List<SortGroupSettingParam> getGroupSettingList() {
        return groupSettingList;
    }

    public void setGroupSettingList(List<SortGroupSettingParam> groupSettingList) {
        this.groupSettingList = groupSettingList;
    }

    public Byte getSortGroupType() {
        return sortGroupType;
    }

    public void setSortGroupType(Byte sortGroupType) {
        this.sortGroupType = sortGroupType;
    }

    /**
     * 获取 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @return sortGroupPickWay 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public Byte getSortGroupPickWay() {
        return this.sortGroupPickWay;
    }

    /**
     * 设置 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     *
     * @param sortGroupPickWay 拣货方式:1、默认；2、电子标签拣货 @see SortGroupPickWayConstants
     */
    public void setSortGroupPickWay(Byte sortGroupPickWay) {
        this.sortGroupPickWay = sortGroupPickWay;
    }

    /**
     * 获取 控制器编号
     *
     * @return callNum 控制器编号
     */
    public String getCallNum() {
        return this.callNum;
    }

    /**
     * 设置 控制器编号
     *
     * @param callNum 控制器编号
     */
    public void setCallNum(String callNum) {
        this.callNum = callNum;
    }
}
