package com.yijiupi.himalaya.supplychain.warehouseproduct.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/16 20:57
 */
public class SortGroupUserParam {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分区ID
     */
    private Long sortGroupId;

    /**
     * 拣货员Id
     */
    private Integer userId;

    /**
     * 拣货员名称
     */
    private String userName;

    /**
     * 工作开始日期
     */
    private String workStartDate;

    /**
     * 工作结束日期
     */
    private String workEndDate;

    /**
     * 工作日类型（1=每周，2=每月）
     */
    private Integer workDayType;

    /**
     * 工作日详情列表
     */
    private List<String> workDayDetailList;

    /**
     * 工作开始时段
     */
    private String workStartTime;

    /**
     * 工作结束时段
     */
    private String workEndTime;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 操作人
     */
    private String executeUser;

    public Long getSortGroupId() {
        return sortGroupId;
    }

    public void setSortGroupId(Long sortGroupId) {
        this.sortGroupId = sortGroupId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getWorkStartDate() {
        return workStartDate;
    }

    public void setWorkStartDate(String workStartDate) {
        this.workStartDate = workStartDate;
    }

    public String getWorkEndDate() {
        return workEndDate;
    }

    public void setWorkEndDate(String workEndDate) {
        this.workEndDate = workEndDate;
    }

    public Integer getWorkDayType() {
        return workDayType;
    }

    public void setWorkDayType(Integer workDayType) {
        this.workDayType = workDayType;
    }

    public String getWorkStartTime() {
        return workStartTime;
    }

    public void setWorkStartTime(String workStartTime) {
        this.workStartTime = workStartTime;
    }

    public String getWorkEndTime() {
        return workEndTime;
    }

    public void setWorkEndTime(String workEndTime) {
        this.workEndTime = workEndTime;
    }

    public String getExecuteUser() {
        return executeUser;
    }

    public void setExecuteUser(String executeUser) {
        this.executeUser = executeUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<String> getWorkDayDetailList() {
        return workDayDetailList;
    }

    public void setWorkDayDetailList(List<String> workDayDetailList) {
        this.workDayDetailList = workDayDetailList;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
