package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.AgencyBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IAgencyService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> 2017/11/26
 */
@Service(timeout = 50000)
public class AgencyServiceImpl implements IAgencyService {
    @Autowired
    private AgencyBL agencyBL;

    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    @Override
    public PageList<AgencyInfoDTO> getAgencyInfoDTO(AgencyInfoQueryDTO agencyInfoQueryDTO) {
        AssertUtils.notNull(agencyInfoQueryDTO.getCityId(), "城市id不能为空");
        return agencyBL.getAgencyInfoDTO(agencyInfoQueryDTO);
    }

}
