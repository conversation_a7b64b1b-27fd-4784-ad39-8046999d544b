package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.AisleBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.aisle.AisleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IAisleService;

/**
 * 巷道
 *
 * <AUTHOR>
 * @date 2024/11/2
 */
@Service(timeout = 30000)
public class AisleServiceImpl implements IAisleService {

    @Autowired
    private AisleBL aisleBL;

    @Override
    public void addAisle(AisleDTO addDTO) {
        aisleBL.addAisle(addDTO);
    }

    @Override
    public PageList<AisleDTO> pageListAisle(AisleQueryDTO queryDTO) {
        return aisleBL.pageListAisle(queryDTO);
    }

    @Override
    public List<AisleDTO> listAisleNoPage(AisleQueryDTO queryDTO) {
        return aisleBL.listAisleNoPage(queryDTO);
    }

    @Override
    public void updateAisle(AisleDTO updateDTO) {
        aisleBL.updateAisle(updateDTO);
    }

    @Override
    public void deleteAisle(AisleDTO deleteDTO) {
        aisleBL.deleteAisle(deleteDTO);
    }
}
