package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.AssetsBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsChangeRecordListQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsListQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IAssetsService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class AssetsServiceImpl implements IAssetsService {

    @Autowired
    private AssetsBL assetsBL;

    @Override
    public List<AssetsTypeDTO> findAssetsType() {
        return assetsBL.findAssetsType();
    }

    @Override
    public AssetsInfoDTO findAssetsInfoByCode(String code) {
        return assetsBL.findAssetsInfoByCode(code);
    }

    @Override
    public List<AssetsInfoDTO> findAssetsInfoByCodes(List<String> codes) {
        return assetsBL.findAssetsInfoByCodes(codes);
    }

    @Override
    public PageList<AssetsInfoDTO> findAssetsInfoByPage(AssetsListQueryDTO queryDTO) {
        return assetsBL.findAssetsInfoByPage(queryDTO);
    }

    @Override
    public List<AssetsChangeRecordDTO>
        findAssetsChangeRecordList(AssetsChangeRecordListQueryDTO changeRecordListQueryDTO) {
        return assetsBL.findAssetsChangeRecordList(changeRecordListQueryDTO);
    }

    @Override
    public void addAssets(AssetsInfoDTO addDTO) {
        assetsBL.addAssets(addDTO);
    }

    @Override
    public void operateAssets(AssetsOperationDTO operationDTO) {
        assetsBL.operateAssets(operationDTO);
    }

    @Override
    public void updateById(AssetsInfoDTO dto) {
        assetsBL.updateById(dto);
    }

    /**
     * 编辑资产信息
     */
    @Override
    public void updateAssetsInfo(AssetsUpdateDTO updateDTO) {
        assetsBL.updateAssetsInfo(updateDTO);
    }
}
