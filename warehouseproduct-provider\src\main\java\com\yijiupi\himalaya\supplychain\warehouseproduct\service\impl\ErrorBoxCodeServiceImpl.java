package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ErrorBoxCodeBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ErrorBoxCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ErrorBoxCodeSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IErrorBoxCodeService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 错误条码记录
 *
 * <AUTHOR>
 * @since 2019/1/8 15:21
 */
@Service(timeout = 300000)
public class ErrorBoxCodeServiceImpl implements IErrorBoxCodeService {

    @Autowired
    private ErrorBoxCodeBL errorBoxCodeBL;

    /**
     * 查看库存矫正记录
     */
    @Override
    public PageList<ErrorBoxCodeDTO> listErrorBoxCode(ErrorBoxCodeSO so) {
        return errorBoxCodeBL.listErrorBoxCode(so);
    }

    /**
     * 批量新增错误条码记录
     */
    @Override
    public void saveErrorBoxCodeList(List<ErrorBoxCodeDTO> dtos) {
        errorBoxCodeBL.saveErrorBoxCodeList(dtos);
    }

}
