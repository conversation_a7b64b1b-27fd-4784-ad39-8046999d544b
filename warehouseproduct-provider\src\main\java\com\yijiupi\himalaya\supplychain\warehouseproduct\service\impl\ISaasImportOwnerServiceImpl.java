package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.ExcelReadClient;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.dto.ExcelReadBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ImportOwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.PartnerStatusEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISaasImportOwnerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/2/8
 */
@Service
public class ISaasImportOwnerServiceImpl implements ISaasImportOwnerService {

    private Logger LOG = LoggerFactory.getLogger(ISaasImportOwnerServiceImpl.class);

    @Autowired
    private OwnerBL ownerBL;

    @Override
    public Boolean importOwner(ImportOwnerDTO importOwnerDTO) {
        LOG.info("importOwner批量货主导入>>{}--CityID:{}", JSON.toJSONString(importOwnerDTO));
        ExcelReadBO excelReadBO = new ExcelReadBO();
        excelReadBO.setUrl(importOwnerDTO.getUrl());
        List<Map<Integer, String>> maps = ExcelReadClient.readExcelMap(excelReadBO);
        if(CollectionUtils.isEmpty(maps)){
            throw new BusinessValidateException("货主导入不能为空");
        }
        List<OwnerDTO> list = new ArrayList<>();
        for (Map<Integer, String> map : maps) {
            OwnerDTO ownerDTO = new OwnerDTO();
            ownerDTO.setOwnerName(map.get(0));
            ownerDTO.setOwnerNo(map.get(1));
            ownerDTO.setUserName(map.get(2));
            ownerDTO.setMobileNo(map.get(3));
            ownerDTO.setOwnerType(Integer.valueOf(map.get(4)));
            list.add(ownerDTO);
        }
        list.forEach(it->it.setCityId(importOwnerDTO.getCityId()));
        list.forEach(it -> {
            setCommonParams(it);
        });
        LOG.info("OwnerDTOList批量货主插入>>{}",JSON.toJSONString(list));
        ownerBL.insertBatchOnwer(list);
        return true;
    }

    /**
     * 设置公共参数
     *
     * @param dto
     */
    private void setCommonParams(OwnerDTO dto) {
        dto.setState(new Byte(PartnerStatusEnum.ENABLE.getValue()));
        dto.setCreateTime(new Date());
        dto.setLastUpdateTime(new Date());
    }
}
