package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SaasBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISaasService;

@Service
public class ISaasServiceImpl implements ISaasService {

    @Autowired
    private SaasBL saasBL;

    @Override
    public PageList<PartnerManagerDTO> findPartnerManagerByCondition(PartnerManagerDTO dto) {
        return saasBL.findPartnerManagerByCondition(dto);
    }
}
