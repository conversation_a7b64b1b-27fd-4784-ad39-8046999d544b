package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;


import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.ExcelReadClient;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.dto.ExcelReadBO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.PartnerManagerBl;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ImportPartnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISassImportPartnerService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.UserInfoContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/2/8
 */
@Service
public class ISassImportPartnerServiceImpl implements ISassImportPartnerService {
    @Autowired
    private PartnerManagerBl partnerManagerBl;

    private Logger LOG = LoggerFactory.getLogger(ISassImportPartnerServiceImpl.class);
    @Override
    public Boolean importPartner(ImportPartnerDTO importPartnerDTO) {
        LOG.info("批量导入供应商信息>>{}--CityId:{}", JSON.toJSONString(importPartnerDTO));
        ExcelReadBO excelReadBO = new ExcelReadBO();
        excelReadBO.setUrl(importPartnerDTO.getUrl());
        List<Map<Integer, String>> maps = ExcelReadClient.readExcelMap(excelReadBO);
        if(CollectionUtils.isEmpty(maps)){
            throw new BusinessValidateException("供应商导入不能为空");
        }
        List<PartnerManagerDTO> partnerManagerDTOList = new ArrayList<>();
        for(Map<Integer, String> map:maps){
            PartnerManagerDTO partnerManagerDTO = new PartnerManagerDTO();
            partnerManagerDTO.setPartnerName(map.get(0));
            partnerManagerDTO.setPartnerNo(map.get(1));
            partnerManagerDTO.setLinkMan(map.get(2));
            partnerManagerDTO.setPhone(map.get(3));
            partnerManagerDTO.setStatus(Byte.valueOf(map.get(4)));
            partnerManagerDTOList.add(partnerManagerDTO);
        }
        partnerManagerDTOList.forEach(it -> {
            it.setCreateUser(Long.valueOf(UserInfoContext.get()));
            it.setLastUpdateUser(Long.valueOf(UserInfoContext.get()));
            it.setCityId(importPartnerDTO.getCityId());
        });
        LOG.info("批量导入供应商信息importPartnerDTO>>{}", JSON.toJSONString(importPartnerDTO));
        partnerManagerBl.insertBatchPartnerManager(partnerManagerDTOList);
        return null;
    }
}
