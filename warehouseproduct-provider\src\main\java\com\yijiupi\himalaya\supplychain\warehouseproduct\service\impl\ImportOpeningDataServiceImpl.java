package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ImportOpeningDataBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ImportOwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ImportOpeningDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

/**
 * 开仓作业初始数据导入
 *
 * <AUTHOR> 2022/10/25
 */
@Service
public class ImportOpeningDataServiceImpl implements ImportOpeningDataService {
    @Autowired
    private ImportOpeningDataBL importOpeningDataBL;

    @Override
    public void importOwner(MultipartFile file, Integer cityId) {
        ImportOwnerDTO dto = new ImportOwnerDTO();
        dto.setCityId(cityId);
        dto.setFile(file);
        importOpeningDataBL.importOwner(dto);
    }

    @Override
    public void importPartner(MultipartFile file, Integer cityId) {
        ImportOwnerDTO dto = new ImportOwnerDTO();
        dto.setCityId(cityId);
        dto.setFile(file);
        importOpeningDataBL.importPartner(dto);
    }
}
