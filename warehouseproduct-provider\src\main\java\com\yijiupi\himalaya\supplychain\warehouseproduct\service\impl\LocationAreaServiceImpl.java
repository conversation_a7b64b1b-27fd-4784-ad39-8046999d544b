package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationAreaBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.VesselBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;

/**
 * 货区
 * 
 * <AUTHOR> 2018/1/19
 */
@Service
public class LocationAreaServiceImpl implements LocationAreaService {
    @Autowired
    private LocationAreaBL locationAreaBL;

    @Autowired
    private VesselBL vesselBL;

    private static final Logger logger = LoggerFactory.getLogger(LocationAreaServiceImpl.class);

    @Override
    public LocationReturnDTO findLocationById(String id) {
        AssertUtils.notNull(id, "id不能为null");
        return locationAreaBL.findLocationById(id);
    }

    @Override
    public List<LocationReturnDTO> findLocationListById(List<String> idList) {
        AssertUtils.notEmpty(idList, "id不能为null");
        return locationAreaBL.findLocationListById(idList);
    }

    @Override
    public List<LocationReturnDTO> findLocationAreaListById(List<String> idList) {
        AssertUtils.notEmpty(idList, "id不能为null");
        return locationAreaBL.findLocationAreaListById(idList);
    }

    /**
     * 新增货区
     * 
     * @param locationAreaDTO
     */
    @Override
    public void addLocationArea(LocationAreaDTO locationAreaDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getCityId(), "cityId为null");
        AssertUtils.notNull(locationAreaDTO.getWarehouseId(), "仓库id为null");
        AssertUtils.notNull(locationAreaDTO.getArea(), "货区为null");
        AssertUtils.notNull(locationAreaDTO.getUserId(), "操作人id为null");
        AssertUtils.isTrue(locationAreaDTO.getArea().length() <= 4, "货区名称不能超过4位字符");
        locationAreaBL.addLocationArea(locationAreaDTO);
    }

    /**
     * 删除货区
     * 
     * @param locationAreaDTO
     */
    @Override
    public void deleteLocationArea(LocationAreaDTO locationAreaDTO, OperatorDTO operatorDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getId(), "id不能为null");
        locationAreaBL.deleteLocationArea(locationAreaDTO, operatorDTO);
    }

    /**
     * 编辑货区
     * 
     * @param locationAreaDTO
     */
    @Override
    public void updateLocationArea(LocationAreaDTO locationAreaDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getId(), "id不能为null");
        if (locationAreaDTO.getArea() != null) {
            AssertUtils.isTrue(locationAreaDTO.getArea().length() <= 4, "货区名称不能超过4位字符");
        }
        AssertUtils.notNull(locationAreaDTO.getUserId(), "操作人id不能为null");
        locationAreaBL.updateLocationArea(locationAreaDTO);
    }

    /**
     * 查询货区
     */
    @Override
    public PageList<LocationAreaReturnDTO> getLocationArea(LocationAreaDTO locationAreaDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getWarehouseId(), "仓库id为null");
        if (locationAreaDTO.getPageNum() == null || locationAreaDTO.getPageNum() < 0) {
            locationAreaDTO.setPageNum(1);
        }
        if (locationAreaDTO.getPageSize() == null || locationAreaDTO.getPageNum() < 0) {
            locationAreaDTO.setPageSize(20);
        }
        StopWatch stopWatch = new StopWatch("查询货区");
        stopWatch.start();
        PageList<LocationAreaReturnDTO> result = locationAreaBL.getLocationArea(locationAreaDTO);
        stopWatch.stop();
        logger.info("查询货区耗时统计: \n{}", stopWatch.prettyPrint());
        return result;
    }

    /**
     * 不分页
     * 
     * @param locationAreaDTO
     * @return
     */
    @Override
    public List<LocationAreaReturnDTO> getLocationAreaNoPage(LocationAreaListDTO locationAreaDTO) {
        AssertUtils.notNull(locationAreaDTO, "DTO为null");
        AssertUtils.notNull(locationAreaDTO.getWarehouseId(), "仓库id为null");
        return locationAreaBL.getLocationAreaNoPage(locationAreaDTO);
    }

    /**
     * 根据货位或者货区名称，查询仓库指定货位
     */
    @Override
    public LocationInfoDTO getLocationByName(String locatioName, Integer warehouseId) {
        return locationAreaBL.getLocationByName(locatioName, warehouseId);
    }

    /**
     * 根据仓库ID查询货区并检查货区是否创建
     * 
     * @param warehouseId
     * @return
     */
    @Override
    public CargoAreaCheckResultDTO isCreatedCargoArea(Integer warehouseId) {
        return locationAreaBL.isCreatedCargoArea(warehouseId);
    }

    /**
     * 根据货位id查询货位和货区信息
     */
    @Override
    public List<LoactionDTO> findLocationAndAreaInfoById(List<Long> idList) {
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(idList), "货位id不能为空");
        return locationAreaBL.findLocationAndAreaInfoById(idList);
    }

    /**
     * 根据skuid查询容器信息
     */
    @Override
    public Map<Long, VesselDetailsDTO> listSKUBesselDetails(List<Long> skuIdList, Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Idb不能为空");
        AssertUtils.notEmpty(skuIdList, "skuid不能为空");
        return vesselBL.listSKUBesselDetails(skuIdList, warehouseId);
    }

    /**
     * 批量导入货区信息
     * 
     * @param file
     * @return
     */
    @Override
    public void importLocationArea(MultipartFile file, Integer cityId, Integer warehouseId) {
        ImportLocationDTO dto = new ImportLocationDTO();
        dto.setCityId(cityId);
        dto.setWarehouseId(warehouseId);
        dto.setFile(file);
        locationAreaBL.importLocationArea(dto);
    }

    @Override
    public List<LocationReturnDTO> findLocationAreaListExcludeDefective(List<String> idList) {
        AssertUtils.notEmpty(idList, "货位ids不能为空");
        return locationAreaBL.findLocationAreaListExcludeDefective(idList);
    }
}
