package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.LocationRuleBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.outlocation.LocationFilterBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.OutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.RecommendOutLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleManageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * 出库位业务
 * <AUTHOR>
 */
@Service
public class LocationRuleManageServiceImpl implements ILocationRuleManageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationRuleManageServiceImpl.class);

    @Resource
    private LocationRuleBL locationRuleBL;
    @Resource
    private LocationFilterBL locationFilterBL;

    /**
     * 保存出库位规则
     */
    @Override
    public void saveRule(List<LocationRuleDTO> locationRuleDTOList) {
        LOGGER.info("保存出库位规则 LocationRuleManageServiceImpl.saveRule locationRuleDTOList={}",
            JSON.toJSONString(locationRuleDTOList));
        locationRuleBL.saveRule(locationRuleDTOList);
    }

    /**
     * 保存规则 过滤掉已存在的
     *
     * @param locationRuleDTOList 出库位规则
     */
    @Override
    public void saveRuleIfAbsent(List<LocationRuleDTO> locationRuleDTOList) {
        LOGGER.info("保存出库位规则 saveRuleIfAbsent: {}", JSON.toJSONString(locationRuleDTOList));
        locationRuleBL.saveRuleIfAbsent(locationRuleDTOList);
    }

    /**
     * 获取出库位
     */
    @Override
    public List<LocationRuleDTO> getLocation(List<LocationRuleDTO> locationRuleDTOList) {
        // todo 目前只按照行政区来做
        LOGGER.info("获取出库位 LocationRuleManageServiceImpl.getLocation locationRuleDTOList={}",
            JSON.toJSONString(locationRuleDTOList));
        return locationRuleBL.getLocation(locationRuleDTOList);
    }

    /**
     * 设置默认出库位
     */
    @Override
    public void saveDefault(LocationRuleDTO locationQueryDTO) {
        locationRuleBL.saveDefault(locationQueryDTO);
    }

    /**
     * 获取推荐出库位
     */
    @Override
    public List<LocationRuleDTO> getOutStockLocation(List<RecommendOutLocationQueryDTO> queryList) {
        LOGGER.info("获取出库位 LocationRuleManageServiceImpl.getLocation locationRuleDTOList={}",
            JSON.toJSONString(queryList));
        return locationFilterBL.filterExistLocationRule(locationRuleBL.getOutStockLocation(queryList));
    }

    /**
     * @param outLocationQueryDTO
     * @return
     */
    @Override
    public List<LocationRuleDTO> getOutLocation(OutLocationQueryDTO outLocationQueryDTO) {
        return locationFilterBL.filterExistLocationRule(locationRuleBL.getOutLocation(outLocationQueryDTO));
    }

    /**
     * @param ids
     */
    @Override
    public void deleteByIds(List<Long> ids) {
        locationRuleBL.deleteByIds(ids);
    }

    /**
     * 获取出库位 (普通订单)
     *
     * @param queryList 查询条件
     */
    @Override
    public List<LocationRuleDTO> getOutStockLocationForNormalOrder(List<RecommendOutLocationQueryDTO> queryList) {
        return locationFilterBL.filterExistLocationRule(locationRuleBL.getOutStockLocationForNormalOrder(queryList));
    }

    /**
     * 获取出库位 (内配订单)
     *
     * @param queryList 查询条件
     */
    @Override
    public List<LocationRuleDTO> getOutStockLocationForAllotOrder(List<RecommendOutLocationQueryDTO> queryList) {
        return locationFilterBL.filterExistLocationRule(locationRuleBL.getOutStockLocationForAllotOrder(queryList));
    }

}
