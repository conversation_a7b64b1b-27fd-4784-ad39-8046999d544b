package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.LocationRuleBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRuleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.locationrule.LocationRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationRuleQueryService;

/**
 * <AUTHOR> 出库位规则查询
 */
@Service
public class LocationRuleQueryServiceImpl implements ILocationRuleQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationRuleQueryServiceImpl.class);

    @Autowired
    private LocationRuleBL locationRule;

    /**
     * 根据warehouseId locationId查询出库位规则信息
     *
     * @param locationRuleDTOList
     * @return
     */
    @Override
    public List<LocationRuleDTO> listLocationRule(List<LocationRuleDTO> locationRuleDTOList) {
        return locationRule.listLocationRule(locationRuleDTOList);
    }

    /**
     * 查询出库位列表
     * 
     * @param locationRuleDTO
     * @return
     */
    @Override
    public List<LocationRuleDTO> listLocation(LocationRuleDTO locationRuleDTO) {
        return locationRule.listLocation(locationRuleDTO);
    }

    /**
     * 查找出库位
     *
     * @param locationRuleQueryDTO
     * @return
     */
    @Override
    public List<LocationRuleDTO> findLocationRuleByCon(LocationRuleQueryDTO locationRuleQueryDTO) {
        return locationRule.findLocationRuleByCon(locationRuleQueryDTO);
    }
}
