package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StoreWareHouseBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.FixLocationAreaInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.LocationSequenceByRouteInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.LocationSequenceByRouteInfoResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationSequenceQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.util.MyListUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2017/11/24
 */
@Service(timeout = 50000)
public class LocationServiceImpl implements ILocationService {
    @Autowired
    private LocationBL locationBL;
    @Autowired
    private StoreWareHouseBL storeWareHouseBL;

    /**
     * 新增经销商与货位关系
     */
    @Override
    public void addAgencyLocation(AgencyConnectLocationDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        locationBL.addAgencyLocation(dto);
    }

    /**
     * 根据经销商id和仓库id查询货位信息
     */
    @Override
    public List<LocationInfoDTO> getLocation(Long agencyId, Integer warehouseId) {
        AssertUtils.notNull(agencyId, "经销商id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return locationBL.getLocation(agencyId, warehouseId);
    }

    /**
     * 根据经销商Id，仓库Id和SKUID获取对应的货位信息
     */
    @Override
    public List<LocationInfoDTO> getLocation(Long agencyId, Integer warehouseId, Long productSkuId) {
        AssertUtils.notNull(agencyId, "经销商id不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(productSkuId, "产品skuId不能为空");
        return locationBL.getLocation(agencyId, warehouseId, productSkuId);
    }

    /**
     * 查询未被关联的货位,供经销商选择.
     */
    @Override
    public PageList<LocationInfoDTO> getUnusedLocation(GetUnusedLocationDTO getUnusedLocationDTO) {
        AssertUtils.notNull(getUnusedLocationDTO.getCityId(), "城市id不能为空");
        return locationBL.getUnusedLocation(getUnusedLocationDTO);
    }

    /**
     * 删除经销商与货位关系
     */
    @Override
    public void removeAgencyLocation(AgencyConnectLocationDTO agencyConnectLocationDTO) {
        locationBL.removeAgencyLocation(agencyConnectLocationDTO);
    }

    /**
     * 根据经销商查询该经销商所有的货位详细信息
     */
    @Override
    public List<LocationInfoDTO> getLocationInfoByAgencyId(Long agencyId) {
        AssertUtils.notNull(agencyId, "城市id不能为空");
        return locationBL.getLocationInfoByAgencyId(agencyId);
    }

    /**
     * 根据经销商id,删除经销商的所有货位配置.
     */
    @Override
    public void removeAgencyLocationByAgencyId(Long agencyId) {
        locationBL.removeAgencyLocationByAgencyId(agencyId);
    }

    /**
     * 查询出已经有货位关联的经销商的id
     */
    @Override
    public List<Long> getAgencyIdByLocation() {
        return locationBL.getAgencyIdByLocation();
    }

    /**
     * @param locationModifyDTO
     * <AUTHOR>
     */
    @Override
    public void deleteLocation(LocationModifyDTO locationModifyDTO, OperatorDTO operatorDTO) {
        locationBL.deleteLocation(locationModifyDTO, operatorDTO);
    }

    /**
     * 修改货位信息
     *
     * @param locationModifyDTO
     * <AUTHOR>
     */
    @Override
    public void modifyLocation(LocationModifyDTO locationModifyDTO) {
        locationBL.modifyLocation(locationModifyDTO);
    }

    /**
     * 给导入的excel提供修改货位信息的接口
     *
     * @param locationModifyWithWarehouseIdDTO
     * <AUTHOR>
     */
    @Override
    public void modifyLocation(LocationModifyWithWarehouseIdDTO locationModifyWithWarehouseIdDTO) {
        locationBL.modifyLocation(locationModifyWithWarehouseIdDTO);
    }

    /**
     * 根据仓库ID，skuIds获取对应产品货位顺序
     *
     * @param query
     * @return
     */
    @Override
    public List<LocationSequenceReturnDTO> findLocationSequenceBySkuIds(LocationSequenceQuery query) {
        return locationBL.findLocationSequenceBySkuIds(query);
    }

    /**
     * 根据货位名称和仓库
     *
     * @param locationCategoryQuery
     * @return
     */
    @Override
    public List<LocationCategoryDTO> findLocationListByName(LocationCategoryQuery locationCategoryQuery) {
        AssertUtils.notNull(locationCategoryQuery.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(locationCategoryQuery.getNameList(), "货位名称集合不能为空");
        return locationBL.findLocationListByName(locationCategoryQuery);
    }

    /**
     * 根据货位id查询对应货区下指定货位类型的所有货位信息
     */
    @Override
    public List<LoactionDTO> findLocationListByIdAndCategory(LocationQueryDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        return locationBL.findLocationListByIdAndCategory(dto);
    }

    /**
     * 查询仓库产品货位信息
     *
     * @param query
     * @return
     */
    @Override
    public List<ProductLocationInfoDTO> findLocationInfoBySkuIds(LocationSequenceQuery query) {
        return locationBL.findLocationInfoBySkuIds(query);
    }

    /**
     * 根据货位/货区名称查询货位/货区信息
     *
     * @return
     */
    @Override
    public LoactionDTO getLocationByName(Integer warehouseId, String locationName) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(locationName, "货位名称不能为空");
        return locationBL.getLocationByName(warehouseId, locationName);
    }

    /**
     * 根据货位/货区名称批量查询货位/货区信息
     *
     * @return
     */
    @Override
    public List<LoactionDTO> getLocationByNames(Integer warehouseId, List<String> locationNameList) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notEmpty(locationNameList, "货位名称不能为空");
        return locationBL.getLocationByNames(warehouseId, locationNameList);
    }

    /**
     * 根据货位id查询货位信息
     *
     * @param locationIds
     * @return
     */
    @Override
    public List<LoactionDTO> findLocationByIds(List<Long> locationIds) {
        AssertUtils.notEmpty(locationIds, "货位id不能为空");
        return locationBL.findLocationByIds(locationIds);
    }

    /**
     * 分页条件查询货位信息
     *
     * @param locationQueryDTO
     * @return
     */
    @Override
    public PageList<LoactionDTO> pageListLocation(LocationInfoQueryDTO locationQueryDTO) {
        return locationBL.pageListLocation(locationQueryDTO);
    }

    /**
     * 分页条件查询货位范围信息
     */
    @Override
    public PageList<LoactionDTO> locationRangePageList(LocationRangeQueryDTO rangeQueryDTO) {
        return locationBL.locationRangePageList(rangeQueryDTO);
    }

    /**
     * 根据名称或者Id，校正错误的货位信息，并返回
     *
     * @return
     */
    @Override
    public Map<Long, String> findErrorLocation(Integer warehouseId, List<AdjustLoactionInfoDTO> lstLocations) {
        if (CollectionUtils.isEmpty(lstLocations)) {
            return new HashMap<>(16);
        }
        return locationBL.findErrorLocation(warehouseId, lstLocations);
    }

    /**
     * 根据仓库id+类型查询货位或货区，不存在时则自动创建货位
     *
     * @return
     */
    @Override
    public LocationReturnDTO getLocationByWarehouseIdByAutoCreate(Integer warehouseId, Integer cityId,
                                                                  Byte subcategory) {
        return locationBL.getLocationByWarehouseIdByAutoCreate(warehouseId, cityId, subcategory);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveBatch(LoactionVisualUpdateDTO loactionVisualUpdateDTO) {
        AssertUtils.notNull(loactionVisualUpdateDTO, "参数不能为空");
        AssertUtils.notNull(loactionVisualUpdateDTO.getLocationAreaId(), "货区id不能为空");

        List<LoactionDTO> lstArea = locationBL.findLocationByIds(Collections.singletonList(loactionVisualUpdateDTO.getLocationAreaId()));
        if (CollectionUtils.isEmpty(lstArea)) {
            throw new BusinessValidateException("货区不存在，请检查");
        }
        // 获取ID与当前仓库ID不一致，拦截，处理批量导入货位的场景
        if (lstArea.stream().anyMatch(p -> !Objects.equals(p.getWarehouseId(), loactionVisualUpdateDTO.getWarehouseId()))) {
            throw new BusinessValidateException("货区不属于该仓库，请检查");
        }

        /** 判断删除的货位是否存在 */
        if (!CollectionUtils.isEmpty(loactionVisualUpdateDTO.getDeleteLocationDTOs())) {
            List<Long> ids = new ArrayList<>();
            loactionVisualUpdateDTO.getDeleteLocationDTOs().forEach(deleteDto -> ids.add(deleteDto.getId()));
            locationBL.deleteByIds(ids);
        }

        if (CollectionUtils.isEmpty(loactionVisualUpdateDTO.getSaveLocationDTOs())) {
            return;
        }

        /** 组装数据 */
        Long locationAreaId = loactionVisualUpdateDTO.getLocationAreaId();
        List<LoactionDTO> newdtos = loactionVisualUpdateDTO.getSaveLocationDTOs();
        newdtos.forEach(dto -> dto.setArea_Id(locationAreaId));

        /** 查询原有货位数据 */
        List<LoactionDTO> origianlLocationDTOs = locationBL.findLocationListByAreaId(locationAreaId);
        if (CollectionUtils.isEmpty(origianlLocationDTOs)) {
            /** 为空 直接新增 */
            newdtos.forEach(dto -> {
                dto.setIsChaosPut((byte) 1);
                dto.setIsChaosBatch((byte) 1);
                dto.setCategory((byte) 0);
                dto.setLocationCapacity(99999);
            });
            locationBL.batchInsertLocation(newdtos, loactionVisualUpdateDTO.getWarehouseId(),
                    loactionVisualUpdateDTO.getCityId());
            return;
        }

        /** 比较两个集合不同的部分，缺少的直接新增，不同的update */
        if (newdtos.equals(origianlLocationDTOs)) {
            /** 相等，直接返回，不用更改 */
            return;
        }

        Map<String, List> compareResult = MyListUtil.getInfoBetweenCollections(newdtos, origianlLocationDTOs);
        List<LoactionDTO> sameInfo = compareResult.get("same");

        newdtos.removeAll(sameInfo);
        if (!CollectionUtils.isEmpty(newdtos)) {
            /** 帅选出 有id - update 无id-则新增 */
            List<LoactionDTO> addDTOs = new ArrayList<>();
            List<LoactionDTO> updateDTOs = new ArrayList<>();
            newdtos.forEach(newDTO -> {
                newDTO.setUserId(loactionVisualUpdateDTO.getOperatorId());
                if (newDTO.getId() == null) {
                    newDTO.setIsChaosPut((byte) 1);
                    newDTO.setIsChaosBatch((byte) 1);
                    newDTO.setCategory((byte) 0);
                    newDTO.setLocationCapacity(99999);
                    addDTOs.add(newDTO);
                } else {
                    updateDTOs.add(newDTO);
                }
            });
            if (!CollectionUtils.isEmpty(addDTOs)) {
                locationBL.batchInsertLocation(addDTOs, loactionVisualUpdateDTO.getWarehouseId(),
                        loactionVisualUpdateDTO.getCityId());
            }
            if (!CollectionUtils.isEmpty(updateDTOs)) {
                updateDTOs.forEach(updateDTO -> locationBL.updateLocation(updateDTO));
            }
        }
    }

    @Override
    public PageList<LocationVisualDTO> listLocationPage(LocationInfoQueryDTO locationInfoQueryDTO) {
        PageList<LoactionDTO> loactionDTOPage = locationBL.listLocationPage(locationInfoQueryDTO);
        PageList<LocationVisualDTO> result = new PageList<>();
        result.setPager(loactionDTOPage.getPager());
        List<LocationVisualDTO> dataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loactionDTOPage.getDataList())) {
            List<LoactionDTO> loactionDTOList = loactionDTOPage.getDataList();
            List<Long> locationIds = new ArrayList<>();
            /** 获取货位id值,获取StorageNum值 */
            loactionDTOList.forEach(item -> locationIds.add(item.getId()));
            Map<Long, BigDecimal> storageNumMap = locationBL.getLocationStoreNumByLocationId(locationIds);

            loactionDTOList.forEach(item -> {
                LocationVisualDTO visualDTO = new LocationVisualDTO();
                BeanUtils.copyProperties(item, visualDTO);
                visualDTO.setStorageNum(
                        storageNumMap.get(item.getId()) == null ? BigDecimal.ZERO : storageNumMap.get(item.getId()));
                dataList.add(visualDTO);
            });
        }
        result.setDataList(dataList);
        return result;
    }

    @Override
    public List<LocationVisualDTO> listLocation(LocationQueryDTO locationQueryDTO) {
        List<LoactionDTO> loactionDTOs = locationBL.listLocation(locationQueryDTO);
        List<LocationVisualDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(loactionDTOs)) {
            return result;
        }
        List<Long> locationIds = new ArrayList<>();
        /** 获取货位id值,获取StorageNum值 */
        loactionDTOs.forEach(item -> locationIds.add(item.getId()));
        Map<Long, BigDecimal> storageNumMap = locationBL.getLocationStoreNumByLocationId(locationIds);

        loactionDTOs.forEach(item -> {
            LocationVisualDTO visualDTO = new LocationVisualDTO();
            BeanUtils.copyProperties(item, visualDTO);
            visualDTO.setStorageNum(
                    storageNumMap.get(item.getId()) == null ? BigDecimal.ZERO : storageNumMap.get(item.getId()));
            result.add(visualDTO);
        });
        return result;
    }

    @Override
    public void updateLocation(LoactionDTO dto) {
        locationBL.updateLocation(dto);
    }

    /**
     * 是否全量拉取货位信息（PDA）
     */
    @Override
    public boolean isFullLoadLocation(LocationInfoQueryDTO queryDTO) {
        return locationBL.isFullLoadLocation(queryDTO);
    }

    @Override
    public List<Long> checkLocation(List<LoactionDTO> locationDTOS) {
        return locationBL.checkLocation(locationDTOS);
    }

    /**
     * 分页条件查询暂存位信息并校验播种任务
     *
     * @param locationQueryDTO
     * @return
     */
    @Override
    public PageList<LoactionDTO> pageListLocationWithoutSowTask(LocationInfoQueryDTO locationQueryDTO) {
        return locationBL.pageListLocationWithoutSowTask(locationQueryDTO);
    }

    @Override
    public void deleteWarehouseLocation(LocationModifyDTO locationModifyDTO) {
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setUserId(locationModifyDTO.getUserId());
        locationBL.deleteLocation(locationModifyDTO, operatorDTO);
    }

    /**
     * 根据动销产品数据分类对应货位
     */
    @Override
    public PageList<LocationVisualDTO> listLoactionInventoryInfo(LocationQueryDTO locationQueryDTO) {
        AssertUtils.notNull(locationQueryDTO, "参数不能为空");
        return locationBL.listLocationInventoryInfo(locationQueryDTO);
    }

    @Override
    public List<LocationWithAreaDTO> findLocationAndAreaByIds(List<Long> locationIds) {
        AssertUtils.notEmpty(locationIds, "货位id不能为空");
        return locationBL.findLocationAndAreaByIds(locationIds);
    }

    @Override
    public List<LoactionDTO> findVesselLocationByIds(LocationQueryDTO queryDTO) {
        return locationBL.findVesselLocationByIds(queryDTO);
    }

    /**
     * 修复货位和货区不匹配工具
     *
     * @param fixDTOList
     */
    @Override
    public void fixLocationAreaInfo(List<FixLocationAreaInfoDTO> fixDTOList) {
        locationBL.fixLocationAreaInfo(fixDTOList);
    }

    @Override
    public void updateLocationBatchById(LocationQueryDTO modifyDTO) {
        locationBL.updateLocationBatchById(modifyDTO);
    }

    @Override
    public void updatePalletCount(LoactionDTO updateDTO) {
        locationBL.updatePalletCount(updateDTO);
    }

    @Override
    public void updatePalletCountBatch(LoactionPalletCountUpdateDTO updateDTO) {
        locationBL.updatePalletCountBatch(updateDTO);
    }

    /**
     * 根据线路ID，查询对应出库位对应的货位序号（批量）
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<LocationSequenceByRouteInfoResultDTO> findLocationSequenceByRouteInfo(LocationSequenceByRouteInfoQueryDTO queryDTO) {
        boolean equation =
                org.springframework.util.CollectionUtils.isEmpty(queryDTO.getRouteIds()) && org.springframework.util.CollectionUtils.isEmpty(queryDTO.getAreaIds());
        AssertUtils.isTrue(!equation, "线路片区信息不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");


        return locationBL.findLocationSequenceByRouteInfo(queryDTO);
    }
}