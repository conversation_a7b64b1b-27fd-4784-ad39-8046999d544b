package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.BatchImportOwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * 货主接口实现
 *
 * <AUTHOR>
 * @date 2018/11/15 17:50
 */
@Service(timeout = 60000)
public class NewOwnerServiceImpl implements OwnerService {

    @Autowired
    private OwnerBL ownerBL;

    @Override
    public String getDefaultOwnerName() {
        return ownerBL.getDefaultOwnerName();
    }

    @Override
    public void insertOrUpdateOwnerByEasyBusiness(List<OwnerDTO> dtoList) {
        ownerBL.insertOrUpdateOwnerByEasyBusiness(dtoList);
    }

    @Override
    public void insertOrUpdateOwner(List<OwnerDTO> dtoList) {
        ownerBL.insertOrUpdateOwner(dtoList);
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByOrgId(Integer orgId) {
        return ownerBL.findOwnerByOrgId(orgId);
    }

    /**
     * 根据ERP供应商，查找供应链货主ID
     *
     * @param refPartnerIds
     * @param ownerType OwnerTypeConst.供应商
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByErpId(List<String> refPartnerIds, Integer ownerType) {
        return ownerBL.findOwnerByErpId(refPartnerIds, ownerType);
    }

    /**
     * 根据仓库查询所有有货的货主
     *
     * @param warehouseId
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByWarehouseId(Integer warehouseId, Integer ownerType) {
        return ownerBL.findOwnerByWarehouseId(warehouseId, ownerType);
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByOrgIdAndType(Integer orgId, Integer ownerType) {
        return ownerBL.findOwnerByOrgIdAndType(orgId, ownerType);
    }

    /**
     * 根据城市查询所有货主
     *
     * @param orgId
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByOrgIdAndTypeList(Integer orgId, List<Integer> ownerTypeList) {
        return ownerBL.findOwnerByOrgIdAndTypeList(orgId, ownerTypeList);
    }

    /**
     * 根据货主id获取货主信息
     *
     * @return
     */
    @Override
    public OwnerDTO getOwnerById(Long id) {
        return ownerBL.getOwnerById(id);
    }

    /**
     * 根据货主id批量获取货主信息
     *
     * @return
     */
    @Override
    public List<OwnerDTO> listOwnerByIds(List<Long> ids) {
        return ownerBL.listOwnerByIds(ids);
    }

    // 1、根据条件查询
    // 2、更新状态
    // 3、批量导入，下载模板

    /**
     * 启用停用货主
     *
     * @param id
     * @param status
     * @return
     */
    @Override
    public void updateStatus(String id, Byte status, Long userId) {
        ownerBL.updateStatus(id, status, userId);
    }

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    @Override
    public PageList<OwnerDTO> findOwnerByCondition(OwnerQueryDTO dto) {
        return ownerBL.findOwnerByCondition(dto);
    }

    /**
     * 新增或更新业务伙伴
     *
     * @param dto
     * @return
     */
    @Override
    public OwnerDTO insertOrUpdateOwnerManager(OwnerDTO dto) {
        AssertUtils.notNull(dto, "保存或更新更新数据不能为空");
        AssertUtils.isTrue(StringUtils.isNotEmpty(dto.getOwnerName()), "名称不能为空");
        AssertUtils.isTrue(StringUtils.isNotEmpty(dto.getOwnerNo()), "编码不能为空");
        AssertUtils.notNull(dto.getOwnerType(), "类型不能为空");
        return ownerBL.insertOrUpdateOwnerManager(dto);
    }

    /**
     * 校验编号的唯一性
     *
     * @param ownerNo
     * @return
     */
    @Override
    public boolean checkOwnerNoUniq(String ownerNo, String id) {
        AssertUtils.isTrue(StringUtils.isNotEmpty(ownerNo), "编号不能为空");
        return ownerBL.checkOwnerNoUniq(ownerNo, id);
    }

    /**
     * 批量更新
     */
    @Override
    public void insertBatchOnwer(List<OwnerDTO> list) {
        AssertUtils.notEmpty(list, "更新数据不能为空");
        ownerBL.insertBatchOnwer(list);
    }

    @Override
    public Map<String, OwnerDTO> findOwnerByOwnerNos(List<String> ownerNos) {
        return ownerBL.findOwnerByOwnerNos(ownerNos);
    }

    @Override
    public List<OwnerDTO> listOwnerByCondition(OwnerQueryDTO dto) {
        return ownerBL.listOwnerByCondition(dto);
    }

    /**
     * 根据外部关联id或货主id查询我们自己的货主ID
     *
     * @return
     */
    @Override
    public Map<String, Long> getOwnerIdMap(List<String> ownerIds) {
        return ownerBL.getOwnerIdMap(ownerIds);
    }

    /**
     * 根据货主id获取货主名称
     *
     * @return
     */
    @Override
    public Map<Long, String> getOwnerNameMap(List<Long> ids) {
        return ownerBL.getOwnerNameMap(ids);
    }

    /**
     * 根据仓库查询所有产品的货主
     *
     * @param warehouseId
     * @return
     */
    @Override
    public List<OwnerDTO> findOwnerByWarehouseIdWithSKU(Integer warehouseId, Integer ownerType) {
        return ownerBL.findOwnerByWarehouseIdWithSKU(warehouseId, ownerType);
    }

    /**
     * 根据仓库查询所有产品的货主
     *
     * @param warehouseId
     * @return
     */
    @Override
    public List<OwnerDTO> listOwnerByWarehouseIdWithSKU(Integer warehouseId) {
        return ownerBL.findOwnerByWarehouseIdWithSKU(warehouseId, null);
    }

    /**
     * 根据条件查询业务伙伴
     *
     * @param dto
     * @return
     */
    @Override
    public PageList<OwnerDTO> findOwnerListByCondition(OwnerQueryDTO dto) {
        return ownerBL.findOwnerListByCondition(dto);
    }

    /**
     * 校验编号的唯一性
     *
     * @param dto
     * @return
     */
    @Override
    public boolean checkOwnerNoUniqueness(OwnerDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        AssertUtils.notNull(dto.getOwnerNo(), "货主编号不能为空");
        return ownerBL.checkOwnerNoUniqueness(dto);
    }

    /**
     * 启用停用货主
     *
     * @param dto
     * @return
     */
    @Override
    public void updateOwnerState(OwnerDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        AssertUtils.notNull(dto.getId(), "货主id不能为空");
        AssertUtils.notNull(dto.getState(), "变更状态不能为空");
        AssertUtils.notNull(dto.getLastUpdateUserId(), "更新人不能为空");
        ownerBL.updateStatus(String.valueOf(dto.getId()), dto.getState(), dto.getLastUpdateUserId());
    }

    /**
     * 批量导入
     */
    @Override
    public void productOwnerUpload(BatchImportOwnerDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空");
        AssertUtils.notNull(dto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(dto.getFile(), "上传失败，无法找到文件");
        AssertUtils.notNull(dto.getFile().getOriginalFilename(), "导入文件不能为空");
        ownerBL.productOwnerUpload(dto);
    }
}
