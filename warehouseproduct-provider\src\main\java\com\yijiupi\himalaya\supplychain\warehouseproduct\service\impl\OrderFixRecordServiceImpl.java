package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OrderFixRecordBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderfixrecord.OrderFixRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOrderFixRecordService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service(timeout = 50000)
public class OrderFixRecordServiceImpl implements IOrderFixRecordService {

    @Autowired
    private OrderFixRecordBL orderFixRecordBL;

    @Override
    public void batchSave(List<OrderFixRecordDTO> orderFixRecordDTOS) {
        orderFixRecordBL.batchSave(orderFixRecordDTOS);
    }

    @Override
    public List<OrderFixRecordDTO> findAll() {
        return orderFixRecordBL.findAll();
    }
}
