package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOwnerService;

/**
 * 货主接口实现
 *
 * <AUTHOR>
 * @date 2018/11/15 17:50
 */
@Service(timeout = 60000)
public class OwnerServiceImpl implements IOwnerService {

    /**
     * 获取灰度仓库的KEY
     */
    private static final String DEFAULT_OWNER_NAME = "DefaultOwnerName";

    @Autowired
    private OwnerBL ownerBL;

    @Reference
    private IVariableValueService variableValueService;

    @Override
    public List<OwnerDTO> getOwnerInfoDTO(OwnerQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "查询仓库货主，参数不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库Id不能为空！");
        return ownerBL.getOwnerInfoDTO(queryDTO);
    }

    @Override
    public List<OwnerDTO> getOwnerIdByErpOwnerId(List<String> erpOwnerId) {
        AssertUtils.notEmpty(erpOwnerId, "参数不能为空！");
        return ownerBL.getOwnerIdByErpOwnerId(erpOwnerId);
    }
}
