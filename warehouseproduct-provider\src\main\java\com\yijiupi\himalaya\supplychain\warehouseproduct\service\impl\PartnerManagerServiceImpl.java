package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.PartnerManagerBl;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPartnerManagerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-08-20
 */
@Service(timeout = 60000)
public class PartnerManagerServiceImpl implements IPartnerManagerService {
    @Autowired
    private PartnerManagerBl partnerManagerBl;

    /**
     * 根据id获取业务伙伴详情
     *
     * @param id
     * @return
     */
    @Override
    public PartnerManagerDTO getPartnerManagerDetailById(String id) {
        return partnerManagerBl.getPartnerManagerDetailById(id);
    }

    /**
     * 根据名称或者状态获取业务伙伴
     *
     * @param dto
     * @return
     */
    @Override
    public PageList<PartnerManagerDTO> findPartnerManagerByCondition(PartnerManagerDTO dto) {
        return partnerManagerBl.findPartnerManagerByCondition(dto);
    }

    /**
     * 新增或更新业务伙伴
     *
     * @param dto
     * @return
     */
    @Override
    public PartnerManagerDTO insertOrUpdatePartnerManager(PartnerManagerDTO dto) {
        AssertUtils.notNull(dto, "保存或更新更新数据不能为空");
        AssertUtils.isTrue(StringUtils.isNotEmpty(dto.getPartnerName()), "名称不能为空");
        AssertUtils.isTrue(StringUtils.isNotEmpty(dto.getPartnerNo()), "编码不能为空");
        AssertUtils.notNull(dto.getType(), "类型不能为空");
        return partnerManagerBl.insertOrUpdatePartnerManager(dto);
    }

    /**
     * 启用停用业务伙伴
     *
     * @param id
     * @param status
     * @return
     */
    @Override
    public void updateStatus(String id, Byte status, Long userId) {
        partnerManagerBl.updateStatus(id, status, userId);
    }

    /**
     * 批量更新
     */
    @Override
    public void insertBatchPartnerManager(List<PartnerManagerDTO> list) {
        AssertUtils.notEmpty(list, "更新数据不能为空");
        partnerManagerBl.insertBatchPartnerManager(list);
    }

    /**
     * 校验编号的唯一性
     *
     * @param partnerNo
     * @return
     */
    @Override
    public boolean checkPartnerManagerNoUniq(String partnerNo, String id) {
        AssertUtils.isTrue(StringUtils.isNotEmpty(partnerNo), "编号不能为空");
        return partnerManagerBl.checkPartnerManagerNoUniq(partnerNo, id);
    }

    /**
     * 获取合作商实体集合
     */
    @Override
    public Map<String, PartnerManagerDTO> findPartnerByPartnerNos(List<String> partnerNos) {
        return partnerManagerBl.findPartnerByPartnerNos(partnerNos);
    }

    /**
     * 根据idlist获取贸易伙伴
     * 
     * @param ids
     * @return
     */
    @Override
    public List<PartnerManagerDTO> listPartnerByIds(List<Long> ids) {
        return partnerManagerBl.listPartnerByIds(ids);
    }

    /**
     * 根据外部的供应商Id查供应商信息
     * 
     * @return
     */
    @Override
    public Map<String, PartnerManagerDTO> getParterMapByRefId(List<String> refParterId) {
        return partnerManagerBl.getParterMapByRefId(refParterId);
    }

    @Override
    public PageList<PartnerManagerDTO> listPartner(PartnerManagerDTO dto) {
        return partnerManagerBl.listPartner(dto);
    }
}
