package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;

import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.PassageBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.passage.EnablePassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IPassageService;

/**
 * 拣货通道
 *
 * <AUTHOR>
 * @since 2018/8/8 17:44
 */
@Service(timeout = 50000)
public class PassageServiceImpl implements IPassageService {

    @Autowired
    private PassageBL passageBL;

    /**
     * 获取通道列表
     */
    @Override
    public PageList<PassageDTO> listPassage(PassageSO so) {
        return passageBL.listPassage(so);
    }

    /**
     * 查看通道详情
     */
    @Override
    public PassageDTO getPassage(Long passageId) {
        return passageBL.getPassage(passageId);
    }

    /**
     * 新增通道
     */
    @Override
    public void savePassage(PassageDTO passageDTO) {
        passageBL.savePassage(passageDTO);
    }

    /**
     * 修改通道
     */
    @Override
    public void updatePassage(PassageDTO passageDTO) {
        passageBL.updatePassage(passageDTO);
    }

    /**
     * 删除通道
     */
    @Override
    public void removePassage(Long passageId) {
        passageBL.removePassage(passageId);
    }

    /**
     * 根据子项id获取通道配置
     */
    @Override
    public List<PassageDTO> listPassageByRelate(PassageItemSO passageItemSO) {
        AssertUtils.notNull(passageItemSO.getWarehouseId(), "仓库id不能为空");
        return passageBL.listPassageByRelate(passageItemSO);
    }

    /**
     * 根据仓库id查询通道类型
     */
    @Override
    public Byte getPassageTypeByWarehouseId(Integer warehouseId) {
        return passageBL.getPassageTypeByWarehouseId(warehouseId);
    }

    /**
     * 查找通道下的明细
     */
    @Override
    public List<PassageItemDTO> findPassageItem(PassageItemSO passageItemSO) {
        return passageBL.findPassageItem(passageItemSO);
    }

    /**
     * 启用通道
     *
     * @param enablePassageDTO
     */
    @Override
    public void enablePassage(EnablePassageDTO enablePassageDTO) {
        AssertUtils.notNull(enablePassageDTO.getPassageId(), "通道信息不能为空");
        enablePassageDTO.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());

        AssertUtils.notNull(enablePassageDTO.getOptUserId(), "操作人不能为空");
        passageBL.enablePassage(enablePassageDTO);
    }

    /**
     * 停用通道
     *
     * @param enablePassageDTO
     */
    @Override
    public void disablePassage(EnablePassageDTO enablePassageDTO) {
        AssertUtils.notNull(enablePassageDTO.getPassageId(), "通道信息不能为空");
        enablePassageDTO.setOptUserId(ThreadLocalUtil.getCurrentUserInfo());
        AssertUtils.notNull(enablePassageDTO.getOptUserId(), "操作人不能为空");
        passageBL.disablePassage(enablePassageDTO);
    }
}
