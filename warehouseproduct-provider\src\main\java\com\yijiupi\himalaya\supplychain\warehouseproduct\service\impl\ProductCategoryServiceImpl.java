package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupService;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoCategoryService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductCategoryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.convertor.ProductCategoryConvertor;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSaleModeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductStatisCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductStatisCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

// import com.yijiupi.himalaya.supplychain.service.LocationCategoryConfigService;

/**
 * 产品类目信息
 *
 * <AUTHOR>
 * @date 2018/9/11 16:59
 */
@Service(timeout = 300000)
public class ProductCategoryServiceImpl implements IProductCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCategoryServiceImpl.class);

    @Autowired
    private ProductCategoryBL productCategoryBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    // @Reference
    // private LocationCategoryConfigService locationCategoryConfigService;

    @Reference
    private IProductInfoCategoryService iProductInfoCategoryService;

    @Reference
    private IProductCategoryGroupService iProductCategoryGroupService;

    /**
     * 初始化产品类目信息表和产品sku表新字段（上线后执行一次即可）
     */
    @Override
    public void initProductCategoryByProductSku() {
        // productCategoryBL.initProductCategoryBySkuId();
    }

    /**
     * 定时更新产品类目信息表
     */
    @Override
    public void initProductCategoryByProductInfo() {
        // productCategoryBL.initProductCategoryByInfoId();
    }

    /**
     * 根据InfoId获取产品类目信息
     *
     * @param infoIds
     * @return
     */
    // @Override
    // public List<ProductCategoryDTO> findCategoryByInfoIds(List<Long> infoIds) {
    // return productCategoryBL.findCategoryByInfoIds(infoIds);
    // }

    /**
     * 根据skuId获取产品类目信息
     *
     * @param skuIds
     * @return
     */
    @Override
    public Map<Long, ProductCategoryDTO> findCategoryBySkuIds(List<Long> skuIds) {
        AssertUtils.notEmpty(skuIds, "参数SKUID集合不能为空！");
        Map<Long, ProductCategoryDTO> resultMap = new HashMap<>(16);

        ProductInfoCategoryQueryDTO productInfoCategoryQueryDTO = new ProductInfoCategoryQueryDTO();
        productInfoCategoryQueryDTO.setSkuIds(skuIds);
        List<ProductInfoCategoryDTO> productInfoCategoryDTOS =
            iProductInfoCategoryService.findProductCategoryBySkuIds(productInfoCategoryQueryDTO);
        List<ProductCategoryDTO> categoryBySkuIds =
            ProductCategoryConvertor.productInfoCategoryDTOS2ProductCategoryDTOS(productInfoCategoryDTOS);
        // List<ProductCategoryDTO> categoryBySkuIds = productCategoryBL.findCategoryBySkuIds(skuIds);
        if (CollectionUtils.isNotEmpty(categoryBySkuIds)) {
            categoryBySkuIds.forEach(p -> {
                resultMap.put(p.getProductSkuId(), p);
            });
        }
        return resultMap;
    }

    /**
     * 根据城市id获取产品类目
     * 
     * @return
     */
    @Override
    public List<ProductStatisCategoryDTO> findCategoryByCityId(Integer cityId) {
        ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO = new ProductCategoryGroupQueryDTO();
        productCategoryGroupQueryDTO.setOrgId(cityId);
        List<ProductCategoryGroupDTO> productCategoryGroupTree =
            iProductCategoryGroupService.findProductCategoryGroupTree(productCategoryGroupQueryDTO);
        // productCategoryBL.findCategoryByCityId(cityId)
        return ProductCategoryConvertor.productCategoryGroupTree2ProductStatisCategoryDTOS(productCategoryGroupTree);
    }

    /**
     * 根据城市id获取产品类目
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductStatisCategoryDTO> findCategory(ProductStatisCategoryQueryDTO queryDTO) {
        ProductCategoryGroupQueryDTO productCategoryGroupQueryDTO = new ProductCategoryGroupQueryDTO();
        productCategoryGroupQueryDTO.setOrgId(queryDTO.getCityId());
        List<ProductCategoryGroupDTO> productCategoryGroupTree =
            iProductCategoryGroupService.findProductCategoryGroupTree(productCategoryGroupQueryDTO);
        // productCategoryBL.findCategoryByCityId(cityId)
        return ProductCategoryConvertor.productCategoryGroupTree2ProductStatisCategoryDTOS(productCategoryGroupTree);
    }

    /**
     * 根据skuId查询开启货位库存的产品
     *
     * @param skuIds
     * @return
     */
    @Override
    public Map<Long, ProductCategoryDTO> findOpenLocationCategoryBySkuIds(Integer warehouseId, List<Long> skuIds) {
        AssertUtils.notEmpty(skuIds, "参数SKUID集合不能为空！");
        AssertUtils.notNull(warehouseId, "仓库Id不能为空！");
        Map<Long, ProductCategoryDTO> resultMap = new HashMap<>(16);
        // Boolean openLocationStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        // if (openLocationStock) {
        // List<ProductCategoryDTO> categoryBySkuIds = productCategoryBL.findCategoryBySkuIds(skuIds);
        // List<Integer> firstCateIds = categoryBySkuIds.stream().filter(p -> p.getStatisticsClass() != null).map(p ->
        // p.getStatisticsClass()).distinct().collect(Collectors.toList());
        // List<Integer> secondCateIds = categoryBySkuIds.stream().filter(p -> p.getSecondStatisticsClass() !=
        // null).map(p -> p.getSecondStatisticsClass()).distinct().collect(Collectors.toList());
        // List<Integer> allCategoryIds = new ArrayList<>();
        // if (CollectionUtils.isNotEmpty(firstCateIds)) {
        // allCategoryIds.addAll(firstCateIds);
        // }
        // if (CollectionUtils.isNotEmpty(secondCateIds)) {
        // allCategoryIds.addAll(secondCateIds);
        // }
        // if (CollectionUtils.isNotEmpty(allCategoryIds)) {
        // LOGGER.info(String.format("查询仓库开启货位库存参数，仓库：%s 类目：%s", warehouseId, JSON.toJSONString(allCategoryIds)));
        // List<Integer> lstNotOpenCategoryIds =
        // locationCategoryConfigService.findNotOpenLocationStockByCategory(warehouseId, allCategoryIds);
        // categoryBySkuIds.forEach(p -> {
        // if ((p.getStatisticsClass() != null && lstNotOpenCategoryIds.contains(p.getStatisticsClass()))
        // || (p.getSecondStatisticsClass() != null && lstNotOpenCategoryIds.contains(p.getSecondStatisticsClass()))) {
        // LOGGER.info(String.format("类目未开启货位库存：%s", JSON.toJSONString(p)));
        // } else {
        // resultMap.put(p.getProductSkuId(), p);
        // }
        // });
        // } else {
        // LOGGER.info(String.format("仓库%s没有查到产品类目，默认开启货位库存，查询类目：%s", warehouseId, JSON.toJSONString(skuIds)));
        // }
        // } else {
        // LOGGER.info(String.format("仓库%s未开启货位库存，查询类目：%s", warehouseId, JSON.toJSONString(skuIds)));
        // }
        return resultMap;
    }

    /**
     * 查询产品销售模式
     * 
     * @return
     */
    @Override
    public List<ProductSaleModeDTO> findProductSaleMode() {
        return Arrays.stream(ProductSaleMode.values()).filter(p -> Objects.nonNull(p.value)).map(p -> {
            ProductSaleModeDTO vo = new ProductSaleModeDTO();
            vo.setCode(p.value.toString());
            vo.setName(p.name());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public ProductCategoryDTO getProductCategoryById(Long id) {
        return productCategoryBL.getProductCategoryById(id);
    }

    /**
     * 根据规格id 批量查询类目信息
     * @param lstSpecId
     * @return
     */
    @Override
    public List<ProductCategoryDTO> getProductCategoryBySpecId(List<Long> lstSpecId) {
        return productCategoryBL.getProductCategoryBySpecId(lstSpecId);
    }

    /**
     * 根据skuIds查询类目日期配置，返回根据sku聚合
     * @param skuIds
     * @return
     */
    @Override
    public List<ProductCategoryDTO> getCategoryPeriodBySkuIds(List<Long> skuIds) {
        return productCategoryBL.getCategoryPeriodBySkuIds(skuIds);
    }
}
