/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.productsync.domain.bl.ProductSkuConfigBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuConfigStateChangeBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StorageAttributeService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductConfigStorageAttributeUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuStorageAgeModDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductConfigService;

/**
 * 产品配置
 *
 * <AUTHOR>
 * @since 2025年1月10日
 */
@Service(timeout = 300000)
public class ProductConfigServiceImpl implements IProductConfigService {
    @Autowired
    private StorageAttributeService storageAttributeService;
    @Autowired
    private ProductSkuConfigStateChangeBL productSkuConfigStateChangeBL;
    @Autowired
    private ProductSkuConfigBL productSkuConfigBL;

    /**
     * 获取产品配置分仓属性
     */
    @Override
    public Map<Long, Byte> queryStorageAttributeBySkuIds(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        return storageAttributeService.queryStorageAttributeBySkuIds(updateDTO);
    }

    /**
     * 批量修改产品配置分仓属性
     */
    @Override
    public void updateStorageAttributeBySkuIds(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        storageAttributeService.updateStorageAttributeBySkuIds(updateDTO);
    }

    /**
     * 按仓库更新产品配置分仓属性
     */
    @Override
    public void updateStorageAttributeByWarehouseId(ProductConfigStorageAttributeUpdateDTO queryDTO) {
        storageAttributeService.updateStorageAttributeByWarehouseId(queryDTO);
    }

    /**
     * 按仓库更新产品配置分仓属性
     */
    @Override
    public void updateByWarehouseIds(List<Integer> warehouseIds) {
        warehouseIds.stream().forEach(warehouseId -> {
            ProductConfigStorageAttributeUpdateDTO updateDTO = new ProductConfigStorageAttributeUpdateDTO();
            updateDTO.setWarehouseId(warehouseId);
            storageAttributeService.updateStorageAttributeByWarehouseId(updateDTO);
        });
    }

    /**
     * 获取产品配置分仓属性
     */
    @Override
    public Map<Long, Integer> getSkuStorageAttributeMap(ProductConfigStorageAttributeUpdateDTO updateDTO) {
        return storageAttributeService.getSkuStorageAttributeMap(updateDTO);
    }

    @Override
    public void disableProductSkuConfigState(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        productSkuConfigStateChangeBL.disableProductSkuConfigState(warehouseId);
    }

    @Override
    public void batchDisableProductSkuConfigState() {
        productSkuConfigStateChangeBL.batchDisableProductSkuConfigState();
    }

    /**
     * 修改库龄
     *
     * @param dto
     */
    @Override
    public void modProductSkuStorageAge(ProductSkuStorageAgeModDTO dto) {
        AssertUtils.notNull(dto.getProductSkuId(), "产品信息不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        productSkuConfigBL.modProductSkuStorageAge(dto);
    }
}
