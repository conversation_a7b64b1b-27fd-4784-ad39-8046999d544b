package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductControlConfigBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductControlConfigService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 控货策略
 *
 * <AUTHOR>
 * @date 2019-10-12 10:51
 */
@Service
public class ProductControlConfigServiceImpl implements IProductControlConfigService {

    @Autowired
    private ProductControlConfigBL productControlConfigBL;

    /**
     * 控货策略列表
     * 
     * @return
     */
    @Override
    public PageList<ProductControlConfigDTO> listProductControlConfig(ProductControlConfigSO productControlConfigSO) {
        return productControlConfigBL.listProductControlConfig(productControlConfigSO);
    }

    /**
     * 根据控货策略id查询控货策略
     * 
     * @return
     */
    @Override
    public List<ProductControlConfigDTO> listProductControlConfigByIds(List<Long> ids) {
        return productControlConfigBL.listProductControlConfigByIds(ids);
    }

    /**
     * 根据控货策略ID查询控货策略名称
     * 
     * @return
     */
    @Override
    public Map<Long, String> getConfigItemNameMap(List<Long> ids) {
        return productControlConfigBL.getConfigItemNameMap(ids);
    }

    /**
     * 获取控货策略详情
     * 
     * @return
     */
    @Override
    public ProductControlConfigDTO getProductControlConfig(Long id) {
        return productControlConfigBL.getProductControlConfig(id);
    }

    /**
     * 新增控货策略
     */
    @Override
    public Long saveProductControlConfig(ProductControlConfigDTO productControlConfigDTO) {
        return productControlConfigBL.saveProductControlConfig(productControlConfigDTO);
    }

    /**
     * 更新控货策略
     */
    @Override
    public void updateProductControlConfig(ProductControlConfigDTO productControlConfigDTO) {
        productControlConfigBL.updateProductControlConfig(productControlConfigDTO);
    }

    /**
     * 删除控货策略
     */
    @Override
    public void deleteProductControlConfig(Long id) {
        productControlConfigBL.deleteProductControlConfig(id);
    }

    /**
     * 根据订单的用户地址，查询产品对应的控货策略（Key -> skuId，Value -> 控货策略）
     * 
     * @return
     */
    @Override
    public Map<Long, ProductControlConfigDTO> getControlConfigIdBySkuId(ProductControlConfigQuery query) {
        return productControlConfigBL.getControlConfigIdBySkuId(query);
    }
}
