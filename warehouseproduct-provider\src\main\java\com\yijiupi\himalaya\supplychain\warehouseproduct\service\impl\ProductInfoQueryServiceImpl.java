package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductInfoTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productinfo.ProductInfoQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.CCPLimitSkuCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productinfo.WarehouseProductInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
public class ProductInfoQueryServiceImpl implements IProductInfoQueryService {
    @Autowired
    private ProductInfoQueryBL productInfoQueryBL;
    @Reference
    private IVariableValueService variableValueService;

    /**
     * 统采独家(0),统采非独家(1),其他-非统采(2),集采(3);
     */
    private static final String CCP_LIMIT_PRODUCT_TYPE = "CCP_LIMIT_PRODUCT_TYPE";
    private static final String CCP_LIMIT_PRODUCT_CATEGORY = "CCP_LIMIT_PRODUCT_CATEGORY";

    @Override
    public List<WarehouseProductInfoDTO> findByIds(List<Long> productInfoIds) {
        AssertUtils.notEmpty(productInfoIds, "产品信息不能为空！");
        return productInfoQueryBL.findByIds(productInfoIds);
    }

    @Override
    public List<WarehouseProductInfoDTO> findBySkuIds(List<Long> lstSkuId) {
        AssertUtils.notEmpty(lstSkuId, "SkuId不能为空！");
        return productInfoQueryBL.findBySkuIds(lstSkuId);
    }

    @Override
    public void checkCCPBySkuIds(CCPLimitSkuCheckDTO skuCheckDTO) {
        List<WarehouseProductInfoDTO> restrictedProducts = getRestrictedProducts(skuCheckDTO);
        if (CollectionUtils.isNotEmpty(restrictedProducts)) {
            String productNames = restrictedProducts.stream()
                    .map(WarehouseProductInfoDTO::getProductName)
                    .distinct().collect(Collectors.joining("、"));
            String categoryNames = restrictedProducts.stream()
                    .filter(p -> StringUtils.isNotEmpty(p.getStatisticsCategoryName()))
                    .map(WarehouseProductInfoDTO::getStatisticsCategoryName)
                    .distinct().collect(Collectors.joining("、"));
            String productInfoTypes = restrictedProducts.stream()
                    .filter(Objects::nonNull)
                    .map(p -> ProductInfoTypeEnum.getEnumName(p.getProductInfoType()))
                    .distinct().collect(Collectors.joining("、"));
            throw new BusinessValidateException(String.format("公司%s类型的%s产品，只能正常销售，不能进入残次品区！\n%s", productInfoTypes, categoryNames, productNames));
        }
    }

    /**
     * 获取受限制的产品列表
     */
    @Override
    public List<WarehouseProductInfoDTO> getRestrictedProducts(CCPLimitSkuCheckDTO skuCheckDTO) {
        AssertUtils.notNull(skuCheckDTO, "参数不能为空！");
        AssertUtils.notNull(skuCheckDTO.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notEmpty(skuCheckDTO.getLstSkuId(), "SkuId列表不能为空！");

        // 获取配置
        String strProductType = getVariableConfig(skuCheckDTO.getWarehouseId(), CCP_LIMIT_PRODUCT_TYPE);
        String strProductCategory = getVariableConfig(skuCheckDTO.getWarehouseId(), CCP_LIMIT_PRODUCT_CATEGORY);

        if (StringUtils.isEmpty(strProductType) || StringUtils.isEmpty(strProductCategory)) {
            return java.util.Collections.emptyList();
        }

        // 解析配置并检查产品
        List<Byte> limitTypes = parseConfig(strProductType, Byte::valueOf);
        List<Long> limitCategories = parseConfig(strProductCategory, Long::valueOf);

        if (CollectionUtils.isEmpty(limitTypes) || CollectionUtils.isEmpty(limitCategories)) {
            return java.util.Collections.emptyList();
        }

        // 查找受限制的产品
        return productInfoQueryBL.findBySkuIds(skuCheckDTO.getLstSkuId())
                .stream()
                .filter(p -> p != null
                        && p.getProductInfoType() != null && limitTypes.contains(p.getProductInfoType())
                        && p.getProductStatisticsClass() != null && limitCategories.contains(p.getProductStatisticsClass()))
                .collect(Collectors.toList());
    }

    /**
     * 获取变量配置
     */
    private String getVariableConfig(Integer warehouseId, String variableKey) {
        VariableValueQueryDTO query = new VariableValueQueryDTO();
        query.setVariableKey(variableKey);
        query.setWarehouseId(warehouseId);
        VariableDefAndValueDTO value = variableValueService.detailVariable(query);
        return value != null ? value.getVariableData() : null;
    }

    /**
     * 解析配置字符串为指定类型列表
     */
    private <T> List<T> parseConfig(String config, java.util.function.Function<String, T> converter) {
        if (StringUtils.isEmpty(config)) {
            return java.util.Collections.emptyList();
        }
        return java.util.Arrays.stream(config.split("、"))
                .map(String::trim)
                .filter(s -> !StringUtils.isEmpty(s))
                .map(converter)
                .collect(Collectors.toList());
    }
}
