/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductLocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.productlocation.ProductLocationHelper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationCheckResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ReplaceProductLocationCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuBaseInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productlocation.ProductLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2017年7月11日 下午3:08:19
 */
@Service(timeout = 10000)
public class ProductLocationServiceImpl implements IProductLocationService {

    @Autowired
    private ProductLocationBL productLocationBL;

    @Resource
    private ProductLocationHelper productLocationHelper;

    @Override
    public void add(LoactionDTO dto) {
        if (dto.getUserId() == null) {
            throw new DataValidateException("操作人信息不能为空！");
        }
        if (dto.getWarehouseId() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        if (dto.getCityId() == null) {
            throw new DataValidateException("城市信息不能为空！");
        }
        productLocationBL.add(dto);
    }

    @Override
    public void modify(LoactionDTO dto) {
        if (dto.getId() == null) {
            throw new DataValidateException("货位标识信息不能为空！");
        }
        if (dto.getUserId() == null) {
            throw new DataValidateException("操作人信息不能为空！");
        }
        productLocationBL.modify(dto);
    }

    @Override
    public void delete(Long locationId, OperatorDTO operatorDTO) {
        productLocationBL.delete(locationId, operatorDTO);
    }

    /**
     * 删除货位信息
     *
     * @param lstIds
     * <AUTHOR>
     */
    @Override
    public void deleteBatch(List<Long> lstIds, OperatorDTO operatorDTO) {
        productLocationBL.deleteBatch(lstIds, operatorDTO);
    }

    /**
     * 根据仓库ID和产品skuId删除产品货位关系
     *
     * @param productSkuIds
     * @param warehouseId
     * @return
     */
    @Override
    public void deleteBySkuId(Integer warehouseId, List<Long> productSkuIds) {
        productLocationBL.deleteBySkuId(warehouseId, productSkuIds);
    }

    @Override
    public List<ProductLoactionItemDTO> findLocationBySkuId(Integer warehouseId, List<Long> productSkuIds) {
        return productLocationBL.findLocationBySkuId(warehouseId, productSkuIds);
    }

    @Override
    public Map<Long, List<LoactionDTO>> findLocationDTOBySkuId(Integer warehouseId, List<Long> productSkuIds) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return new HashMap<>(16);
        }
        return productLocationBL.findLocationDTOBySkuId(warehouseId, productSkuIds);
    }

    @Override
    public ProductLocationDetailDTO getProductLocationBySkuId(LocationQueryDTO dto) {
        if (dto.getSkuId() == null) {
            throw new DataValidateException("产品信息不能为空！");
        }
        if (dto.getWarehouseId() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        return productLocationBL.getProductLocationBySkuId(dto);
    }

    @Override
    public List<ProductLocationDetailDTO> findProductLocationBySkuId(LocationQueryDTO dto) {
        // if (dto.getSkuIdList() == null || dto.getSkuIdList().isEmpty()) {
        // throw new DataValidateException("产品信息不能为空！");
        // return new ArrayList<ProductLocationDetailDTO>();
        // }
        if (dto.getWarehouseId() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        return productLocationBL.findProductLocationBySkuId(dto);
    }

    @Override
    public PageList<LoactionDTO> findProductLocationPageList(LocationQueryDTO dto) {
        return productLocationBL.findProductLocationPageList(dto);
    }

    @Override
    public PageList<LoactionDTO> findProductLocationPageListNew(LocationQueryDTO dto) {
        return productLocationBL.findProductLocationPageListNew(dto);
    }

    @Override
    public PageList<LoactionDTO> findProductLocationOrAreaPageListNew(LocationQueryDTO dto) {
        return productLocationBL.findProductLocationOrAreaPageListNew(dto);
    }

    @Override
    public List<LoactionDTO> findLocationListByWarehouseIdAndAreaType(LocationQueryDTO dto) {
        return productLocationBL.findLocationListByWarehouseIdAndAreaType(dto);
    }

    @Override
    public List<LoactionDTO> findProductLocationList(LocationQueryDTO dto) {
        return productLocationBL.findProductLocationList(dto);
    }

    @Override
    public List<LocationReturnDTO> findLocationList(LocationQueryDTO dto) {
        return productLocationBL.findLocationList(dto);
    }

    @Override
    public List<LocationReturnDTO> listLocationByCondition(LocationQueryDTO dto) {
        return productLocationBL.listLocationByCondition(dto);
    }

    /**
     * 根据货位模糊查询商品skuid
     */
    @Override
    public List<Long> findProductSkuByProductLocation(LocationQueryDTO dto) {
        return productLocationBL.findProductSkuByProductLocation(dto);
    }

    /**
     * 经销商入库确认收货. (sku和货位管理)
     */
    @Override
    public void agencyPutInStore(List<LoactionDTO> dto) {
        productLocationBL.agencyPutInStore(dto);
    }

    /**
     * 批量导入货位信息,不传area_id,需要根据areaName查询id
     */
    @Override
    public void importLocation(LocationImportDTO dto) {
        if (dto.getUser_Id() == null) {
            throw new DataValidateException("操作人信息不能为空！");
        }
        if (dto.getWarehouse_Id() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        if (dto.getCity_Id() == null) {
            throw new DataValidateException("城市信息不能为空！");
        }
        if (dto.getLocationList() == null || dto.getLocationList().isEmpty()) {
            throw new DataValidateException("导入数据不能为空!");
        }
        productLocationBL.importLocation(dto);
    }

    /**
     * 获取产品货位配置列表
     *
     * @param so
     * @return
     */
    @Override
    public PageList<ProductLocationListDTO> listProductLocation(ProductLocationListSO so) {
        return productLocationBL.listProductLocation(so);
    }

    /**
     * 供应链客户端查询关联货位
     *
     * @param so
     * @return
     */
    @Override
    public PageList<ProductLocationListDTO> findProductLocationForSupplyChainClient(ProductLocationListSO so) {
        return productLocationBL.findProductLocationForSupplyChainClient(so);
    }

    /**
     * 获取产品货位配置列表（不分页）
     *
     * @param so
     * @return
     */
    @Override
    public List<ProductLocationListDTO> listProductLocationNoPage(ProductLocationListSO so) {
        return productLocationBL.listProductLocationNoPage(so);
    }

    /**
     * 新增产品货位
     *
     * @param dto
     */
    @Override
    public Long saveProductLocation(ProductLocationDTO dto) {
        return productLocationBL.saveProductLocation(dto);
    }

    /**
     * 修改产品货位
     *
     * @param dto
     */
    @Override
    public void updateProductLocation(ProductLocationDTO dto) {
        productLocationBL.updateProductLocation(dto);
    }

    /**
     * 删除产品货位
     *
     * @param id
     */
    @Override
    public void removeProductLocation(Long id, OperatorDTO operatorDTO) {
        productLocationBL.removeProductLocation(id, operatorDTO);
    }

    /**
     * 批量删除产品货位
     *
     * @param ids
     */
    @Override
    public void removeProductLocationBatch(List<Long> ids, OperatorDTO operatorDTO) {
        productLocationBL.removeProductLocationBatch(ids, operatorDTO);
    }

    /**
     * 导入产品货位
     *
     * @param dto
     */
    @Override
    public void importProductLocation(ProductLocationImportDTO dto) {
        productLocationBL.importProductLocation(dto);
    }

    /**
     * 获取所有货区及货位信息
     *
     * @param warehouseId
     * @return
     */
    @Override
    public List<LocationAreaInfoDTO> listLocation(Integer warehouseId) {
        return productLocationBL.listLocation(warehouseId);
    }

    /**
     * 根据货位模糊查找匹配的所有货位
     *
     * @param warehouseId
     * @param locatinName
     * @return
     */
    @Override
    public List<LocationInfoDTO> listLocationByName(Integer warehouseId, String locatinName) {
        return productLocationBL.listLocationByName(warehouseId, locatinName);
    }

    /**
     * 根据货位名称查找货位
     *
     * @param warehouseId
     * @param cityId
     * @param locatinName
     * @return
     */
    @Override
    public LoactionDTO getLocationIdByName(Integer warehouseId, Integer cityId, String locatinName) {
        return productLocationBL.getLocationIdByName(warehouseId, cityId, locatinName);
    }

    /**
     * 根据skuId列表查询所有货位产品关联信息
     *
     * @param skuIdList
     * @return
     */
    @Override
    public List<ProductLocationDTO> listProductLocationPOBySkuIdList(List<Long> skuIdList) {
        return productLocationBL.listProductLocationPOBySkuIdList(skuIdList);
    }

    /**
     * 批量添加货位商品关联信息
     *
     * @param productLocationDTOList
     * @return
     */
    @Override
    public void insertBatch(List<ProductLocationDTO> productLocationDTOList) {
        productLocationBL.insertBatch(productLocationDTOList);
    }

    /**
     * 批量添加货位商品关联信息（排除已存在的关联货位）
     *
     * @return
     */
    @Override
    public void insertBatchExcludeExist(List<ProductLocationDTO> productLocationDTOList) {
        productLocationBL.insertBatchExcludeExist(productLocationDTOList);
    }

    /**
     * 检查关联货位 1: 不允许混放的货位，不允许关联多个产品 2: 允许混放的货位，需要提示出已经过关联的产品
     *
     * @return
     */
    @Override
    public ProductLocationCheckDTO checkProductLocation(ProductLocationCheckSO productLocationCheckSO) {
        return productLocationBL.checkProductLocation(productLocationCheckSO);
    }

    /**
     * 根据产品配置货位信息查询相关产品SKU基础信息
     */
    @Override
    public PageList<ProductSkuBaseInfoDTO> findProductBaseInfoByProductLocation(ProductLocationListSO so) {
        return productLocationBL.findProductBaseInfoByProductLocation(so);
    }

    /**
     * 根据产品SKU信息查询仓库产品配置货位信息
     */
    @Override
    public List<ProductLocationListDTO> findProductLocationBySkuInfo(ProductLocationListSO query) {
        return productLocationBL.findProductLocationBySkuInfo(query);
    }

    @Override
    public List<EnumDTO> productLocationList(CategoryTypeDTO categoryTypeDTO) {
        return productLocationBL.productLocationList(categoryTypeDTO);
    }

    @Override
    public List<LocationAreaInfoDTO> listLocationSaas(ProductLocationListSO productLocationListSO) {
        return productLocationBL.listLocation(productLocationListSO.getWarehouseId());
    }

    /**
     * 根据SKUID集合查找配置货位
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductLoactionItemDTO> findLocationItemByCon(ProductLocationItemQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");

        return productLocationBL.findLocationItemByCon(queryDTO);
    }

    /**
     * 根据货位查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductLoactionSkuInfoDTO> findProductLocationGroup(LocationProductQuery queryDTO) {
        return productLocationBL.findProductLocationGroup(queryDTO);
    }

    /**
     * 根据货区查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductLoactionSkuInfoDTO> findProductLocationGroupByArea(LocationProductQuery queryDTO) {
        return productLocationBL.findProductLocationGroupByArea(queryDTO);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public List<ProductLocationDetailDTO> findProductLocationDetailList(LocationQueryDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        return productLocationBL.findProductLocationBySkuIds(dto);
    }

    @Override
    public List<LocationReturnDTO> findLocationListByCondition(LocationQueryDTO dto) {
        return productLocationBL.findLocationListByCondition(dto);
    }

    /**
     * SCM-15228 2.5+实施优化，易点货收货入库交互调整
     *
     * @param param 查询条件
     * @return 查询结果
     */
    @Override
    public PageList<LoactionDTO> pageListLocation(ProductLocationQueryDTO param) {
        AssertUtils.notNull(param.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(param.getQueryType(), "查询类型 不能为空");
        return productLocationHelper.pageListLocation(param);
    }

    /**
     * 根据条件查找关联产品信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductLoactionSkuInfoDTO> findProductByCondition(LocationProductQuery queryDTO) {
        return productLocationBL.findProductByCondition(queryDTO);
    }

    /**
     * 根据条件查找产品关联货位信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductLoactionSkuInfoDTO> findProductLocationByCondition(LocationProductQuery queryDTO) {
        return productLocationBL.findProductLocationByCondition(queryDTO);
    }

    /**
     * 批量重新关联产品货位配置（支持校验和删除旧关联）
     *
     * @param replaceDTO
     * @return
     */
    @Override
    public void replaceProductLocationBatch(ReplaceProductLocationBatchDTO replaceDTO) {
        productLocationBL.replaceProductLocationBatch(replaceDTO);
    }

    /**
     * 替换产品关联货位检查
     *
     * @param checkDTOS
     * @return
     */
    @Override
    public List<ProductLocationCheckResultDTO> replaceProductLocationCheck(List<ReplaceProductLocationCheckDTO> checkDTOS) {
        return productLocationBL.replaceProductLocationCheck(checkDTOS);
    }
}
