package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductRelationGroupBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductRelationGroupQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductGroupInitDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupAddDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductRelationGroupDelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductRelationGroupService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Service(timeout = 60000)
public class ProductRelationGroupServiceImpl implements IProductRelationGroupService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductRelationGroupServiceImpl.class);

    @Autowired
    private ProductRelationGroupBL productRelationGroupBL;

    @Autowired
    private ProductRelationGroupQueryBL productRelationGroupQueryBL;

    /**
     * 保存产品关联关系 - 有重复则会抛出异常
     */
    @Override
    public void saveProductRelation(ProductRelationGroupAddDTO groupAddDTO) {
        AssertUtils.notNull(groupAddDTO, "新增产品分组关系不能为空");
        AssertUtils.notNull(groupAddDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(groupAddDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(groupAddDTO.getGroupRelations(), "仓库ID不能为空");
        productRelationGroupBL.preconditioning();
        productRelationGroupBL.saveProductRelation(Collections.singletonList(groupAddDTO), true);
    }

    /**
     * 批量-保存产品关联关系 - 重复数据直接跳过
     */
    @Override
    public void automaticSaveProductRelation(List<ProductRelationGroupAddDTO> groupAddList) {
        productRelationGroupBL.preconditioning();
        productRelationGroupBL.saveProductRelation(groupAddList, false);
    }

    /**
     * 删除产品关联关系
     */
    @Override
    public void delProductRelation(ProductRelationGroupDelDTO delDTO) {
        productRelationGroupBL.delProductRelation(delDTO);
    }

    /**
     * 拆分关联关系
     */
    @Override
    public void splitProductRelation(Integer warehouseId, Long splitSkuId) {
        productRelationGroupBL.splitProductRelation(warehouseId, splitSkuId);
    }

    @Override
    public void initProductGroup(ProductGroupInitDTO initDTO) {
        AssertUtils.notNull(initDTO, "初始化参数不能为空");
        // 设置仓库范围：仅仅参数校验用
        initDTO.setScope(StringUtils.trimToNull(initDTO.getScope()));
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(initDTO.getWarehouseIdList())
            || StringUtils.equalsIgnoreCase(initDTO.getScope(), "ALL"), "仓库信息不能为空");
        // 仓库ID集合
        Set<Integer> wareHouseIdList = new HashSet<>(50);
        if (StringUtils.equalsIgnoreCase(initDTO.getScope(), "ALL")) {
            // 所有店仓仓库ID
            List<Integer> dianCangIdList = productRelationGroupQueryBL.findDianCangWareHouseList();
            // 加入店仓ID
            wareHouseIdList.addAll(dianCangIdList);
        }
        if (CollectionUtils.isNotEmpty(initDTO.getWarehouseIdList())) {
            wareHouseIdList.addAll(initDTO.getWarehouseIdList());
        }
        LOGGER.info("共 {} 个店仓仓库[{}]开始处理仓库产品分组信息！", wareHouseIdList.size(), JSON.toJSONString(wareHouseIdList));
        // 按仓库初始化产品分组关系
        wareHouseIdList.stream().filter(e -> e != null).forEach(e -> {
            productRelationGroupBL.initProductGroup(e);
        });
    }

    /**
     * 根据仓库类型\skuId查询产品分组信息（判断是否开启货位库存及是否店仓仓库）
     */
    @Override
    public Map<ProductSkuDTO, List<ProductSkuDTO>> findGroupTotalProductBySkuIds(Integer warehouseId,
        List<Long> skuIdList) {
        // return productRelationGroupQueryBL.findGroupTotalProductBySkuIds(warehouseId, skuIdList);
        return Collections.emptyMap();
    }

    /**
     * 根据仓库类型\skuId查询产品分组信息（判断是否开启货位库存及是否店仓仓库）
     */
    @Override
    public Map<Long, List<ProductSkuDTO>> findSameGroupProductBySkuIds(Integer warehouseId, List<Long> skuIdList) {
        // return productRelationGroupQueryBL.findSameGroupProductBySkuIds(warehouseId, skuIdList);
        return Collections.emptyMap();
    }

    /**
     * 直接查询仓库产品分组信息（数据库中有就返回）
     */
    @Override
    public Map<Long, List<ProductSkuDTO>> findSameGroupProductDirect(Integer warehouseId, List<Long> skuIdList) {
        // return productRelationGroupQueryBL.findSameGroupProductDirect(warehouseId, skuIdList);
        return Collections.emptyMap();
    }

    /**
     * 直接查询仓库产品分组信息（数据库中有就返回）
     */
    @Override
    public Map<ProductSkuDTO, List<ProductSkuDTO>> findGroupTotalProductDirect(Integer warehouseId,
        List<Long> skuIdList) {
        // return productRelationGroupQueryBL.findGroupTotalProductDirect(warehouseId, skuIdList);
        return Collections.emptyMap();
    }

    /**
     * 直接查询仓库产品分组sku（数据库中有就返回）
     */
    @Override
    public Map<Long, List<Long>> findGroupProductDirect(Integer warehouseId, List<Long> skuIdList) {
        // return productRelationGroupQueryBL.findGroupProductDirect(warehouseId, skuIdList);
        return Collections.emptyMap();
    }
}
