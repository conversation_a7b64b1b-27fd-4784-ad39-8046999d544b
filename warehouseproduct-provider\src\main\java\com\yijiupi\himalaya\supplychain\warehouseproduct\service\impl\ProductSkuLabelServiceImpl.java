package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuLabelBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuLabelService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 产品标签管理
 *
 * <AUTHOR>
 * @date 2019-11-26 18:13
 */
@Service
public class ProductSkuLabelServiceImpl implements IProductSkuLabelService {

    @Autowired
    private ProductSkuLabelBL productSkuLabelBL;

    @Override
    public Map<Long, Boolean> getProductByDirectDeliveryMap(ProductSkuLabelSO productSkuLabelSO) {
        return productSkuLabelBL.getProductByDirectDeliveryMap(productSkuLabelSO);
    }

    @Override
    public List<Long> getEnableSellProductByWarehouseId(Integer warehouseId) {
        return productSkuLabelBL.getEnableSellProductByWarehouseId(warehouseId);
    }

    @Override
    public Map<Long, Boolean> getProductSkuLabelByEnableSellMap(ProductSkuLabelSO so) {
        return productSkuLabelBL.getProductSkuLabelByEnableSellMap(so);
    }

}
