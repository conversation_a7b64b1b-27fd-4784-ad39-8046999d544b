/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuProcessBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductInventoryRatioUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.RepeatProductDeleteDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * 产品操作微服务实现类
 * 
 * @author: yanpin
 * @date: 2018年10月31日 下午3:56:09
 */
@Service(timeout = 300000)
public class ProductSkuProcessServiceImpl implements IProductSkuProcessService {
    @Autowired
    private ProductSkuProcessBL productSkuProcessBL;

    /**
     * 修改产品信息
     */
    @Override
    public void update(ProductSkuDTO queryDto) {
        productSkuProcessBL.update(queryDto);
    }

    /**
     * 批量修改产品信息（导入）
     */
    @Override
    public void batchUpdate(List<ProductSkuDTO> productSkuDTOList) {
        productSkuProcessBL.batchUpdate(productSkuDTOList);
    }

    /**
     * 批量设置属性
     */
    @Override
    public void batchUpdateAttr(List<ProductSkuDTO> productSkuDTOList) {
        productSkuProcessBL.batchUpdateAttr(productSkuDTOList);
    }

    /**
     * 批量设置属性(是否独家、是否允许混放)
     */
    @Override
    public void batchUpdateAttrByUniqueAndFleeGoods(List<ProductSkuDTO> productSkuDTOList) {
        productSkuProcessBL.batchUpdateAttrByUniqueAndFleeGoods(productSkuDTOList);
    }

    /**
     * 批量设置库存占用比例
     */
    @Override
    public void batchUpdateInventoryRatio(ProductInventoryRatioUpdateDTO productInventoryRatioUpdateDTO) {
        productSkuProcessBL.batchUpdateInventoryRatio(productInventoryRatioUpdateDTO);
    }

    /**
     * 修改异常订单的产品信息
     */
    @Override
    public void updateByExceptionOrder(List<ProductSkuDTO> productSkuList) {
        productSkuProcessBL.updateByExceptionOrder(productSkuList);
    }

    @Override
    public void updateReplaceToSkuId(List<Long> skuIdList, Long replaceToSkuId) {
        productSkuProcessBL.updateReplaceToSkuId(skuIdList, replaceToSkuId);
    }

    /**
     * 根据skuId删除
     */
    @Override
    public void deleteBySkuId(Long skuId) {
        productSkuProcessBL.deleteBySkuId(skuId);
    }

    /**
     * 历史重复产品标记删除或还原
     */
    @Override
    public void deleteOrRemoveRepeatProductSku(RepeatProductDeleteDTO deleteDTO) {
        productSkuProcessBL.deleteOrRemoveRepeatProductSku(deleteDTO);
    }

    /**
     * 逻辑删除 sku
     *
     * @param skuIds skuIds
     */
    @Override
    public void logicDelete(Collection<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        productSkuProcessBL.logicDelete(skuIds, true);
    }

    /**
     * 还原逻辑删除 sku
     *
     * @param skuIds skuIds
     */
    @Override
    public void restoreLogicDelete(Collection<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        productSkuProcessBL.logicDelete(skuIds, false);
    }

    /**
     * 批量更新 sku 状态
     *
     * @param skuIds        skuIds
     * @param productStatus sku 状态
     */
    @Override
    public void updateProductStateBatch(Collection<Long> skuIds, Integer productStatus) {
        productSkuProcessBL.updateProductStateBatch(skuIds, productStatus);
    }

    /**
     * 批量更新 sku 状态
     *
     * @param updateDTO
     */
    @Override
    public void updateProductStateBySkuIds(ProductSkuDTO updateDTO) {
        productSkuProcessBL.updateProductStateBatch(updateDTO.getRefProductSkuIdList(), updateDTO.getProductState());
    }
}
