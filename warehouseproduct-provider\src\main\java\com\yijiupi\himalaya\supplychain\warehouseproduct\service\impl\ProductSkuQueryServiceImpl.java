/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.search.SpellSearchSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductStockRatioQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.QuerySkuDetailsParamDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;

/**
 * 产品查询微服务
 * 
 * @author: yanpin
 * @date: 2018年10月31日 下午3:55:05
 */
@Service(timeout = 300000)
public class ProductSkuQueryServiceImpl implements IProductSkuQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuQueryServiceImpl.class);

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    /**
     * 分页查询产品列表
     */
    @Override
    public PageList<ProductSkuDTO> pageList(ProductSkuQueryDTO queryDto) {
        AssertUtils.notNull(queryDto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(queryDto.getWarehouseId(), "仓库id不能为空");
        return productSkuQueryBL.pageList(queryDto);
    }

    /**
     * 查询产品列表
     */
    @Override
    public PageList<ProductSkuDTO> listProductSku(ProductSkuQueryDTO queryDto) {
        AssertUtils.notNull(queryDto.getCityId(), "城市id不能为空");
        AssertUtils.notNull(queryDto.getWarehouseId(), "仓库id不能为空");
        return productSkuQueryBL.listProductSku(queryDto);
    }

    /**
     * 查询产品列表（完整的sku仓库配置）
     */
    @Override
    public PageList<ProductSkuDTO> listProductSkuFull(ProductSkuSO productSkuSO) {
        AssertUtils.notNull(productSkuSO, "参数不能为空");
        AssertUtils.notNull(productSkuSO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productSkuSO.getWarehouseId(), "仓库id不能为空");
        return productSkuQueryBL.listProductSkuFull(productSkuSO);
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置）
     */
    @Override
    public List<ProductSkuDTO> findBySkuFull(Integer warehouseId, List<Long> productSkuIds) {
        return productSkuQueryBL.findBySkuFull(warehouseId, productSkuIds);
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置） 没有条码/箱码
     */
    @Override
    public List<ProductSkuDTO> findSkuInfoWithConfig(Integer warehouseId, List<Long> productSkuIds) {
        return productSkuQueryBL.findSkuInfoWithConfig(warehouseId, productSkuIds);
    }

    /**
     * 根据skuId查找产品
     */
    @Override
    public List<ProductSkuDTO> findBySku(List<Long> productSkuId) {
        return productSkuQueryBL.findBySku(productSkuId);
    }

    @Override
    public Map<Long, ProductSkuDTO> findBySkuWithMap(List<Long> productSkuId) {
        List<ProductSkuDTO> skuList = productSkuQueryBL.findBySku(productSkuId);
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.EMPTY_MAP;
        }
        return skuList.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity(), (v1, v2) -> v1 != null ? v1 : v2));
    }

    /**
     * 根据规格查询产品
     */
    @Override
    public List<ProductSkuDTO> findBySpec(ProductSkuBySpecificationSO productSkuBySpecSO) {
        return productSkuQueryBL.findBySpec(productSkuBySpecSO);
    }

    /**
     * 查询加工产品
     */
    @Override
    public PageList<ProcessProductSkuDTO>
        pageListProcessProductSku(ProcessProductSkuQueryDTO processProductSkuQueryDTO) {
        AssertUtils.notNull(processProductSkuQueryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(processProductSkuQueryDTO.getWarehouseId(), "仓库id不能为空");
        return productSkuQueryBL.pageListProcessProductSku(processProductSkuQueryDTO);
    }

    /**
     * 通过infoId、规格id、货主id查询对应的可加工产品
     */
    @Override
    public Map<Long, List<ProcessProductSkuDTO>>
        listProcessProductSkuByInfo(ProcessProductInfoQueryDTO processProductInfoQueryDTO) {
        AssertUtils.notNull(processProductInfoQueryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(processProductInfoQueryDTO.getWarehouseId(), "仓库id不能为空");
        return productSkuQueryBL.listProcessProductSkuByInfo(processProductInfoQueryDTO);
    }

    /**
     * 通过infoId查询对应的可加工产品
     */
    @Override
    public Map<Long, List<ProcessProductSkuDTO>>
        listProcessProductSkuByInfoId(ProcessProductInfoQueryDTO processProductInfoQueryDTO) {
        AssertUtils.notNull(processProductInfoQueryDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(processProductInfoQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(processProductInfoQueryDTO.getProductInfoId(), "产品信息id不能为空");
        return productSkuQueryBL.listProcessProductSkuByInfoId(processProductInfoQueryDTO);
    }

    /**
     * 拼音搜索
     */
    @Override
    public List<String> getSpellSearch(SpellSearchSO spellSearchSO) {
        return productSkuQueryBL.getSpellSearch(spellSearchSO);
    }

    @Override
    public List<ProductSkuDTO> findLocationProducts(LocationProductQuery productQuery) {
        return productSkuQueryBL.findLocationProducts(productQuery);
    }

    /**
     * 根据城市id，规格id，货主查询产品信息
     */
    @Override
    public List<ProductSkuDTO> findByProductSpecificationIds(List<Long> productSpecificationIds, Long ownerId,
        Integer orgId) {
        return productSkuQueryBL.findByProductSpecificationIds(productSpecificationIds, ownerId, orgId);
    }

    @Override
    public ProductCheckByRatioDTO stockRatioProductInfoIsExist(ProductStockRatioQuery ratioQuery) {
        return productSkuQueryBL.stockRatioProductInfoIsExist(ratioQuery);
    }

    /**
     * 获取产品的异常原因
     * 
     * @return 正常(0), 未设置产品属性(1), 未关联货位(2)
     */
    @Override
    public Integer getProductExceptionReason(Integer cityId, Integer warehouseId, List<Long> productSkuIds,
        Boolean isNP) {
        Integer result = productSkuQueryBL.getProductExceptionReason(cityId, warehouseId, productSkuIds, isNP);
        LOGGER.info("{}获取产品的异常原因: {}", JSON.toJSONString(productSkuIds), result);
        return result;

    }

    /**
     * 获取存在异常原因的产品skuId
     * 
     * @return
     */
    @Override
    public List<Long> getExceptionProductSkuIds(ExceptionProductSkuQuery exceptionProductSkuQuery) {
        return productSkuQueryBL.getExceptionProductSkuIds(exceptionProductSkuQuery);
    }

    /**
     * 根据订单产品查询实际发货产品sku （key：订单产品skuId，value：实际发货sku）
     * 
     * @return
     */
    @Override
    public Map<Long, ProductSkuDTO> getProductSkuDeliveryMap(ProductSkuDeliverySO productSkuDeliverySO) {
        return productSkuQueryBL.getProductSkuDeliveryMap(productSkuDeliverySO);
    }

    /**
     * 根据skuId获取产品图片url
     * 
     * @return
     */
    @Override
    public Map<Long, ProductImageDTO> getProductImageUrl(List<Long> skuIds) {
        return productSkuQueryBL.getProductImageUrl(skuIds);
    }

    /**
     * 触发易款sku同步
     */
    @Override
    public void sendEasyChainProductSkuSyncMessage(List<Long> skuIds) {
        productSkuQueryBL.sendEasyChainProductSkuSyncMessage(skuIds);
    }

    /**
     * 根据skuId\refSkuId查找产品
     */
    @Override
    public Map<String, ProductSkuDTO> findBySkuAndRef(List<String> productSkuIds) {
        return productSkuQueryBL.findBySkuAndRef(productSkuIds);
    }

    /**
     * 获取产品相关价格（成本价/销售价）
     * 
     * @return
     */
    @Override
    public List<ProductPriceDTO> getProductPrice(List<ProductPriceQueryDTO> productPriceQueryDTOS) {
        return productSkuQueryBL.getProductPrice(productPriceQueryDTOS);
    }

    /**
     * 获取产品成本价
     * 
     * @return
     */
    @Override
    public Map<Long, BigDecimal> getProductCostPriceMap(ProductCostPriceQueryDTO productCostPriceQueryDTO) {
        return productSkuQueryBL.getProductCostPriceMap(productCostPriceQueryDTO);
    }

    /**
     * 查询产品属性：批次生产日、分拣占用及残次品信息
     */
    @Override
    public List<ProductRelevantInfoDTO> findProductRelevantInfo(ProductRelevantInfoQueryDTO relevantInfoQueryDTO) {
        return productSkuQueryBL.findProductRelevantInfo(relevantInfoQueryDTO);
    }

    /**
     * 根据条件查询产品信息及类目信息
     * 
     * @return
     */
    @Override
    public List<com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO>
        findProductInfoAndCategory(ProductInfoCategoryQuery query) {
        return productSkuQueryBL.findProductInfoAndCategory(query);
    }

    /**
     * 查询有仓库库存[包含负库存]产品信息及类目
     */
    @Override
    public PageList<ProductSkuInfoDTO> findHaveInventoryProductInfo(ProductInfoCategoryQuery query) {
        return productSkuQueryBL.findHaveInventoryProductInfo(query);
    }

    /**
     * 产品是否休食类或者酒饮类
     */
    @Override
    public List<ProductRestOrDrinkCategoryDTO> findProductRestOrDrinkCategory(ProductInfoCategoryQuery query) {
        return productSkuQueryBL.findProductRestOrDrinkCategory(query);
    }

    @Override
    public ProductSkuDTO getProductSkuBySpecId(Integer cityId, Long specId, Long ownerId, Integer source) {
        return productSkuQueryBL.getProductSkuBySpecId(cityId, specId, ownerId, source);
    }

    @Override
    public ProductSkuDTO selectBySkuId(Long skuId) {
        return productSkuQueryBL.selectBySkuId(skuId);
    }

    @Override
    public boolean isHaveReplaceToSku(List<ProductSkuDTO> skuList) {
        return productSkuQueryBL.isHaveReplaceToSku(skuList);
    }

    private void verifyQueryProductCodeParam(Integer orgId, List<Long> skuIds) {
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(skuIds, "skuId不能为空");
    }

    @Override
    public Map<Long, List<ProductSkuDTO>> listRelationPackageSku(ProductSkuQueryDTO queryDto) {
        return productSkuQueryBL.listRelationPackageSku(queryDto);
    }

    @Override
    public List<ProductSkuDTO> listSkuDetails(QuerySkuDetailsParamDTO querySkuDetailsParamDTO) {
        return productSkuQueryBL.listSkuDetails(querySkuDetailsParamDTO);
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置）
     */
    @Override
    public List<ProductSkuDTO> findProductBySkuFull(Integer warehouseId, List<Long> productSkuIds) {
        return productSkuQueryBL.findProductBySkuFull(warehouseId, productSkuIds);
    }

    @Override
    public Map<Long, ProductImageDTO> saasGetProductImageUrl(ProductSkuQueryDTO productSkuQueryDTO) {
        return productSkuQueryBL.getProductImageUrl(productSkuQueryDTO.getSkuIdList());
    }

    /**
     * 根据skuId查找产品（完整的sku仓库配置）商户
     */
    @Override
    public List<ProductSkuDTO> findByProductSkuIds(ProductSkuQueryDTO queryDto) {
        AssertUtils.notNull(queryDto, "查询参数不能为空");
        AssertUtils.notNull(queryDto.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDto.getSkuIdList(), "SKU_ID不能为空");
        return productSkuQueryBL.findBySkuFull(queryDto.getWarehouseId(), queryDto.getSkuIdList());
    }

    /**
     * 获取融销sku信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductSkuDTO> getFsProductSku(FinanceSaleProductSkuQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDTO.getProductSpecificationIds(), "包装规格信息不能为空");

        return productSkuQueryBL.getFsProductSku(queryDTO);
    }

    /**
     * 根据外部编码查询产品列表
     */
    @Override
    public List<ProductSkuDTO> listProductSkuByOutCode(ProductSkuQueryDTO queryDto) {
        AssertUtils.notNull(queryDto, "查询参数不能为空");
        AssertUtils.notNull(queryDto.getCityId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDto.getOuterCodeList(), "外部编码不能为空");
        return productSkuQueryBL.findBySkuOuterCode(queryDto.getCityId(), queryDto.getOuterCodeList());
    }

    /**
     * 根据订单产品查询实际发货产品sku （key：订单产品skuId，value：实际发货sku）
     *
     * @param productSkuDeliverySO
     * @return
     */
    @Override
    public Map<Long, ProductSkuDTO> getProductSkuDeliveryMapWithSecOwnerId(ProductSkuDeliverySO productSkuDeliverySO) {
        return productSkuQueryBL.getProductSkuDeliveryMapWithSecOwnerId(productSkuDeliverySO);
    }

    @Override
    public List<ProductSkuDTO> findSkuFullByCondition(ProductSkuQueryDTO queryDTO) {
        return productSkuQueryBL.findSkuFullByCondition(queryDTO);
    }
}
