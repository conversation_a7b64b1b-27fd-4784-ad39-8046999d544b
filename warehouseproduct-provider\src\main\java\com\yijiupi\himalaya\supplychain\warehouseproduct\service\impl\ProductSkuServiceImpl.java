package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuServiceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuAndCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productiondate.ProductionDateQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service(timeout = 60000)
public class ProductSkuServiceImpl implements IProductSkuService {
    @Autowired
    private ProductSkuServiceBL bl;

    /**
     * 产品信息导入
     *
     * @param productSkuDTO
     * @return: void
     */
    @Override
    public void add(ProductSkuDTO dto) {
        this.bl.add(dto);
    }

    /**
     * 修改
     */
    @Override
    public void update(ProductSkuDTO dto) {
        this.bl.add(dto);
    }

    /**
     * 产品信息修改只调整名称目前
     *
     * @param list
     * @return: void
     */
    @Override
    public void updateName(ProductSkuUpdateNameDTO dto) {
        this.bl.updateName(dto);
    }

    /**
     * 获取SKU序号
     *
     * @param cityId
     * @param productSkuId
     * @return
     * @return: Map<Long, Integer>
     */
    @Override
    public Map<Long, Integer> getSequenceByCityAndProductSkuIds(Integer cityId, List<Long> productSkuIds) {
        return this.bl.getSequenceByCityAndProductSkuIds(cityId, productSkuIds);
    }

    /**
     * 根据城市id,产品名称查询产品列表.
     */
    @Override
    public PageList<ProductSkuInfoReturnDTO> getProductSkuInfo(ProductSkuInfoSearchDTO productSkuInfoSearchDTO) {
        return bl.getProductSkuInfo(productSkuInfoSearchDTO);
    }

    /**
     * 根据skuid批量查询配送系数
     *
     * @param productSkuIdList
     * @return
     * @return: Map<skuid, DistributionPercentForAmount>
     */
    @Override
    public Map<Long, BigDecimal> getDistributionPercentForAmountBySkuList(List<Long> productSkuIdList) {
        if (productSkuIdList == null || productSkuIdList.isEmpty()) {
            return new HashMap<>(16);
        }
        return bl.getProductInfoBySkuList(productSkuIdList);
    }

    /**
     * 根据时间段查询SKU的数量
     *
     * @param params
     * @return
     */
    @Override
    public int countProductInPeriod(Map<String, Object> params) {
        return bl.countProductInPeriod(params);
    }

    @Override
    public List<OwnerInfoDTO> getOwnerInfoBySkuId(List<Long> productSkuList) {
        return bl.getOwnerInfoBySkuId(productSkuList);
    }

    @Override
    public Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySkuId(List<Long> productSkuList) {
        AssertUtils.notNull(productSkuList, "skuId不能为null");
        return bl.getProductInfoBySkuId(productSkuList);
    }

    @Override
    public Byte getProductFeatureBySkuId(List<Long> productSkuList, Integer warehouseId) {
        AssertUtils.notEmpty(productSkuList, "skuId不能为null");
        AssertUtils.notNull(warehouseId, "仓库id不能为null");
        return bl.getProductFeatureBySkuId(productSkuList, warehouseId);
    }

    // @Override
    // public Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySpecificationId(List<Long> productSpecificationList) {
    // AssertUtils.notNull(productSpecificationList, "规格参数Id不能为null");
    // return bl.getProductInfoBySpecificationId(productSpecificationList);
    // }

    @Override
    public Map<Long, ProductSkuInfoReturnDTO> getProductInfoBySpecificationIdAndSource(Integer city_Id,
        List<ProductSkuBySpecificationQueryDTO> productSpecificationList, Integer source) {
        AssertUtils.notEmpty(productSpecificationList, "规格参数Id不能为空");
        AssertUtils.notNull(city_Id, "city_Id不能为null");
        AssertUtils.notNull(source, "source不能为null");
        return bl.getProductInfoBySpecificationId(city_Id, productSpecificationList, source);
    }

    @Override
    public Boolean checkProductBySkuIdList(List<Long> productSkuIdList) {
        AssertUtils.notNull(productSkuIdList, "skuId不能为null");
        return bl.checkProductBySkuIdList(productSkuIdList);
    }
    //
    // @Override
    // public Boolean checkProductBySpecificationIdList(List<Long> productSpecificationList) {
    // AssertUtils.notNull(productSpecificationList, "规格参数id不能为null");
    // return bl.checkProductBySpecificationIdList(productSpecificationList);
    //
    // }

    /**
     * 获取商品详情
     *
     * @param productSkuInfoSO
     * @return
     */
    @Override
    public PageList<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO) {
        return bl.listProductSkuInfo(productSkuInfoSO);
    }

    /**
     * 获取商品详情（包含库存数）
     *
     * @param productSkuInfoSO
     * @return
     */
    @Override
    public PageList<ProductSkuInfoDTO> listProductSkuInfoIncludeStore(ProductSkuInfoSO productSkuInfoSO) {
        return bl.listProductSkuInfoIncludeStore(productSkuInfoSO);
    }

    /**
     * 根据产品skuId查询产品信息
     */
    @Override
    public List<ProductSkuInfoDTO> listProductSkuInfoBySkuId(Integer cityId, List<Long> productSkuIds) {
        AssertUtils.notEmpty(productSkuIds, "SkuId集合不能为空！");
        return bl.listProductSkuInfoBySkuId(cityId, productSkuIds);
    }

    /**
     * 根据产品skuId查询产品信息
     *
     * @param cityId 城市 id
     * @param productSkuIds skuId
     */
    @Override
    public List<ProductSkuInfoDTO> findProductSkuInfo(Integer cityId, List<Long> productSkuIds) {
        AssertUtils.notEmpty(productSkuIds, "SkuId集合不能为空！");
        return bl.findProductSkuInfo(cityId, productSkuIds);
    }

    /**
     * 查询城市所有SKU数量
     */
    @Override
    public int countProductByCity(Integer cityId) {
        return bl.countProductByCity(cityId);
    }

    /**
     * 根据产品skuId查询产品生产日期详情
     *
     * @return
     */
    @Override
    public List<ProductionDateDTO> listProductionDate(Integer cityId, Integer warehouseId, List<Long> productSkuIds) {
        return bl.listProductionDate(cityId, warehouseId, productSkuIds);
    }

    /**
     * 根据产品skuId查询sku下的库存和最新生产日期，区分仓库配置版本 2.5，,2.5+
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductionDateDTO> querySkuInventoryAndProductionDate(ProductionDateQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(queryDTO.getProductList(), "产品信息不能为空！");
        return bl.querySkuInventoryAndProductionDate(queryDTO);
    }

    /**
     * 获取箱码和瓶码集合
     *
     * @return
     */
    @Override
    public Map<Long, ProductCodeDTO> getPackageAndUnitCode(Set<Long> skuIdSet, Integer cityId) {
        return bl.getPackageAndUnitCode(skuIdSet, cityId);
    }

    /**
     * 获取箱码和瓶码集合
     *
     * @return
     */
    @Override
    public Map<Long, ProductCodeDTO> getPackageAndUnitCodeBySkuId(ProductCodeQueryDTO codeQueryDTO) {
        return bl.getPackageAndUnitCode(codeQueryDTO.getSkuIdSet(), codeQueryDTO.getCityId());
    }

    @Override
    public Map<Long, ProductSkuAndCategoryDTO> getProductSkuAndCategoryInfoBySkuId(List<Long> productSkuList) {
        return bl.getProductSkuAndCategoryInfoBySkuId(productSkuList);
    }

    /**
     * 根据产品skuId查询产品信息
     *
     * @return
     */
    @Override
    public ProductSkuInfoReturnDTO findProductSkuInfoBySkuId(Long productSkuId) {
        AssertUtils.notNull(productSkuId, "SkuId不能为空！");
        return bl.findProductSkuInfoBySkuId(productSkuId);
    }

    /**
     * 通过瓶码或者箱码查询sku列表
     *
     * @param code 条码
     * @param cityId
     * @return
     */
    @Override
    public List<Long> getProductSkuIdByCode(String code, Integer cityId) {
        AssertUtils.notNull(code, "条码不能为空！");
        AssertUtils.notNull(cityId, "城市ID不能为空！");
        return bl.getProductSkuIdByCode(code, cityId);
    }

    @Override
    public List<ProductSkuDTO> getProductCharacteristic(Set<Long> productSkuList, Integer warehouseId) {
        return bl.getProductCharacteristic(productSkuList, warehouseId);
    }

    /**
     * 查询sku条码信息
     *
     * @param skuDTOList
     * @return
     */
    @Override
    public List<ProductSkuDTO> listSkuCode(List<ProductSkuDTO> skuDTOList) {
        return bl.listSkuCode(skuDTOList);
    }

    /**
     * 根据SKUID获取商品条码
     *
     * @return
     */
    @Override
    public List<ProductInfoCodeDTO> getProductInfoCodes(ProductSkuInfoSO productSkuInfoSO) {
        return bl.getProductInfoCodes(productSkuInfoSO);
    }

    /**
     * 清理条码缓存
     *
     * @param productSpecificationId
     */
    @Override
    public void clearProductCodeCache(Integer productSpecificationId) {
        bl.clearProductCodeCache(productSpecificationId);
    }

    /**
     * 获取SKU分仓属性
     *
     * @param productSkuInfoSO
     * @return: Map<Long, Byte>
     */
    @Override
    public Map<Long, String> getSkuConfigStorageAttribute(ProductSkuInfoSO productSkuInfoSO) {
        return bl.getSkuConfigStorageAttribute(productSkuInfoSO);
    }

    @Override
    public void productWarehouseAllocationTypeVerify(ProductWarehouseAllocationTypeVerifyQuery query) {
        bl.productWarehouseAllocationTypeVerify(query);
    }
}
