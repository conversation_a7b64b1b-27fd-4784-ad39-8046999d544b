package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceNewBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSourceCodeInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSourceService;

@Service(timeout = 60000)
public class ProductSourceServiceImpl implements IProductSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSourceServiceImpl.class);

    @Autowired
    private ProductSourceBL productSourceBL;

    @Autowired
    private ProductSourceNewBL productSourceNewBL;

    @Override
    public List<String> generateProductSourceCode(ProductSourceCodeGeneratorDTO productSourceCodeGeneratorDTO) {
        AssertUtils.notNull(productSourceCodeGeneratorDTO, "参数不能为空");
        AssertUtils.notNull(productSourceCodeGeneratorDTO.getCount(), "生成数量不能为空");
        AssertUtils.notNull(productSourceCodeGeneratorDTO.getWarehouseId(), "仓库id不能为空");
        return productSourceBL.generateProductSourceCode(productSourceCodeGeneratorDTO);
    }

    @Override
    public String batchSaveProductSourceCode(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        return productSourceBL.batchSaveProductSourceCode(productSourceCodeDTOS);
    }

    @Override
    public void productSourceCodesAssociateOrder(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        LOG.info("溯源码关联订单数据:{}", JSON.toJSONString(productSourceCodeDTOS));
        AssertUtils.notEmpty(productSourceCodeDTOS, "溯源码数据不能为空");
        productSourceCodeDTOS.forEach(code -> {
            AssertUtils.notNull(code.getCode(), "溯源码不能为空");
            AssertUtils.notNull(code.getBusinessNo(), "单据编号不能为空");
        });
        productSourceBL.productSourceCodesAssociateOrder(productSourceCodeDTOS);
    }

    @Override
    public Boolean checkProductSource(Long configId, String code) {
        AssertUtils.notNull(configId, "控货策略id不能为空");
        AssertUtils.notNull(code, "溯源码不能为空");
        return productSourceBL.checkProductSource(configId, code);
    }

    @Override
    public PageList<ProductSourceCodeDTO>
        pageListProductSourceCode(ProductSourceCodeQueryDTO productSourceCodeQueryDTO) {
        return productSourceBL.pageListProductSourceCode(productSourceCodeQueryDTO);
    }

    @Override
    public void collectProductSourceCode(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        AssertUtils.notEmpty(productSourceCodeDTOS, "溯源码采集信息不能为空");
        productSourceCodeDTOS.forEach(sourceCode -> {
            AssertUtils.notNull(sourceCode.getCode(), "溯源码不能为空");
            AssertUtils.notNull(sourceCode.getConfigId(), "控货策略不能为空");
            AssertUtils.notNull(sourceCode.getBusinessNo(), "订单号不能为空");
            AssertUtils.notNull(sourceCode.getBusinessId(), "订单id不能为空");
        });
        productSourceBL.collectProductSourceCode(productSourceCodeDTOS);
    }

    @Override
    public void deleteBySourceBusinessNos(List<String> sourceBusinessNos) {
        AssertUtils.notEmpty(sourceBusinessNos, "来源单据不能为空");
        LOG.info("通过来源单据删除溯源码:{}", JSON.toJSONString(sourceBusinessNos));
        productSourceBL.deleteBySourceBusinessNos(sourceBusinessNos);
    }

    @Override
    public void checkRepeatedProductSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        AssertUtils.notEmpty(productSourceCodeDTOS, "数据不能为空");
        productSourceBL.checkRepeatedProductSourceCodes(productSourceCodeDTOS);
    }

    /**
     * 修改溯源码
     */
    @Override
    public void updateProductSourceCode(ProductSourceCodeDTO codeDTO) {
        productSourceBL.updateProductSourceCode(codeDTO);
    }

    /**
     * 删除溯源码
     */
    @Override
    public void deleteProductSourceCode(Long id) {
        productSourceBL.deleteProductSourceCode(id);
    }

    /**
     * 获取溯源码记录列表
     * 
     * @return
     */
    @Override
    public PageList<ProductSourceCodeRecordDTO> listProductSourceCodeRecord(ProductSourceCodeRecordSO recordSO) {
        return productSourceBL.listProductSourceCodeRecord(recordSO);
    }

    /**
     * 查询溯源码
     * 
     * @return
     */
    @Override
    public ProductSourceCodeDTO getProductSourceCodeDTO(String code) {
        return productSourceBL.getProductSourceCodeDTO(code);
    }

    /**
     * 新增溯源码记录
     */
    @Override
    public void saveProductSourceCodeRecord(List<ProductSourceCodeRecordDTO> recordDTOList) {
        productSourceBL.saveProductSourceCodeRecord(recordDTOList);
    }

    /**
     * 溯源码操作
     */
    @Override
    public void processSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        AssertUtils.notEmpty(productSourceCodeDTOS, "溯源码采集信息不能为空");
        productSourceCodeDTOS.forEach(sourceCode -> {
            AssertUtils.notNull(sourceCode.getBusinessNo(), "订单号不能为空");
            // AssertUtils.notNull(sourceCode.getBusinessId(), "订单id不能为空");
            AssertUtils.notNull(sourceCode.getCode(), "溯源码不能为空");
            AssertUtils.notNull(sourceCode.getConfigId(), "控货策略不能为空");
        });
        productSourceBL.processSourceCodes(productSourceCodeDTOS);
    }

    @Override
    public Map<Long, List<String>> findErrProductSourceCodes(Map<Long, List<String>> configIdAndSourceCodeMap) {
        return productSourceBL.findErrProductSourceCodes(configIdAndSourceCodeMap);
    }

    @Override
    public void checkProductSourceCodes(List<ProductSourceCodeDTO> productSourceCodeDTOS) {
        AssertUtils.notEmpty(productSourceCodeDTOS, "溯源码采集信息不能为空");
        productSourceCodeDTOS.forEach(sourceCode -> {
            AssertUtils.notNull(sourceCode.getBusinessNo(), "订单号不能为空");
            AssertUtils.notNull(sourceCode.getBusinessId(), "订单id不能为空");
            AssertUtils.notNull(sourceCode.getCode(), "溯源码不能为空");
            AssertUtils.notNull(sourceCode.getConfigId(), "控货策略不能为空");
        });
        productSourceBL.checkProductSourceCodes(productSourceCodeDTOS);
    }

    @Override
    public void clearRelationByCodes(List<String> codes, String operator) {
        productSourceBL.clearRelationByCodes(codes, operator);
    }

    @Override
    public void clearRelationByBusinessNos(List<String> businessNos, String operator) {
        productSourceBL.clearRelationByBusinessNos(businessNos, operator);
    }

    @Override
    public void bindProductSourceCodes(List<ProductSourceCodeBindDTO> productSourceCodeBindDTOS) {
        AssertUtils.notEmpty(productSourceCodeBindDTOS, "溯源码信息不能为空");
        productSourceBL.bindProductSourceCodes(productSourceCodeBindDTOS);
    }

    @Override
    public void checkProductSourceCodeAndConfig(List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS) {
        productSourceBL.checkProductSourceCodeAndConfig(productSourceCodeCheckDTOS);
    }

    /**
     * 根据溯源码和控货策略组合
     */
    @Override
    public Map<Long, List<String>> sourceCodeMatchControlConfig(List<String> sourceCodes, List<Long> configIds) {
        AssertUtils.notEmpty(sourceCodes, "溯源码信息不能为空");
        AssertUtils.notEmpty(configIds, "控货策略信息不能为空");
        return productSourceBL.sourceCodeMatchControlConfig(sourceCodes, configIds);
    }

    @Override
    public List<ProductSourceCodeDTO> getProductSourceCodeAndRecord(String code) {
        return productSourceBL.getProductSourceCodeAndRecord(code);
    }

    @Override
    public void modifyProductSourceCodeState(Long codeId, Byte state, String operator) {
        productSourceNewBL.modifyProductSourceCodeState(codeId, state, operator);
    }

    @Override
    public Map<Long, ProductControlConfigDTO> getControlConfigBySourceCodeId(List<Long> sourceCodeIds) {
        return productSourceNewBL.getControlConfigBySourceCodeId(sourceCodeIds);
    }

    /**
     * 溯源码校验 根据溯源码查询在库数据，校验能否出库
     *
     * @param productSourceCodeCheckDTOS
     */
    @Override
    public List<ProductSourceCodeInfoDTO>
        checkProductSourceId(List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS) {
        productSourceCodeCheckDTOS.forEach(sourceCode -> {
            AssertUtils.notNull(sourceCode.getSourceCode(), "溯源码不能为空");
            AssertUtils.notNull(sourceCode.getProductSkuId(), "skuId不能为空");
        });
        LOG.info("[溯源码校验]请求参数：{}", JSON.toJSONString(productSourceCodeCheckDTOS));
        // return productSourceBL.checkProductSourceId(productSourceCodeCheckDTOS);
        return productSourceBL.checkProductSourceIdNew(productSourceCodeCheckDTOS);
    }

    /**
     * 溯源码 出入库修改溯源码相关信息
     *
     * @param updateProductSourceCodeDTOS
     */
    @Override
    public void updateProductSourceCodeInfo(List<UpdateProductSourceCodeDTO> updateProductSourceCodeDTOS) {
        AssertUtils.notEmpty(updateProductSourceCodeDTOS, "数据不能为空");
        updateProductSourceCodeDTOS.forEach(sourceCode -> {
            AssertUtils.notNull(sourceCode.getProductSourceCodeId(), "溯源码id不能为空");
            AssertUtils.notNull(sourceCode.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(sourceCode.getBusinessType(), "单据类型不能为空");
        });

        productSourceBL.updateProductSourceCodeInfo(updateProductSourceCodeDTOS);
    }

    @Override
    public void syncProductSourceCodeTrace(ProductSourceCodeRecordSyncDTO codeRecordSyncDTO) {
        productSourceNewBL.syncProductSourceCodeTrace(codeRecordSyncDTO);
    }
}
