package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSupplierBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierBySpecIdDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

@Service
public class ProductSupplierServiceImpl implements IProductSupplierService {

    @Autowired
    private ProductSupplierBL productSupplierBL;

    @Override
    @Async(value = "warehouseProductTaskExecutor")
    public void batchSaveProductSupplier(List<ProductSupplierDTO> productSupplierList) {
        productSupplierBL.batchSaveProductSupplier(productSupplierList);
    }

    @Override
    public List<ProductSupplierDTO> selectProductSupplierBySpecInfo(ProductSupplierBySpecIdDTO queryDTO) {
        return productSupplierBL.selectProductSupplierBySpecInfo(queryDTO);
    }

}
