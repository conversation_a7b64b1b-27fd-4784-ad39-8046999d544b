package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SalesAreaServiceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISalesAreaService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 控货策略
 *
 * <AUTHOR>
 * @date 2019-10-12 10:51
 */
@Service
public class SalesAreaServiceImpl implements ISalesAreaService {

    @Autowired
    private SalesAreaServiceBL serviceBL;

    /**
     * 新增销售区域配置
     */
    @Override
    public SalesAreaConfigDTO insertOrUpdate(SalesAreaConfigDTO areaConfigDTO) {
        return serviceBL.insertOrUpdate(areaConfigDTO);
    }

    /**
     * 新增销售区域配置-子表
     */

    @Override
    public List<SalesAreaItemDTO> insertItemOrUpdateItem(List<SalesAreaItemDTO> items) {
        return serviceBL.insertItemOrUpdateItem(items);
    }

    /**
     * 查询销售区域配置(仓库Id，企业编码)
     */
    @Override
    public PageList<SalesAreaConfigDTO> selectPageList(SalesAreaQueryDTO query) {
        return serviceBL.selectPageList(query);
    }

    /**
     * 根据条件查询销售范围
     *
     * @return
     */

    @Override
    public List<SalesAreaConfigDTO> selectByIds(List<Long> ids) {
        return serviceBL.selectByIds(ids);
    }

    /**
     * 根据条件查询销售范围-子表
     *
     * @return
     */

    @Override
    public List<SalesAreaItemDTO> selectItemsByParentId(Long parentId) {
        return serviceBL.selectItemsByParentId(parentId);
    }

    /**
     * 根据条件删除销售范围
     */

    @Override
    public void deleteConfigByIds(List<Long> ids) {
        serviceBL.deleteConfigByIds(ids);
    }

    /**
     * 根据条件删除销售范围
     */
    @Override
    public void deleteItemByIds(Long configId, List<Long> itemIds) {
        serviceBL.deleteItemByIds(configId, itemIds);
    }

    /**
     * 根据条件，启用/禁用销售范围
     */
    @Override
    public void updateConfigState(List<Long> ids, Byte state) {
        serviceBL.updateConfigState(ids, state);
    }

    /**
     * 根据条件，启用/禁用销售范围
     */
    @Override
    public void updateItemState(List<Long> ids, Byte state) {
        serviceBL.updateItemState(ids, state);
    }

    /**
     * 查询销售区域配置(仓库Id，企业编码)-子表
     * 
     * @param query
     */
    @Override
    public PageList<SalesAreaItemDTO> pageListSalesAreaItems(SalesAreaItemQueryDTO query) {
        return serviceBL.pageListSalesAreaItems(query);
    }
}
