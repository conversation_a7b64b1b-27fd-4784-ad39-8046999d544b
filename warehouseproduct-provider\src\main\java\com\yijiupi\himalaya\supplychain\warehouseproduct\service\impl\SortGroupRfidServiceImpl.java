package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SortGroupRfidBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupRfidService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 分区标签列配置
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Service(timeout = 30000)
public class SortGroupRfidServiceImpl implements ISortGroupRfidService {

    @Autowired
    private SortGroupRfidBL sortGroupRfidBL;

    @Override
    public List<SortGroupRfidDTO> listSortGroupRfidInfo(SortGroupRfidSO so) {
        return sortGroupRfidBL.listSortGroupRfid(so);
    }

    @Override
    public PageList<SortGroupRfidDTO> pageListSortGroupRfidInfo(SortGroupRfidSO so) {
        return sortGroupRfidBL.pageListSortGroupRfid(so);
    }

    @Override
    public void batchSaveSortGroupRfid(List<SortGroupRfidDTO> addDTOList) {
        sortGroupRfidBL.batchSaveSortGroupRfid(addDTOList);
    }

    @Override
    public void batchDeleteBatchByIds(List<Long> ids) {
        sortGroupRfidBL.deleteBatchByIds(ids);
    }

    @Override
    public void batchUpdate(List<SortGroupRfidDTO> addDTOList) {
        sortGroupRfidBL.batchUpdate(addDTOList);
    }
}
