package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;
import java.util.Objects;

import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SortGroupBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;

/**
 * 分区
 *
 * <AUTHOR>
 * @since 2018/5/15 20:01
 */
@Service
public class SortGroupServiceImpl implements ISortGroupService {

    @Autowired
    private SortGroupBL sortGroupBL;
    private static final Logger LOGGER = LoggerFactory.getLogger(SortGroupServiceImpl.class);

    /**
     * 获取分区列表
     */
    @Override
    public PageList<SortGroupListDTO> listGroup(SortGroupSO so) {
        return sortGroupBL.listGroup(so);
    }

    /**
     * 查看分区
     */
    @Override
    public SortGroupDTO getGroup(Long id) {
        return sortGroupBL.getGroup(id);
    }

    /**
     * 新增分区
     */
    @Override
    public Long insertGroup(SortGroupDTO dto) {
        return sortGroupBL.insertGroup(dto);
    }

    /**
     * 修改分区
     */
    @Override
    public void updateGroup(SortGroupDTO dto) {
        sortGroupBL.updateGroup(dto);
    }

    /**
     * 删除分区
     *
     * @param id 分区ID
     */
    @Override
    public void deleteGroup(Long id) {
        sortGroupBL.deleteGroup(id);
    }

    /**
     * 根据分区设置获取分区人员列表
     */
    @Override
    public List<SortGroupUserDTO> listGroupUserBySelect(SortGroupUserSelectSO so) {
        return sortGroupBL.listGroupUserBySelect(so);
    }

    /**
     * 获取分区人员列表
     */
    @Override
    public PageList<SortGroupUserDTO> listGroupUser(SortGroupUserSO so) {
        return sortGroupBL.listGroupUser(so);
    }

    /**
     * 查看分区人员
     */
    @Override
    public SortGroupUserDTO getGroupUser(Long id) {
        return sortGroupBL.getGroupUser(id);
    }

    /**
     * 新增分区人员
     */
    @Override
    public Long insertGroupUser(SortGroupUserDTO dto) {
        return sortGroupBL.insertGroupUser(dto);
    }

    /**
     * 修改分区人员
     */
    @Override
    public void updateGroupUser(SortGroupUserDTO dto) {
        sortGroupBL.updateGroupUser(dto);
    }

    /**
     * 删除分区人员
     */
    @Override
    public void deleteGroupUser(Long id) {
        sortGroupBL.deleteGroupUser(id);
    }

    /**
     * 获取分区人员所属的分区id集合
     *
     * @param flag 分区标识 0：分区拣货 1：分区补货 2：分区上架
     */
    @Override
    public List<Long> listGroupIdByUserId(Integer userId, Byte flag) {
        return sortGroupBL.listGroupIdByUserId(userId, flag);
    }

    /**
     * 根据条件查询分区信息
     */
    @Override
    public List<SortGroupDTO> findSortGroupTotalList(SortGroupSO so) {
        return sortGroupBL.findSortGroupTotalList(so);
    }

    /**
     * 根据分区 id 查询关联的货位名
     *
     * @param groupIds 分区 id
     * @return 货位名
     */
    @Override
    public List<String> listSortNameByGroupIds(List<Long> groupIds) {
        return sortGroupBL.listSortNameByGroupIds(groupIds);
    }

    /**
     * 根据分区 id 查询关联的货位名
     *
     * @param groupIds 分区 id
     * @return 货位名
     */
    @Override
    public List<String> listLocationNameByGroupIds(List<Long> groupIds) {
        return sortGroupBL.listLocationNameByGroupIds(groupIds);
    }

    /**
     * 启用分区
     *
     * @param dto
     */
    @Override
    public void enableSortGroup(SortGroupDTO dto) {
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        LOGGER.info("启用分区，用户信息为:{}", userId);
        if (Objects.nonNull(userId)) {
            dto.setCreateUser(userId.toString());
        }
        sortGroupBL.enableSortGroup(dto);
    }

    /**
     * 停用分区
     *
     * @param dto
     */
    @Override
    public void disableSortGroup(SortGroupDTO dto) {
        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        LOGGER.info("停用分区，用户信息为:{}", userId);
        if (Objects.nonNull(userId)) {
            dto.setCreateUser(userId.toString());
        }
        sortGroupBL.disableSortGroup(dto);
    }

}
