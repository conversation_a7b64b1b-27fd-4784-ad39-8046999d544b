package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.SortUserConfigBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.StatisticsCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortUserConfigService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 分拣员
 *
 * <AUTHOR> 2018/3/19
 */
@Service
public class SortUserConfigServiceImpl implements ISortUserConfigService {
    @Autowired
    private SortUserConfigBL sortUserConfigBL;

    @Override
    public List<SortUserConfigDTO> findSortUserByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为null");
        return sortUserConfigBL.findSortUserByWarehouseId(warehouseId);
    }

    @Override
    public PageList<SortUserConfigDTO> selectSortUser(SelectSortUser selectSortUser) {
        AssertUtils.notNull(selectSortUser.getType(), "查询事件不能为null");
        return sortUserConfigBL.selectSortUser(selectSortUser);
    }

    @Override
    public SortUserConfigDTO findSortUserById(Integer id) {
        AssertUtils.notNull(id, " id不能为null");
        return sortUserConfigBL.findSortUserById(id);
    }

    @Override
    public void addSortUserConfig(SortUserConfigDTO sortUserConfigDTO) {
        AssertUtils.notNull(sortUserConfigDTO.getUserId(), " 用户id不能为null");
        AssertUtils.notNull(sortUserConfigDTO.getCreateUser(), "操作人不能为null");
        AssertUtils.notNull(sortUserConfigDTO.getType(), "操作事件不能为null");
        sortUserConfigBL.addSortUserConfig(sortUserConfigDTO);
    }

    @Override
    public void updateSortUserConfig(SortUserConfigDTO sortUserConfigDTO) {
        AssertUtils.notNull(sortUserConfigDTO.getUserId(), " 用户id不能为null");
        AssertUtils.notNull(sortUserConfigDTO.getCreateUser(), "操作人不能为null");
        AssertUtils.notNull(sortUserConfigDTO.getType(), "操作事件不能为null");
        sortUserConfigBL.updateSortUserConfig(sortUserConfigDTO);

    }

    @Override
    public SortUserConfigDTO findSortUserProperty(Integer userId, Byte type) {
        AssertUtils.notNull(userId, " 用户id不能为null");
        AssertUtils.notNull(type, "操作事件不能为null");
        return sortUserConfigBL.findSortUserProperty(userId, type);
    }

    @Override
    public void deleteSortUserConfig(Integer userId, Byte type) {
        AssertUtils.notNull(userId, " 用户id不能为null");
        AssertUtils.notNull(type, " 操作事件不能为null");
        sortUserConfigBL.deleteSortUserConfig(userId, type);

    }

    @Override
    public SortPropertyDTO findSortProperty(FindAllProperty findAllProperty) {
        AssertUtils.notNull(findAllProperty.getWarehouseId(), " 仓库id不能为null");
        AssertUtils.notNull(findAllProperty.getCityId(), " 城市id不能为null");
        return sortUserConfigBL.findSortProperty(findAllProperty);
    }

    @Override
    public List<StatisticsCategoryDTO> findCategorys(Integer cityId) {
        AssertUtils.notNull(cityId, " 城市id不能为null");
        return sortUserConfigBL.findCategorys(cityId);
    }

    @Override
    public List<SortUserConfigDTO> findSortPropertyByUser(SortPropertyByUserDTO sortPropertyByUserDTO) {
        AssertUtils.notNull(sortPropertyByUserDTO.getType(), " 策略类型不能为null");
        AssertUtils.notEmpty(sortPropertyByUserDTO.getPropertyList(), "策略值不能为null");
        return sortUserConfigBL.findSortPropertyByUser(sortPropertyByUserDTO);
    }
}
