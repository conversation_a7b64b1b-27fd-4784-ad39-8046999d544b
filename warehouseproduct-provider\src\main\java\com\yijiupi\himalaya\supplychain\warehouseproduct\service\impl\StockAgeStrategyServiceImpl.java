package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StockAgeStrategyBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStockAgeStrategyService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StockAgeStrategyServiceImpl implements IStockAgeStrategyService {

    @Autowired
    private StockAgeStrategyBL stockAgeStrategyBL;

    @Override
    public void saveStockAgeStrategy(StockAgeStrategyDTO stockAgeStrategyDTO) {
        AssertUtils.notEmpty(stockAgeStrategyDTO.getStockAgeStrategyConfigDTOS(), "策略配置不能为空");
        stockAgeStrategyBL.saveStockAgeStrategy(stockAgeStrategyDTO);
    }

    @Override
    public void updateStockAgeStrategy(StockAgeStrategyDTO stockAgeStrategyDTO) {
        stockAgeStrategyBL.updateStockAgeStrategy(stockAgeStrategyDTO);
    }

    @Override
    public void updateStockAgeStrategyAll(StockAgeStrategyDTO stockAgeStrategyDTO) {
        stockAgeStrategyBL.updateStockAgeStrategyAll(stockAgeStrategyDTO);
    }

    @Override
    public PageList<StockAgeStrategyDTO> pageListStockAgeStrategy(StockAgeStrategyQueryDTO stockAgeStrategyQueryDTO) {
        return stockAgeStrategyBL.pageListStockAgeStrategy(stockAgeStrategyQueryDTO);
    }

    @Override
    public PageList<StockAgeStrategyConfigDTO>
        pageListStockAgeStrategyConfig(StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO) {
        return stockAgeStrategyBL.pageListStockAgeStrategyConfig(stockAgeStrategyConfigQueryDTO);
    }

    @Override
    public PageList<StockAgeStrategyDTO>
        pageListStockAgeProductStrategy(StockAgeProductQueryDTO stockAgeProductQueryDTO) {
        return stockAgeStrategyBL.pageListStockAgeProductStrategy(stockAgeProductQueryDTO);
    }

    @Override
    public Map<Long, Boolean> isProductStockAgeControls(Integer cityId, Integer warehouseId, List<Long> productSkuIds,
        Boolean selectRefProduct) {
        if (selectRefProduct == null || !selectRefProduct) {
            return stockAgeStrategyBL.isProductStockAgeControls(cityId, warehouseId, productSkuIds);
        } else {
            return stockAgeStrategyBL.isRefProductStockAgeControls(cityId, warehouseId, productSkuIds);
        }
    }

    @Override
    public List<StockAgeControlProductDTO> isProductStockAgeControls(StockAgeControlProductQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDTO.getSkuIdList(), "产品SkuId不能为空");
        List<Long> skuIdList =
            queryDTO.getSkuIdList().stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        Map<Long, Boolean> result = null;
        if (queryDTO.getSelectRefProduct() == null || !queryDTO.getSelectRefProduct()) {
            result = stockAgeStrategyBL.isProductStockAgeControls(queryDTO.getCityId(), queryDTO.getWarehouseId(),
                skuIdList);
        } else {
            result = stockAgeStrategyBL.isRefProductStockAgeControls(queryDTO.getCityId(), queryDTO.getWarehouseId(),
                skuIdList);
        }
        if (result == null || result.isEmpty()) {
            return new ArrayList<>();
        }
        List<StockAgeControlProductDTO> resultList = new ArrayList<>();
        result.forEach((k, v) -> {
            StockAgeControlProductDTO ctrlDTO = new StockAgeControlProductDTO();
            ctrlDTO.setSkuId(k);
            ctrlDTO.setStoreAgeControlProduct(v);
            resultList.add(ctrlDTO);
        });
        return resultList;
    }

    /**
     * 根据规格货主查询产品是否库龄管控产品
     */
    public List<StockAgeControlProductBySpecDTO>
        isProductStockAgeControlsBySpec(ProductSkuBySpecificationSO productSkuBySpecSO) {
        return stockAgeStrategyBL.isProductStockAgeControlsBySpec(productSkuBySpecSO);
    }

    /**
     * 根据库存信息附带的规格、货主、二级货主判断是否库龄管控产品
     */
    public Boolean isProductStockAgeControlsBySpec(Integer cityId, Integer warehouseId, Long productSpecificationId,
        Long ownerId, Long secOwnerId) {
        // 判断是否库龄管控产品
        ProductSkuBySpecificationQueryDTO specQueryDTO = new ProductSkuBySpecificationQueryDTO();
        specQueryDTO.setProductSpecificationId(productSpecificationId);
        specQueryDTO.setOwnerId(ownerId);
        specQueryDTO.setSecOwnerId(secOwnerId);
        // 查询参数组装
        ProductSkuBySpecificationSO specificationSO = new ProductSkuBySpecificationSO();
        specificationSO.setCityId(cityId);
        specificationSO.setWarehouseId(warehouseId);
        specificationSO.setSpecList(Lists.newArrayList(specQueryDTO));
        List<StockAgeControlProductBySpecDTO> productStockAgeControlsBySpec =
            stockAgeStrategyBL.isProductStockAgeControlsBySpec(specificationSO);
        if (CollectionUtils.isEmpty(productStockAgeControlsBySpec)) {
            return false;
        }
        return productStockAgeControlsBySpec.stream()
            .anyMatch(e -> e != null && ObjectUtils.defaultIfNull(e.getStoreAgeControlProduct(), Boolean.FALSE));
    }

    @Override
    public PageList<StockAgeStrategyConfigInfoDTO>
        pageListStockAgeStrategyConfigInfo(StockAgeStrategyConfigQueryDTO stockAgeStrategyConfigQueryDTO) {
        return stockAgeStrategyBL.pageListStockAgeStrategyConfigInfo(stockAgeStrategyConfigQueryDTO);
    }
}
