package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StoreWareHouseBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.WareHouseCascadeQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 仓库服务
 * @date 2018/3/9 10:43
 */
@Service(timeout = 50000)
public class StoreWareHouseServiceImpl implements IStoreWareHouseService {
    @Autowired
    private StoreWareHouseBL storeWareHouseBL;

    @Override
    public WareHouseDTO getWareHouseByCityId(Integer cityId) {
        AssertUtils.notNull(cityId, "城市Id不能为空");
        return storeWareHouseBL.getWareHouseByCityId(cityId);
    }

    @Override
    public WareHouseDTO getWareHouseByShopId(Long shopId) {
        AssertUtils.notNull(shopId, "经销商Id不能为空");
        return storeWareHouseBL.selectWareHouseByShopId(shopId);
    }

    @Override
    public void listAllWarehouseForSupplychain() {
        storeWareHouseBL.listAllWarehouseForSupplychain();
    }

    @Override
    public List<Integer> getAllCityDefaultWareHouseByCityId(Integer cityId) {
        AssertUtils.notNull(cityId, "城市Id不能为空");
        return storeWareHouseBL.getAllCityDefaultWareHouseByCityId(cityId);
    }

    @Override
    /**
     * 通过仓库id获取所有默认使用这个仓库的城市
     * 
     * @param warehouseId
     * @return
     */
    public List<Integer> getAllCityByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空");
        return storeWareHouseBL.getAllCityByWarehouseId(warehouseId);
    }

    @Override
    /**
     * 通过仓库id获取仓库的主要城市
     * 
     * @param warehouseId
     * @return
     */
    public Integer getMajorCityByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空");
        return storeWareHouseBL.getMajorCityByWarehouseId(warehouseId);
    }

    @Override
    public PageList<WareHouseDTO> getListWareHouse(WareHouseDTO wareHouseDTO, PagerCondition pager) {
        return storeWareHouseBL.getListWareHouse(wareHouseDTO, pager);
    }

    @Override
    public PageList<WareHouseDTO> findWareHouseByParentOrgId(WareHouseCascadeQuery query, PagerCondition pager) {
        return storeWareHouseBL.findWareHouseByParentOrgId(query, pager);
    }

    @Override
    public WareHouseDTO findWareHouseById(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空");
        return storeWareHouseBL.findWareHouseById(warehouseId);

    }

    @Override
    public Map<Integer, String> findWareHouseNamesByIds(List<Integer> warehouseIds) {
        return storeWareHouseBL.findWareHouseNamesByIds(warehouseIds);

    }

    /**
     * 查询仓库状态
     * 
     * @param warehouseId
     * @return
     */
    @Override
    public Integer queryWarehouseStatusByWarehouseId(Integer warehouseId) {
        return storeWareHouseBL.queryWarehouseStatusByWarehouseId(warehouseId);
    }

    /**
     * 通过城市id获取仓库列表
     * 
     * @param cityId
     * @return
     */
    @Override
    public List<WareHouseDTO> findWarehouseListForOPAdmin(Integer cityId) {
        return storeWareHouseBL.findWarehouseListForOPAdmin(cityId);
    }

    /**
     * 获取代运营公司名称
     * 
     * @param wareHouseDTO
     * @return
     */
    @Override
    public String getProxyCompanyName(WareHouseDTO wareHouseDTO) {
        return storeWareHouseBL.getProxyCompanyName(wareHouseDTO);
    }
}
