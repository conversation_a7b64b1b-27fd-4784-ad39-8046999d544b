package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSpecIdAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.UnifySkuSimpleDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IUnifySkuService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UnifySkuServiceImpl implements IUnifySkuService {
    //
    // @Autowired
    // private UnifySkuCacheBL unifySkuCacheBL;

    /**
     * 根据规格货主查询中台skuId
     */
    @Override
    public Map<ProductSpecIdAndOwnerIdDTO, Long>
        findUnifySkuBySpecIdAndOwnerId(List<ProductSpecIdAndOwnerIdDTO> productSpecIdAndOwnerIdDTOS) {
        return new HashMap<>(16);
        // return unifySkuCacheBL.findUnifySkuBySpecIdAndOwnerId(productSpecIdAndOwnerIdDTOS);
    }

    /**
     * 根据skuID查询中台skuId
     */
    @Override
    public Map<Long, Long> findUnifySkuIdMapBySkuIds(List<Long> skuIds) {
        return new HashMap<>(16);
        // if (!CollectionUtils.isEmpty(skuIds)) {
        // skuIds.removeIf(p -> Objects.equals(null, p));
        // }
        // AssertUtils.notEmpty(skuIds, "SkuId参数不能为空");
        // return unifySkuCacheBL.findUnifySkuIdMapBySkuIds(skuIds);
    }

    /**
     * 根据中台skuId查询产品规格货主信息
     */
    @Override
    public Map<Long, ProductSpecIdAndOwnerIdDTO> findProductSpecIdAndOwnerIdMapByUnifySkuIds(List<Long> unifySkuIds) {
        return new HashMap<>(16);
        // return unifySkuCacheBL.findProductSpecIdAndOwnerIdMapByUnifySkuIds(unifySkuIds);
    }

    /**
     * 缓存中台sku信息
     */
    @Override
    public void setUnifySkuToRedis(List<UnifySkuSimpleDTO> unifySkuSimpleDTOS) {
        // unifySkuCacheBL.setUnifySkuToRedis(unifySkuSimpleDTOS);
    }

    /**
     * 根据城市、中台sku查询产品sku信息
     *
     * @param cityId 城市ID
     * @param unifySkuIds 中台SKU
     * @return 产品信息
     */
    @Override
    public Map<Long, List<ProductSkuDTO>> findSkuByUnifySkuAndCity(Integer cityId, List<Long> unifySkuIds) {
        return new HashMap<>(16);
        // return unifySkuCacheBL.findSkuByUnifySkuAndCity(cityId, unifySkuIds);
    }

    /**
     * 根据城市ID,中台sku查找sku基础信息[获取满足条件的第一个DTO]
     *
     * @param cityId
     * @param unifySkuId
     * @return skuDTO
     */
    @Override
    public ProductSkuDTO findSimpleSkuInfoByUnifySkuAndCity(Integer cityId, Long unifySkuId) {
        return null;
        // return unifySkuCacheBL.findSimpleSkuInfoByUnifySkuAndCity(cityId, unifySkuId);
    }
}
