package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import java.util.List;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.vessel.VesselTransferUtilBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.TransferVesselToLocationDTO;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.StoreWareHouseBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.VesselBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IVesselService;

/**
 *
 * <AUTHOR> 2022/10/20
 */
@Service(timeout = 50000)
public class VesselServiceImpl implements IVesselService {
    @Autowired
    private VesselBL vesselBL;
    @Autowired
    private StoreWareHouseBL storeWareHouseBL;
    @Autowired
    private VesselTransferUtilBL vesselTransferUtilBL;

    /**
     * 分页条件查询容器信息
     * 
     * @param vesselInfoQueryDTO
     * @return
     */
    @Override
    public PageList<VesselDTO> pageListVessel(VesselInfoQueryDTO vesselInfoQueryDTO) {
        return vesselBL.pageListVessel(vesselInfoQueryDTO);
    }

    @Override
    public void addVessel(LoactionDTO dto) {
        if (dto.getUserId() == null) {
            throw new DataValidateException("操作人信息不能为空！");
        }
        if (dto.getWarehouseId() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        if (dto.getCityId() == null) {
            throw new DataValidateException("城市信息不能为空！");
        }
        vesselBL.addVesselInfo(dto);
    }

    @Override
    public void addVesselByLocationIds(VesselDTO dto) {
        if (dto.getUserId() == null) {
            throw new DataValidateException("操作人信息不能为空！");
        }
        if (dto.getWarehouseId() == null) {
            throw new DataValidateException("仓库信息不能为空！");
        }
        if (dto.getCityId() == null) {
            throw new DataValidateException("城市信息不能为空！");
        }
        vesselBL.addVesselByLocationIds(dto);
    }

    @Override
    public void vesselRelationLocation(LoactionDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "入参不能为空！");
        AssertUtils.notNull(updateDTO.getId(), "容器信息id不能为空！");
        AssertUtils.notNull(updateDTO.getWarehouseId(), "仓库id不能为空！");
        AssertUtils.notNull(updateDTO.getArea_Id(), "对应货位id不能为空！");
        vesselBL.vesselRelationLocation(updateDTO);
    }

    @Override
    public void updateVesselInfoFreezeState(VesselInfoModifyDTO updateDTO) {
        AssertUtils.notNull(updateDTO, "入参不能为空！");
        AssertUtils.notNull(updateDTO.getIds(), "容器信息ids不能为空！");
        vesselBL.updateVesselInfoFreezeState(updateDTO);
    }

    @Override
    public void addStoreTransferOrder(List<VesselMoveDTO> dtoList) {
        AssertUtils.notEmpty(dtoList, "新增移库单入参不能为空！");
        dtoList.stream().forEach(p -> {
            AssertUtils.notNull(p.getCityId(), "城市id不能为空！");
            AssertUtils.notNull(p.getWarehouseId(), "仓库id不能为空！");
            AssertUtils.notNull(p.getVesselId(), "容器id不能为空！");
            AssertUtils.notNull(p.getVesselName(), "容器名称不能为空！");
            AssertUtils.notNull(p.getFromLocationId(), "源货位id不能为空！");
            AssertUtils.notNull(p.getToLocationId(), "目标货位id不能为空！");
            AssertUtils.notNull(p.getUserId(), "用户id不能为空");
            AssertUtils.notNull(p.getUserName(), "用户名称不能为空");
        });

        vesselBL.addStoreTransferOrder(dtoList);
    }

    @Override
    public void updateVesselInfoCurrentLocation(List<VesselInfoModifyDTO> updateDTOS) {
        AssertUtils.notEmpty(updateDTOS, "入参不能为空！");
        updateDTOS.stream().forEach(p -> {
            AssertUtils.notNull(p.getLocationId(), "容器id不能为空！");
            AssertUtils.notNull(p.getCurrentLocationId(), "当前货位id不能为空！");
            AssertUtils.notNull(p.getCurrentLocationName(), "当前货位名称不能为空！");
        });

        vesselBL.updateVesselInfoCurrentLocation(updateDTOS);
    }

    @Override
    public void vesselDeleteAndTransfer(VesselDTO dto) {
        vesselBL.vesselDeleteAndTransfer(dto);
    }

    /**
     * 批量转移容器库存到关联货位
     *
     * @param dto
     */
    @Override
    public void transferVesselToLocation(TransferVesselToLocationDTO dto) {
        vesselTransferUtilBL.transferVesselToLocation(dto);
    }
}