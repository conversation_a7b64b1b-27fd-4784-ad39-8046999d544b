package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.Warehouse2DModelQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointAndPickPointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationPointQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseRoutePointDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseStarirsDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouse2DModelQueryService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/21 18:20
 */

@Service
public class Warehouse2DModelQueryServiceImpl implements IWarehouse2DModelQueryService {

    @Resource
    private Warehouse2DModelQueryBL warehouse2DModelQueryBL;

    @Override
    public List<WarehouseRoutePointDTO> getWarehouseRoutePoints(Integer warehouesId) {
        return warehouse2DModelQueryBL.getWarehouseRoutePoints(warehouesId);
    }

    @Override
    public List<LocationPointInfoDTO> findWarehouseLocatinPoints(Integer warehouesId) {
        return warehouse2DModelQueryBL.findWarehouseLocatinPoints(warehouesId);
    }


    @Override
    public LocationPointInfoDTO findStarPoint(Integer warehouesId){
        return warehouse2DModelQueryBL.findFirstFloor(warehouesId);
    }


    @Override
    public List<LocationPointInfoDTO> findAllStarPoint(Integer warehouesId){
        return warehouse2DModelQueryBL.findAllStarPoint(warehouesId);
    }


    @Override
    public List<WarehouseStarirsDTO> findWarehouseStrairs(Integer warehouesId){
        return warehouse2DModelQueryBL.findWarehouseStrairs(warehouesId);
    }

    @Override
    public List<LocationPointAndPickPointDTO> getWarehousePointByLocations(LocationPointQueryDTO query){
        return warehouse2DModelQueryBL.getWarehousePointByLocations(query);
    }
}
