package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location.Warehouse2DModelingBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AddLocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AddLocationAreaResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.StrairsCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DModelDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DSaveAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.Warehouse2DSaveLocationGroupDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouse2DModelingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-03-21 10:08
 **/
@Service
public class Warehouse2DModelingServiceImpl implements IWarehouse2DModelingService {

    @Resource
    private Warehouse2DModelingBL warehouse2DModelingBL;

    private static final Logger logger = LoggerFactory.getLogger(Warehouse2DModelingServiceImpl.class);

    /**
     * 保存或更新 货区画布布局配置信息
     *
     * @param warehouse2DModelDTO 货区画布布局配置信息
     */
    @Override
    public void saveOrUpdateLocationConfig(Warehouse2DModelDTO warehouse2DModelDTO) {
        logger.info("保存或更新 货区画布布局配置信息: {}", JSON.toJSONString(warehouse2DModelDTO));
        warehouse2DModelingBL.saveOrUpdateLocationConfig(warehouse2DModelDTO);
    }

    /**
     * 查询 货区画布布局配置信息
     *
     * @param warehouseId 仓库 id
     * @param configType 分仓配置
     * @return 查询结果
     */
    @Override
    public Warehouse2DModelDTO queryLocationConfig(Integer warehouseId, Integer configType) {
        return warehouse2DModelingBL.queryLocationConfig(warehouseId, configType);
    }

    @Override
    public AddLocationAreaResultDTO createWarehouseLocationArea(AddLocationAreaDTO addLocationAreaDTO) {
        logger.info("新增货区: {}", JSON.toJSONString(addLocationAreaDTO));
        return warehouse2DModelingBL.createWarehouseLocationArea(addLocationAreaDTO);
    }

    @Override
    public void updateLocationArea(Warehouse2DSaveAreaDTO areaDTO) {
        logger.info("修改货区: {}", JSON.toJSONString(areaDTO));
        warehouse2DModelingBL.updateLocationArea(areaDTO);
    }

    @Override
    public void updateLocationShelve(Warehouse2DSaveLocationGroupDTO saveLocationGroupDTO) {
        logger.info("修改货架: {}", JSON.toJSONString(saveLocationGroupDTO));
        warehouse2DModelingBL.updateLocationShelve(saveLocationGroupDTO);
    }


    @Override
    public Boolean checkStairsExists(StrairsCheckDTO strairsPoint) {
        logger.info("校验楼梯是否重复: {}", JSON.toJSONString(strairsPoint));
        return warehouse2DModelingBL.checkStairsExists(strairsPoint);
    }

    @Override
    public PageList<LocationAreaReturnDTO> getUnboundLocationArea(LocationAreaDTO locationAreaDTO) {
        return warehouse2DModelingBL.getUnboundLocationArea(locationAreaDTO);
    }


}
