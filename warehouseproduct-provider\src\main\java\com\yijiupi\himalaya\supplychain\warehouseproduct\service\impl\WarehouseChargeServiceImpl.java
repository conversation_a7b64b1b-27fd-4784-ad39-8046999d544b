package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.WareHouseChargeBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseCopyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouseChargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 仓库及仓库费用增删改查dubbo接口
 *
 * <AUTHOR>
 * @since 2018/11/22 13:47
 */
@Service
public class WarehouseChargeServiceImpl implements IWarehouseChargeService {

    @Autowired
    private WareHouseChargeBL wareHouseChargeBL;

    private static final Logger logger = LoggerFactory.getLogger(WarehouseChargeServiceImpl.class);

    /**
     * 查询仓库信息
     */
    @Override
    public PageList<WareHouseChargeDTO> listWarehouseChargeConfigSelective(WareHouseChargeQueryDTO query) {
        return wareHouseChargeBL.listWarehouseChargeConfigSelective(query);
    }

    /**
     * 仓库启用停用方法
     *
     * @param status 0停用、1启用
     */
    @Override
    public void disabled(Integer warehouseId, Byte status) {
        wareHouseChargeBL.disabled(warehouseId, status);
    }

    /**
     * 添加仓库
     */
    @Override
    public void addWarehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        wareHouseChargeBL.addWarehouse(wareHouseChargeDTO);
    }

    /**
     * 修改仓库
     */
    @Override
    public void updateWarehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        wareHouseChargeBL.updateWarehouse(wareHouseChargeDTO);
    }

    /**
     * 查询仓库信息
     *
     * @param warehouseId 仓库主键
     */
    @Override
    public WareHouseChargeDTO getWarehouseChargeConfigSelective(Integer warehouseId) {
        return wareHouseChargeBL.getWarehouseChargeConfigSelective(warehouseId);
    }

    /**
     * 仅查询本级服务商仓库信息
     */
    @Override
    public List<WareHouseDTO> listWarehouseDTOByCityIdList(List<Integer> cityIdList) {
        return wareHouseChargeBL.listWarehouseDTOByCityIdList(cityIdList);
    }

    /**
     * 仅查询本级服务商仓库信息
     */
    @Override
    public List<WareHouseDTO> listWarehouseDTOByCityId(Integer cityId) {
        return wareHouseChargeBL.listWarehouseDTOByCityId(cityId);
    }

    /**
     * 复制仓库信息
     *
     * @param dto 仓库
     */
    @Override
    public void copyWarehouse(WarehouseCopyDTO dto) {
        logger.info("复制仓库: {}", JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
        wareHouseChargeBL.copyWarehouse(dto);
    }

    @Override
    public void updateWareChargehouse(WareHouseChargeDTO wareHouseChargeDTO) {
        wareHouseChargeBL.updateChargeWarehouse(wareHouseChargeDTO);
    }
}
