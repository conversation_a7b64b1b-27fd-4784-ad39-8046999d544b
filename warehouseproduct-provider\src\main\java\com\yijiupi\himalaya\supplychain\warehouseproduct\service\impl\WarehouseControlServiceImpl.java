package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseUnlockLengthenTimeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouseControlService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 仓库风控延长时间
 */
@Service
public class WarehouseControlServiceImpl implements IWarehouseControlService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private final String WAREHOUSE_CONTROL_PREFIX = "sup:warehouse:product:warehouse:control:time";

    /**
     * 根据仓库角色 仓库id 延长解锁后使用周期 延长时间为3小时
     *
     * @param warehouseUnlockLengthenTimeDTO
     */
    @Override
    public void warehouseUnlockLengthenTime(WarehouseUnlockLengthenTimeDTO warehouseUnlockLengthenTimeDTO) {
        // 保存在redis中
        redisTemplate.opsForValue().set(getKey(warehouseUnlockLengthenTimeDTO),
            warehouseUnlockLengthenTimeDTO.getRoleCode(), 12, TimeUnit.HOURS);
    }

    /**
     * 检查仓库是否已经解锁过
     *
     * @param warehouseUnlockLengthenTimeDTO
     * @return TRUE 在时效中 允许继续登录 false 不在时效中 需要授权
     */
    @Override
    public Boolean checkWarehouseIsUnlock(WarehouseUnlockLengthenTimeDTO warehouseUnlockLengthenTimeDTO) {
        return !StringUtils.isBlank(redisTemplate.opsForValue().get(getKey(warehouseUnlockLengthenTimeDTO)));
    }

    private String getKey(WarehouseUnlockLengthenTimeDTO warehouseUnlockLengthenTimeDTO) {
        return WAREHOUSE_CONTROL_PREFIX + warehouseUnlockLengthenTimeDTO.getRoleCode()
            + warehouseUnlockLengthenTimeDTO.getWarehouseId();
    }
}
