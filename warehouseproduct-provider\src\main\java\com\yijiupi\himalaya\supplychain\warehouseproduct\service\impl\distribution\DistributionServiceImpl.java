package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.distribution;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.distribution.DistributionBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.DistributionPercentRecordMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IDistributionService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 配送系数
 *
 * <AUTHOR> 2017/12/1
 */
@Service
public class DistributionServiceImpl implements IDistributionService {

    @Autowired
    private DistributionBL distributionBL;

    /**
     * 给skuId绑定配送系数
     */
    @Override
    public void addDistributionPercent(DistributionPercentSearchDTO distributionPercentSearchDTO) {
        AssertUtils.notNull(distributionPercentSearchDTO.getDistributionPercentForAmount(), "配送系数-工资不能为空");
        distributionBL.addDistributionPercent(distributionPercentSearchDTO);
    }

    /**
     * 根据skuId获取配送系数
     */
    @Override
    public DistributionPercentDTO getDistributionPercentBySku(Long productSkuId) {
        return distributionBL.getDistributionPercentBySku(productSkuId);
    }

    /**
     * 根据skuIdList获取配送系数
     */
    @Override
    public List<DistributionPercentDTO> getDistributionPercentBySkuList(List<Long> productSkuIdList) {
        return distributionBL.getDistributionPercentBySkuList(productSkuIdList);
    }

    /**
     * 查询一个月内城市配送系数修改记录
     */
    @Override
    public Integer findDistributionPercentRecord(DistributionPercentRecordDTO distributionRecordDTO) {
        AssertUtils.notNull(distributionRecordDTO, "入参对象为null");
        AssertUtils.notNull(distributionRecordDTO.getCityId(), "城市id不能为null");
        AssertUtils.notNull(distributionRecordDTO.getBeginTime(), "查询时间起始值不能为null");
        Integer count = distributionBL.findDistributionPercentRecord(distributionRecordDTO);
        return count;
    }

}
