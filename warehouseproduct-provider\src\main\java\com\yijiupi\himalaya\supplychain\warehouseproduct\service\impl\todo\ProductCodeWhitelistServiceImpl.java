package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.todo;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo.ProductCodeWhitelistBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductCodeWhitelistDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.todo.IProductCodeWhitelistService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-05 11:55
 **/
@Service
public class ProductCodeWhitelistServiceImpl implements IProductCodeWhitelistService {

    @Resource
    private ProductCodeWhitelistBL productCodeWhitelistBL;

    private static final Logger logger = LoggerFactory.getLogger(ProductCodeWhitelistServiceImpl.class);

    /**
     * 保存或更新条码白名单
     *
     * @param dto 保存参数
     */
    @Override
    public void saveOrUpdateProductCodeWhitelist(ProductCodeWhitelistDTO dto) {
        logger.info("保存或更新条码白名单: {}", JSON.toJSONString(dto));
        productCodeWhitelistBL.saveOrUpdateProductCodeWhitelist(dto);
    }

    /**
     * 通过仓库 id 和 sku id 查询有效的产品条码白名单
     *
     * @param warehouseId 仓库 id
     * @param skuId      sku id
     * @return 查询结果
     */
    @Override
    public List<ProductCodeWhitelistDTO> selectValidWhitelist(Integer warehouseId, Collection<Long> skuId) {
        return productCodeWhitelistBL.selectValidWhitelist(warehouseId, skuId);
    }
}
