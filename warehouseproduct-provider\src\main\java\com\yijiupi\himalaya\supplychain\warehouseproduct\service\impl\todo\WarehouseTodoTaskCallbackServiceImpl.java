package com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.todo;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.todo.WarehouseTodoTaskCallbackBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.todo.IWarehouseTodoTaskCallbackService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-11-19 16:26
 **/
@Service
public class WarehouseTodoTaskCallbackServiceImpl implements IWarehouseTodoTaskCallbackService {

    @Resource
    private WarehouseTodoTaskCallbackBL warehouseTodoTaskCallbackBL;

    /**
     * 和待办任务上的备注对比, 校验条码是否有被矫正过
     *
     * @param warehouseId 仓库 id
     * @param specId      规格 id
     * @param skuId skuId
     */
    @Override
    public void checkProductCode(Integer warehouseId, Long specId, Long skuId) {
        warehouseTodoTaskCallbackBL.checkProductCode(warehouseId, specId, skuId);
    }

    /**
     * 通过待办任务的 businessNo 判断这个任务能否被完成<br/>
     * 只能处理 <b>条码矫正类型的任务</b>
     *
     * @param businessNos 待办任务的 businessNo
     * @return 能完成掉的 businessNo
     */
    @Override
    public Set<String> checkCanBeCompleted(Set<String> businessNos) {
        if (CollectionUtils.isEmpty(businessNos)) {
            return Collections.emptySet();
        }
        return warehouseTodoTaskCallbackBL.checkCanBeCompleted(businessNos);
    }
}
