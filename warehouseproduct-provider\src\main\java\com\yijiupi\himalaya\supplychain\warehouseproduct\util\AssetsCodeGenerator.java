/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.NumberFormat;
import java.util.Date;

/**
 * 资产code生成器
 */
@Component
public class AssetsCodeGenerator {

    private NumberFormat nf;

    private BoundValueOperations<Object, Object> boundValueOperations;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @PostConstruct
    public void init() {
        nf = NumberFormat.getInstance();
        // 设置是否使用分组
        nf.setGroupingUsed(false);
        // 设置最大整数位数
        nf.setMaximumIntegerDigits(4);
        // 设置最小整数位数
        nf.setMinimumIntegerDigits(4);
        boundValueOperations = redisTemplate.boundValueOps("supf:assets:assetsCode:sequence");
        if (boundValueOperations.get() == null) {
            // 从0开始
            boundValueOperations.set(0);
        }
    }

    public String generator(Integer warehouseId, String assetsTypeName) {
        if (warehouseId == null) {
            throw new RuntimeException("生成资产编码时仓库ID不能为空！");
        }
        if (StringUtils.isBlank(assetsTypeName)) {
            throw new RuntimeException("生成资产编码时资产类型不能为空！");
        }
        StringBuilder builder = new StringBuilder();
        builder.append(assetsTypeName).append(warehouseId).append(DateFormatUtils.format(new Date(), "yyyyMM"))
            .append(getSequence());
        return builder.toString();
    }

    private String getSequence() {
        long sequence = boundValueOperations.increment(1);
        return nf.format(sequence);
    }
}
