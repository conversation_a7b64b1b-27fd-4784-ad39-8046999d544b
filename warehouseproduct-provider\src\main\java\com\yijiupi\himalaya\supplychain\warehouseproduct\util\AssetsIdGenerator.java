/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 资产ID生成器
 */
@Component
public class AssetsIdGenerator {

    private BoundValueOperations<Object, Object> boundValueOperations;

    private static int sequence = 1000;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @PostConstruct
    public void init() {
        boundValueOperations = redisTemplate.boundValueOps("supf:assets:assetsId:sequence");
        if (boundValueOperations.get() == null) {
            // 从 sequence 开始
            boundValueOperations.set(sequence);
        }
    }

    public synchronized long getUUidLong() {
        AssetsIdGenerator.sequence++;
        // 产生一个时间戳
        long uuid = System.currentTimeMillis() + sequence;
        if (sequence >= Integer.MAX_VALUE - 1) {
            sequence = 1000;
        }
        return uuid + getSequence();

    }

    private long getSequence() {
        return boundValueOperations.increment(1);
    }
}
