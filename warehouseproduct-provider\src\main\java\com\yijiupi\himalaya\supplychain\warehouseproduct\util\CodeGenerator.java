package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-03-24
 */
@Component
public class CodeGenerator {
    private static final Logger LOG = LoggerFactory.getLogger(CodeGenerator.class);
    private final DateFormat dateFormat = new SimpleDateFormat("yyMMdd");
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    public static final String PRODUCT_SOURCE_CODE = "productsourcecode";

    private Map<String, String> generatorNoMap = new HashMap<>(16);

    @PostConstruct
    public void init() {
        generatorNoMap.put(PRODUCT_SOURCE_CODE, "SY");
    }

    /**
     * 获取唯一Id
     *
     * @param key warehouseId
     * @param hashKey 单据类型+日期
     * @return
     * @throws BusinessException
     */
    private Long incrementHash(String key, String hashKey) throws BusinessException {
        try {
            // dela 增加量（不传采用1）
            Long increment = redisTemplate.opsForHash().increment(key, hashKey, 1);
            LOG.info(String.format("Id生成-Key:%s,HashKey:%s,value:%s", key, hashKey, increment));
            return increment;
        } catch (Exception e) {
            // redis宕机时采用uuid的方式生成唯一id
            int first = new Random(10).nextInt(8) + 1;
            int randNo = UUID.randomUUID().toString().hashCode();
            if (randNo < 0) {
                randNo = -randNo;
            }
            return Long.valueOf(first + String.format("%16d", randNo));
        }
    }
    //
    // public String generatorByType(String orderType) {
    // return generator(null, orderType);
    // }

    public String generator(Integer warehouseId, String orderType) {
        String dateStr = dateFormat.format(new Date());
        Long tmpId = incrementHash(String.format("supf:stockorder:IdGenerator:%s", warehouseId),
            String.format("%s%s", orderType, dateStr));
        return String.format("%s%s%s%05d", StringUtils.isNotEmpty(orderType) ? orderType : "", warehouseId, dateStr,
            tmpId);
    }

    public String generatorId() {
        return generator(20, null);
    }

    public Long generatorLong(Integer warehouseId, String orderTypeName) {
        String dateStr = dateFormat.format(new Date());
        return incrementHash(String.format("supf:" + orderTypeName + ":IdGenerator:%s", warehouseId),
            String.format("%s%s", null, dateStr));
    }

    public String generatorNo(Integer warehouseId, String orderTypeName) {
        String dateStr = dateFormat.format(new Date());
        Long tmpId = incrementHash(String.format("supf:" + orderTypeName + ":IdGenerator:%s", warehouseId),
            String.format("%s%s", generatorNoMap.get(orderTypeName), dateStr));
        return String.format("%s%s%s%05d",
            StringUtils.isNotEmpty(generatorNoMap.get(orderTypeName)) ? generatorNoMap.get(orderTypeName) : "",
            warehouseId, dateStr, tmpId);
    }

    /**
     * 删除货位操作时间
     */
    private String deleteLocationKey = "supp:location:delete:time";

    /**
     * 保存当前仓库最近删除货位操作时间
     * 
     * @return
     */
    public void updateLocationDeleteTime(Integer warehouseId) {
        if (warehouseId == null) {
            return;
        }
        String nowTime = DateUtil.formatDateTime(new Date());
        redisTemplate.opsForHash().put(deleteLocationKey, warehouseId, nowTime);
        LOG.info("保存删除货位时间 warehouseId:{}, deleteTime:{}", warehouseId, nowTime);
    }

    /**
     * 获取当前仓库最近删除货位操作时间
     * 
     * @return
     */
    public String getLocationDeleteTime(Integer warehouseId) {
        if (warehouseId == null) {
            return null;
        }
        return (String)redisTemplate.opsForHash().get(deleteLocationKey, warehouseId);
    }
}
