package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 * 
 * <AUTHOR>
 * @date 2018/5/17 9:41
 */
public class DateUtil {

    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);
    private static ThreadLocal<SimpleDateFormat> dateTimeFormat =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static ThreadLocal<SimpleDateFormat> dateFormat =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    private static ThreadLocal<SimpleDateFormat> timeFormat =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("HH:mm:ss"));

    /**
     * 年月日时分秒格式的日期转化为字符串
     * 
     * @param date
     * @return
     */
    public static String formatDateTime(Date date) {
        if (null == date) {
            return null;
        }
        return dateTimeFormat.get().format(date);
    }

    /**
     * 年月日时分秒格式的字符串转化为日期
     * 
     * @param str
     * @return
     */
    public static Date parseDateTime(String str) {
        if (null == str) {
            return null;
        }
        Date date = null;
        try {
            date = dateTimeFormat.get().parse(str);
        } catch (ParseException e) {
            logger.error("年月日时分秒格式的字符串转化为日期异常", e);
        }
        return date;
    }

    /**
     * 年月日格式的日期转化为字符串
     * 
     * @param date
     * @return
     */
    public static String formatDate(Date date) {
        if (null == date) {
            return null;
        }
        return dateFormat.get().format(date);
    }

    /**
     * 年月日格式的字符串转化为日期
     * 
     * @param str
     * @return
     */
    public static Date parseDate(String str) {
        if (null == str) {
            return null;
        }
        Date date = null;
        try {
            date = dateFormat.get().parse(str);
        } catch (ParseException e) {
            logger.error("年月日格式的字符串转化为日期异常", e);
        }
        return date;
    }

    /**
     * 时分秒格式的日期转化为字符串
     * 
     * @param date
     * @return
     */
    public static String formatTime(Date date) {
        if (null == date) {
            return null;
        }
        return timeFormat.get().format(date);
    }

    /**
     * 时分秒格式的字符串转化为日期
     * 
     * @param str
     * @return
     */
    public static Date parseTime(String str) {
        if (null == str) {
            return null;
        }
        Date date = null;
        try {
            date = timeFormat.get().parse(str);
        } catch (ParseException e) {
            logger.error("时分秒格式的字符串转化为日期异常", e);
        }
        return date;
    }

    /**
     * 获取当前年月日时分秒
     * 
     * @return
     */
    public static Date curDateTime() {
        Date date = new Date();
        SimpleDateFormat sdf = dateTimeFormat.get();
        try {
            return sdf.parse(sdf.format(date));
        } catch (ParseException e) {
            logger.error("获取当前年月日时分秒异常", e);
        }
        return null;
    }

    /**
     * 获取当前年月日
     * 
     * @return
     */
    public static Date curDate() {
        Date date = new Date();
        SimpleDateFormat sdf = dateFormat.get();
        try {
            return sdf.parse(sdf.format(date));
        } catch (ParseException e) {
            logger.error("获取当前年月日异常", e);
        }
        return null;
    }

    /**
     * 获取当前时分秒
     * 
     * @return
     */
    public static Date curTime() {
        Date date = new Date();
        SimpleDateFormat sdf = timeFormat.get();
        try {
            return sdf.parse(sdf.format(date));
        } catch (ParseException e) {
            logger.error("获取当前时分秒异常", e);
        }
        return null;
    }

    /**
     * 获取当前星期几（1：星期一， 2：星期二）
     * 
     * @return
     */
    public static int getDayOfWeek() {
        Calendar cal = Calendar.getInstance();
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w == 0) {
            w = 7;
        }
        return w;
    }

    /**
     * 获取今天是几号
     * 
     * @return
     */
    public static int getDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        int w = cal.get(Calendar.DAY_OF_MONTH);
        return w;
    }

    /**
     * 判断指定日期是否在日期区间内
     * 
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean betweenDate(Date date, Date startDate, Date endDate) {
        if (null != startDate && null != endDate) {
            if (date.getTime() >= startDate.getTime() && date.getTime() <= endDate.getTime()) {
                return true;
            }
        } else if (null != startDate && null == endDate) {
            if (date.getTime() >= startDate.getTime()) {
                return true;
            }
        } else if (null == startDate && null != endDate) {
            if (date.getTime() <= endDate.getTime()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将星期数转化为对应的中文格式（1 = 周一，2 = 周二 ...）
     * 
     * @return
     */
    public static String fomcatDayOfWeek(Integer weeks) {
        String value = "";
        switch (weeks) {
            case 1:
                value = "周一";
                break;
            case 2:
                value = "周二";
                break;
            case 3:
                value = "周三";
                break;
            case 4:
                value = "周四";
                break;
            case 5:
                value = "周五";
                break;
            case 6:
                value = "周六";
                break;
            case 7:
                value = "周日";
                break;
            default:
                value = "";
                break;
        }
        return value;
    }
}
