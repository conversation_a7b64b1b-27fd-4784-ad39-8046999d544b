package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.config.UserWarehouseAllocation;
import com.yijiupi.himalaya.supplychain.dto.config.WarehouseAllocationQuery;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.config.IWarehouseAllocationConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.WarehouseAllocationFilterSwitchEnum;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;
import io.getunleash.Unleash;
import io.getunleash.UnleashContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 常用缓存类，一小时更新一次
 *
 * <AUTHOR>
 */
@Component
public class GlobalCache {
    private static final Logger LOG = LoggerFactory.getLogger(GlobalCache.class);
    private static final int MIN_SIZE = 100;
    private static final int MAX_SIZE = 1000;

    @Reference
    private IVariableValueService iVariableValueService;
    @Reference
    private IWarehouseAllocationConfigService iWarehouseAllocationConfigService;
    @Autowired
    private Unleash unleash;

    private static Cache<Integer, Boolean> version4TransBatchInventoryCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();
    private static Cache<Integer, Boolean> trayPositionLocation =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();
    private static Cache<Integer, Boolean> PickByCustomerCache =
        Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(90, TimeUnit.SECONDS).build();
    private static Cache<String, Integer> WarehouseAllocationTypeCache =
            Caffeine.newBuilder().initialCapacity(100).maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES).build();

    private static final String OPEN_VERSION_4_TRANS_BATCH_INVENTORY = "Version4LocationChangedTransBatchInventory";

    /**
     * 是否开启4.0转移货位库存
     *
     * @param warehouseId
     * @return
     */
    public boolean isOpenVersion4TransBatchInventory(Integer warehouseId) {
        Boolean isOpenVersion4TransBatchInventory = version4TransBatchInventoryCache.getIfPresent(warehouseId);
        if (Objects.isNull(isOpenVersion4TransBatchInventory)) {
            isOpenVersion4TransBatchInventory = openVersion4TransBatchInventory(warehouseId);
            version4TransBatchInventoryCache.put(warehouseId, isOpenVersion4TransBatchInventory);
        }

        return isOpenVersion4TransBatchInventory;
    }

    /**
     * 查询是否开启库存转移配置
     *
     * @param warehouseId
     * @return
     */
    private boolean openVersion4TransBatchInventory(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey(OPEN_VERSION_4_TRANS_BATCH_INVENTORY);
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 检查仓库是否是否开启托盘位
     */
    private boolean getOpenTrayPositionLocationByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey("isNeedPalletForStock");
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启按客户拣货
     */
    private boolean getPickByCustomer(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        VariableValueQueryDTO varQuery = new VariableValueQueryDTO();
        varQuery.setVariableKey("PickByCustomer");
        varQuery.setWarehouseId(warehouseId);
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(varQuery.getVariableKey())
            && com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(varQuery.getVariableKeyList())) {
            throw new BusinessValidateException("参数key不能为空");
        }
        VariableDefAndValueDTO keyConfig = iVariableValueService.detailVariable(varQuery);
        if (keyConfig == null || com.alibaba.dubbo.common.utils.StringUtils.isEmpty(keyConfig.getVariableData())) {
            return false;
        }
        return "true".equals(keyConfig.getVariableData());
    }

    /**
     * 是否开启托盘位
     *
     * @param warehouseId
     * @return
     */
    public Boolean getOpenTrayPositionLocation(Integer warehouseId) {
        Boolean openRobotPicking = trayPositionLocation.getIfPresent(warehouseId);
        if (Objects.isNull(openRobotPicking)) {
            openRobotPicking = getOpenTrayPositionLocationByWarehouseId(warehouseId);
            trayPositionLocation.put(warehouseId, openRobotPicking);
        }
        return openRobotPicking;
    }

    /**
     * 是否开启按客户拣货
     *
     * @param warehouseId
     * @return
     */
    public Boolean getPickByCustomerFromCache(Integer warehouseId) {
        Boolean pickByCustomer = PickByCustomerCache.getIfPresent(warehouseId);
        if (Objects.isNull(pickByCustomer)) {
            pickByCustomer = getPickByCustomer(warehouseId);
            PickByCustomerCache.put(warehouseId, pickByCustomer);
        }
        return pickByCustomer;
    }

    // 查询人员分仓属性
    public Integer getEmployeeWarehouseAllocationType(WarehouseAllocationFilterSwitchEnum filterSwitchEnum,
                                                      Integer warehouseId, HttpServletRequest request) {
        if (warehouseId == null && request != null) {
            String warehouseIdStr = request.getHeader("Warehouse");
            if (warehouseIdStr == null) {
                throw new BusinessValidateException("请求头中仓库Id为空。URI=" + request.getRequestURI());
            }
            warehouseId = Integer.valueOf(warehouseIdStr);
        }
        AssertUtils.notNull(warehouseId, "仓库Id不能为空。");

        if (filterSwitchEnum != null) {
            UnleashContext context = UnleashContext.builder()
                    .addProperty("warehouseId", warehouseId.toString())
                    .build();
            boolean open = unleash.isEnabled(filterSwitchEnum.getCode(), context);
            if (open) {
                // 查询人员分仓属性:开启
            } else {
                return null;
            }
        }

        Integer userId = ThreadLocalUtil.getCurrentUserInfo();
        AssertUtils.notNull(userId, "用户Id不能为空。");

        String key = String.format("%s_%s", warehouseId, userId);
        Integer configType = WarehouseAllocationTypeCache.getIfPresent(key);
        if (configType == null) {
            WarehouseAllocationQuery query = new WarehouseAllocationQuery();
            query.setWarehouseIds(Collections.singletonList(warehouseId));
            query.setUserId(userId);
            List<UserWarehouseAllocation> userWarehouseAllocations = iWarehouseAllocationConfigService.queryConfigByUser(query);
            if (!CollectionUtils.isEmpty(userWarehouseAllocations)) {
                // 有多个属性时
                if (userWarehouseAllocations.size() > 1) {
                    LOG.info("查询人员分仓属性:有多个分仓属性:仓库Id={}:用户Id={}:分仓属性={}", warehouseId, userId, userWarehouseAllocations);
                } else {
                    UserWarehouseAllocation userWarehouseAllocation = userWarehouseAllocations.get(0);
                    if (userWarehouseAllocation.getEnableWarehouseSplit()) {
                        configType = userWarehouseAllocation.getConfigType();
                    }
                }
            }
            if (configType != null) {
                WarehouseAllocationTypeCache.put(key, configType);
            }
        }
        LOG.info("查询人员分仓属性:带缓存:仓库Id={}:用户Id={}:分仓属性={}", warehouseId, userId, configType);
        return configType;
    }
}
