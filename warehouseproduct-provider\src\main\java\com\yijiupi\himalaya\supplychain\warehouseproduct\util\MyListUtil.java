package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * list工具类
 * 
 * <AUTHOR>
 * @date: 2019年11月29日 下午5:38:29
 */
public class MyListUtil {

    /**
     * 获取两个列表相同和相异的部分 返回一个map key：diff 不同的 key：same 相同的
     * 
     * @param list1
     * @param list2
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Map<String, List> getInfoBetweenCollections(List<? extends Object> list1,
        List<? extends Object> list2) {
        List diff = new ArrayList();
        List same = new ArrayList();
        Map<Object, Integer> map = new HashMap<Object, Integer>(list1.size() + list2.size());
        List maxList = list1;
        List minList = list2;
        if (list2.size() > list1.size()) {
            maxList = list2;
            minList = list1;
        }
        maxList.forEach(max -> {
            map.put(max, 1);
        });
        minList.forEach(min -> {
            Integer count = map.get(min);
            if (count != null) {
                map.put(min, ++count);
                return;
            }
            map.put(min, 1);
        });
        map.entrySet().forEach(entry -> {
            if (entry.getValue() == 1) {
                diff.add(entry.getKey());
            } else {
                same.add(entry.getKey());
            }
        });
        Map<String, List> result = new HashMap<>(16);
        result.put("diff", diff);
        result.put("same", same);
        return result;
    }

    /**
     * 判断两个Long列表是否相等
     * 
     * @param list1
     * @param list2
     * @return
     */
    public static boolean isEquals(List<Long> list1, List<Long> list2) {
        if (null != list1 && null != list2) {
            if (list1.containsAll(list2) && list2.containsAll(list1)) {
                return true;
            }
            return false;
        }
        return true;
    }
}
