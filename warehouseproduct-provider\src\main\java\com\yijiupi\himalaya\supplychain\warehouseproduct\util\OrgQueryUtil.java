package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 机构查询工具类
 * 
 * <AUTHOR>
 * @date: 2020年1月4日 下午2:51:45
 */
@Component
public class OrgQueryUtil {

    @Reference
    private IOrgService iOrgService;

    /**
     * 如果是顶级组织则查本身以及下级，如果是下级组织只查自己
     * 
     * @param cityId
     * @return
     */
    public List<Integer> findSelfAndSubOrg(Integer cityId) {
        List<Integer> cityIdList = new ArrayList<>();
        PageList<OrgDTO> orgDTOPageList = iOrgService.listSelfAndSubById(cityId);
        if (orgDTOPageList != null) {
            if (!CollectionUtils.isEmpty(orgDTOPageList.getDataList())) {
                orgDTOPageList.getDataList().stream().forEach(orgDTO -> {
                    cityIdList.add(orgDTO.getId());
                });
            }
        }
        return cityIdList;
    }
}