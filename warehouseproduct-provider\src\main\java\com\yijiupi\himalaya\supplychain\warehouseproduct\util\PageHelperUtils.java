package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.search.PageCondition;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 分页查询工具类
 *
 * <AUTHOR>
 * @since 2023-07-26 11:20
 **/
@Deprecated
public class PageHelperUtils {

    private static final Logger logger = LoggerFactory.getLogger(PageHelperUtils.class);
    /**
     * 一次最多查询数量
     */
    public static final int LIMIT_COUNT = 5000;

    /**
     * 使用默认分页参数进行查询
     *
     * @param queryFunction 分页查询函数
     * @param <T>           结果数据类型
     * @return 查询结果
     * @see #LIMIT_COUNT
     */
    public static <T> List<T> pageQuery(Supplier<List<T>> queryFunction) {
        return pageQuery(queryFunction, LIMIT_COUNT);
    }

    /**
     * 分批查询
     *
     * @param list          查询参数
     * @param queryFunction 查询方法
     * @param <T>           参数类型
     * @param <R>           结果类型
     * @return 查询结果
     */
    public static <T, R> List<R> splitPageQuery(Collection<T> list, Function<List<T>, List<R>> queryFunction) {
        return splitPageQuery(list, LIMIT_COUNT, queryFunction);
    }

    /**
     * 分批、分页查询
     *
     * @param list          查询参数
     * @param queryFunction 查询方法
     * @param <T>           参数类型
     * @param <R>           结果类型
     * @return 查询结果
     */
    public static <T, R> List<R> splitWithPageQuery(Collection<T> list, Function<List<T>, List<R>> queryFunction) {
        return splitWithPageQuery(list, LIMIT_COUNT, queryFunction);
    }

//    /**
//     * 缩小参数的分页查询<br/>
//     * 本方法使用场景是某些 mapper<br/>
//     * <ol>
//     *     <li>不设置分页参数, 就相当于不分页查全表</li>
//     *     <li>设置超大分页参数, 还是相当于不分页查全表</li>
//     * </ol>
//     *
//     * @param param 分页、查询参数
//     * @param query 查询方法
//     * @param <T>   返回值类型
//     * @param <P>   入参类型
//     * @return 查询结果
//     */
//    public static <T extends Serializable, P extends PageCondition> PageResult<T> pageQuery(P param, PageQuery<T, P> query) {
//        return pageQuery(param, LIMIT_COUNT, query);
//    }

    /**
     * 分批、分页查询
     *
     * @param list          查询参数
     * @param splitSize     批次大小
     * @param queryFunction 查询方法
     * @param <T>           参数类型
     * @param <R>           结果类型
     * @return 查询结果
     */
    public static <T, R> List<R> splitWithPageQuery(Collection<T> list, int splitSize, Function<List<T>, List<R>> queryFunction) {
        return splitWithPageQuery(list, splitSize, LIMIT_COUNT, queryFunction);
    }

    /**
     * 使用默认分页参数进行查询
     *
     * @param limitCount    分页参数
     * @param queryFunction 分页查询函数
     * @param <T>           结果数据类型
     * @return 查询结果
     */
    public static <T> List<T> pageQuery(Supplier<List<T>> queryFunction, int limitCount) {
        long count = PageHelper.count(queryFunction::get);
        logger.info("pageQuery, count: {}", count);
        if (count < limitCount) {
            return queryFunction.get();
        } else {
            int loop = (int) count / limitCount;
            // 若有余数, 则增加一次查询次数
            if (count % limitCount != 0) {
                loop = loop + 1;
            }
            logger.info("pageQuery, loop: {}", loop);
            return IntStream.rangeClosed(1, loop).mapToObj(page -> {
                logger.info("pageQuery, 正在进行第 {} 次分页查询, page: {}, size: {}", page, page, limitCount);
                PageHelper.startPage(page, limitCount);
                return queryFunction.get();
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }
    }

    public static <T, R> List<R> splitPageQuery(Collection<T> list, int size, Function<List<T>, List<R>> queryFunction) {
        return Lists.partition(new ArrayList<>(list), size).stream().map(queryFunction)
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public static <T, R> List<R> splitWithPageQuery(Collection<T> list, int splitSize, int pageSize, Function<List<T>, List<R>> queryFunction) {
        return Lists.partition(new ArrayList<>(list), splitSize).stream().map(it -> pageQuery(() -> queryFunction.apply(it), pageSize))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

//    public static <T extends Serializable, P extends PageCondition> PageResult<T> pageQuery(P param, int size, PageQuery<T, P> query) {
//        Integer pageNum = param.getPageNum();
//        Integer pageSize = param.getPageSize();
//        param.setPageNum(null);
//        param.setPageSize(null);
//        // 去掉分页参数执行一次 count
//        long count = PageHelper.count(() -> query.query(param));
//        if (count < size) {
//            // 若数据量比 size 少则直接返回
//            param.setPageNum(pageNum);
//            param.setPageSize(pageSize);
//            return query.query(param);
//        }
//        int loop = (int) count / size;
//        // 若有余数, 则增加一次查询次数
//        if (count % size != 0) {
//            loop = loop + 1;
//        }
//        int pages = 0;
//        // 如果入参设置了 pageSize
//        if (pageSize != null) {
//            if (pageSize < size) {
//                // 若 pageSize 比 size 少则直接返回
//                param.setPageNum(pageNum);
//                param.setPageSize(pageSize);
//                return query.query(param);
//            }
//            int resize = pageSize / size;
//            if (pageSize % size != 0) {
//                resize++;
//            }
//            loop = Math.min(loop, resize);
//            pages = (int) (count / pageSize);
//            if (count % pageSize != 0) {
//                pages++;
//            }
//        }
//        List<T> result = IntStream.rangeClosed(1, loop).mapToObj(page -> {
//            logger.info("pageQuery2, 正在进行第 {} 次分页查询, page: {}, size: {}", page, page, size);
//            PageHelper.startPage(page, size);
//            return query.query(param);
//        }).flatMap(Collection::stream).collect(Collectors.toList());
//        // 还原入参引用
//        param.setPageNum(pageNum);
//        param.setPageSize(pageSize);
//        // 封装返回值
//        PageResult<T> pageResult = new PageResult<>();
//        pageResult.setPageNum(pageNum == null ? 0 : pageNum);
//        pageResult.setPageSize(pageSize == null ? 0 : pageSize);
//        pageResult.setPages(pages);
//        pageResult.setTotal(count);
//        pageResult.addAll(result);
//        return pageResult;
//    }

    public interface PageQuery<T extends Serializable, P extends PageCondition> {
        PageResult<T> query(P param);
    }

}
