/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Title: DriverMarkServiceImpl.java
 * @Package com.yijiupi.himalaya.supplychain.delivery.service.impl.driver
 * @Description: id生成器.
 * <AUTHOR>
 * @date 2017年9月5日 下午3:55:03
 * @version
 */
@Component
public class ProductIdGenerator {
    private NumberFormat nf;
    private BoundValueOperations<Object, Object> boundValueOperations;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @PostConstruct
    public void init() {
        nf = NumberFormat.getInstance();
        // 设置是否使用分组
        nf.setGroupingUsed(false);
        // 设置最大整数位数
        nf.setMaximumIntegerDigits(5);
        // 设置最小整数位数
        nf.setMinimumIntegerDigits(5);
        boundValueOperations =
            redisTemplate.boundValueOps(String.format("supf:stockorder:IdGenerator:%s", "delivery_id"));
    }

    /*
     * 生成打印ID
     */
    public Long generatorLocationId(int cityId) {
        return generator(cityId, 2);
    }

    public Long generator(int cityId, int type) {
        // if (cityId > 999) {
        // throw new RuntimeException("生成订单编号时传入的城市编号和订单类型不合法：cityId[" + cityId + "],type[" + type + "]");
        // }
        StringBuilder stringBuilder = new StringBuilder();
        Date date = new Date();
        if (cityId > 9999) {
            stringBuilder.append(String.valueOf(cityId).substring(0, 4));
        } else {
            stringBuilder.append(cityId);
        }
        stringBuilder.append(type).append(new SimpleDateFormat("yyMMddHH").format(date)).append(getSequence());
        return Long.valueOf(stringBuilder.toString());
    }

    private String getSequence() {
        long sequence = boundValueOperations.increment(1);
        return nf.format(sequence);
    }
}
