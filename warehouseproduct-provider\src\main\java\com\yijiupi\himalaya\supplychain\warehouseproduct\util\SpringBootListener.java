package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

@SuppressWarnings("rawtypes")
public class SpringBootListener implements ApplicationListener {
    private static final Logger LOG = LoggerFactory.getLogger(SpringBootListener.class);
    private static final String APP_NAME = "supplychain.warehouseproduct.start.";

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ApplicationStartedEvent) {
            LOG.info(APP_NAME + "启动");
        } else if (event instanceof ApplicationEnvironmentPreparedEvent) {
            LOG.info(APP_NAME + "Enviroment已经准备完毕");
        } else if (event instanceof ApplicationPreparedEvent) {
            LOG.info(APP_NAME + "context创建完成");
        } else if (event instanceof ApplicationFailedEvent) {
            LOG.warn(APP_NAME + "异常", event);
        } else if (event instanceof ContextRefreshedEvent) {
            LOG.info(APP_NAME + "ok");
        }
    }
}
