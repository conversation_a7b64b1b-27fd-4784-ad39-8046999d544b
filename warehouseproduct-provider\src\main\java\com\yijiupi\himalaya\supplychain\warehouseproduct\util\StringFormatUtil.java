package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.apache.commons.lang3.StringUtils;

/**
 * string格式化工具类
 *
 * <AUTHOR>
 * @date 2019-09-26 16:48
 */
public class StringFormatUtil {

    /*
     * 格式：%s-%s-%s
     */
    public static final String FORMAT_1 = "%s-%s-%s";

    /*
     * 分割符號：-
     */
    public static final String SPLIT_1 = "-";

    /**
     * %s-%s-%s
     *
     * @return
     */
    public static String format(Object... args) {
        return String.format(FORMAT_1, args);
    }

    public static String[] split(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        return StringUtils.split(content, SPLIT_1);
    }
}
