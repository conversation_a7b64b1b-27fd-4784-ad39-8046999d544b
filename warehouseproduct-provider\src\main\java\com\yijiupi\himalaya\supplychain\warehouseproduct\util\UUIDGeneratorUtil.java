package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * UUID生成器 （uuid始终是19位，取值范围是：4611686018427387904 - 9223372036854775807)
 * 
 * <AUTHOR>
 * @date 2019-08-29 10:24
 */
public class UUIDGeneratorUtil {

    /**
     * UUID生成器实例(一般定义为数据库表名)
     */
    public static final String PRODUCT_CONTROL_CONFIG = "productcontrolconfig";
    public static final String PRODUCT_CONTROL_CONFIG_ITEM = "productcontrolconfigitem";
    public static final String PRODUCT_SOURCE_CODE = "productsourcecode";
    public static final String PRODUCT_RELATION = "productrelationconfig";
    public static final String PRODUCT_SKU_LABEL = "productskulabel";
    public static final String PRODUCT_SOURCE_CODE_RECORD = "productsourcecoderecord";

    /**
     * 获取uuid
     * 
     * @param table
     * @return
     */
    public static long getUUID(String table) {
        return UUIDGenerator.getUUID(table);
    }
}
