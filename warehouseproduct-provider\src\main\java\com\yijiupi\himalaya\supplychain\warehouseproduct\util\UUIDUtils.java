/**
 * Copyright © 2018 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;

/**
 * 随机正long值uuid生成器
 *
 * @author: weiyangjun copy from yanpin
 * @date: 2018年4月11日 下午3:37:10
 */
@Deprecated
public class UUIDUtils {

    private static final Logger LOG = LoggerFactory.getLogger(UUIDUtils.class);

    private UUIDUtils() {
        super();
    }

    public static Long randonUUID() {
        return UUIDGenerator.getUUID(UUIDUtils.class.getName());
    }

    @Deprecated
    public static Integer randonIntegerUUID() {
        LOG.error("随机数生成器方法生成重复主键的概率较高，请调整方案：MySQL自增长主键或者Redis增长序列", new IllegalAccessException("randonIntegerUUID"));
        SecureRandom rand = new SecureRandom();
        return rand.nextInt(**********);
    }
}
