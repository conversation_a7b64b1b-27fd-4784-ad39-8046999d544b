package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
public class UserInfoContext {

    private static final ThreadLocal<Integer> THREADLOCAL = new ThreadLocal<>();
    private static final Logger LOGGER = LoggerFactory.getLogger(UserInfoContext.class);

    public static void set(Integer user) {
        THREADLOCAL.set(user);
    }

    public static void remove() {
        try {
            THREADLOCAL.remove();
        } catch (Exception e) {
            LOGGER.error("移除用户失败");
        }
    }

    public static Integer get() {
        return THREADLOCAL.get();
    }
}
