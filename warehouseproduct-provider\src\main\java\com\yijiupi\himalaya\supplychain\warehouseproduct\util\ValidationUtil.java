package com.yijiupi.himalaya.supplychain.warehouseproduct.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/9/6 17:13
 */
public class ValidationUtil {

    private ValidationUtil() {
        super();
    }

    public static boolean validationPhone(String phone) {
        String regex = "^1[3456789]\\d{9}$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(phone);
        boolean isMatch = m.matches();
        if (!isMatch) {
            return false;
        }
        return true;
    }
}
