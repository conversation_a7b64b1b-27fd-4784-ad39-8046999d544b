package com.yijiupi.himalaya.supplychain.warehouseproduct.util.constant;

public class RequestErrorTrackingConstant {
    // 订单新增
    public static final Byte REQUESTTYPE_ORDERADD = 0;
    // 退货单新增
    public static final Byte REQUESTTYPE_RETURNORDERADD = 10;
    // 导入产品SKU
    public static final Byte REQUESTTYPE_IMPORTSKU = 20;
    // 产品信息修改
    public static final Byte REQUESTTYPE_SKUUPDATE = 30;
    // 合作商订单新增
    public static final Byte REQUESTTYPE_PARTNERORDERADD = 40;
    // 不干胶码新增
    public static final Byte REQUESTTYPE_BARCODEADD = 50;
    /**
     * 
     */
    // 订单
    public static final Byte PRODUCTLINE_ORDER = 0;
}
