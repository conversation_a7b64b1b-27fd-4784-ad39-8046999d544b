/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct.util.enums;

/**
 * <AUTHOR>
 * @date 2019/3/18
 */
public enum Status {
    //状态 枚举
    启用(0), 停用(1);

    private int value;

    Status(int value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }
}
