package com.yijiupi.himalaya.supplychain.warehouseproduct.util.excel;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.enums.AccessPlatformEnum;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockApplyDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockItemApplyDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductUploadDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.SpecificationDictionaryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.constant.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.enums.OwnerTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.enums.PartnerTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.model.dto.ExcelHead;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationImportItemDTO;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: ExcelUtils
 * @Description:
 * @date Apr 21, 2019 12:03:18 PM
 */
public class ExcelUtils {

    private ExcelUtils() {}

    private static final Logger LOG = LoggerFactory.getLogger(ExcelUtils.class);

    private static final String AREA = "区域";
    private static final String DEPARTMENT = "部门";
    private static final String POST = "岗位";
    private static final String SEQUENCE = "考勤序号";
    private static final String USERNAME = "姓名";
    private static final String MOBILENO = "手机号";
    private static final String WORKINGTIME = "工作时间";

    public static final String PRIMARY_FLAG = "primary";
    public static final String ITEM_FLAG = "item";

    /**
     * Excel表头对应Entity属性 解析封装javabean
     *
     * @param classzz 类
     * @param in excel流
     * @param fileName 文件名
     * @param excelHeads excel表头与entity属性对应关系
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readExcelToEntity(Class<T> classzz, InputStream in, String fileName,
        List<ExcelHead> excelHeads) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        List<T> excelForBeans = readExcel(classzz, workbook, excelHeads); // 解析Excel
        return excelForBeans;
    }

    /**
     * 解析Excel转换为Entity
     *
     * @param classzz 类
     * @param in excel流
     * @param fileName 文件名
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readExcelToEntity(Class<T> classzz, InputStream in, String fileName) throws Exception {
        return readExcelToEntity(classzz, in, fileName, null);
    }

    /**
     * 校验是否是Excel文件
     *
     * @param fileName
     * @throws Exception
     */
    public static void checkFile(String fileName) throws Exception {
        boolean isNotExcel =
            !StringUtils.isEmpty(fileName) && !(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"));
        if (isNotExcel) {
            throw new Exception("不是Excel文件！");
        }
    }

    /**
     * 兼容新老版Excel
     *
     * @param in
     * @param fileName
     * @return
     * @throws IOException
     */
    public static Workbook getWorkBoot(InputStream in, String fileName) throws IOException {
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(in);
        } else {
            return new HSSFWorkbook(in);
        }
    }

    /**
     * 解析Excel
     *
     * @param classzz 类
     * @param workbook 工作簿对象
     * @param excelHeads excel与entity对应关系实体
     * @param <T>
     * @return
     * @throws Exception
     */
    private static <T> List<T> readExcel(Class<T> classzz, Workbook workbook, List<ExcelHead> excelHeads)
        throws Exception {
        List<T> beans = new ArrayList<T>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            String sheetName = sheet.getSheetName();
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum + 1);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            Field[] fields = classzz.getDeclaredFields();
            for (int rowIndex = firstRowNum + 3; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                T instance = classzz.newInstance();
                if (CollectionUtils.isEmpty(excelHeads)) { // 非头部映射方式，默认不校验是否为空，提高效率
                    firstCellNum = dataRow.getFirstCellNum();
                    lastCellNum = dataRow.getLastCellNum();
                }
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    // String value11 = cell.getStringCellValue();
                    headCell.setCellType(CellType.STRING);
                    String headName = headCell.getStringCellValue().trim();
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    ExcelHead eHead = null;
                    if (!CollectionUtils.isEmpty(excelHeads)) {
                        for (ExcelHead excelHead : excelHeads) {
                            if (headName.equals(excelHead.getExcelName())) {
                                eHead = excelHead;
                                headName = eHead.getEntityName();
                                break;
                            }
                        }
                    }
                    for (Field field : fields) {
                        if (headName.equalsIgnoreCase(field.getName())) {
                            String methodName = MethodUtils.setMethodName(field.getName());
                            Method method = classzz.getMethod(methodName, field.getType());
                            if (isDateFied(field)) {
                                Date date = null;
                                if (cell != null) {
                                    date = cell.getDateCellValue();
                                }
                                if (date == null) {
                                    volidateValueRequired(eHead, sheetName, rowIndex);
                                    break;
                                }
                                method.invoke(instance, cell.getDateCellValue());
                            } else {
                                String value = "";
                                if (cell != null) {
                                    cell.setCellType(CellType.STRING);
                                    value = cell.getStringCellValue();
                                }
                                if (StringUtils.isEmpty(value)) {
                                    volidateValueRequired(eHead, sheetName, rowIndex);
                                    break;
                                }
                                method.invoke(instance, convertType(field.getType(), value.trim()));
                            }
                            break;
                        }
                    }
                }
                beans.add(instance);
            }
        }
        return beans;
    }

    /**
     * 是否日期字段
     *
     * @param field
     * @return
     */
    private static boolean isDateFied(Field field) {
        return (Date.class == field.getType());
    }

    /**
     * 空值校验
     *
     * @param excelHead
     * @throws Exception
     */
    private static void volidateValueRequired(ExcelHead excelHead, String sheetName, int rowIndex) throws Exception {
        if (excelHead != null && excelHead.isRequired()) {
            throw new Exception(
                "《" + sheetName + "》第" + (rowIndex + 1) + "行:\"" + excelHead.getExcelName() + "\"不能为空！");
        }
    }

    /**
     * 类型转换
     *
     * @param classzz
     * @param value
     * @return
     */
    private static Object convertType(Class<?> classzz, String value) {
        if (Integer.class == classzz || int.class == classzz) {
            return Integer.valueOf(value);
        }
        if (Short.class == classzz || short.class == classzz) {
            return Short.valueOf(value);
        }
        if (Byte.class == classzz || byte.class == classzz) {
            return Byte.valueOf(value);
        }
        if (Character.class == classzz || char.class == classzz) {
            return value.charAt(0);
        }
        if (Long.class == classzz || long.class == classzz) {
            return Long.valueOf(value);
        }
        if (Float.class == classzz || float.class == classzz) {
            return Float.valueOf(value);
        }
        if (Double.class == classzz || double.class == classzz) {
            return Double.valueOf(value);
        }
        if (Boolean.class == classzz || boolean.class == classzz) {
            return Boolean.valueOf(value.toLowerCase());
        }
        if (BigDecimal.class == classzz) {
            return new BigDecimal(value);
        }
        /*
         * if (Date.class == classzz) { SimpleDateFormat formatter = new
         * SimpleDateFormat(FULL_DATA_FORMAT); ParsePosition pos = new ParsePosition(0);
         * Date date = formatter.parse(value, pos); return date; }
         */
        return value;
    }

    /**
     * 获取properties的set和get方法
     */
    static class MethodUtils {
        private static final String SET_PREFIX = "set";
        private static final String GET_PREFIX = "get";

        private static String capitalize(String name) {
            if (name == null || name.length() == 0) {
                return name;
            }
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }

        public static String setMethodName(String propertyName) {
            return SET_PREFIX + capitalize(propertyName);
        }

        public static String getMethodName(String propertyName) {
            return GET_PREFIX + capitalize(propertyName);
        }
    }

    /**
     * 导出Excel
     *
     * @param sheetName sheet名称
     * @param title 标题
     * @param values 内容
     * @param wb HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String[] title, String[][] values, HSSFWorkbook wb) {

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        // 声明列对象
        HSSFCell cell = null;

        // 创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }

        // 创建内容
        for (int i = 0; i < values.length; i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values[i].length; j++) {
                // 将内容按顺序赋给对应的列对象
                row.createCell(j).setCellValue(values[i][j]);
            }
        }
        return wb;
    }

    /**
     * 
     * 合并单元格导出
     * 
     * @param sheetName
     * @param title
     * @param values
     * @param wb
     * @param mergcellStartColum 合并开始列
     * @param mergcellEndColum 合并结束列
     * @return
     */
    public static synchronized HSSFWorkbook getHSSFWorkbookWithMergeCells(String sheetName, String[] title,
        List<String[]> values, HSSFWorkbook wb, List<Integer> mergecloumns) {
        try {
            // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
            if (wb == null) {
                wb = new HSSFWorkbook();
            }

            // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
            HSSFSheet sheet = wb.createSheet(sheetName);

            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
            HSSFRow row = sheet.createRow(0);

            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCellStyle style = wb.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
            style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

            // 声明列对象
            HSSFCell cell = null;

            // 创建标题
            for (int i = 0; i < title.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(title[i]);
                cell.setCellStyle(style);
            }

            /** 创建内容 */
            int mergeCellStartRow = 0; // 起点从0开始 行数
            int mergeCellEndRow = 0;
            boolean isMerge = false; // 是否合并
            for (int i = 0; i < values.size(); i++) {
                row = sheet.createRow(i + 1);
                String[] valueArray = values.get(i);
                if (PRIMARY_FLAG.equals(valueArray[0])) {
                    /** 主表信息, mergeCellStartRow 和 mergeCellEndRow 字段重新初始化 */
                    /** 合并单元格 行数 从0开始 列从0开始 */
                    // LOG.info("合并单元格数，开始：{}, 结束：{}", mergeCellStartRow, mergeCellEndRow);
                    if (isMerge && mergeCellStartRow != mergeCellEndRow) {
                        for (Integer mergeCloum : mergecloumns) {
                            CellRangeAddress region =
                                new CellRangeAddress(mergeCellStartRow, mergeCellEndRow, mergeCloum, mergeCloum);
                            sheet.addMergedRegion(region);
                        }
                    }
                    mergeCellStartRow = i + 1;
                    mergeCellEndRow = i + 1;
                } else if (ITEM_FLAG.equals(valueArray[0])) {
                    /** 子表信息 计算合并行数 */
                    mergeCellEndRow++;
                    isMerge = true;
                }
                for (int j = 1; j < valueArray.length; j++) {
                    /** 将内容按顺序赋给对应的列对象 */
                    if (StringUtils.isNotEmpty(valueArray[j])) {
                        /** 不是空，才开始写入 */
                        HSSFCell cellUnit = row.createCell(j - 1);
                        cellUnit.setCellStyle(style);
                        cellUnit.setCellValue(valueArray[j]);
                    }
                }
            }

            /** 设置自动适应宽度 */
            for (int i = 0; i < title.length; i++) {
                sheet.autoSizeColumn((short)i, true); // 自动调整每列宽度
            }
            /** 适配中文字符 */
            setSizeColumn(sheet, title.length);

            return wb;
        } finally {
            if (null != wb) {
                try {
                    wb.close();
                } catch (IOException e) {
                    LOG.error("关闭wb失败", e);
                }
            }
        }

    }

    public static void setSizeColumn(HSSFSheet sheet, int size) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
                HSSFRow currentRow;
                // 当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }

                if (currentRow.getCell(columnNum) != null) {
                    HSSFCell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }

            if (columnWidth > 255) {
                columnWidth = 255;
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }

    public static List<PartnerManagerDTO> readExcelParter(InputStream in, String fileName) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        return readPartnerManagerExcel(workbook); // 解析Excel
    }

    public static List<OwnerDTO> readExcelOnwer(InputStream in, String fileName) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        return readOwnerExcel(workbook); // 解析Excel
    }

    /**
     * 读取产品信息模版数据
     */
    public static List<ProductUploadDTO> readExcelProductInfo(InputStream in, String fileName, StringBuilder error)
        throws Exception {
        checkFile(fileName);
        Workbook workbook = getWorkBoot(in, fileName);
        List<ProductUploadDTO> productUploadDTOS = readProductInfoExcel(workbook, error);
        checkError(error);
        return productUploadDTOS;
    }

    private static void checkError(StringBuilder error) {
        if (error.length() > 0 && !"null".equals(error.toString()) && !"".equals(error.toString())) {
            throw new BusinessValidateException("部分数据存在错误：" + error.toString());
        }
    }

    /**
     * 读取包装规格模版数据
     */
    public static List<SpecificationDictionaryDTO> readExcelSpecificationDictionary(InputStream in, String fileName)
        throws Exception {
        checkFile(fileName);
        Workbook workbook = getWorkBoot(in, fileName);
        return readSpecificationDictionaryExcel(workbook);
    }

    /**
     * 解析入库单
     * 
     * @param in
     * @param fileName
     * @return
     * @throws Exception
     */
    public static List<InStockApplyDTO> readExcelInStockOrder(InputStream in, String fileName) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        return readInStockOrderExcel(workbook); // 解析Excel
    }

    /**
     * 解析知花知果采购申请单
     * 
     * @param in
     * @param fileName
     * @return
     * @throws Exception
     */
    public static List<InStockApplyDTO> readExcelZhzgInStockApply(InputStream in, String fileName) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        return readZhzgInStockApplyExcel(workbook); // 解析Excel
    }

    /**
     *
     * 校验并解析库区
     * 
     * @param in
     * @param fileName
     * @return
     * @throws Exception
     */

    public static List<LocationAreaDTO> readLocationArea(InputStream in, String fileName) throws Exception {
        checkFile(fileName); // 是否EXCEL文件
        Workbook workbook = getWorkBoot(in, fileName); // 兼容新老版本
        return readLocationAreaExcel(workbook); // 解析Excel
    }

    /**
     * 解析库区
     * 
     * @param workbook
     * @return
     * @throws Exception
     */
    private static List<LocationAreaDTO> readLocationAreaExcel(Workbook workbook) throws Exception {
        List<LocationAreaDTO> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                LocationAreaDTO entity = new LocationAreaDTO();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }

                    String cellValue = null;
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        cellValue = cell.getStringCellValue();
                    }

                    if (ExcelLocationAreaConstant.AREA.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        AssertUtils.isTrue(null != cellValue && cellValue.length() <= 3,
                            "第" + cellIndex + "行货区名称不能大于3个字符！");
                        entity.setArea(cellValue);
                    } else if (ExcelLocationAreaConstant.LOCATIONCAPACITY.equals(headName)) {
                        entity.setLocationCapacity(Integer.valueOf(cellValue));
                    } else if (ExcelLocationAreaConstant.REMO.equals(headName)) {
                        entity.setRemo(cellValue);
                    } else if (ExcelLocationAreaConstant.SUBCATEGORY.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setSubcategory(ExcelLocationAreaConstant.getSubcategoryMap().get(cellValue));
                    }
                }
                beans.add(entity);
            }
        }
        return beans;
    }

    private static List<PartnerManagerDTO> readPartnerManagerExcel(Workbook workbook) throws Exception {
        List<PartnerManagerDTO> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                PartnerManagerDTO entity = new PartnerManagerDTO();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    String cellValue = null;
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        cellValue = cell.getStringCellValue();
                    }

                    if (ExcelConstant.TRADEPARTNERNAME.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + rowIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setPartnerName(cellValue);
                    } else if (ExcelConstant.TRADEPARTNERNO.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + rowIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setPartnerNo(cellValue);
                    } else if (ExcelConstant.LINKMAN.equals(headName)) {
                        entity.setLinkMan(cellValue);
                    } else if (ExcelConstant.PHONE.equals(headName)) {
                        entity.setPhone(cellValue);
                    } else if (ExcelConstant.TYPE.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + rowIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setType(new Byte(PartnerTypeEnum.getTypeByName(cellValue)));
                    }
                }
                beans.add(entity);
            }
        }
        return beans;
    }

    /**
     * 解析入库单excel
     * 
     * @param workbook
     * @return
     * @throws Exception
     */
    private static List<InStockApplyDTO> readInStockOrderExcel(Workbook workbook) throws Exception {
        List<InStockApplyDTO> mainList = new ArrayList<>();

        // 遍历页sheet
        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            Map<String, InStockApplyDTO> mergeMap = new HashMap<>(16);
            // 遍历行
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                InStockApplyDTO inStockApplyDTO = new InStockApplyDTO();
                List<InStockItemApplyDTO> itemList = new ArrayList<InStockItemApplyDTO>();
                InStockItemApplyDTO itemDTO = new InStockItemApplyDTO();
                // 构建相同单据的key用于合并单据详情
                StringBuilder keyBuilder = new StringBuilder();
                // 遍历列
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    if (cell == null) {
                        continue;
                    }
                    cell.setCellType(CellType.STRING);
                    String cellValue =
                        StringUtils.isEmpty(cell.getStringCellValue()) ? null : cell.getStringCellValue();
                    // 货主编号设置到货主名称 后面再转换
                    if (ExcelInStockOrderConstant.OWNERNO.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        inStockApplyDTO.setOwnerName(cellValue);
                        keyBuilder.append(cellValue);
                        // 供应商设置到供应商名称 后面再转换
                    } else if (ExcelInStockOrderConstant.SUPPLIERID.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        inStockApplyDTO.setSupplierName(cellValue);
                        keyBuilder.append(cellValue);
                        // 单据类型
                    } else if (ExcelInStockOrderConstant.ORDERTYPE.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        inStockApplyDTO.setBusinessType(ExcelInStockOrderConstant.getOrderTypeMap().get(cellValue));
                        keyBuilder.append(cellValue);
                        // 产品名称
                    } else if (ExcelInStockOrderConstant.PRODUCTNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        // 把编码放在产品名称用于后面的完整数据查询
                        itemDTO.setProductName(cellValue);
                        // 大单位名称
                    } else if (ExcelInStockOrderConstant.PACKAGENAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setPackageName(cellValue);
                        // 小单位名称
                    } else if (ExcelInStockOrderConstant.UNITNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setUnitName(cellValue);
                        // 规格名称
                    } else if (ExcelInStockOrderConstant.SPECNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setSpecificationText(cellValue);
                        // 大单位数量
                    } else if (ExcelInStockOrderConstant.PACKAGECOUNT.equals(headName)) {
                        itemDTO.setApplyPackageCount(setBigDecimal(cellValue));
                        // 小单位数量
                    } else if (ExcelInStockOrderConstant.UNITTOTALCOUNT.equals(headName)) {
                        itemDTO.setApplyUnitCount(setBigDecimal(cellValue));
                        // 保质期(天)"
                    } else if (ExcelInStockOrderConstant.DAYOFSHELFLIFE.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setDayOfShelfLife(Integer.valueOf(cellValue));
                        // 备注
                    } else if (ExcelInStockOrderConstant.REMARK.equals(headName)) {
                        itemDTO.setRemark(cellValue);
                    }
                }
                if (BigDecimal.ZERO.equals(itemDTO.getApplyPackageCount())
                    && BigDecimal.ZERO.equals(itemDTO.getApplyUnitCount())) {
                    throw new BusinessValidateException("第" + rowIndex + "行入库数量不能为空或零！");
                }
                InStockApplyDTO oldDto = mergeMap.get(keyBuilder.toString());
                if (oldDto == null) {
                    itemList.add(itemDTO);
                    inStockApplyDTO.setItems(itemList);
                    mainList.add(inStockApplyDTO);
                    mergeMap.put(keyBuilder.toString(), inStockApplyDTO);
                } else {
                    oldDto.getItems().add(itemDTO);
                }
            }
        }
        return mainList;
    }

    private static BigDecimal setBigDecimal(String cellValue) {
        return StringUtils.isNotEmpty(cellValue) ? new BigDecimal(cellValue) : BigDecimal.ZERO;
    }

    /**
     * 解析知花知果采购入库单excel
     * 
     * @param workbook
     * @return
     * @throws Exception
     */
    private static List<InStockApplyDTO> readZhzgInStockApplyExcel(Workbook workbook) throws Exception {
        List<InStockApplyDTO> mainList = new ArrayList<>();

        // 遍历页sheet
        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            Map<String, InStockApplyDTO> mergeMap = new HashMap<>(16);
            // 遍历行
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                InStockApplyDTO inStockApplyDTO = new InStockApplyDTO();
                List<InStockItemApplyDTO> itemList = new ArrayList<InStockItemApplyDTO>();
                InStockItemApplyDTO itemDTO = new InStockItemApplyDTO();
                // 构建相同单据的key用于合并单据详情
                StringBuilder keyBuilder = new StringBuilder();
                // 遍历列
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    if (cell == null) {
                        continue;
                    }

                    if (!cell.getCellType().equals(CellType.STRING)) {
                        cell.setCellType(CellType.STRING);
                    }
                    String cellValue =
                        StringUtils.isEmpty(cell.getStringCellValue()) ? null : cell.getStringCellValue();
                    // 供应商 后面再转换
                    if (ExcelZhzgInStockConstant.SUPPLIERNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        inStockApplyDTO.setSupplierName(cellValue);
                        keyBuilder.append(cellValue);
                        // 采购员 后面再转换
                    } else if (ExcelZhzgInStockConstant.BUYER.equals(headName)) {
                        inStockApplyDTO.setPurchaseName(cellValue);
                        keyBuilder.append(cellValue);
                        // 大单位数量
                    } else if (ExcelZhzgInStockConstant.QUANTITY.equals(headName)) {
                        itemDTO.setApplyPackageCount(cellValue != null ? new BigDecimal(cellValue) : BigDecimal.ZERO);
                        // 产品条码
                    } else if (ExcelZhzgInStockConstant.PRODUCTBARCODE.equals(headName)) {
                        // 把编码放在产品名称用于后面的完整数据查询
                        // itemDTO.setProductName(cellValue);
                        // 产品名称
                    } else if (ExcelZhzgInStockConstant.PRODUCTNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setProductName(cellValue);
                        // 规格id
                    } else if (ExcelZhzgInStockConstant.SPECID.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setProductSpecificationId(cellValue != null ? Long.valueOf(cellValue) : null);
                        // 参考采购价
                    } else if (ExcelZhzgInStockConstant.PRICE.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setPrice(cellValue != null ? new BigDecimal(cellValue) : BigDecimal.ZERO);
                        // 规格名称
                    } else if (ExcelZhzgInStockConstant.SPECNAME.equals(headName)) {
                        AssertUtils.notNull(cellValue, "第" + rowIndex + "行【" + headName + "】不能为空！");
                        itemDTO.setSpecificationText(cellValue);
                    }
                }
                InStockApplyDTO oldDto = mergeMap.get(keyBuilder.toString());
                if (oldDto == null) {
                    itemList.add(itemDTO);
                    inStockApplyDTO.setItems(itemList);
                    mainList.add(inStockApplyDTO);
                    mergeMap.put(keyBuilder.toString(), inStockApplyDTO);
                } else {
                    oldDto.getItems().add(itemDTO);
                }
            }
        }
        return mainList;
    }

    private static List<OwnerDTO> readOwnerExcel(Workbook workbook) throws Exception {
        List<OwnerDTO> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                OwnerDTO entity = new OwnerDTO();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }

                    String cellValue = null;
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        cellValue = cell.getStringCellValue();
                    }

                    if (ExcelConstant.PRODUCTOWNERNAME.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setOwnerName(cellValue);
                    } else if (ExcelConstant.PRODUCTOWNERNO.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setOwnerNo(cellValue);
                    } else if (ExcelConstant.LINKMAN.equals(headName)) {
                        entity.setUserName(cellValue);
                    } else if (ExcelConstant.PHONE.equals(headName)) {
                        entity.setMobileNo(cellValue);
                    } else if (ExcelConstant.TYPE.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setOwnerType(OwnerTypeEnum.getTypeByName(cellValue));
                    }
                }
                beans.add(entity);
            }
        }
        return beans;
    }

    private static List<ProductUploadDTO> readProductInfoExcel(Workbook workbook, StringBuilder error)
        throws Exception {
        List<ProductUploadDTO> productInfoDTOList = new ArrayList<>();

        int sheetNum = workbook.getNumberOfSheets();
        // 遍历页
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            // 遍历行
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                ProductUploadDTO productInfoDTO = new ProductUploadDTO();
                List<ProductInfoSpecificationDTO> specificationList = new ArrayList<>();
                ProductInfoSpecificationDTO specificationDTO = new ProductInfoSpecificationDTO();
                Boolean isAllNull = true;
                try {
                    // 遍历列
                    for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                        Cell headCell = head.getCell(cellIndex);
                        if (headCell == null) {
                            continue;
                        }
                        Cell cell = dataRow.getCell(cellIndex);
                        String headName = "";
                        if (headCell.getCellType().equals(CellType.NUMERIC)) {
                            headName = String.valueOf((int)headCell.getNumericCellValue());
                        } else {
                            headName = headCell.getStringCellValue().trim();
                        }
                        if (StringUtils.isEmpty(headName)) {
                            continue;
                        }
                        String cellValue = "";
                        if (cell == null) {
                            cellValue = null;
                        } else {
                            cell.setCellType(CellType.STRING);
                            cellValue = cell.getStringCellValue().trim();
                        }

                        if (StringUtils.isNotEmpty(cellValue)) {
                            isAllNull = false;
                        }

                        // 产品名称
                        if (ExcelProductInfoConstant.PRODUC_NAME.equals(headName)) {
                            productInfoDTO.setProductName(cellValue);
                            // 产品编码
                        } else if (ExcelProductInfoConstant.PRODUCT_CODE.equals(headName)) {
                            productInfoDTO.setProductCode(cellValue);
                            // 类目
                        } else if (ExcelProductInfoConstant.CATEGORY_NAME.equals(headName)) {
                            productInfoDTO.setStatisticsCategoryName(cellValue);
                            // 品牌
                        } else if (ExcelProductInfoConstant.BRAND.equals(headName)) {
                            productInfoDTO.setBrand(cellValue);
                            // 保存条件
                        } else if (ExcelProductInfoConstant.OWNER.equals(headName)) {
                            productInfoDTO.setOwnerName(cellValue);
                            // 保存条件
                        } else if (ExcelProductInfoConstant.STORAGETYPE.equals(headName)) {
                            productInfoDTO.setStorageType(ExcelProductInfoConstant.getStorageTypeMap().get(cellValue));
                            // 是否有保质期
                        } else if (ExcelProductInfoConstant.SHELFLIFELONGTIME.equals(headName)) {
                            productInfoDTO.setShelfLifeLongTime(
                                ExcelProductInfoConstant.getShelfLifeLongTimeMap().get(cellValue));
                            // 保质期
                        } else if (ExcelProductInfoConstant.MONTHOFSHELFLIFE.equals(headName)) {
                            productInfoDTO.setMonthOfShelfLife(
                                StringUtils.isNotEmpty(cellValue) ? Integer.valueOf(cellValue) : null);
                            // 保质期单位
                        } else if (ExcelProductInfoConstant.SHELFLIFEUNIT.equals(headName)) {
                            productInfoDTO
                                .setShelfLifeUnit(ExcelProductInfoConstant.getShelfLifeUnitMap().get(cellValue));
                            // 是否加工
                        } else if (ExcelProductInfoConstant.PROCESS.equals(headName)) {
                            productInfoDTO.setProcess(ExcelProductInfoConstant.getProcessMap().get(cellValue));
                            // 最小单位条码
                        } else if (ExcelProductInfoConstant.BOTTLECODE.equals(headName)) {
                            productInfoDTO.setBottleCode(cellValue);
                            // 规格名称
                        } else if (ExcelProductInfoConstant.SPECNAME.equals(headName)) {
                            specificationDTO.setName(cellValue);
                            // 包装数量
                        } else if (ExcelProductInfoConstant.PACKAGEQUANTITY.equals(headName)) {
                            specificationDTO.setPackageQuantity(
                                StringUtils.isNotEmpty(cellValue) ? new BigDecimal(cellValue) : null);
                            // 内包装单位
                        } else if (ExcelProductInfoConstant.UNITNAME.equals(headName)) {
                            specificationDTO.setUnitName(cellValue);
                            // 外包装单位
                        } else if (ExcelProductInfoConstant.PACKAGENAME.equals(headName)) {
                            specificationDTO.setPackageName(cellValue);
                            // 条码
                        } else if (ExcelProductInfoConstant.BARCODE.equals(headName)) {
                            // todo 条码
                            // 长
                        } else if (ExcelProductInfoConstant.LENGTH.equals(headName)) {
                            specificationDTO
                                .setLength(StringUtils.isNotEmpty(cellValue) ? Double.valueOf(cellValue) : null);
                            // 宽
                        } else if (ExcelProductInfoConstant.WIDTH.equals(headName)) {
                            specificationDTO
                                .setWidth(StringUtils.isNotEmpty(cellValue) ? Double.valueOf(cellValue) : null);
                            // 高
                        } else if (ExcelProductInfoConstant.HEIGHT.equals(headName)) {
                            specificationDTO
                                .setHeight(StringUtils.isNotEmpty(cellValue) ? Double.valueOf(cellValue) : null);
                            // 体积
                        } else if (ExcelProductInfoConstant.VOLUME.equals(headName)) {
                            specificationDTO.setVolume(cellValue);
                            // 重量
                        } else if (ExcelProductInfoConstant.WEIGHT.equals(headName)) {
                            specificationDTO
                                .setWeight(StringUtils.isNotEmpty(cellValue) ? Double.valueOf(cellValue) : null);
                        } else if (ExcelProductInfoConstant.THIRDSKU.equals(headName)) {
                            productInfoDTO.setThirdSkuId(cellValue);
                        } else if (ExcelProductInfoConstant.THIRDSYSNAME.equals(headName)) {
                            productInfoDTO.setSysName(cellValue);
                        } else if (ExcelProductInfoConstant.THIRDSYSCODE.equals(headName)) {
                            productInfoDTO.setSysCode(cellValue);
                        }
                    }
                    if (isAllNull) {
                        continue;
                    }

                    if (StringUtils.isNotEmpty(productInfoDTO.getSysName())) {
                        productInfoDTO.setSysCode(String.valueOf(
                            AccessPlatformEnum.getAccessPlatformEnumByText(productInfoDTO.getSysName()).getValue()));
                    }
                    specificationDTO.setState((byte)1);
                    specificationList.add(specificationDTO);
                    productInfoDTO.setSpecificationList(specificationList);
                    StringBuilder rowError = new StringBuilder();
                    if (!checkIsNotNull(productInfoDTO, rowError)) {
                        throw new BusinessValidateException(rowError.toString());
                    }

                    productInfoDTOList.add(productInfoDTO);
                } catch (Exception e) {
                    error.append("第" + rowIndex + "条数据有问题,可能原因：");
                    if (null == e.getMessage() || "".equals(e.getMessage())) {
                        error.append("数据格式有误,字段中有空字符串；");
                    } else {
                        error.append(e.getMessage());
                    }
                }

            }
        }
        return productInfoDTOList;
    }

    private static void convertSysCode(ProductUploadDTO entity) {

        switch (entity.getSysName()) {
            case "拼多多":
                entity.setSysCode("1");
                break;
            case "淘宝":
                entity.setSysCode("2");
                break;
            case "京东":
                entity.setSysCode("3");
                break;
            case "云深优选":
                entity.setSysCode("4");
                break;
            case "旺店通":
                entity.setSysCode("5");
                break;
            default:
                break;
        }

    }

    private static Boolean checkIsNotNull(ProductUploadDTO dto, StringBuilder error) {
        Boolean flag = true;
        if (StringUtils.isEmpty(dto.getProductName())) {
            error.append("产品名称不能为空;");
            flag = false;
        }
        if (CollectionUtils.isEmpty(dto.getSpecificationList())) {
            error.append("产品规格信息不存在");
            flag = false;
        } else {
            ProductInfoSpecificationDTO specificationDTO = dto.getSpecificationList().get(0);
            if (StringUtils.isEmpty(specificationDTO.getName())) {
                flag = false;
                error.append("产品规格名称不能为空;");
            }
            if (StringUtils.isEmpty(specificationDTO.getPackageName())) {
                flag = false;
                error.append("产品大规格名称不能为空;");
            }
            if (StringUtils.isEmpty(specificationDTO.getUnitName())) {
                flag = false;
                error.append("产品小规格名称不能为空;");
            }
            if (specificationDTO.getPackageQuantity() == null) {
                flag = false;
                error.append("产品规格系数不能为空;");
            }
        }
        String sysCode = dto.getSysCode();
        String thirdSkuId = dto.getThirdSkuId();

        if (StringUtils.isNotEmpty(sysCode) && StringUtils.isEmpty(thirdSkuId)) {
            flag = false;
            error.append("存在外部编码时,外部SKUID不能为空;");
        }

        if (StringUtils.isEmpty(sysCode) && StringUtils.isNotEmpty(thirdSkuId)) {
            flag = false;
            error.append("存在外部SKUID时,外部系统编码不能为空;");
        }
        return flag;
    }

    private static List<SpecificationDictionaryDTO> readSpecificationDictionaryExcel(Workbook workbook)
        throws Exception {
        List<SpecificationDictionaryDTO> specificationDictionaryDTOS = new ArrayList<>();

        int sheetNum = workbook.getNumberOfSheets();
        // 遍历页
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            // 遍历行
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }

                SpecificationDictionaryDTO specificationDictionaryDTO = new SpecificationDictionaryDTO();

                // 遍历列
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    if (cell == null) {
                        continue;
                    }
                    cell.setCellType(CellType.STRING);
                    String cellValue = cell.getStringCellValue();
                    // 内包装单位
                    if (ExcelSpecDictionaryConstant.UNITNAME.equals(headName)) {
                        specificationDictionaryDTO.setUnitName(cellValue);
                        // 包装数量
                    } else if (ExcelSpecDictionaryConstant.PACKAGEQUANTITY.equals(headName)) {
                        specificationDictionaryDTO
                            .setPackageQuantity(cellValue != null ? new BigDecimal(cellValue) : null);
                        // 外包装单位
                    } else if (ExcelSpecDictionaryConstant.PACKAGENAME.equals(headName)) {
                        specificationDictionaryDTO.setPackageName(cellValue);
                        // 状态
                    } else if (ExcelSpecDictionaryConstant.STATE.equals(headName)) {
                        specificationDictionaryDTO.setState(ExcelSpecDictionaryConstant.getStateMap().get(cellValue));
                    }
                }
                specificationDictionaryDTOS.add(specificationDictionaryDTO);
            }
        }
        return specificationDictionaryDTOS;
    }

    public static List<ProductCategoryGroupDTO> readExcelCategory(InputStream inputStream, String originalFilename)
        throws Exception {
        checkFile(originalFilename);
        Workbook workbook = getWorkBoot(inputStream, originalFilename);
        return readCategoryManagerExcel(workbook);
    }

    private static List<ProductCategoryGroupDTO> readCategoryManagerExcel(Workbook workbook) {
        List<ProductCategoryGroupDTO> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                ProductCategoryGroupDTO entity = new ProductCategoryGroupDTO();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    if (cell == null) {
                        continue;
                    }
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }
                    String cellValue = null;
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        cellValue = cell.getStringCellValue();
                    }

                    if (ExcelConstant.CATEGORYNAME.equals(headName)) {
                        entity.setName(cellValue);
                    } else if (ExcelConstant.CATEGORYSEQUENCE.equals(headName)) {
                        entity.setSequence(Integer.valueOf(cellValue));
                    } else if (ExcelConstant.REMARK.equals(headName)) {
                        entity.setRemark(cellValue);
                    }
                }
                beans.add(entity);
            }
        }
        return beans;
    }

    /**
     *
     * 校验并解析产品库位
     * 
     * @param in
     * @param fileName
     * @return
     * @throws Exception
     */

    public static List<ProductLocationImportItemDTO> readProductlocation(InputStream in, String fileName)
        throws Exception {
        /** 是否EXCEL文件 */
        checkFile(fileName);
        /** 兼容新老版本 */
        Workbook workbook = getWorkBoot(in, fileName);
        /** 解析Excel */
        return readProductlocationExcel(workbook);
    }

    /**
     * 解析产品库位
     * 
     * @param workbook
     * @return
     * @throws Exception
     */
    private static List<ProductLocationImportItemDTO> readProductlocationExcel(Workbook workbook) throws Exception {
        List<ProductLocationImportItemDTO> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null) {
                continue;
            }
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    continue;
                }
                ProductLocationImportItemDTO entity = new ProductLocationImportItemDTO();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null) {
                        continue;
                    }
                    Cell cell = dataRow.getCell(cellIndex);
                    String headName = "";
                    if (headCell.getCellType().equals(CellType.NUMERIC)) {
                        headName = String.valueOf((int)headCell.getNumericCellValue());
                    } else {
                        headName = headCell.getStringCellValue().trim();
                    }
                    if (StringUtils.isEmpty(headName)) {
                        continue;
                    }

                    String cellValue = null;
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        cellValue = cell.getStringCellValue();
                    }
                    if (ExcelProductlocationConstant.SKU.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setProductSkuId(Long.valueOf(cellValue));
                    } else if (ExcelProductlocationConstant.LOCATION.equals(headName)) {
                        AssertUtils.isTrue(cellValue != null && !"".equals(cellValue),
                            "第" + cellIndex + "行【" + headName + "】字段内容不能为空");
                        entity.setLocationName(cellValue);
                    }
                }
                beans.add(entity);
            }
        }
        return beans;
    }
}
