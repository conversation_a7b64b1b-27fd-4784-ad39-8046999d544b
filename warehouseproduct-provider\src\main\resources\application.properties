#dubbo
dubbo.scanpackage=com.yijiupi.himalaya
dubbo.zookeeper.address=Zookeeper01.yjp.com:2181,Zookeeper02.yjp.com:2181,Zookeeper03.yjp.com:2181
dubbo.provider=supplychain-warehouseproduct
dubbo.port=${random.int[3000,4000]}
#db
#spring.datasource.url=*************************************************************************************
#spring.datasource.url=*************************************************************************************
spring.datasource.url=*************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=mycat
config.sources=sc_ProductDB
#MQ
spring.rabbitmq.addresses=RabbitMQ01.yjp.com:6000,RabbitMQ02.yjp.com:6000,RabbitMQ03.yjp.com:6000
spring.rabbitmq.username=yjp
spring.rabbitmq.password=yjp
spring.rabbitmq.listener.retry.enabled=false
spring.rabbitmq.listener.retry.max-attempts=0
spring.rabbitmq.listener.acknowledge-mode=auto
spring.rabbitmq.recover.enable=true
spring.rabbitmq.virtual-host=/
# MyBatis
mybatis.typealiasespackage=com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.po,com.yijiupi.himalaya.productsync.po
mybatis.mapper-locations=classpath:/mappings/warehouseproduct/*.xml,com/yijiupi/himalaya/supplychain/productsync/domain/dao/*.xml
mybatis.configLocation=classpath:/mybatis-config.xml
#Redis cluster
# production
spring.redis.cluster.nodes=Redis01.yjp.com:6301,Redis01.yjp.com:6302,Redis01.yjp.com:6303,Redis02.yjp.com:6301,Redis02.yjp.com:6302,Redis02.yjp.com:6303
#Redis cluster cache
spring.cache.type=redis
# \u5F02\u5E38\u8BA2\u5355\u5904\u7406\u6210\u529F\u540E\u540C\u6B65\u7ED9OMS
ex.supplychain.exceptionOrder.orderSync=ex.supplychain.exceptionOrder.orderSync
mq.supplychain.productSku.relateGroupAdd=mq.supplychain.productSku.relateGroupAdd
mq.supplychain.productSku.warehouseRelateGroupInit=mq.supplychain.productSku.warehouseRelateGroupInit
#\u56FA\u5B9A\u8D44\u4EA7\u7EDF\u8BA1
mq.supplychain.warehouseproduct.warehousePropertySync=mq.supplychain.warehouseproduct.warehousePropertySync
inventory.erpAPIUrl=http://in-erp5-innerapi.yjp.com/inventory/
# \u540C\u6B65TMS\u7684\u6EAF\u6E90\u7801\u53D8\u66F4\u8BB0\u5F55
mq.supplychain.productSourceCode.codeRecordSync=mq.supplychain.productSourceCode.codeRecordSync
#mq
mq.supplychain.partner.AddPartner=mq.supplychain.partner.AddPartner
mq.supplychain.partner.UpdatePartner=mq.supplychain.partner.UpdatePartner
mq.supplychain.partner.EnablePartner=mq.supplychain.partner.EnablePartner
mq.supplychain.partner.DisablePartner=mq.supplychain.partner.DisablePartner
mq.supplychain.owner.agencySync=mq.supplychain.owner.agencySync
mq.supplychain.owner.zhzgSync=mq.supplychain.owner.zhzgSync
mq.supplychain.region.easychainSync=mq.supplychain.region.easychainSync
mq.supplychain.owner.easysaleSync=mq.supplychain.owner.easysaleSync
# \u540C\u6B65\u6613\u7ECF\u9500\u7684\u4F9B\u5E94\u5546
mq.supplychain.provider.easysaleSync=mq.supplychain.provider.easysaleSync
# \u62FC\u97F3\u641C\u7D22
ai.search.url=http://in-ai-search-service.yjp.com
#tms\u7F51\u5173
tms.gateway.url=http://in-scapi.yjp.com/
#sqlreport id
sqlreport.productstorebatch.id=5191044708419177709
sqlreport.productlocationstore.id=5191045060166094061
sqlreport.productstorebatchsku.id=5191045274993874946
sqlreport.productlocationsku.id=5191045379549485058
sqlreport.productdate.id=5191045485413718022
sqlreport.productlocationdate.id=5192736742741839078
xxl.job.executor.appname=scm-app-warehouseproduct
xxl.job.executor.name=scm-app-warehouseproduct

#product???????
mq.supplychain.productSku.add.name=mq.supplychain.productSku.add
mq.supplychain.productSku.modify.name=mq.supplychain.productSku.modify
mq.supplychain.productInfo.add.name=mq.supplychain.productInfo.add
mq.supplychain.productInfo.modify.name=mq.supplychain.productInfo.modify
# \u4EA4\u6613\u4EA7\u54C1sku\u540C\u6B65\uFF08\u4EA4\u6613\u5E73\u53F0\u5316\u53BBSKU\uFF09
mq.supplychain.productSku.allInfosync=mq.supplychain.productSku.allInfosync
mq.supplychain.trdproductSku.sync=mq.supplychain.trdproductSku.sync
mq.supplychain.productSku.syncShop=mq.supplychain.productSku.syncShop
# \u4EA7\u54C1sku\u540C\u6B65\u7ED9\u5916\u90E8\u7CFB\u7EDF\uFF08ERP\uFF09
ex.supplychain.productSku.syncProductSku=ex.supplychain.productSku.syncProductSku
# \u4EA7\u54C1\u5173\u8054
ex.supplychain.productSku.relateGroupAdd=ex.supplychain.productSku.relateGroupAdd
#\uFFFD\u05FF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u01B7\u036C\uFFFD\uFFFD
mq.supplychain.easychainproduct.addProductSku=mq.supplychain.easychainproduct.addProductSku
# \u6613\u6B3E\u65B0\u589E\u4ED3\u5E93\u540C\u6B65SKU
mq.supplychain.easychainproduct.addWarehouseProductSku=mq.supplychain.easychainproduct.addWarehouseProductSku
# \u4EA7\u54C1[\u6761\u7801/\u7BB1\u7801]\u4FE1\u606F\u540C\u6B65
mq.supplychain.productCodeInfo.change=mq.supplychain.productCodeInfo.change
# \u4EA4\u6613\u7C7B\u76EE\u4FE1\u606F\u540C\u6B65
mq.supplychain.productCategory.change=mq.supplychain.productCategory.change
# ERP\u6210\u672C\u4EF7\u540C\u6B65
mq.supplychain.costprice.yjpsync=mq.supplychain.costprice.yjpsync
mq.supplychain.costprice.yksync=mq.supplychain.costprice.yksync
# \u6839\u636E\u4EA4\u6613\u62C6\u5305\u6D88\u606F\uFF0C\u540C\u6B65sku
mq.supplychain.productSku.splitPackageSync=mq.supplychain.productSku.splitPackageSync
#\u4F9B\u5E94\u94FE\u5931\u8D25\u6D88\u606F
ex.supplychain.mq.sendFaild=ex.supplychain.mq.sendFaild
# \u6613\u7ECF\u9500\u4EA7\u54C1\u540C\u6B65\uFF08Informa\uFF09
mq.supplychain.productSku.easysalesync=mq.supplychain.productSku.easysalesync
#\u5BF9\u63A5bi\u83B7\u53D6url
bi.token.url=http://in-bi-service-web.yjp.com/one-vue/prod-api/system/login
bi.base.url=http://in-bi-service-web.yjp.com
#\u83B7\u53D6token\u8BA4\u8BC1\u7684\u7528\u6237
authuser.username=SCM-Admin
authuser.password=Scm@2020
# \u4EA4\u6613\u5151\u5956\u4EA7\u54C1sku\u540C\u6B65
mq.supplychain.productSku.syncawardproduct=mq.supplychain.productSku.syncawardproduct

# Unleash Feature Toggles
unleash.api-key=wms:production.23d36a798e7903c833e2ed964415804a5f8ecc5b05456bb6c1d9677b

