<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AgencyConnectLocationMapper">

    <resultMap id="locationNameMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO">
        <result column="location_id" property="locationId" jdbcType="INTEGER"/>
        <result column="locationname" property="locationName" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="locationMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO">
        <result column="id" property="locationId" jdbcType="BIGINT"/>
        <result column="Name" property="locationName" jdbcType="VARCHAR"/>
        <result column="area_Id" property="areaId" jdbcType="BIGINT"/>
        <result column="Area" property="area" jdbcType="VARCHAR"/>
        <result column="Pallets" property="pallets" jdbcType="VARCHAR"/>
        <result column="RoadWay" property="roadWay" jdbcType="VARCHAR"/>
        <result column="ProductLocation" property="productLocation" jdbcType="VARCHAR"/>
        <result column="sequence" property="sequence" jdbcType="INTEGER"/>
        <result column="category" property="category" jdbcType="TINYINT"/>
        <result column="subcategory" property="subcategory" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="locationInfoMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO">
        <result column="id" property="locationId" jdbcType="BIGINT"/>
        <result column="Name" property="locationName" jdbcType="VARCHAR"/>
        <result column="area_Id" property="areaId" jdbcType="BIGINT"/>
        <result column="Area" property="area" jdbcType="VARCHAR"/>
        <result column="Pallets" property="pallets" jdbcType="VARCHAR"/>
        <result column="RoadWay" property="roadWay" jdbcType="VARCHAR"/>
        <result column="ProductLocation" property="productLocation" jdbcType="VARCHAR"/>
        <result column="owner_id" property="agencyId" jdbcType="BIGINT"/>
        <result column="agencyname" property="agencyName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="sequence" property="sequence" jdbcType="INTEGER"/>
        <result column="category" property="category" jdbcType="TINYINT"/>
        <result column="subcategory" property="subcategory" jdbcType="TINYINT"/>
    </resultMap>

    <!--关联经销商和货位-->
    <insert id="insertAgencyLocation">
        INSERT into ownerlocation
        (id,owner_id,agencyname,city_id,warehouse_id,location_id,locationname,createtime,userid,remark)
        VALUES
        <foreach collection="locationInfoDTOList" index="index" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},#{agencyId,jdbcType=BIGINT},#{agencyName,jdbcType=VARCHAR},#{cityId,jdbcType=INTEGER}
            ,#{warehouseId,jdbcType=INTEGER},#{item.locationId,jdbcType=BIGINT},#{item.locationName,jdbcType=VARCHAR}
            ,NOW(),#{userId,jdbcType=INTEGER},#{remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!--根据经销商id和仓库id查询货位信息-->
    <select id="getLocation" resultMap="locationNameMap">
        SELECT location_id,locationname
        from ownerlocation
        <where>
            1=1
            <if test="agencyId != null and agencyId != ''">
                AND owner_id =#{agencyId,jdbcType=BIGINT}
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                AND warehouse_id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
    <!--根据经销商Id，仓库Id和SKUID获取对应的货位信息-->
    <select id="getLocationBySku" resultMap="locationNameMap">
        SELECT a.location_id, a.locationname FROM ownerlocation a
        INNER JOIN productlocation p ON a.location_id = p.Location_Id
        WHERE
        p.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
        <if test="agencyId != null and agencyId != ''">
            AND a.owner_id =#{agencyId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null and warehouseId != ''">
            AND a.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>
    <!--查询未被关联的货位,供经销商选择.-->
    <select id="getUnusedLocation" resultMap="locationMap">
        SELECT loct.id,loct.Name,locta.name AS Area,loct.Pallets,loct.RoadWay,loct.ProductLocation,loct.sequence
        FROM location loct
        LEFT JOIN location locta ON loct.Warehouse_Id=locta.Warehouse_Id AND loct.area_Id=locta.Id
        WHERE loct.category = 0
        and loct.City_Id =#{getUnusedLocationDTO.cityId,jdbcType=INTEGER}
        and loct.warehouse_id =#{getUnusedLocationDTO.warehouseId,jdbcType=INTEGER}
        and loct.id NOT IN (SELECT location_id FROM ownerlocation WHERE city_id =
        #{getUnusedLocationDTO.cityId,jdbcType=INTEGER})
        <if test="getUnusedLocationDTO.areaCode != null and getUnusedLocationDTO.areaCode != ''">-- 经销商名称
            AND locta.name like concat('%',concat(#{getUnusedLocationDTO.areaCode,jdbcType=VARCHAR}),'%')
        </if>
        <if test="getUnusedLocationDTO.locationName != null and getUnusedLocationDTO.locationName != ''">-- 经销商名称
            AND loct.Name like concat('%',concat(#{getUnusedLocationDTO.locationName,jdbcType=VARCHAR}),'%')
        </if>
    </select>
    <!--删除经销商与货位关系-->
    <delete id="removeAgencyLocation">
        DELETE FROM ownerlocation where owner_id = #{agencyId,jdbcType=INTEGER} AND location_id in
        <foreach collection="locationInfoDTOList" index="index" item="item" separator="," open="(" close=")">
            #{item.locationId,jdbcType=BIGINT}
        </foreach>
    </delete>
    <!--删除-->
    <delete id="removeAgencyLocationByAgencyId">
        DELETE from ownerlocation where owner_id = #{agencyId,jdbcType=INTEGER}
    </delete>
    <!--查询locationId-->
    <select id="getAgencyLocationId" resultType="java.lang.Long">
        SELECT location_id FROM ownerlocation WHERE owner_id = #{agencyId,jdbcType=INTEGER}
        AND warehouse_id=#{warehouseId,jdbcType=INTEGER} AND location_id in
        <foreach collection="locationInfoDTOList" index="index" item="item" separator="," open="(" close=")">
            #{item.locationId,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="getLocationInfoByAgencyId" resultMap="locationInfoMap">
        SELECT
        l.id,
        l.`Name`,
        locta.name as Area,
        l.Pallets,
        l.RoadWay,
        l.sequence,
        l.ProductLocation,
        a.owner_id,
        a.agencyname,
        a.remark,
        l.subcategory
        FROM
        ownerlocation a
        INNER JOIN location l ON a.location_id = l.id
        LEFT JOIN location locta ON l .Warehouse_Id=locta.Warehouse_Id AND l .area_Id=locta.Id
        WHERE
        a.owner_id = #{agencyId,jdbcType=INTEGER}
    </select>
    <!--查询出已经有货位关联的经销商的id-->
    <select id="getAgencyIdByLocation" resultType="java.lang.Long">
        select DISTINCT owner_id from ownerlocation
    </select>
</mapper>
