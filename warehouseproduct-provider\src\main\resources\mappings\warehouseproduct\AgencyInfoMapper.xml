<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AgencyInfoMapper">


    <resultMap id="agencyInfoMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AgencyInfoPO">
        <result column="owner_id" property="agencyId" jdbcType="INTEGER"/>
        <result column="agencyname" property="agencyName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <!--根据经销商名称或者货位信息,获取经销商信息.-->
    <select id="getAgencyInfoPO" resultMap="agencyInfoMap">
        SELECT DISTINCT owner_id,agencyname, remark
        from ownerlocation
        where
        city_id = #{agencyInfoQueryDTO.cityId,jdbcType=INTEGER}
        <if test="agencyInfoQueryDTO.agencyName != null and agencyInfoQueryDTO.agencyName != ''">-- 经销商名称
            and agencyname like concat('%',concat(#{agencyInfoQueryDTO.agencyName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="agencyInfoQueryDTO.locationName != null and agencyInfoQueryDTO.locationName != ''">-- 货位名称
            and locationname like concat('%',concat(#{agencyInfoQueryDTO.locationName,jdbcType=VARCHAR}),'%')
        </if>
    </select>

</mapper>
