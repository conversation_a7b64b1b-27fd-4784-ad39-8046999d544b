<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AislePOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="aisleNo" jdbcType="VARCHAR" property="aisleNo"/>
        <result column="areaId" jdbcType="BIGINT" property="areaId"/>
        <result column="areaName" jdbcType="VARCHAR" property="areaName"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
                , cityId, warehouseId, aisleNo, areaId, areaName, state,
        remark, createUser, createTime, lastUpdateUser,lastUpdateTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from aisle
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="pageListAisle" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from aisle
        where 1=1
        <if test="cityId != null">
            and cityId = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and warehouseId = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="aisleNo != null and aisleNo != ''">
            and aisleNo = #{aisleNo,jdbcType=VARCHAR}
        </if>
        <if test="areaId != null">
            and areaId = #{areaId,jdbcType=BIGINT}
        </if>
        <if test="areaName != null and areaName != ''">
            and areaName = #{areaName,jdbcType=VARCHAR}
        </if>
        <if test="state != null">
            and state= #{state,jdbcType=TINYINT}
        </if>
    </select>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO">
        insert into aisle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cityId != null">
                cityId,
            </if>
            <if test="warehouseId != null">
                warehouseId,
            </if>
            <if test="aisleNo != null">
                aisleNo,
            </if>
            <if test="areaId != null">
                areaId,
            </if>
            <if test="areaName != null">
                areaName,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createUser != null">
                createUser,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="aisleNo != null">
                #{aisleNo,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=BIGINT},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="listAisleNoPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM aisle
        where 1=1
        <if test="cityId != null">
            and cityId = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and warehouseId = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="aisleNo != null and aisleNo != ''">
            and aisleNo = #{aisleNo,jdbcType=VARCHAR}
        </if>
        <if test="areaId != null">
            and areaId = #{areaId,jdbcType=BIGINT}
        </if>
        <if test="areaName != null and areaName != ''">
            and areaName = #{areaName,jdbcType=VARCHAR}
        </if>
        <if test="state != null">
            and state= #{state,jdbcType=TINYINT}
        </if>
        <if test="aisleNoList != null and aisleNoList.size() > 0">
            and aisleNo in
            <foreach collection="aisleNoList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="areaAisleNoList != null and areaAisleNoList.size() > 0">
            and
            <foreach collection="areaAisleNoList" item="item" open="(" close=")" separator="or">
                (
                areaId = #{item.areaId,jdbcType=BIGINT}
                and aisleNo = #{item.aisleNo,jdbcType=VARCHAR}
                )
            </foreach>
        </if>
    </select>

    <update id="updateAisle"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO">
        UPDATE aisle
        <set>
            lastUpdateTime = now(),
            <if test="aisleNo != null and aisleNo != ''">
                aisleNo = #{aisleNo,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                areaId = #{areaId,jdbcType=BIGINT},
            </if>
            <if test="areaName != null and areaName != ''">
                areaName = #{areaName,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser != null">
                lastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM aisle
        WHERE id = #{id}
    </delete>

    <select id="getAisleByAisleNoAndAreaId"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AislePO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM aisle
        WHERE warehouseId = #{warehouseId}
        and aisleNo = #{aisleNo}
        and areaId = #{areaId}
        LIMIT 1
    </select>
</mapper>