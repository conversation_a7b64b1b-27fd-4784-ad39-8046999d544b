<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AssetsChangeRecordPOMapper">

    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Assetsinfo_Id" property="assetsinfoId" jdbcType="BIGINT"/>
        <result column="AssetsinfoCode" property="assetsinfoCode" jdbcType="VARCHAR"/>
        <result column="ChangePerson" property="changePerson" jdbcType="VARCHAR"/>
        <result column="ChangeTime" property="changeTime" jdbcType="TIMESTAMP"/>
        <result column="ChangeEvent" property="changeEvent" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Assetsinfo_Id, AssetsinfoCode, ChangePerson, ChangeTime, ChangeEvent, Remark
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from assetschangerecord
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from assetschangerecord
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into assetschangerecord
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetsinfoId != null">
                Assetsinfo_Id,
            </if>
            <if test="assetsinfoCode != null">
                AssetsinfoCode,
            </if>
            <if test="changePerson != null">
                ChangePerson,
            </if>
            <if test="changeTime != null">
                ChangeTime,
            </if>
            <if test="changeEvent != null">
                ChangeEvent,
            </if>
            <if test="remark != null">
                Remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetsinfoId != null">
                #{assetsinfoId,jdbcType=BIGINT},
            </if>
            <if test="assetsinfoCode != null">
                #{assetsinfoCode,jdbcType=VARCHAR},
            </if>
            <if test="changePerson != null">
                #{changePerson,jdbcType=VARCHAR},
            </if>
            <if test="changeTime != null">
                #{changeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="changeEvent != null">
                #{changeEvent,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsChangeRecordPO">
        update assetschangerecord
        <set>
            <if test="assetsinfoId != null">
                Assetsinfo_Id = #{assetsinfoId,jdbcType=BIGINT},
            </if>
            <if test="assetsinfoCode != null">
                AssetsinfoCode = #{assetsinfoCode,jdbcType=VARCHAR},
            </if>
            <if test="changePerson != null">
                ChangePerson = #{changePerson,jdbcType=VARCHAR},
            </if>
            <if test="changeTime != null">
                ChangeTime = #{changeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="changeEvent != null">
                ChangeEvent = #{changeEvent,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertList" parameterType="list">
        INSERT INTO assetschangerecord
        (
        Id, Assetsinfo_Id, AssetsinfoCode, ChangePerson, ChangeTime, ChangeEvent, Remark
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id, jdbcType=BIGINT}, #{item.assetsinfoId,jdbcType=BIGINT}, #{item.assetsinfoCode,jdbcType=VARCHAR},
            #{item.changePerson,jdbcType=VARCHAR}, #{item.changeTime,jdbcType=TIMESTAMP},
            #{item.changeEvent,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="findChangeRecordByParentIdAndCondition" resultMap="BaseResultMap"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.query.AssetsChangeRecordListQueryDTO">
        select
        <include refid="Base_Column_List"/>
        from assetschangerecord
        where Assetsinfo_Id = #{assetsInfoId,jdbcType=BIGINT}
        <if test="changeTimeStart != null">
            and TO_DAYS(ChangeTime) <![CDATA[ >= ]]> TO_DAYS(#{changeTimeStart})
        </if>
        <if test="changeTimeEnd != null">
            and TO_DAYS(ChangeTime) <![CDATA[ <= ]]> TO_DAYS(#{changeTimeEnd})
        </if>
        <if test="changeEvent != null">
            and ChangeEvent = #{changeEvent,jdbcType=TINYINT}
        </if>
    </select>
</mapper>