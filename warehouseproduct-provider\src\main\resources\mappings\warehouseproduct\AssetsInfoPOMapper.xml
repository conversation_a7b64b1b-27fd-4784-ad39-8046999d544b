<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.AssetsInfoPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="Code" property="code" jdbcType="VARCHAR"/>
        <result column="Name" property="name" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="Retrieve" property="retrieve" jdbcType="TINYINT"/>
        <result column="Specifications" property="specifications" jdbcType="VARCHAR"/>
        <result column="AssetsType" property="assetsType" jdbcType="VARCHAR"/>
        <result column="Description" property="description" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="Length" property="length" jdbcType="DECIMAL"/>
        <result column="Width" property="width" jdbcType="DECIMAL"/>
        <result column="Height" property="height" jdbcType="DECIMAL"/>
        <result column="Volume" property="volume" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Warehouse_Id, Code, Name, State, Retrieve, Specifications, AssetsType, Description,
        Remark, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, Length, Width, Height, Volume
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from assetsinfo
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from assetsinfo
        where Code = #{code,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from assetsinfo
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO">
        insert into assetsinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="code != null">
                Code,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="retrieve != null">
                Retrieve,
            </if>
            <if test="specifications != null">
                Specifications,
            </if>
            <if test="assetsType != null">
                AssetsType,
            </if>
            <if test="description != null">
                Description,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="length != null">
                Length,
            </if>
            <if test="width != null">
                Width,
            </if>
            <if test="height != null">
                Height,
            </if>
            <if test="volume != null">
                Volume,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="retrieve != null">
                #{retrieve,jdbcType=TINYINT},
            </if>
            <if test="specifications != null">
                #{specifications,jdbcType=VARCHAR},
            </if>
            <if test="assetsType != null">
                #{assetsType,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="length != null">
                #{length,jdbcType=DECIMAL},
            </if>
            <if test="width != null">
                #{width,jdbcType=DECIMAL},
            </if>
            <if test="height != null">
                #{height,jdbcType=DECIMAL},
            </if>
            <if test="volume != null">
                #{volume,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO">
        update assetsinfo
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="code != null and code!=''">
                Code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name!=''">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="retrieve != null">
                Retrieve = #{retrieve,jdbcType=TINYINT},
            </if>
            <if test="specifications != null">
                Specifications = #{specifications,jdbcType=VARCHAR},
            </if>
            <if test="assetsType != null">
                AssetsType = #{assetsType,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                Description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="description == null">
                Description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="length != null">
                Length = #{length,jdbcType=DECIMAL},
            </if>
            <if test="width != null">
                Width = #{width,jdbcType=DECIMAL},
            </if>
            <if test="height != null">
                Height = #{height,jdbcType=DECIMAL},
            </if>
            <if test="volume != null">
                Volume = #{volume,jdbcType=DECIMAL},
            </if>
            LastUpdateTime = now()
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateAssetsInfoByEdit"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.AssetsInfoPO">
        update assetsinfo set
        Name = #{name,jdbcType=VARCHAR},
        Specifications = #{specifications,jdbcType=VARCHAR},
        Description = #{description,jdbcType=VARCHAR},
        Length = #{length,jdbcType=DECIMAL},
        Width = #{width,jdbcType=DECIMAL},
        Height = #{height,jdbcType=DECIMAL},
        Volume = #{volume,jdbcType=DECIMAL},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
        LastUpdateTime = now()
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertList" parameterType="list">
        INSERT INTO assetsinfo
        (
        Id, Warehouse_Id, Code, Name, State, Retrieve, Specifications,
        AssetsType, Description, Remark, CreateUser, LastUpdateUser, Length, Width, Height, Volume
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.warehouseId},#{item.code}, #{item.name}, #{item.state}, #{item.retrieve},
            #{item.specifications},
            #{item.assetsType}, #{item.description}, #{item.remark}, #{item.createUser}, #{item.lastUpdateUser},
            #{item.length}, #{item.width}, #{item.height}, #{item.volume}
            )
        </foreach>
    </insert>

    <select id="selectByCodeAndState" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from assetsinfo
        where code IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="stateList != null and stateList.size() > 0">
            AND State IN
            <foreach collection="stateList" item="state" index="index" separator="," open="(" close=")">
                #{state,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateAssetsInfo" parameterType="java.util.List">
        update assetsinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="State =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.state != null ">
                        when id = #{po.id} then #{po.state}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.remark != null ">
                        when id = #{po.id} then #{po.remark}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.lastUpdateUser != null ">
                        when id = #{po.id} then #{po.lastUpdateUser}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id}
        </foreach>
    </update>

    <select id="findAssetsInfoByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from assetsinfo
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="state != null">
            AND State = #{state,jdbcType=TINYINT}
        </if>
        <if test="assetsType != null">
            AND AssetsType = #{assetsType,jdbcType=VARCHAR}
        </if>
        <if test="name != null">
            AND Name like concat('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        <if test="code != null">
            AND Code like concat(#{code,jdbcType=VARCHAR},'%')
        </if>
    </select>

</mapper>