<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.CityWarehouseMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.CityWarehousePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="IsDefault" jdbcType="TINYINT" property="isDefault"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, Warehouse_Id, City_Id, IsDefault, CreateTime, CreateUser_Id, LastUpdateTime,
        LastUpdateUser_Id
    </sql>

    <insert id="insertCityWarehousePOBatch">
        insert into citywarehouse (
        id,
        Warehouse_Id,
        City_Id,
        IsDefault,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id)
        values
        <foreach collection="poList" item="item" index="index" separator=",">
            (#{item.id,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.cityId,jdbcType=INTEGER},
            #{item.isDefault,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUserId,jdbcType=INTEGER})
        </foreach>
    </insert>

    <delete id="deleteCityWarehouseByWareHouseId">
        delete from citywarehouse
        where Warehouse_Id in
        <foreach item="item" index="index" collection="warehouseIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>