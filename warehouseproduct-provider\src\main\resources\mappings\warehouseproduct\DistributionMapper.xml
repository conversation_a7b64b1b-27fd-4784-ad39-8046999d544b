<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.DistributionMapper">

    <resultMap id="distributionPercentMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentDTO">
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="distributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
    </resultMap>
    <update id="addDistributionPercent">
        UPDATE productsku
        set
        distributionPercentForAmount =#{distributionPercentForAmount,jdbcType=DECIMAL},
        LastUpdateTime = now(),LastUpdateUserId = #{userId}
        where ProductSku_Id in
        <foreach collection="skuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <select id="getDistributionPercentBySku" resultMap="distributionPercentMap">
        SELECT distributionPercentForAmount from productsku
        where ProductSku_Id = #{productSkuId}
    </select>
    <!--根据skuIdList获取配送系数-->
    <select id="getDistributionPercentBySkuList" resultMap="distributionPercentMap">
        SELECT ProductSku_Id, City_Id,distributionPercentForAmount from productsku
        where ProductSku_Id in
        <foreach collection="productSkuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <!--插入配送系数变更记录-->
    <insert id="addDistributionPercentRecord">
        INSERT INTO changerecordlog (
        id,
        City_Id,
        Business_Id,
        BusinessType,
        Description,
        OldContext,
        CreateTime,
        CreateUserId
        )
        VALUES
        <foreach collection="poList" index="index" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT} ,#{item.cityId},#{item.businessId},#{item.businessType},
            #{item.description},#{item.oldContext}, NOW(),#{item.userId})
        </foreach>
    </insert>

</mapper>