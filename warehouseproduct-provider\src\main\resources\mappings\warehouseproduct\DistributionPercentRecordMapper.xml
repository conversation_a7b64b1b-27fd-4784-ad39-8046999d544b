<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.DistributionPercentRecordMapper">

    <select id="findDistributionPercentRecord"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentRecordDTO"
            resultType="integer">
        SELECT count(Id) FROM changerecordlog
        where
        BusinessType='修改配送系数'
        AND TO_DAYS(CreateTime) >= TO_DAYS(#{beginTime})
        AND TO_DAYS(CreateTime) &lt;= TO_DAYS(#{endTime})
        ORDER BY CreateTime desc

    </select>
</mapper>