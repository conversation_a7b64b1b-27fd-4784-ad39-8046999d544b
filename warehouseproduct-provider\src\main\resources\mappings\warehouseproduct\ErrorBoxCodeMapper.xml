<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ErrorBoxCodeMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ErrorBoxCodePO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="SkuId" jdbcType="BIGINT" property="skuId"/>
        <result column="Code" jdbcType="VARCHAR" property="code"/>
        <result column="OpType" jdbcType="INTEGER" property="opType"/>
        <result column="InputType" jdbcType="INTEGER" property="inputType"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id,
        SkuId,Code,OpType,InputType,State,Remark,CreateTime,LastUpdateTime,CreateUser,LastUpdateUser
    </sql>

    <insert id="insertList" parameterType="java.util.List">
        insert into ErrorBoxCode (
        Id, Org_Id, Warehouse_Id, SkuId,Code,OpType,InputType,State,Remark,CreateUser
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.skuId,jdbcType=BIGINT},
            #{item.code,jdbcType=VARCHAR},
            #{item.opType,jdbcType=INTEGER},
            #{item.inputType,jdbcType=INTEGER},
            #{item.state,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="listRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ErrorBoxCode
        <where>
            <if test="orgId != null">
                and Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="opType != null">
                and opType = #{opType,jdbcType=TINYINT}
            </if>
            <if test="inputType != null">
                and inputType = #{inputType,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and State = #{state,jdbcType=TINYINT}
            </if>
            <if test="startTime != null">
                and <![CDATA[ CreateTime >=  #{startTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="endTime != null">
                and <![CDATA[ CreateTime <=  #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="remark != null and remark != ''">
                <bind name="likeRemark" value="'%'+remark+'%'"/>
                and Remark like #{likeRemark}
            </if>
        </where>
        order by Org_Id, Warehouse_Id, CreateTime desc
    </select>

</mapper>