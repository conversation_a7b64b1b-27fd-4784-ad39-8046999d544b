<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.GridPOMapper">
    <delete id="deleteByWarehouseId">
        delete from warehouseroutepoints where WarehouseId = #{warehouseId,jdbcType=INTEGER}
    </delete>

    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouseroutepoints where WarehouseId = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="selectByWarehouseIdAndFloor" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouseroutepoints
        where WarehouseId = #{warehouseId,jdbcType=INTEGER}
        and Floor = #{floor,jdbcType=INTEGER}
    </select>
</mapper>