<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationAreaPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO">

        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouse_Id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Area" jdbcType="VARCHAR" property="area"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="subcategory" jdbcType="TINYINT" property="subcategory"/>
        <result column="locationCapacity" jdbcType="INTEGER" property="locationCapacity"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Width" jdbcType="DECIMAL" property="width"/>
        <result column="Height" jdbcType="DECIMAL" property="height"/>
        <result column="CoordinateX" jdbcType="DECIMAL" property="coordinateX"/>
        <result column="CoordinateY" jdbcType="DECIMAL" property="coordinateY"/>
        <result column="Layer" jdbcType="INTEGER" property="layer"/>
        <result column="BusinessType" jdbcType="TINYINT" property="businessType"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Warehouse_Id, City_Id,Name,Area, Remo, CreateTime, CreateUserId, LastUpdateTime,
        LastUpdateUserId,category,subcategory,locationCapacity, State, Width, Height, CoordinateX,
        CoordinateY, Layer, BusinessType
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location
        where Id = #{id,jdbcType=BIGINT} and category=1
    </select>
    <select id="findAreaIdByAreaList" resultMap="BaseResultMap">
        select id,name from location where name in
        <foreach collection="areaList" open="(" close=")" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and warehouse_Id=#{warehouse_Id,jdbcType=INTEGER}
        and city_Id=#{city_Id,jdbcType=INTEGER}
        and category=1
    </select>

    <insert id="addLocationArea"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO">
        INSERT INTO location(<include refid="Base_Column_List"/>)
        VALUES
        (#{id,jdbcType=BIGINT},#{warehouse_Id},#{city_Id},#{name},null,#{remo},now(),#{createUserId},now(),#{lastUpdateUserId},1,
        #{subcategory},#{locationCapacity},1,
        #{width,jdbcType=DECIMAL}, #{height, jdbcType=DECIMAL},
        #{coordinateX,jdbcType=DECIMAL}, #{coordinateY,jdbcType=DECIMAL}, #{layer,jdbcType=INTEGER}, #{businessType})
    </insert>

    <select id="getLocationArea"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        WHERE Warehouse_Id =#{warehouse_Id}
        and City_Id =#{city_Id}
        and Name =#{name}
        and category=1 LIMIT 1
    </select>

    <select id="listLocationArea"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO"
            resultMap="BaseResultMap">
        SELECT
        loc.Id, loc.Warehouse_Id, loc.City_Id,loc.Name,loc.Area, loc.Remo, loc.CreateTime, loc.CreateUserId,
        loc.LastUpdateTime,
        loc.LastUpdateUserId,loc.category,loc.subcategory,loc.locationCapacity, loc.State, loc.Width, loc.Height,
        loc.CoordinateX,
        loc.CoordinateY, loc.Layer, loc.BusinessType
        FROM location loc
        WHERE Warehouse_Id = #{locationAreaPO.warehouse_Id,jdbcType=INTEGER}
        and category = 1
        <if test="locationAreaPO.name != null and locationAreaPO.name !=''">
            and name like concat('%',#{locationAreaPO.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationAreaPO.remo != null and locationAreaPO.remo !=''">
            and Remo like concat('%',#{locationAreaPO.remo,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationAreaPO.subcategory != null and locationAreaPO.subcategory !=''">
            and subcategory =#{locationAreaPO.subcategory}
        </if>
        <if test="hasLocation != null and hasLocation">
            and exists(select l.id from location l where l.area_id = loc.id and l.state = 1 and l.category = 0)
            and loc.state = 1
        </if>
        <if test="locationAreaPO.excludeIds != null and locationAreaPO.excludeIds.size() != 0">
            and id not in
            <foreach collection="locationAreaPO.excludeIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="locationAreaPO.state !=null">
            and State = #{locationAreaPO.state,jdbcType=TINYINT}
        </if>
        <if test="locationAreaPO.subCategories != null and locationAreaPO.subCategories.size() != 0">
            and subcategory in
            <foreach collection="locationAreaPO.subCategories" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="locationAreaPO.businessType != null">
            and BusinessType = #{locationAreaPO.businessType}
        </if>
        order by CreateTime desc
    </select>

    <select id="listLocationAreaNoPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        WHERE category=1
        <if test="locationAreaDTO.warehouseId != null ">
            and Warehouse_Id = #{locationAreaDTO.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="locationAreaDTO.area != null and locationAreaDTO.area !=''">
            and name like concat('%',#{locationAreaDTO.area,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationAreaDTO.remo != null and locationAreaDTO.remo !=''">
            and Remo like concat('%',#{locationAreaDTO.remo,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationAreaDTO.subcategory != null  and locationAreaDTO.subcategory !=''">
            and subcategory =#{locationAreaDTO.subcategory}
        </if>
        <if test="locationAreaDTO.subcategoryList != null and locationAreaDTO.subcategoryList.size >0">
            and subcategory in
            <foreach collection="locationAreaDTO.subcategoryList " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLocationAreaById" resultMap="BaseResultMap">
        select id,name,City_Id,Warehouse_Id,subcategory from location where id=#{id} and category=1
    </select>

    <select id="getLocationAreaByArea" resultType="string">
        SELECT name from location
        WHERE Warehouse_Id =#{warehouseId} and Name =#{area} and category=1 and id not in(#{id})
    </select>

    <update id="updateLocationArea"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO">
        UPDATE location SET
        Remo=#{remo,jdbcType=VARCHAR},
        LastUpdateTime=now(),
        <if test="locationCapacity!=null">
            locationCapacity=#{locationCapacity},
        </if>
        LastUpdateUserId=#{lastUpdateUserId}
        where id=#{id} and category=1
    </update>

    <delete id="deleteLocation">
        DELETE FROM location WHERE id=#{id} and category=1
    </delete>

    <update id="updateLocationAreaByPO"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationAreaPO">
        UPDATE location
        SET
        <if test="name != null and name != '' ">
            Name=#{name,jdbcType=VARCHAR},
        </if>
        <if test="remo!=null">
            Remo=#{remo,jdbcType=VARCHAR},
        </if>
        <if test="subcategory!=null">
            subcategory=#{subcategory},
        </if>
        <if test="state!=null">
            State=#{state},
        </if>
        <if test="locationCapacity!=null">
            locationCapacity=#{locationCapacity},
        </if>
        <if test="width!=null">
            Width=#{width,jdbcType=DECIMAL},
        </if>
        <if test="height !=null">
            Height=#{height,jdbcType=DECIMAL},
        </if>
        <if test="coordinateX !=null">
            CoordinateX=#{coordinateX,jdbcType=DECIMAL},
        </if>
        <if test="coordinateY !=null">
            CoordinateY=#{coordinateY,jdbcType=DECIMAL},
        </if>
        <if test="layer !=null">
            Layer=#{layer,jdbcType=DECIMAL},
        </if>
        <if test="businessType != null and businessType == 0">
            BusinessType = null,
        </if>
        <if test="businessType != null and businessType != 0">
            BusinessType = #{businessType},
        </if>
        LastUpdateTime=now(),
        LastUpdateUserId=#{lastUpdateUserId}
        where id=#{id,jdbcType=VARCHAR} and category=1
    </update>


</mapper>