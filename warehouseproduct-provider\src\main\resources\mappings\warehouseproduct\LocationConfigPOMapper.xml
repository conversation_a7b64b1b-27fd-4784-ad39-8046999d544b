<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationConfigPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        <!--@Table locationconfig-->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId"/>
      <result column="ConfigType" jdbcType="TINYINT" property="configType"/>
        <result column="LocationConfig" jdbcType="LONGVARCHAR" property="locationConfig"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="INTEGER" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="INTEGER" property="lastUpdateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
      Id, WarehouseId, ConfigType, LocationConfig, CreateTime, `CreateUser`, LastUpdateTime,
      LastUpdateUser
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from locationconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from locationconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
      insert into locationconfig (Id, WarehouseId, ConfigType,
      LocationConfig, CreateTime, `CreateUser`,
      LastUpdateTime, LastUpdateUser)
      values (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{configType,jdbcType=TINYINT},
      #{locationConfig,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER},
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        insert into locationconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
          <if test="configType != null">
            ConfigType,
          </if>
            <if test="locationConfig != null">
                LocationConfig,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
          <if test="configType != null">
            #{configType,jdbcType=TINYINT},
          </if>
            <if test="locationConfig != null">
                #{locationConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        update locationconfig
        <set>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
          <if test="configType != null">
            ConfigType = #{configType,jdbcType=TINYINT},
          </if>
            <if test="locationConfig != null">
                LocationConfig = #{locationConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
            </if>
            LastUpdateTime = now()
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        update locationconfig
        set WarehouseId = #{warehouseId,jdbcType=INTEGER},
      ConfigType = #{configType,jdbcType=TINYINT},
        LocationConfig = #{locationConfig,jdbcType=LONGVARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update locationconfig
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                </foreach>
            </trim>
          <trim prefix="ConfigType = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
              when Id = #{item.id,jdbcType=BIGINT} then #{item.configType,jdbcType=TINYINT}
            </foreach>
          </trim>
            <trim prefix="LocationConfig = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.locationConfig,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update locationconfig
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
          <trim prefix="ConfigType = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
              <if test="item.configType != null">
                when Id = #{item.id,jdbcType=BIGINT} then #{item.configType,jdbcType=TINYINT}
              </if>
            </foreach>
          </trim>
            <trim prefix="LocationConfig = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.locationConfig != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.locationConfig,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into locationconfig
      (Id, WarehouseId, ConfigType, LocationConfig, CreateTime, `CreateUser`, LastUpdateTime,
      LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
          (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.configType,jdbcType=TINYINT},
          #{item.locationConfig,jdbcType=LONGVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
          #{item.createUser,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="map">
        <!--@mbg.generated-->
        insert into locationconfig
      (Id, WarehouseId, ConfigType, LocationConfig, CreateTime, `CreateUser`, LastUpdateTime,
      LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
          (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.configType,jdbcType=TINYINT},
          #{item.locationConfig,jdbcType=LONGVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
          #{item.createUser,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=INTEGER})
        </foreach>
        on duplicate key update
        Id=values(Id),
        WarehouseId=values(WarehouseId),
      ConfigType=values(ConfigType),
        LocationConfig=values(LocationConfig),
        CreateTime=values(CreateTime),
        CreateUser=values(CreateUser),
        LastUpdateTime=values(LastUpdateTime),
        LastUpdateUser=values(LastUpdateUser)
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from locationconfig where Id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <insert id="insertOrUpdate"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        insert into locationconfig
      (Id, WarehouseId, ConfigType, LocationConfig, CreateTime, `CreateUser`, LastUpdateTime,
      LastUpdateUser)
        values
      (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{configType,jdbcType=TINYINT},
      #{locationConfig,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER},
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=INTEGER})
        on duplicate key update
        Id = #{id,jdbcType=BIGINT},
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
      ConfigType = #{configType,jdbcType=TINYINT},
        LocationConfig = #{locationConfig,jdbcType=LONGVARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationConfigPO">
        <!--@mbg.generated-->
        insert into locationconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
          <if test="configType != null">
            ConfigType,
            </if>
            <if test="locationConfig != null">
                LocationConfig,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
          <if test="configType != null">
            #{configType,jdbcType=TINYINT},
            </if>
            <if test="locationConfig != null">
                #{locationConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
          <if test="configType != null">
            ConfigType = #{configType,jdbcType=TINYINT},
            </if>
            <if test="locationConfig != null">
                LocationConfig = #{locationConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                `CreateUser` = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>