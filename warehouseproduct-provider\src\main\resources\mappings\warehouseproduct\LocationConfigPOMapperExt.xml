<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationConfigPOMapper">
    <select id="selectByWarehouseIdAndConfigType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from locationconfig
        where WarehouseId = #{param1,jdbcType=INTEGER}
        and ConfigType = #{param2,jdbcType=INTEGER}
    </select>

    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from locationconfig
        where WarehouseId = #{param1,jdbcType=INTEGER}
    </select>

</mapper>