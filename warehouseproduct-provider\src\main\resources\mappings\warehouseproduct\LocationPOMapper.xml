<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouse_Id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Area" jdbcType="VARCHAR" property="area"/>
        <result column="Area_Id" jdbcType="BIGINT" property="area_Id"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Pallets" jdbcType="VARCHAR" property="pallets"/>
        <result column="ProductLocation" jdbcType="VARCHAR" property="productLocation"/>
        <result column="RoadWay" jdbcType="VARCHAR" property="roadWay"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="subcategory" jdbcType="TINYINT" property="subcategory"/>
        <result column="ischaosbatch" jdbcType="TINYINT" property="isChaosBatch"/>
        <result column="ischaosput" jdbcType="TINYINT" property="isChaosPut"/>
        <result column="locationCapacity" jdbcType="INTEGER" property="locationCapacity"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Width" jdbcType="DECIMAL" property="width"/>
        <result column="Height" jdbcType="DECIMAL" property="height"/>
        <result column="CoordinateX" jdbcType="DECIMAL" property="coordinateX"/>
        <result column="CoordinateY" jdbcType="DECIMAL" property="coordinateY"/>
        <result column="Layer" jdbcType="INTEGER" property="layer"/>
        <result column="IsExpress" jdbcType="TINYINT" property="express"/>
        <result column="locationSubcategory" jdbcType="INTEGER" property="locationSubcategory"/>
        <result column="palletCount" jdbcType="INTEGER" property="palletCount"/>
        <result column="BusinessType" jdbcType="TINYINT" property="businessType"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Warehouse_Id, City_Id, Name, Area, area_Id,sequence, Pallets, ProductLocation, RoadWay,
        Remo, CreateTime,
        CreateUserId, LastUpdateTime, LastUpdateUserId,category,subcategory,ischaosbatch,ischaosput,locationCapacity,
        State,Width, Height, CoordinateX, CoordinateY, Layer, IsExpress, palletCount, BusinessType
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from location
        where Id = #{id,jdbcType=BIGINT}
        and category = 0
    </delete>
    <delete id="deleteByPrimaryKeyBatch" parameterType="java.lang.Long">
        delete from location
        where Id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and category=0
    </delete>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        insert into location
        <trim prefix="(" suffix=")" suffixOverrides=",">
            Id,
            <if test="warehouse_Id != null">
                Warehouse_Id,
            </if>
            <if test="city_Id != null">
                City_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="area != null">
                Area,
            </if>
            <if test="area_Id != null">
                Area_Id,
            </if>
            <if test="sequence != null">
                sequence,
            </if>
            <if test="pallets != null">
                Pallets,
            </if>
            <if test="productLocation != null">
                ProductLocation,
            </if>
            <if test="roadWay != null">
                RoadWay,
            </if>
            <if test="remo != null">
                Remo,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
            category,
            <if test="isChaosPut != null">
                ischaosput,
            </if>
            <if test="isChaosBatch != null">
                ischaosbatch,
            </if>
            <if test="subcategory != null">
                subcategory,
            </if>
            <if test="locationCapacity != null">
                locationCapacity,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="express != null">
                IsExpress,
            </if>
            <if test="aisleId != null">
                aisleId,
            </if>
            <if test="aisleNo != null">
                aisleNo,
            </if>
            <if test="businessType != null">
                BusinessType,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=BIGINT},
            <if test="warehouse_Id != null">
                #{warehouse_Id,jdbcType=INTEGER},
            </if>
            <if test="city_Id != null">
                #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="area_Id != null">
                #{area_Id,jdbcType=BIGINT},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=INTEGER},
            </if>
            <if test="pallets != null">
                #{pallets,jdbcType=VARCHAR},
            </if>
            <if test="productLocation != null">
                #{productLocation,jdbcType=VARCHAR},
            </if>
            <if test="roadWay != null">
                #{roadWay,jdbcType=VARCHAR},
            </if>
            <if test="remo != null">
                #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="category == null">
                0,
            </if>
            <if test="category != null">
                #{category,jdbcType=INTEGER},
            </if>
            <if test="isChaosPut != null">
                #{isChaosPut},
            </if>
            <if test="isChaosBatch != null">
                #{isChaosBatch},
            </if>
            <if test="subcategory != null">
                #{subcategory},
            </if>
            <if test="locationCapacity != null">
                #{locationCapacity},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="express != null">
                #{express},
            </if>
            <if test="aisleId != null">
                #{aisleId},
            </if>
            <if test="aisleNo != null">
                #{aisleNo},
            </if>
            <if test="businessType != null">
                #{businessType},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        update location
        <set>
            <if test="warehouse_Id != null">
                Warehouse_Id = #{warehouse_Id,jdbcType=INTEGER},
            </if>
            <if test="city_Id != null">
                City_Id = #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                sequence = #{sequence,jdbcType=INTEGER},
            </if>
            <if test="area != null">
                Area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="area_Id != null">
                area_Id = #{area_Id,jdbcType=BIGINT},
            </if>
            <if test="pallets != null">
                Pallets = #{pallets,jdbcType=VARCHAR},
            </if>
            <if test="productLocation != null">
                ProductLocation = #{productLocation,jdbcType=VARCHAR},
            </if>
            <if test="roadWay != null">
                RoadWay = #{roadWay,jdbcType=VARCHAR},
            </if>
            <if test="remo != null">
                Remo = #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="subcategory != null">
                subcategory = #{subcategory},
            </if>
            <if test="locationCapacity != null">
                locationCapacity=#{locationCapacity},
            </if>
            <if test="isChaosPut!= null">
                isChaosPut=#{isChaosPut},
            </if>
            <if test="isChaosBatch!= null">
                ischaosbatch=#{isChaosBatch},
            </if>
            <if test="state!= null">
                State=#{state},
            </if>
            <if test="express!= null">
                IsExpress=#{express},
            </if>
            <if test="aisleId != null">
                aisleId = #{aisleId},
            </if>
            <if test="aisleNo != null and aisleNo != ''">
                aisleNo = #{aisleNo},
            </if>
            <if test="businessType != null and businessType == 0">
                BusinessType = null,
            </if>
            <if test="businessType != null and businessType != 0">
                BusinessType = #{businessType},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT} and category=0
    </update>

    <select id="selectBySequence" resultType="java.lang.Integer">
        select count(*)
        from location
        where category=0
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and City_Id = #{cityId,jdbcType=INTEGER}
        and sequence = #{sequence,jdbcType=INTEGER}
        <if test="id != null">
            and Id <![CDATA[<>]]> #{id,jdbcType=BIGINT}
        </if>
    </select>

    <!--获取指定仓库下的所有货位-->
    <select id="listLocation" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        WHERE category = 0
        <if test="warehouseId != null">
            AND Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="locationName != null and locationName != ''">
            AND Name like concat('%', #{locationName,jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY createTime DESC
    </select>

    <select id="listLocationByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List">
        </include>
        FROM location
        WHERE 1 = 1
        <if test="dto.categoryList == null or dto.categoryList.size() == 0">
            and category = 0
        </if>
        <if test="dto.categoryList != null and dto.categoryList.size() != 0">
            and category in
            <foreach collection="dto.categoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.cityId!=null">
            and City_Id=#{dto.cityId,jdbcType=INTEGER}
        </if>
        <if test="dto.area !=null and dto.area !='' ">
            and Area=#{dto.area,jdbcType=VARCHAR}
        </if>
        <if test="dto.warehouseId!=null">
            AND Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.subcategoryList!=null and dto.subcategoryList.size() != 0">
            and subcategory in
            <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.locationIdList!=null and dto.locationIdList.size() != 0">
            and Id in
            <foreach collection="dto.locationIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.areaIdList!=null and dto.areaIdList.size() != 0">
            and area_Id in
            <foreach collection="dto.areaIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.state!= null">
            and State=#{dto.state}
        </if>
        <if test="dto.name != null">
            and Name = #{dto.name}
        </if>
        <if test="dto.businessTypeList != null and dto.businessTypeList.size() > 0">
            and BusinessType in
            <foreach collection="dto.businessTypeList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <select id="selectCargoAreaSingleByWarehouseIdAndCategory" resultMap="BaseResultMap">
        SELECT l.Id, l.Warehouse_Id, l.City_Id, l. NAME, l.Area, l.area_Id,
        l.sequence, l.Pallets, l.ProductLocation, l.RoadWay, l.Remo, l.CreateTime,
        l.CreateUserId, l.LastUpdateTime, l.LastUpdateUserId, l.category,
        l.subcategory, l.ischaosbatch, l.ischaosput, l.locationCapacity
        FROM
        location l
        JOIN (
        SELECT
        max(id) AS id,
        subcategory
        FROM
        location
        WHERE
        Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="category != null">
            and category = #{category,jdbcType=TINYINT}
        </if>
        GROUP BY
        subcategory
        ) tmp ON l.Id = tmp.id
    </select>
    <select id="findLocationAndAreaInfoById"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        SELECT
        l.Id, l.Warehouse_Id as warehouseId, l.City_Id as cityId, l.Name, l.Area, l.area_Id, l.sequence, l.Pallets,
        l.ProductLocation, l.RoadWay, l.Remo, l.CreateTime,
        l.CreateUserId as userId, l.LastUpdateTime, l.LastUpdateUserId, l.category, l.subcategory, l.ischaosbatch,
        l.ischaosput, l.locationCapacity,
        IFNULL(vesselarea.subcategory, area.subcategory) AS areacategory
        FROM
        location l
        INNER JOIN location area ON IFNULL( l.area_Id, l.Id ) = area.Id
        AND l.City_Id = area.City_Id
        LEFT JOIN location vesselarea ON area.area_Id = vesselarea.Id
        AND l.City_Id = area.City_Id
        WHERE
        l.Id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findLocationPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        WHERE 1 = 1
        <if test="dto.categoryList == null or dto.categoryList.size() == 0">
            and category = 0
        </if>
        <if test="dto.categoryList != null and dto.categoryList.size() != 0">
            and category in
            <foreach collection="dto.categoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.cityId!=null">
            and City_Id=#{dto.cityId,jdbcType=INTEGER}
        </if>
        <if test="dto.area !=null and dto.area !='' ">
            and Area=#{dto.area,jdbcType=VARCHAR}
        </if>
        <if test="dto.warehouseId!=null">
            AND Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.subcategoryList!=null and dto.subcategoryList.size() != 0">
            and subcategory in
            <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.locationIdList!=null and dto.locationIdList.size() != 0">
            and Id in
            <foreach collection="dto.locationIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.areaIdList!=null and dto.areaIdList.size() != 0">
            and area_Id in
            <foreach collection="dto.areaIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.state!= null">
            and State=#{dto.state}
        </if>
    </select>

    <insert id="insertSelectiveBatch" parameterType="java.util.List">
        insert into location (
        Id, Warehouse_Id, City_Id, Name, Area, area_Id, sequence, Pallets, ProductLocation, RoadWay, Remo, CreateTime,
        CreateUserId, LastUpdateTime,
        LastUpdateUserId, category, ischaosput, ischaosbatch, subcategory, locationCapacity, State,IsExpress
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},#{item.warehouse_Id,jdbcType=INTEGER},#{item.city_Id,jdbcType=INTEGER},#{item.name,jdbcType=VARCHAR},#{item.area,jdbcType=VARCHAR},
            #{item.area_Id,jdbcType=BIGINT},#{item.sequence,jdbcType=INTEGER},#{item.pallets,jdbcType=VARCHAR},#{item.productLocation,jdbcType=VARCHAR},#{item.roadWay,jdbcType=VARCHAR},
            #{item.remo,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP},#{item.createUserId,jdbcType=INTEGER},#{item.lastUpdateTime,jdbcType=TIMESTAMP},#{item.lastUpdateUserId,jdbcType=INTEGER},
            #{item.category},#{item.isChaosPut},#{item.isChaosBatch}, #{item.subcategory}, #{item.locationCapacity},
            #{item.state},#{item.express}
            )
        </foreach>
    </insert>

    <delete id="deleteVesselByPrimaryKeyBatch" parameterType="java.lang.Long">
        delete from location
        where Id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and category = 2
    </delete>
</mapper>
