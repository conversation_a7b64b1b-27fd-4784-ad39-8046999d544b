<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPOMapper">
    <resultMap id="ResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="city_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="area_Id" jdbcType="BIGINT" property="area_Id"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="roadway" jdbcType="VARCHAR" property="roadway"/>
        <result column="productlocation" jdbcType="VARCHAR" property="productlocation"/>
        <result column="pallets" jdbcType="VARCHAR" property="pallets"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="subcategory" jdbcType="TINYINT" property="subcategory"/>
        <result column="ischaosbatch" jdbcType="TINYINT" property="isChaosBatch"/>
        <result column="ischaosput" jdbcType="TINYINT" property="isChaosPut"/>
        <result column="locationCapacity" jdbcType="INTEGER" property="locationCapacity"/>
        <result column="locationGrade" jdbcType="TINYINT" property="locationGrade"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="palletCount" jdbcType="INTEGER" property="palletCount"/>
        <collection property="productSkuIdList" ofType="java.lang.Long"
                    javaType="list">
            <result column="productSkuId"/>
        </collection>
    </resultMap>

    <resultMap id="ResultMapNew" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="city_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="area_Id" jdbcType="BIGINT" property="area_Id"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="roadway" jdbcType="VARCHAR" property="roadway"/>
        <result column="productlocation" jdbcType="VARCHAR" property="productlocation"/>
        <result column="pallets" jdbcType="VARCHAR" property="pallets"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="subcategory" jdbcType="TINYINT" property="subcategory"/>
        <result column="ischaosbatch" jdbcType="TINYINT" property="isChaosBatch"/>
        <result column="ischaosput" jdbcType="TINYINT" property="isChaosPut"/>
        <result column="locationCapacity" jdbcType="INTEGER" property="locationCapacity"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
    </resultMap>

    <resultMap id="ResultMapVessel"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="area_Id" jdbcType="BIGINT" property="area_Id"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="roadway" jdbcType="VARCHAR" property="roadway"/>
        <result column="productlocation" jdbcType="VARCHAR" property="productlocation"/>
        <result column="pallets" jdbcType="VARCHAR" property="pallets"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="subcategory" jdbcType="TINYINT" property="subcategory"/>
        <result column="ischaosbatch" jdbcType="TINYINT" property="isChaosBatch"/>
        <result column="ischaosput" jdbcType="TINYINT" property="isChaosPut"/>
        <result column="locationCapacity" jdbcType="INTEGER" property="locationCapacity"/>
        <result column="locationGrade" jdbcType="TINYINT" property="locationGrade"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="productName" jdbcType="VARCHAR" property="productName"/>
        <result column="storeTotalCount" property="storeTotalCount" jdbcType="DECIMAL"/>
        <result column="correspondLoactionName" jdbcType="VARCHAR" property="correspondLoactionName"/>
        <result column="productSkuId" property="productSkuId" jdbcType="BIGINT"/>

        <!--        <collection property="productSkuIdList" ofType="java.lang.Long"-->
        <!--                    javaType="list">-->
        <!--            <result column="productSkuId"/>-->
        <!--        </collection>-->
        <collection property="vesselInfoDTO"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselInfoDTO">
            <id column="vesselId" property="id" jdbcType="BIGINT"/>
            <result column="vesselWarehouseId" jdbcType="INTEGER" property="warehouseId"/>
            <result column="vesselLocationId" property="locationId" jdbcType="BIGINT"/>
            <result column="vesselLocationNo" jdbcType="VARCHAR" property="locationNo"/>
            <result column="vesselCurrentLocationId" property="currentLocationId" jdbcType="BIGINT"/>
            <result column="vesselcCurrentLocationName" jdbcType="VARCHAR" property="currentLocationName"/>
            <result column="vesselIsFreeze" property="isFreeze" jdbcType="TINYINT"/>
            <result column="configRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="vesselCreateTime" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="vesselCreateUser" jdbcType="VARCHAR" property="createUser"/>
            <result column="vesselLastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
            <result column="vesselLastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
            <result column="vesselRremark" jdbcType="VARCHAR" property="remark"/>
        </collection>
    </resultMap>


    <select id="selectByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location
        where name = #{name,jdbcType=VARCHAR}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and category in (0,2)
        <if test="cityId != null">
            and City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location
        where name in
        <foreach collection="names" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and category=0
        <if test="cityId != null">
            and City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <sql id="findProductLocation">
        <where>
            <if test="warehouseId != null">
                and a.warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and a.city_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="name != null and name != ''">
                and a.name like
                concat('%',TRIM(#{name,jdbcType=VARCHAR}),'%')
            </if>
            <if test="area != null and area != ''">
                and a.area = #{area,jdbcType=VARCHAR}
            </if>
            <if test="isChaosPut != null ">
                and a.ischaosput = #{isChaosPut}
            </if>
            <if test="isChaosBatch != null ">
                and a.ischaosbatch = #{isChaosBatch}
            </if>
            <if test="subcategory != null ">
                and a.subcategory = #{subcategory}
            </if>
            <if test="state != null ">
                and a.State = #{state}
            </if>
            <if test="subcategoryList!=null and subcategoryList.size()>0">
                and subcategory in
                <foreach collection="subcategoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categoryList != null">
                and a.category in
                <foreach collection="categoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categoryList == null">
                and a.category = 0
            </if>
        </where>
    </sql>

    <select id="findProductLocationPageList" resultMap="ResultMap" parameterType="java.util.Map">
        select a.id,a.name,a.city_Id,a.warehouse_Id,
        a.area as area,a.area_Id,a.sequence,a.roadway,a.productlocation,a.pallets,a.createTime,
        b.productSku_Id as productSkuId,b.Id as
        productLocationId,a.category,a.subcategory,a.ischaosput,a.ischaosbatch,
        a.locationCapacity,a.locationGrade
        from
        location a
        left join productlocation b on a.Id = b.location_Id
        <if test="productName != null and productName != ''">
            inner join productsku sku on b.ProductSku_Id = sku.productsku_id
            <if test="productName != null and productName != ''">
                and sku.name like
                concat('%',TRIM(#{productName,jdbcType=VARCHAR}),'%')
            </if>
            <if test="cityId != null">
                and sku.city_Id = #{cityId,jdbcType=INTEGER}
            </if>
        </if>
        <include refid="findProductLocation"/>
        ORDER BY a.area asc,a.sequence asc, a.id asc
    </select>

    <select id="findProductLocationPageListNew" resultMap="ResultMapNew" parameterType="java.util.Map">
        select a.id,a.name,a.city_Id,a.warehouse_Id,
        a.area as area,a.area_Id,a.sequence,a.roadway,a.productlocation,a.pallets,a.createTime,
        a.category,a.subcategory,a.ischaosput,a.ischaosbatch,a.locationCapacity,a.State
        from location a
        <include refid="findProductLocation"/>
        ORDER BY a.area ,a.sequence ,a.id
    </select>

    <select id="findProductLocationOrAreaPageListNew" resultMap="ResultMapNew" parameterType="java.util.Map">
        select a.id,a.name,a.city_Id,a.warehouse_Id,
        a.area as area,a.area_Id,a.sequence,a.roadway,a.productlocation,a.pallets,a.createTime,
        a.category,a.subcategory,a.ischaosput,a.ischaosbatch,a.locationCapacity
        from location a
        <where>
            <if test="warehouseId != null">
                and a.warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="name != null and name != ''">
                and a.name like
                concat('%',TRIM(#{name,jdbcType=VARCHAR}),'%')
            </if>
        </where>
        ORDER BY a.area asc,a.sequence asc,a.id asc
    </select>
    <select id="findLocationListByWarehouseIdAndAreaType" resultMap="ResultMap" parameterType="java.util.Map">
        select a.Id, a.Warehouse_Id,a.City_Id, a.Name, a.Area, a.area_Id,a.sequence,a.Pallets, a.ProductLocation,
        a.RoadWay,
        a.Remo, a.CreateTime,
        a.CreateUserId, a.LastUpdateTime,
        a.LastUpdateUserId,a.category,a.subcategory,a.ischaosbatch,a.ischaosput,a.locationCapacity,0 as
        productSkuId,a.locationGrade, a.palletCount
        from
        location a
        inner JOIN location area on a.area_id = area.id
        and a.warehouse_Id = #{warehouseId,jdbcType=INTEGER} and area.subcategory = #{subcategory,jdbcType=INTEGER}
        <if test="locSubcategory != null">
            and a.subcategory = #{locSubcategory,jdbcType=INTEGER}
        </if>
        <if test="noPalletCount != null and noPalletCount == true ">
            and a.palletCount is null
        </if>
        ORDER BY a.createTime desc
    </select>

    <select id="findProductLocationCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(distinct(a.id))
        from
        location a
        left join productlocation b on a.Id = b.location_Id
        <if test="productName != null and productName != ''">
            inner join productsku sku on b.ProductSku_Id = sku.productsku_id
            <if test="productName != null and productName != ''">
                and sku.name like
                concat('%',TRIM(#{productName,jdbcType=VARCHAR}),'%')
            </if>
            <if test="cityId != null">
                and sku.city_Id = #{cityId,jdbcType=INTEGER}
            </if>
        </if>
        <include refid="findProductLocation"/>
    </select>

    <select id="getProductLocationBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDetailDTO">
        select a.id,a.name,a.city_Id as cityId,a.warehouse_Id as warehouseId,
        a.area as area,a.roadway,a.productlocation,a.pallets,
        b.productSku_Id,a.locationCapacity,a.locationGrade
        from
        location a
        inner join productlocation b
        on a.Id =
        b.location_Id and a.category=0
        <if test="dto.skuId != null">
            and b.ProductSku_Id = #{dto.skuId,jdbcType=BIGINT}
        </if>
        <if test="dto.warehouseId != null">
            and a.warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.cityId != null">
            and a.city_Id = #{dto.cityId,jdbcType=INTEGER}
        </if>
        limit 0,1
    </select>

    <select id="findProductLocationBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDetailDTO">
        select a.id,a.name,a.city_Id as cityId,a.warehouse_Id as warehouseId,
        a.area as area,a.roadway,a.productlocation,a.pallets,b.productSku_Id as productSkuId,
        a.category,a.subcategory,a.locationCapacity,a.locationGrade,
        a.area_Id as areaId
        from
        location a
        inner join productlocation b
        on a.Id =
        b.location_Id and a.category=0
        <if test="dto.warehouseId != null">
            and a.warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.cityId != null">
            and a.city_Id = #{dto.cityId,jdbcType=INTEGER}
        </if>
        <if test="dto.skuIdList != null and dto.skuIdList.size()>0">
            and b.ProductSku_Id in
            <foreach item="item" index="index" collection="dto.skuIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.productLocation != null and dto.productLocation != ''">
            and a.Name LIKE CONCAT('%', #{dto.productLocation,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="findProductSkuByProductLocation" resultType="long"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO">
        SELECT DISTINCT b.ProductSku_Id from location a inner join productlocation b
        on a.Id=b.Location_Id and a.category=0
        <where>
            <if test="cityId != null and cityId>0">
                and a.City_Id=#{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null and warehouseId>0">
                and a.Warehouse_Id=#{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productLocation != null and productLocation != ''">
                and a.Name LIKE CONCAT('%', #{productLocation,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>
    <select id="findLocationListById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        where id in
        <foreach collection="idList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="findLocationAreaListById" resultMap="BaseResultMap">
        select loc.id
        ,loc.Name
        ,ifnull(area.id, loc.id) as area_Id
        ,ifnull(area.name, loc.Name) as Area
        ,loc.Warehouse_Id
        ,loc.City_Id
        ,loc.category
        ,area.subcategory
        ,loc.subcategory as locationSubcategory
        from location loc
        left join location area on area.Id = loc.area_id
        where loc.Id in
        <foreach collection="idList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="findLocationList" resultMap="BaseResultMap">
        select locL.Id, locL.Warehouse_Id, locL.City_Id, locL.Name, locL.Area, locL.area_Id,locL.sequence, locL.Pallets,
        locL.ProductLocation, locL.RoadWay,
        locL.Remo, locL.CreateTime,
        locL.CreateUserId, locL.LastUpdateTime,
        locL.LastUpdateUserId,locL.category,locL.subcategory,locL.ischaosbatch,locL.ischaosput,locL.locationCapacity,locL.locationGrade,locL.IsExpress
        from location locL
        inner join location locArea on locL.area_id = locArea.id
        where locL.category = 0
        <if test="dto.subcategory!=null">
            and locArea.subcategory=#{dto.subcategory}
        </if>
        <if test="dto.subcategoryList!=null and dto.subcategoryList.size>0">
            and locArea.subcategory in
            <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.warehouseId!=null">
            and locL.Warehouse_Id=#{dto.warehouseId}
        </if>
        <if test="dto.cityId!=null">
            and locL.City_Id=#{dto.cityId}
        </if>
        <if test="dto.state != null">
            and locL.State = #{dto.state}
        </if>
        <if test="dto.express!=null">
            and locL.IsExpress=#{dto.express}
        </if>
        union all
        select locArea.Id, locArea.Warehouse_Id, locArea.City_Id, locArea.Name, locArea.Name as Area, locArea.Id as
        area_Id,locArea.sequence, locArea.Pallets, locArea.ProductLocation, locArea.RoadWay,
        locArea.Remo, locArea.CreateTime,
        locArea.CreateUserId, locArea.LastUpdateTime,
        locArea.LastUpdateUserId,locArea.category,locArea.subcategory,locArea.ischaosbatch,locArea.ischaosput,locArea.locationCapacity,locArea.locationGrade,locArea.IsExpress
        from location locArea
        where locArea.category = 1
        <if test="dto.subcategory!=null">
            and locArea.subcategory=#{dto.subcategory}
        </if>
        <if test="dto.subcategoryList!=null and dto.subcategoryList.size>0">
            and locArea.subcategory in
            <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.warehouseId!=null">
            and locArea.Warehouse_Id=#{dto.warehouseId}
        </if>
        <if test="dto.cityId!=null">
            and locArea.City_Id=#{dto.cityId}
        </if>
        <if test="dto.areaState != null">
            and locArea.State = #{dto.areaState}
        </if>
        <if test="dto.express!=null">
            and locArea.IsExpress=#{dto.express}
        </if>
    </select>

    <insert id="batchInsert">
        insert into location
        (Id,Warehouse_Id,City_Id,sequence,Name,Area,Area_Id,ProductLocation,RoadWay,Remo,CreateTime,
        CreateUserId,LastUpdateTime,LastUpdateUserId,
        category,subcategory,isChaosPut,isChaosBatch,locationCapacity,locationGrade,
        Width,Height,CoordinateX,CoordinateY,Layer)
        values
        <foreach collection="locationList" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{warehouse_Id,jdbcType=INTEGER},
            #{city_Id,jdbcType=INTEGER},
            #{item.sequence,jdbcType=INTEGER},
            #{item.name,jdbcType=VARCHAR},
            #{item.area,jdbcType=VARCHAR},
            #{item.area_Id,jdbcType=BIGINT},
            #{item.productLocation,jdbcType=VARCHAR},
            #{item.roadWay,jdbcType=VARCHAR},
            #{item.remo,jdbcType=VARCHAR},
            NOW(),
            #{item.createUserId,jdbcType=INTEGER},
            NOW(),
            #{item.lastUpdateUserId,jdbcType=INTEGER},
            0,#{item.subcategory},
            #{item.isChaosPut},
            #{item.isChaosBatch},
            #{item.locationCapacity},
            #{item.locationGrade},
            #{item.width},
            #{item.height},
            #{item.coordinateX},
            #{item.coordinateY},
            #{item.layer}
            )
        </foreach>
    </insert>

    <select id="listLocationDTO" resultMap="BaseResultMap">
        select loct.Id,
        loct.Warehouse_Id,
        loct.City_Id,
        loct.Name,
        loct1.Area,
        loct.State,
        loct.Pallets,
        loct.RoadWay,
        loct.Remo,
        loct.CreateTime,
        loct.CreateUserId,
        loct.LastUpdateTime,
        loct.LastUpdateUserId,
        loct.area_Id,
        loct.locationGrade
        from location loct
        inner join location loct1 ON loct.area_Id = loct1.id
        WHERE loct.area_Id = #{area_Id}
        and loct.category = 0
    </select>
    <update id="updateName">
        UPDATE location SET
        Area =CASE id
        <foreach collection="poList" item="item">
            WHEN #{item.id} THEN #{item.area}
        </foreach>
        end,
        Name =CASE id
        <foreach collection="poList" item="item">
            WHEN #{item.id} THEN #{item.name}
        </foreach>
        end,
        LastUpdateTime =CASE id
        <foreach collection="poList" item="item">
            WHEN #{item.id} THEN #{item.lastUpdateTime}
        </foreach>
        end,
        LastUpdateUserId =CASE id
        <foreach collection="poList" item="item">
            WHEN #{item.id} THEN #{item.lastUpdateUserId}
        </foreach>
        end
        where Id=#{item.id} and category=0
    </update>
    <delete id="deleteByAreaId">
        DELETE
        FROM location
        WHERE area_Id = #{area_Id}
        and category = 0
    </delete>

    <select id="selectByAreaId" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        select Id as id, Name as name
        FROM location
        WHERE area_Id = #{area_Id}
        and category = 0
    </select>

    <select id="listProductCount" resultType="integer">
        SELECT COUNT(loct.Id)
        FROM productlocation pl
        INNER JOIN location loct ON loct.Id = pl.Location_Id
        WHERE loct.area_Id = #{area_Id}
        and category = 1
    </select>
    <select id="listProductCountList" resultMap="BaseResultMap">
        SELECT
        loct.area_Id AS Area_Id,loct.ProductLocation,loct.Name
        FROM
        location AS loct
        WHERE
        loct.area_Id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and loct.category=0
    </select>
    <select id="findLocationById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        where id =#{id}
    </select>
    <select id="findLocationByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        where Warehouse_Id =#{warehouseId}
        and Name =#{name}
        limit 1
    </select>

    <select id="findLocationByNames" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM location
        where Warehouse_Id =#{warehouseId}
        and Name in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findLocationListByName"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationCategoryDTO">
        SELECT
        Id as id,
        Warehouse_Id as warehouseId,
        City_Id as cityId,
        Name as name,
        Area as area,
        Pallets as pallets,
        ProductLocation as productLocation,
        RoadWay as roadWay,
        Remo as remo,
        CreateTime as createTime,
        CreateUserId as createUserId,
        LastUpdateTime as lastUpdateTime,
        LastUpdateUserId as lastUpdateUserId,
        sequence as sequence,
        area_id as areaId,
        IsChaosPut as isChaosPut,
        IsChaosBatch as isChaosBatch,
        category as category,
        subcategory as subcategory,
        LocationCapacity as locationCapacity,
        locationGrade
        FROM location
        where Warehouse_Id =#{warehouseId}
        <if test="nameList.size>0">
            and Name IN
            <foreach collection="nameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and category=0
    </select>


    <delete id="deleteLocationByPrimaryKeyList" parameterType="java.util.List">
        DELETE location
        FROM location LEFT JOIN productlocation ON location.id=productlocation.location_id
        WHERE location.category=0 AND productlocation.id IS NULL
        AND location.id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getLocationByPrimaryKeyList" parameterType="java.util.List"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        SELECT
        location.Id as id,
        location.Name as name
        FROM location LEFT JOIN productlocation ON location.id=productlocation.location_id
        WHERE location.category=0 AND productlocation.id IS NULL
        AND location.id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateSelectiveByIdList"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationModifyDTO">
        update location
        <set>
            <if test="isChaosPut!=null">
                isChaosPut=#{isChaosPut,jdbcType=TINYINT},
            </if>
            <if test="isChaosBatch!=null">
                isChaosBatch=#{isChaosBatch,jdbcType=TINYINT},
            </if>
            <if test="locationCapacity!=null">
                locationCapacity=#{locationCapacity,jdbcType=TINYINT},
            </if>
            <if test="subcategory!=null">
                subcategory=#{subcategory,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateTime!=null">
                LastUpdateTime=#{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="locationGrade!=null">
                locationGrade=#{locationGrade,jdbcType=TINYINT},
            </if>
            <if test="state!=null">
                `State`=#{state,jdbcType=TINYINT},
            </if>
            <if test="palletCount != null">
                palletCount = #{palletCount,jdbcType=INTEGER},
            </if>
            <if test="aisleId != null">
                aisleId = #{aisleId,jdbcType=BIGINT},
            </if>
            <if test="aisleNo != null and aisleNo != ''">
                aisleNo = #{aisleNo,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            Warehouse_Id =#{warehouseId,jdbcType=INTEGER}
            and City_Id=#{cityId,jdbcType=INTEGER}
            <if test="idList!=null">
                and id IN
                <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
    <update id="updateSelectiveByNameWithWarehouseIdAndCityId"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationModifyDTO">
        update location
        <set>
            <if test="isChaosPut!=null">
                isChaosPut=#{isChaosPut,jdbcType=TINYINT},
            </if>
            <if test="isChaosBatch!=null">
                isChaosBatch=#{isChaosBatch,jdbcType=TINYINT},
            </if>
            <if test="locationCapacity!=null">
                locationCapacity=#{locationCapacity,jdbcType=TINYINT},
            </if>
            <if test="sequence!=null">
                sequence=#{sequence,jdbcType=INTEGER},
            </if>
            <if test="subcategory!=null">
                subcategory=#{subcategory,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateTime!=null">
                LastUpdateTime=#{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="locationGrade!=null">
                locationGrade=#{locationGrade,jdbcType=TINYINT},
            </if>
        </set>
        <where>
            category=0
            and Warehouse_Id =#{warehouseId,jdbcType=INTEGER}
            and City_Id=#{cityId,jdbcType=INTEGER}
            and name=#{name,jdbcType=VARCHAR}
        </where>
    </update>
    <select id="countLocationArea" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from location where category=1
        and id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findLocationListByIdAndCategory"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        select Id as id,
        Warehouse_Id as warehouseId,
        City_Id as cityId,
        Name as name,
        Area as area,
        Pallets as pallets,
        ProductLocation as productLocation,
        RoadWay as roadWay,
        Remo as remo,
        CreateTime as createTime,
        CreateUserId as createUserId,
        LastUpdateTime as lastUpdateTime,
        LastUpdateUserId as lastUpdateUserId,
        sequence as sequence,
        area_id as areaId,
        IsChaosPut as isChaosPut,
        IsChaosBatch as isChaosBatch,
        category as category,
        subcategory as subcategory,
        LocationCapacity as locationCapacity,
        locationGrade,
        state
        from location
        where City_Id = #{queryDTO.cityId,jdbcType=INTEGER}
        <if test="queryDTO.warehouseId != null ">
            and Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="queryDTO.locationId != null ">
            and area_id = (SELECT area_id FROM location WHERE Id = #{queryDTO.locationId,jdbcType=BIGINT})
        </if>
        <if test="queryDTO.subcategory != null ">
            and category = #{queryDTO.subcategory,jdbcType=TINYINT}
        </if>
        <if test="queryDTO.locSubcategory != null ">
            and subcategory = #{queryDTO.locSubcategory,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findLocationByIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        select Id as id,
        Warehouse_Id as warehouseId,
        City_Id as cityId,
        Name as name,
        Area as area,
        Pallets as pallets,
        ProductLocation as productLocation,
        RoadWay as roadWay,
        Remo as remo,
        CreateTime as createTime,
        CreateUserId as createUserId,
        LastUpdateTime as lastUpdateTime,
        LastUpdateUserId as lastUpdateUserId,
        sequence as sequence,
        area_id as areaId,
        IsChaosPut as isChaosPut,
        IsChaosBatch as isChaosBatch,
        category as category,
        subcategory as subcategory,
        LocationCapacity as locationCapacity,
        locationGrade,
        aisleId,
        aisleNo,
        Area_Id as area_Id,
        palletCount,
        BusinessType, State
        from location
        where id in
        <foreach collection="locationIds" item="locationId" open="(" separator="," close=")">
            #{locationId,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="findLocationAreaIdsByIds"
            resultType="Long">
        select distinct area_id
        from location
        where id in
        <foreach collection="locationIds" item="locationId" open="(" separator="," close=")">
            #{locationId,jdbcType=BIGINT}
        </foreach>
        and category = 0
    </select>

    <select id="pageListLocation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        select Id as id,
        Warehouse_Id as warehouseId,
        City_Id as cityId,
        Name as name,
        Area as area,
        Pallets as pallets,
        ProductLocation as productlocation,
        RoadWay as roadway,
        State as state,
        Remo as remo,
        CreateTime as createTime,
        CreateUserId as createUserId,
        LastUpdateTime as lastUpdateTime,
        LastUpdateUserId as lastUpdateUserId,
        sequence as sequence,
        area_id as area_Id,
        IsChaosPut as isChaosPut,
        IsChaosBatch as isChaosBatch,
        category as category,
        subcategory as subcategory,
        LocationCapacity as locationCapacity,
        locationGrade, width,height,coordinateX,coordinateY,layer,
        IsExpress as express,
        LastUpdateTime as lastUpdateTime,
        aisleId,
        aisleNo,
        palletCount,
        BusinessType
        from location
        where 1=1
        <if test="locationQuery.category != null">
            and category = #{locationQuery.category}
        </if>
        <if test="locationQuery.categoryList != null and locationQuery.categoryList.size() > 0">
            and category in
            <foreach collection="locationQuery.categoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.warehouseId != null">
            and Warehouse_Id = #{locationQuery.warehouseId}
        </if>
        <if test="locationQuery.cityId != null">
            and City_Id = #{locationQuery.cityId}
        </if>
        <if test="locationQuery.name != null and locationQuery.name != ''">
            and Name like
            concat('%',TRIM(#{locationQuery.name,jdbcType=VARCHAR}),'%')
        </if>
        <if test="locationQuery.area != null and locationQuery.area != ''">
            and Area = #{locationQuery.area}
        </if>
        <if test="locationQuery.isChaosPut != null ">
            and IsChaosPut = #{locationQuery.isChaosPut}
        </if>
        <if test="locationQuery.isChaosBatch != null ">
            and IsChaosBatch = #{locationQuery.isChaosBatch}
        </if>
        <if test="locationQuery.subcategory != null ">
            and subcategory = #{locationQuery.subcategory}
        </if>
        <if test="locationQuery.subcategoryList != null and locationQuery.subcategoryList.size() > 0">
            and subcategory in
            <foreach collection="locationQuery.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.roadway != null and locationQuery.roadway != ''">
            and RoadWay like
            concat(#{locationQuery.roadway,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationQuery.exactRoadway != null and locationQuery.exactRoadway != ''">
            and RoadWay = #{locationQuery.exactRoadway,jdbcType=VARCHAR}
        </if>
        <if test="locationQuery.sequenceStart != null">
            and sequence <![CDATA[ >= ]]> #{locationQuery.sequenceStart}
        </if>
        <if test="locationQuery.sequenceEnd != null">
            and sequence <![CDATA[ <= ]]> #{locationQuery.sequenceEnd}
        </if>
        <if test="locationQuery.excludeLocationIds != null and locationQuery.excludeLocationIds.size() > 0">
            and Id not in
            <foreach collection="locationQuery.excludeLocationIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.areaIds != null and locationQuery.areaIds.size() > 0">
            and area_Id in
            <foreach collection="locationQuery.areaIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.createTime != null">
            and CreateTime <![CDATA[ >= ]]> #{locationQuery.createTime}
        </if>
        <if test="locationQuery.lastUpdateTime != null">
            and LastUpdateTime <![CDATA[ >= ]]> #{locationQuery.lastUpdateTime}
        </if>
        <if test="locationQuery.state != null ">
            and `State` = #{locationQuery.state}
        </if>
        <if test="locationQuery.filterLocations!=null and locationQuery.filterLocations.size()!=0">
            and subcategory not in
            <foreach collection="locationQuery.filterLocations" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.locationName != null and locationQuery.locationName != ''">
            and Name = #{locationQuery.locationName}
        </if>
        <if test="locationQuery.aisleId != null">
            and aisleId = #{locationQuery.aisleId}
        </if>
        <if test="locationQuery.aisleNo != null and locationQuery.aisleNo != ''">
            and aisleNo = #{locationQuery.aisleNo}
        </if>
        <if test="locationQuery.businessType != null">
            and BusinessType = #{locationQuery.businessType, jdbcType=TINYINT}
        </if>
        ORDER BY Area asc,sequence asc,Id asc
    </select>

    <!-- 货位范围查询条件 -->
    <sql id="locationRangeCondition">
        <if test="locationRange.category != null">
            and category = #{locationRange.category}
        </if>
        <if test="locationRange.warehouseId != null">
            and Warehouse_Id = #{locationRange.warehouseId}
        </if>
        <if test="locationRange.cityId != null">
            and City_Id = #{locationRange.cityId}
        </if>
        <if test="locationRange.area != null and locationRange.area != ''">
            and Area = #{locationRange.area}
        </if>
        <if test="locationRange.isChaosPut != null ">
            and IsChaosPut = #{locationRange.isChaosPut}
        </if>
        <if test="locationRange.isChaosBatch != null ">
            and IsChaosBatch = #{locationRange.isChaosBatch}
        </if>
        <if test="locationRange.subcategory != null ">
            and subcategory = #{locationRange.subcategory}
        </if>
        <if test="locationRange.subcategoryList != null and locationRange.subcategoryList.size() > 0">
            and subcategory in
            <foreach collection="locationRange.subcategoryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationRange.roadway != null and locationRange.roadway != ''">
            and RoadWay like
            concat(#{locationRange.roadway,jdbcType=VARCHAR},'%')
        </if>
    </sql>

    <select id="findLocationRangeByCondition"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationRangeSequenceDTO">
        SELECT
        <if test="locationRange.locationNameStart != null and locationRange.locationNameStart != ''">
            MIN(sequence) as minSequence
        </if>
        <if test="locationRange.locationNameStart != null and locationRange.locationNameStart != '' and locationRange.locationNameEnd != null and locationRange.locationNameEnd != ''">
            ,
        </if>
        <if test="locationRange.locationNameEnd != null and locationRange.locationNameEnd != ''">
            MAX(sequence) as maxSequence
        </if>
        from (
        <if test="locationRange.locationNameStart != null and locationRange.locationNameStart != ''">
            SELECT
            MIN(sequence) as sequence
            from location
            where Name like
            concat('%',TRIM(#{locationRange.locationNameStart,jdbcType=VARCHAR}),'%')
            <include refid="locationRangeCondition">
            </include>
        </if>
        <if test="locationRange.locationNameStart != null and locationRange.locationNameStart != '' and locationRange.locationNameEnd != null and locationRange.locationNameEnd != ''">
            union ALL
        </if>
        <if test="locationRange.locationNameEnd != null and locationRange.locationNameEnd != ''">
            SELECT
            MAX(sequence) as sequence
            from location
            where Name like
            concat('%',TRIM(#{locationRange.locationNameEnd,jdbcType=VARCHAR}),'%')
            <include refid="locationRangeCondition">
            </include>
        </if>
        ) tmp
    </select>

    <update id="updateSelectiveById"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        update location
        <set>
            LastUpdateTime = now(),
            <if test="isChaosPut!=null">
                isChaosPut=#{isChaosPut,jdbcType=TINYINT},
            </if>
            <if test="isChaosBatch!=null">
                isChaosBatch=#{isChaosBatch,jdbcType=TINYINT},
            </if>
            <if test="locationCapacity!=null">
                locationCapacity=#{locationCapacity,jdbcType=TINYINT},
            </if>
            <if test="subcategory!=null">
                subcategory=#{subcategory,jdbcType=TINYINT},
            </if>
            <if test="locationGrade!=null">
                locationGrade=#{locationGrade,jdbcType=TINYINT},
            </if>
            <if test="name !=null and name!=''">
                Name=#{name,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area !=''">
                Area=#{area,jdbcType=VARCHAR},
            </if>
            <if test="roadway != null and roadway !='' ">
                RoadWay=#{roadway,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null and sequence !='' ">
                sequence=#{sequence,jdbcType=INTEGER},
            </if>
            <if test="pallets != null and pallets !=''">
                Pallets=#{pallets,jdbcType=VARCHAR},
            </if>
            <if test="null != userId">
                LastUpdateUserId=#{userId,jdbcType=INTEGER},
            </if>
            <if test="state!=null">
                State=#{state,jdbcType=TINYINT},
            </if>
            <if test="width!=null">
                Width=#{width,jdbcType=DECIMAL},
            </if>
            <if test="height !=null">
                Height=#{height,jdbcType=DECIMAL},
            </if>
            <if test="coordinateX !=null">
                CoordinateX=#{coordinateX,jdbcType=DECIMAL},
            </if>
            <if test="coordinateY !=null">
                CoordinateY=#{coordinateY,jdbcType=DECIMAL},
            </if>
            <if test="layer !=null">
                Layer=#{layer,jdbcType=DECIMAL},
            </if>
            <if test="userId !=null">
                LastUpdateUserId=#{userId,jdbcType=INTEGER},
            </if>
            <if test="area_Id !=null">
                area_Id = #{area_Id,jdbcType=INTEGER},
            </if>
            <if test="palletCount !=null">
                palletCount = #{palletCount,jdbcType=INTEGER},
            </if>
        </set>
        <where>
            id =#{id, jdbcType=BIGINT}
        </where>
    </update>

    <select id="pageListVesselLocation" resultMap="ResultMapVessel">
        select l.Id as id, l.Warehouse_Id as warehouseId, l.City_Id as cityId,
        max(l.Name) as name, max(l.ProductLocation) as productlocation, max(l.State) as State,
        max(l.IsChaosPut) as ischaosput, max(l.IsChaosBatch) as ischaosbatch, max(l.category) as category,
        max(l.subcategory) as subcategory,
        max(vi.id) as vesselId, max(vi.warehouseId) as vesselWarehouseId, max(vi.locationId) as vesselLocationId,
        max(vi.locationNo) as vesselLocationNo,
        max(vi.currentLocationId) as vesselCurrentLocationId, max(vi.currentLocationName) as vesselcCurrentLocationName,
        max(vi.isFreeze) as vesselIsFreeze,
        max(psku.NAME) AS productName, max(psku.specificationName) as specificationName, max(psku.unitName) as unitName,
        max(psku.packageName) as packageName, max(psku.packageQuantity) as packageQuantity,
        max(psku.ProductSku_Id) AS productSkuId, sum(psb.totalcount_minunit) as storeTotalCount, max(la.Name) as
        correspondLoactionName
        from location l
        LEFT JOIN location la ON la.Id = l.area_id
        LEFT JOIN vesselinfo vi ON vi.locationId = l.Id and vi.warehouseId = l.Warehouse_Id
        LEFT JOIN productstorebatch psb on l.Id = psb.location_id
        LEFT JOIN productstore ps on psb.productstore_id = ps.id and l.warehouse_id = ps.warehouse_id
        LEFT JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where 1=1
        <if test="locationQuery.category != null">
            and l.category = #{locationQuery.category}
        </if>
        <if test="locationQuery.warehouseId != null">
            and l.Warehouse_Id = #{locationQuery.warehouseId}
        </if>
        <if test="locationQuery.cityId != null">
            and l.City_Id = #{locationQuery.cityId}
        </if>
        <choose>
            <when test="locationQuery.useVague != null and locationQuery.useVague == true">
                <if test="locationQuery.vesselLocationNo != null and locationQuery.vesselLocationNo != ''">
                    and l.Name like
                    concat('%',TRIM(#{locationQuery.vesselLocationNo,jdbcType=VARCHAR}),'%')
                </if>
                <if test="locationQuery.name != null and locationQuery.name != ''">
                    and la.Name like
                    concat('%',TRIM(#{locationQuery.name,jdbcType=VARCHAR}),'%')
                </if>
            </when>
            <otherwise>
                <if test="locationQuery.vesselLocationNo != null and locationQuery.vesselLocationNo != ''">
                    and l.Name = #{locationQuery.vesselLocationNo,jdbcType=VARCHAR}
                </if>
                <if test="locationQuery.name != null and locationQuery.name != ''">
                    and la.Name = #{locationQuery.name,jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
        <!--        <if test="locationQuery.subcategory != null ">-->
        <!--            and l.subcategory = #{locationQuery.subcategory}-->
        <!--        </if>-->
        <if test="locationQuery.state != null ">
            and l.State = #{locationQuery.state}
        </if>
        <if test="locationQuery.productName != null and locationQuery.productName != ''">
            and psku.Name like
            concat('%',TRIM(#{locationQuery.productName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="locationQuery.freezeState != null ">
            and vi.isFreeze = #{locationQuery.freezeState}
        </if>
        <if test="locationQuery.hasRealStore != null and locationQuery.hasRealStore == 0">
            and (psb.totalcount_minunit <![CDATA[ <= ]]> 0 or psb.totalcount_minunit is null)
        </if>
        <if test="locationQuery.hasRealStore != null and locationQuery.hasRealStore == 1">
            and psb.totalcount_minunit <![CDATA[ > ]]> 0
        </if>
        <if test="locationQuery.skuIds != null and locationQuery.skuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach collection="locationQuery.skuIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationQuery.vesselLocationIds != null and locationQuery.vesselLocationIds.size() > 0">
            and l.Id in
            <foreach collection="locationQuery.vesselLocationIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by l.Id, l.City_Id, l.Warehouse_Id
        ORDER BY l.Id
    </select>

    <select id="selectVesselByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location
        where name = #{name,jdbcType=VARCHAR}
        and warehouse_id = #{warehouseId,jdbcType=INTEGER}
        and category = 2
        <if test="cityId != null">
            and City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listVesselByAreaIds" resultMap="BaseResultMap">
        select
        loct.Id as Id, loct.Warehouse_Id as Warehouse_Id, loct.City_Id as City_Id, loct.Name as Name, loct.area_id as
        Area_Id,
        loct.Remo as Remo, loct.CreateTime as CreateTime,
        loct.CreateUserId as CreateUserId, loct.LastUpdateTime as LastUpdateTime, loct.LastUpdateUserId as
        LastUpdateUserId, loct.State as State,
        loct1.Area as Area
        from location loct
        inner join location loct1 ON loct.area_Id = loct1.id
        WHERE loct.Warehouse_Id = #{warehouseId}
        and loct.category = 2
        and loct.area_Id in
        <foreach collection="areaIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listSKUBesselDetails"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselDetailsDTO">
        select la.Id as locationId, la.Name as locationName, la.area_id as areaId, la.Name areaName, pl.ProductSku_Id as
        skuId
        from sc_product.location l
        INNER JOIN productlocation pl on pl.location_id = l.Id
        INNER JOIN location la ON la.area_id = l.id
        where la.category = 2
        and l.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and pl.ProductSku_Id in
        <foreach collection="skuIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="pageListProductCountList" resultMap="BaseResultMap">
        SELECT
        loct.area_Id AS Area_Id,loct.ProductLocation
        FROM
        location AS loct
        WHERE
        loct.area_Id IN
        <foreach collection="locationQueryDTO.locationAreaId" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and category=0
    </select>
    <select id="pageListProductLocationBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDetailDTO">
        select a.id,a.name,a.city_Id as cityId,a.warehouse_Id as warehouseId,
        a.area as area,a.roadway,a.productlocation,a.pallets,b.productSku_Id as productSkuId,
        a.category,a.subcategory,a.locationCapacity,a.locationGrade
        from
        location a
        inner join productlocation b
        on a.Id =
        b.location_Id and a.category=0
        <if test="dto.warehouseId != null">
            and a.warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="dto.cityId != null">
            and a.city_Id = #{dto.cityId,jdbcType=INTEGER}
        </if>
        <if test="dto.skuIdList != null and dto.skuIdList.size()>0">
            and b.ProductSku_Id in
            <foreach item="item" index="index" collection="dto.skuIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="dto.productLocation != null and dto.productLocation != ''">
            and a.Name LIKE CONCAT('%', #{dto.productLocation,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="findVesselByCondition"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO">
        select Id as id,
        Warehouse_Id as warehouseId,
        City_Id as cityId,
        Name as name,
        Area as area,
        Pallets as pallets,
        ProductLocation as productLocation,
        RoadWay as roadWay,
        Remo as remo,
        CreateTime as createTime,
        CreateUserId as createUserId,
        LastUpdateTime as lastUpdateTime,
        LastUpdateUserId as lastUpdateUserId,
        sequence as sequence,
        area_id as areaId,
        IsChaosPut as isChaosPut,
        IsChaosBatch as isChaosBatch,
        category as category,
        subcategory as subcategory,
        LocationCapacity as locationCapacity,
        locationGrade
        from location
        where City_Id = #{queryDTO.cityId,jdbcType=INTEGER}
        <if test="queryDTO.warehouseId != null ">
            and Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="queryDTO.locationId != null ">
            and Id = #{queryDTO.locationId,jdbcType=BIGINT}
        </if>
        <if test="queryDTO.category != null ">
            and category = #{queryDTO.category,jdbcType=TINYINT}
        </if>
        <if test="queryDTO.locationIdList != null and queryDTO.locationIdList.size() > 0">
            and id in
            <foreach collection="queryDTO.locationIdList" item="locationId" open="(" separator="," close=")">
                #{locationId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="selectLocationByAreaIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        select Id as id, Name as name, area_Id
        FROM location
        WHERE area_Id in
        <foreach collection="areaIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and category = 2
    </select>


    <select id="findLocationAreaListExcludeDefective" resultMap="BaseResultMap">
        select loc.id
        ,loc.Name
        ,ifnull(area.id, loc.id) as area_Id
        ,ifnull(area.name, loc.Name) as Area
        ,loc.Warehouse_Id
        ,loc.City_Id
        ,loc.category
        ,area.subcategory
        ,loc.subcategory as locationSubcategory
        from location loc
        left join location area on area.Id = loc.area_id
        where loc.Id in
        <foreach collection="idList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and loc.subcategory != 29 and area.subcategory != 60
    </select>

    <select id="pageListLocationByCondition" resultMap="BaseResultMap">
        <if test="dto.category == 0">
            select locL.Id, locL.Warehouse_Id, locL.City_Id, locL.Name, locL.Area, locL.area_Id,locL.sequence, locL.Pallets,
            locL.ProductLocation, locL.RoadWay,
            locL.Remo, locL.CreateTime,
            locL.CreateUserId, locL.LastUpdateTime,
            locL.LastUpdateUserId,locL.category,locL.subcategory,locL.ischaosbatch,locL.ischaosput,locL.locationCapacity,locL.locationGrade,locL.IsExpress,
            locL.palletCount
            from location locL
            inner join location locArea on locL.area_id = locArea.id
            where locL.category = 0
            <if test="dto.subcategory!=null">
                and locArea.subcategory=#{dto.subcategory}
            </if>
            <if test="dto.subcategoryList!=null and dto.subcategoryList.size>0">
                and locArea.subcategory in
                <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.warehouseId!=null">
                and locL.Warehouse_Id=#{dto.warehouseId}
            </if>
            <if test="dto.cityId!=null">
                and locL.City_Id=#{dto.cityId}
            </if>
            <if test="dto.areaState != null">
                and locArea.State = #{dto.areaState}
            </if>
            <if test="dto.express!=null">
                and locL.IsExpress=#{dto.express}
            </if>
            and locArea.Id > #{dto.lastMaxId,jdbcType=BIGINT}
            order by locArea.Id limit #{dto.batchCount,jdbcType=INTEGER}
        </if>
        <if test="dto.category == null">
            ; -- 不允许出现
        </if>
        <if test="dto.category == 1">
            select locArea.Id, locArea.Warehouse_Id, locArea.City_Id, locArea.Name, locArea.Name as Area, locArea.Id as
            area_Id,locArea.sequence, locArea.Pallets, locArea.ProductLocation, locArea.RoadWay,
            locArea.Remo, locArea.CreateTime,
            locArea.CreateUserId, locArea.LastUpdateTime,
            locArea.LastUpdateUserId,locArea.category,locArea.subcategory,locArea.ischaosbatch,locArea.ischaosput,locArea.locationCapacity,locArea.locationGrade,locArea.IsExpress,
            locArea.palletCount
            from location locArea
            where locArea.category = 1
            <if test="dto.subcategory!=null">
                and locArea.subcategory=#{dto.subcategory}
            </if>
            <if test="dto.subcategoryList!=null and dto.subcategoryList.size>0">
                and locArea.subcategory in
                <foreach collection="dto.subcategoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.warehouseId!=null">
                and locArea.Warehouse_Id=#{dto.warehouseId}
            </if>
            <if test="dto.cityId!=null">
                and locArea.City_Id=#{dto.cityId}
            </if>
            <if test="dto.areaState != null">
                and locArea.State = #{dto.areaState}
            </if>
            <if test="dto.express!=null">
                and locArea.IsExpress=#{dto.express}
            </if>
            and locArea.Id > #{dto.lastMaxId,jdbcType=BIGINT}
            order by locArea.Id limit #{dto.batchCount,jdbcType=INTEGER}
        </if>
    </select>

    <select id="pageListRoadway" resultMap="BaseResultMap">
        SELECT DISTINCT (loc.RoadWay)
        from location loc
        <if test="hasLocation != null and hasLocation">
            inner join location area on area.id = loc.area_id
        </if>
        <where>
            loc.City_Id = #{cityId,jdbcType=INTEGER}
            and loc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            and loc.area_id = #{locationAreaId,jdbcType=BIGINT}
            and loc.RoadWay is not null
            <if test="status != null">
                and loc.State = #{status,jdbcType=TINYINT}
            </if>
            <if test="subCategoryList != null and subCategoryList.size() != 0">
                and loc.subcategory in
                <foreach collection="subCategoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="onlyEmptyLocation != null and onlyEmptyLocation">
                and not exists(SELECT ps.TotalCount_MinUnit as unitTotolCount
                FROM productstore ps
                INNER JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
                AND ps.City_Id = psku.City_Id
                AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
                AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
                INNER JOIN productstorebatch psb on psb.productstore_id = ps.id
                LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
                LEFT JOIN Location l on l.id = psb.location_id and l.warehouse_id = ps.warehouse_id
                LEFT JOIN location a on l.area_id = a.id
                where l.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
                AND l.City_Id = #{cityId,jdbcType=INTEGER}
                AND ps.totalcount_minunit != 0
                AND psb.totalcount_minunit != 0
                and l.Id = loc.Id)
            </if>
        </where>
    </select>

    <select id="pageListLocationByAreaAndRoad" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from location
        <where>
            City_Id = #{cityId,jdbcType=INTEGER}
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            and RoadWay = #{roadWayName,jdbcType=VARCHAR}
            and area_id = #{locationAreaId,jdbcType=BIGINT}
            and category = 0
            <if test="status != null">
                and State = #{status,jdbcType=TINYINT}
            </if>
            <if test="subCategoryList != null and subCategoryList.size() != 0">
                and subcategory in
                <foreach collection="subCategoryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="pageListLocationByCond" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location where id in (
        SELECT distinct (area.id) as Id
        FROM location area
        <if test="dto.hasLocation != null and dto.hasLocation">
            inner join location loc on area.id = loc.area_id
        </if>
        <where>
            <if test="dto.categoryList == null or dto.categoryList.size() == 0">
                and area.category = 0
            </if>
            <if test="dto.categoryList != null and dto.categoryList.size() != 0">
                and area.category in
                <foreach collection="dto.categoryList" item="category" open="(" close=")" separator=",">
                    #{category,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.cityId != null">
                and area.City_Id = #{dto.cityId,jdbcType=INTEGER}
            </if>
            <if test="dto.area != null and dto.area != ''">
                and area.Area = #{dto.area,jdbcType=VARCHAR}
            </if>
            <if test="dto.warehouseId != null">
                AND area.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.subcategoryList != null and dto.subcategoryList.size() != 0">
                and area.subcategory in
                <foreach collection="dto.subcategoryList" item="subcategory" open="(" close=")" separator=",">
                    #{subcategory,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="dto.locationIdList != null and dto.locationIdList.size() != 0">
                and area.Id in
                <foreach collection="dto.locationIdList" item="locationId" open="(" close=")" separator=",">
                    #{locationId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.areaIdList != null and dto.areaIdList.size() != 0">
                and area.area_Id in
                <foreach collection="dto.areaIdList" item="areaId" open="(" close=")" separator=",">
                    #{areaId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.state != null">
                and area.State = #{dto.state}
            </if>
            <if test="dto.name != null">
                and area.Name = #{dto.name}
            </if>
            <if test="dto.hasLocation != null and dto.hasLocation">
                and loc.RoadWay is not null
                <if test="dto.locSubCategoryList != null">
                    and loc.subcategory in
                    <foreach collection="dto.locSubCategoryList" item="locSubCategory" open="(" close=")" separator=",">
                        #{locSubCategory,jdbcType=TINYINT}
                    </foreach>
                </if>
            </if>
            <if test="dto.onlyEmptyLocation != null and dto.onlyEmptyLocation and dto.hasLocation != null and dto.hasLocation">
                and not exists(SELECT ps.TotalCount_MinUnit as unitTotolCount
                FROM productstore ps
                INNER JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
                AND ps.City_Id = psku.City_Id
                AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
                AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
                INNER JOIN productstorebatch psb on psb.productstore_id = ps.id
                LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
                LEFT JOIN Location l on l.id = psb.location_id and l.warehouse_id = ps.warehouse_id
                LEFT JOIN location a on l.area_id = a.id
                where l.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
                AND l.City_Id = #{dto.cityId,jdbcType=INTEGER}
                AND ps.totalcount_minunit != 0
                AND psb.totalcount_minunit != 0
                and l.Id = loc.Id)
            </if>
        </where>
        )
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location
        where Id in
        <foreach collection="collection" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectByAreaInfo" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        select Id as id, Name as name
        FROM location
        WHERE
        area_Id in
        <foreach collection="areaIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and category = #{category}
        AND warehouse_Id = #{warehouseId}
    </select>

    <select id="selectLocationIds" resultType="java.lang.Long">
        select Id
        FROM location
        WHERE category = #{category}
        AND warehouse_Id = #{warehouseId}
        AND state = 1
    </select>


    <update id="updateLocationBatchById">
        update location set area_id = #{areaId,jdbcType=BIGINT}
        where id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="clearAisleNoBatchById">
        update location
        set aisleId = null,aisleNo = null
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findLocationByAreaIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from location where area_id in
        <foreach collection="areaIds" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        and category = 0
    </select>

    <update id="updateLocationBusinessTypeById">
        update location set
        <if test="businessType != null and businessType == 0">
            BusinessType = null
        </if>
        <if test="businessType != null and businessType != 0">
            BusinessType = #{businessType}
        </if>
        where id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>
