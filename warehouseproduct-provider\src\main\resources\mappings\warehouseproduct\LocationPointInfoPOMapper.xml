<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPointInfoPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        <!--@Table locationpointinfo-->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="LocationId" jdbcType="BIGINT" property="locationId"/>
        <result column="X" jdbcType="INTEGER" property="x"/>
        <result column="Y" jdbcType="INTEGER" property="y"/>
        <result column="Z" jdbcType="INTEGER" property="z"/>
        <result column="Floor" jdbcType="INTEGER" property="floor"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="INTEGER" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="INTEGER" property="lastUpdateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        Id, WarehouseId, LocationId, X, Y, Z, `Floor`, Remark, CreateTime, `CreateUser`,
        LastUpdateTime, LastUpdateUser
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from locationpointinfo
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from locationpointinfo
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        insert into locationpointinfo (Id, WarehouseId, LocationId,
        X, Y, Z, `Floor`,
        Remark, CreateTime, `CreateUser`,
        LastUpdateTime, LastUpdateUser)
        values (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{locationId,jdbcType=BIGINT},
        #{x,jdbcType=INTEGER}, #{y,jdbcType=INTEGER}, #{z,jdbcType=INTEGER}, #{floor,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        insert into locationpointinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="locationId != null">
                LocationId,
            </if>
            <if test="x != null">
                X,
            </if>
            <if test="y != null">
                Y,
            </if>
            <if test="z != null">
                Z,
            </if>
            <if test="floor != null">
                `Floor`,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="x != null">
                #{x,jdbcType=INTEGER},
            </if>
            <if test="y != null">
                #{y,jdbcType=INTEGER},
            </if>
            <if test="z != null">
                #{z,jdbcType=INTEGER},
            </if>
            <if test="floor != null">
                #{floor,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        update locationpointinfo
        <set>
            <if test="locationId != null">
                LocationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="x != null">
                X = #{x,jdbcType=INTEGER},
            </if>
            <if test="y != null">
                Y = #{y,jdbcType=INTEGER},
            </if>
            <if test="z != null">
                Z = #{z,jdbcType=INTEGER},
            </if>
            <if test="floor != null">
                `Floor` = #{floor,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
                LastUpdateTime = now()
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        update locationpointinfo
        set WarehouseId = #{warehouseId,jdbcType=INTEGER},
        LocationId = #{locationId,jdbcType=BIGINT},
        X = #{x,jdbcType=INTEGER},
        Y = #{y,jdbcType=INTEGER},
        Z = #{z,jdbcType=INTEGER},
        `Floor` = #{floor,jdbcType=INTEGER},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update locationpointinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LocationId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.locationId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="X = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.x,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="Y = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.y,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="Z = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.z,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`Floor` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.floor,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="Remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update locationpointinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LocationId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.locationId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.locationId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="X = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.x != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.x,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Y = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.y != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.y,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Z = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.z != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.z,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`Floor` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.floor != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.floor,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into locationpointinfo
        (Id, WarehouseId, LocationId, X, Y, Z, `Floor`, Remark, CreateTime, `CreateUser`,
        LastUpdateTime, LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.locationId,jdbcType=BIGINT},
            #{item.x,jdbcType=INTEGER}, #{item.y,jdbcType=INTEGER}, #{item.z,jdbcType=INTEGER},
            #{item.floor,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="map">
        <!--@mbg.generated-->
        insert into locationpointinfo
        (Id, WarehouseId, LocationId, X, Y, Z, `Floor`, Remark, CreateTime, `CreateUser`,
        LastUpdateTime, LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.locationId,jdbcType=BIGINT},
            #{item.x,jdbcType=INTEGER}, #{item.y,jdbcType=INTEGER}, #{item.z,jdbcType=INTEGER},
            #{item.floor,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=INTEGER})
        </foreach>
        on duplicate key update
        Id=values(Id),
        WarehouseId=values(WarehouseId),
        LocationId=values(LocationId),
        X=values(X),
        Y=values(Y),
        Z=values(Z),
        Floor=values(Floor),
        Remark=values(Remark),
        CreateTime=values(CreateTime),
        CreateUser=values(CreateUser),
        LastUpdateTime=values(LastUpdateTime),
        LastUpdateUser=values(LastUpdateUser)
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from locationpointinfo where Id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <insert id="insertOrUpdate"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        insert into locationpointinfo
        (Id, WarehouseId, LocationId, X, Y, Z, `Floor`, Remark, CreateTime, `CreateUser`,
        LastUpdateTime, LastUpdateUser)
        values
        (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{locationId,jdbcType=BIGINT},
        #{x,jdbcType=INTEGER}, #{y,jdbcType=INTEGER}, #{z,jdbcType=INTEGER}, #{floor,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=INTEGER})
        on duplicate key update
        Id = #{id,jdbcType=BIGINT},
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
        LocationId = #{locationId,jdbcType=BIGINT},
        X = #{x,jdbcType=INTEGER},
        Y = #{y,jdbcType=INTEGER},
        Z = #{z,jdbcType=INTEGER},
        `Floor` = #{floor,jdbcType=INTEGER},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPointInfoPO">
        <!--@mbg.generated-->
        insert into locationpointinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="locationId != null">
                LocationId,
            </if>
            <if test="x != null">
                X,
            </if>
            <if test="y != null">
                Y,
            </if>
            <if test="z != null">
                Z,
            </if>
            <if test="floor != null">
                `Floor`,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="x != null">
                #{x,jdbcType=INTEGER},
            </if>
            <if test="y != null">
                #{y,jdbcType=INTEGER},
            </if>
            <if test="z != null">
                #{z,jdbcType=INTEGER},
            </if>
            <if test="floor != null">
                #{floor,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="locationId != null">
                LocationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="x != null">
                X = #{x,jdbcType=INTEGER},
            </if>
            <if test="y != null">
                Y = #{y,jdbcType=INTEGER},
            </if>
            <if test="z != null">
                Z = #{z,jdbcType=INTEGER},
            </if>
            <if test="floor != null">
                `Floor` = #{floor,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                `CreateUser` = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>