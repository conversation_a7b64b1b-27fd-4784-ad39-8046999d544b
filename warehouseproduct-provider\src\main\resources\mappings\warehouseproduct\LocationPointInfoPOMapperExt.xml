<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationPointInfoPOMapper">
    <delete id="deleteByWarehouseId">
        delete
        from locationpointinfo where WarehouseId = #{warehouseId,jdbcType=INTEGER}
    </delete>

    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from locationpointinfo where WarehouseId = #{warehouseId,jdbcType=INTEGER}
    </select>


    <select id="findDisableLocationPoint" resultType="Long">
        select t.id from locationpointinfo t
        inner join location l on l.id = t.LocationId
        where t.WarehouseId = #{warehouseId,jdbcType=INTEGER}
          and l.State = 0
          and l.area_id in
        <foreach close=")" collection="areaIds" item="areaId" open="(" separator=", ">
            #{areaId,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="findLocationAreaIds"
            resultType="Long">
        select distinct l.area_id
        from locationpointinfo t
        inner join location l on l.id = t.LocationId
        where t.WarehouseId = #{warehouseId,jdbcType=INTEGER}
        and category = 0
    </select>


    <select id="findBindLocationIdByAreaId" resultMap="BaseResultMap">
        select
        t.Id,t.WarehouseId, t.LocationId, t.X, t.Y, t.Z, t.Floor
        from locationpointinfo t
        inner join location l on l.id = t.LocationId
        where t.WarehouseId = #{warehouseId,jdbcType=INTEGER}
        and l.area_id in
        <foreach close=")" collection="areaIds" item="areaId" open="(" separator=", ">
            #{areaId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findBindLocationAreaByWarehouseId" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationPO">
        select
            distinct area.Id ,area.Name
        from locationpointinfo t
        inner join location l on l.Id = t.LocationId
        inner join location area on l.area_id = area.Id
        where t.WarehouseId = #{warehouseId,jdbcType=INTEGER}
    </select>
</mapper>