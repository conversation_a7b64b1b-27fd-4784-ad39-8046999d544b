<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper">
    <insert id="insert">
        insert into locationRule (
        id,
        warehouseId,
        ruleType,
        ruleName,
        ruleId,
        locationId,
        locationName,
        createUserId,
        createTime,
        lastUpdateUserId,
        lastUpdateTime,
        extInfo)
        values
        <foreach collection="locationRulePOList" item="item" index="index" separator=",">
            (#{item.id},
            #{item.warehouseId},
            #{item.ruleType},
            #{item.ruleName},
            #{item.ruleId},
            #{item.locationId},
            #{item.locationName},
            #{item.createUserId},
            now(),
            #{item.lastUpdateUserId},
            now(),
            #{item.extInfo}
            )
        </foreach>
    </insert>
    <delete id="delete">
        delete from locationRule where locationId in
        <foreach collection="collect" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and warehouseId=#{warehouseId}
    </delete>

    <select id="listLocationRule"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select * from locationRule where

        <foreach collection="locationRuleDTOList" item="item" separator="or">
            (
            locationId = #{item.locationId,jdbcType=VARCHAR} and ruleType = #{item.ruleType,jdbcType=INTEGER}
            )
        </foreach>
        and warehouseId=#{warehouseId}
    </select>
    <select id="getLocationByName"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select *
        from locationRule
        where warehouseId = #{warehouseId}
        and ruleType = 5
        and ruleName = #{joinRegionName}
        limit 1
    </select>
    <select id="queryByLocationIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select * from locationRule where
        locationId in
        <foreach collection="collect" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and warehouseId=#{warehouseId}
        and ruleType=#{ruleType}
        order by locationName asc
    </select>
    <select id="listLocationByRuleId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select * from locationRule where
        ruleId in
        <foreach collection="collect" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and warehouseId=#{warehouseId}
        and ruleType=#{ruleType}
    </select>
    <delete id="deleteByIds">
        delete from locationRule where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getLocationsByWarehouseAndRuleType"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select *
        from locationRule
        where warehouseId = #{warehouseId}
        <if test="ruleType != null">
            and ruleType = #{ruleType}
        </if>
    </select>


</mapper>