<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.LocationRuleMapper">

    <select id="listRuleInfoByLocationId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.LocationRulePO">
        select * from locationRule where
        locationId in
        <foreach collection="locationIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and warehouseId=#{warehouseId}
        <if test="ruleType != null">
            and ruleType=#{ruleType}
        </if>

    </select>


</mapper>