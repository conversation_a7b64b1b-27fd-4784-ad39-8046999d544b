<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OrderFixRecordPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderfixrecord.OrderFixRecordDTO">
        <id column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
    </resultMap>

    <insert id="batchInsert">
        insert into orderfixrecord (OrderNo)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.orderNo,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="findAll" resultMap="BaseResultMap">
        select OrderNo from orderFixRecord
    </select>

</mapper>