<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OrderLocationPalletMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="orgId" jdbcType="INTEGER" property="orgId"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="palletNo" jdbcType="VARCHAR" property="palletNo"/>
        <result column="locationId" jdbcType="BIGINT" property="locationId"/>
        <result column="locationName" jdbcType="VARCHAR" property="locationName"/>
        <result column="orderId" jdbcType="BIGINT" property="orderId"/>
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="batchTaskId" jdbcType="VARCHAR" property="batchTaskId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
                , orgId, warehouseId, palletNo, locationId, locationName, orderId, orderNo, batchTaskId, remark,
        createUser, createTime,lastUpdateUser, lastUpdateTime
    </sql>
    <insert id="insertSelectiveBatch" parameterType="java.util.List">
        insert into orderlocationpallet (
        id,
        orgId,
        warehouseId,
        palletNo,
        locationId,
        locationName,
        orderId,
        orderNo,
        batchTaskId,
        remark,
        createUser,
        createTime,
        lastUpdateUser,
        lastUpdateTime
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId},
            #{item.warehouseId},
            #{item.palletNo,jdbcType=VARCHAR},
            #{item.locationId,jdbcType=BIGINT},
            #{item.locationName,jdbcType=VARCHAR},
            #{item.orderId,jdbcType=BIGINT},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.batchTaskId,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=VARCHAR},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO">
        update orderlocationpallet
        <set>
            <if test="orgId != null">
                orgId = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                warehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="palletNo != null and palletNo != ''">
                palletNo = #{palletNo,jdbcType=VARCHAR},
            </if>
            <if test="locationId != null">
                locationId = #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationName != null and locationName != ''">
                locationName = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                orderId = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null and orderNo != ''">
                orderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="batchTaskId != null and batchTaskId != ''">
                batchTaskId = #{batchTaskId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                `createUser` = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null and lastUpdateUser != ''">
                lastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBatchSelective" parameterType="java.util.List">
        update orderlocationpallet
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="orgId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orgId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="warehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="palletNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.palletNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.palletNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="locationId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.locationId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.locationId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="locationName = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.locationName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.locationName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="orderId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.orderId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="orderNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.orderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="batchTaskId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.batchTaskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.batchTaskId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`createUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUser != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="createTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUser != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderlocationpallet
        where id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByConditions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderlocationpallet
        where warehouseId = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.orgId != null">
            and orgId = #{query.orgId, jdbcType=INTEGER}
        </if>
        <if test="query.orderNo != null">
            and orderNo = #{query.orderNo, jdbcType=VARCHAR}
        </if>
        <if test="query.orderId != null">
            and orderId = #{query.orderId, jdbcType=BIGINT}
        </if>
        <if test="query.batchTaskId != null">
            and batchTaskId = #{query.batchTaskId, jdbcType=BIGINT}
        </if>
        <if test="query.locationId != null">
            and locationId = #{query.locationId, jdbcType=BIGINT}
        </if>
        <if test="query.orderIdList != null and query.orderIdList.size() > 0">
            and orderId IN
            <foreach collection="query.orderIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.locationIdList != null and query.locationIdList.size() > 0">
            and locationId IN
            <foreach collection="query.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.batchTaskIdList != null and query.batchTaskIdList.size() > 0">
            and batchTaskId IN
            <foreach collection="query.batchTaskIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.palletNoList != null and query.palletNoList.size() > 0">
            and palletNo IN
            <foreach collection="query.palletNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <delete id="deleteByPrimaryKeyBatch" parameterType="java.lang.Long">
        delete from orderlocationpallet
        where Id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderlocationpallet where orderId in
        <foreach collection="orderIds" item="item" open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectByBatchTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orderlocationpallet where batchTaskId = #{batchTaskId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByBatchTaskId">
        delete from orderlocationpallet where batchTaskId = #{batchTaskId,jdbcType=VARCHAR}
    </delete>
</mapper>
