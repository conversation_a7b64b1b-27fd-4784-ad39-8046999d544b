<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.OwnerMapper">


    <resultMap id="ownerMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO">
        <result column="owner_id" property="id" jdbcType="BIGINT"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>
        <result column="mobileNo" property="mobileNo" jdbcType="VARCHAR"/>
        <result column="userName" property="userName" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="county" property="county" jdbcType="VARCHAR"/>
        <result column="street" property="street" jdbcType="VARCHAR"/>
        <result column="Ref_Partner_Id" property="refPartnerId" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="OwnerType" jdbcType="TINYINT" property="ownerType"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="Street" jdbcType="VARCHAR" property="street"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="MobileNo" jdbcType="VARCHAR" property="mobileNo"/>
        <result column="UserName" jdbcType="VARCHAR" property="userName"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="OwnerNo" jdbcType="VARCHAR" property="ownerNo"/>
        <result column="IsDefault" jdbcType="TINYINT" property="isDefault"/>
        <result column="CreateUser_Id" jdbcType="BIGINT" property="createUserId"/>
        <result column="LastUpdateUser_Id" jdbcType="BIGINT" property="lastUpdateUserId"/>
        <result column="Ref_Partner_Id" jdbcType="VARCHAR" property="refPartnerId"/>
        <result column="Company_Id" jdbcType="BIGINT" property="companyId"/>
        <result column="IsReturn" jdbcType="TINYINT" property="isReturn"/>
    </resultMap>

    <resultMap id="PageResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO"
               extends="BaseResultMap">
    </resultMap>

    <sql id="Base_Column_List">
        Id, City_Id, OwnerType, OwnerName, State, Province, City, County, Street, Address,
        MobileNo, UserName, CreateTime, LastUpdateTime,OwnerNo,IsDefault, CreateUser_Id,
        LastUpdateUser_Id, Ref_Partner_Id, Company_Id, IsReturn
    </sql>

    <!--根据仓库Id获取所有在本仓库存放货物的货主-->
    <select id="getOwnerInfoDTO" resultMap="ownerMap">
        select distinct ps.owner_id,o.OwnerName,o.mobileNo,o.userName,o.province,o.city,o.county,o.street
        from productstore ps
        inner join owner o on o.id = ps.owner_id
        where ps.owner_id is not null and ps.warehouse_id = #{dto.warehouseId,jdbcType=INTEGER}
    </select>
    <select id="getOwnerIdByErpOwnerId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO">
        select id,Ref_Partner_Id as refPartnerId
        from owner where Ref_Partner_Id in
        <foreach collection="erpOwnerId" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from owner
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listOwnerByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from owner
        where Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findOwnerByOrgId" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        select
        <include refid="Base_Column_List"/>
        from owner
        where City_Id = #{cityId,jdbcType=INTEGER}
    </select>

    <select id="findOwnerByWarehouseId" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        select DISTINCT own.Id, own.City_Id, own.OwnerType, own.OwnerName, own.State, own.Province, own.City,
        own.County, own.Street, own.Address,
        own.MobileNo, own.UserName, own.CreateTime, own.LastUpdateTime,own.IsDefault, own.CreateUser_Id,
        own.LastUpdateUser_Id, own.Ref_Partner_Id
        from `owner` own
        INNER JOIN productstore ps
        on ps.owner_id is not null
        and (ps.owner_id = own.id or (ps.secOwner_id is not null and ps.secOwner_id = own.id))
        where ps.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerType!=null">
            AND own.OwnerType = #{ownerType,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findOwnerByOrgIdAndType" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        select
        <include refid="Base_Column_List"/>
        from owner
        where City_Id = #{cityId,jdbcType=INTEGER}
        <if test="ownerType!=null">
            AND OwnerType= #{ownerType,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findOwnerByOrgIdAndTypeList"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        select
        <include refid="Base_Column_List"/>
        from owner
        where City_Id = #{cityId,jdbcType=INTEGER}
        <if test="list != null and list.size() > 0">
            AND OwnerType in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        insert into owner(
        Id,
        City_Id,
        OwnerType,
        OwnerName,
        State,
        Province,
        City,
        County,
        Street,
        Address,
        MobileNo,
        UserName,
        OwnerNo,
        IsDefault,
        Ref_Partner_Id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.ownerType,jdbcType=TINYINT},
            #{item.ownerName,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR},
            #{item.county,jdbcType=VARCHAR},
            #{item.street,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.userName,jdbcType=VARCHAR},
            #{item.ownerNo,jdbcType=VARCHAR},
            #{item.isDefault,jdbcType=TINYINT},
            #{item.refPartnerId,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        City_Id = VALUES(City_Id),
        OwnerType = VALUES(OwnerType),
        OwnerName = VALUES(OwnerName),
        State = VALUES(State),
        Province = VALUES(Province),
        City = VALUES(City),
        County = VALUES(County),
        Street = VALUES(Street),
        Address = VALUES(Address),
        MobileNo = VALUES(MobileNo),
        UserName = VALUES(UserName),
        OwnerNo = VALUES(OwnerNo),
        IsDefault = VALUES(IsDefault),
        Ref_Partner_Id = VALUES(Ref_Partner_Id)
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into owner(
        Id,
        City_Id,
        OwnerType,
        OwnerName,
        State,
        Province,
        City,
        County,
        Street,
        Address,
        MobileNo,
        UserName,
        OwnerNo,
        IsDefault,
        CreateUser_Id,
        LastUpdateUser_Id,
        Ref_Partner_Id,
        Company_Id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.ownerType,jdbcType=TINYINT},
            #{item.ownerName,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR},
            #{item.county,jdbcType=VARCHAR},
            #{item.street,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.userName,jdbcType=VARCHAR},
            #{item.ownerNo,jdbcType=VARCHAR},
            #{item.isDefault,jdbcType=TINYINT},
            #{item.createUserId,jdbcType=BIGINT},
            #{item.lastUpdateUserId,jdbcType=BIGINT},
            #{item.refPartnerId,jdbcType=VARCHAR},
            #{item.companyId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        insert into owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="ownerType != null">
                OwnerType,
            </if>
            <if test="ownerName != null">
                OwnerName,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="province != null">
                Province,
            </if>
            <if test="city != null">
                City,
            </if>
            <if test="county != null">
                County,
            </if>
            <if test="street != null">
                Street,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="mobileNo != null">
                MobileNo,
            </if>
            <if test="userName != null">
                UserName,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="ownerNo != null">
                OwnerNo,
            </if>
            <if test="isDefault != null">
                IsDefault,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
            <if test="refPartnerId != null">
                Ref_Partner_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="ownerType != null">
                #{ownerType,jdbcType=TINYINT},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                #{street,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ownerNo != null">
                #{ownerNo,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=BIGINT},
            </if>
            <if test="refPartnerId != null">
                #{refPartnerId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        update owner
        <set>
            <if test="cityId != null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="ownerType != null">
                OwnerType = #{ownerType,jdbcType=TINYINT},
            </if>
            <if test="ownerName != null">
                OwnerName = #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                Street = #{street,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                MobileNo = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                UserName = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ownerNo != null">
                OwnerNo = #{ownerNo,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                IsDefault = #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=BIGINT},
            </if>
            <if test="refPartnerId != null">
                Ref_Partner_Id = #{refPartnerId,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from owner
        where Id = #{id,jdbcType=BIGINT}
    </delete>


    <update id="updateStatus">
        UPDATE owner
        set State = #{state,jdbcType=TINYINT},
            LastUpdateTime = now()
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>


    <select id="findOwnerByCondition" resultMap="PageResultMap">
        select
        <include refid="Base_Column_List"/>
        from owner
        <include refid="ownerQueryCondition"/>
        order by LastUpdateTime desc
    </select>

    <sql id="ownerQueryCondition">
        <where>
            <if test="cityId!=null">
                AND City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="cityId==null and cityIdList != null and cityIdList.size() > 0">
                and City_Id IN
                <foreach collection="cityIdList" separator="," open="(" close=")" item="item">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ownerType!=null">
                AND OwnerType= #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="ownerName!=null and ownerName!=''">
                AND OwnerName like CONCAT('%',#{ownerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="fullOwnerName!=null and fullOwnerName!=''">
                AND OwnerName = #{fullOwnerName,jdbcType=VARCHAR}
            </if>
            <if test="state != null">
                AND State = #{state,jdbcType=TINYINT}
            </if>
            <if test="userName != null and userName !=''">
                AND UserName = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="ownerNo != null and ownerNo !=''">
                AND OwnerNo like CONCAT('%',#{ownerNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="isDefault != null">
                AND IsDefault = #{isDefault,jdbcType=TINYINT}
            </if>
            <if test=" null != mobileNo and mobileNo != '' ">
                AND MobileNo like CONCAT('%',#{mobileNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="ownerNameList != null and ownerNameList.size() > 0">
                AND OwnerName IN
                <foreach collection="ownerNameList" separator="," open="(" close=")" item="item">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="ownerTypes != null and ownerTypes.size() > 0">
                AND OwnerType IN
                <foreach collection="ownerTypes" separator="," open="(" close=")" item="item">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="refPartnerId != null and refPartnerId !=''">
                AND (Ref_Partner_Id = #{refPartnerId, jdbcType=VARCHAR} or Id = #{refPartnerId, jdbcType=VARCHAR})
            </if>
        </where>
    </sql>

    <select id="checkOwnerNoUniq" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM owner
        <where>
            OwnerNo = #{ownerNo,jdbcType=VARCHAR}
            <if test="id != null and id !=''">
                and id != #{id,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="findOwnerByOwnerNoIn" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM owner
        <where>
            OwnerNo IN
            <foreach collection="list" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>


    <select id="findOwnerByNo" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM owner
        <where>
            OwnerNo IN
            <foreach collection="list" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="listOwnerByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from owner
        <include refid="ownerQueryCondition"/>
        order by LastUpdateTime desc
    </select>

    <select id="listOwnerByRefPartnerIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM owner
        <where>
            Ref_Partner_Id IN
            <foreach collection="list" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
            <if test="ownerType!=null">
                AND OwnerType= #{ownerType,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="listOwnerByRefPartnerIdsOrOwnerIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM owner
        <where>
            (
            Id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            or
            Ref_Partner_Id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
            <if test="ownerType!=null">
                AND OwnerType= #{ownerType,jdbcType=TINYINT}
            </if>
        </where>
        order by CreateTime
    </select>

    <select id="findOwnerByWarehouseIdWithSKU"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerPO">
        SELECT ps.Company_Id as id, IFNULL(MAX(ps.OwnerName), MAX(o.OwnerName)) as ownerName FROM productsku ps
        inner join productskuconfig psc on ps.ProductSku_Id = psc.ProductSku_Id
        left join owner o on ps.Company_Id = o.Id
        where psc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerType!=null">
            AND o.OwnerType = #{ownerType,jdbcType=TINYINT}
        </if>
        group by ps.Company_Id
        order by ps.Company_Id
    </select>
</mapper>
