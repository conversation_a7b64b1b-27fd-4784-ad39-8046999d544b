<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PartnerManagerMapper">

    <resultMap id="ownerManagerResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PartnerManagerPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="partnerName" jdbcType="VARCHAR" property="partnerName"/>
        <result column="shortCn" jdbcType="VARCHAR" property="shortCn"/>
        <result column="shortEn" jdbcType="VARCHAR" property="shortEn"/>
        <result column="partnerNo" jdbcType="VARCHAR" property="partnerNo"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="linkMan" jdbcType="VARCHAR" property="linkMan"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="line" jdbcType="VARCHAR" property="line"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="companyId" jdbcType="BIGINT" property="companyId"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="warehouseId" jdbcType="BIGINT" property="warehouseId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="createUser" jdbcType="BIGINT" property="createUser"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateUser" jdbcType="BIGINT" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="refPartnerId" jdbcType="VARCHAR" property="refPartnerId"/>
    </resultMap>

    <sql id="ownerManagerColumns">
        partner.Id AS "id",
        partner.PartnerName AS "partnerName",
        partner.ShortCn AS "shortCn",
        partner.ShortEn AS "shortEn",
        partner.PartnerNo AS "partnerNo",
        partner.Address AS "address",
        partner.LinkMan AS "linkMan",
        partner.Phone AS "phone",
        partner.Email AS "email",
        partner.Line AS "line",
        partner.Type AS "type",
        partner.Company_Id AS "companyId",
        partner.City_Id AS "cityId",
        partner.Warehouse_Id AS "warehouseId",
        partner.Status AS "status",
        partner.CreateUser AS "createUser",
        partner.CreateTime AS "createTime",
        partner.LastUpdateUser AS "lastUpdateUser",
        partner.LastUpdateTime AS "lastUpdateTime",
        partner.Ref_Partner_Id AS "refPartnerId"
    </sql>


    <select id="getPartnerManagerDetailById" parameterType="java.lang.Long" resultMap="ownerManagerResultMap">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            partner.Id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="getPartnerManagerDetailByRefId" parameterType="java.lang.String" resultMap="ownerManagerResultMap">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            partner.Ref_Partner_Id = #{refPartnerId,jdbcType=VARCHAR}
        </where>
    </select>

    <select id="listPartnerManagerDetailByRefId" resultMap="ownerManagerResultMap">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            partner.Ref_Partner_Id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>

    <select id="findPartnerManagerByCondition"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            <if test="type == null">
                and Type in (1,2,3)
            </if>
            <if test="type != null">
                and Type = #{type,jdbcType=TINYINT}
            </if>
            <if test="partnerName != null">
                and PartnerName like CONCAT('%',#{partnerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null">
                and Status = #{status,jdbcType=TINYINT}
            </if>
            <if test="partnerNo!= null">
                and PartnerNo like CONCAT('%',#{partnerNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="companyId!= null">
                and Company_Id = #{companyId,jdbcType=BIGINT}
            </if>
            <if test="warehouseId!= null">
                and Warehouse_Id = #{warehouseId,jdbcType=BIGINT}
            </if>
            <if test="cityId!= null">
                and City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="cityId==null and cityIdList != null and cityIdList.size() > 0">
                and City_Id IN
                <foreach collection="cityIdList" separator="," open="(" close=")" item="item">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by LastUpdateTime desc
    </select>


    <insert id="insertBatch">
        INSERT INTO partner(
        Id,
        PartnerName,
        PartnerNo,
        ShortCn,
        ShortEn,
        Address,
        LinkMan,
        Phone,
        Email,
        Line,
        Status,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        Company_Id,
        Warehouse_Id,
        City_Id,
        Type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.partnerName,jdbcType=VARCHAR},
            #{item.partnerNo,jdbcType=VARCHAR},
            #{item.shortCn,jdbcType=VARCHAR},
            #{item.shortEn,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.linkMan,jdbcType=VARCHAR},
            #{item.phone,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR},
            #{item.line,jdbcType=VARCHAR},
            #{item.status,jdbcType=TINYINT},
            #{item.createUser,jdbcType=BIGINT},
            now(),
            #{item.lastUpdateUser,jdbcType=BIGINT},
            now(),
            #{item.companyId,jdbcType=BIGINT},
            #{item.warehouseId,jdbcType=BIGINT},
            #{item.cityId,jdbcType=INTEGER},
            #{item.type,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <insert id="insert">
        INSERT INTO partner(
        Id,
        PartnerName,
        PartnerNo,
        ShortCn,
        ShortEn,
        Address,
        LinkMan,
        Phone,
        Email,
        Line,
        Status,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        Company_Id,
        Warehouse_Id,
        City_Id,
        Type,
        Ref_Partner_Id
        )
        VALUES (
        #{id,jdbcType=BIGINT},
        #{partnerName,jdbcType=VARCHAR},
        #{partnerNo,jdbcType=VARCHAR},
        #{shortCn,jdbcType=VARCHAR},
        #{shortEn,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR},
        #{linkMan,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR},
        #{line,jdbcType=VARCHAR},
        #{status,jdbcType=TINYINT},
        #{createUser,jdbcType=BIGINT},
        now(),
        #{lastUpdateUser,jdbcType=BIGINT},
        now(),
        #{companyId,jdbcType=BIGINT},
        #{warehouseId,jdbcType=BIGINT},
        #{cityId,jdbcType=INTEGER},
        #{type,jdbcType=TINYINT},
        #{refPartnerId,jdbcType=TINYINT}
        )
    </insert>


    <update id="update">
        UPDATE partner
        <set>
            <if test="id!= null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="partnerName!= null">
                PartnerName = #{partnerName,jdbcType=VARCHAR},
            </if>
            <if test="partnerNo!= null">
                PartnerNo = #{partnerNo,jdbcType=VARCHAR},
            </if>
            <if test="shortCn!= null">
                ShortCn = #{shortCn,jdbcType=VARCHAR},
            </if>
            <if test="shortEn!= null">
                ShortEn = #{shortEn,jdbcType=VARCHAR},
            </if>
            <if test="address!= null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="linkMan!= null">
                LinkMan = #{linkMan,jdbcType=VARCHAR},
            </if>
            <if test="phone!= null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email!= null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="line!= null">
                Line = #{line,jdbcType=VARCHAR},
            </if>
            <if test="status!= null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser!= null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="companyId!= null">
                Company_Id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="warehouseId!= null">
                Warehouse_Id = #{warehouseId,jdbcType=BIGINT},
            </if>
            <if test="cityId!= null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="type!= null">
                Type = #{type,jdbcType=TINYINT},
            </if>
            LastUpdateTime = now()
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateStatus">
        UPDATE partner
        set Status = #{status,jdbcType=TINYINT},
        LastUpdateTime = now(),
        LastUpdateUser = #{userId,jdbcType=BIGINT}
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>


    <select id="checkPartnerManagerNoUniq" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM partner
        <where>
            partner.PartnerNo = #{partnerNo,jdbcType=VARCHAR}
            and Type in (1,2,3)
            <if test="id != null and id !=''">
                and id != #{id,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="findPartnerByPartnerNoIn"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            PartnerNo IN
            <foreach collection="list" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>


    <select id="findPartnerByNo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            PartnerNo IN
            <foreach collection="list" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>


    <select id="listPartnerByIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PartnerManagerDTO">
        SELECT
        <include refid="ownerManagerColumns"/>
        FROM partner
        <where>
            Id IN
            <foreach collection="ids" close=")" index="index" open="(" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>