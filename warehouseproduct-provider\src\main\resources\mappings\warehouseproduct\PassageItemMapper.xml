<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageItemMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="Passage_Id" property="passageId" jdbcType="BIGINT"/>
        <result column="RelateType" property="relateType" jdbcType="TINYINT"/>
        <result column="Relate_Id" property="relateId" jdbcType="VARCHAR"/>
        <result column="RelateName" property="relateName" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Warehouse_Id, Passage_Id, RelateType, Relate_Id, RelateName, CreateTime
    </sql>

    <select id="listPassageItemByPassageId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="passageId != null">
                Passage_Id = #{passageId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="listPassageItemByPassageIdAndBiggerId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="passageId != null">
                AND Passage_Id = #{passageId,jdbcType=BIGINT}
            </if>
            <if test="id != null">
                and id > #{id,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="listPassageItemByPassageIdPage" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="passageId != null">
                Passage_Id = #{passageId,jdbcType=BIGINT}
            </if>
        </where>
        order by id asc
        limit #{offset},#{limit}
    </select>

    <select id="listPassageItemByPassageIdCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM passageitem
        <where>
            <if test="passageId != null">
                Passage_Id = #{passageId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="listPassageItem" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="passageId != null">
                Passage_Id != #{passageId,jdbcType=BIGINT}
            </if>
            <if test="warehouseId != null">
                AND Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="listPassageItemByRelate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="so.warehouseId != null">
                Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="so.relateType != null">
                AND RelateType = #{so.relateType,jdbcType=TINYINT}
            </if>
            <if test="so.relateIdList != null">
                AND Relate_Id IN
                <foreach collection="so.relateIdList" item="relateId" open="(" close=")" separator=",">
                    #{relateId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="so.passageId != null">
                AND Passage_Id = #{so.passageId, jdbcType=BIGINT}
            </if>
            <if test="so.passageIdList != null and so.passageIdList.size() > 0">
                AND Passage_Id IN
                <foreach collection="so.passageIdList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO passageitem (
        Id,
        Warehouse_Id,
        Passage_Id,
        RelateType,
        Relate_Id,
        RelateName
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.passageId,jdbcType=BIGINT},
            #{item.relateType,jdbcType=TINYINT},
            #{item.relateId,jdbcType=VARCHAR},
            #{item.relateName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="pageListPassageItem" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM passageitem
        <where>
            <if test="passageDTO.id != null">
                Passage_Id != #{passageDTO.id,jdbcType=BIGINT}
            </if>
            <if test="passageDTO.warehouseId != null">
                AND Warehouse_Id = #{passageDTO.warehouseId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <delete id="deleteByPassageId" parameterType="java.lang.Long">
        DELETE FROM passageitem
        WHERE Passage_Id = #{passageId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByIds" parameterType="java.lang.Long">
        DELETE FROM passageitem
        WHERE Id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByPassageIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from passageitem where Passage_Id in
        <foreach collection="collection" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectByPassageIdsAndLocationIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from passageItem
        <where>
            Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            AND Passage_Id in
            <foreach collection="passageIds" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            and Relate_Id in
            <foreach collection="locationIds" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>

</mapper>