<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.PassageMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="PassageName" property="passageName" jdbcType="VARCHAR"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="PassageType" property="passageType" jdbcType="TINYINT"/>
        <result column="PassageCode" property="passageCode" jdbcType="VARCHAR"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="PickingGroupStrategy" property="pickingGroupStrategy" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="SowType" property="sowType" jdbcType="TINYINT"/>
        <result column="IsRobotPick" property="isRobotPick" jdbcType="TINYINT"/>
        <result column="PackageType" property="packageType" jdbcType="TINYINT"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="AgvPickConfigType" property="agvPickConfigType" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="ResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="PassageName" property="passageName" jdbcType="VARCHAR"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="PassageType" property="passageType" jdbcType="TINYINT"/>
        <result column="PassageCode" property="passageCode" jdbcType="VARCHAR"/>
        <result column="PickingType" property="pickingType" jdbcType="TINYINT"/>
        <result column="PickingGroupStrategy" property="pickingGroupStrategy" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="SowType" property="sowType" jdbcType="TINYINT"/>
        <result column="IsRobotPick" property="isRobotPick" jdbcType="TINYINT"/>
        <result column="PackageType" property="packageType" jdbcType="TINYINT"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="AgvPickConfigType" property="agvPickConfigType" jdbcType="TINYINT"/>
        <collection property="passageItemPOS"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassageItemPO">
            <id column="item_Id" property="id" jdbcType="BIGINT"/>
            <result column="item_Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
            <result column="Passage_Id" property="passageId" jdbcType="BIGINT"/>
            <result column="RelateType" property="relateType" jdbcType="TINYINT"/>
            <result column="Relate_Id" property="relateId" jdbcType="VARCHAR"/>
            <result column="RelateName" property="relateName" jdbcType="VARCHAR"/>
            <result column="item_CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        Id, PassageName, Warehouse_Id, PassageType, PassageCode, PickingType, PickingGroupStrategy, Remark,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser, SowType, IsRobotPick, PackageType, State,
        AgvPickConfigType
    </sql>

    <sql id="Column_List">
        p.Id, p.PassageName, p.Warehouse_Id, p.PassageType, p.PassageCode, p.PickingType, p.PickingGroupStrategy,
        p.Remark,
        p.CreateTime, p.CreateUser, p.LastUpdateTime, p.LastUpdateUser, p.SowType, p.IsRobotPick, p.PackageType,
        p.State,p.AgvPickConfigType,
        pi.Id as item_Id, pi.Warehouse_Id as item_Warehouse_Id, pi.Passage_Id, pi.RelateType, pi.Relate_Id,
        pi.RelateName, pi.CreateTime as item_CreateTime
    </sql>

    <select id="listPassageByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM passage
        <where>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="passageName != null and passageName != ''">
                AND PassageName LIKE concat('%', #{passageName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="passageType != null">
                AND PassageType = #{passageType,jdbcType=TINYINT}
            </if>
            <if test="passageId != null">
                AND Id = #{passageId,jdbcType=BIGINT}
            </if>
            <if test="packageType != null">
                AND packageType = #{packageType,jdbcType=TINYINT}
            </if>
            <if test="passageIdList != null and passageIdList.size() > 0">
                and Id IN
                <foreach collection="passageIdList" item="id" open="(" close=")" separator=",">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="state != null">
                AND State = #{state,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY CreateTime
    </select>

    <select id="listPassageByIds" resultMap="BaseResultMap" parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List"/>
        FROM passage
        WHERE Id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM passage
        WHERE Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listPassageTypeByWarehouseId" resultType="java.lang.Byte" parameterType="java.lang.Integer">
        SELECT DISTINCT PassageType
        FROM passage
        WHERE Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="selectPassageTypeByWarehouseId" resultType="java.lang.Byte" parameterType="java.lang.Integer">
        SELECT
        PassageType
        FROM passage
        WHERE Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        order by CreateTime desc limit 1
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO">
        INSERT INTO passage (
        Id,
        PassageName,
        Warehouse_Id,
        PassageType,
        PickingType,
        <if test="passageCode != null">
            PassageCode,
        </if>
        <if test="pickingGroupStrategy != null">
            PickingGroupStrategy,
        </if>
        <if test="remark != null and remark != ''">
            Remark,
        </if>
        <if test="sowType != null">
            SowType,
        </if>
        <if test="isRobotPick != null">
            IsRobotPick,
        </if>
        <if test="agvPickConfigType != null">
            AgvPickConfigType,
        </if>
        CreateUser
        )
        VALUES(
        #{id,jdbcType=BIGINT},
        #{passageName,jdbcType=VARCHAR},
        #{warehouseId,jdbcType=INTEGER},
        #{passageType,jdbcType=TINYINT},
        #{pickingType,jdbcType=TINYINT},
        <if test="passageCode != null">
            #{passageCode,jdbcType=VARCHAR},
        </if>
        <if test="pickingGroupStrategy != null">
            #{pickingGroupStrategy,jdbcType=TINYINT},
        </if>
        <if test="remark != null and remark != ''">
            #{remark,jdbcType=VARCHAR},
        </if>
        <if test="sowType != null">
            #{sowType,jdbcType=TINYINT},
        </if>
        <if test="isRobotPick != null">
            #{isRobotPick,jdbcType=TINYINT},
        </if>
        <if test="agvPickConfigType != null">
            #{agvPickConfigType,jdbcType=TINYINT},
        </if>
        #{createUser,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.PassagePO">
        UPDATE passage
        <set>
            <if test="passageName != null and passageName != ''">
                PassageName = #{passageName,jdbcType=VARCHAR},
            </if>
            <if test="passageType != null">
                PassageType = #{passageType,jdbcType=TINYINT},
            </if>
            <if test="passageCode != null">
                PassageCode = #{passageCode,jdbcType=VARCHAR},
            </if>
            <if test="pickingType != null">
                PickingType = #{pickingType,jdbcType=TINYINT},
            </if>
            <if test="pickingGroupStrategy != null">
                PickingGroupStrategy = #{pickingGroupStrategy,jdbcType=TINYINT},
            </if>
            <if test="pickingGroupStrategy == null">
                PickingGroupStrategy = 0,
            </if>
            <if test="remark != null and remark != ''">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="sowType != null">
                SowType = #{sowType,jdbcType=TINYINT},
            </if>
            <if test="isRobotPick != null">
                IsRobotPick = #{isRobotPick,jdbcType=TINYINT},
            </if>
            <if test="packageType != null">
                PackageType = #{packageType,jdbcType=TINYINT},
            </if>
            <if test="agvPickConfigType != null">
                AgvPickConfigType = #{agvPickConfigType,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM passage
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="findAll" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from passage p
        inner join passageitem pi on pi.Passage_Id = p.Id
        where p.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
        <if test="so.relateType != null">
            and pi.RelateType = #{so.relateType,jdbcType=TINYINT}
        </if>
        <if test="so.relateIdList != null and so.relateIdList.size() >0 ">
            and pi.Relate_Id in
            <foreach collection="so.relateIdList" item="relateId" open="(" close=")" separator=",">
                #{relateId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="so.passageType != null">
            and p.PassageType = #{so.passageType,jdbcType=TINYINT}
        </if>
    </select>

    <select id="listPassageAllByCondition" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from passage p
        inner join passageitem pi on pi.Passage_Id = p.Id
        where p.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="passageName != null and passageName != ''">
            AND p.PassageName LIKE concat('%', #{passageName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="passageType != null">
            AND p.PassageType = #{passageType,jdbcType=TINYINT}
        </if>
        <if test="passageId != null">
            AND p.Id = #{passageId,jdbcType=BIGINT}
        </if>
        order by pi.id
    </select>

    <select id="pageListPassageItem" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from passage p
        inner join passageitem pi on pi.Passage_Id = p.Id
        where p.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
        <if test="so.relateType != null">
            and pi.RelateType = #{so.relateType,jdbcType=TINYINT}
        </if>
        <if test="so.relateIdList != null and so.relateIdList.size() >0 ">
            and pi.Relate_Id in
            <foreach collection="so.relateIdList" item="relateId" open="(" close=")" separator=",">
                #{relateId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="so.passageType != null">
            and p.PassageType = #{so.passageType,jdbcType=TINYINT}
        </if>
        <if test="so.state != null">
            and p.state = #{so.state,jdbcType=TINYINT}
        </if>
        and pi.id > #{so.lastMaxId,jdbcType=BIGINT}
        order by pi.Id limit #{so.batchCount,jdbcType=INTEGER}
    </select>
</mapper>