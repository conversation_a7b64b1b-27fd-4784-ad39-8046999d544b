<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductCodeWhitelistPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        <!--@Table productcodewhitelist-->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="SkuId" jdbcType="BIGINT" property="skuId"/>
        <result column="SpecificationId" jdbcType="BIGINT" property="specificationId"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        Id, WarehouseId, ProductName, SkuId, SpecificationId, CreateTime, CreateUserId, LastUpdateTime,
        LastUpdateUserId
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from productcodewhitelist
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from productcodewhitelist
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        insert into productcodewhitelist (Id, WarehouseId, ProductName,
        SkuId, SpecificationId, CreateTime,
        CreateUserId, LastUpdateTime, LastUpdateUserId
        )
        values (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR},
        #{skuId,jdbcType=BIGINT}, #{specificationId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER}
        )
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        insert into productcodewhitelist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="productName != null and productName != ''">
                ProductName,
            </if>
            <if test="skuId != null">
                SkuId,
            </if>
            <if test="specificationId != null">
                SpecificationId,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productName != null and productName != ''">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        update productcodewhitelist
        <set>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productName != null and productName != ''">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                SkuId = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                SpecificationId = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        update productcodewhitelist
        set WarehouseId = #{warehouseId,jdbcType=INTEGER},
        ProductName = #{productName,jdbcType=VARCHAR},
        SkuId = #{skuId,jdbcType=BIGINT},
        SpecificationId = #{specificationId,jdbcType=BIGINT},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUserId = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productcodewhitelist
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ProductName = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="SpecificationId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.specificationId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CreateUserId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUserId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUserId,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productcodewhitelist
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductName = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productName != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.skuId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SpecificationId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.specificationId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.specificationId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateUserId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUserId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUserId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUserId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into productcodewhitelist
        (Id, WarehouseId, ProductName, SkuId, SpecificationId, CreateTime, CreateUserId,
        LastUpdateTime, LastUpdateUserId)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.productName,jdbcType=VARCHAR},
            #{item.skuId,jdbcType=BIGINT}, #{item.specificationId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUserId,jdbcType=INTEGER})
        </foreach>
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from productcodewhitelist where Id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <insert id="insertOrUpdate"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        insert into productcodewhitelist
        (Id, WarehouseId, ProductName, SkuId, SpecificationId, CreateTime, CreateUserId,
        LastUpdateTime, LastUpdateUserId)
        values
        (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR},
        #{skuId,jdbcType=BIGINT}, #{specificationId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER}
        )
        on duplicate key update
        Id = #{id,jdbcType=BIGINT},
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
        ProductName = #{productName,jdbcType=VARCHAR},
        SkuId = #{skuId,jdbcType=BIGINT},
        SpecificationId = #{specificationId,jdbcType=BIGINT},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUserId = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductCodeWhitelistPO">
        <!--@mbg.generated-->
        insert into productcodewhitelist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="productName != null and productName != ''">
                ProductName,
            </if>
            <if test="skuId != null">
                SkuId,
            </if>
            <if test="specificationId != null">
                SpecificationId,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productName != null and productName != ''">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productName != null and productName != ''">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                SkuId = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                SpecificationId = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>