<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductCodeWhitelistPOMapper">
    <select id="selectValidWhitelist" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcodewhitelist
        <where>
            WarehouseId = #{warehouseId,jdbcType=INTEGER}
            and SkuId in
            <foreach collection="skuId" open="(" close=")" separator="," item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
            and LastUpdateTime >= sysdate() - interval 3 month
        </where>
    </select>

    <select id="selectByWarehouseIdAndSkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcodewhitelist
        where WarehouseId = #{warehouseId,jdbcType=INTEGER}
        and SkuId = #{skuId,jdbcType=BIGINT}
        limit 1
    </select>
</mapper>