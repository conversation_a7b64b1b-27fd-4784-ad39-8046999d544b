<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigItemPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Config_Id" jdbcType="BIGINT" property="configId"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="Street" jdbcType="VARCHAR" property="street"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="Channel" jdbcType="VARCHAR" property="channel"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Config_Id, City, County, Street, Address, Channel, Name, Remark, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcontrolconfigitem
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcontrolconfigitem
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO">
        insert into productcontrolconfigitem (Id, Config_Id, City,
        County, Street, Address,
        Channel, Name, Remark,
        CreateTime, CreateUser_Id, LastUpdateTime,
        LastUpdateUser_Id)
        values (#{id,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{city,jdbcType=VARCHAR},
        #{county,jdbcType=VARCHAR}, #{street,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
        #{channel,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{lastUpdateUserId,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO">
        insert into productcontrolconfigitem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="configId != null">
                Config_Id,
            </if>
            <if test="city != null">
                City,
            </if>
            <if test="county != null">
                County,
            </if>
            <if test="street != null">
                Street,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="channel != null">
                Channel,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=BIGINT},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                #{street,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO">
        update productcontrolconfigitem
        <set>
            <if test="configId != null">
                Config_Id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="city != null">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                Street = #{street,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                Channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO">
        update productcontrolconfigitem
        set Config_Id = #{configId,jdbcType=BIGINT},
        City = #{city,jdbcType=VARCHAR},
        County = #{county,jdbcType=VARCHAR},
        Street = #{street,jdbcType=VARCHAR},
        Address = #{address,jdbcType=VARCHAR},
        Channel = #{channel,jdbcType=VARCHAR},
        Name = #{name,jdbcType=VARCHAR},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into productcontrolconfigitem (
        Id,
        Config_Id,
        City,
        County,
        Street,
        Address,
        Channel,
        Name,
        Remark,
        CreateUser_Id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.configId,jdbcType=BIGINT},
            #{item.city,jdbcType=VARCHAR},
            #{item.county,jdbcType=VARCHAR},
            #{item.street,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.channel,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createUserId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <delete id="deleteByConfigId" parameterType="java.lang.Long">
        delete from productcontrolconfigitem
        where Config_Id = #{configId,jdbcType=BIGINT}
    </delete>

    <select id="listProductControlConfigItemByConfigId" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcontrolconfigitem
        where Config_Id in
        <foreach collection="configIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        order by Id
    </select>

    <select id="findProductControlConfigItemByConfigId" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcontrolconfigitem
        where Config_Id = #{configId}
    </select>

</mapper>