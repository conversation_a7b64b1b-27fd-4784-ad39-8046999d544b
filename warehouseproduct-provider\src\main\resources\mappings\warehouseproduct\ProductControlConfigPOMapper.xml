<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Provider_Id" jdbcType="BIGINT" property="providerId"/>
        <result column="Provider" jdbcType="VARCHAR" property="provider"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="IsDelete" jdbcType="TINYINT" property="isDelete"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id, Provider_Id, Provider, ProductSku_Id, State, IsDelete,
        Name, Remark, CreateTime, CreateUser_Id, LastUpdateTime, LastUpdateUser_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productcontrolconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productcontrolconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        insert into productcontrolconfig (Id, City_Id, Warehouse_Id,
        Provider_Id, Provider, ProductSku_Id,
        State, IsDelete, Name,
        Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id)
        values (#{id,jdbcType=BIGINT}, #{cityId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{providerId,jdbcType=BIGINT}, #{provider,jdbcType=VARCHAR}, #{productSkuId,jdbcType=BIGINT},
        #{state,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        insert into productcontrolconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="providerId != null">
                Provider_Id,
            </if>
            <if test="provider != null">
                Provider,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="isDelete != null">
                IsDelete,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="providerId != null">
                #{providerId,jdbcType=BIGINT},
            </if>
            <if test="provider != null">
                #{provider,jdbcType=VARCHAR},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        update productcontrolconfig
        <set>
            <if test="cityId != null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="providerId != null">
                Provider_Id = #{providerId,jdbcType=BIGINT},
            </if>
            <if test="provider != null">
                Provider = #{provider,jdbcType=VARCHAR},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                IsDelete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        update productcontrolconfig
        set City_Id = #{cityId,jdbcType=INTEGER},
        Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        Provider_Id = #{providerId,jdbcType=BIGINT},
        Provider = #{provider,jdbcType=VARCHAR},
        ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
        State = #{state,jdbcType=TINYINT},
        IsDelete = #{isDelete,jdbcType=TINYINT},
        Name = #{name,jdbcType=VARCHAR},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getProductControlConfig" parameterType="java.lang.Long"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO">
        select
        pc.Id as id,
        pc.City_Id as cityId,
        pc.Warehouse_Id as warehouseId,
        pc.Provider_Id as providerId,
        pc.Provider as provider,
        pc.ProductSku_Id as productSkuId,
        sku.Name as productName
        from productcontrolconfig pc left join productsku sku on pc.ProductSku_Id = sku.ProductSku_Id
        where pc.Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listProductControlConfig"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO">
        select
        pc.Id as id,
        pc.City_Id as cityId,
        pc.Warehouse_Id as warehouseId,
        pc.Provider_Id as providerId,
        pc.Provider as provider,
        pc.ProductSku_Id as productSkuId,
        pc.State as state,
        pc.CreateTime as createTime,
        sku.Name as productName
        from productcontrolconfig pc left join productsku sku on pc.ProductSku_Id = sku.ProductSku_Id
        where pc.City_Id = #{cityId,jdbcType=INTEGER}
        and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and pc.IsDelete = 0
        <if test="state != null">
            and pc.State = #{state,jdbcType=TINYINT}
        </if>
        <if test="providerId != null">
            and pc.Provider_Id = #{providerId,jdbcType=BIGINT}
        </if>
        <if test="provider != null and provider != ''">
            and pc.Provider like concat('%', #{provider,jdbcType=VARCHAR}, '%')
        </if>
        <if test="productSkuId != null">
            and pc.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
        </if>
        <if test="productSkuIds != null and productSkuIds.size() > 0">
            and pc.ProductSku_Id in
            <foreach collection="productSkuIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="productName != null and productName != ''">
            and sku.Name like concat('%', #{productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="city != null or county != null or street != null">
            and pc.Id IN (
            select distinct(s.Config_Id)
            FROM productcontrolconfigitem s
            <where>
                <if test="city != null">
                    s.City = #{city,jdbcType=VARCHAR}
                </if>
                <if test="county != null">
                    and s.County = #{county,jdbcType=VARCHAR}
                </if>
                <if test="street != null">
                    and s.Street = #{street,jdbcType=VARCHAR}
                </if>
            </where>
            )
        </if>
    </select>

    <select id="listProductControlConfigByAddress"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO">
        select distinct pc.Id as id, pc.ProductSku_Id as productSkuId
        from productcontrolconfig pc
        inner join productcontrolconfigitem pci on pc.Id = pci.Config_Id
        where pc.City_Id = #{cityId,jdbcType=INTEGER}
        and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and pc.IsDelete = 0
        and pc.State = 1
        and pc.ProductSku_Id in
        <foreach collection="productSkuIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="city != null">
            and (pci.City is null or pci.City = '' or pci.City = #{city,jdbcType=VARCHAR})
        </if>
        <if test="county != null">
            and (pci.County is null or pci.County = '' or pci.County = #{county,jdbcType=VARCHAR})
        </if>
    </select>

    <select id="listProductControlConfigByIds" parameterType="java.util.List"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigPO">
        select
        cfg.Id as id,
        cfg.City_Id as cityId,
        cfg.Warehouse_Id as warehouseId,
        cfg.Provider_Id as providerId,
        cfg.Provider as provider,
        cfg.ProductSku_Id as productSkuId,
        cfg.State as state,
        cfg.IsDelete as isDelete,
        cfg.Name as name,
        cfg.Remark as remark,
        sku.Name as productName
        from productcontrolconfig cfg
        inner join productsku sku on cfg.ProductSku_Id = sku.ProductSku_Id
        where cfg.Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="countProductControlConfig" resultType="java.lang.Long">
        select count(*)
        from productcontrolconfig
        where ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
        and Provider = #{provider,jdbcType=VARCHAR}
        and IsDelete = 0
        <if test="id != null">
            and Id != #{id,jdbcType=BIGINT}
        </if>
    </select>

</mapper>