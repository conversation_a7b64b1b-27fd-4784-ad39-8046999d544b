<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouse_Id"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSku_Id"/>
        <result column="Location_Id" jdbcType="BIGINT" property="location_Id"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id
                , City_Id, Warehouse_Id, ProductSku_Id, Location_Id, Remo, CreateTime,
        CreateUserId,
        LastUpdateTime, LastUpdateUserId
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productlocation
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productlocation
        where Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from productlocation
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteBatch" parameterType="java.util.List">
        delete from productlocation
        where Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        insert into productlocation (Id, City_Id, Warehouse_Id,
                                     ProductSku_Id, Location_Id, Remo,
                                     CreateTime, CreateUserId, LastUpdateTime,
                                     LastUpdateUserId)
        values (#{id,jdbcType=BIGINT}, #{city_Id,jdbcType=INTEGER},
                #{warehouse_Id,jdbcType=INTEGER},
                #{productSku_Id,jdbcType=BIGINT}, #{location_Id,jdbcType=BIGINT}, #{remo,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
                #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{lastUpdateUserId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        insert into productlocation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="city_Id != null">
                City_Id,
            </if>
            <if test="warehouse_Id != null">
                Warehouse_Id,
            </if>
            <if test="productSku_Id != null">
                ProductSku_Id,
            </if>
            <if test="location_Id != null">
                Location_Id,
            </if>
            <if test="remo != null">
                Remo,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="city_Id != null">
                #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="warehouse_Id != null">
                #{warehouse_Id,jdbcType=INTEGER},
            </if>
            <if test="productSku_Id != null">
                #{productSku_Id,jdbcType=BIGINT},
            </if>
            <if test="location_Id != null">
                #{location_Id,jdbcType=BIGINT},
            </if>
            <if test="remo != null">
                #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into productlocation (
        Id,
        City_Id,
        Warehouse_Id,
        ProductSku_Id,
        Location_Id,
        CreateTime,
        CreateUserId
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.city_Id,jdbcType=INTEGER},
            #{item.warehouse_Id,jdbcType=INTEGER},
            #{item.productSku_Id,jdbcType=BIGINT},
            #{item.location_Id,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        update productlocation
        <set>
            <if test="city_Id != null">
                City_Id = #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="warehouse_Id != null">
                Warehouse_Id = #{warehouse_Id,jdbcType=INTEGER},
            </if>
            <if test="productSku_Id != null">
                ProductSku_Id = #{productSku_Id,jdbcType=BIGINT},
            </if>
            <if test="location_Id != null">
                Location_Id = #{location_Id,jdbcType=BIGINT},
            </if>
            <if test="remo != null">
                Remo = #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        update productlocation
        set City_Id          = #{city_Id,jdbcType=INTEGER},
            Warehouse_Id     = #{warehouse_Id,jdbcType=INTEGER},
            ProductSku_Id    = #{productSku_Id,jdbcType=BIGINT},
            Location_Id      = #{location_Id,jdbcType=BIGINT},
            Remo             = #{remo,jdbcType=VARCHAR},
            CreateTime       = #{createTime,jdbcType=TIMESTAMP},
            CreateUserId     = #{createUserId,jdbcType=INTEGER},
            LastUpdateTime   = #{lastUpdateTime,jdbcType=TIMESTAMP},
            LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <!--查询该条记录是否存在-->
    <select id="getProductLocationCount" resultType="java.lang.Long">
        select count(1) from productlocation
        <where>
            <if test="productSkuId != null and productSkuId != ''">
                and ProductSku_Id =#{productSkuId}
            </if>
            <if test="locationId != null and locationId != ''">
                and Location_Id =#{locationId}
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                and Warehouse_Id =#{warehouseId}
            </if>
        </where>
    </select>

    <select id="getProductLocationByMixUpCount" resultType="java.lang.Long">
        select count(1)
        from productlocation pl
                     inner join location loc on pl.Location_Id = loc.Id
        where pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
          and loc.category in (0, 2)
          and loc.IsChaosPut = 0
          and pl.Location_Id = #{locationId,jdbcType=BIGINT}
          and pl.ProductSku_Id != #{productSkuId,jdbcType=BIGINT}
    </select>

    <!--获取产品货位配置列表 -->
    <select id="listProductLocation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO">
        SELECT
        distinct
        sku.ProductSku_Id AS productSkuId,
        CONCAT(case sku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, sku.NAME) AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageQuantity,
        sku.packageName,
        sku.unitName,
        sku.OwnerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductState as productState,
        pl.id AS productLocationId,
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.area_Id AS areaId,
        loc.area AS areaName,
        loc.subcategory,
        loc.locationGrade as locationGrade,
        loc.category as category,
        loc.BusinessType,
        area.subcategory as areaSubcategory,
        pct.StatisticsClassName,
        pct.SecondStatisticsClassName,
        ps.TotalCount_MinUnit as unitTotolCount
        FROM productsku sku
        LEFT JOIN productstore ps ON ps.productspecification_id = sku.productspecification_id
        AND ps.Channel = 0
        AND ps.city_id = sku.city_id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
            AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        <if test="warehouseId != null">
            and pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN location loc ON pl.Location_Id = loc.id and loc.warehouse_id = pl.Warehouse_Id
        LEFT JOIN location area ON loc.area_id = area.id
        LEFT JOIN productinfocategory pct on sku.ProductInfoCategory_Id=pct.id
        <if test="limitSku != null and limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
            <if test="warehouseId != null">
                and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </if>
        <where>
            <if test="productState != null">
                sku.ProductState =#{productState,jdbcType=TINYINT}
            </if>
            <if test="statisticsClass!=null">
                and pct.statisticsClass =#{statisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass!=null">
                and pct.secondStatisticsClass =#{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationName != null and locationName != ''">
                and loc.name like concat(#{locationName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationFullName != null and locationFullName != ''">
                and loc.name = #{locationFullName,jdbcType=VARCHAR}
            </if>
            <if test="subcategory != null">
                and loc.subcategory = #{subcategory,jdbcType=TINYINT}
            </if>
            <if test="existLocation != null and existLocation == 1">
                and loc.id is not null
            </if>
            <if test="hasRealStore != null and hasRealStore == 1">
                and ps.TotalCount_MinUnit > 0
            </if>
            <if test="productSkuId != null">
                and sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        ORDER BY sku.ProductSku_Id
    </select>

    <!--获取产品货位配置列表 -->
    <select id="listProductLocationWithInventory"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO">
        SELECT
        distinct
        sku.ProductSku_Id AS productSkuId,
        CONCAT(case sku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, sku.NAME) AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageQuantity,
        sku.packageName,
        sku.unitName,
        sku.OwnerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductState as productState,
        pl.id AS productLocationId,
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.area_Id AS areaId,
        loc.area AS areaName,
        loc.subcategory,
        loc.locationGrade as locationGrade,
        loc.category as category,
        loc.BusinessType,
        area.subcategory as areaSubcategory,
        pct.StatisticsClassName,
        pct.SecondStatisticsClassName
        FROM productsku sku
        LEFT JOIN productstore ps ON ps.productspecification_id = sku.productspecification_id
        AND ps.Channel = 0
        AND ps.city_id = sku.city_id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
            AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        <if test="warehouseId != null">
            and pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN location loc ON pl.Location_Id = loc.id and loc.warehouse_id = pl.Warehouse_Id
        LEFT JOIN location area ON loc.area_id = area.id
        LEFT JOIN productinfocategory pct on sku.ProductInfoCategory_Id=pct.id
        <if test="limitSku != null and limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
            <if test="warehouseId != null">
                and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </if>
        <where>
            <if test="productState != null">
                sku.ProductState =#{productState,jdbcType=TINYINT}
            </if>
            <if test="statisticsClass!=null">
                and pct.statisticsClass =#{statisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass!=null">
                and pct.secondStatisticsClass =#{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationName != null and locationName != ''">
                and loc.name like concat(#{locationName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationFullName != null and locationFullName != ''">
                and loc.name = #{locationFullName,jdbcType=VARCHAR}
            </if>
            <if test="subcategory != null">
                and loc.subcategory = #{subcategory,jdbcType=TINYINT}
            </if>
            <if test="existLocation != null and existLocation == 1">
                and loc.id is not null
            </if>
            <if test="hasRealStore != null and hasRealStore == 1">
                and ps.TotalCount_MinUnit > 0
            </if>
            <if test="productSkuId != null">
                and sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="businessType != null">
                and loc.BusinessType = #{businessType,jdbcType=TINYINT}
            </if>
            <if test="warehouseAllocationType != null">
                and (pc.storageAttribute = #{warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </where>
        ORDER BY sku.ProductSku_Id
    </select>

    <!--获取产品货位配置列表 -->
    <select id="listProductLocationWithoutInventory"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO">
        SELECT
        distinct
        sku.ProductSku_Id AS productSkuId,
        CONCAT(case sku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, sku.NAME) AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageQuantity,
        sku.packageName,
        sku.unitName,
        sku.OwnerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductState as productState,
        pl.id AS productLocationId,
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.area_Id AS areaId,
        loc.area AS areaName,
        loc.subcategory,
        loc.locationGrade as locationGrade,
        loc.category as category,
        loc.BusinessType,
        area.subcategory as areaSubcategory,
        pct.StatisticsClassName,
        pct.SecondStatisticsClassName
        FROM productsku sku
        <if test="limitSku != null and limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
            <if test="warehouseId != null">
                and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </if>
        LEFT JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        <if test="warehouseId != null">
            and pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN location loc ON pl.Location_Id = loc.id and loc.warehouse_id = pl.Warehouse_Id
        LEFT JOIN location area ON loc.area_id = area.id
        LEFT JOIN productinfocategory pct on sku.ProductInfoCategory_Id=pct.id
        <where>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="productState != null">
                and sku.ProductState =#{productState,jdbcType=TINYINT}
            </if>
            <if test="statisticsClass!=null">
                and pct.statisticsClass =#{statisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass!=null">
                and pct.secondStatisticsClass =#{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationName != null and locationName != ''">
                and loc.name like concat(#{locationName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationFullName != null and locationFullName != ''">
                and loc.name = #{locationFullName,jdbcType=VARCHAR}
            </if>
            <if test="subcategory != null">
                and loc.subcategory = #{subcategory,jdbcType=TINYINT}
            </if>
            <if test="existLocation != null and existLocation == 1">
                and loc.id is not null
            </if>
            <if test="productSkuId != null">
                and sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="businessType != null">
                and loc.BusinessType = #{businessType,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY sku.ProductSku_Id
    </select>

    <select id="listProductLocationNoPage"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO">
        SELECT
        sku.ProductSku_Id AS productSkuId,
        sku. NAME AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        pl.id AS productLocationId,
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.subcategory,
        loc.category,
        loc.sequence AS locationSequence,
        sku.OwnerName
        FROM productsku sku
        INNER JOIN productstore ps ON ps.productspecification_id = sku.productspecification_id
        AND ps.Channel = 0
        and sku.City_id = ps.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id and ps.Warehouse_Id = pl.Warehouse_Id
        INNER JOIN location loc ON pl.Location_Id = loc.id AND loc.category = 0 and loc.warehouse_id = ps.warehouse_id
        <where>
            <if test="warehouseId != null">
                ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productSkuId != null">
                and sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <!--获取所有产品货位记录 -->
    <select id="listProductLocationSelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from productlocation
        <where>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productSkuId != null">
                and ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findLocationPruductsByRelation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        SELECT
        sku.Id as id,
        sku.City_Id as city_Id,
        sku.ProductSpecification_Id as productSpecification_Id,
        sku.ProductSku_Id as productSku_Id,
        sku.name as name,
        sku.Sequence as sequence,
        sku.Remo as remo,
        sku.CreateTime as createTime,
        sku.CreateUserId as createUserId,
        sku.LastUpdateTime as lastUpdateTime,
        sku.LastUpdateUserId as lastUpdateUserId,
        sku.Company_Id as company_Id,
        sku.SaleModel as saleModel,
        sku.DistributionPercent as distributionPercent,
        sku.DistributionPercentForAmount as distributionPercentForAmount,
        sku.specificationName as specificationName,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.packageQuantity as packageQuantity,
        sku.source as source,
        sku.warehouseCustodyFee as warehouseCustodyFee,
        sku.DeliveryFee as deliveryFee,
        sku.DeliveryPayType as deliveryPayType,
        sku.ProductInfo_Id as productInfoId,
        sku.productBrand as productBrand,
        sku.ownerName as ownerName,
        sku.ProductState as productState,
        sku.Unpackage as unpackage,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductFeature as productFeature,
        sku.MaxInventory as maxInventory,
        sku.MinInventory as minInventory,
        sku.MaxReplenishment as maxReplenishment,
        sku.MinReplenishment as minReplenishment,
        sku.isComplete as isComplete
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN location loc on loc.id = pl.Location_Id
        where pl.City_Id = #{cityId,jdbcType=INTEGER}
        and pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationId != null">
            and pl.Location_Id = #{locationId,jdbcType=BIGINT}
        </if>
        <if test="locationName != null">
            AND loc.Name like concat(#{locationName,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY sku.id
    </select>

    <!--根据产品配置货位信息查询产品SKU基础信息 -->
    <select id="findProductBaseInfoByProductLocation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuBaseInfoDTO">
        SELECT
        sku.ProductSku_Id as productSkuId,
        CONCAT(case sku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, sku.NAME) AS productName,
        sku.specificationName,
        sku.unitName,
        sku.packageName,
        sku.packageQuantity,
        sku.Source as source,
        sku.SaleModel as saleModel,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.ownerName,
        sku.Company_Id as ownerId,
        sku.secOwner_Id as secOwnerId,
        sku.productBrand,
        sku.ProductState as productState,
        IFNULL(info.MonthOfShelfLife, sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit, sku.ShelfLifeUnit) as shelfLifeUnit,
        info.ShelfLifeLongTime
        FROM productsku sku
        INNER JOIN productinfo info on sku.ProductInfo_Id = info.Id
        <if test="hasRealStore != null or ownerType != null">
            INNER JOIN
        </if>
        <if test="hasRealStore == null and ownerType == null">
            LEFT JOIN
        </if>
        (
        SELECT
        psku.City_Id, psku.ProductSku_Id, SUM(ps.TotalCount_MinUnit) as TotalCount_MinUnit
        FROM productsku psku
        INNER JOIN productstore ps ON ps.productspecification_id = psku.productspecification_id
        AND ps.Channel = 0
        AND ps.city_id = psku.city_id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((psku.Company_Id IS NULL AND ps.Owner_Id IS NULL) OR (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id IS NULL) OR (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        <if test="warehouseId != null">
            AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <where>
            <if test="productState != null">
                AND psku.ProductState =#{productState,jdbcType=TINYINT}
            </if>
            <if test="cityId != null">
                and psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and psku.Name like concat('%',#{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="hasRealStore != null and hasRealStore == 1">
                and ps.TotalCount_MinUnit > 0
            </if>
            <if test="productSkuId != null">
                and psku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="ownerType != null">
                and ps.OwnerType = #{ownerType,jdbcType=INTEGER}
            </if>
        </where>
        GROUP BY psku.City_Id, psku.ProductSku_Id
        ) tmp on sku.City_Id = tmp.City_Id AND sku.ProductSku_Id = tmp.ProductSku_Id
        LEFT JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        <if test="warehouseId != null">
            and pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN location loc ON pl.Location_Id = loc.id
        <if test="warehouseId != null">
            and loc.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN productinfocategory pct on sku.ProductInfoCategory_Id = pct.id

        <if test="warehouseId != null">
            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                inner JOIN productskuconfig cfg ON cfg.ProductSku_Id = sku.ProductSku_Id
                    AND cfg.Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and cfg.State = 1
                    and cfg.storageAttribute = #{warehouseAllocationType, jdbcType=INTEGER}
            </if>
        </if>

        <where>
            <if test="productState != null">
                AND sku.ProductState =#{productState,jdbcType=TINYINT}
            </if>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productSkuId != null">
                and sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="statisticsClass!=null">
                and pct.statisticsClass =#{statisticsClass,jdbcType=BIGINT}
            </if>
            <if test="secondStatisticsClass!=null">
                and pct.secondStatisticsClass =#{secondStatisticsClass,jdbcType=BIGINT}
            </if>
            <if test="locationName != null and locationName != ''">
                and loc.name like concat(#{locationName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="locationFullName != null and locationFullName != ''">
                and loc.name = #{locationFullName,jdbcType=VARCHAR}
            </if>
            <if test="subcategory != null">
                and loc.subcategory = #{subcategory,jdbcType=TINYINT}
            </if>
            <if test="existLocation != null and existLocation == 1">
                and loc.id is not null
            </if>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        ORDER BY tmp.TotalCount_MinUnit DESC, sku.ProductSku_Id
    </select>

    <!--根据产品SKU信息查询仓库产品配置货位信息 -->
    <select id="findProductLocationBySkuInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationListPO">
        SELECT
        sku.ProductSku_Id AS productSkuId,
        CONCAT(case sku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, sku.NAME) AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageQuantity,
        sku.packageName,
        sku.unitName,
        sku.OwnerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductState as productState,
        pl.id AS productLocationId,
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.subcategory as subcategory,
        loc.locationGrade as locationGrade
        FROM productsku sku
        INNER JOIN productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        LEFT JOIN location loc ON pl.Location_Id = loc.id and loc.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        WHERE pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="productSkuIdList != null and productSkuIdList.size() >0">
            AND pl.ProductSku_Id in
            <foreach collection="productSkuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="cityId != null">
            AND pl.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="listProductByLocationIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductLocationPO">
        SELECT
        sku.ProductSku_Id as productSku_Id,
        psb.location_id as location_Id
        FROM productstore ps
        INNER JOIN productstorebatch psb on ps.id = psb.productstore_id
        INNER JOIN productsku sku ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND sku.City_Id = ps.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        LEFT JOIN owner ow on ow.id = ps.SecOwner_Id
        where 1=1
        <if test="list != null and list.size() > 0">
            and psb.location_id in
            <foreach collection="list" item="locationId" separator="," open="(" close=")">
                #{locationId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="warehouseId == null and cityId != null">
            and ps.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findProductByRelationLocation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO">
        SELECT
        loc.id AS locationId,
        loc.NAME AS locationName,
        loc.subcategory as subcategory,
        loc.category as category,
        loc.area_id as locationAreaId,
        sku.ProductSku_Id as productSkuId,
        sku.name as productName,
        sku.City_Id as cityId,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as companyId,
        sku.SaleModel as saleModel,
        sku.specificationName as specificationName,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.packageQuantity as packageQuantity,
        sku.source as source,
        sku.ProductInfo_Id as productInfoId,
        sku.productBrand as productBrand,
        sku.ownerName as ownerName,
        sku.ProductState as productState,
        IFNULL(MAX(f.ProductFeature), sku.ProductFeature) as productFeature,
        IFNULL(MAX(f.MaxReplenishment), sku.MaxReplenishment) as maxReplenishment,
        IFNULL(MAX(f.MinReplenishment), sku.MinReplenishment) as minReplenishment,
        MAX(f.palletQuantity) as palletQuantity
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN location loc on loc.id = pl.Location_Id
        left join productskuconfig f on f.ProductSku_Id = sku.ProductSku_Id and f.Warehouse_Id =
        #{warehouseId,jdbcType=INTEGER}
        where pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationId != null">
            and pl.Location_Id = #{locationId,jdbcType=BIGINT}
        </if>
        <if test="locationName != null">
            AND loc.Name like concat(#{locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationIds != null and locationIds.size() > 0">
            and pl.Location_Id in
            <foreach collection="locationIds" item="locationId" separator="," open="(" close=")">
                #{locationId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        GROUP BY loc.id,sku.id
    </select>

    <select id="findProductByLocationGroup"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO">
        SELECT
        loc.id AS locationId,
        loc.NAME AS locationName,
        sku.ProductSku_Id as productSkuId,
        sku.name as productName,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        sku.ProductState as productState,
        area.id AS locationAreaId,
        area.NAME AS locationAreaName
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN location loc on loc.id = pl.Location_Id
        INNER JOIN location area on area.id = loc.area_id
        where pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationId != null">
            and area.id = #{locationId,jdbcType=BIGINT}
        </if>
        <if test="locationIds != null and locationIds.size() > 0">
            and area.id in
            <foreach collection="locationIds" item="locationId" separator="," open="(" close=")">
                #{locationId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="subcategoryList != null and subcategoryList.size() > 0">
            and loc.subcategory in
            <foreach collection="subcategoryList" item="subcategory" separator="," open="(" close=")">
                #{subcategory,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        GROUP BY area.id,sku.id,loc.id
    </select>

    <select id="findProductByCondition"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO">
        SELECT
        loc.id AS locationId,
        loc.NAME AS locationName,
        sku.ProductSku_Id as productSkuId,
        sku.name as productName,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        sku.ProductState as productState,
        area.id AS locationAreaId,
        area.NAME AS locationAreaName
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN location loc on loc.id = pl.Location_Id
        INNER JOIN location area on area.id = loc.area_id
        where pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationAreaList != null and locationAreaList.size() > 0">
            and (
            loc.Area in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or
            loc.`Name` in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="roadWayList != null and roadWayList.size() > 0">
            and loc.RoadWay in
            <foreach collection="roadWayList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>
</mapper>
