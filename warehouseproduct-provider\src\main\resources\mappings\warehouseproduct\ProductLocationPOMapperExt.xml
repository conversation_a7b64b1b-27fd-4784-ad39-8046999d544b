<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductLocationPOMapper">
    <delete id="deleteByLocationId">
        delete
        from productlocation
        where Location_Id = #{locationId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByLocationIds">
        delete from productlocation
        where Location_Id IN
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <delete id="deleteBySkuId">
        delete from productlocation
        where ProductSku_Id IN
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </delete>

    <select id="findByLocationId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productlocation
        where location_id = #{locationId,jdbcType=BIGINT}
    </select>


    <select id="findLocationBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO">
        select loc.id as locationId,loc.name as locationName,loc.city_Id as cityId,loc.warehouse_Id as warehouseId,
        loc.area as areaName,loc.area_Id as areaId,loc.sequence,loc.roadway,loc.pallets,
        pl.ProductSku_Id as productSkuId,loc.category,loc.subcategory,loc.ischaosput,loc.ischaosbatch,loc.ischaosbatch,
        loc.locationCapacity, loc.State, loc.businessType,
        pl.id, pl.CreateTime, pl.lastUpdateTime
        FROM productlocation pl
        INNER JOIN location loc ON loc.id = pl.Location_Id
        WHERE pl.warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND loc.warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and pl.ProductSku_Id IN
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findLocationByLocationIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO">
        select loc.id as locationId,loc.name as locationName,loc.city_Id as cityId,loc.warehouse_Id as warehouseId,
        loc.area as areaName,loc.area_Id as areaId,loc.sequence,loc.roadway,loc.pallets,
        pl.ProductSku_Id as productSkuId, sku.Name as productName,
        loc.category,loc.subcategory,loc.ischaosput,loc.ischaosbatch,loc.ischaosbatch,
        loc.locationCapacity
        from location loc
        left join productlocation pl ON loc.id = pl.Location_Id
        left join productsku sku on pl.ProductSku_Id = sku.ProductSku_Id
        WHERE loc.warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and loc.id IN
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectBySkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productlocation where
        ProductSku_Id in
        <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="countHavingSku" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from productlocation where location_id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findByLocationIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productlocation
        where location_id in
        <foreach collection="list" item="locationId" open="(" separator="," close=")">
            #{locationId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findLocationItemByCon"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO">
        select loc.id as locationId,loc.name as locationName,loc.city_Id as cityId,loc.warehouse_Id as warehouseId,
        loc.area as areaName,loc.area_Id as areaId,loc.sequence,
        pl.ProductSku_Id as productSkuId,loc.category,loc.subcategory
        FROM productlocation pl
        INNER JOIN
        (select locL.id,locL.name,locL.area_Id,locL.area,locL.category,locL.subcategory,locL.warehouse_id,locL.city_Id,
        locL.sequence
        from location locL
        inner join location locArea on locL.area_id = locArea.id
        where locArea.subcategory in
        <foreach collection="dto.subcategorys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and locArea.warehouse_id = #{dto.warehouseId}
        union all
        select
        locArea.id,locArea.name,locArea.area_Id,locArea.area,locArea.category,locArea.subcategory,locArea.warehouse_id,locArea.city_Id,
        locArea.sequence
        from location locArea
        where locArea.subcategory in
        <foreach collection="dto.subcategorys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and locArea.warehouse_id = #{dto.warehouseId}
        ) as loc ON loc.id = pl.Location_Id
        where pl.warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        and pl.ProductSku_Id IN
        <foreach item="item" collection="dto.productSkuIds" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findNoneRefProductLocation"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.AbnormalLocationBO">
        select distinct lc.Warehouse_Id as warehouseId, lc.name as locationName, lc.subcategory as subCategory
        from location lc
        left join productlocation plc
        on lc.Id = plc.Location_Id
        where plc.Id is null
        and lc.State = 1
        and lc.category = 0
        and lc.subcategory in (0, 24)
        and lc.Warehouse_Id in
        <foreach collection="collection" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="findNoLocationSku"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.AbnormalLocationBO">
        SELECT distinct sku.ProductSku_Id as skuId, ps.Warehouse_Id as warehouseId
        FROM sc_product.productstore ps
        INNER JOIN sc_product.productsku sku ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND sku.City_Id = ps.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        LEFT JOIN sc_product.productlocation plc on sku.ProductSku_Id = plc.ProductSku_Id
        where plc.Id is null
        and ps.Warehouse_Id in
        <foreach collection="collection" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
        and ps.TotalCount_MinUnit > 0
    </select>

    <select id="findNoLocationSkuBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.location.AbnormalLocationBO">
        select distinct sku.ProductSku_Id as skuId, sku.Name as productName
        from productsku sku
        inner join productinfocategory ic on sku.ProductInfo_Id = ic.ProductInfo_Id
        left join productlocation plc on sku.ProductSku_Id = plc.ProductSku_Id
        where sku.ProductSku_Id in
        <foreach collection="param1" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="param2 != null and param2.size() != 0">
            and ic.StatisticsClass not in
            <foreach collection="param2" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        and plc.Id is null
    </select>

    <select id="findProductLocationByCondition"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO">
        SELECT distinct
        loc.id AS locationId,
        loc.NAME AS locationName,
        sku.ProductSku_Id as productSkuId,
        sku.name as productName,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        sku.ProductState as productState,
        area.id AS locationAreaId,
        area.NAME AS locationAreaName,
        pl.Warehouse_Id as warehouseId,
        COALESCE(loc.BusinessType, area.BusinessType,vesselarea.BusinessType) as businessType
        FROM productlocation pl
        INNER JOIN productsku sku on sku.productsku_id = pl.productsku_id
        INNER JOIN location loc on loc.id = pl.Location_Id
        INNER JOIN location area on area.id = loc.area_id
        LEFT JOIN location vesselarea ON area.area_Id = vesselarea.Id
        where pl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="locationAreaList != null and locationAreaList.size() > 0">
            and (
            loc.Area in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or
            loc.`Name` in
            <foreach collection="locationAreaList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="roadWayList != null and roadWayList.size() > 0">
            and loc.RoadWay in
            <foreach collection="roadWayList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="deleted != null and deleted.size > 0">
            and sku.IsDelete in
            <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="productParams != null and productParams.size > 0">
            and
            <foreach collection="productParams" index="index" item="item" separator="or" open="(" close=")">
                (
                sku.ProductSpecification_Id = #{item.specificationId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    AND sku.Company_Id is null
                </if>
                <if test="item.ownerId != null">
                    AND sku.Company_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                <if test="item.secOwnerId == null">
                    AND sku.secOwner_Id is null
                </if>
                <if test="item.secOwnerId != null">
                    AND sku.secOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
    </select>
</mapper>
