<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductRelationGroupPOMapper">

    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="GroupId" property="groupId" jdbcType="BIGINT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id, ProductSku_Id, GroupId, CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from productrelationgroup
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectExistProductGroupInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO">
        SELECT
        proGroup.Id as id, proGroup.City_Id as cityId, proGroup.Warehouse_Id as warehouseId, proGroup.ProductSku_Id as
        productSkuId, proSku.ProductSpecification_Id as productSpecificationId, proSku.Name as productName,
        proGroup.GroupId as groupId, proGroup.CreateUser as createUser, proGroup.CreateTime as createTime,
        proGroup.LastUpdateUser as lastUpdateUser, proGroup.LastUpdateTime as lastUpdateTime
        from productrelationgroup proGroup
        INNER JOIN productsku proSku on proGroup.ProductSku_Id = proSku.ProductSku_Id
        INNER JOIN (
        <!--
            1、通过传入sku找到同城市通规格ID的所有skuId
            2、通过 1 中查询出来的 sku 找到对应的分组信息
            3、通过 2 中分组信息查询出所有分组产品
         -->
        SELECT
        relgoup.Warehouse_Id as warehouseId, relgoup.GroupId as groupId
        from productrelationgroup relgoup
        INNER join (
        SELECT
        sku.City_Id, sku.ProductSku_Id, sku.ProductSpecification_Id, sku.Name
        from productsku sku INNER JOIN (
        SELECT
        City_Id, ProductSku_Id, ProductSpecification_Id, Name
        from productsku where ProductSku_Id IN
        <foreach collection="skuIdList" item="skuId" open="(" close=")" separator=",">
            #{skuId,jdbcType=BIGINT}
        </foreach>
        ) filSku on sku.City_Id = filSku.City_Id and sku.ProductSpecification_Id = filSku.ProductSpecification_Id
        ) rsSku on relgoup.ProductSku_Id = rsSku.ProductSku_Id
        where relgoup.Warehouse_Id IN
        <foreach collection="warehouseIdList" item="warehouseId" open="(" close=")" separator=",">
            #{warehouseId,jdbcType=INTEGER}
        </foreach>
        group by relgoup.Warehouse_Id, relgoup.GroupId
        ) tmpGroup on proGroup.Warehouse_Id = tmpGroup.warehouseId and proGroup.GroupId = tmpGroup.groupId
        where proGroup.Warehouse_Id IN
        <foreach collection="warehouseIdList" item="warehouseId" open="(" close=")" separator=",">
            #{warehouseId,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectTotalGroupConfig" resultMap="BaseResultMap"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductTotalGroupQueryDTO">
        select
        prl.Id, prl.City_Id, prl.Warehouse_Id, prl.ProductSku_Id, prl.GroupId, prl.CreateUser, prl.CreateTime,
        prl.LastUpdateUser,
        prl.LastUpdateTime
        from productrelationgroup prl
        inner join (
        select
        Warehouse_Id, GroupId
        from productrelationgroup
        where ProductSku_Id IN
        <foreach collection="skuIdList" item="skuId" open="(" close=")" separator=",">
            #{skuId,jdbcType=BIGINT}
        </foreach>
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        group BY Warehouse_Id, GroupId
        ) prr on prl.Warehouse_Id = prr.Warehouse_Id and prl.GroupId = prr.GroupId
        where prl.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="findYkAndJpSameSpecProducts"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productrelationgroup.ProductGroupInitQueryResultDTO">
        SELECT
        sku.City_Id as cityId, cfg.Warehouse_Id as warehouseId, sku.ProductSku_Id as productSkuId,
        sku.ProductSpecification_Id as productSpecificationId
        FROM
        productsku sku
        -- 仓库产品配置表
        INNER JOIN productskuconfig cfg ON sku.ProductSku_Id = cfg.ProductSku_Id
        -- 仓库表
        inner join warehouse w on w.id = cfg.Warehouse_id and w.City_Id = sku.City_id and w.warehouseType = 6
        -- 关联产品表(排除已经存在关联关系)
        left join productrelationgroup rel on rel.City_Id = sku.City_Id and rel.Warehouse_Id = cfg.Warehouse_Id and
        rel.ProductSku_Id = sku.ProductSku_Id
        where w.warehouseType = 6
        <if test="warehouseIdList != null and warehouseIdList.size() > 0">
            and w.id in
            <foreach collection="warehouseIdList" item="warehouseId" open="(" close=")" separator=",">
                #{warehouseId,jdbcType=INTEGER}
            </foreach>
        </if>
        and rel.id is null
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productrelationgroup
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByPrimaryKeyList" parameterType="java.util.List">
        delete from productrelationgroup
        where Id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO">
        insert into productrelationgroup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="groupId != null">
                GroupId,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=BIGINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="insertOrUpdateBatchList">
        INSERT INTO productrelationgroup
        (
        id, City_Id, Warehouse_Id, ProductSku_Id, GroupId,
        CreateUser, LastUpdateUser
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.cityId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.productSkuId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT},
            #{item.createUser,jdbcType=VARCHAR}, #{item.lastUpdateUser,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        GroupId = VALUES(GroupId)
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductRelationGroupPO">
        update productrelationgroup
        <set>
            <if test="cityId != null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="groupId != null">
                GroupId = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <select id="countRelationByWarehouseId" resultType="java.lang.Integer">
        select COUNT (*) from productrelationgroup where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>
</mapper>