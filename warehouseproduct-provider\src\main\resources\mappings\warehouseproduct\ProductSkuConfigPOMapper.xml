<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuConfigPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuConfigPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="TINYINT" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="Unpackage" jdbcType="TINYINT" property="unpackage"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="MaxInventory" jdbcType="DECIMAL" property="maxInventory"/>
        <result column="MinInventory" jdbcType="DECIMAL" property="minInventory"/>
        <result column="MaxReplenishment" jdbcType="DECIMAL" property="maxReplenishment"/>
        <result column="MinReplenishment" jdbcType="DECIMAL" property="minReplenishment"/>
        <result column="isComplete" jdbcType="TINYINT" property="isComplete"/>
        <result column="StorageType" jdbcType="TINYINT" property="storageType"/>
        <result column="IsPick" jdbcType="TINYINT" property="pick"/>
        <result column="IsSow" jdbcType="TINYINT" property="sow"/>
        <result column="InventoryRatio" jdbcType="VARCHAR" property="inventoryRatio"/>
        <result column="IsFleeGoods" jdbcType="TINYINT" property="fleeGoods"/>
        <result column="ProductRelevantState" jdbcType="TINYINT" property="productRelevantState"/>
        <result column="IsUnique" jdbcType="TINYINT" property="unique"/>
        <result column="productGrade" jdbcType="TINYINT" property="productGrade"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="SellingPrice" jdbcType="DECIMAL" property="sellingPrice"/>
        <result column="SellingPriceUnit" jdbcType="VARCHAR" property="sellingPriceUnit"/>
        <result column="DistributionPercent" jdbcType="DECIMAL" property="distributionPercent"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="StoreState" jdbcType="TINYINT" property="storeState"/>
        <result column="SaleState" jdbcType="TINYINT" property="saleState"/>
        <result column="UnifySkuId" jdbcType="BIGINT" property="unifySkuId"/>
        <result column="inventoryPinProperty" jdbcType="VARCHAR" property="inventoryPinProperty"/>
        <result column="palletQuantity" jdbcType="DECIMAL" property="palletQuantity"/>
        <result column="BusinessTag" jdbcType="TINYINT" property="businessTag"/>
        <result column="StorageAttribute" jdbcType="TINYINT" property="storageAttribute"/>
        <result column="ProductStorageMaxAge" jdbcType="INTEGER" property="productStorageMaxAge"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Warehouse_Id, ProductSku_Id, warehouseCustodyFee, DeliveryFee, DeliveryPayType,
        sortingFee, Unpackage, ProductFeature, MaxInventory, MinInventory, MaxReplenishment,
        MinReplenishment, isComplete, StorageType, IsPick, IsSow, InventoryRatio, IsFleeGoods,
        ProductRelevantState, IsUnique, productGrade, Remark, CreateTime, CreateUser_Id,
        LastUpdateTime, LastUpdateUser_Id, CostPrice, SellingPrice, SellingPriceUnit, DistributionPercent,
        DistributionPercentForAmount, State, StoreState, SaleState, UnifySkuId, inventoryPinProperty,
        palletQuantity, BusinessTag, StorageAttribute, ProductStorageMaxAge
    </sql>

    <select id="findSkuConfigById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findDisableSkuConfigBySkuIdsAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskuconfig
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and State = 0
        <if test="productSkuIds != null and productSkuIds.size() > 0">
            and ProductSku_Id in
            <foreach collection="productSkuIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <update id="enableProductSku">
        update productskuconfig set State = 1
        where id in
        <foreach collection="configIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="disableProductSku">
        update productskuconfig set State = 0
        where id in
        <foreach collection="configIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getSkuOffShelfAndStockZeroThreeMonthsAgoMaxId" resultType="java.lang.Long">
        select Max(id) AS max_id
        from (SELECT csku.id, sum(ps.TotalCount_MinUnit) TotalCount
        FROM productsku sku
        INNER JOIN productskuconfig csku ON sku.ProductSku_Id = csku.ProductSku_Id
        INNER JOIN productstore ps ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((sku.Company_Id IS NULL AND ps.Owner_Id IS NULL) OR (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        AND sku.City_Id = ps.City_Id
        WHERE sku.ProductState in (0, 1) # 产品状态,下架(0), 作废(1),
        AND csku.State = 1 # 状态 1: 启用
        AND csku.Warehouse_Id = #{warehouseId, jdbcType=INTEGER}
        group by csku.id
        having sum(ps.TotalCount_MinUnit) = 0 and max(ps.lastupdatetime) &lt; DATE_SUB(CURDATE(), INTERVAL 90 DAY)) tmp
    </select>

    <select id="findSkuOffShelfAndStockZeroThreeMonthsAgo" resultType="java.lang.Long">
        SELECT csku.id
        FROM productsku sku
        INNER JOIN productskuconfig csku ON sku.ProductSku_Id = csku.ProductSku_Id
        INNER JOIN productstore ps ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((sku.Company_Id IS NULL AND ps.Owner_Id IS NULL) OR (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        AND sku.City_Id = ps.City_Id
        WHERE sku.ProductState in (0, 1) # 产品状态,下架(0), 作废(1),
        AND csku.State = 1 # 状态 1: 启用
        AND csku.Warehouse_Id = #{warehouseId, jdbcType=INTEGER}
        AND csku.id &lt;= #{lastMinId,jdbcType=INTEGER}
        group by csku.id
        having sum(ps.TotalCount_MinUnit) = 0 and max(ps.lastupdatetime) &lt; DATE_SUB(CURDATE(), INTERVAL 90 DAY)
        ORDER BY id DESC
        LIMIT 5000
    </select>
</mapper>