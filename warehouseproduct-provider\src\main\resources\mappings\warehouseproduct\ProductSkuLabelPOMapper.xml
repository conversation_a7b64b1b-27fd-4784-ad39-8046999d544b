<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuLabelMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="LabelType" jdbcType="TINYINT" property="labelType"/>
        <result column="Label_Id" jdbcType="VARCHAR" property="labelId"/>
        <result column="LabelName" jdbcType="VARCHAR" property="labelName"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, ProductSku_Id, LabelType, Label_Id, LabelName, Remark,
        CreateTime, CreateUser_Id, LastUpdateTime, LastUpdateUser_Id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskulabel
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productskulabel
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO">
        insert into productskulabel (Id, Org_Id, Warehouse_Id,
        ProductSku_Id, LabelType, Label_Id,
        LabelName, Remark, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id
        )
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{productSkuId,jdbcType=BIGINT}, #{labelType,jdbcType=TINYINT}, #{labelId,jdbcType=VARCHAR},
        #{labelName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO">
        insert into productskulabel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSkuId != null">
                ProductSku_Id,
            </if>
            <if test="labelType != null">
                LabelType,
            </if>
            <if test="labelId != null">
                Label_Id,
            </if>
            <if test="labelName != null">
                LabelName,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="labelType != null">
                #{labelType,jdbcType=TINYINT},
            </if>
            <if test="labelId != null">
                #{labelId,jdbcType=VARCHAR},
            </if>
            <if test="labelName != null">
                #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO">
        update productskulabel
        <set>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSkuId != null">
                ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
            </if>
            <if test="labelType != null">
                LabelType = #{labelType,jdbcType=TINYINT},
            </if>
            <if test="labelId != null">
                Label_Id = #{labelId,jdbcType=VARCHAR},
            </if>
            <if test="labelName != null">
                LabelName = #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuLabelPO">
        update productskulabel
        set Org_Id = #{orgId,jdbcType=INTEGER},
        Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
        ProductSku_Id = #{productSkuId,jdbcType=BIGINT},
        LabelType = #{labelType,jdbcType=TINYINT},
        Label_Id = #{labelId,jdbcType=VARCHAR},
        LabelName = #{labelName,jdbcType=VARCHAR},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser_Id = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listProductSkuLabel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskulabel
        <where>
            <if test="orgId != null">
                Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productSkuIds != null">
                and ProductSku_Id in
                <foreach collection="productSkuIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="labelType != null">
                and LabelType = #{labelType,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="getProductSkuLabelByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productskulabel
        where Org_Id = #{orgId,jdbcType=INTEGER}
        and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
        and LabelType = #{labelType,jdbcType=TINYINT}
        limit 1
    </select>
</mapper>