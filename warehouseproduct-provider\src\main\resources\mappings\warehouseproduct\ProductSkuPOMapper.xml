<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecification_Id"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSku_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="Company_Id" jdbcType="BIGINT" property="company_Id"/>
        <result column="SaleModel" jdbcType="TINYINT" property="saleModel"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="TINYINT" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="ProductBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="ProductState" jdbcType="TINYINT" property="productState"/>
        <result column="Unpackage" jdbcType="TINYINT" property="unpackage"/>
        <result column="MonthOfShelfLife" jdbcType="INTEGER" property="monthOfShelfLife"/>
        <result column="ShelfLifeUnit" jdbcType="TINYINT" property="shelfLifeUnit"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="MaxInventory" jdbcType="DECIMAL" property="maxInventory"/>
        <result column="MinInventory" jdbcType="DECIMAL" property="minInventory"/>
        <result column="MaxReplenishment" jdbcType="DECIMAL" property="maxReplenishment"/>
        <result column="MinReplenishment" jdbcType="DECIMAL" property="minReplenishment"/>
        <result column="isComplete" jdbcType="TINYINT" property="isComplete"/>
        <result column="StorageType" jdbcType="TINYINT" property="storageType"/>
        <result column="IsPick" jdbcType="TINYINT" property="pick"/>
        <result column="IsSow" jdbcType="TINYINT" property="sow"/>
        <result column="IsUnique" jdbcType="TINYINT" property="unique"/>
        <result column="IsFleeGoods" jdbcType="TINYINT" property="fleeGoods"/>
        <result column="StatisticsClass" jdbcType="BIGINT" property="statisticsClassId"/>
        <result column="Replace_To_SkuId" jdbcType="BIGINT" property="replaceToSkuId"/>
        <result column="packageUnifyPackageId" jdbcType="BIGINT" property="packageUnifyPackageId"/>
        <result column="unitUnifyPackageId" jdbcType="BIGINT" property="unitUnifyPackageId"/>
        <result column="secOwnerId" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="IsDelete" jdbcType="TINYINT" property="isDelete"/>
        <result column="ProductInfoCategory_Id" jdbcType="INTEGER" property="productInfoCategoryId"/>

    </resultMap>

    <resultMap id="productInfoMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO">
        <result column="Name" jdbcType="VARCHAR" property="productName"/>
        <result column="City_Id" jdbcType="INTEGER" property="cityId"/>
        <result column="SaleModel" jdbcType="INTEGER" property="saleModel"/>
        <result column="Company_Id" jdbcType="BIGINT" property="companyId"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="skuId"/>
        <result column="distributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id
        , City_Id, ProductSpecification_Id, ProductSku_Id, Name, Sequence, Remo,
        CreateTime, CreateUserId, LastUpdateTime, LastUpdateUserId, Company_Id, SaleModel,
        distributionPercentForAmount, specificationName, packageName, unitName, packageQuantity,
        Source, warehouseCustodyFee, DeliveryFee, DeliveryPayType, sortingFee,
        ProductInfo_Id, productBrand, OwnerName, ProductState, Unpackage, MonthOfShelfLife, ShelfLifeUnit,
        ProductFeature,
        MaxInventory,MinInventory,MaxReplenishment,MinReplenishment,isComplete,StorageType, IsPick, IsSow, IsUnique,
        IsFleeGoods, secOwner_Id as secOwnerId, IsDelete, ProductInfoCategory_Id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update productsku
        set IsDelete = 1
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        insert into productsku (Id, City_Id, ProductSpecification_Id,
        ProductSku_Id, Name, Sequence,
        SaleModel, Company_Id, Remo,
        CreateTime, CreateUserId, LastUpdateTime,
        LastUpdateUserId, OwnerName, IsDelete)
        values (#{id,jdbcType=BIGINT}, #{city_Id,jdbcType=INTEGER}, #{productSpecification_Id,jdbcType=BIGINT},
        #{productSku_Id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{sequence,jdbcType=INTEGER},
        #{saleModel,jdbcType=TINYINT}, #{company_Id,jdbcType=BIGINT}, #{remo,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{lastUpdateUserId,jdbcType=INTEGER}, #{ownerName,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        insert into productsku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="city_Id != null">
                City_Id,
            </if>
            <if test="productSpecification_Id != null">
                ProductSpecification_Id,
            </if>
            <if test="productSku_Id != null">
                ProductSku_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sequence != null">
                Sequence,
            </if>
            <if test="saleModel != null">
                SaleModel,
            </if>
            <if test="company_Id != null">
                Company_Id,
            </if>
            <if test="remo != null">
                Remo,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
            <if test="ownerName != null">
                OwnerName,
            </if>
            <if test="isDelete != null">
                IsDelete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="city_Id != null">
                #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="productSpecification_Id != null">
                #{productSpecification_Id,jdbcType=BIGINT},
            </if>
            <if test="productSku_Id != null">
                #{productSku_Id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=INTEGER},
            </if>
            <if test="saleModel != null">
                #{saleModel,jdbcType=TINYINT},
            </if>
            <if test="company_Id != null">
                #{company_Id,jdbcType=BIGINT},
            </if>
            <if test="remo != null">
                #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        update productsku
        <set>
            <if test="city_Id != null">
                City_Id = #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="productSpecification_Id != null">
                ProductSpecification_Id = #{productSpecification_Id,jdbcType=BIGINT},
            </if>
            <if test="productSku_Id != null">
                ProductSku_Id = #{productSku_Id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                Sequence = #{sequence,jdbcType=INTEGER},
            </if>
            <if test="saleModel != null">
                SaleModel = #{saleModel,jdbcType=TINYINT},
            </if>
            <if test="company_Id != null">
                Company_Id = #{company_Id,jdbcType=BIGINT},
            </if>
            <if test="remo != null">
                Remo = #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="productFeature != null">
                productFeature = #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                maxInventory = #{maxInventory,jdbcType=DECIMAL},
            </if>
            <if test="minInventory != null">
                minInventory = #{minInventory,jdbcType=DECIMAL},
            </if>
            <if test="maxReplenishment != null">
                maxReplenishment = #{maxReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="minReplenishment != null">
                minReplenishment = #{minReplenishment,jdbcType=DECIMAL},
            </if>
            <if test="isComplete != null">
                isComplete = #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="ownerName != null">
                OwnerName = #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IsDelete = #{isDelete,jdbcType=BOOLEAN},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        update productsku
        set City_Id = #{city_Id,jdbcType=INTEGER},
        ProductSpecification_Id = #{productSpecification_Id,jdbcType=BIGINT},
        ProductSku_Id = #{productSku_Id,jdbcType=BIGINT},
        Name = #{name,jdbcType=VARCHAR},
        Sequence = #{sequence,jdbcType=INTEGER},
        SaleModel = #{saleModel,jdbcType=TINYINT},
        Company_Id = #{company_Id,jdbcType=BIGINT},
        Remo = #{remo,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUserId = #{createUserId,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
        OwnerName = #{ownerName,jdbcType=VARCHAR}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <!--根据城市id,产品名称查询产品列表.-->
    <select id="getProductSkuInfo" resultMap="productInfoMap">
        SELECT `Name`,
        City_Id,
        SaleModel,
        Company_Id,
        ProductSku_Id,
        distributionPercentForAmount,
        OwnerName
        from productsku
        <where>
            <if test="dto.cityId != null">
                and City_Id = #{dto.cityId,jdbcType=VARCHAR}
            </if>
            <if test="dto.productName != null and dto.productName != '' and dto.precise == 0">
                and `Name` like concat('%', concat(#{dto.productName,jdbcType=VARCHAR}), '%')
            </if>
            <if test="dto.productName != null and dto.productName != '' and dto.precise == 1">
                and `Name` = #{dto.productName,jdbcType=VARCHAR}
            </if>
            <if test="dto.productSkuId != null">
                and ProductSku_Id = #{dto.productSkuId}
            </if>
        </where>
        order by id
    </select>

    <select id="countProductInPeriod" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(*)
        from productsku
        WHERE CreateTime BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    </select>

    <!--查询所有prouductSkuId-->
    <select id="listProductSkuId" resultType="java.lang.Long">
        select distinct ProductSku_Id
        from productsku
    </select>

    <!--更新产品信息id-->
    <update id="updateProductInfoId">
        update productsku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ProductInfo_Id =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.productInfoId}
                </foreach>
            </trim>
            <trim prefix="productBrand =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.productBrand}
                </foreach>
            </trim>
            <trim prefix="ProductState =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.productState}
                </foreach>
            </trim>
            <trim prefix="OwnerName =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.ownerName}
                </foreach>
            </trim>
            <trim prefix="Unpackage =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.unpackage}
                </foreach>
            </trim>
            <trim prefix="ShelfLifeUnit =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.shelfLifeUnit}
                </foreach>
            </trim>
            <trim prefix="MonthOfShelfLife =case" suffix="end,">
                <foreach collection="list" item="item">
                    when ProductSku_Id=#{item.productSku_Id} then #{item.monthOfShelfLife}
                </foreach>
            </trim>
        </trim>
        where ProductSku_Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.productSku_Id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!--获取商品详情-->
    <select id="listProductSkuInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        select
        distinct sku.Id as id,
        sku.City_Id as orgId,
        sku.ProductSku_Id as productSkuId,
        sku.Name as productName,
        sku.SaleModel as saleModel,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.specificationName as specName,
        sku.packageQuantity as specQuantity,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.productBrand as productBrand,
        sku.ProductState as productState,
        sku.OwnerName as ownerName,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        cg.StatisticsClass as statisticsClass,
        cg.StatisticsClassName as statisticsClassName,
        cg.SecondStatisticsClass as secondStatisticsClass,
        cg.SecondStatisticsClassName as secondStatisticsClassName,
        sku.Unpackage,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductInfo_Id as productInfoId,
        sku.Source as source,
        sku.StorageType,
        sku.ProductType
        from productsku sku
        left join productinfocategory cg on sku.ProductInfoCategory_Id = cg.Id

        <if test="limitSku != null and limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
            <if test="warehouseId != null">
                and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                and (pc.storageAttribute = #{warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>

        <if test="limitSku == null or limitSku != 1">
            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
                <if test="warehouseId != null">
                    and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
                </if>
                and (pc.storageAttribute = #{warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or
                pc.StorageAttribute is null)
            </if>
        </if>

        <if test="(allWarehouse != null and allWarehouse == 1) or hasRealStoreType != null">
            INNER JOIN productstore ps ON sku.City_Id = ps.City_Id
            and sku.ProductSpecification_Id = ps.ProductSpecification_Id
            <if test="eraseOwnerId == null or eraseOwnerId == 0">
                AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
                AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="hasRealStoreType != null and hasRealStoreType == 1">
                and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
            </if>
            <if test="hasRealStoreType != null and hasRealStoreType == 0">
                and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
            </if>
            <if test="hasRealStoreType != null and hasRealStoreType == -1">
                and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
            </if>
        </if>
        <where>
            <if test="isDelete != null">
                sku.IsDelete = #{isDelete,jdbcType=TINYINT}
            </if>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null and productBrand != ''">
                and sku.productBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="ownerId != null and ownerId != 0">
                and sku.Company_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="ownerId != null and ownerId == 0">
                and sku.Company_Id is null
            </if>
            <if test="excludeOwnerIds != null and excludeOwnerIds.size() > 0">
                and (sku.Company_Id not in
                <foreach collection="excludeOwnerIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
                <if test="ownerId == null">
                    or sku.Company_Id is null)
                </if>
                <if test="ownerId != null and ownerId == 0">
                    or sku.Company_Id is null)
                </if>
                <if test="ownerId != null and ownerId != 0">
                    and sku.Company_Id = #{ownerId,jdbcType=BIGINT})
                </if>
            </if>
            <if test="ownerName != null and ownerName != ''">
                and sku.OwnerName like concat('%',#{ownerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="saleModelList != null and saleModelList.size() > 0">
                and sku.SaleModel in
                <foreach collection="saleModelList" item="saleModel" separator="," open="(" close=")">
                    #{saleModel,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="productStateList != null and productStateList.size() > 0">
                and sku.ProductState in
                <foreach collection="productStateList" item="productState" separator="," open="(" close=")">
                    #{productState,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="statisticsClassList != null and statisticsClassList.size() > 0">
                and cg.StatisticsClassName in
                <foreach collection="statisticsClassList" item="statisticsClass" separator="," open="(" close=")">
                    #{statisticsClass,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                and sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="productSkuId" separator="," open="(" close=")">
                    #{productSkuId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="storageType != null">
                and sku.StorageType = #{storageType,jdbcType=TINYINT}
            </if>
            <if test="productNameList != null and productNameList.size() > 0">
                and sku.Name in
                <foreach collection="productNameList" item="productName" separator="," open="(" close=")">
                    #{productName,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productTypeList != null and productTypeList.size() > 0">
                and sku.productType in
                <foreach collection="productTypeList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="excludeProductTypeList != null and excludeProductTypeList.size() > 0">
                and sku.productType not in
                <foreach collection="excludeProductTypeList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            order by sku.Name desc
        </where>
    </select>

    <!--获取商品详情（有库存的商品）-->
    <select id="listProductSkuInfoIncludeStore"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        SELECT
        sku.Id as id,
        sku.City_Id as orgId,
        sku.ProductSku_Id as productSkuId,
        sku.Name as productName,
        sku.SaleModel as saleModel,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.specificationName as specName,
        sku.packageQuantity as specQuantity,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.productBrand as productBrand,
        sku.ProductState as productState,
        sku.OwnerName as ownerName,
        sku.Company_Id as companyId,
        sku.Source as source,
        cg.StatisticsClass as statisticsClass,
        cg.StatisticsClassName as statisticsClassName,
        cg.SecondStatisticsClass as secondStatisticsClass,
        cg.SecondStatisticsClassName as secondStatisticsClassName,
        sku.Unpackage,
        ifnull(pi.MonthOfShelfLife, sku.MonthOfShelfLife) as monthOfShelfLife,
        ifnull(pi.ShelfLifeUnit, sku.ShelfLifeUnit) as shelfLifeUnit
        from productsku sku
        INNER JOIN productstore ps ON sku.City_Id = ps.City_Id
        and sku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
            AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        </if>
        LEFT JOIN productinfocategory cg on sku.ProductInfoCategory_Id = cg.Id
        left join productinfo pi on pi.id = sku.productinfo_id

        <if test="warehouseId != null">
            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                inner JOIN productskuconfig cfg ON cfg.ProductSku_Id = sku.ProductSku_Id
                AND cfg.Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and cfg.State = 1
                and cfg.storageAttribute = #{warehouseAllocationType, jdbcType=INTEGER}
            </if>
        </if>

        <where>
            ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productName != null">
                and sku.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null">
                and sku.productBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="ownerName != null">
                and sku.OwnerName like concat('%',#{ownerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="saleModelList != null and saleModelList.size() > 0">
                and sku.SaleModel in
                <foreach collection="saleModelList" item="saleModel" separator="," open="(" close=")">
                    #{saleModel,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="productStateList != null and productStateList.size() > 0">
                and sku.ProductState in
                <foreach collection="productStateList" item="productState" separator="," open="(" close=")">
                    #{productState,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="productSkuIdList != null and productSkuIdList.size() > 0">
                and sku.ProductSku_Id in
                <foreach collection="productSkuIdList" item="productSkuId" separator="," open="(" close=")">
                    #{productSkuId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="statisticsClassList != null and statisticsClassList.size() > 0">
                and cg.StatisticsClassName in
                <foreach collection="statisticsClassList" item="statisticsClass" separator="," open="(" close=")">
                    #{statisticsClass,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="statisticsClassIds != null and statisticsClassIds.size() > 0">
                and cg.StatisticsClass in
                <foreach collection="statisticsClassIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="secondStatisticsClassIds != null and secondStatisticsClassIds.size() > 0">
                and cg.SecondStatisticsClass in
                <foreach collection="secondStatisticsClassIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by
        <if test="productName != null or productBrand != null or ownerName != null or (productSkuIdList != null and productSkuIdList.size() > 0)">
            ps.TotalCount_MinUnit DESC,
        </if>
        sku.ProductSku_Id
    </select>

    <!--根据产品skuId查询产品信息-->
    <select id="listProductSkuInfoBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        select
        sku.Id as id,
        sku.City_Id as orgId,
        sku.ProductSku_Id as productSkuId,
        sku.Name as productName,
        sku.SaleModel as saleModel,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.specificationName as specName,
        sku.packageQuantity as specQuantity,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.productBrand as productBrand,
        sku.ProductState as productState,
        sku.OwnerName as ownerName,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        cg.StatisticsClass as statisticsClass,
        cg.StatisticsClassName as statisticsClassName,
        cg.SecondStatisticsClass as secondStatisticsClass,
        cg.SecondStatisticsClassName as secondStatisticsClassName,
        sku.Unpackage,
        IFNULL(info.MonthOfShelfLife,sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit,sku.ShelfLifeUnit) as shelfLifeUnit,
        sku.Source as source,
        info.shelfLifeLongTime as shelfLifeLongTime,
        info.id as productInfoId
        from productsku sku
        left join productinfocategory cg on sku.ProductInfoCategory_Id = cg.Id
        left join productinfo info on sku.ProductInfo_Id=info.Id
        <where>
            sku.ProductSku_Id in
            <foreach collection="list" item="productSkuId" separator="," open="(" close=")">
                #{productSkuId,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>

    <!--根据产品skuId查询产品信息-->
    <select id="findProductSkuInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        select
        sku.Id as id,
        sku.City_Id as orgId,
        sku.ProductSku_Id as productSkuId,
        sku.Name as productName,
        sku.SaleModel as saleModel,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.specificationName as specName,
        sku.packageQuantity as specQuantity,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.productBrand as productBrand,
        sku.ProductState as productState,
        sku.OwnerName as ownerName,
        sku.Company_Id as companyId,
        sku.secOwner_Id as secOwnerId,
        cg.StatisticsClass as statisticsClass,
        cg.StatisticsClassName as statisticsClassName,
        cg.SecondStatisticsClass as secondStatisticsClass,
        cg.SecondStatisticsClassName as secondStatisticsClassName,
        sku.Unpackage,
        IFNULL(info.MonthOfShelfLife,sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit,sku.ShelfLifeUnit) as shelfLifeUnit,
        sku.Source as source,
        info.shelfLifeLongTime as shelfLifeLongTime,
        info.id as productInfoId
        from productsku sku
        left join productinfocategory cg on sku.ProductInfoCategory_Id = cg.Id
        left join productinfo info on sku.ProductInfo_Id=info.Id
        <where>
            sku.ProductSku_Id in
            <foreach collection="list" item="productSkuId" separator="," open="(" close=")">
                #{productSkuId,jdbcType=BIGINT}
            </foreach>
            <if test="cityId != null">
                and sku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="findProductSkuInfoBySkuId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM productsku
        WHERE ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
    </select>

    <select id="countProductByCity" resultType="java.lang.Integer">
        select count(0)
        from productsku sku
        <where>
            sku.City_Id = #{cityId,jdbcType=INTEGER}
        </where>
    </select>

    <!--根据产品skuId查询产品生产日期详情-->
    <select id="listProductionDate"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductionDateDTO">
        SELECT
        psb.productiondate as productionDate,
        sku.ProductSku_Id as productSkuId,
        sku.ProductSpecification_Id as productSpecificationId,
        psb.location_id as locationId,
        psb.location_name as locationName,
        ps.channel,
        psb.id as batchId,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        psb.totalcount_minunit as unitTotalCount,
        psb.productstore_id as productStoreId,
        psb.BatchAttributeInfoNo as batchAttributeInfoNo,
        loc.subcategory as locationSubcategory,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        sku.OwnerName as ownerName,
        ow.OwnerName as secOwnerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit
        FROM productstore ps
        INNER JOIN productstorebatch psb on ps.id = psb.productstore_id
        INNER JOIN productsku sku ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND sku.City_Id = ps.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        LEFT JOIN owner ow on ow.id = ps.SecOwner_Id
        where ps.totalcount_minunit != 0 and psb.totalcount_minunit != 0
        <if test="skuIds != null and skuIds.size() > 0">
            and sku.ProductSku_Id in
            <foreach collection="skuIds" item="productSkuId" separator="," open="(" close=")">
                #{productSkuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="warehouseId == null and cityId != null">
            and ps.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="locationIds != null and locationIds.size() > 0">
            and psb.location_Id in
            <foreach collection="locationIds" item="locationId" separator="," open="(" close=")">
                #{locationId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="pageListProcessProductSku"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProcessProductSkuDTO">
        SELECT
        ps.ProductSku_Id AS productSkuId,
        pss.Channel,
        psb.location_id AS locationId,
        psb.location_name AS locationName,
        SUM(psb.totalcount_minunit) AS totalCountMinUnit
        FROM
        productsku ps
        INNER JOIN productinfo pi ON pi.id = ps.ProductInfo_Id
        LEFT JOIN productstore pss ON pss.City_Id = ps.City_Id
        AND pss.ProductSpecification_Id = ps.ProductSpecification_Id
        AND pss.Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        LEFT JOIN productstorebatch psb ON pss.id = psb.productstore_id
        WHERE
        pi.IsProcess = 1
        AND ps.City_Id = #{queryDTO.orgId,jdbcType=INTEGER}
        <if test="queryDTO.productStatisticsClass != null">
            AND pi.ProductStatisticsClass = #{queryDTO.productStatisticsClass,jdbcType=BIGINT}
        </if>
        <if test="queryDTO.productName != null and queryDTO.productName != '' ">
            AND pi.ProductName LIKE concat('%',concat(#{queryDTO.productName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="queryDTO.status != null">
            AND pi.Status = #{queryDTO.status,jdbcType=TINYINT}
        </if>
        GROUP BY
        psb.location_id,
        ps.ProductSku_Id,
        pss.Channel,
        psb.location_name
    </select>

    <select id="listProcessProductSkuByInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProcessProductSkuDTO">
        SELECT
        ps.City_Id AS orgId,
        ps.ProductInfo_Id AS productInfoId,
        ps.ProductSku_Id AS productSkuId,
        ps.productBrand,
        ps.ProductSpecification_Id AS productSpecificationId,
        ps.Company_Id AS ownerId,
        ps.OwnerName,
        ps.Source,
        pss.Channel,
        pc.StatisticsClass,
        pc.StatisticsClassName,
        ps.`Name` as productName,
        ps.specificationName,
        psb.location_id AS locationId,
        psb.location_name AS locationName,
        psb.totalcount_minunit AS totalCountMinUnit,
        ps.ProductState,
        psb.productiondate AS productionDate,
        ps.packageName,
        ps.unitName,
        ps.packageQuantity,
        pis.ConvertProductInfoSpec_Id AS convertProductInfoSpecId,
        pis.ConvertSpecQuantity,
        psb.id AS productStoreBatchId
        FROM
        productsku ps
        INNER JOIN productinfospecification pis ON pis.ProductInfo_Id = ps.ProductInfo_Id
        AND pis.id = ps.ProductSpecification_Id
        LEFT JOIN productinfocategory pc ON pc.Id = ps.ProductInfoCategory_Id
        INNER JOIN productstore pss ON pss.City_Id = ps.City_Id
        AND pss.ProductSpecification_Id = ps.ProductSpecification_Id
        INNER JOIN productstorebatch psb ON pss.id = psb.productstore_id
        WHERE ps.City_Id = #{queryDTO.orgId,jdbcType=INTEGER}
        AND pss.Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        AND pis.ConvertProductInfoSpec_Id IN
        <foreach collection="queryDTO.productSkuQueryDTOS" item="item" open="(" close=")" separator=",">
            #{item.productSpecificationId,jdbcType=BIGINT}
        </foreach>
        UNION ALL
        SELECT
        ps.City_Id AS orgId,
        ps.ProductInfo_Id AS productInfoId,
        ps.ProductSku_Id AS productSkuId,
        ps.productBrand,
        ps.ProductSpecification_Id AS productSpecificationId,
        ps.Company_Id AS ownerId,
        ps.OwnerName,
        ps.Source,
        pss.Channel,
        pc.StatisticsClass,
        pc.StatisticsClassName,
        ps.`Name` as productName,
        ps.specificationName,
        psb.location_id AS locationId,
        psb.location_name AS locationName,
        psb.totalcount_minunit AS totalCountMinUnit,
        ps.ProductState,
        psb.productiondate AS productionDate,
        ps.packageName,
        ps.unitName,
        ps.packageQuantity,
        pis.ConvertProductInfoSpec_Id AS convertProductInfoSpecId,
        pis.ConvertSpecQuantity,
        psb.id AS productStoreBatchId
        FROM
        productsku ps
        INNER JOIN productinfospecification pis ON pis.ProductInfo_Id = ps.ProductInfo_Id
        AND pis.id = ps.ProductSpecification_Id
        LEFT JOIN productinfocategory pc ON pc.Id = ps.ProductInfoCategory_Id
        INNER JOIN productstore pss ON pss.City_Id = ps.City_Id
        AND pss.ProductSpecification_Id = ps.ProductSpecification_Id
        INNER JOIN productstorebatch psb ON pss.id = psb.productstore_id
        WHERE ps.City_Id = #{queryDTO.orgId,jdbcType=INTEGER}
        AND pss.Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        AND pis.Id IN
        <foreach collection="queryDTO.productSkuQueryDTOS" item="item" open="(" close=")" separator=",">
            #{item.convertProductInfoSpecId,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="listProcessProductSkuByInfoId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProcessProductSkuDTO">
        SELECT
        ps.City_Id AS orgId,
        ps.ProductInfo_Id AS productInfoId,
        ps.ProductSku_Id AS productSkuId,
        ps.productBrand,
        ps.ProductSpecification_Id AS productSpecificationId,
        ps.Company_Id AS ownerId,
        ps.OwnerName,
        ps.Source,
        pss.Channel,
        pc.StatisticsClass,
        pc.StatisticsClassName,
        ps.`Name` as productName,
        ps.specificationName,
        psb.location_id AS locationId,
        psb.location_name AS locationName,
        psb.totalcount_minunit AS totalCountMinUnit,
        ps.ProductState,
        psb.productiondate AS productionDate,
        ps.packageName,
        ps.unitName,
        ps.packageQuantity,
        pis.ConvertProductInfoSpec_Id AS convertProductInfoSpecId,
        pis.ConvertSpecQuantity,
        psb.id AS productStoreBatchId
        FROM
        productsku ps
        INNER JOIN productinfospecification pis ON pis.ProductInfo_Id = ps.ProductInfo_Id
        AND pis.id = ps.ProductSpecification_Id
        left JOIN productinfocategory pc ON pc.Id = ps.ProductInfoCategory_Id
        left JOIN productstore pss ON pss.City_Id = ps.City_Id
        AND pss.ProductSpecification_Id = ps.ProductSpecification_Id
        AND pss.Warehouse_Id = #{queryDTO.warehouseId,jdbcType=INTEGER}
        left JOIN productstorebatch psb ON pss.id = psb.productstore_id
        WHERE ps.City_Id = #{queryDTO.orgId,jdbcType=INTEGER}
        <if test="queryDTO.productInfoId != null">
            AND ps.ProductInfo_Id = #{queryDTO.productInfoId}
        </if>
        <if test="queryDTO.productSkuId != null">
            AND ps.ProductSku_Id = #{queryDTO.productSkuId}
        </if>
        <if test="queryDTO.productSpecificationId != null">
            AND ps.ProductSpecification_Id = #{queryDTO.productSpecificationId}
        </if>
    </select>

    <select id="findLocationPruductsByBatchInfo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        select
        sku.Id as id,
        sku.City_Id as city_Id,
        sku.ProductSpecification_Id as productSpecification_Id,
        sku.ProductSku_Id as productSku_Id,
        sku.name as name,
        sku.Sequence as sequence,
        sku.Remo as remo,
        sku.CreateTime as createTime,
        sku.CreateUserId as createUserId,
        sku.LastUpdateTime as lastUpdateTime,
        sku.LastUpdateUserId as lastUpdateUserId,
        sku.Company_Id as company_Id,
        sku.SaleModel as saleModel,
        sku.DistributionPercentForAmount as distributionPercentForAmount,
        sku.specificationName as specificationName,
        sku.packageName as packageName,
        sku.unitName as unitName,
        sku.packageQuantity as packageQuantity,
        sku.source as source,
        sku.warehouseCustodyFee as warehouseCustodyFee,
        sku.DeliveryFee as deliveryFee,
        sku.DeliveryPayType as deliveryPayType,
        sku.ProductInfo_Id as productInfoId,
        sku.productBrand as productBrand,
        sku.ownerName as ownerName,
        sku.ProductState as productState,
        sku.Unpackage as unpackage,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductFeature as productFeature,
        sku.MaxInventory as maxInventory,
        sku.MinInventory as minInventory,
        sku.MaxReplenishment as maxReplenishment,
        sku.MinReplenishment as minReplenishment,
        sku.isComplete as isComplete
        FROM productstore ps
        INNER JOIN productstorebatch psb on ps.id = psb.productstore_id
        INNER JOIN productsku sku ON sku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND sku.City_Id = ps.City_Id
        AND ((sku.Company_Id is null AND ps.Owner_Id is null) OR (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN location loc on psb.location_id = loc.Id AND loc.warehouse_id = ps.warehouse_id
        where ps.City_Id = #{cityId,jdbcType=INTEGER}
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="locationName != null">
            AND loc.Name like concat(#{locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="locationId != null">
            AND psb.location_id = #{locationId,jdbcType=BIGINT}
        </if>
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        GROUP BY sku.id
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        update productsku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ProductFeature =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.productFeature,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="MaxInventory =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.maxInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="MinInventory =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.minInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="MaxReplenishment =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.maxReplenishment,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="MinReplenishment =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.minReplenishment,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="isComplete =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.isComplete,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="StorageType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.storageType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsPick =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.pick,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsSow =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.sow,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="productGrade =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.productGrade,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        WHERE productsku_Id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.productSku_Id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findByProductSpecificationIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM productsku
        WHERE City_Id = #{orgId,jdbcType=INTEGER}
        <if test="ownerId != null ">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        <if test="ownerId == null ">
            and Company_Id is null
        </if>
        and ProductSpecification_Id in
        <foreach collection="productSpecificationIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateBatchAttr" parameterType="java.util.List">
        update productsku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ProductFeature =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.productFeature != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.productFeature,jdbcType=TINYINT}
                    </if>
                    <if test="item.productFeature == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.ProductFeature
                    </if>
                </foreach>
            </trim>
            <trim prefix="StorageType =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.storageType != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.storageType,jdbcType=TINYINT}
                    </if>
                    <if test="item.storageType == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.StorageType
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsPick =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.pick != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.pick,jdbcType=TINYINT}
                    </if>
                    <if test="item.pick == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.IsPick
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsSow =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.sow != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.sow,jdbcType=TINYINT}
                    </if>
                    <if test="item.sow == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.IsSow
                    </if>
                </foreach>
            </trim>
            <trim prefix="productGrade =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.productGrade != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.productGrade,jdbcType=TINYINT}
                    </if>
                    <if test="item.productGrade == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.productGrade
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE productsku_Id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.productSku_Id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchRelate" parameterType="java.util.List">
        update productsku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="IsUnique =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.unique,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="IsFleeGoods =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.fleeGoods,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="ProductRelevantState =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when productsku_Id=#{item.productSku_Id} then #{item.productRelevantState,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        WHERE productsku_Id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.productSku_Id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchByUniqueAndFleeGoods" parameterType="java.util.List">
        update productsku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="IsUnique =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.unique != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.unique,jdbcType=TINYINT}
                    </if>
                    <if test="item.unique == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.IsUnique
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsFleeGoods =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.fleeGoods != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.fleeGoods,jdbcType=TINYINT}
                    </if>
                    <if test="item.fleeGoods == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.IsFleeGoods
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductRelevantState =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.productRelevantState != null">
                        when productsku_Id=#{item.productSku_Id} then #{item.productRelevantState,jdbcType=TINYINT}
                    </if>
                    <if test="item.productRelevantState == null">
                        when productsku_Id=#{item.productSku_Id} then productsku.ProductRelevantState
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE productsku_Id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.productSku_Id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchInventoryRatio">
        update productsku
        set InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR}
        WHERE productsku_Id IN
        <foreach collection="productSkuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="listProductSkuByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where ProductSku_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findProductInLocation" resultMap="BaseResultMap">
        select sku.ProductSku_Id,
        sku.productBrand,
        pic.StatisticsClass
        from productsku sku
        inner join productLocation pl on sku.ProductSku_Id = pl.ProductSku_Id
        left join productinfocategory pic on sku.ProductInfoCategory_Id = pic.id
        where pl.Warehouse_Id = #{warehouseId}
        and pl.Location_Id = #{locationId,jdbcType=BIGINT}
    </select>

    <select id="findProductBrandCategoryBySku" resultMap="BaseResultMap">
        select sku.productBrand,
        pic.StatisticsClass
        from productsku sku
        left join productinfocategory pic on sku.ProductInfoCategory_Id = pic.id
        where sku.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
    </select>

    <select id="findUnifySkuBySpecIdAndOwnerId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.UnifySkuSimplePO">
        select UnifySkuId, ProductSpecification_Id as productSpecId, Company_Id as ownerId,
        ProductSku_Id as productSkuId
        from productsku
        where
        <foreach collection="productSpecIdAndOwnerIds" item="item" open="(" close=")" separator="or">
            (
            ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND Company_Id is null
            </if>
            <if test="item.ownerId != null">
                AND Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
        and UnifySkuId is not null
    </select>

    <select id="findUnifySkuBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.UnifySkuSimplePO">
        select City_Id as cityId, UnifySkuId as unifySkuId, ProductSpecification_Id as productSpecId, Company_Id as
        ownerId, secOwner_Id as secOwnerId,
        ProductSku_Id as productSkuId
        from productsku
        where
        ProductSku_Id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId,jdbcType=BIGINT}
        </foreach>
        and UnifySkuId is not null
    </select>

    <select id="findUnifySkuByUnifySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.UnifySkuSimplePO">
        select City_Id as cityId, UnifySkuId as unifySkuId, ProductSpecification_Id as productSpecId, Company_Id as
        ownerId, secOwner_Id as secOwnerId,
        ProductSku_Id as productSkuId,name,
        productBrand,specificationName,packageName,unitName,packageQuantity,source
        from productsku
        where
        UnifySkuId in
        <foreach collection="unifySkuIds" item="unifySkuId" open="(" close=")" separator=",">
            #{unifySkuId,jdbcType=BIGINT}
        </foreach>
        <if test="cityId != null">
            AND City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getProductSkuBySpecId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,Replace_To_SkuId
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        <if test="source != null">
            and Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="ownerId == null">
            and Company_Id is null
        </if>
        <if test="ownerId != null">
            and Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        order by ProductState desc
        limit 1
    </select>

    <update id="updateReplaceToSkuId">
        update productsku
        set Replace_To_SkuId = #{replaceToSkuId,jdbcType=INTEGER}
        where ProductSku_Id in
        <foreach collection="skuIdList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectBySkuId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,Replace_To_SkuId
        from productsku
        where ProductSku_Id = #{skuId,jdbcType=BIGINT}
    </select>

    <select id="isHaveReplaceSku" resultType="long">
        select
        count(1)
        from productsku
        where
        <foreach collection="skuList" item="item" open="(" close=")" separator="or">
            (
            ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            AND City_Id = #{item.cityId,jdbcType=INTEGER}
            <if test="item.company_Id == null">
                AND Company_Id is null
            </if>
            <if test="item.company_Id != null">
                AND Company_Id = #{item.company_Id,jdbcType=BIGINT}
            </if>
            )
        </foreach>
        and Replace_To_SkuId is not null
        limit 1
    </select>

    <select id="listSameUnifyProductInfoSku" resultMap="BaseResultMap">
        select
        sku.ProductSku_Id as Id, tarSku.City_Id, tarSku.ProductSpecification_Id, tarSku.ProductSku_Id, tarSku.Name,
        tarSku.Sequence, tarSku.Remo,
        tarSku.CreateTime, tarSku.CreateUserId, tarSku.LastUpdateTime, tarSku.LastUpdateUserId, tarSku.Company_Id,
        tarSku.SaleModel,
        tarSku.distributionPercentForAmount, tarSku.specificationName, tarSku.packageName, tarSku.unitName,
        tarSku.packageQuantity,
        tarSku.Source, tarSku.warehouseCustodyFee, tarSku.DeliveryFee, tarSku.DeliveryPayType, tarSku.sortingFee,
        tarSku.ProductInfo_Id, tarSku.productBrand, tarSku.OwnerName, tarSku.ProductState, tarSku.Unpackage,
        tarSku.MonthOfShelfLife, tarSku.ShelfLifeUnit, tarSku.ProductFeature,
        tarSku.MaxInventory,tarSku.MinInventory,tarSku.MaxReplenishment,tarSku.MinReplenishment,tarSku.isComplete,tarSku.StorageType,
        tarSku.IsPick, tarSku.IsSow, tarSku.IsUnique, tarSku.IsFleeGoods,
        tarSku.UnifySkuId,pkgUnifyPkg.id packageUnifyPackageId,unifyInfo.Package_id unitUnifyPackageId
        from productsku sku
        inner join unifyProductSku unifySku on sku.UnifySkuId = unifySku.Sku_Id     <!-- 根据中台skuId查到中台infoId -->
        inner join unifyProductSku unifyInfo on unifyInfo.Info_Id = unifySku.Info_Id    <!-- 根据中台infoId查中台sku -->
        inner join productsku tarSku on tarSku.UnifySkuId = unifyInfo.Sku_Id and tarSku.City_Id = sku.City_Id and
        ((tarSku.company_id is null and sku.company_id is null ) or tarSku.company_id =
        sku.company_id)   <!-- 根据中台skuId查出sku -->
        left join unifyproductpackage pkgUnifyPkg on unifyInfo.Package_id = pkgUnifyPkg.childPackage_Id and
        unifyInfo.packageQuantity = pkgUnifyPkg.childPackage_Quantity    <!-- 找到大单位packageId -->
        where
        sku.City_Id = #{cityId,jdbcType=INTEGER} and
        sku.ProductSku_Id in
        <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
            #{skuId,jdbcType=BIGINT}
        </foreach>
    </select>


    <select id="listSkuDetails" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM productsku
        WHERE
        City_Id = #{cityId,jdbcType=INTEGER}
        <if test="isDelete != null">
            and IsDelete = #{isDelete,jdbcType=TINYINT}
        </if>
        AND
        <foreach collection="querySkuParamList" index="index" item="item" separator="or" open="(" close=")">
            (
            ProductSpecification_Id = #{item.specificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND Company_Id is null
            </if>
            <if test="item.ownerId != null">
                AND Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            <if test="item.secOwnerId == null">
                AND secOwner_Id is null
            </if>
            <if test="item.secOwnerId != null">
                AND secOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="queryInventoryProperty" resultType="java.util.Map">
        select ProductSku_Id as productSkuId, inventoryPinProperty as inventoryRatio
        from sc_product.productskuconfig p
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and p.inventoryPinProperty is not null
        order by p.inventoryPinProperty asc;
    </select>

    <select id="pageListProductSkuInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from productsku
        <where>
            1=1
            <if test="dto.cityIdList != null and dto.cityIdList.size() > 0">
                and City_Id in
                <foreach collection="dto.cityIdList" item="cityId" separator="," open="(" close=")">
                    #{cityId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="dto.skuIdList != null and dto.skuIdList.size() > 0">
                and ProductSku_Id in
                <foreach collection="dto.skuIdList" item="skuId" separator="," open="(" close=")">
                    #{skuId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateIsDeleteBatch">
        update productsku
        SET IsDelete = #{isDelete,jdbcType=TINYINT}
        WHERE ProductSku_Id IN
        <foreach collection="skuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>