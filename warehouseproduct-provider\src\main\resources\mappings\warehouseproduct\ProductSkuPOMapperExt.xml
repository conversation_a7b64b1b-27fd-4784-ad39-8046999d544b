<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuPOMapper">
    <resultMap id="BaseResultMap_new" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecification_Id"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSku_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="SaleModel" jdbcType="TINYINT" property="saleModel"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="Company_Id" jdbcType="BIGINT" property="company_Id"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="INTEGER" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="unpackage" jdbcType="BIT" property="unpackage"/>
        <result column="productBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="StatisticsClassName" jdbcType="VARCHAR" property="statisticsClass"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="secOwner_Id" jdbcType="TINYINT" property="secOwnerId"/>
        <result column="ProductType" jdbcType="TINYINT" property="productType"/>
        <result column="productGrade" jdbcType="TINYINT" property="productGrade"/>
        <result column="IsDelete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>

    <resultMap id="ResultMapPO" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecification_Id"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSku_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="Company_Id" jdbcType="BIGINT" property="company_Id"/>
        <result column="SaleModel" jdbcType="TINYINT" property="saleModel"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="sourceName" jdbcType="VARCHAR" property="sourceName"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="productBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="ProductState" jdbcType="TINYINT" property="productState"/>
        <result column="MonthOfShelfLife" jdbcType="INTEGER" property="monthOfShelfLife"/>
        <result column="ShelfLifeUnit" jdbcType="INTEGER" property="shelfLifeUnit"/>
        <result column="Length" jdbcType="DOUBLE" property="length"/>
        <result column="Width" jdbcType="DOUBLE" property="width"/>
        <result column="Height" jdbcType="DOUBLE" property="height"/>
        <result column="Weight" jdbcType="DOUBLE" property="weight"/>
        <result column="Volume" jdbcType="VARCHAR" property="volume"/>
        <result column="ConvertProductInfoSpec_Id" jdbcType="BIGINT" property="convertProductInfoSpecId"/>
        <result column="ConvertSpecQuantity" jdbcType="DECIMAL" property="convertSpecQuantity"/>
        <result column="Status" jdbcType="TINYINT" property="productInfoStatus"/>
        <result column="DefaultImageFile_Id" jdbcType="VARCHAR" property="defaultImageFileId"/>
        <result column="SeriesName" jdbcType="VARCHAR" property="seriesName"/>
        <result column="IsProcess" jdbcType="TINYINT" property="process"/>
        <result column="StatisticsClass" jdbcType="BIGINT" property="statisticsClassId"/>
        <result column="StatisticsClassName" jdbcType="VARCHAR" property="statisticsClass"/>
        <result column="SecondStatisticsClass" jdbcType="BIGINT" property="secondStatisticsClassId"/>
        <result column="SecondStatisticsClassName" jdbcType="VARCHAR" property="secondStatisticsClass"/>
        <result column="secOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="Ref_ProductSku_Id" jdbcType="VARCHAR" property="refProductSkuId"/>
        <result column="ProductType" jdbcType="TINYINT" property="productType"/>
        <result column="ShelfLifeLongTime" jdbcType="BIT" property="shelfLifeLongTime"/>
        <!--productSkuConfig -->
        <result column="warehouseCustodyFee" jdbcType="DECIMAL" property="warehouseCustodyFee"/>
        <result column="DeliveryFee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="DeliveryPayType" jdbcType="DECIMAL" property="deliveryPayType"/>
        <result column="sortingFee" jdbcType="DECIMAL" property="sortingFee"/>
        <result column="Unpackage" jdbcType="BIT" property="unpackage"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
        <result column="MaxInventory" jdbcType="DECIMAL" property="maxInventory"/>
        <result column="MinInventory" jdbcType="DECIMAL" property="minInventory"/>
        <result column="MaxReplenishment" jdbcType="DECIMAL" property="maxReplenishment"/>
        <result column="MinReplenishment" jdbcType="DECIMAL" property="minReplenishment"/>
        <result column="isComplete" jdbcType="TINYINT" property="isComplete"/>
        <result column="storageType" jdbcType="TINYINT" property="storageType"/>
        <result column="IsPick" jdbcType="TINYINT" property="pick"/>
        <result column="IsSow" jdbcType="TINYINT" property="sow"/>
        <result column="InventoryRatio" jdbcType="VARCHAR" property="inventoryRatio"/>
        <result column="IsUnique" jdbcType="TINYINT" property="unique"/>
        <result column="IsFleeGoods" jdbcType="TINYINT" property="fleeGoods"/>
        <result column="ProductRelevantState" jdbcType="TINYINT" property="productRelevantState"/>
        <result column="productGrade" jdbcType="TINYINT" property="productGrade"/>
        <result column="CostPrice" jdbcType="DECIMAL" property="costPrice"/>
        <result column="SellingPrice" jdbcType="DECIMAL" property="sellingPrice"/>
        <result column="SellingPriceUnit" jdbcType="VARCHAR" property="sellingPriceUnit"/>
        <result column="OuterCode" jdbcType="VARCHAR" property="outerCode"/>
        <result column="palletQuantity" jdbcType="DECIMAL" property="palletQuantity"/>
        <result column="BusinessTag" jdbcType="TINYINT" property="businessTag"/>
        <result column="inventoryPinProperty" jdbcType="VARCHAR" property="inventoryPinProperty"/>
        <result column="StorageAttribute" jdbcType="TINYINT" property="storageAttribute"/>
    </resultMap>

    <resultMap id="Base_ResultMapPO" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecification_Id"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSku_Id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="Company_Id" jdbcType="BIGINT" property="company_Id"/>
        <result column="SaleModel" jdbcType="TINYINT" property="saleModel"/>
        <result column="DistributionPercentForAmount" jdbcType="DECIMAL" property="distributionPercentForAmount"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specificationName"/>
        <result column="packageName" jdbcType="VARCHAR" property="packageName"/>
        <result column="unitName" jdbcType="VARCHAR" property="unitName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="sourceName" jdbcType="VARCHAR" property="sourceName"/>
        <result column="ProductInfo_Id" jdbcType="BIGINT" property="productInfoId"/>
        <result column="ProductInfoCategory_Id" jdbcType="BIGINT" property="productInfoCategoryId"/>
        <result column="productBrand" jdbcType="VARCHAR" property="productBrand"/>
        <result column="OwnerName" jdbcType="VARCHAR" property="ownerName"/>
        <result column="ProductState" jdbcType="TINYINT" property="productState"/>
        <result column="MonthOfShelfLife" jdbcType="INTEGER" property="monthOfShelfLife"/>
        <result column="ShelfLifeUnit" jdbcType="INTEGER" property="shelfLifeUnit"/>
        <result column="Length" jdbcType="DOUBLE" property="length"/>
        <result column="Width" jdbcType="DOUBLE" property="width"/>
        <result column="Height" jdbcType="DOUBLE" property="height"/>
        <result column="Weight" jdbcType="DOUBLE" property="weight"/>
        <result column="Volume" jdbcType="VARCHAR" property="volume"/>
        <result column="ConvertProductInfoSpec_Id" jdbcType="BIGINT" property="convertProductInfoSpecId"/>
        <result column="ConvertSpecQuantity" jdbcType="DECIMAL" property="convertSpecQuantity"/>
        <result column="Status" jdbcType="TINYINT" property="productInfoStatus"/>
        <result column="DefaultImageFile_Id" jdbcType="VARCHAR" property="defaultImageFileId"/>
        <result column="SeriesName" jdbcType="VARCHAR" property="seriesName"/>
        <result column="IsProcess" jdbcType="TINYINT" property="process"/>
        <result column="StatisticsClass" jdbcType="BIGINT" property="statisticsClassId"/>
        <result column="StatisticsClassName" jdbcType="VARCHAR" property="statisticsClass"/>
        <result column="SecondStatisticsClass" jdbcType="BIGINT" property="secondStatisticsClassId"/>
        <result column="SecondStatisticsClassName" jdbcType="VARCHAR" property="secondStatisticsClass"/>
        <result column="secOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="Ref_ProductSku_Id" jdbcType="VARCHAR" property="refProductSkuId"/>
        <result column="ShelfLifeLongTime" jdbcType="BIT" property="shelfLifeLongTime"/>
        <result column="ProductFeature" jdbcType="TINYINT" property="productFeature"/>
    </resultMap>

    <sql id="Base_ProductSku_Sql">
            a.Id,
            a.City_Id,
            a.ProductSpecification_Id,
            a.ProductSku_Id,
            a.ProductType,
            CONCAT(case a.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, a.NAME) AS name,
            a.Sequence,
            a.Remo,
            a.CreateTime,
            a.CreateUserId,
            a.LastUpdateTime,
            a.LastUpdateUserId,
            a.Company_Id,
            a.SaleModel,
            a.DistributionPercentForAmount,
            a.specificationName,
            a.packageName,
            a.unitName,
            a.packageQuantity,
            a.source,
            a.ProductInfo_Id,
            a.productBrand,
            a.OwnerName,
            a.OwnerName                                                                               as sourceName,
            a.ProductState,
            IFNULL(d.MonthOfShelfLife, a.MonthOfShelfLife)                                            as MonthOfShelfLife,
            IFNULL(d.ShelfLifeUnit, a.ShelfLifeUnit)                                                  as ShelfLifeUnit,
            a.secOwner_Id,
            a.Ref_ProductSku_Id,
            a.ProductInfoCategory_Id,
            c.Length,
            c.Width,
            c.Height,
            c.Weight,
            c.Volume,
            c.ConvertProductInfoSpec_Id,
            c.ConvertSpecQuantity,
            d.Status,
            d.DefaultImageFile_Id,
            d.SeriesName,
            d.IsProcess,
            b.StatisticsClass,
            b.StatisticsClassName,
            b.SecondStatisticsClass,
            b.SecondStatisticsClassName,
            d.ShelfLifeLongTime
    </sql>

    <sql id="ProductSku_Config_Sql">
            f.warehouseCustodyFee,
            f.DeliveryFee,
            f.DeliveryPayType,
            f.sortingFee,
            f.Unpackage,
            f.ProductFeature,
            f.MaxInventory,
            f.MinInventory,
            f.MaxReplenishment,
            f.MinReplenishment,
            f.isComplete,
            f.StorageType,
            f.IsPick,
            f.IsSow,
            f.InventoryRatio,
            f.IsUnique,
            f.IsFleeGoods,
            f.ProductRelevantState,
            f.productGrade,
            f.CostPrice,
            f.SellingPrice,
            f.SellingPriceUnit,
            f.palletQuantity,
            f.BusinessTag as BusinessTag,
            f.inventoryPinProperty,
            f.StorageAttribute
    </sql>

    <select id="selectCountByCityId" resultType="java.lang.Integer">
        select count(productSku_Id)
        from productsku
        where city_Id = #{cityId,jdbcType=INTEGER}
    </select>

    <select id="selectByCityIdAndProductSkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where productSku_Id=#{productSkuId,jdbcType=BIGINT}
        limit 1
    </select>

    <insert id="insertByCity" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        insert into productsku (Id, City_Id,
                                ProductSpecification_Id, ProductSku_Id,
                                Name, Sequence, Remo,
                                CreateTime, CreateUserId, LastUpdateTime,
                                LastUpdateUserId, distributionPercentForAmount, SaleModel, Company_Id, OwnerName)
        select #{id,jdbcType=BIGINT},
               #{city_Id,jdbcType=INTEGER},
               #{productSpecification_Id,jdbcType=BIGINT},
               #{productSku_Id,jdbcType=BIGINT},
               #{name,jdbcType=VARCHAR},
               count(1) + 1,
               #{remo,jdbcType=VARCHAR},
               #{createTime,jdbcType=TIMESTAMP},
               #{createUserId,jdbcType=INTEGER},
               #{lastUpdateTime,jdbcType=TIMESTAMP},
               #{lastUpdateUserId,jdbcType=INTEGER},
               #{distributionPercentForAmount,jdbcType=DECIMAL},
               #{saleModel,jdbcType=TINYINT},
               #{company_Id,jdbcType=BIGINT},
               #{ownerName,jdbcType=VARCHAR}
        from productsku
        where city_Id = #{city_Id,jdbcType=INTEGER}
    </insert>

    <select id="selectSkuIdsWithDifferenceName" resultType="java.lang.Long">
        select id from productsku
        where
        ProductSpecification_Id in
        <foreach item="item" collection="productSpecificationIds" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
        and name != #{name,jdbcType=VARCHAR}
    </select>

    <update id="updateSkuName">
        update productsku set name=#{name,jdbcType=VARCHAR}
        where id in
        <foreach item="item" collection="skuIds" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getSequenceByCityAndProductSkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where city_Id = #{cityId,jdbcType=INTEGER}
        and productSku_Id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getProductSkuInfoBySkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        <where>
            ProductSku_Id in
            <foreach collection="list" open="(" close=")" item="productSku_Id" separator=",">
                #{productSku_Id,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>

    <select id="getOwnerInfoBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.OwnerInfoPO">
        SELECT
        `Name` as productName,
        SaleModel as saleModel,
        Company_Id as CompanyId,
        productsku_id as productSkuId,
        OwnerName
        FROM
        productsku
        <where>
            productsku_id IN
            <foreach collection="list" open="(" close=")" item="productSku_Id" separator=",">
                #{productSku_Id,jdbcType=BIGINT}
            </foreach>
        </where>
    </select>

    <select id="getProductInfoBySkuId" resultMap="BaseResultMap_new">
        SELECT Id, City_Id, ProductSpecification_Id, ProductSku_Id, Name, Sequence, SaleModel,
        DistributionPercentForAmount, Company_Id, Remo, CreateTime, CreateUserId, LastUpdateTime,
        LastUpdateUserId, Source, ProductInfo_Id, secOwner_Id,
        specificationName, packageName, unitName, packageQuantity, warehouseCustodyFee, DeliveryFee,
        DeliveryPayType, sortingFee,unpackage, productBrand, ProductFeature,productGrade,ProductType
        FROM productsku
        WHERE ProductSku_Id IN
        <foreach collection="productSkuList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getProductInfoBySpecificationId" resultMap="BaseResultMap_new">
        SELECT psku.Id, psku.City_Id, psku.ProductSpecification_Id, psku.ProductSku_Id, psku.Name, psku.Sequence,
        psku.SaleModel,
        psku.DistributionPercentForAmount, psku.Company_Id, psku.Remo, psku.CreateTime, psku.CreateUserId,
        psku.LastUpdateTime,
        psku.LastUpdateUserId, psku.Source, psku.ProductInfo_Id,
        psku.specificationName, psku.packageName, psku.unitName, psku.packageQuantity, psku.warehouseCustodyFee,
        psku.DeliveryFee,
        psku.DeliveryPayType, psku.sortingFee, psku.unpackage, psku.productBrand,
        pc.StatisticsClassName,psku.productGrade,psku.IsDelete
        FROM productsku psku
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        WHERE psku.city_Id = #{cityId}
        <if test="source != null">
            AND psku.Source = #{source}
        </if>
        <if test="ownerId==null">
            and psku.Company_Id is null
        </if>
        <if test="ownerId!=null">
            and psku.Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        AND psku.ProductSpecification_Id IN
        <foreach collection="productSpecificationList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listProductSku" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        <include refid="ProductSku_Config_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id = b.Id
        left join productinfospecification c on a.ProductSpecification_Id = c.Id
        left join productinfo d on a.ProductInfo_Id = d.Id
        <if test="so.limitSku != null and so.limitSku == 1">
            inner join productskuconfig f on f.ProductSku_Id = a.ProductSku_Id
            and f.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
            and f.State = 1
        </if>
        <if test="so.limitSku == null or so.limitSku != 1">
            left join productskuconfig f on f.ProductSku_Id = a.ProductSku_Id
            and f.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
            and f.State = 1
        </if>
        <where>
            a.city_Id = #{so.cityId,jdbcType=INTEGER}
            <if test="so.isDelete != null">
                and a.IsDelete = #{so.isDelete,jdbcType=TINYINT}
            </if>
            <if test="so.productName != null and so.productName != ''">
                and a.name like concat('%', #{so.productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="so.sourceName != null">
                and a.ownerName = #{so.sourceName,jdbcType=VARCHAR}
            </if>
            <if test="so.ownerId != null">
                and a.Company_Id = #{so.ownerId,jdbcType=BIGINT}
            </if>
            <if test="so.statisticsClass != null">
                and b.StatisticsClassName = #{so.statisticsClass,jdbcType=VARCHAR}
            </if>
            <if test="so.statisticsClassIdList != null and so.statisticsClassIdList.size() > 0">
                and b.StatisticsClass in
                <foreach collection="so.statisticsClassIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="so.statisticsClassList != null and so.statisticsClassList.size() > 0">
                and b.StatisticsClassName in
                <foreach collection="so.statisticsClassList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="so.secondStatisticsClass != null">
                and b.SecondStatisticsClassName = #{so.secondStatisticsClass,jdbcType=VARCHAR}
            </if>
            <if test="so.secondStatisticsClassList != null and so.secondStatisticsClassList.size() > 0">
                and b.SecondStatisticsClassName in
                <foreach collection="so.secondStatisticsClassList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="so.isComplete == false">
                and f.isComplete = 0
            </if>
            <if test="so.process != null">
                and d.IsProcess = #{so.process,jdbcType=TINYINT}
            </if>
            <if test="so.pick != null">
                and f.IsPick = #{so.pick,jdbcType=TINYINT}
            </if>
            <if test="so.sow != null">
                and f.IsSow = #{so.sow,jdbcType=TINYINT}
            </if>
            <if test="so.skuIdList != null and so.skuIdList.size() > 0">
                and a.ProductSku_Id in
                <foreach collection="so.skuIdList" item="productSkuId" separator="," open="(" close=")">
                    #{productSkuId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="so.existStore != null and so.existStore == 0">
                and not exists(
                select s.Id from productstore s where s.ProductSpecification_Id = a.ProductSpecification_Id AND
                s.City_Id = a.City_Id
                AND ((a.Company_Id is null and s.Owner_Id is null) or (s.Owner_Id = a.Company_Id))
                AND ((a.secOwner_Id is null) or (a.secOwner_Id = s.secOwner_Id))
                and s.TotalCount_MinUnit > 0
                and s.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
                )
            </if>
            <if test="so.existStore != null and so.existStore == 1">
                and exists(
                select s.Id from productstore s where s.ProductSpecification_Id = a.ProductSpecification_Id AND
                s.City_Id = a.City_Id
                AND ((a.Company_Id is null and s.Owner_Id is null) or (s.Owner_Id = a.Company_Id))
                AND ((a.secOwner_Id is null) or (a.secOwner_Id = s.secOwner_Id))
                and s.TotalCount_MinUnit > 0
                and s.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
                )
            </if>
            <if test="so.unique != null and so.unique != -1">
                and f.IsUnique = #{so.unique,jdbcType=TINYINT}
            </if>
            <if test="so.unique != null and so.unique == -1">
                and f.IsUnique is null
            </if>
            <if test="so.fleeGoods != null and so.fleeGoods != -1">
                and f.IsFleeGoods = #{so.fleeGoods,jdbcType=TINYINT}
            </if>
            <if test="so.fleeGoods != null and so.fleeGoods == -1">
                and f.IsFleeGoods is null
            </if>
            <if test="so.productRelevantState != null and so.productRelevantState != -1">
                and f.ProductRelevantState = #{so.productRelevantState,jdbcType=TINYINT}
            </if>
            <if test="so.productRelevantState != null and so.productRelevantState == -1">
                and f.ProductRelevantState is null
            </if>
            <if test="so.exceptionProductFlag != null and so.exceptionProductFlag == true">
                and
                (
                f.IsUnique IS NULL
                OR (
                f.IsUnique = FALSE AND
                (
                (f.IsFleeGoods IS NULL) OR (f.IsFleeGoods = TRUE AND (f.ProductRelevantState IS NULL OR
                f.ProductRelevantState = FALSE))
                )
                )
                )
            </if>
            <if test="so.storageType != null and so.storageType != -1">
                and f.StorageType = #{so.storageType,jdbcType=TINYINT}
            </if>
            <if test="so.storageType != null and so.storageType == -1">
                and f.StorageType is null
            </if>
            <if test="so.enableSell != null and so.enableSell == 0">
                and not exists( select psl.id from productskulabel psl where psl.ProductSku_Id = a.ProductSku_Id and
                psl.LabelType = 1 and psl.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER})
            </if>
            <if test="so.enableSell != null and so.enableSell == 1">
                and exists( select psl.id from productskulabel psl where psl.ProductSku_Id = a.ProductSku_Id and
                psl.LabelType = 1 and psl.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER})
            </if>
            <if test="so.productTypeList != null">
                and a.productType in
                <foreach collection="so.productTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="so.hasLocation != null and so.hasLocation">
                and exists(select pl.id
                from productlocation pl
                where pl.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
                and pl.City_Id = #{so.cityId,jdbcType=INTEGER}
                and pl.ProductSku_Id = a.ProductSku_Id
                )
            </if>
            <if test="so.hasLocation != null and !so.hasLocation">
                and not exists(select pl.id
                from productlocation pl
                where pl.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
                and pl.City_Id = #{so.cityId,jdbcType=INTEGER}
                and pl.ProductSku_Id = a.ProductSku_Id
                )
            </if>
            <if test="so.noReplenishmentConfig != null and so.noReplenishmentConfig">
                and (f.MaxReplenishment is null or f.MinReplenishment is null)
            </if>
            <if test="so.newProductFlag != null and so.newProductFlag">
                and a.ProductType != 4
                and (f.ProductFeature is null or f.MaxReplenishment is null or f.MinReplenishment is null
                or f.MaxReplenishment <![CDATA[ <= ]]> 0 or f.MinReplenishment <![CDATA[ <= ]]> 0)
                and exists (
                select s.Id from productstore s where s.ProductSpecification_Id = a.ProductSpecification_Id AND
                s.City_Id = a.City_Id
                and ((a.Company_Id is null and s.Owner_Id is null) or (s.Owner_Id = a.Company_Id))
                and ((a.secOwner_Id is null) or (a.secOwner_Id = s.secOwner_Id))
                and s.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
                and s.CreateTime >= #{so.startTime,jdbcType=TIMESTAMP}
                )
            </if>
            <if test="so.productSkuStateList != null and so.productSkuStateList.size() > 0">
                and a.ProductState in
                <foreach collection="so.productSkuStateList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="so.storageAttribute != null">
                and f.StorageAttribute = #{so.storageAttribute,jdbcType=TINYINT}
            </if>
        </where>
        order by a.createtime, a.name, a.Id desc
    </select>

    <select id="listProductSkuFull" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        <include refid="ProductSku_Config_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        inner join productskuconfig f on f.ProductSku_Id=a.ProductSku_Id
        where f.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="productName != null and productName !=''">
            and a.name like concat('%',#{productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="skuIdList != null and skuIdList.size() > 0">
            and a.ProductSku_Id in
            <foreach collection="skuIdList" item="productSkuId" separator="," open="(" close=")">
                #{productSkuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="source != null">
            and a.source = #{source,jdbcType=INTEGER}
        </if>
        <if test="ownerId != null and ownerId == 0">
            and a.Company_Id is null
        </if>
        <if test="ownerId != null and ownerId != 0">
            and a.Company_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        <if test="statisticsClassIdList != null and statisticsClassIdList.size() > 0">
            and b.StatisticsClass in
            <foreach collection="statisticsClassIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="productSkuStateList != null and productSkuStateList.size() > 0">
            and a.ProductState in
            <foreach collection="productSkuStateList" item="productSkuState" separator="," open="(" close=")">
                #{productSkuState,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="productConfigStateList != null and productConfigStateList.size() > 0">
            and f.State in
            <foreach collection="productConfigStateList" item="productConfigState" separator="," open="(" close=")">
                #{productConfigState,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <select id="findBySkuFull" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        <include refid="ProductSku_Config_Sql"/>
        from productsku a
                 left join productinfocategory b on a.ProductInfoCategory_Id = b.Id
                 left join productinfospecification c on a.ProductSpecification_Id = c.Id
                 left join productinfo d on a.ProductInfo_Id = d.Id
                 left join productskuconfig f on f.ProductSku_Id = a.ProductSku_Id
                             and f.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <where>
            a.productsku_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findBySkuFullNew" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        <include refid="ProductSku_Config_Sql"/>
        from productsku a
                 left join productinfocategory b on a.ProductInfoCategory_Id = b.Id
                 left join productinfospecification c on a.ProductSpecification_Id = c.Id
                 left join productinfo d on a.ProductInfo_Id = d.Id
                 left join productskuconfig f on f.ProductSku_Id = a.ProductSku_Id
                    and f.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <where>
            a.productsku_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="warehouseAllocationType != null">
                and (f.storageAttribute = #{warehouseAllocationType,jdbcType=INTEGER} or f.StorageAttribute = 0 or
                     f.StorageAttribute is null)
            </if>
        </where>
    </select>

    <select id="listProuductPrice" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductPriceDTO">
        select
        sku.City_Id as cityId,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.ProductSku_Id as productSkuId,
        sku.Company_Id as ownerId,
        cfg.Warehouse_Id as warehouseId,
        cfg.CostPrice as costPrice,
        cfg.SellingPrice as sellingPrice,
        cfg.SellingPriceUnit as sellingPriceUnit
        from productsku sku
        inner join productskuconfig cfg on cfg.ProductSku_Id = sku.ProductSku_Id
        where
        <foreach collection="list" item="item" open="(" separator="or" close=")">
            (
            cfg.Warehouse_Id = #{item.warehouseId,jdbcType=INTEGER}
            and sku.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                and sku.Company_Id is null
            </if>
            <if test="item.ownerId != null">
                and sku.Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="listProductSkuBySpec" resultMap="Base_ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        left join
        (select sku.City_Id, r.ProductSpecification_Id, r.Owner_Id, r.Source, 1 as relateFlag
        from productrelationconfig r
        inner join productsku sku on r.ProductSpecification_Id = sku.ProductSpecification_Id and r.Source = sku.source
        and if(r.Source = 3, 1=1, ((r.Owner_Id = sku.Company_Id) or (r.Owner_Id is null and sku.Company_Id is null)))
        and sku.City_Id = #{cityId,jdbcType=INTEGER}
        group by sku.City_Id, r.ProductSpecification_Id, r.Owner_Id, r.Source) h
        on h.City_Id = a.City_Id and h.ProductSpecification_Id = a.ProductSpecification_Id and h.Source = a.Source
        where a.City_Id = #{cityId,jdbcType=INTEGER}
        and
        <foreach collection="list" item="item" index="index" separator="or" open="(" close=")">
            (
            a.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            and a.source = #{item.source,jdbcType=TINYINT}
            <if test="item.ownerId == null">
                and a.Company_Id is null
            </if>
            <if test="item.ownerId != null">
                and a.Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="findBySku" resultMap="Base_ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        a.ProductFeature
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        <where>
            a.productsku_id in
            <foreach collection="list" close=")" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findBySkuAndRef" resultMap="Base_ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        <where>
            <if test="skuList != null and skuList.size() > 0">
                and a.productsku_id in
                <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="refSkuList != null and refSkuList.size() > 0">
                or a.Ref_ProductSku_Id in
                <foreach collection="refSkuList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findBySpec" resultMap="Base_ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        where a.City_Id = #{cityId,jdbcType=INTEGER}
        AND
        <foreach collection="specList" item="item" open="(" close=")" separator="or">
            (
            a.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND a.Company_Id is null
            </if>
            <if test="item.ownerId != null">
                AND a.Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            <if test="item.secOwnerId == null">
                AND a.secOwner_Id is null
            </if>
            <if test="item.secOwnerId != null">
                AND a.secOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
        <if test="source != null">
            and a.Source = #{source,jdbcType=TINYINT}
        </if>
    </select>

    <update id="updateByProductSkuIdSelective">
        update productsku
        <set>
            <if test="city_Id != null">
                City_Id = #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="productSpecification_Id != null">
                ProductSpecification_Id = #{productSpecification_Id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                Sequence = #{sequence,jdbcType=INTEGER},
            </if>
            <if test="saleModel != null">
                SaleModel = #{saleModel,jdbcType=TINYINT},
            </if>
            <if test="company_Id != null">
                Company_Id = #{company_Id,jdbcType=BIGINT},
            </if>
            <if test="remo != null">
                Remo = #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="productFeature != null">
                ProductFeature = #{productFeature,jdbcType=TINYINT},
            </if>
            <if test="maxInventory != null">
                maxInventory = #{maxInventory,jdbcType=INTEGER},
            </if>
            <if test="minInventory != null">
                minInventory = #{minInventory,jdbcType=INTEGER},
            </if>
            <if test="maxReplenishment != null">
                maxReplenishment = #{maxReplenishment,jdbcType=INTEGER},
            </if>
            <if test="minReplenishment != null">
                minReplenishment = #{minReplenishment,jdbcType=INTEGER},
            </if>
            <if test="isComplete != null">
                isComplete = #{isComplete,jdbcType=TINYINT},
            </if>
            <if test="storageType != null">
                StorageType = #{storageType,jdbcType=TINYINT},
            </if>
            <if test="pick != null">
                IsPick = #{pick,jdbcType=TINYINT},
            </if>
            <if test="sow != null">
                IsSow = #{sow,jdbcType=TINYINT},
            </if>
            <if test="inventoryRatio != null">
                InventoryRatio = #{inventoryRatio,jdbcType=VARCHAR},
            </if>
            <if test="fleeGoods != null and fleeGoods != -1">
                IsFleeGoods = #{fleeGoods,jdbcType=TINYINT},
            </if>
            <if test="fleeGoods != null and fleeGoods == -1">
                IsFleeGoods = null,
            </if>
            <if test="productRelevantState != null">
                ProductRelevantState = #{productRelevantState,jdbcType=TINYINT},
            </if>
            <if test="unique != null and unique != -1">
                IsUnique = #{unique,jdbcType=TINYINT},
            </if>
            <if test="unique != null and unique == -1">
                IsUnique = null,
            </if>
            <if test="productGrade != null">
                productGrade = #{productGrade,jdbcType=TINYINT},
            </if>
        </set>
        where productsku_Id = #{productSku_Id,jdbcType=BIGINT}
    </update>

    <select id="findProductInfoBySpecificationIdAndOwners" resultMap="BaseResultMap_new">
        SELECT psku.Id, psku.City_Id, psku.ProductSpecification_Id, psku.ProductSku_Id, psku.Name, psku.Sequence,
        psku.SaleModel,
        psku.DistributionPercentForAmount, psku.Company_Id, psku.Remo, psku.CreateTime, psku.CreateUserId,
        psku.LastUpdateTime,
        psku.LastUpdateUserId, psku.Source, psku.ProductInfo_Id,
        psku.specificationName, psku.packageName, psku.unitName, psku.packageQuantity, psku.warehouseCustodyFee,
        psku.DeliveryFee,
        psku.DeliveryPayType, psku.sortingFee, psku.unpackage, psku.productBrand,psku.productGrade
        FROM productsku psku
        WHERE psku.city_Id = #{cityId}
        and psku.Company_Id IN
        <foreach collection="ownerIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND psku.ProductSpecification_Id IN
        <foreach collection="productSpecificationList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findProductSkuByProductRelation" resultMap="Base_ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        left join
        (select sku.City_Id, r.ProductSpecification_Id, r.Owner_Id, r.Source, 1 as relateFlag
        from productrelationconfig r
        inner join productsku sku on r.ProductSpecification_Id = sku.ProductSpecification_Id and r.Source = sku.source
        and if(r.Source = 3, 1=1, ((r.Owner_Id = sku.Company_Id) or (r.Owner_Id is null and sku.Company_Id is null)))
        <if test="cityId != null">
            and sku.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        group by sku.City_Id, r.ProductSpecification_Id, r.Owner_Id, r.Source) h
        on h.City_Id = a.City_Id and h.ProductSpecification_Id = a.ProductSpecification_Id and h.Source = a.Source
        where
        <foreach collection="list" item="item" index="index" separator="or" open="(" close=")">
            (
            a.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            and a.source = #{item.source,jdbcType=TINYINT}
            <if test="item.source != null and item.source == 0">
                <if test="item.ownerId == null">
                    and a.Company_Id is null
                </if>
                <if test="item.ownerId != null">
                    and a.Company_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
            </if>
            )
        </foreach>
        <if test="cityId != null">
            and a.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getProductSkuBySpecIdAndSource" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id = #{specId,jdbcType=BIGINT}
        and Source = #{source,jdbcType=INTEGER}
        limit 1
    </select>

    <select id="listProductSkuDelivery"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuDeliveryPO">
        SELECT
        a.ProductSku_Id as skuId,
        a.ProductSpecification_Id as specId,
        a.Company_Id as ownerId,
        a.SaleModel as saleModel,
        b.ProductSku_Id as deliverySkuId,
        b.ProductState as productState
        from productsku a
        left join productsku b on a.ProductSpecification_Id = b.ProductSpecification_Id
        and ((a.Company_Id is null and b.Company_Id is null) or (a.Company_Id = b.Company_Id))
        and a.city_id = #{cityId,jdbcType=INTEGER}
        and b.city_id = #{deliveryCityId,jdbcType=INTEGER}
        where a.city_id = #{cityId,jdbcType=INTEGER}
        and
        <foreach collection="specList" item="item" open="(" close=")" separator="or">
            (
            a.ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND a.Company_Id is null
            </if>
            <if test="item.ownerId != null">
                AND a.Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
        order by b.ProductState desc, b.ProductSku_Id
    </select>

    <select id="listProductSkuDeliveryByCPJH"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuDeliveryPO">
        SELECT
        a.ProductSku_Id as skuId,
        a.ProductSpecification_Id as specId,
        a.Company_Id as ownerId,
        a.SaleModel as saleModel,
        b.ProductSku_Id as deliverySkuId,
        b.ProductState as productState
        from productsku a
        left join productsku b on a.ProductSpecification_Id = b.ProductSpecification_Id
        and a.city_id = #{cityId,jdbcType=INTEGER}
        and b.city_id = #{deliveryCityId,jdbcType=INTEGER}
        and b.Source = 3
        where a.city_id = #{cityId,jdbcType=INTEGER}
        and
        <foreach collection="specList" item="item" open="(" close=")" separator="or">
            (
            a.ProductSku_Id = #{item.skuId,jdbcType=BIGINT}
            <if test="item.ownerId == null">
                AND b.Company_Id is null
            </if>
            <if test="item.ownerId != null">
                AND b.Company_Id = #{item.ownerId,jdbcType=BIGINT}
            </if>
            )
        </foreach>
    </select>

    <select id="getSkuIdByRefSkuId" resultType="java.lang.Long">
        select ProductSku_Id from productsku
        where city_id = #{cityId,jdbcType=INTEGER}
        and Ref_ProductSku_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and isDelete = 0
    </select>

    <select id="getSkuIdByCityIdAndSpecId" resultType="java.lang.Long">
        select ProductSku_Id
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and Source = #{source,jdbcType=INTEGER}
        and isDelete = 0
    </select>

    <sql id="Product_CategoryType_SQL">
        SELECT psk.Id                        AS id,
               psk.City_Id                   AS orgId,
               psk.ProductSku_Id             AS productSkuId,
               psk.Name                      AS productName,
               psk.SaleModel                 AS saleModel,
               psk.ProductSpecification_Id   AS productSpecificationId,
               psk.specificationName         AS specName,
               psk.packageQuantity           AS specQuantity,
               psk.packageName               AS packageName,
               psk.unitName                  AS unitName,
               psk.productBrand              AS productBrand,
               psk.ProductState              AS productState,
               psk.MonthOfShelfLife          AS monthOfShelfLife,
               psk.ShelfLifeUnit             AS shelfLifeUnit,
               psk.Source                    AS source,
               psk.OwnerName                 AS ownerName,
               psk.Company_Id                AS companyId,
               psk.secOwner_Id               AS secOwnerId,
               pic.StatisticsClass           AS statisticsClass,
               pic.StatisticsClassName       AS statisticsClassName,
               pic.SecondStatisticsClass     AS secondStatisticsClass,
               pic.SecondStatisticsClassName AS secondStatisticsClassName,
               pcg.Remark                    AS categoryRemark
        FROM productsku psk
                     LEFT JOIN productinfocategory pic ON psk.ProductInfoCategory_Id = pic.Id
                     LEFT JOIN productcategorygroup pcg ON pic.StatisticsClass = pcg.Id AND pic.categorygroup_id =
                                                                                            pcg.categorygroup_id
    </sql>

    <!-- 供应商版本上线后同sku不同二级货主会有不同的库存查询产品信息及类目用 DISTINCT 去重  -->
    <sql id="ProductInfo_Category_SQL">
        SELECT DISTINCT psk.Id                        AS id,
                        psk.City_Id                   AS orgId,
                        psk.ProductSku_Id             AS productSkuId,
                        psk.Name                      AS productName,
                        psk.SaleModel                 AS saleModel,
                        psk.ProductSpecification_Id   AS productSpecificationId,
                        psk.specificationName         AS specName,
                        psk.packageQuantity           AS specQuantity,
                        psk.packageName               AS packageName,
                        psk.unitName                  AS unitName,
                        psk.productBrand              AS productBrand,
                        psk.ProductState              AS productState,
                        psk.MonthOfShelfLife          AS monthOfShelfLife,
                        psk.ShelfLifeUnit             AS shelfLifeUnit,
                        psk.Source                    AS source,
                        psk.OwnerName                 AS ownerName,
                        psk.Company_Id                AS companyId,
                        psk.secOwner_Id               AS secOwnerId,
                        pic.StatisticsClass           AS statisticsClass,
                        pic.StatisticsClassName       AS statisticsClassName,
                        pic.SecondStatisticsClass     AS secondStatisticsClass,
                        pic.SecondStatisticsClassName AS secondStatisticsClassName,
                        pcg.Remark                    AS categoryRemark
        FROM productsku psk
                     INNER JOIN productskuconfig pc ON pc.ProductSku_Id = psk.ProductSku_Id
                     LEFT JOIN productinfocategory pic ON psk.ProductInfoCategory_Id = pic.Id
                     LEFT JOIN productcategorygroup pcg ON pic.StatisticsClass = pcg.Id AND pic.categorygroup_id =
                                                                                            pcg.categorygroup_id
    </sql>

    <!--根据产品skuId查询产品信息及类目-->
    <select id="findProductInfoAndCategory"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        <include refid="ProductInfo_Category_SQL"/>
        WHERE psk.City_Id = #{cityId,jdbcType=INTEGER}
        <if test="warehouseId != null">
            AND pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="productSkuIdList != null and productSkuIdList.size() > 0">
            AND psk.ProductSku_Id IN
            <foreach collection="productSkuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <!--查询有仓库库存[包含负库存]产品信息及类目-->
    <select id="findHaveInventoryProductInfo"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        <include refid="ProductInfo_Category_SQL"/>
        INNER JOIN productstore ps ON psk.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psk.Company_Id IS NULL AND ps.Owner_Id IS NULL) OR (psk.Company_Id = ps.Owner_Id))
        AND ((psk.secOwner_Id IS NULL) OR (psk.secOwner_Id = ps.secOwner_Id))
        AND psk.City_Id = ps.City_Id
        WHERE pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND ps.TotalCount_MinUnit != 0
        <if test="cityId != null">
            AND psk.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="productSkuIdList != null and productSkuIdList.size() > 0">
            AND psk.ProductSku_Id in
            <foreach collection="productSkuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        AND psk.isDelete=0
        order by psk.ProductSku_Id
    </select>

    <select id="findProductAndTopCategory"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.query.ProductInfoCategoryQuery"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuInfoDTO">
        <include refid="Product_CategoryType_SQL"/>
        WHERE psk.ProductSku_Id IN
        <foreach collection="productSkuIdList" item="skuId" open="(" close=")" separator=",">
            #{skuId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listProductSkuInfoCount" resultType="java.lang.Long">
        select
        coalesce(count(distinct sku.Id))
        from productsku sku
        left join productinfocategory cg on sku.ProductInfoCategory_Id = cg.Id
        <if test="query.limitSku != null and query.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id
            <if test="query.warehouseId != null">
                and pc.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
        </if>
        <if test="(query.allWarehouse != null and query.allWarehouse == 1) or query.hasRealStoreType != null">
            INNER JOIN productstore ps ON sku.City_Id = ps.City_Id
            and sku.ProductSpecification_Id = ps.ProductSpecification_Id
            <if test="query.eraseOwnerId == null or query.eraseOwnerId == 0">
                AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
                AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
            </if>
            <if test="query.warehouseId != null">
                and ps.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="query.hasRealStoreType != null and query.hasRealStoreType == 1">
                and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
            </if>
            <if test="query.hasRealStoreType != null and query.hasRealStoreType == 0">
                and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
            </if>
            <if test="query.hasRealStoreType != null and query.hasRealStoreType == -1">
                and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
            </if>
        </if>
        <where>
            <if test="query.isDelete != null">
                sku.IsDelete = #{query.isDelete,jdbcType=TINYINT}
            </if>
            <if test="query.cityId != null">
                and sku.City_Id = #{query.cityId,jdbcType=INTEGER}
            </if>
            <if test="query.productName != null and query.productName != ''">
                and sku.Name like concat('%',#{query.productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.productBrand != null and query.productBrand != ''">
                and sku.productBrand like concat('%',#{query.productBrand,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.ownerId != null and query.ownerId != 0">
                and sku.Company_Id = #{query.ownerId,jdbcType=BIGINT}
            </if>
            <if test="query.ownerId != null and query.ownerId == 0">
                and sku.Company_Id is null
            </if>
            <if test="query.ownerName != null and query.ownerName != ''">
                and sku.OwnerName like concat('%',#{query.ownerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.source != null">
                and sku.Source = #{query.source,jdbcType=INTEGER}
            </if>
            <if test="query.saleModelList != null and query.saleModelList.size() > 0">
                and sku.SaleModel in
                <foreach collection="query.saleModelList" item="saleModel" separator="," open="(" close=")">
                    #{saleModel,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="query.productStateList != null and query.productStateList.size() > 0">
                and sku.ProductState in
                <foreach collection="query.productStateList" item="productState" separator="," open="(" close=")">
                    #{productState,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="query.statisticsClassList != null and query.statisticsClassList.size() > 0">
                and cg.StatisticsClassName in
                <foreach collection="query.statisticsClassList" item="statisticsClass" separator="," open="(" close=")">
                    #{statisticsClass,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="query.productSkuIdList != null and query.productSkuIdList.size() > 0">
                and sku.ProductSku_Id in
                <foreach collection="query.productSkuIdList" item="productSkuId" separator="," open="(" close=")">
                    #{productSkuId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.storageType != null">
                and sku.StorageType = #{query.storageType,jdbcType=TINYINT}
            </if>
            <if test="query.productTypeList != null and query.productTypeList.size() > 0">
                and sku.productType in
                <foreach collection="query.productTypeList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getProductCharacteristicBySkuIds"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSkuPO">
        select
        ProductFeature as productFeature,ProductSku_Id as productSku_Id
        from productskuconfig
        where Warehouse_Id =#{warehouseId} and
        ProductSku_Id in
        <foreach collection="productSkuList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listSkuBottleCode" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO">
        select productinfo.BottleCode as barCode, productsku.ProductSku_Id as productSkuId
        from productsku
        left join productinfo on productsku.ProductInfo_Id = productinfo.Id
        where
        productsku.City_Id =#{cityId} and
        productsku.ProductSku_Id in
        <foreach collection="skuList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listSkuPackagingCode" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO">
        select productsku.ProductSku_Id as productSkuId, productinfo.packagingCode as barCode
        from productsku
        left join productinfo on productsku.ProductInfo_Id = productinfo.Id
        where
        productsku.City_Id =#{cityId} and
        productsku.ProductSku_Id in
        <foreach collection="skuList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findProductBySkuFull" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        <include refid="ProductSku_Config_Sql"/>
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        left join productinfospecification c on a.ProductSpecification_Id=c.Id
        left join productinfo d on a.ProductInfo_Id=d.Id
        inner join productskuconfig f on f.ProductSku_Id=a.ProductSku_Id
        and f.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <where>
            a.productsku_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="getProductSkuByCon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsku
        where City_Id = #{cityId,jdbcType=INTEGER}
        and ProductSpecification_Id in
        <foreach collection="productSpecificationIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and Company_Id = #{companyId,jdbcType=BIGINT}
        and secOwner_Id = #{dealerId,jdbcType=BIGINT}
        and isDelete=0
    </select>

    <select id="findBySkuOuterCode" resultMap="ResultMapPO">
        select
        <include refid="Base_ProductSku_Sql"/>,
        c.outerCode
        from productsku a
        left join productinfocategory b on a.ProductInfoCategory_Id=b.Id
        inner join productinfospecification c on a.ProductSpecification_Id=c.Id
        inner join productinfo d on a.ProductInfo_Id=d.Id
        <where>
            a.City_id = #{cityId} and
            c.outerCode in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <update id="updateProductStateBatch">
        update productsku
        SET ProductState = #{state,jdbcType=INTEGER}
        WHERE ProductSku_Id IN
        <foreach collection="skuIds" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getSkuConfigStorageAttribute" resultMap="ResultMapPO">
        select sku.productSku_Id as ProductSku_Id, f.StorageAttribute as StorageAttribute
        from productsku sku
        inner join productskuconfig f on f.ProductSku_Id = sku.ProductSku_Id
        where f.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and sku.productSku_Id in
        <foreach item="item" collection="productSkuIdList" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="isDelete != null">
            and sku.IsDelete = #{isDelete,jdbcType=TINYINT}
        </if>
    </select>

    <select id="findProductWarehouseAllocationTypeVerifyBO" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bo.ProductWarehouseAllocationTypeVerifyBO">
        select sku.productSku_Id     as    ProductSkuId,
               sku.Name              as    ProductName,
               csku.Warehouse_Id           WarehouseId,
               sku.ProductSpecification_Id ProductInfoId,
               csku.StorageAttribute as    WarehouseAllocationType
        from sc_product.productsku sku
                 inner join sc_product.productskuconfig csku on csku.ProductSku_Id = sku.ProductSku_Id
        where sku.IsDelete = 0
        AND csku.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.productSkuIds != null">
            and sku.productSku_Id in
            <foreach collection="query.productSkuIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.productSkuIds == null">
            AND sku.ProductSpecification_Id IN
            <foreach collection="query.productInfoIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>

    </select>
</mapper>