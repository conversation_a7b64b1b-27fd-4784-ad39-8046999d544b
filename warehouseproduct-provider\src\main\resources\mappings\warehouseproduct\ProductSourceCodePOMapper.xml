<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodePOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Config_Id" jdbcType="BIGINT" property="configId"/>
        <result column="Code" jdbcType="VARCHAR" property="code"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="SourceBusiness_Id" jdbcType="VARCHAR" property="sourceBusinessId"/>
        <result column="SourceBusinessNo" jdbcType="VARCHAR" property="sourceBusinessNo"/>
        <result column="Business_Id" jdbcType="BIGINT" property="businessId"/>
        <result column="BusinessNo" jdbcType="VARCHAR" property="businessNo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="ProductionDate" jdbcType="VARCHAR" property="productionDate"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="SourceBusinessItem_Id" jdbcType="VARCHAR" property="sourceBusinessItemId"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
    </resultMap>

    <resultMap id="ResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Config_Id" jdbcType="BIGINT" property="configId"/>
        <result column="Code" jdbcType="VARCHAR" property="code"/>
        <result column="Source" jdbcType="TINYINT" property="source"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="SourceBusiness_Id" jdbcType="VARCHAR" property="sourceBusinessId"/>
        <result column="SourceBusinessNo" jdbcType="VARCHAR" property="sourceBusinessNo"/>
        <result column="Business_Id" jdbcType="BIGINT" property="businessId"/>
        <result column="BusinessNo" jdbcType="VARCHAR" property="businessNo"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="Provider_Id" jdbcType="BIGINT" property="providerId"/>
        <result column="Provider" jdbcType="VARCHAR" property="provider"/>
        <result column="productName" jdbcType="VARCHAR" property="productName"/>
        <result column="specificationName" jdbcType="VARCHAR" property="specName"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="SourceBusinessItem_Id" jdbcType="VARCHAR" property="sourceBusinessItemId"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="ProductionDate" jdbcType="VARCHAR" property="productionDate"/>
        <result column="BatchNo" jdbcType="VARCHAR" property="batchNo"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="productSkuId" jdbcType="BIGINT" property="productSkuId"/>
        <collection property="productControlConfigItemPOS" column="Config_Id"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductControlConfigItemPO"
                    select="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductControlConfigItemPOMapper.findProductControlConfigItemByConfigId"/>
        <collection property="productSourceCodeRecordPOS" column="Id"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO"
                    select="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodeRecordPOMapper.findByProductSourceCodeId"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Config_Id, Code, Source, SourceBusiness_Id, SourceBusinessNo, State, Business_Id, BusinessNo, CreateTime,
        LastUpdateTime,
        ProductionDate, BatchNo, SourceBusinessItem_Id, Remark, CreateUser, LastUpdateUser, Warehouse_Id
    </sql>

    <sql id="Column_List">
        psc.Id, psc.Config_Id, psc.Code, psc.Source, psc.SourceBusiness_Id, psc.SourceBusinessNo, psc.State,
        psc.Business_Id, psc.BusinessNo, psc.CreateTime, psc.LastUpdateTime,
        psc.ProductionDate, psc.BatchNo, psc.SourceBusinessItem_Id, psc.Remark, pcc.Provider_Id, pcc.Provider, sku.Name
        as productName, sku.specificationName, psc.LastUpdateUser, psc.CreateUser,
        psc.Warehouse_Id, pcc.ProductSku_Id as productSkuId
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productsourcecode
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO">
        insert into productsourcecode (Id, Config_Id, Code,
        Source, State, SourceBusiness_Id, SourceBusinessNo,
        Business_Id, BusinessNo, CreateTime, LastUpdateTime,
        ProductionDate, BatchNo, SourceBusinessItem_Id,
        Remark, CreateUser, Warehouse_Id
        )
        values (
        #{id,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR},
        #{source,jdbcType=TINYINT}, #{state,jdbcType=TINYINT}, #{sourceBusinessId,jdbcType=BIGINT},
        #{sourceBusinessNo,jdbcType=VARCHAR},
        #{businessId,jdbcType=BIGINT}, #{businessNo,jdbcType=VARCHAR}, now(), now()
        #{productionDate,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{sourceBusinessItemId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{warehouseId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO">
        insert into productsourcecode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="configId != null">
                Config_Id,
            </if>
            <if test="code != null">
                Code,
            </if>
            <if test="source != null">
                Source,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="sourceBusinessId != null">
                SourceBusiness_Id,
            </if>
            <if test="sourceBusinessNo != null">
                SourceBusinessNo,
            </if>
            <if test="businessId != null">
                Business_Id,
            </if>
            <if test="businessNo != null">
                BusinessNo,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="productionDate != null and productionDate != '' ">
                ProductionDate,
            </if>
            <if test="batchNo != null and batchNo != '' ">
                BatchNo,
            </if>
            <if test="sourceBusinessItemId != null and sourceBusinessItemId != '' ">
                SourceBusinessItem_Id,
            </if>
            <if test="remark != null and remark != '' ">
                Remark,
            </if>
            <if test="createUser != null and createUser != '' ">
                CreateUser,
            </if>
            <if test="lastUpdateUser != null and lastUpdateUser != '' ">
                LastUpdateUser,
            </if>
            <if test="warehouseId != null ">
                Warehouse_Id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="sourceBusinessId != null">
                #{sourceBusinessId,jdbcType=VARCHAR},
            </if>
            <if test="sourceBusinessNo != null">
                #{sourceBusinessNo,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=BIGINT},
            </if>
            <if test="businessNo != null">
                #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productionDate != null and productionDate != '' ">
                #{productionDate,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null and batchNo != '' ">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBusinessItemId != null and sourceBusinessItemId != '' ">
                #{sourceBusinessItemId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != '' ">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null and lastUpdateUser != '' ">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null ">
                #{warehouseId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodePO">
        update productsourcecode
        <set>
            <if test="configId != null">
                Config_Id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                Code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                Source = #{source,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="sourceBusinessId != null">
                SourceBusiness_Id = #{sourceBusinessId,jdbcType=VARCHAR},
            </if>
            <if test="sourceBusinessNo != null">
                SourceBusinessNo = #{sourceBusinessNo,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                Business_Id = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="businessNo != null">
                BusinessNo = #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productionDate != null and productionDate != '' ">
                ProductionDate = #{productionDate,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null and batchNo != '' ">
                BatchNo = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceBusinessItemId != null and sourceBusinessItemId != '' ">
                SourceBusinessItem_Id = #{sourceBusinessItemId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != '' ">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null and lastUpdateUser != '' ">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="warehouseId != null ">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsertOrUpdate">
        insert into productsourcecode (Id, Config_Id, Code,
        Source, State, SourceBusiness_Id, SourceBusinessNo,
        Business_Id, BusinessNo, CreateTime, LastUpdateTime,
        ProductionDate, BatchNo, SourceBusinessItem_Id,
        Remark, CreateUser, LastUpdateUser, Warehouse_Id
        )
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id,jdbcType=BIGINT}, #{item.configId,jdbcType=BIGINT}, #{item.code,jdbcType=VARCHAR},
            #{item.source,jdbcType=TINYINT}, #{item.state,jdbcType=TINYINT}, #{item.sourceBusinessId,jdbcType=BIGINT},
            #{item.sourceBusinessNo,jdbcType=VARCHAR},
            #{item.businessId,jdbcType=BIGINT}, #{item.businessNo,jdbcType=VARCHAR}, now(), now(),
            #{item.productionDate,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.sourceBusinessItemId,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR},
            #{item.lastUpdateUser,jdbcType=VARCHAR},
            #{item.warehouseId,jdbcType=INTEGER}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        Config_Id=VALUES(Config_Id),
        Code=VALUES(Code),
        Source=VALUES(Source),
        State=VALUES(State),
        SourceBusiness_Id=VALUES(SourceBusiness_Id),
        SourceBusinessNo=VALUES(SourceBusinessNo),
        Business_Id=VALUES(Business_Id),
        BusinessNo=VALUES(BusinessNo),
        ProductionDate=VALUES(ProductionDate),
        BatchNo=VALUES(BatchNo),
        SourceBusinessItem_Id=VALUES(SourceBusinessItem_Id),
        Warehouse_Id=VALUES(Warehouse_Id)
    </insert>

    <update id="productSourceCodesAssociateOrder">
        update productsourcecode set
        Business_Id =
        case Code
        <foreach collection="list" index="index" item="item">
            WHEN #{item.code,jdbcType=VARCHAR}
            THEN #{item.businessId,jdbcType=BIGINT}
        </foreach>
        end,
        BusinessNo =
        case Code
        <foreach collection="list" index="index" item="item">
            WHEN #{item.code,jdbcType=VARCHAR}
            THEN #{item.businessNo,jdbcType=VARCHAR}
        </foreach>
        end,
        State = 1
        where code in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.code,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getByConfigIdAndCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Code = #{code,jdbcType=VARCHAR}
        and Config_Id = #{configId,jdbcType=BIGINT}
    </select>

    <select id="pageListProductSourceCode" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from productsourcecode psc
        inner join productcontrolconfig pcc
        on psc.Config_Id = pcc.Id
        inner join productsku sku
        on sku.ProductSku_Id = pcc.ProductSku_Id and sku.City_Id = pcc.City_Id
        where 1=1
        <if test="query.warehouseId != null and query.warehouseId != '' ">
            and psc.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.code != null and query.code != '' ">
            and psc.Code = #{query.code,jdbcType=VARCHAR}
        </if>
        <if test="query.configId != null">
            and psc.Config_Id = #{query.configId,jdbcType=BIGINT}
        </if>
        <if test="query.state != null ">
            and psc.State = #{query.state,jdbcType=TINYINT}
        </if>
        <if test="query.source != null ">
            and psc.Source = #{query.source,jdbcType=TINYINT}
        </if>
        <if test="query.sourceBusinessId != null and query.sourceBusinessId != '' ">
            and psc.SourceBusiness_Id = #{query.sourceBusinessId,jdbcType=VARCHAR}
        </if>
        <if test="query.sourceBusinessNo != null and query.sourceBusinessNo != '' ">
            and psc.SourceBusinessNo = #{query.sourceBusinessNo,jdbcType=VARCHAR}
        </if>
        <if test="query.businessId != null ">
            and psc.Business_Id = #{query.businessId,jdbcType=BIGINT}
        </if>
        <if test="query.businessNo != null and query.businessNo != '' ">
            and psc.BusinessNo = #{query.businessNo,jdbcType=VARCHAR}
        </if>
        <if test="query.configIds != null and query.configIds.size() > 0">
            and psc.Config_Id in
            <foreach collection="query.configIds" item="configId" open="(" separator="," close=")">
                #{configId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.productName != null and query.productName != '' ">
            and sku.Name like CONCAT('%', #{query.productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.providerId != null ">
            and pcc.Provider_Id = #{query.providerId,jdbcType=BIGINT}
        </if>
        <if test="query.sourceBusinessItemId != null and query.sourceBusinessItemId != '' ">
            and psc.SourceBusinessItem_Id = #{query.sourceBusinessItemId,jdbcType=VARCHAR}
        </if>
        <if test="query.businessNoList != null and query.businessNoList.size() > 0">
            and psc.BusinessNo in
            <foreach collection="query.businessNoList" item="businessNo" open="(" separator="," close=")">
                #{businessNo,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getProductSourceCode" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from productsourcecode psc
        inner join productcontrolconfig pcc
        on psc.Config_Id = pcc.Id
        inner join productsku sku
        on sku.ProductSku_Id = pcc.ProductSku_Id and sku.City_Id = pcc.City_Id
        where psc.Code = #{code,jdbcType=VARCHAR}
        limit 1
    </select>

    <update id="collectProductSourceCode">
        update productsourcecode set
        BusinessNo =
        case Code
        <foreach collection="list" item="item">
            WHEN #{item.code,jdbcType=VARCHAR}
            THEN #{item.businessNo,jdbcType=VARCHAR}
        </foreach>
        END,
        Business_Id =
        case Code
        <foreach collection="list" item="item">
            WHEN #{item.code,jdbcType=VARCHAR}
            THEN #{item.businessId,jdbcType=BIGINT}
        </foreach>
        END,
        State = 1
        where Config_Id = #{configId,jdbcType=BIGINT}
        and Code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.code,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="findBySourceCodesAndState" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Code in
        <foreach collection="productSourceCodes" item="code" open="(" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
        <if test="sourceCodeState != null ">
            and State = #{sourceCodeState,jdbcType=TINYINT}
        </if>
    </select>

    <update id="clearByBusinessNos">
        update productsourcecode
        set State = 0,
        BusinessNo = null,
        Business_Id = null
        <if test="operator != null">
            ,LastUpdateUser = #{operator,jdbcType=VARCHAR}
        </if>
        where BusinessNo in
        <foreach collection="businessNos" item="businessNo" open="(" separator="," close=")">
            #{businessNo,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteBySourceBusinessNos">
        delete from productsourcecode
        where SourceBusinessNo in
        <foreach collection="sourceBusinessNos" item="sourceBusinessNo" open="(" separator="," close=")">
            #{sourceBusinessNo,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getCodesCountByConfigIdAndCode" resultType="int">
        select count(1) from productsourcecode
        where Config_Id = #{configId,jdbcType=BIGINT}
        and Code = #{code,jdbcType=VARCHAR}
        <if test="sourceCodeState != null ">
            and State = #{sourceCodeState,jdbcType=TINYINT}
        </if>
    </select>

    <select id="getProductSourceByCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="sourceCodeState != null ">
            and State = #{sourceCodeState,jdbcType=TINYINT}
        </if>
    </select>

    <update id="processSourceCodes">
        update productsourcecode
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Business_Id =case" suffix="end,">
                <foreach collection="sourceCodes" item="sourceCode" index="index">
                    when Code = #{sourceCode.code} then #{sourceCode.businessId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="BusinessNo =case" suffix="end,">
                <foreach collection="sourceCodes" item="sourceCode" index="index">
                    when Code = #{sourceCode.code} then #{sourceCode.businessNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="State =case" suffix="end,">
                <foreach collection="sourceCodes" item="sourceCode" index="index">
                    when Code = #{sourceCode.code} then #{sourceCode.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="sourceCodes" item="sourceCode" index="index">
                    when Code = #{sourceCode.code} then #{sourceCode.lastUpdateUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where Code in
        <foreach collection="sourceCodes" item="sourceCode" index="index" open="(" close=")" separator=",">
            #{sourceCode.code}
        </foreach>
    </update>

    <select id="findByBusinessNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where BusinessNo in
        <foreach collection="businessNos" item="businessNo" open="(" separator="," close=")">
            #{businessNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="clearRelationByCodes">
        update productsourcecode
        set State = 0,
        BusinessNo = null,
        Business_Id = null
        <if test="operator != null">
            ,LastUpdateUser = #{operator,jdbcType=VARCHAR}
        </if>
        where Code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="bindProductSourceCodes">
        update productsourcecode
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Business_Id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Id = #{item.id} then #{item.businessId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="BusinessNo =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Id = #{item.id} then #{item.businessNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="State =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Id = #{item.id} then #{item.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Id = #{item.id} then #{item.lastUpdateUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where Id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <select id="getProductSourceCodeAndRecord" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from productsourcecode psc
        inner join productcontrolconfig pcc
        on psc.Config_Id = pcc.Id
        inner join productsku sku
        on sku.ProductSku_Id = pcc.ProductSku_Id and sku.City_Id = pcc.City_Id
        where psc.Code = #{code,jdbcType=VARCHAR}
    </select>

    <select id="getProductSourceByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Id in
        <foreach collection="productSourceCodeIds" item="productSourceCodeId" open="(" separator="," close=")">
            #{productSourceCodeId,jdbcType=BIGINT}
        </foreach>
        <if test="sourceCodeState != null ">
            and State = #{sourceCodeState,jdbcType=TINYINT}
        </if>
    </select>

    <update id="batchUpdateProductSourceCode" parameterType="java.util.List">
        update productsourcecode
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="State =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.state != null ">
                        when id = #{po.id} then #{po.state}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Warehouse_Id =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.warehouseId != null ">
                        when id = #{po.id} then #{po.warehouseId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    when id = #{po.id} then #{po.lastUpdateUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="Business_Id =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.businessId != null ">
                        when id = #{po.id} then #{po.businessId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BusinessNo =case" suffix="end,">
                <foreach collection="list" item="po" index="index">
                    <if test="po.businessNo != null ">
                        when id = #{po.id} then #{po.businessNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            Lastupdatetime = now()
        </trim>
        where Id in
        <foreach collection="list" item="po" index="index" open="(" close=")" separator=",">
            #{po.id}
        </foreach>
    </update>

    <select id="getProductSourceByCodeInfoList"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSourceCodeInfoDTO">
        select
        psc.id as productSourceCodeId,pcc.ProductSku_Id as productSkuId,
        psc.code as productSourceCode,psc.Warehouse_Id as warehouseId, psc.State as state,
        sku.ProductSpecification_Id as productSpecificationId, sku.Company_Id as companyId
        from productsourcecode psc
        INNER JOIN productcontrolconfig pcc ON psc.Config_Id = pcc.id
        INNER JOIN productsku sku ON sku.ProductSku_Id = pcc.ProductSku_Id and sku.City_Id = pcc.City_Id
        WHERE
        <foreach collection="codeSkuList" item="codeSku" open="(" separator="or" close=")">
            (psc.code = #{codeSku.sourceCode,jdbcType=VARCHAR} and psc.Warehouse_Id =
            #{codeSku.warehouseId,jdbcType=INTEGER})
        </foreach>
        <if test="sourceCodeState != null ">
            and psc.State = #{sourceCodeState,jdbcType=TINYINT}
        </if>
    </select>

    <update id="updateProductSourceState">
        update productsourcecode
        set `state` = #{state,jdbcType=TINYINT}, LastUpdateUser = #{operator,jdbcType=VARCHAR}, LastUpdateTime = now()
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getProductSourceByCodeIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecode
        where Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>