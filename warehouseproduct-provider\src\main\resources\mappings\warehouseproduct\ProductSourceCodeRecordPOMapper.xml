<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSourceCodeRecordPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="ProductSourceCode_Id" jdbcType="BIGINT" property="productSourceCodeId"/>
        <result column="Code" jdbcType="VARCHAR" property="code"/>
        <result column="Provider_Id" jdbcType="BIGINT" property="providerId"/>
        <result column="Provider" jdbcType="VARCHAR" property="provider"/>
        <result column="BeforeState" jdbcType="TINYINT" property="beforeState"/>
        <result column="AfterState" jdbcType="TINYINT" property="afterState"/>
        <result column="BusinessType" jdbcType="TINYINT" property="businessType"/>
        <result column="Business_Id" jdbcType="VARCHAR" property="businessId"/>
        <result column="BusinessNo" jdbcType="VARCHAR" property="businessNo"/>
        <result column="Description" jdbcType="VARCHAR" property="describe"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Warehouse_Id, ProductSourceCode_Id, Code, Provider_Id, Provider, BeforeState,
        AfterState, BusinessType, Business_Id, BusinessNo, Description, Remark, CreateTime,
        CreateUser, LastUpdateTime, LastUpdateUser
    </sql>

    <select id="listProductSourceCodeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecoderecord
        where 1=1
        <if test="code != null">
            and Code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId != null">
            and Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="productSourceCodeId != null">
            and ProductSourceCode_Id = #{productSourceCodeId,jdbcType=BIGINT}
        </if>
        order by CreateTime desc, Id
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecoderecord
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productsourcecoderecord
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="listByCodeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecoderecord
        where ProductSourceCode_Id = #{productSourceCodeId,jdbcType=BIGINT}
    </select>

    <delete id="deleteBatch" parameterType="java.util.List">
        delete from productsourcecoderecord
        where Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO">
        insert into productsourcecoderecord
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSourceCodeId != null">
                ProductSourceCode_Id,
            </if>
            <if test="code != null">
                Code,
            </if>
            <if test="providerId != null">
                Provider_Id,
            </if>
            <if test="provider != null">
                Provider,
            </if>
            <if test="beforeState != null">
                BeforeState,
            </if>
            <if test="afterState != null">
                AfterState,
            </if>
            <if test="businessType != null">
                BusinessType,
            </if>
            <if test="businessId != null">
                Business_Id,
            </if>
            <if test="businessNo != null">
                BusinessNo,
            </if>
            <if test="describe != null">
                Description,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSourceCodeId != null">
                #{productSourceCodeId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="providerId != null">
                #{providerId,jdbcType=BIGINT},
            </if>
            <if test="provider != null">
                #{provider,jdbcType=VARCHAR},
            </if>
            <if test="beforeState != null">
                #{beforeState,jdbcType=TINYINT},
            </if>
            <if test="afterState != null">
                #{afterState,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessNo != null">
                #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="describe != null">
                #{describe,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO productsourcecoderecord (
        Id,
        Warehouse_Id,
        ProductSourceCode_Id,
        Code,
        Provider_Id,
        Provider,
        BeforeState,
        AfterState,
        BusinessType,
        Business_Id,
        BusinessNo,
        Description,
        Remark,
        CreateUser,
        LastUpdateUser
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.productSourceCodeId,jdbcType=BIGINT},
            #{item.code,jdbcType=VARCHAR},
            #{item.providerId,jdbcType=BIGINT},
            #{item.provider,jdbcType=VARCHAR},
            #{item.beforeState,jdbcType=TINYINT},
            #{item.afterState,jdbcType=TINYINT},
            #{item.businessType,jdbcType=TINYINT},
            #{item.businessId,jdbcType=VARCHAR},
            #{item.businessNo,jdbcType=VARCHAR},
            #{item.describe,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.lastUpdateUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSourceCodeRecordPO">
        update productsourcecoderecord
        <set>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSourceCodeId != null">
                ProductSourceCode_Id = #{productSourceCodeId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                Code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="providerId != null">
                Provider_Id = #{providerId,jdbcType=BIGINT},
            </if>
            <if test="provider != null">
                Provider = #{provider,jdbcType=VARCHAR},
            </if>
            <if test="beforeState != null">
                BeforeState = #{beforeState,jdbcType=TINYINT},
            </if>
            <if test="afterState != null">
                AfterState = #{afterState,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                BusinessType = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="businessId != null">
                Business_Id = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessNo != null">
                BusinessNo = #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="describe != null">
                Description = #{describe,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listProductSourceCodeRecordIds" resultType="java.lang.Long">
        select id
        from productsourcecoderecord
        where
        <foreach collection="list" item="item" separator="or">
            (
            ProductSourceCode_Id = #{item.productSourceCodeId,jdbcType=BIGINT}
            <if test="item.businessNo != null">
                and BusinessNo = #{item.businessNo,jdbcType=VARCHAR}
            </if>
            <if test="item.businessNo == null">
                and BusinessNo is null
            </if>
            and AfterState = #{item.afterState,jdbcType=TINYINT}
            )
        </foreach>
    </select>

    <select id="getProductSourceCodeState" resultType="java.lang.Byte">
        select AfterState
        from productsourcecoderecord
        where ProductSourceCode_Id = #{productSourceCodeId,jdbcType=BIGINT}
        order by CreateTime desc limit 1
    </select>

    <select id="findByProductSourceCodeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsourcecoderecord
        where ProductSourceCode_Id = #{productSourceCodeId}
    </select>
</mapper>