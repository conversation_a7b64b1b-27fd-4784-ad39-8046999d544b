<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductStoreBatchPOMapper">
    <sql id="base_column_sql">
        id
        ,productstore_id,totalcount_minunit,productiondate, expiretime,
        batchtime,createtimeDate, createuserId,lastupdatetime, lastupdateuserId,
        BatchAttributeInfoNo,location_id, location_name,locationCategory,subcategory
    </sql>
    <select id="countHavingSku" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from productstorebatch where productstorebatch.totalcount_minunit!=0 and location_id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateProductStoreBatchLocation">
        update productstorebatch set
        location_id = #{locationId,jdbcType=BIGINT},
        location_name = #{locationName,jdbcType=VARCHAR}
        where Id in
        <foreach collection="lstId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="countHavingGroupByLocationId"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationStorageNumDTO"
            parameterType="java.util.List">
        select location_id AS id, count(*) storageNum from productstorebatch where totalcount_minunit!=0
        and location_id in
        <foreach collection="locationIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by location_id
    </select>
    <select id="invertoryBatchStateList" resultType="java.util.Map">
        SELECT
        psb.location_id AS locationId,
        CASE WHEN count(CASE WHEN psb.totalcount_minunit <![CDATA[<]]> 0 THEN 1 ELSE null END) <![CDATA[>]]>  0 THEN 2
        WHEN count(CASE WHEN psb.totalcount_minunit <![CDATA[>]]> 0 THEN 1 ELSE null END) <![CDATA[>]]> 0 THEN 0
        WHEN count(CASE WHEN psb.totalcount_minunit <![CDATA[=]]> 0 THEN 1 ELSE null END) <![CDATA[>]]> 0 THEN 1
        ELSE 3 END AS status
        from productstorebatch psb
        inner join productstore ps on psb.productstore_id =ps.id
        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join location loc on loc.Id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER} and ps.City_Id = #{dto.cityId,jdbcType=INTEGER} and
        psb.location_id is not null and
        loc.Id IN
        <foreach collection="dto.locationIds" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        group by locationid
    </select>
    <select id="invertoryStateList" resultType="java.util.Map">
        SELECT loc.id AS locationId,
        CASE
        WHEN SUM(CASE WHEN ps.TotalCount_MinUnit <![CDATA[<]]> 0 THEN 1 ELSE 0 END) <![CDATA[>]]> 0 THEN 2
        WHEN SUM(CASE WHEN ps.TotalCount_MinUnit  <![CDATA[>]]> 0 THEN 1 ELSE 0 END) <![CDATA[>]]> 0 THEN 0
        WHEN SUM(CASE WHEN ps.TotalCount_MinUnit  <![CDATA[=]]> 0 THEN 1 ELSE 0 END) <![CDATA[>]]> 0 THEN 1
        ELSE 3 END AS status
        FROM sc_product.productsku sku
        LEFT JOIN sc_product.productstore ps ON ps.productspecification_id = sku.productspecification_id
        AND ps.Channel = 0
        AND ps.city_id = sku.city_id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        and ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        LEFT JOIN sc_product.productlocation pl ON sku.ProductSku_Id = pl.ProductSku_Id
        and pl.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
        LEFT JOIN sc_product.location loc ON pl.Location_Id = loc.id and loc.warehouse_id = pl.Warehouse_Id
        LEFT JOIN sc_product.location area ON loc.area_id = area.id
        where sku.City_Id = #{dto.cityId}
        and ps.Warehouse_Id = #{dto.warehouseId}
        and ps.City_Id = #{dto.cityId}
        and pl.Location_Id is not null
        and pl.Location_Id IN
        <foreach collection="dto.locationIds" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        group by loc.id
        order by loc.Id desc;
    </select>
    <select id="productShelfLifeList" resultType="java.util.Map">
        select psb.location_id as locationId,<![CDATA[
               case
                   when MIN(psb.productiondate) is null or
                        IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) is null then 0
                   when DATEDIFF(now(), MIN(psb.productionDate)) <=
                        (case IFNULL(MIN(pi.ShelfLifeUnit), MIN(psku.ShelfLifeUnit))
                             when 1 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 365
                             when 2 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                             when 0 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                             else IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) end * 2 / 3) then 0
                   when DATEDIFF(now(), MIN(psb.productionDate)) <=
                        (case IFNULL(MIN(pi.ShelfLifeUnit), MIN(psku.ShelfLifeUnit))
                             when 1 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 365
                             when 2 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                             when 0 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                             else IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife))
                            end)
                       and DATEDIFF(now(), MIN(psb.productionDate)) >
                           (case IFNULL(MIN(pi.ShelfLifeUnit), MIN(psku.ShelfLifeUnit))
                                when 1 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 365
                                when 2 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                                when 0 then IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife)) * 30
                                else IFNULL(MIN(pi.MonthOfShelfLife), MIN(psku.MonthOfShelfLife))
                                end * 2 / 3) then 1
                   else 2 end ]]>AS status
        FROM sc_product.productstore ps
        INNER JOIN sc_product.productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN sc_product.productstorebatch psb on psb.productstore_id = ps.id
        LEFT JOIN sc_product.productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN sc_product.Location loc on loc.id = psb.location_id and loc.warehouse_id = ps.warehouse_id
        where ps.Warehouse_Id = #{dto.warehouseId}
        and ps.City_Id = #{dto.cityId} and psb.location_id in
        <foreach collection="dto.locationIds" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        group by psb.location_id;
    </select>

    <insert id="insertIntoNewWarehouseStoreFromOtherWarehouse">
        insert into productstore
        select replace(uuid(),
        ‘-’,’’),916,9001034,TotalCount_Minunit,ProductSpecification_Id,ownerType,Owner_Id,createtime,lastupdatetime,secowner_id,channel,lastmodifytime,unifyskuid
        from productstore where warehouse_id = 1031;
    </insert>

    <insert id="insertIntoNewWarehouseStoreBatch">
        insert into
        productstorebatch(id,productstore_id,totalcount_minunit,createtime,createuserId,lastupdatetime,lastupdateuserId,BatchAttributeInfoNo,location_id,location_name,locationCategory,subcategory)
        select replace(uuid(), '-',''),id,10000,now(),1,now(),1,concat('PSX-',FLOOR(RAND() * 1000000000) + 1),
        5356183290574585443, 'JHQ1-1', 0, 24
        from productstore where warehouse_id = 9001034;
    </insert>

</mapper>
