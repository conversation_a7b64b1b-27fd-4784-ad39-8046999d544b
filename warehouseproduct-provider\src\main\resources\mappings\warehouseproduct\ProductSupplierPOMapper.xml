<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSupplierPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="SupplierType" property="supplierType" jdbcType="TINYINT"/>
        <result column="SupplierId" property="supplierId" jdbcType="VARCHAR"/>
        <result column="SupplierName" property="supplierName" jdbcType="VARCHAR"/>
        <result column="IsDefault" property="isDefault" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id, ProductSpecification_Id, Owner_Id, SecOwner_Id, SupplierType,
        SupplierId, SupplierName, IsDefault, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from productsupplier
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from productsupplier
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO">
        insert into productsupplier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="cityId != null">
                City_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id,
            </if>
            <if test="ownerId != null">
                Owner_Id,
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id,
            </if>
            <if test="supplierType != null">
                SupplierType,
            </if>
            <if test="supplierId != null">
                SupplierId,
            </if>
            <if test="supplierName != null">
                SupplierName,
            </if>
            <if test="isDefault != null">
                IsDefault,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSpecificationId != null">
                #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="supplierType != null">
                #{supplierType,jdbcType=TINYINT},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                #{isDefault,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.ProductSupplierPO">
        update productsupplier
        <set>
            <if test="cityId != null">
                City_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                Warehouse_Id = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="productSpecificationId != null">
                ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                Owner_Id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="secOwnerId != null">
                SecOwner_Id = #{secOwnerId,jdbcType=BIGINT},
            </if>
            <if test="supplierType != null">
                SupplierType = #{supplierType,jdbcType=TINYINT},
            </if>
            <if test="supplierId != null">
                SupplierId = #{supplierId,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                SupplierName = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                IsDefault = #{isDefault,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectProductSupplierBySpecInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productsupplier
        <where>
            Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            <if test="specList != null and specList.size() > 0">
                AND
                <foreach collection="specList" item="item" open="(" close=")" separator="or">
                    (
                    ProductSpecification_Id = #{item.productSpecificationId,jdbcType=BIGINT}
                    <if test="item.ownerId == null">AND Owner_Id is null</if>
                    <if test="item.ownerId != null">AND Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    <if test="item.secOwnerId == null">AND SecOwner_Id is null</if>
                    <if test="item.secOwnerId != null">AND SecOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
        </where>
        order by Warehouse_Id, ProductSpecification_Id, Owner_Id, SecOwner_Id
    </select>

    <insert id="insertOrUpdateBatchSupplierList" parameterType="java.util.List">
        insert into productsupplier
        (
        id, City_Id, Warehouse_Id, ProductSpecification_Id,
        Owner_Id, SecOwner_Id, SupplierType, SupplierId,
        SupplierName, IsDefault, CreateUser, LastUpdateUser, LastUpdateTime
        ) VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.cityId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.ownerId,jdbcType=BIGINT}, #{item.secOwnerId,jdbcType=BIGINT}, #{item.supplierType,jdbcType=TINYINT},
            #{item.supplierId,jdbcType=VARCHAR},
            #{item.supplierName,jdbcType=VARCHAR}, #{item.isDefault,jdbcType=TINYINT},
            #{item.createUser,jdbcType=VARCHAR}, #{item.lastUpdateUser,jdbcType=VARCHAR}, SYSDATE()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        IsDefault=VALUES(IsDefault),
        LastUpdateUser=VALUES(LastUpdateUser),
        LastUpdateTime=VALUES(LastUpdateTime)
    </insert>

</mapper>