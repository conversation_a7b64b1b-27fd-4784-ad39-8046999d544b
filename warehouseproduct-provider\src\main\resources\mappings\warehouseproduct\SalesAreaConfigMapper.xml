<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SalesAreaConfigMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="WarehouseName" property="warehouseName" jdbcType="INTEGER"/>
        <result column="CompanyCode" property="companyCode" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        sc.Id, sc.Warehouse_Id,wa.Name as WarehouseName, sc.CompanyCode, sc.State, sc.Remark, sc.CreateTime,
        sc.CreateUser, sc.LastUpdateUser,
        sc.LastUpdateTime
    </sql>

    <resultMap id="ResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="WarehouseName" property="warehouseName" jdbcType="INTEGER"/>
        <result column="CompanyCode" property="companyCode" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <collection property="items"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO">
            <id column="sci_Id" property="id" jdbcType="BIGINT"/>
            <result column="Parent_Id" property="parentId" jdbcType="BIGINT"/>
            <result column="Province" property="province" jdbcType="VARCHAR"/>
            <result column="City" property="city" jdbcType="VARCHAR"/>
            <result column="County" property="county" jdbcType="VARCHAR"/>
            <result column="Street" property="street" jdbcType="VARCHAR"/>
            <result column="Type" property="type" jdbcType="TINYINT"/>
            <result column="Item_Id" property="itemId" jdbcType="VARCHAR"/>
            <result column="ItemName" property="itemName" jdbcType="VARCHAR"/>
            <result column="sci_State" property="state" jdbcType="TINYINT"/>
            <result column="sci_Remark" property="remark" jdbcType="VARCHAR"/>
            <result column="sci_CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="sci_CreateUser" property="createUser" jdbcType="VARCHAR"/>
            <result column="sci_LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
            <result column="sci_LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <sql id="Column_List">
        sc.Id, sc.Warehouse_Id,wa.Name as WarehouseName, sc.CompanyCode, sc.State, sc.Remark, sc.CreateTime,
        sc.CreateUser, sc.LastUpdateUser,
        sc.LastUpdateTime,
        sci.Id as sci_Id, sci.Parent_Id, sci.Province, sci.City, sci.County, sci.Street, sci.Type, sci.Item_Id,
        sci.ItemName,
        sci.State as sci_State, sci.Remark as sci_Remark, sci.CreateTime as sci_CreateTime, sci.CreateUser as
        sci_CreateUser,
        sci.LastUpdateUser as sci_LastUpdateUser, sci.LastUpdateTime as sci_LastUpdateTime
    </sql>

    <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from salesareaconfig sc
        left join warehouse wa on wa.id = sc.warehouse_id
        where sc.Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from salesareaconfig sc
        left join warehouse wa on wa.id = sc.warehouse_id
        where 1=1
        <if test="dto.warehouseId != null">
            and sc.Warehouse_Id = #{dto.warehouseId}
        </if>
        <if test="dto.companyCode != null">
            and sc.companyCode = #{dto.companyCode}
        </if>
        <if test="dto.state != null">
            and sc.state = #{dto.state}
        </if>
        order by sc.createTime desc
    </select>
    <delete id="deleteByIds" parameterType="java.lang.Long">
        delete from salesareaconfig
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>
    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO">
        insert into salesareaconfig (Id, Warehouse_Id, CompanyCode,
        State, Remark,
        CreateUser, LastUpdateUser
        )
        values
        (#{dto.id,jdbcType=BIGINT}, #{dto.warehouseId,jdbcType=INTEGER}, #{dto.companyCode,jdbcType=VARCHAR},
        #{dto.state,jdbcType=TINYINT}, #{dto.remark,jdbcType=VARCHAR},
        #{dto.createUser,jdbcType=VARCHAR}, #{dto.lastUpdateUser,jdbcType=VARCHAR}
        )
    </insert>
    <update id="update"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaConfigDTO">
        update salesareaconfig
        <set>
            <if test="companyCode != null">
                CompanyCode = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR}
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateState">
        update salesareaconfig
        <set>
            State = #{state,jdbcType=TINYINT}
        </set>
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from salesareaconfig sc
        left join warehouse wa on wa.id = sc.warehouse_id
        where sc.Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAllById" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from salesareaconfig sc
        left join salesareaitem sci on sc.Id = sci.Parent_Id
        left join warehouse wa on wa.id = sc.warehouse_id
        where sc.Id = #{id,jdbcType=BIGINT}
    </select>
</mapper>