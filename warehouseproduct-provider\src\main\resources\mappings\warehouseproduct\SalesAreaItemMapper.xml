<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SalesAreaItemMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Parent_Id" property="parentId" jdbcType="BIGINT"/>
        <result column="Province" property="province" jdbcType="VARCHAR"/>
        <result column="City" property="city" jdbcType="VARCHAR"/>
        <result column="County" property="county" jdbcType="VARCHAR"/>
        <result column="Street" property="street" jdbcType="VARCHAR"/>
        <result column="Type" property="type" jdbcType="TINYINT"/>
        <result column="Item_Id" property="itemId" jdbcType="VARCHAR"/>
        <result column="ItemName" property="itemName" jdbcType="VARCHAR"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Parent_Id, Province, City, County, Street, Type, Item_Id, ItemName, State, Remark,
        CreateTime, CreateUser, LastUpdateUser, LastUpdateTime
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from salesareaitem
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByParentIds" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from salesareaitem
        where Parent_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <delete id="deleteByIds" parameterType="java.lang.Long">
        delete from salesareaitem
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteParentIds" parameterType="java.lang.Long">
        delete from salesareaitem
        where Parent_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <insert id="insertBatch"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO">
        insert into salesareaitem (Id, Parent_Id, Province,
        City, County, Street,
        Type, Item_Id, ItemName,
        State, Remark,
        CreateUser, LastUpdateUser
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.parentId,jdbcType=BIGINT}, #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.county,jdbcType=VARCHAR}, #{item.street,jdbcType=VARCHAR},
            #{item.type,jdbcType=TINYINT}, #{item.itemId,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, #{item.lastUpdateUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertUpdateBatch"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO">
        insert into salesareaitem (Id, Parent_Id, Province,
        City, County, Street,
        Type, Item_Id, ItemName,
        State, Remark,
        CreateUser, LastUpdateUser
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.parentId,jdbcType=BIGINT}, #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.county,jdbcType=VARCHAR}, #{item.street,jdbcType=VARCHAR},
            #{item.type,jdbcType=TINYINT}, #{item.itemId,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR}, #{item.lastUpdateUser,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        Province=VALUES(Province),
        City=VALUES(City),
        County=VALUES(County),
        Street=VALUES(Street),
        Type=VALUES(Type),
        Item_Id=VALUES(Item_Id),
        ItemName=VALUES(ItemName),
        State=VALUES(State),
        Remark=VALUES(Remark),
        LastUpdateUser=VALUES(LastUpdateUser)
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.salesArea.SalesAreaItemDTO">
        update salesareaitem
        <set>
            <if test="parentId != null">
                Parent_Id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                Street = #{street,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                Type = #{type,jdbcType=TINYINT},
            </if>
            <if test="itemId != null">
                Item_Id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="itemName != null">
                ItemName = #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStateByParentId">
        update salesareaitem
        <set>
            State = #{state,jdbcType=TINYINT}
        </set>
        where Parent_Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateStateByIds">
        update salesareaitem
        <set>
            State = #{state,jdbcType=TINYINT}
        </set>
        where Id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="pageListSalesAreaItems" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from salesareaitem
        where 1=1
        <if test="query.province != null and query.province != ''">
            and Province = #{query.province,jdbcType=VARCHAR}
        </if>
        <if test="query.city != null and query.city != ''">
            and City = #{query.city,jdbcType=VARCHAR}
        </if>
        <if test="query.county != null and query.county != ''">
            and (County = #{query.county,jdbcType=VARCHAR} or (County = '' and Street = ''))
        </if>
        <if test="query.street != null and query.street != ''">
            and (Street = #{query.street,jdbcType=VARCHAR} or Street = '')
        </if>
        <if test="query.state != null">
            and State = #{query.state,jdbcType=TINYINT}
        </if>
        <if test="query.type != null">
            and Type = #{query.type,jdbcType=TINYINT}
        </if>
    </select>
</mapper>