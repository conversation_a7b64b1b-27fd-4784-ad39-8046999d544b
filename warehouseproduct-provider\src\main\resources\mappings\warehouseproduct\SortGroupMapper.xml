<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Name" property="name" jdbcType="VARCHAR"/>
        <result column="SortGroupType" property="sortGroupType" jdbcType="TINYINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="Flag" property="flag" jdbcType="TINYINT"/>
        <result column="SortGroupPickWay" property="sortGroupPickWay" jdbcType="TINYINT"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="callNum" property="callNum" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ListResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupListPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Name" property="name" jdbcType="VARCHAR"/>
        <result column="SortGroupType" property="sortGroupType" jdbcType="TINYINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="Type" property="type" jdbcType="VARCHAR"/>
        <result column="Area" property="area" jdbcType="VARCHAR"/>
        <result column="Location" property="location" jdbcType="VARCHAR"/>
        <result column="SortUser" property="sortUser" jdbcType="VARCHAR"/>
        <result column="Flag" property="flag" jdbcType="TINYINT"/>
        <result column="SortGroupPickWay" property="sortGroupPickWay" jdbcType="TINYINT"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="callNum" property="callNum" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Name, SortGroupType, Warehouse_Id, Remark, Flag,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser,SortGroupPickWay, State, callNum
    </sql>

    <select id="listGroupByCondition" resultMap="ListResultMap">
        SELECT * FROM (
        SELECT Id, Name, SortGroupType, Remark, Flag, SortGroupPickWay,State, callNum,
        case SortGroupType when 3 then (SELECT b.SortName FROM (SELECT SortGroup_Id, group_concat(SortName separator
        '、') SortName FROM sortgroupsetting WHERE SortType = 3 GROUP BY SortGroup_Id) b WHERE b.SortGroup_Id = a.Id)
        else '' end Type,
        case SortGroupType when 1 then (SELECT b.SortName FROM (SELECT SortGroup_Id, group_concat(SortName separator
        '、') SortName FROM sortgroupsetting WHERE SortType = 1 GROUP BY SortGroup_Id) b WHERE b.SortGroup_Id = a.Id)
        else '' end Area,
        case SortGroupType when 2 then (SELECT b.SortName FROM (SELECT SortGroup_Id, group_concat(SortName separator
        '、') SortName FROM sortgroupsetting WHERE SortType = 2 GROUP BY SortGroup_Id) b WHERE b.SortGroup_Id = a.Id)
        else '' end Location,
        (SELECT b.UserName FROM (SELECT SortGroup_Id, group_concat(UserName separator '、') UserName FROM sortgroupuser
        GROUP BY SortGroup_Id) b WHERE b.SortGroup_Id = a.Id) SortUser
        FROM sortgroup a
        <where>
            <if test="warehouseId != null">
                AND Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="groupType != null">
                AND SortGroupType = #{groupType,jdbcType=INTEGER}
            </if>
            <if test="flag != null">
                AND Flag = #{flag,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                AND state = #{state,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY CreateTime
        ) s
        <where>
            <if test="type != null and type != ''">
                AND Type LIKE concat(concat('%',#{type,jdbcType=VARCHAR}),'%')
            </if>
            <if test="area != null and area != ''">
                AND Area LIKE concat(concat('%',#{area,jdbcType=VARCHAR}),'%')
            </if>
            <if test="location != null and location != ''">
                AND Location LIKE concat(concat('%',#{location,jdbcType=VARCHAR}),'%')
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroup
        WHERE Id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySortGroupDTOList" resultMap="BaseResultMap"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupSO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroup
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="groupType != null">
            AND SortGroupType = #{groupType,jdbcType=TINYINT}
        </if>
        <if test="flag != null">
            AND Flag = #{flag,jdbcType=TINYINT}
        </if>
        <if test="callNum != null">
            AND callNum = #{callNum, jdbcType=VARCHAR}
        </if>
        <if test="state != null">
            AND state = #{state,jdbcType=TINYINT}
        </if>
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupPO">
        INSERT INTO sortgroup (
        Id,
        Name,
        SortGroupType,
        Warehouse_Id,
        Remark,
        CreateUser,
        Flag
        )
        VALUES(
        #{id,jdbcType=BIGINT},
        #{name,jdbcType=VARCHAR},
        #{sortGroupType,jdbcType=TINYINT},
        #{warehouseId,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR},
        #{flag,jdbcType=TINYINT}
        )
    </insert>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupPO">
        UPDATE sortgroup
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sortGroupPickWay != null">
                SortGroupPickWay = #{sortGroupPickWay,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="callNum != null">
                callNum = #{callNum,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM sortgroup
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectSortNameByIds" resultType="java.lang.String">
        select s.SortName
        from sortgroup g
        inner join sortgroupsetting s on g.Id = s.SortGroup_Id
        where g.Id in
        <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>