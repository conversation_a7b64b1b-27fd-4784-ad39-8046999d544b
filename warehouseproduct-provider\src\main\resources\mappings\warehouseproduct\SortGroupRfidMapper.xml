<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupRfidMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupRfidPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="rfidType" jdbcType="TINYINT" property="rfidType"/>
        <result column="controlId" jdbcType="BIGINT" property="controlId"/>
        <result column="controlName" jdbcType="VARCHAR" property="controlName"/>
        <result column="deviceId" jdbcType="BIGINT" property="deviceId"/>
        <result column="deviceTagNo" jdbcType="VARCHAR" property="deviceTagNo"/>
        <result column="deviceTypeName" jdbcType="VARCHAR" property="deviceTypeName"/>
        <result column="busId" jdbcType="BIGINT" property="busId"/>
        <result column="busTagNo" jdbcType="VARCHAR" property="busTagNo"/>
        <result column="busTypeName" jdbcType="VARCHAR" property="busTypeName"/>
        <result column="sortId" jdbcType="BIGINT" property="sortId"/>
        <result column="sortName" jdbcType="VARCHAR" property="sortName"/>
        <result column="enable" jdbcType="TINYINT" property="enable"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="lastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>

    <resultMap id="BaseDTOResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.SortGroupRfidDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="rfidType" jdbcType="TINYINT" property="rfidType"/>
        <result column="controlId" jdbcType="BIGINT" property="controlId"/>
        <result column="controlName" jdbcType="VARCHAR" property="controlName"/>
        <result column="deviceId" jdbcType="BIGINT" property="deviceId"/>
        <result column="deviceTagNo" jdbcType="VARCHAR" property="deviceTagNo"/>
        <result column="deviceTypeName" jdbcType="VARCHAR" property="deviceTypeName"/>
        <result column="busId" jdbcType="BIGINT" property="busId"/>
        <result column="busTagNo" jdbcType="VARCHAR" property="busTagNo"/>
        <result column="busTypeName" jdbcType="VARCHAR" property="busTypeName"/>
        <result column="sortId" jdbcType="BIGINT" property="sortId"/>
        <result column="sortName" jdbcType="VARCHAR" property="sortName"/>
        <result column="enable" jdbcType="TINYINT" property="enable"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="lastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
                , warehouseId, rfidType, controlId,controlName, deviceId,
        deviceTagNo, deviceTypeName, busId, busTagNo, busTypeName, sortId, sortName,
        enable, remark, createTime, createUser, lastUpdateUser, lastUpdateTime
    </sql>

    <select id="listSortGroupRfid" resultMap="BaseDTOResultMap">
        select
        <include refid="Base_Column_List"/>
        from sortgrouprfid
        <where>
            warehouseId = #{warehouseId,jdbcType=INTEGER}
            <if test="rfidType != null">
                and rfidType = #{rfidType,jdbcType=TINYINT}
            </if>
            <if test="enable != null and enable == 1">
                and enable = #{enable,jdbcType=TINYINT}
            </if>
            <if test="controlId != null">
                and controlId = #{controlId,jdbcType=BIGINT}
            </if>
            <if test="controlName != null and controlName != ''">
                and controlName = #{controlName,jdbcType=VARCHAR}
            </if>
            <if test="deviceId != null">
                and deviceId = #{deviceId,jdbcType=BIGINT}
            </if>
            <if test="deviceTagNo != null and deviceTagNo != ''">
                and deviceTagNo = #{deviceTagNo,jdbcType=VARCHAR}
            </if>
            <if test="deviceTypeName != null and deviceTypeName != ''">
                and deviceTypeName = #{deviceTypeName,jdbcType=VARCHAR}
            </if>
            <if test="busId != null">
                and busId = #{busId,jdbcType=BIGINT}
            </if>
            <if test="busTagNo != null and busTagNo != ''">
                and busTagNo = #{busTagNo,jdbcType=VARCHAR}
            </if>
            <if test="busTypeName != null and busTypeName != ''">
                and busTypeName= #{busTypeName,jdbcType=VARCHAR}
            </if>
            <if test="sortIdList != null and sortIdList.size() > 0 ">
                and sortId in
                <foreach collection="sortIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sortgrouprfid (
        id,
        warehouseId,
        rfidType,
        controlId,
        controlName,
        deviceId,
        deviceTagNo,
        deviceTypeName,
        busId,
        busTagNo,
        busTypeName,
        sortId,
        sortName,
        enable,
        remark,
        createUser,
        createTime,
        lastUpdateUser,
        lastUpdateTime
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.rfidType,jdbcType=TINYINT},
            #{item.controlId,jdbcType=BIGINT},
            #{item.controlName,jdbcType=VARCHAR},
            #{item.deviceId,jdbcType=BIGINT},
            #{item.deviceTagNo,jdbcType=VARCHAR},
            #{item.deviceTypeName,jdbcType=VARCHAR},
            #{item.busId,jdbcType=BIGINT},
            #{item.busTagNo,jdbcType=VARCHAR},
            #{item.busTypeName,jdbcType=VARCHAR},
            #{item.sortId,jdbcType=BIGINT},
            #{item.sortName,jdbcType=VARCHAR},
            #{item.enable,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=VARCHAR},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <delete id="deleteByPrimaryKeyBatch" parameterType="java.lang.Long">
        delete from sortgrouprfid
        where id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>