<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupSettingMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupSettingPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="SortGroup_Id" property="sortGroupId" jdbcType="BIGINT"/>
        <result column="SortType" property="sortType" jdbcType="TINYINT"/>
        <result column="Sort_Id" property="sortId" jdbcType="VARCHAR"/>
        <result column="SortName" property="sortName" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, SortGroup_Id, SortType, Sort_Id, SortName, CreateTime
    </sql>

    <select id="listByGroupId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </select>

    <select id="listByGroupIds" resultMap="BaseResultMap" parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="countByGroupId" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </select>
    <select id="pageListByGroupId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT} order by CreateTime desc limit #{index},#{totalCount}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sortgroupsetting (
        Id,
        SortGroup_Id,
        SortType,
        Sort_Id,
        SortName
        )
        VALUES
        <foreach collection="list" item="settting" separator=",">
            (
            #{settting.id,jdbcType=BIGINT},
            #{settting.sortGroupId,jdbcType=BIGINT},
            #{settting.sortType,jdbcType=TINYINT},
            #{settting.sortId,jdbcType=VARCHAR},
            #{settting.sortName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <delete id="deleteByGroupId" parameterType="java.lang.Long">
        DELETE FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </delete>

    <select id="newPageListByGroupId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id in
        <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findListByGroupIdCount" parameterType="java.lang.Long" resultType="java.lang.Integer" >
        SELECT
        count(1)
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </select>

    <select id="findListByGroupIdAndBiggerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
        <if test="id != null">
            and id > #{id,jdbcType=BIGINT}
        </if>
    </select>
    <select id="pageListByGroupIdPage" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
        order by id asc
        limit #{offset},#{limit}
    </select>

    <select id="findListByGroupId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupsetting
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </select>

</mapper>