<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortGroupUserMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupUserPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="SortGroup_Id" property="sortGroupId" jdbcType="BIGINT"/>
        <result column="User_Id" property="userId" jdbcType="INTEGER"/>
        <result column="UserName" property="userName" jdbcType="VARCHAR"/>
        <result column="WorkStartDate" property="workStartDate" jdbcType="DATE"/>
        <result column="WorkEndDate" property="workEndDate" jdbcType="DATE"/>
        <result column="WorkDayType" property="workDayType" jdbcType="TINYINT"/>
        <result column="WorkDayDetail" property="workDayDetail" jdbcType="VARCHAR"/>
        <result column="WorkStartTime" property="workStartTime" jdbcType="TIME"/>
        <result column="WorkEndTime" property="workEndTime" jdbcType="TIME"/>
        <result column="State" property="state" jdbcType="TINYINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, SortGroup_Id, User_Id, UserName, WorkStartDate, WorkEndDate, WorkDayType, WorkDayDetail, WorkStartTime,
        WorkEndTime, State,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser
    </sql>

    <select id="listGroupUserByWarehouseId" resultMap="BaseResultMap">
        SELECT
        sgu.Id, sgu.SortGroup_Id, sgu.User_Id, sgu.UserName, sgu.WorkStartDate, sgu.WorkEndDate, sgu.WorkDayType,
        sgu.WorkDayDetail, sgu.WorkStartTime, sgu.WorkEndTime, sgu.State,
        sgu.CreateTime, sgu.CreateUser, sgu.LastUpdateTime, sgu.LastUpdateUser
        FROM sortgroupuser sgu
        INNER JOIN sortgroup sg on sg.Id = sgu.SortGroup_Id
        WHERE sgu.State = 1
        <if test="so != null">
            <if test="so.groupId != null and so.groupId != ''">
                AND sg.Id = #{so.groupId,jdbcType=BIGINT}
            </if>
            <if test="so.warehouseId != null and so.warehouseId != ''">
                AND sg.Warehouse_Id = #{so.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="so.flag != null">
                AND sg.Flag = #{so.flag,jdbcType=TINYINT}
            </if>
            <if test="so.propertyList != null">
                AND sg.Id IN (
                SELECT DISTINCT(s.SortGroup_Id) FROM sortgroupsetting s
                WHERE
                s.Sort_Id IN
                <foreach collection="so.propertyList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="so.type != null">
                    AND s.SortType = #{so.type,jdbcType=TINYINT}
                </if>
                )
            </if>
        </if>
    </select>

    <select id="listGroupUserByGroupId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupuser
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
        ORDER BY CreateTime
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortgroupuser
        WHERE Id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupUserPO">
        INSERT INTO sortgroupuser (
        Id,
        SortGroup_Id,
        User_Id,
        UserName,
        WorkStartDate,
        WorkEndDate,
        WorkDayType,
        WorkDayDetail,
        WorkStartTime,
        WorkEndTime,
        State,
        CreateUser
        )
        VALUES(
        #{id,jdbcType=BIGINT},
        #{sortGroupId,jdbcType=BIGINT},
        #{userId,jdbcType=INTEGER},
        #{userName,jdbcType=VARCHAR},
        #{workStartDate,jdbcType=DATE},
        #{workEndDate,jdbcType=DATE},
        #{workDayType,jdbcType=TINYINT},
        #{workDayDetail,jdbcType=VARCHAR},
        #{workStartTime,jdbcType=TIME},
        #{workEndTime,jdbcType=TIME},
        #{state,jdbcType=TINYINT},
        #{createUser,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sortgroupuser (
        Id,
        SortGroup_Id,
        User_Id,
        UserName,
        WorkStartDate,
        WorkEndDate,
        WorkDayType,
        WorkDayDetail,
        WorkStartTime,
        WorkEndTime,
        State,
        CreateUser
        )
        VALUES
        <foreach collection="list" item="user" separator=",">
            (
            #{user.id,jdbcType=BIGINT},
            #{user.sortGroupId,jdbcType=BIGINT},
            #{user.userId,jdbcType=INTEGER},
            #{user.userName,jdbcType=VARCHAR},
            #{user.workStartDate,jdbcType=DATE},
            #{user.workEndDate,jdbcType=DATE},
            #{user.workDayType,jdbcType=TINYINT},
            #{user.workDayDetail,jdbcType=VARCHAR},
            #{user.workStartTime,jdbcType=TIME},
            #{user.workEndTime,jdbcType=TIME},
            #{user.state,jdbcType=TINYINT},
            #{user.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortGroupUserPO">
        UPDATE sortgroupuser
        <set>
            <if test="userId != null">
                User_Id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                UserName = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="workStartDate != null">
                WorkStartDate = #{workStartDate,jdbcType=DATE},
            </if>
            <if test="workEndDate != null">
                WorkEndDate = #{workEndDate,jdbcType=DATE},
            </if>
            <if test="workDayType != null">
                WorkDayType = #{workDayType,jdbcType=TINYINT},
            </if>
            <if test="workDayDetail != null">
                WorkDayDetail = #{workDayDetail,jdbcType=VARCHAR},
            </if>
            <if test="workStartTime != null">
                WorkStartTime = #{workStartTime,jdbcType=TIME},
            </if>
            <if test="workEndTime != null">
                WorkEndTime = #{workEndTime,jdbcType=TIME},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE Id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM sortgroupuser
        WHERE Id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByGroupId" parameterType="java.lang.Long">
        DELETE FROM sortgroupuser
        WHERE SortGroup_Id = #{groupId,jdbcType=BIGINT}
    </delete>

    <select id="listGroupIdByUserId" resultType="java.lang.Long">
        select sgu.SortGroup_Id
        from sortgroupuser sgu
        inner join sortgroup sg on sg.Id = sgu.SortGroup_Id
        where sgu.User_Id = #{userId,jdbcType=INTEGER}
        and sgu.State = 1
        and sg.Flag = #{flag,jdbcType=TINYINT}
    </select>
</mapper>