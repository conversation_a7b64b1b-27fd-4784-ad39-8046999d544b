<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.SortUserConfigMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.SortUserConfigPO">
        <id column="Id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="userName" property="userName" jdbcType="VARCHAR"/>
        <result column="warehouse_id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="sortType" property="sortType" jdbcType="TINYINT"/>
        <result column="sort_id" property="sortId" jdbcType="VARCHAR"/>
        <result column="sortName" property="sortName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="createUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,user_id, userName, warehouse_id, sortType, sort_id, sortName, remark,createUser,createTime
    </sql>
    <insert id="addSortUserConfigList">
        INSERT INTO
        sortuserconfig
        (user_id, userName, warehouse_id, sortType, sort_id, sortName, remark,createUser,type)
        VALUES
        <foreach collection="sortUserConfigPOList" item="item" separator=",">
            (
            #{item.userId},
            #{item.userName},
            #{item.warehouseId},
            #{item.sortType},
            #{item.sortId},
            #{item.sortName},
            #{item.remark},
            #{item.createUser},
            #{item.type}
            )
        </foreach>
    </insert>
    <delete id="deleteSortUserConfig">
        DELETE FROM sortuserconfig
        WHERE user_id=#{userId} and type=#{type}
    </delete>
    <select id="findSortUserProperty" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortuserconfig
        where user_id=#{userId} and type=#{type}
    </select>
    <select id="findSortPropertyByUser" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sortuserconfig
        WHERE 1=1
        <if test="sortPropertyByUserDTO.warehouseId!=null">
            and Warehouse_Id=#{sortPropertyByUserDTO.warehouseId}
        </if>
        <if test="sortPropertyByUserDTO.selectType!=null">
            and type=#{sortPropertyByUserDTO.selectType}
        </if>
        <if test="sortPropertyByUserDTO.type==3">
            and sortName in
            <foreach collection="sortPropertyByUserDTO.propertyList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sortPropertyByUserDTO.type==1 ||sortPropertyByUserDTO.type==2">
            and sort_id in
            <foreach collection="sortPropertyByUserDTO.propertyList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectSortUser" resultMap="BaseResultMap">
        SELECT
        user_id,userName,remark
        FROM sortuserconfig
        WHERE type=#{selectSortUser.type}
        <if test="selectSortUser.warehouseId !=null">
            and warehouse_id=#{selectSortUser.warehouseId}
        </if>
        <if test="selectSortUser.sortUserName !=null and selectSortUser.sortUserName!=''">
            and userName like concat('%',#{selectSortUser.sortUserName},'%')
        </if>
        <if test="(selectSortUser.area !=null and selectSortUser.area!='') ||( selectSortUser.location!=null and selectSortUser.location!='')|| (selectSortUser.categoryName!=null and selectSortUser.categoryName!='')">
            and(
            <trim suffixOverrides="or">
                <if test="selectSortUser.area !=null and selectSortUser.area!=''">
                    (sortType=1 and sortName=#{selectSortUser.area}) or
                </if>
                <if test="selectSortUser.location !=null and selectSortUser.location!=''">
                    ( sortType=2 and sortName=#{selectSortUser.location}) or
                </if>
                <if test="selectSortUser.categoryName !=null and selectSortUser.categoryName!=''">
                    (sortType=3 and sortName=#{selectSortUser.categoryName}) or
                </if>
            </trim>
            )
        </if>
        GROUP BY user_id,userName,remark
    </select>
</mapper>