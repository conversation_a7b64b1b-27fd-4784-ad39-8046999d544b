<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.StockAgeStrategyConfigMapper">

    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="StockAgeStrategy_Id" jdbcType="BIGINT" property="stockAgeStrategyId"/>
        <result column="RelatedId" jdbcType="BIGINT" property="relatedId"/>
        <result column="ConfigType" jdbcType="TINYINT" property="configType"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, StockAgeStrategy_Id, RelatedId, ConfigType, Remark, CreateTime,
        CreateUser, LastUpdateTime, LastUpdateUser
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockagestrategyconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from stockagestrategyconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO">
        insert into stockagestrategyconfig (Id, Org_Id, Warehouse_Id,
        StockAgeStrategy_Id, RelatedId, ConfigType,
        Remark, CreateTime, CreateUser,
        LastUpdateTime, LastUpdateUser)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{stockAgeStrategyId,jdbcType=BIGINT}, #{relatedId,jdbcType=BIGINT}, #{configType,jdbcType=TINYINT},
        #{remark,jdbcType=VARCHAR}, now(), #{createUser,jdbcType=VARCHAR},
        now(), #{lastUpdateUser,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO">
        insert into stockagestrategyconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="stockAgeStrategyId != null">
                StockAgeStrategy_Id,
            </if>
            <if test="relatedId != null">
                RelatedId,
            </if>
            <if test="configType != null">
                ConfigType,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            CreateTime,
            LastUpdateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="stockAgeStrategyId != null">
                #{stockAgeStrategyId,jdbcType=BIGINT},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=BIGINT},
            </if>
            <if test="configType != null">
                #{configType,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO">
        update stockagestrategyconfig
        <set>
            <if test="stockAgeStrategyId != null">
                StockAgeStrategy_Id = #{stockAgeStrategyId,jdbcType=BIGINT},
            </if>
            <if test="relatedId != null">
                RelatedId = #{relatedId,jdbcType=BIGINT},
            </if>
            <if test="configType != null">
                ConfigType = #{configType,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyConfigPO">
        update stockagestrategyconfig
        set
        StockAgeStrategy_Id = #{stockAgeStrategyId,jdbcType=BIGINT},
        RelatedId = #{relatedId,jdbcType=BIGINT},
        ConfigType = #{configType,jdbcType=TINYINT},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsertOrUpdate">
        insert into stockagestrategyconfig (Id, Org_Id, Warehouse_Id,
        StockAgeStrategy_Id, RelatedId, ConfigType,
        Remark, CreateTime, CreateUser,
        LastUpdateTime, LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=INTEGER}, #{item.warehouseId,jdbcType=INTEGER},
            #{item.stockAgeStrategyId,jdbcType=BIGINT}, #{item.relatedId,jdbcType=BIGINT},
            #{item.configType,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR}, now(), #{item.createUser,jdbcType=VARCHAR},
            now(), #{item.lastUpdateUser,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        RelatedId=VALUES(RelatedId),
        ConfigType=VALUES(ConfigType),
        Remark=VALUES(Remark),
        LastUpdateUser=VALUES(LastUpdateUser)
    </insert>

    <select id="listStockAgeStrategyConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockagestrategyconfig
        <where>
            <if test="query.orgId != null">
                and Org_Id = #{query.orgId,jdbcType=INTEGER}
            </if>
            <if test="query.warehouseId != null ">
                and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="query.relatedIds != null and query.relatedIds.size() > 0">
                and RelatedId in
                <foreach collection="query.relatedIds" open="(" item="item" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.stockAgeStrategyIds != null and query.stockAgeStrategyIds.size() > 0">
                and StockAgeStrategy_Id in
                <foreach collection="query.stockAgeStrategyIds" item="strategyId" open="(" separator="," close=")">
                    #{strategyId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.configType != null">
                and ConfigType = #{query.configType,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="pageListStockAgeStrategyConfig"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO">
        select
        Id, Org_Id as orgId, Warehouse_Id as warehouseId, StockAgeStrategy_Id as stockAgeStrategyId, RelatedId,
        ConfigType, Remark,
        CreateTime, CreateUser, LastUpdateTime, LastUpdateUser
        from stockagestrategyconfig
        <where>
            <if test="query.orgId != null">
                and Org_Id = #{query.orgId,jdbcType=INTEGER}
            </if>
            <if test="query.warehouseId != null ">
                and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="query.relatedIds != null and query.relatedIds.size() > 0">
                and RelatedId in
                <foreach collection="query.relatedIds" open="(" item="item" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.stockAgeStrategyIds != null and query.stockAgeStrategyIds.size() > 0">
                and StockAgeStrategy_Id in
                <foreach collection="query.stockAgeStrategyIds" item="strategyId" open="(" separator="," close=")">
                    #{strategyId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.configType != null">
                and ConfigType = #{query.configType,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="findByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockagestrategyconfig
        where Org_Id = #{orgId}
        and StockAgeStrategy_Id = #{strategyId}
    </select>

    <delete id="deleteByIds">
        delete from stockagestrategyconfig
        where Id in
        <foreach collection="configIds" item="id" separator="," open="(" close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>