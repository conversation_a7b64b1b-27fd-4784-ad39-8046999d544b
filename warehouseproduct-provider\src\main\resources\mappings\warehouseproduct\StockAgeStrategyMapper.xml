<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.StockAgeStrategyMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="StrategyType" jdbcType="TINYINT" property="strategyType"/>
        <result column="MinStockAge" jdbcType="DECIMAL" property="minStockAge"/>
        <result column="MaxStockAge" jdbcType="DECIMAL" property="maxStockAge"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="StrategyConfigCount" jdbcType="INTEGER" property="strategyConfigCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, Name, StrategyType, MinStockAge, MaxStockAge, State, StrategyConfigCount,
        Remark, CreateTime, CreateUser, LastUpdateTime, LastUpdateUser
    </sql>

    <resultMap id="ResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyDTO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="StrategyType" jdbcType="TINYINT" property="strategyType"/>
        <result column="MinStockAge" jdbcType="DECIMAL" property="minStockAge"/>
        <result column="MaxStockAge" jdbcType="DECIMAL" property="maxStockAge"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="StrategyConfigCount" jdbcType="INTEGER" property="strategyConfigCount"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <collection property="stockAgeStrategyConfigDTOS"
                    ofType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO">
            <id column="configId" jdbcType="BIGINT" property="id"/>
            <result column="config_OrgId" jdbcType="INTEGER" property="orgId"/>
            <result column="config_WarehouseId" jdbcType="INTEGER" property="warehouseId"/>
            <result column="StockAgeStrategy_Id" jdbcType="BIGINT" property="stockAgeStrategyId"/>
            <result column="RelatedId" jdbcType="BIGINT" property="relatedId"/>
            <result column="ConfigType" jdbcType="TINYINT" property="configType"/>
            <result column="config_Remark" jdbcType="VARCHAR" property="remark"/>
            <result column="config_CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="config_CreateUser" jdbcType="VARCHAR" property="createUser"/>
            <result column="config_LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
            <result column="config_LastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
            <result column="skuId" jdbcType="BIGINT" property="skuId"/>
        </collection>

    </resultMap>

    <sql id="Column_List">
        strategy.Id, strategy.Org_Id, strategy.Warehouse_Id, strategy.Name, strategy.StrategyType, strategy.MinStockAge,
        strategy.MaxStockAge, strategy.State, strategy.StrategyConfigCount,
        strategy.Remark, strategy.CreateTime, strategy.CreateUser, strategy.LastUpdateTime, strategy.LastUpdateUser,
        config.Id as configId, config.Org_Id as config_OrgId, config.Warehouse_Id as config_WarehouseId,
        config.StockAgeStrategy_Id, config.RelatedId, config.ConfigType, config.Remark as config_Remark,
        config.CreateTime as config_CreateTime, config.CreateUser as config_CreateUser,
        config.LastUpdateTime as config_LastUpdateTime, config.LastUpdateUser as config_LastUpdateUser
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockagestrategy
        where Id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from stockagestrategy
        where Id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO">
        insert into stockagestrategy (Id, Org_Id, Warehouse_Id,
        Name, StrategyType, MinStockAge,
        MaxStockAge, State, StrategyConfigCount,
        Remark, CreateTime, CreateUser,
        LastUpdateTime, LastUpdateUser)
        values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
        #{name,jdbcType=VARCHAR}, #{strategyType,jdbcType=TINYINT}, #{minStockAge,jdbcType=DECIMAL},
        #{maxStockAge,jdbcType=DECIMAL}, #{state,jdbcType=TINYINT}, #{strategyConfigCount,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, now(), #{createUser,jdbcType=VARCHAR},
        now(), #{lastUpdateUser,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO">
        insert into stockagestrategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="orgId != null">
                Org_Id,
            </if>
            <if test="warehouseId != null">
                Warehouse_Id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="strategyType != null">
                StrategyType,
            </if>
            <if test="minStockAge != null">
                MinStockAge,
            </if>
            <if test="maxStockAge != null">
                MaxStockAge,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="strategyConfigCount != null">
                StrategyConfigCount,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            CreateTime,
            LastUpdateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="strategyType != null">
                #{strategyType,jdbcType=TINYINT},
            </if>
            <if test="minStockAge != null">
                #{minStockAge,jdbcType=DECIMAL},
            </if>
            <if test="maxStockAge != null">
                #{maxStockAge,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="strategyConfigCount != null">
                #{strategyConfigCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO">
        update stockagestrategy
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="strategyType != null">
                StrategyType = #{strategyType,jdbcType=TINYINT},
            </if>
            <if test="minStockAge != null">
                MinStockAge = #{minStockAge,jdbcType=DECIMAL},
            </if>
            <if test="maxStockAge != null">
                MaxStockAge = #{maxStockAge,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="strategyConfigCount != null">
                StrategyConfigCount = #{strategyConfigCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.StockAgeStrategyPO">
        update stockagestrategy
        set
        Name = #{name,jdbcType=VARCHAR},
        StrategyType = #{strategyType,jdbcType=TINYINT},
        MinStockAge = #{minStockAge,jdbcType=DECIMAL},
        MaxStockAge = #{maxStockAge,jdbcType=DECIMAL},
        State = #{state,jdbcType=TINYINT},
        StrategyConfigCount = #{strategyConfigCount,jdbcType=INTEGER},
        Remark = #{remark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=VARCHAR}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="pageListStockAgeStrategyAll" resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from stockagestrategy strategy
        inner join stockagestrategyconfig config
        on strategy.Id = config.StockAgeStrategy_Id
        <where>
            <if test="query.orgId != null">
                and strategy.Org_Id = #{query.orgId,jdbcType=INTEGER}
            </if>
            <if test="query.warehouseId != null">
                and strategy.Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="query.strategyName != null and query.strategyName != '' ">
                and strategy.Name like concat('%',#{query.strategyName},'%')
            </if>
            <if test="query.strategyType != null">
                and strategy.strategyType = #{query.strategyType,jdbcType=TINYINT}
            </if>
            <if test="query.state != null">
                and strategy.state = #{query.state,jdbcType=TINYINT}
            </if>
            <if test="query.relatedIds != null and query.relatedIds.size() > 0">
                and config.relatedId in
                <foreach collection="query.relatedIds" item="relatedId" open="(" separator="," close=")">
                    #{relatedId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="pageListStockAgeStrategy" resultMap="ResultMap">
        select
        <include refid="Base_Column_List"/>
        from stockagestrategy
        <where>
            <if test="query.orgId != null">
                and Org_Id = #{query.orgId,jdbcType=INTEGER}
            </if>
            <if test="query.warehouseId != null">
                and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="query.strategyName != null and query.strategyName != '' ">
                and Name like concat('%',#{query.strategyName},'%')
            </if>
            <if test="query.strategyType != null">
                and strategyType = #{query.strategyType,jdbcType=TINYINT}
            </if>
            <if test="query.state != null">
                and state = #{query.state,jdbcType=TINYINT}
            </if>
        </where>
        order by LastUpdateTime desc
    </select>

    <select id="pageListStockAgeProductStrategy" resultMap="ResultMap">
        select
        <include refid="Column_List"/>,
        sku.ProductSku_id as skuId
        from stockagestrategy strategy
        inner join stockagestrategyconfig config
        on strategy.Id = config.StockAgeStrategy_Id
        inner join productsku sku
        on sku.ProductSku_id = config.RelatedId and strategy.Org_Id = sku.City_Id
        <if test="query.productConfigQueryDTO != null ">
            left join productskuconfig skuconfig
            on skuconfig.ProductSku_id = sku.ProductSku_id and skuconfig.Warehouse_id = strategy.Warehouse_id
        </if>
        where strategy.Warehouse_id = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.orgId != null ">
            and strategy.Org_Id = #{query.orgId,jdbcType=INTEGER}
        </if>
        <if test="query.ownerId != null ">
            and sku.Company_Id = #{query.ownerId,jdbcType=BIGINT}
        </if>
        <if test="query.productName != null and query.productName != '' ">
            and sku.Name like concat('%',#{query.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.skuIds != null and query.skuIds.size() > 0">
            and config.RelatedId in
            <foreach collection="query.skuIds" item="skuId" separator="," open="(" close=")">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.productConfigQueryDTO != null ">
            and skuconfig.StorageType = #{query.productConfigQueryDTO.storageType,jdbcType=TINYINT}
        </if>
    </select>
</mapper>