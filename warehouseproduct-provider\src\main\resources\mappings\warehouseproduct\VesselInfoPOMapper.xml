<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.VesselInfoPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.VesselInfoPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="locationId" jdbcType="BIGINT" property="locationId"/>
        <result column="locationNo" jdbcType="VARCHAR" property="locationNo"/>
        <result column="currentLocationId" jdbcType="BIGINT" property="currentLocationId"/>
        <result column="currentLocationName" jdbcType="VARCHAR" property="currentLocationName"/>
        <result column="isFreeze" jdbcType="TINYINT" property="isFreeze"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="lastUpdateUser" jdbcType="VARCHAR" property="lastUpdateUser"/>
        <result column="lastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="relationLocationId" jdbcType="BIGINT" property="relationLocationId"/>
        <result column="relationLocationNo" jdbcType="VARCHAR" property="relationLocationNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
                , warehouseId, locationId, locationNo, currentLocationId, currentLocationName, isFreeze,
        createUser, createTime,lastUpdateUser, lastUpdateTime,remark
    </sql>
    <select id="selectByLocationNo"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.VesselInfoPO">
        select
        <include refid="Base_Column_List"/>
        from vesselinfo
        where locationNo = #{locationNo,jdbcType=VARCHAR}
        and warehouseId = #{warehouseId,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.VesselInfoPO">
        insert into vesselinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            Id,
            <if test="warehouseId != null">
                warehouseId,
            </if>
            <if test="locationId != null">
                locationId,
            </if>
            <if test="locationNo != null">
                locationNo,
            </if>
            <if test="currentLocationId != null">
                currentLocationId,
            </if>
            <if test="currentLocationName != null">
                currentLocationName,
            </if>
            <if test="isFreeze != null">
                isFreeze,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                createUser,
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                lastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=BIGINT},
            <if test="warehouseId != null">
                #{warehouseId},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=BIGINT},
            </if>
            <if test="locationNo != null">
                #{locationNo,jdbcType=VARCHAR},
            </if>
            <if test="currentLocationId != null">
                #{currentLocationId,jdbcType=BIGINT},
            </if>
            <if test="currentLocationName != null">
                #{currentLocationName,jdbcType=VARCHAR},
            </if>
            <if test="isFreeze != null">
                #{isFreeze,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insertSelectiveBatch" parameterType="java.util.List">
        insert into vesselinfo (
        Id,
        warehouseId,
        locationId,
        locationNo,
        currentLocationId,
        currentLocationName,
        isFreeze,
        CreateTime,
        createUser,
        lastUpdateTime,
        lastUpdateUser,
        remark
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.warehouseId},
            #{item.locationId,jdbcType=BIGINT},
            #{item.locationNo,jdbcType=VARCHAR},
            #{item.currentLocationId,jdbcType=BIGINT},
            #{item.currentLocationName,jdbcType=VARCHAR},
            #{item.isFreeze,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUser,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="selectById" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.VesselInfoPO">
        select
        <include refid="Base_Column_List"/>
        from vesselinfo
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from vesselinfo
        where id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <update id="updateFreezeStateByIdList"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.VesselInfoModifyDTO">
        update vesselinfo
        <set>
            <if test="isFreeze != null">
                isFreeze = #{isFreeze, jdbcType=TINYINT},
            </if>
            <if test="lastUpdateUser != null and lastUpdateUser != ''">
                lastUpdateUser = #{lastUpdateUser, jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime, jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            1=1
            <if test="id != null">
                and id = #{id, jdbcType=BIGINT}
            </if>
            <if test="ids != null">
                and id IN
                <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update vesselinfo
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="isFreeze =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isFreeze != null ">
                        when id = #{item.id} then #{item.isFreeze}
                    </if>
                </foreach>
            </trim>
            <trim prefix="currentLocationId =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.currentLocationId != null ">
                        when id = #{item.id} then #{item.currentLocationId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="currentLocationName =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.currentLocationName != null ">
                        when id = #{item.id} then #{item.currentLocationName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lastUpdateUser =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.lastUpdateUser != null ">
                        when id = #{item.id} then #{item.lastUpdateUser}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lastUpdateTime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.lastUpdateTime !=null ">
                        when id = #{item.id} then #{item.lastUpdateTime}
                    </if>
                    <if test="item.lastUpdateTime == null ">
                        when id = #{item.id} then now()
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id, jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectByConditions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from vesselinfo
        where warehouseId = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.vesselLocationId != null">
            and locationId = #{query.vesselLocationId, jdbcType=BIGINT}
        </if>
        <if test="query.vesselLocationIds != null and query.vesselLocationIds.size() > 0">
            and locationId IN
            <foreach collection="query.vesselLocationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.currentLocationIds != null and query.currentLocationIds.size() > 0">
            and currentLocationId IN
            <foreach collection="query.currentLocationIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findInfoByIds" resultMap="BaseResultMap">
        select
        v.id, v.warehouseId, v.locationId, v.locationNo, v.currentLocationId, v.currentLocationName, v.isFreeze,
        v.createUser, v.createTime,v.lastUpdateUser, v.lastUpdateTime, v.remark,
        l.area_id as relationLocationId, la.Name as relationLocationNo
        from vesselinfo v
        left join location l on l.id = v.locationId and l.Warehouse_Id = v.warehouseId
        left join location la on la.id = l.area_id and la.Warehouse_Id = l.Warehouse_Id
        where v.id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE
        FROM vesselinfo
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByPrimaryKeyBatch" parameterType="java.lang.Long">
        delete from vesselinfo
        where Id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
