<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WareHouseMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO">
        <id column="Id" jdbcType="INTEGER" property="id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="Street" jdbcType="VARCHAR" property="street"/>
        <result column="DetailAddress" jdbcType="VARCHAR" property="detailAddress"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="warehouseType" jdbcType="TINYINT" property="warehouseType"/>
        <result column="WarehouseClass" jdbcType="TINYINT" property="warehouseClass"/>
        <result column="ShopId" jdbcType="INTEGER" property="shopId"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="Longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="Latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="Rent" jdbcType="DECIMAL" property="rent"/>
        <result column="RentUnit" jdbcType="TINYINT" property="rentUnit"/>
        <result column="Acreage" jdbcType="DECIMAL" property="acreage"/>
    </resultMap>
    <resultMap id="BaseResultDtoMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO">
        <id column="Id" jdbcType="INTEGER" property="id"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="Street" jdbcType="VARCHAR" property="street"/>
        <result column="DetailAddress" jdbcType="VARCHAR" property="detailAddress"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser_Id" jdbcType="INTEGER" property="lastUpdateUserId"/>
        <result column="warehouseType" jdbcType="TINYINT" property="warehouseType"/>
        <result column="WarehouseClass" jdbcType="TINYINT" property="warehouseClass"/>
        <result column="ShopId" jdbcType="INTEGER" property="shopId"/>
        <result column="cityId" jdbcType="INTEGER" property="cityId"/>
        <result column="Longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="Latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="Rent" jdbcType="DECIMAL" property="rent"/>
        <result column="RentUnit" jdbcType="TINYINT" property="rentUnit"/>
        <result column="Acreage" jdbcType="DECIMAL" property="acreage"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id
        , Name, Remark, State, Province, City, County, Street, DetailAddress, CreateTime,
        CreateUser_Id, LastUpdateTime, LastUpdateUser_Id, warehouseType,
        WarehouseClass, ShopId,City_Id as cityId,Longitude,Latitude, Rent, RentUnit, Acreage
    </sql>

    <select id="findWareHouseList" resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        WHERE state = 1
        <if test="dto.name!=null and dto.name!=''">
            and Name like CONCAT('%',#{dto.name,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <select id="findWareHouseByParentOrgId" resultMap="BaseResultDtoMap">
        SELECT w.Id,
               w.Name,
               w.Remark,
               w.State,
               w.Province,
               w.City,
               w.County,
               w.Street,
               w.DetailAddress,
               w.CreateTime,
               w.CreateUser_Id,
               w.LastUpdateTime,
               w.LastUpdateUser_Id,
               w.warehouseType,
               w.WarehouseClass,
               w.ShopId,
               w.City_Id as cityId,
               w.Longitude,
               w.Latitude,
               w.Rent,
               w.RentUnit,
               w.Acreage
        FROM warehouse w
        WHERE EXISTS(
                SELECT 1
                FROM (SELECT id
                      FROM org
                      WHERE id = #{dto.parentOrgId,jdbcType=INTEGER}
                        and state = 1
                      UNION ALL
                      SELECT id
                      FROM org
                      WHERE ParentOrg_Id = #{dto.parentOrgId,jdbcType=INTEGER}
                        and state = 1) g
                WHERE g.id = w.City_Id
            )
          and w.state = 1
        ORDER BY w.Id
    </select>

    <select id="findWareHouseById" resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        WHERE id = #{warehouseId,jdbcType=INTEGER}
    </select>

    <select id="findWareHousesByIds" resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        WHERE id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectWareHouseByCityId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT b.Id,
               b.NAME,
               b.Remark,
               b.State,
               b.Province,
               b.City,
               b.County,
               b.Street,
               b.DetailAddress,
               b.CreateTime,
               b.CreateUser_Id,
               b.LastUpdateTime,
               b.LastUpdateUser_Id,
               b.warehouseType,
               b.WarehouseClass,
               b.City_Id   as cityId,
               b.ShopId,
               b.Longitude as longitude,
               b.Latitude  as latitude,
               b.Rent,
               b.RentUnit,
               b.Acreage
        FROM citywarehouse a
                 LEFT JOIN warehouse b ON a.Warehouse_Id = b.Id
        WHERE b.state = 1
          AND a.IsDefault = 1
          AND a.City_Id = #{cityId,jdbcType=INTEGER}
    </select>

    <select id="selectWareHouseByShopId" resultMap="BaseResultMap">
        SELECT b.Id,
               b.NAME,
               b.Remark,
               b.State,
               b.Province,
               b.City,
               b.County,
               b.Street,
               b.DetailAddress,
               b.CreateTime,
               b.CreateUser_Id,
               b.LastUpdateTime,
               b.LastUpdateUser_Id,
               b.warehouseType,
               b.WarehouseClass,
               b.City_Id   as cityId,
               b.ShopId,
               b.Longitude as longitude,
               b.Latitude  as latitude,
               b.Rent,
               b.RentUnit,
               b.Acreage
        FROM warehouse b
        WHERE b.ShopId = #{shopId,jdbcType=BIGINT} limit 1
    </select>
    <select id="getAllCityByCityDefaultWareHouse" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT c.City_Id
        FROM citywarehouse c
        WHERE c.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
          AND c.IsDefault = 1
    </select>
    <insert id="insertWarehousePOBatch">
        insert into warehouse (
        Id,
        Name,
        Remark,
        State,
        Province,
        City,
        County,
        Street,
        DetailAddress,
        CreateTime,
        CreateUser_Id,
        LastUpdateTime,
        LastUpdateUser_Id,
        warehouseType,
        WarehouseClass,
        ShopId,City_Id)
        values
        <foreach collection="poList" item="item" index="index" separator=",">
            (#{item.id,jdbcType=INTEGER},
            #{item.name,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR},
            #{item.county,jdbcType=VARCHAR},
            #{item.street,jdbcType=VARCHAR},
            #{item.detailAddress,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            #{item.lastUpdateUserId,jdbcType=INTEGER},
            #{item.warehouseType,jdbcType=TINYINT},
            #{item.warehouseClass,jdbcType=TINYINT},
            #{item.shopId,jdbcType=INTEGER},
            #{item.cityId,jdbcType=INTEGER})
        </foreach>
        ON DUPLICATE KEY UPDATE
        Name = VALUES(Name),
        State = VALUES(State),
        Province = VALUES(Province),
        City = VALUES(City),
        County = VALUES(County),
        Street = VALUES(Street),
        DetailAddress = VALUES(DetailAddress),
        CreateTime = VALUES(CreateTime),
        CreateUser_Id = VALUES(CreateUser_Id),
        LastUpdateTime = VALUES(LastUpdateTime),
        LastUpdateUser_Id = VALUES(LastUpdateUser_Id),
        warehouseType = VALUES(warehouseType),
        WarehouseClass = VALUES(WarehouseClass),
        ShopId = VALUES(ShopId),
        City_Id = VALUES(City_Id)
    </insert>

    <select id="getMajorCityByWarehouseId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT City_Id
        FROM warehouse
        WHERE id = #{warehouseId,jdbcType=INTEGER}
    </select>
    <update id="updateSelectiveByPrimary"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO">
        update warehouse
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                county = #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                street = #{street,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                createUser_Id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                lastUpdateUser_Id = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="warehouseType != null">
                warehouseType = #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="warehouseClass != null">
                warehouseClass = #{warehouseClass,jdbcType=TINYINT},
            </if>
            <if test="shopId != null">
                shopId = #{shopId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                city_Id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="longitude != null">
                longitude = #{longitude,jdbcType=DECIMAL},
            </if>
            <if test="latitude != null">
                latitude = #{latitude,jdbcType=DECIMAL},
            </if>
            <if test="detailAddress != null">
                detailAddress=#{detailAddress,jdbcType=VARCHAR},
            </if>
            <if test="rent != null">
                Rent = #{rent,jdbcType=DECIMAL},
            </if>
            <if test="rentUnit != null">
                RentUnit = #{rentUnit,jdbcType=TINYINT},
            </if>
            <if test="acreage != null">
                Acreage = #{acreage,jdbcType=DECIMAL}
            </if>
        </set>
        where Id = #{id,jdbcType=INTEGER}
    </update>
    <select id="getMaxPrimaryKey" resultType="java.lang.Integer">
        select max(id)
        from warehouse
    </select>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WareHousePO">
        insert into warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="street != null">
                street,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
            <if test="createUserId != null">
                createUser_Id,
            </if>
            <if test="lastUpdateTime != null">
                lastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                lastUpdateUser_Id,
            </if>
            <if test="warehouseType != null">
                warehouseType,
            </if>
            <if test="warehouseClass != null">
                warehouseClass,
            </if>
            <if test="shopId != null">
                shopId,
            </if>
            <if test="cityId != null">
                city_Id,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="detailAddress != null">
                DetailAddress,
            </if>
            <if test="rent != null">
                Rent,
            </if>
            <if test="rentUnit != null">
                RentUnit,
            </if>
            <if test="acreage != null">
                Acreage,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="street != null">
                #{street,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
            <if test="warehouseType != null">
                #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="warehouseClass != null">
                #{warehouseClass,jdbcType=TINYINT},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=DECIMAL},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=DECIMAL},
            </if>
            <if test="detailAddress != null">
                #{detailAddress,jdbcType=VARCHAR},
            </if>
            <if test="rent != null">
                #{rent,jdbcType=DECIMAL},
            </if>
            <if test="rentUnit != null">
                #{rentUnit,jdbcType=TINYINT},
            </if>
            <if test="acreage != null">
                #{acreage,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <!--    <insert id="insertProductstoreDays">-->
    <!--    	insert into productstoredays(Id,Warehouse_Id,TotalCount) SELECT UUID_SHORT(), Warehouse_Id, sum(ps.TotalCount_MinUnit) FROM productstore ps GROUP BY Warehouse_Id-->
    <!--    </insert>-->

    <insert id="updateWarehouseAreaById">
        update warehouse
            Length = #{length,jdbcType=DECIMAL},
            Width = #{width,jdbcType=DECIMAL}
        where Id = #{id,jdbcType=INTEGER}
    </insert>
</mapper>
