<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WareHouseMapper">
    <select id="listWarehouseDTOByCityIdList"
            parameterType="java.util.List"
            resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        where city_id IN
        <foreach collection="cityIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by LastUpdateTime DESC
    </select>
    <select id="listWarehouseDTOByCityId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        where city_id =#{cityId,jdbcType=INTEGER}
        order by LastUpdateTime DESC
    </select>

    <select id="getCountByWarehouseType" resultType="java.lang.Integer">
        SELECT count(1)
        FROM warehouse
        WHERE warehouseType = #{warehouseType,jdbcType=TINYINT}
    </select>

    <select id="getCountByCityId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM warehouse
        WHERE Id LIKE CONCAT(#{cityId,jdbcType=INTEGER},'%')
    </select>

    <select id="findDianCangWareHouseList" resultMap="BaseResultDtoMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse
        WHERE state = 1
        and warehouseType = 6
    </select>
    <select id="queryWarehouseStatusByWarehouseId" resultType="java.lang.Integer">
        select
        State
        from warehouse
        WHERE id=#{warehouseId}
    </select>
</mapper>
