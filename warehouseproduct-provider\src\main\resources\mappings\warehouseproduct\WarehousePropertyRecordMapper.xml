<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarehousePropertyRecordMapper">
    <insert id="insert">
        INSERT INTO warehousepropertyrecord
        (id,
        warehouseId,
        requestId,
        detailsDescription,
        applicantDate,
        lastUpdateUser,
        lastUpdateTime,
        createTime,
        createUser)
        VALUES (
        #{id},
        #{warehouseId},
        #{requestId},
        #{detailsDescription},
        #{applicantDate},
        #{lastUpdateUser},
        now(),
        now(),
        #{createUser});
    </insert>

    <select id="queryRecordDateByWarehouse" resultType="java.util.Date">
        select applicantDate from warehousePropertyRecord where warehouseId=#{warehouseId} order by applicantDate desc
        limit 1
    </select>
</mapper>
