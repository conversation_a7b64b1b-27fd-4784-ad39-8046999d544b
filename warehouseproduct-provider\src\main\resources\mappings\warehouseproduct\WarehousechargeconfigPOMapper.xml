<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarehousechargeconfigPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouse_Id" jdbcType="INTEGER"/>
        <result column="UnloadingCharge" property="unloadingCharge" jdbcType="DECIMAL"/>
        <result column="SortingCharge" property="sortingCharge" jdbcType="DECIMAL"/>
        <result column="CustodianCharge" property="custodianCharge" jdbcType="DECIMAL"/>
        <result column="LoadingCharge" property="loadingCharge" jdbcType="DECIMAL"/>
        <result column="TransportCharge" property="transportCharge" jdbcType="DECIMAL"/>
        <result column="LandingCharge" property="landingCharge" jdbcType="DECIMAL"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Warehouse_Id, UnloadingCharge, SortingCharge, CustodianCharge, LoadingCharge,
        TransportCharge, LandingCharge, Status, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from warehousechargeconfig
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from warehousechargeconfig
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        insert into warehousechargeconfig (Id, Warehouse_Id, UnloadingCharge,
        SortingCharge, CustodianCharge, LoadingCharge,
        TransportCharge, LandingCharge, Status,
        CreateUser, CreateTime, LastUpdateUser,
        LastUpdateTime)
        values (#{id,jdbcType=BIGINT}, #{warehouse_Id,jdbcType=INTEGER}, #{unloadingCharge,jdbcType=DECIMAL},
        #{sortingCharge,jdbcType=DECIMAL}, #{custodianCharge,jdbcType=DECIMAL}, #{loadingCharge,jdbcType=DECIMAL},
        #{transportCharge,jdbcType=DECIMAL}, #{landingCharge,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT},
        #{createUser,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=BIGINT},
        #{lastUpdateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        insert into warehousechargeconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouse_id != null">
                Warehouse_Id,
            </if>
            <if test="unloadingCharge != null">
                UnloadingCharge,
            </if>
            <if test="sortingCharge != null">
                SortingCharge,
            </if>
            <if test="custodianCharge != null">
                CustodianCharge,
            </if>
            <if test="loadingCharge != null">
                LoadingCharge,
            </if>
            <if test="transportCharge != null">
                TransportCharge,
            </if>
            <if test="landingCharge != null">
                LandingCharge,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="createUser != null">
                CreateUser,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouse_id != null">
                #{warehouse_id,jdbcType=INTEGER},
            </if>
            <if test="unloadingCharge != null">
                #{unloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="sortingCharge != null">
                #{sortingCharge,jdbcType=DECIMAL},
            </if>
            <if test="custodianCharge != null">
                #{custodianCharge,jdbcType=DECIMAL},
            </if>
            <if test="loadingCharge != null">
                #{loadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="transportCharge != null">
                #{transportCharge,jdbcType=DECIMAL},
            </if>
            <if test="landingCharge != null">
                #{landingCharge,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        update warehousechargeconfig
        <set>
            <if test="warehouse_id != null">
                Warehouse_Id = #{warehouse_id,jdbcType=INTEGER},
            </if>
            <if test="unloadingCharge != null">
                UnloadingCharge = #{unloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="sortingCharge != null">
                SortingCharge = #{sortingCharge,jdbcType=DECIMAL},
            </if>
            <if test="custodianCharge != null">
                CustodianCharge = #{custodianCharge,jdbcType=DECIMAL},
            </if>
            <if test="loadingCharge != null">
                LoadingCharge = #{loadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="transportCharge != null">
                TransportCharge = #{transportCharge,jdbcType=DECIMAL},
            </if>
            <if test="landingCharge != null">
                LandingCharge = #{landingCharge,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        update warehousechargeconfig
        set Warehouse_Id = #{warehouse_Id,jdbcType=INTEGER},
        UnloadingCharge = #{unloadingCharge,jdbcType=DECIMAL},
        SortingCharge = #{sortingCharge,jdbcType=DECIMAL},
        CustodianCharge = #{custodianCharge,jdbcType=DECIMAL},
        LoadingCharge = #{loadingCharge,jdbcType=DECIMAL},
        TransportCharge = #{transportCharge,jdbcType=DECIMAL},
        LandingCharge = #{landingCharge,jdbcType=DECIMAL},
        Status = #{status,jdbcType=TINYINT},
        CreateUser = #{createUser,jdbcType=BIGINT},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP}
        where Id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
