<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarehousechargeconfigPOMapper">
    <sql id="listWarehouseChargeConfigSelectiveBase">
        from warehouse w2 left join warehousechargeconfig w1 on w1.Warehouse_Id=w2.id
        <where>
            <if test="warehouseId!=null">
                and w2.id=#{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="city!=null">
                and w2.city like CONCAT('%',#{city,jdbcType=VARCHAR},'%')
            </if>
            <if test="name!=null">
                and w2.name like CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="status!=null">
                and w2.state=#{status,jdbcType=INTEGER}
            </if>
            <if test="cityId!=null">
                and w2.city_Id=#{cityId,jdbcType=INTEGER}
            </if>
            <if test="cityIdList!=null">
                and w2.city_Id in
                <foreach collection="cityIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <sql id="unionWarehouseAndWarehouseCharge">
        w1.Id, w2.Id warehouseId, w1.unloadingCharge, w1.sortingCharge, w1.custodianCharge,
        w1.loadingCharge,w2.warehouseType,w2.WarehouseClass as warehouseClass,
        w1.transportCharge, w1.landingCharge, w2.state status, w1.createUser, w1.createTime, w1.lastUpdateUser,
        w1.lastUpdateTime,w2.name,w2.city_id cityId,w2.province,w2.city,w2.county,w2.street,w2.detailAddress,
        w2.latitude,w2.longitude, w2.Rent, w2.RentUnit, w2.Acreage
    </sql>
    <select id="listWarehouseChargeConfigSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO">
        select
        <include refid="unionWarehouseAndWarehouseCharge"/>
        <include refid="listWarehouseChargeConfigSelectiveBase"/>
        order by w2.LastUpdateTime DESC
    </select>
    <select id="countListWarehouseChargeConfigSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO"
            resultType="java.lang.Integer">
        select count(w2.Id)
        <include refid="listWarehouseChargeConfigSelectiveBase"/>
    </select>
    <update id="updateByWarehouseSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarehousechargeconfigPO">
        update warehousechargeconfig
        <set>
            <if test="warehouse_id != null">
                Warehouse_Id = #{warehouse_id,jdbcType=INTEGER},
            </if>
            <if test="unloadingCharge != null">
                UnloadingCharge = #{unloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="sortingCharge != null">
                SortingCharge = #{sortingCharge,jdbcType=DECIMAL},
            </if>
            <if test="custodianCharge != null">
                CustodianCharge = #{custodianCharge,jdbcType=DECIMAL},
            </if>
            <if test="loadingCharge != null">
                LoadingCharge = #{loadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="transportCharge != null">
                TransportCharge = #{transportCharge,jdbcType=DECIMAL},
            </if>
            <if test="landingCharge != null">
                LandingCharge = #{landingCharge,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                CreateUser = #{createUser,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where warehouse_Id = #{warehouse_id,jdbcType=BIGINT}
    </update>
    <select id="getMaxPrimaryKey" resultType="java.lang.Integer">
        select max(id) from warehousechargeconfig
    </select>
    <select id="getWarehouseChargeConfigSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO">
        select w1.Id, w2.Id warehouseId, w1.unloadingCharge, w1.sortingCharge, w1.custodianCharge,
        w1.loadingCharge,w2.warehouseType,w2.WarehouseClass as warehouseClass,
        w1.transportCharge, w1.landingCharge, w1.status, w1.createUser, w1.createTime, w1.lastUpdateUser,
        w1.lastUpdateTime,w2.name,w2.city_id cityId,w2.province,w2.city,w2.county,w2.street,w2.detailAddress,
        w2.latitude,w2.longitude, w2.Rent, w2.RentUnit, w2.Acreage, w2.Remark, w2.JiupiType,w2.width,w2.length
        from warehouse w2 left join warehousechargeconfig w1 on w1.Warehouse_Id=w2.id
        <where>
            <if test="warehouseId!=null">
                and w2.id=#{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="city!=null">
                and w2.city like CONCAT('%',#{city,jdbcType=VARCHAR},'%')
            </if>
            <if test="name!=null">
                and w2.name like CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="cityId!=null">
                and w2.city_Id=#{cityId,jdbcType=INTEGER}
            </if>
        </where>
        order by w2.LastUpdateTime DESC limit 1
    </select>
    <select id="listStockCount" resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO">
        select warehouse_Id warehouseId,count(id) stockNumber from productstore where
        TotalCount_MinUnit>0 and
        warehouse_Id in
        <foreach collection="warehouseIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by warehouseId
    </select>
</mapper>
