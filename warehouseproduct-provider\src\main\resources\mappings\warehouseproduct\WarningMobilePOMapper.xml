<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarningMobilePOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="City_Id" jdbcType="INTEGER" property="city_Id"/>
        <result column="GroupId" jdbcType="VARCHAR" property="groupId"/>
        <result column="ProductFirstCategory_Id" jdbcType="BIGINT" property="productFirstCategory_Id"/>
        <result column="CategoryClass" jdbcType="BIGINT" property="categoryClass"/>
        <result column="Mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="Remo" jdbcType="VARCHAR" property="remo"/>
        <result column="CreateUserId" jdbcType="INTEGER" property="createUserId"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUserId" jdbcType="INTEGER" property="lastUpdateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">

        Id, City_Id, GroupId, ProductFirstCategory_Id, CategoryClass, Mobile, Remo, CreateUserId,
        CreateTime, LastUpdateTime, LastUpdateUserId
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from warningmobile
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">

        delete from warningmobile
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">

        insert into warningmobile (Id, City_Id, GroupId,
        ProductFirstCategory_Id, CategoryClass,
        Mobile, Remo, CreateUserId,
        CreateTime, LastUpdateTime, LastUpdateUserId
        )
        values (#{id,jdbcType=BIGINT}, #{city_Id,jdbcType=INTEGER}, #{groupId,jdbcType=VARCHAR},
        #{productFirstCategory_Id,jdbcType=BIGINT}, #{categoryClass,jdbcType=BIGINT},
        #{mobile,jdbcType=VARCHAR}, #{remo,jdbcType=VARCHAR}, #{createUserId,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=INTEGER}
        )
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">

        insert into warningmobile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="city_Id != null">
                City_Id,
            </if>
            <if test="groupId != null">
                GroupId,
            </if>
            <if test="productFirstCategory_Id != null">
                ProductFirstCategory_Id,
            </if>
            <if test="categoryClass != null">
                CategoryClass,
            </if>
            <if test="mobile != null">
                Mobile,
            </if>
            <if test="remo != null">
                Remo,
            </if>
            <if test="createUserId != null">
                CreateUserId,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="city_Id != null">
                #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="productFirstCategory_Id != null">
                #{productFirstCategory_Id,jdbcType=BIGINT},
            </if>
            <if test="categoryClass != null">
                #{categoryClass,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="remo != null">
                #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 15:00:10 CST 2017.
        -->
        update warningmobile
        <set>
            <if test="city_Id != null">
                City_Id = #{city_Id,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                GroupId = #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="productFirstCategory_Id != null">
                ProductFirstCategory_Id = #{productFirstCategory_Id,jdbcType=BIGINT},
            </if>
            <if test="categoryClass != null">
                CategoryClass = #{categoryClass,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                Mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="remo != null">
                Remo = #{remo,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                CreateUserId = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUserId != null">
                LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 15:00:10 CST 2017.
        -->
        update warningmobile
        set City_Id = #{city_Id,jdbcType=INTEGER},
        GroupId = #{groupId,jdbcType=VARCHAR},
        ProductFirstCategory_Id = #{productFirstCategory_Id,jdbcType=BIGINT},
        CategoryClass = #{categoryClass,jdbcType=BIGINT},
        Mobile = #{mobile,jdbcType=VARCHAR},
        Remo = #{remo,jdbcType=VARCHAR},
        CreateUserId = #{createUserId,jdbcType=INTEGER},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUserId = #{lastUpdateUserId,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listWarningMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warningmobile
        where City_Id = #{so.cityId,jdbcType=INTEGER}
        <if test="so.mobile != null and so.mobile != ''">
            and Mobile = #{so.mobile,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>