<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.WarningMobilePOMapper">

    <select id="findWarningMobileList"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        select
        distinct
        groupId
        from warningMobile where city_id=#{so.cityId,jdbcType=INTEGER}
        <if test="so.groupId!=null and so.groupId!=''">
            and groupId=#{so.groupId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="findWarningMobileListCategory"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        select
        distinct
        groupId,
        ProductFirstCategory_Id
        from warningMobile where city_id=#{so.cityId,jdbcType=INTEGER}
        <if test="so.groupId!=null and so.groupId!=''">
            and groupId=#{so.groupId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="findWarningMobileListMobile"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.domain.po.WarningMobilePO">
        select
        distinct
        groupId,
        mobile
        from warningMobile where city_id=#{so.cityId,jdbcType=INTEGER}
        <if test="so.groupId!=null and so.groupId!=''">
            and groupId=#{so.groupId,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insertBatch">
        insert into warningMobile (
        Id,City_Id, GroupId,
        ProductFirstCategory_Id, CategoryClass,
        Mobile, CreateTime, CreateUserId)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},#{item.city_Id,jdbcType=INTEGER}, #{item.groupId,jdbcType=VARCHAR},
            #{item.productFirstCategory_Id,jdbcType=BIGINT}, #{item.categoryClass,jdbcType=BIGINT},
            #{item.mobile,jdbcType=VARCHAR}, now(), #{item.createUserId,jdbcType=INTEGER})
        </foreach>
    </insert>

    <delete id="deleteByGroupId" parameterType="java.lang.String">
        delete from warningMobile where GroupId = #{groupId,jdbcType=VARCHAR}
    </delete>

    <select id="findMobileByCategoryIds" resultType="java.lang.String">
        SELECT DISTINCT(a.Mobile) from warningMobile a
        where a.city_id= #{cityId,jdbcType=INTEGER}
        and a.ProductFirstCategory_Id in
        <foreach item="item" collection="so" separator="," open="(" close=")" index="">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>