- hosts:
    - "*"
  signVerify: false
  stripPath: false
  timeout: 60000
  priority: 10
  extras:
    wrapper: "ROResult:wms"
  pathRouters:
    - paths:
        - "/oldpda/product/listProductLocationInfo"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/listProductLocationInfo"
    - paths:
        - "/oldpda/product/checkCCPBySkuIds"
        - "/oldwms/product/checkCCPBySkuIds"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/checkCCPBySkuIds"
    - paths:
        - "/oldwms/sortGroup/enableSortGroup"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/sortGroup/enableSortGroup"
    - paths:
        - "/oldwms/sortGroup/disableSortGroup"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/sortGroup/disableSortGroup"
    - paths:
        - "/oldwms/passage/enablePassage"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/passage/enablePassage"
    - paths:
        - "/oldwms/passage/disablePassage"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/passage/disablePassage"
    - paths:
        - "/oldwms/product/listSkuCategoryPeriod"
        - "/oldpda/product/listSkuCategoryPeriod"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/listSkuCategoryPeriod"
    - paths:
        - "/wmsSaas/productsync/{service}/{method}"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.productsync.service.<service>/<method>"
    - paths:
        - "/IProductCategoryGroupConfigService/getCategoryGroupIdByParentOrgId"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupConfigService/getCategoryGroupIdByParentOrgId"
    - paths:
        - "/IProductCategoryGroupConfigService/getCategoryGroupIdByOrgId"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupConfigService/getCategoryGroupIdByOrgId"
    - paths:
        - "/IProductCategoryGroupService/findProductCategoryGroupTree"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupService/findProductCategoryGroupTree"
    - paths:
        - "/IProductInfoCategoryService/findProductCategoryBySpecId"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoCategoryService/findProductCategoryBySpecId"
    - paths:
        - "/oldwms/sortGroup/updateGroup"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/sortGroup/updateGroup"
    - paths:
        - "/oldpda/product/getProductLocationInfo"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/getProductLocationInfo"
    - paths:
        - "/oldpda/productlocation/replaceProductLocationBatch"
        - "/oldwms/productlocation/replaceProductLocationBatch"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/productlocation/replaceProductLocationBatch"
    - paths:
        - "/oldpda/productlocation/replaceProductLocationCheck"
        - "/oldwms/productlocation/replaceProductLocationCheck"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/productlocation/replaceProductLocationCheck"
    - paths:
        - "/oldpda/product/listProductPromotionInfo"
        - "/oldwms/product/listProductPromotionInfo"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/listProductPromotionInfo"
    - paths:
        - "/oldpda/warehouseproduct/{service}/{method}"
        - "/oldwms/warehouseproduct/{service}/{method}"
        - "/wmsSaas/warehouseproduct/{service}/{method}"
        - "/wmsSaas/productowner/{service}/{method}"
      proxyUrl: "dubbo://supplychain-microservice-warehouseproduct/com.yijiupi.himalaya.supplychain.warehouseproduct.service.<service>/<method>"
    - paths:
        - "/oldpda/product/markSkuCanBeOutOfStock"
        - "/oldwms/product/markSkuCanBeOutOfStock"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/markSkuCanBeOutOfStock"

    - paths:
        - "/oldpda/product/productWarehouseAllocationTypeVerify"
        - "/oldwms/product/productWarehouseAllocationTypeVerify"
      stripPath: true
      signVerify: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/product/productWarehouseAllocationTypeVerify"

    - paths:
        - "/oldpda/redirectToErp/erp/**"
      stripPath: true
      signVerify: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/redirectToErp/erp/"
    - paths:
        - "/oldpda/productlocation/pageList"
        - "/oldwms/productlocation/pageList"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/productlocation/pageList"

    - paths:
        - "/oldwms/location/rule/save"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/location/rule/save"
    - paths:
        - "/oldwms/location/rule/queryList"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/location/rule/queryList"
    - paths:
        - "/oldwms/location/rule/findLocationRuleByCon"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-warehouseproduct#/location/rule/findLocationRuleByCon"

