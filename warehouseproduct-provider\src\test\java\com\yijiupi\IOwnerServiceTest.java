package com.yijiupi;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IOwnerService;
import com.yijiupi.junit.runner.GeneralRunner;
import org.apache.logging.log4j.core.util.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/5/22
 */

@RunWith(GeneralRunner.class)
public class IOwnerServiceTest {
    @Reference
    private IOwnerService iOwnerService;

    @Test
    public void listLoactionInventoryInfoTest() {
        OwnerQueryDTO queryDTO = new OwnerQueryDTO();
        queryDTO.setWarehouseId(9981);
        List<OwnerDTO> ownerDTOList = iOwnerService.getOwnerInfoDTO(queryDTO);
        Assert.isNonEmpty(ownerDTOList);
        System.out.println("ownerDTOList结果："+JSON.toJSONString(ownerDTOList));
    }

}
