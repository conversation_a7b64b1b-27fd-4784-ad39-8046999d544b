package com.yijiupi;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationVisualDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.junit.runner.GeneralRunner;
import org.apache.logging.log4j.core.util.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Arrays;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/4/14
 */

@RunWith(GeneralRunner.class)
public class LocationServiceTest {
    @Reference
    private ILocationService locationService;

    @Test
    public void listLoactionInventoryInfoTest() {
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setAreaId(169368317283391074L);
        locationQueryDTO.setAreaIdList(Arrays.asList(169368317283391074L));
        locationQueryDTO.setWarehouseId(9981);
        PageList<LocationVisualDTO> locationVisualDTOPageList = locationService.listLoactionInventoryInfo(locationQueryDTO);
        Assert.isNonEmpty(locationVisualDTOPageList);
        System.out.println(JSON.toJSONString(locationVisualDTOPageList));
    }


    @Test
    public void pageListLocationTest() {
        LocationInfoQueryDTO locationQueryDTO = new LocationInfoQueryDTO();
        locationQueryDTO.setWarehouseId(9981);
        locationQueryDTO.setCityId(998);
        locationQueryDTO.setPageNum(1);
        locationQueryDTO.setPageSize(20);
        locationQueryDTO.setFilterLocations(Arrays.asList((byte) 50));
        PageList<LoactionDTO> loactionDTOPageList = locationService.pageListLocation(locationQueryDTO);
        Assert.isNonEmpty(loactionDTOPageList);
    }

    @Test
    public void locationProductShelfLifeTest() {
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        locationQueryDTO.setAreaId(169368317302844265L);
        locationQueryDTO.setAreaIdList(Arrays.asList(169368317302844265L));
        locationQueryDTO.setWarehouseId(9981);
        PageList<LocationVisualDTO> locationVisualDTOPageList = locationService.listLoactionInventoryInfo(locationQueryDTO);
        Assert.isNonEmpty(locationQueryDTO);
        System.out.println(JSON.toJSONString(locationVisualDTOPageList));
    }

}
