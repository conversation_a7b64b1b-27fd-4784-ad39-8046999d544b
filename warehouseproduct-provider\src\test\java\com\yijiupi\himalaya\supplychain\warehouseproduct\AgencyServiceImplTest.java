package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.AgencyBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.OwnerBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.WareHouseChargeBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IAgencyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.acl.Owner;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2017/11/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class AgencyServiceImplTest {

    @Autowired
    private AgencyBL agencyBL;
    @Autowired
    private IAgencyService iAgencyService;
    @Autowired
    private OwnerBL ownerBL;

    /**
     * 根据经销商名称或者货位信息,获取经销商信息.
     */
    @Test
    public void getAgencyInfoDTO() {
        OwnerQueryDTO ownerDto = new OwnerQueryDTO();
        ownerDto.setWarehouseId(9991);
        List<OwnerDTO> ownerInfoDTO = ownerBL.getOwnerInfoDTO(ownerDto);
        // List<AgencyInfoDTO> agencyInfoDTO = agencyBL.getAgencyInfoDTO("经销商001", "货位1", 999);
        // List<AgencyInfoDTO> agencyInfoDTO = agencyBL.getAgencyInfoDTO("经销商001", null, 999);
        AgencyInfoQueryDTO dto = new AgencyInfoQueryDTO();
        dto.setCityId(999);
        dto.setAgencyName(null);
        dto.setLocationName("货位");
        dto.setPageNum(1);
        dto.setPageSize(20);
        PageList<AgencyInfoDTO> agencyInfoDTO = agencyBL.getAgencyInfoDTO(dto);
    }

    @Autowired
    private LocationBL locationBL;
    @Autowired
    private WareHouseChargeBL wareHouseChargeBL;

    @Test
    public void test() {
        List<WareHouseDTO> warehouseChargeConfigDTOList = wareHouseChargeBL.listWarehouseDTOByCityId(999);
        System.out.println("===============\n==============\n===============\n=================\n==============\n"
            + "wrehouse列表是:" + new Gson().toJson(warehouseChargeConfigDTOList));

    }
}
