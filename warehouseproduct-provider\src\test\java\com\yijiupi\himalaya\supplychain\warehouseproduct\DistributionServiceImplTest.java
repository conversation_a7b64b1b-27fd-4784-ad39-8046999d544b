package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.distribution.DistributionPercentSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.distribution.DistributionServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2017/12/1
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class DistributionServiceImplTest {

    @Autowired
    private DistributionServiceImpl distributionService;

    /**
     * 给skuId绑定配送系数
     */
    @Test
    public void addDistributionPercent() {
        DistributionPercentSearchDTO dto = new DistributionPercentSearchDTO();
        dto.setDistributionPercent(BigDecimal.valueOf(0.88));
        dto.setDistributionPercentForAmount(BigDecimal.valueOf(0.88));
        ArrayList<Long> longs = new ArrayList<>();
        longs.add(10000000003376L);
        longs.add(10000000004564L);
        dto.setSkuIdList(longs);
        dto.setUserId(126);
        dto.setTrueName("lius");
        distributionService.addDistributionPercent(dto);
    }

    /**
     * 根据skuId获取配送系数
     */
    @Test
    public void getDistributionPercentBySku() {
        DistributionPercentDTO dto = distributionService.getDistributionPercentBySku(10000000001495L);
    }

    @Test
    public void getDistributionPercentBySkuList() {
        List<DistributionPercentDTO> distributionPercentBySkuList =
            distributionService.getDistributionPercentBySkuList(Collections.singletonList(10000000001495L));
    }

    /**
     * 查询一个月内城市配送系数修改记录
     */
    @Test
    public void findDistributionPercentRecord() throws ParseException {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = sim.parse("2018-01-08");
        DistributionPercentRecordDTO distributionRecordDTO = new DistributionPercentRecordDTO();
        distributionRecordDTO.setCityId(999);
        distributionRecordDTO.setEndTime(endDate);
        System.out.println(distributionService.findDistributionPercentRecord(distributionRecordDTO));
    }
}
