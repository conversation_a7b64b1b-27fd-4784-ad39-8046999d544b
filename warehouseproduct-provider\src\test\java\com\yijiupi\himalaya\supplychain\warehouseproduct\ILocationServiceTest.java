package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationCategoryQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IUnifySkuService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @author: lidengfeng
 * @date 2019/3/13 13:35
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class ILocationServiceTest {

    @Reference
    private ILocationService iLocationService;

    @Reference
    private IUnifySkuService iUnifySkuService;

    @Test
    public void findLocationListByName() {
        LocationCategoryQuery query = new LocationCategoryQuery();
        query.setWarehouseId(99968);
        List<String> list = new ArrayList<>();
        list.add("CCQ");
        list.add("CCQ-01-01");
        query.setNameList(list);
        List<LocationCategoryDTO> locationListByName = iLocationService.findLocationListByName(query);
        System.out.println(JSON.toJSONString(locationListByName));
    }

    @Test
    public void saveLocation() {
        LocationCategoryQuery query = new LocationCategoryQuery();
        query.setWarehouseId(99968);
        List<String> list = new ArrayList<>();
        list.add("CCQ");
        list.add("CCQ-01-01");
        query.setNameList(list);
        List<LocationCategoryDTO> locationListByName = iLocationService.findLocationListByName(query);
        System.out.println(JSON.toJSONString(locationListByName));
    }

    @Test
    public void findSkuByUnifySkuAndCity() {
        Integer cityId = 103;
        List<Long> unifySkuIds = Arrays.asList(4957937077252244690L, 4957936732140717252L, 4906455979379921181L);
        Map<Long, List<ProductSkuDTO>> skuMaps = iUnifySkuService.findSkuByUnifySkuAndCity(cityId, unifySkuIds);
        System.out.println(JSON.toJSONString(skuMaps));
    }

}
