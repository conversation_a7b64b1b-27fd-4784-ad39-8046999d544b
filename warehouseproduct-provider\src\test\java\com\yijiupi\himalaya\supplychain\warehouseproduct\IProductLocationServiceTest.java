/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.warehouseproduct;

import java.lang.reflect.Array;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @Title: IProductLocationServiceTest.java
 * @Package com.yijiupi.himalaya.supplychain.warehouseproduct
 * @Description:
 * @date 2017年7月12日 上午10:35:13
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class IProductLocationServiceTest {
    @Autowired
    private IProductLocationService iProductLocationService;

    @Test
    public void listLocation() {
        List<LocationAreaInfoDTO> list = iProductLocationService.listLocation(9991);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void importProductLocation() {
        ProductLocationImportDTO dto = new ProductLocationImportDTO();
        dto.setCityId(999);
        dto.setWarehouseId(9991);
        dto.setUserId(123);
        List<ProductLocationImportItemDTO> itemList = new ArrayList<>();
        ProductLocationImportItemDTO itemDTO = new ProductLocationImportItemDTO();
        itemDTO.setProductSkuId(Long.valueOf("99900000964399"));
        itemDTO.setLocationName("ZYX4-01-01-01");
        itemList.add(itemDTO);
        ProductLocationImportItemDTO itemDTO2 = new ProductLocationImportItemDTO();
        itemDTO2.setProductSkuId(Long.valueOf("99900000482969"));
        itemDTO2.setLocationName("ZYX4-01-01-01");
        itemList.add(itemDTO2);
        dto.setItemList(itemList);
        System.out.println(JSON.toJSONString(dto));
        // iProductLocationService.importProductLocation(dto);
    }

    /**
     * 新增产品货位
     */
    @Test
    public void saveProductLocation() {
        ProductLocationDTO dto = new ProductLocationDTO();
        dto.setCityId(555);
        dto.setWarehouseId(45646);
        dto.setProductSkuId(Long.valueOf("123"));
        dto.setLocationId(Long.valueOf("987"));
        dto.setUserId(999);
        iProductLocationService.saveProductLocation(dto);
    }

    /**
     * 修改产品货位
     */
    @Test
    public void updateProductLocation() {
        ProductLocationDTO dto = new ProductLocationDTO();
        dto.setId(Long.valueOf("55521808011700001"));
        dto.setLocationId(Long.valueOf("996"));
        dto.setUserId(999);
        iProductLocationService.updateProductLocation(dto);
    }

    /**
     * 删除产品货位
     */
    @Test
    public void removeProductLocation() {
        iProductLocationService.removeProductLocation(Long.valueOf("99921808021400006"), null);
    }

    @Test
    public void listProductLocation() {
        ProductLocationListSO so = new ProductLocationListSO();
        so.setCityId(999);
        so.setWarehouseId(9991);
        so.setPageNum(1);
        so.setPageSize(100);
        so.setProductName("52度");
        so.setLocationName("1800");
        so.setSubcategory(Byte.valueOf("1"));
        so.setExistLocation(Byte.valueOf("0"));
        PageList<ProductLocationListDTO> pageList = iProductLocationService.listProductLocation(so);
        System.out.println(JSON.toJSONString(pageList));
    }

    @Test
    public void addTest() {
        LoactionDTO dto = new LoactionDTO();
        dto.setArea_Id(97564580594584998L);
        dto.setCityId(999);
        dto.setWarehouseId(9991);
        dto.setArea("A01");
        dto.setPallets("托盘11");
        dto.setProductlocation("货位11");
        dto.setRoadway("巷道11");
        dto.setUserId(125);
        dto.setSequence(123);
        dto.setIsChaosBatch((byte)1);
        dto.setIsChaosPut((byte)1);
        List<Long> productSkuIdList = new ArrayList<>();
        productSkuIdList.add(99900003456657l);
        // productSkuIdList.add(99900004092449l);
        dto.setProductSkuIdList(productSkuIdList);
        iProductLocationService.add(dto);
    }

    @Test
    public void modify() {
        LoactionDTO dto = new LoactionDTO();
        dto.setId(97302402050295115L);
        dto.setUserId(4530);
        dto.setPallets("2");
        dto.setProductlocation("2");
        dto.setRoadway("f");
        dto.setArea("2");
        dto.setArea_Id(96947491400156832L);
        iProductLocationService.modify(dto);
    }

    @Test
    public void delete() {
        iProductLocationService.delete(745434009612647226L, null);
    }

    @Test
    public void findProductLocationPageListTest() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setCityId(999);
        dto.setWarehouseId(9991);
        // dto.setProductName("货位");
        PageList<LoactionDTO> pageList = iProductLocationService.findProductLocationPageList(dto);
        System.err.println(new Gson().toJson(pageList));
    }

    @Test
    public void findProductLocationList() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(1031);
        // dto.setSubcategory(Arrays.asList((byte) 53));
        iProductLocationService.findProductLocationList(dto);
    }

    @Test
    public void findLocationList() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(9991);
        // dto.setSubcategory(Arrays.asList((byte) 53));
        iProductLocationService.findLocationList(dto);
    }

    @Test
    public void findProductLocationBySkuIdTest() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(9991);
        List<ProductLocationDetailDTO> list = iProductLocationService.findProductLocationBySkuId(dto);
        System.err.println(list);
    }

    @Test
    public void getProductLocationBySkuIdTest() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(9991);
        dto.setCityId(999);
        dto.setSkuId(99900000008527L);
        ProductLocationDetailDTO result = iProductLocationService.getProductLocationBySkuId(dto);
        System.err.println(result);
    }

    @Test
    public void agencyPutInStore() {
        ArrayList<LoactionDTO> loactionDTOS = new ArrayList<>();
        LoactionDTO dto1 = new LoactionDTO();
        dto1.setId(97302402050295113L);
        dto1.setCityId(999);
        dto1.setWarehouseId(9991);
        dto1.setUserId(99);
        dto1.setProductSkuIdList(Collections.singletonList(10000000003376L));
        LoactionDTO dto2 = new LoactionDTO();
        dto2.setId(97302402050295114L);
        dto2.setCityId(999);
        dto2.setWarehouseId(9991);
        dto2.setUserId(99);
        dto2.setProductSkuIdList(Collections.singletonList(10000000004564L));
        loactionDTOS.add(dto1);
        loactionDTOS.add(dto2);
        iProductLocationService.agencyPutInStore(loactionDTOS);
    }

    @Test
    public void importLocation() {
        LocationImportDTO dto = new LocationImportDTO();
        dto.setCity_Id(999);
        dto.setWarehouse_Id(9992);
        dto.setUser_Id(12306);
        List<LoactionDTO> list = new ArrayList<>();
        LoactionDTO locationDto = new LoactionDTO();
        locationDto.setArea("Y10");
        locationDto.setRoadway("1");
        locationDto.setProductlocation("1");
        list.add(locationDto);
        dto.setLocationList(list);
        iProductLocationService.importLocation(dto);
    }

    @Test
    public void findLocationDTOBySkuId() {
        Map<Long, List<LoactionDTO>> locationDTOBySkuId =
            iProductLocationService.findLocationDTOBySkuId(9991, Collections.singletonList(99900065820211L));
    }

    /**
     * 新增产品货位
     */
    @Test
    public void saveProductLocationTest() {
        ProductLocationDTO dto = new ProductLocationDTO();
        dto.setCityId(555);
        dto.setWarehouseId(45646);
        dto.setProductSkuId(Long.valueOf("123"));
        dto.setLocationId(Long.valueOf("987"));
        dto.setUserId(999);
        iProductLocationService.saveProductLocation(dto);
    }
}
