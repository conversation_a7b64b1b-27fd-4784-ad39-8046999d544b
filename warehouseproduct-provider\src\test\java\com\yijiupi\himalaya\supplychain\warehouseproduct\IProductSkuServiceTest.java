package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoSearchDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.ProductSourceCodeCheckDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.UpdateProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuProcessService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSourceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class IProductSkuServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(IProductSkuServiceTest.class);
    @Reference
    private IProductSkuService iProductSkuService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IProductSkuProcessService iProductSkuProcessService;
    @Autowired
    private IProductSourceService iProductSourceService;

    @Test
    public void getProductExceptionReason() {
        List<Long> productSkuIds = new ArrayList<>();
        productSkuIds.add(Long.valueOf("99700000001780"));
        productSkuIds.add(Long.valueOf("99700000002956"));
        Integer result = iProductSkuQueryService.getProductExceptionReason(997, 9971, productSkuIds, null);
        System.out.println("异常类型：" + result);
    }

    @Test
    public void listProductionDate() {
        iProductSkuService.listProductionDate(999, 9991, Arrays.asList(Long.valueOf("99900065758185")));
    }

    @Test
    public void listProductSkuInfo() {
        ProductSkuInfoSO productSkuInfoSO = new ProductSkuInfoSO();
        productSkuInfoSO.setCityId(999);
        productSkuInfoSO.setProductName("口子");
        productSkuInfoSO.setSaleModelList(Arrays.asList(Byte.valueOf("0"), Byte.valueOf("7")));
        iProductSkuService.listProductSkuInfo(productSkuInfoSO);
    }

    // @Test
    public void add() {
        ProductSkuDTO productSkuDTO = new ProductSkuDTO();
        productSkuDTO.setCityId(1);
        productSkuDTO.setName("xxx111111");
        productSkuDTO.setProductSkuId(1232L);
        productSkuDTO.setProductSpecificationId(Long.valueOf(1));
        iProductSkuService.add(productSkuDTO);
    }

    @Test
    public void updatename() {
        List<Integer> ids = new ArrayList();
        ids.add(1);
        // iProductSkuService.updateName("asdfsafa", ids);
    }

    @Test
    public void checkProductBySkuIdList() {
        List<Long> longs = new ArrayList<>();
        longs.add(10000000009680L);
        longs.add(10000000019146L);
        iProductSkuService.checkProductBySkuIdList(longs);
    }

    @Test
    public void getProductInfoBySkuId() {
        List<Long> longs = new ArrayList<>();
        longs.add(10000000001495L);
        longs.add(10000000002191L);
        iProductSkuService.getProductInfoBySkuId(longs);
    }

    /**
     * 根据城市id,产品名称查询产品列表.
     */
    @Test
    public void getProductSkuInfo() {
        ProductSkuInfoSearchDTO dto = new ProductSkuInfoSearchDTO();
        dto.setProductName("中国至尊丹");
        dto.setPrecise(0);
        PageList<ProductSkuInfoReturnDTO> productSkuInfo = iProductSkuService.getProductSkuInfo(dto);
    }

    /**
     * 根据productskuid列表查询配送系数-工资 TODO
     * 
     * @return: void
     */
    @Test
    public void getProductInfoBySkuList() {
        List<Long> productSkuIdList = new ArrayList<>();
        productSkuIdList.add(10000000001495L);
        productSkuIdList.add(10000000002191L);
        productSkuIdList.add(10000000003376L);
        productSkuIdList.add(10000000004564L);
        Map<Long, BigDecimal> info = iProductSkuService.getDistributionPercentForAmountBySkuList(productSkuIdList);
        System.err.println(new Gson().toJson(info));
    }

    @Test
    public void getOwnerInfoBySkuId() {
        ArrayList<Long> list = new ArrayList<>();
        list.add(10000000001495L);// 0
        list.add(10000000075587L);// 1
        list.add(10000000065991L);// 3
        list.add(99900055427595L);// 3
        list.add(10100015856682L);// 2
        list.add(99700044438659L);// 6
        List<OwnerInfoDTO> ownerInfoBySkuId = iProductSkuService.getOwnerInfoBySkuId(list);
    }

    @Test
    public void pageList() {
        ProductSkuQueryDTO dto = new ProductSkuQueryDTO();
        dto.setCityId(999);
        dto.setIsComplete(true);
        PageList<ProductSkuDTO> pageList = iProductSkuQueryService.pageList(dto);
        System.err.println(new Gson().toJson(pageList));
    }

    @Test
    public void findBySku() {
        List<ProductSkuDTO> pageList = iProductSkuQueryService.findBySku(Arrays.asList(99800055743620L));
        System.err.println(new Gson().toJson(pageList));
    }

    @Test
    public void batchUpdate() {
        ProductSkuDTO dto = new ProductSkuDTO();
        dto.setProductSkuId(Long.valueOf("99700008906786"));
        dto.setProductSpecificationId(Long.valueOf("8906"));
        dto.setWidth(2D);
        dto.setHeight(3D);
        dto.setWeight(4D);
        dto.setLength(1D);
        dto.setMaxInventory(new BigDecimal(999));
        dto.setMinInventory(new BigDecimal(1));
        dto.setMaxReplenishment(new BigDecimal(888));
        dto.setMinReplenishment(new BigDecimal(2));
        dto.setProductFeature((byte)2);
        dto.setPick((byte)1);
        dto.setSow((byte)0);
        dto.setStorageType((byte)1);
        List<ProductSkuDTO> list = new ArrayList<>();
        list.add(dto);
        iProductSkuProcessService.batchUpdate(list);
    }

    @Autowired
    private ProductSourceBL productSourceBL;

    @Test
    public void checkProductSourceId() {
        List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOS = new ArrayList<>();
        List<ProductSourceCodeCheckDTO> productSourceCodeCheckDTOSNew = new ArrayList<>();
        // ProductSourceCodeCheckDTO productSourceCodeCheckDTO = new ProductSourceCodeCheckDTO();
        // productSourceCodeCheckDTO.setSourceCode("SY9981191101000081");
        // productSourceCodeCheckDTO.setConfigId(4722118260078519513L);
        // productSourceCodeCheckDTO.setWarehouseId(9981);
//
//        ProductSourceCodeCheckDTO productSourceCodeCheckDTO1 = new ProductSourceCodeCheckDTO();
//        productSourceCodeCheckDTO1.setSourceCode("************");
//        productSourceCodeCheckDTO1.setProductSkuId(10000064458800L);
//        productSourceCodeCheckDTO1.setWarehouseId(1001);

        // productSourceCodeCheckDTOS.add(productSourceCodeCheckDTO);
//        productSourceCodeCheckDTOS.add(productSourceCodeCheckDTO1);
        String 校验通过 = "[\n" +
                "    {\n" +
                "        \"productSkuId\": 5153340886046885060,\n" +
                "        \"sourceCode\": \"11\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    },\n" +
                "    {\n" +
                "        \"productSkuId\": 5153340886046885060,\n" +
                "        \"sourceCode\": \"11\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    }\n" +
                "]";

        String 重复使用 = "[\n" +
                "    {\n" +
                "        \"productSkuId\": 5153340886046885060,\n" +
                "        \"sourceCode\": \"12\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    },\n" +
                "    {\n" +
                "        \"productSkuId\": 5153340886046885060,\n" +
                "        \"sourceCode\": \"12\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    },\n" +
                "    {\n" +
                "        \"productSkuId\": 5153340886046885060,\n" +
                "        \"sourceCode\": \"13\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    },\n" +
//                "    {\n" +
//                "        \"productSkuId\": 5153340886046885060,\n" +
//                "        \"sourceCode\": \"14\",\n" +
//                "        \"warehouseId\": 1641\n" +
//                "    },\n" +
                "    {\n" +
                "        \"productSkuId\": 5152898630144580369,\n" +
                "        \"sourceCode\": \"12\",\n" +
                "        \"warehouseId\": 1641\n" +
                "    }\n" +
                "]";
//        productSourceCodeCheckDTOS = JSON.parseArray(重复使用, ProductSourceCodeCheckDTO.class);
//        List<ProductSourceCodeInfoDTO> productSourceCodeDTOS =
//            productSourceBL.checkProductSourceId(productSourceCodeCheckDTOS);
//        System.out.println("productSourceCodeDTOS结果:"+JSON.toJSONString(productSourceCodeDTOS));

        productSourceCodeCheckDTOSNew = JSON.parseArray(重复使用, ProductSourceCodeCheckDTO.class);
        List<ProductSourceCodeInfoDTO> productSourceCodeDTOSNew =
                productSourceBL.checkProductSourceIdNew(productSourceCodeCheckDTOSNew);
        System.out.println("productSourceCodeDTOSNew结果:"+JSON.toJSONString(productSourceCodeDTOSNew));
    }

    @Test
    public void updateProductSourceCodeInfo() {

        List<UpdateProductSourceCodeDTO> updateProductSourceCodeDTOS = new ArrayList<>();
        UpdateProductSourceCodeDTO UpdateProductSourceCodeDTO = new UpdateProductSourceCodeDTO();
        UpdateProductSourceCodeDTO.setProductSourceCodeId(4990193692689989636L);
        UpdateProductSourceCodeDTO.setWarehouseId(9981);
        UpdateProductSourceCodeDTO.setBusinessType((byte)2);
        UpdateProductSourceCodeDTO.setNeedToChangeWarehouse(true);
        UpdateProductSourceCodeDTO.setOperateUserId(14399);

        // UpdateProductSourceCodeDTO UpdateProductSourceCodeDTO2 = new UpdateProductSourceCodeDTO();
        // UpdateProductSourceCodeDTO2.setProductSourceCodeId(47221502731144193L);
        // UpdateProductSourceCodeDTO2.setWarehouseId(998);
        // UpdateProductSourceCodeDTO2.setBusinessType((byte) 2);
        // UpdateProductSourceCodeDTO2.setNeedToChangeWarehouse(true);
        // UpdateProductSourceCodeDTO.setOperateUserId(14399);

        updateProductSourceCodeDTOS.add(UpdateProductSourceCodeDTO);
        // updateProductSourceCodeDTOS.add(UpdateProductSourceCodeDTO2);
        iProductSourceService.updateProductSourceCodeInfo(updateProductSourceCodeDTOS);
    }

}
