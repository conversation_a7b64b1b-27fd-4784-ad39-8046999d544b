package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ISortGroupService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 分区测试类
 * 
 * <AUTHOR>
 * @date 2018/5/16 10:53
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ISortGroupServiceTest {

    @Autowired
    private ISortGroupService iSortGroupService;

    @Test
    public void listGroupIdByUserId() {
        List<Long> ids = iSortGroupService.listGroupIdByUserId(13031, (byte)0);
        System.out.println(JSON.toJSONString(ids));
    }

    /**
     * 获取分区列表
     */
    @Test
    public void listGroupTest() {
        SortGroupSO so = new SortGroupSO();
        so.setWarehouseId(123456);
        so.setPageNum(1);
        so.setPageSize(20);
        so.setType("啤酒");
        PageList<SortGroupListDTO> pageList = iSortGroupService.listGroup(so);
        pageList.getDataList().forEach(dto -> {
            System.out.println(dto);
        });

        System.out.println(pageList.getPager().getTotalPage());
        System.out.println(pageList.getPager().getPageSize());
        System.out.println(pageList.getPager().getRecordCount());
        System.out.println(pageList.getPager().getCurrentPage());
    }

    /**
     * 新增分区
     */
    @Test
    public void insertGroupTest() {
        SortGroupDTO sortGroupDTO = new SortGroupDTO();
        sortGroupDTO.setName("分区3");
        sortGroupDTO.setWarehouseId(123456);
        sortGroupDTO.setRemark("这是备注3");
        sortGroupDTO.setCreateUser("余攀");

        List<SortGroupSettingDTO> settingDTOList = new ArrayList<>();
        SortGroupSettingDTO settingDTO = new SortGroupSettingDTO();
        settingDTO.setSortType(3);
        settingDTO.setSortId("2");
        settingDTO.setSortName("啤酒");
        settingDTOList.add(settingDTO);

        SortGroupSettingDTO settingDTO2 = new SortGroupSettingDTO();
        settingDTO2.setSortType(3);
        settingDTO2.setSortId("1");
        settingDTO2.setSortName("饮料");
        settingDTOList.add(settingDTO2);
        // 分区类别
        sortGroupDTO.setGroupSettingList(settingDTOList);

        try {
            List<SortGroupUserDTO> userDTOList = new ArrayList<>();
            SortGroupUserDTO userDTO = new SortGroupUserDTO();
            userDTO.setUserId(1);
            userDTO.setUserName("王师傅");
            userDTO.setWorkStartDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-04-01"));
            userDTO.setWorkEndDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-09-01"));
            userDTO.setWorkDayType(1);
            userDTO.setWorkDayDetail("1,3,5");
            userDTO.setWorkStartTime(new SimpleDateFormat("HH:mm:ss").parse("09:00:00"));
            userDTO.setWorkEndTime(new SimpleDateFormat("HH:mm:ss").parse("18:00:00"));
            userDTOList.add(userDTO);

            SortGroupUserDTO userDTO2 = new SortGroupUserDTO();
            userDTO2.setUserId(2);
            userDTO2.setUserName("李师傅");
            userDTO2.setWorkStartDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-01-08"));
            // userDTO2.setWorkEndDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-09-01"));
            userDTO2.setWorkDayType(2);
            userDTO2.setWorkDayDetail("1,2,3,4,5,6,7,8,9,10");
            userDTO2.setWorkStartTime(new SimpleDateFormat("HH:mm:ss").parse("19:00:00"));
            userDTO2.setWorkEndTime(new SimpleDateFormat("HH:mm:ss").parse("07:00:00"));
            userDTOList.add(userDTO2);

            // 分区人员
            sortGroupDTO.setGroupUserList(userDTOList);
        } catch (ParseException e) {
        }

        Long groupId = iSortGroupService.insertGroup(sortGroupDTO);
        System.out.println("新增分区Id：" + groupId);
    }

    /**
     * 查看分区
     */
    @Test
    public void getGroupTest() {
        SortGroupDTO sortGroupDTO = iSortGroupService.getGroup(new Long("97564580594587725"));
        System.out.println(sortGroupDTO);

        sortGroupDTO.getGroupSettingList().forEach(settingDTO -> {
            System.out.println(settingDTO);
        });

        sortGroupDTO.getGroupUserList().forEach(userDTO -> {
            System.out.println(userDTO);
        });
    }

    /**
     * 修改分区
     */
    @Test
    public void updateGroupTest() {
        SortGroupDTO sortGroupDTO = new SortGroupDTO();
        sortGroupDTO.setId(new Long("97564580594587725"));
        sortGroupDTO.setName("分区2");
        sortGroupDTO.setRemark("备注2");
        sortGroupDTO.setLastUpdateUser("余攀1");

        List<SortGroupSettingDTO> settingDTOList = new ArrayList<>();
        SortGroupSettingDTO settingDTO = new SortGroupSettingDTO();
        settingDTO.setSortType(11);
        settingDTO.setSortId("1001");
        settingDTO.setSortName("存储区1");
        settingDTOList.add(settingDTO);

        SortGroupSettingDTO settingDTO2 = new SortGroupSettingDTO();
        settingDTO2.setSortType(31);
        settingDTO2.setSortId("11");
        settingDTO2.setSortName("饮料1");
        settingDTOList.add(settingDTO2);
        // 分区类别
        sortGroupDTO.setGroupSettingList(settingDTOList);

        iSortGroupService.updateGroup(sortGroupDTO);
        System.out.println("修改分区");
    }

    /**
     * 删除分区
     */
    @Test
    public void deleteGroupTest() {
        iSortGroupService.deleteGroup(new Long("97564580594587725"));
    }

    /**
     * 获取拣货员列表
     */
    @Test
    public void listGroupUser() {
        SortGroupUserSO so = new SortGroupUserSO();
        so.setPageNum(1);
        so.setPageSize(20);
        so.setGroupId(new Long("97564580594587736"));
        PageList<SortGroupUserDTO> pageList = iSortGroupService.listGroupUser(so);
        pageList.getDataList().forEach(dto -> {
            System.out.println(dto);
        });

        System.out.println(pageList.getPager().getTotalPage());
        System.out.println(pageList.getPager().getPageSize());
        System.out.println(pageList.getPager().getRecordCount());
        System.out.println(pageList.getPager().getCurrentPage());
    }

    /**
     * 新增拣货员
     */
    @Test
    public void insertGroupUserTest() {
        try {
            SortGroupUserDTO dto = new SortGroupUserDTO();
            dto.setSortGroupId(new Long("97564580594587725"));
            dto.setUserId(3);
            dto.setUserName("余师傅");
            dto.setWorkStartDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-05-01"));
            dto.setWorkEndDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-06-06"));
            dto.setWorkDayType(1);
            dto.setWorkDayDetail("2,4,6");
            dto.setWorkStartTime(new SimpleDateFormat("HH:mm:ss").parse("09:00:00"));
            dto.setWorkEndTime(new SimpleDateFormat("HH:mm:ss").parse("18:00:00"));
            dto.setCreateUser("余攀");
            Long id = iSortGroupService.insertGroupUser(dto);
            System.out.println("新增拣货员ID：" + id);
        } catch (ParseException e) {
        }
    }

    /**
     * 查看拣货员
     */
    @Test
    public void getGroupUserTest() {
        SortGroupUserDTO dto = iSortGroupService.getGroupUser(new Long("97564580594587728"));
        System.out.println(dto);
    }

    /**
     * 修改拣货员
     */
    @Test
    public void updateGroupUserTest() {
        try {
            SortGroupUserDTO dto = new SortGroupUserDTO();
            dto.setId(new Long("97564580594587728"));
            dto.setUserId(4);
            dto.setUserName("刘师傅");
            dto.setWorkStartDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-05-05"));
            dto.setWorkEndDate(new SimpleDateFormat("yyyy-MM-dd").parse("2018-06-06"));
            dto.setWorkDayType(2);
            dto.setWorkDayDetail("2,4,6，8");
            dto.setWorkStartTime(new SimpleDateFormat("HH:mm:ss").parse("10:00:00"));
            dto.setWorkEndTime(new SimpleDateFormat("HH:mm:ss").parse("15:00:00"));
            dto.setLastUpdateUser("余攀1");
            iSortGroupService.updateGroupUser(dto);
            System.out.println("修改拣货员");
        } catch (ParseException e) {
        }
    }

    /**
     * 修改拣货员状态
     */
    @Test
    public void updateGroupUserStateTest() {
        SortGroupUserDTO dto = new SortGroupUserDTO();
        dto.setId(new Long("97564580594587728"));
        dto.setState(0);
        dto.setLastUpdateUser("余攀2");
        iSortGroupService.updateGroupUser(dto);
    }

    /**
     * 删除拣货员
     */
    @Test
    public void deleteGroupUserTest() {
        iSortGroupService.deleteGroupUser(new Long("97564580594587734"));
        System.out.println("删除拣货员");
    }

    /**
     * 获取拣货员列表
     */
    @Test
    public void listGroupUserBySelecTest() {
        SortGroupUserSelectSO so = new SortGroupUserSelectSO();
        // so.setWarehouseId(123456);
        // so.setType(new Byte("3"));
        // List<String> propertyList = new ArrayList<>();
        // propertyList.add("啤酒");
        // propertyList.add("红茶");
        // so.setPropertyList(propertyList);
        so.setGroupId(Long.valueOf("1466881769037891741"));
        List<SortGroupUserDTO> list = iSortGroupService.listGroupUserBySelect(so);
        if (list != null) {
            list.forEach(dto -> {
                System.out.println(dto);
            });
        }
    }

}
