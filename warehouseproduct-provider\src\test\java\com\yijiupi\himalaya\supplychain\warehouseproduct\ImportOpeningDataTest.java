package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ImportOpeningDataBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ImportOwnerDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * chengkai 2022/10/25
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ImportOpeningDataTest {
    @Autowired
    private ImportOpeningDataBL importOpeningDataBL;

    @Test
    public void importOwnerTest() throws IOException {
        ImportOwnerDTO dto = new ImportOwnerDTO();
        File file = new File("E:\\货主.xls");
        MultipartFile cMultiFile = new MockMultipartFile("file", file.getName(), null, new FileInputStream(file));
        dto.setFile(cMultiFile);
        dto.setCityId(998);
        importOpeningDataBL.importOwner(dto);
    }
}
