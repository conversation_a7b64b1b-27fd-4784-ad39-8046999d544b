package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationAreaBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018/1/19
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class LocationAreaTest {
    @Autowired
    private LocationAreaService locationAreaService;
    @Autowired
    private LocationBL locationBL;

    @Test
    public void addLocationArea() {
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setCityId(999);
        locationAreaDTO.setWarehouseId(9992);
        locationAreaDTO.setArea("T22");
        locationAreaDTO.setUserId(4233);

        locationAreaDTO.setWidth(BigDecimal.valueOf(10000L));
        locationAreaDTO.setHeight(BigDecimal.valueOf(10000L));
        locationAreaDTO.setCoordinateX(BigDecimal.valueOf(10000L));
        locationAreaDTO.setCoordinateY(BigDecimal.valueOf(10000L));
        locationAreaDTO.setLayer(1);
        locationAreaService.addLocationArea(locationAreaDTO);
    }

    @Test
    public void findLocationListById() {
        List<String> idList = new ArrayList<>();
        idList.add("96947491400156832");
        idList.add("97302402050295113");
        List<LocationReturnDTO> locationListById = locationAreaService.findLocationListById(idList);
        System.out.println(1);
    }

    @Test
    public void getLocationArea() {
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setCityId(999);
        locationAreaDTO.setWarehouseId(9991);
        locationAreaDTO.setArea("");
        PageList<LocationAreaReturnDTO> locationArea = locationAreaService.getLocationArea(locationAreaDTO);
        System.out.println(1);

    }

    @Test
    public void getLocationAreaNoPage1() {
        // LocationAreaListDTO
        LocationAreaListDTO dto = new LocationAreaListDTO();
        dto.setWarehouseId(9992);
        dto.setCityId(999);
        List<LocationAreaReturnDTO> dtos = locationAreaService.getLocationAreaNoPage(dto);
        System.out.println(JSON.toJSONString(dtos));
    }

    @Test
    public void deleteLocationArea() {
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setId(97564580594584318L);
        locationAreaService.deleteLocationArea(locationAreaDTO, null);
    }

    @Test
    public void getLocationAreaNoPage() {
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setWarehouseId(9991);
        // locationAreaService.getLocationAreaNoPage(locationAreaDTO);
    }

    @Test
    public void updateLocationArea() {
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setId(819295497423562886L);
        locationAreaDTO.setArea("T22");
        locationAreaDTO.setRemo("测试货区5");
        locationAreaDTO.setSubcategory((byte)50);
        locationAreaDTO.setUserId(9737);

        // locationAreaDTO.setWidth(BigDecimal.valueOf(20000L));
        // locationAreaDTO.setHeight(BigDecimal.valueOf(20000L));
        // locationAreaDTO.setCoordinateX(BigDecimal.valueOf(20000L));
        // locationAreaDTO.setCoordinateY(BigDecimal.valueOf(20000L));
        locationAreaService.updateLocationArea(locationAreaDTO);
    }

    @Test
    public void test1() {
        LoactionDTO loactionDTO = new LoactionDTO();
        loactionDTO.setId(1121111L);
        loactionDTO.setIsChaosBatch((byte)0);
        loactionDTO.setIsChaosPut((byte)0);
        loactionDTO.setLocationCapacity(1111);
        System.out.println("==============\n===============\n==============\n===============\n" + ":"
            + new Gson().toJson(loactionDTO).toString());
    }

    @Autowired
    private LocationAreaBL locationAreaBL;

    @Test
    public void importLocationAreaTest() throws IOException {
        ImportLocationDTO dto = new ImportLocationDTO();
        File file = new File("E:\\库区.xls");
        MultipartFile cMultiFile = new MockMultipartFile("file", file.getName(), null, new FileInputStream(file));
        dto.setFile(cMultiFile);
        dto.setCityId(998);
        dto.setWarehouseId(9981);
        locationAreaBL.importLocationArea(dto);
    }
}
