package com.yijiupi.himalaya.supplychain.warehouseproduct;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.LocationBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AgencyConnectLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.GetUnusedLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionVisualUpdateDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationModifyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationVisualDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.LocationServiceImpl;

/**
 * <AUTHOR> 2017/11/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class LocationServiceImplTest {

    @Autowired
    private LocationBL locationBL;
    @Autowired
    private LocationServiceImpl locationServiceImpl;

    /**
     * 经销商关联货位
     */
    @Test
    public void AgencyConnectLocation() {
        AgencyConnectLocationDTO dto = getAgencyConnectLocationDTO();
        locationBL.addAgencyLocation(dto);
    }

    /**
     * 根据经销商id和仓库id查询货位信息
     */
    @Test
    public void getLocation() {
        List<LocationInfoDTO> location = locationBL.getLocation(123456789L, 9991);
        System.err.println(location);
    }

    /**
     * 根据经销商id和仓库id,sku查询货位信息
     */
    @Test
    public void getLocation2() {
        List<LocationInfoDTO> location = locationBL.getLocation(123456789L, 9991, 99900000482969L);
        System.err.println(location);
    }

    /**
     * 查询某个城市下,未被关联的货位 2/28测试
     */
    @Test
    public void getUnusedLocation() {
        GetUnusedLocationDTO dto = new GetUnusedLocationDTO();
        dto.setCityId(999);
        dto.setPageNum(1);
        dto.setPageSize(20);
        PageList<LocationInfoDTO> location = locationBL.getUnusedLocation(dto);
        System.err.println(location.getDataList().size());
    }

    /**
     * 删除经销商与货位关系
     */
    @Test
    public void delete() {
        AgencyConnectLocationDTO dto = getAgencyConnectLocationDTO();
        dto.setAgencyId(123456789L);
        ArrayList<LocationInfoDTO> locationInfoDTOS = new ArrayList<>();
        LocationInfoDTO locationInfoDTO1 = new LocationInfoDTO();
        locationInfoDTO1.setLocationId(97302402050303372L);
        locationInfoDTOS.add(locationInfoDTO1);
        LocationInfoDTO locationInfoDTO2 = new LocationInfoDTO();
        locationInfoDTO2.setLocationId(97302402050295113L);
        locationInfoDTOS.add(locationInfoDTO2);

        dto.setLocationInfoDTOList(locationInfoDTOS);
        locationBL.removeAgencyLocation(dto);
    }

    /**
     * 根据经销商ud查询经销商所有货位信息
     */
    @Test
    public void getLocationInfoByAgencyId() {
        List<LocationInfoDTO> locationInfoByAgencyId = locationBL.getLocationInfoByAgencyId(6L);
        System.out.println(locationInfoByAgencyId.size());
    }

    /**
     * 根据经销商id删除货位配置
     */
    @Test
    public void removeAgencyLocationByAgencyId() {
        locationBL.removeAgencyLocationByAgencyId(123456L);
        System.out.println(locationBL.getLocationInfoByAgencyId(123456L).size());
    }

    private AgencyConnectLocationDTO getAgencyConnectLocationDTO() {
        AgencyConnectLocationDTO dto = new AgencyConnectLocationDTO();
        dto.setAgencyId(123456732L);
        dto.setAgencyName("经销商001");
        dto.setCityId(999);
        dto.setUserId(99);
        dto.setWarehouseId(9991);
        dto.setRemark("备注");

        ArrayList<LocationInfoDTO> list = new ArrayList<>();

        LocationInfoDTO locationInfoDTO1 = new LocationInfoDTO();
        locationInfoDTO1.setLocationName("货位1");
        locationInfoDTO1.setLocationId(97302402050295113L);

        LocationInfoDTO locationInfoDTO2 = new LocationInfoDTO();
        locationInfoDTO2.setLocationName("货位2");
        locationInfoDTO2.setLocationId(97302402050303372L);
        list.add(locationInfoDTO1);
        list.add(locationInfoDTO2);

        dto.setLocationInfoDTOList(list);
        return dto;
    }

    @Test
    public void test() {
        String locationType = "分拣位";
        LocationEnum[] locationEnums = LocationEnum.values();
        for (int i = 0; i < locationEnums.length; i++) {
            System.out.println("=====================\n==================\n"
                + "====================\n====================\n==================\n" + locationEnums[i].name());
            if (locationEnums[i].name().equals(locationType)) {
                System.out.println("=====================\n==================\n"
                    + "====================\n====================\n==================\n"
                    + locationEnums[i].getType().byteValue());
                break;
            }
        }
    }

    @Test
    public void test2() {
        List<LocationModifyDTO> locationModifyDTOList = new ArrayList<LocationModifyDTO>();
        LocationModifyDTO locationModifyDTO = new LocationModifyDTO();
        locationModifyDTO.setName("nimasl");
        locationModifyDTO.setSubcategoryStr("分拣位");
        locationModifyDTO.setIsChaosBatch((byte)0);
        locationModifyDTO.setIsChaosPut((byte)0);
        locationModifyDTO.setLocationCapacity(777);
        locationModifyDTO.setSequence(555888);
        LocationModifyDTO locationModifyDTO2 = new LocationModifyDTO();
        locationModifyDTO2.setName("CCQ");
        locationModifyDTO2.setSubcategoryStr("操作台");
        locationModifyDTO2.setIsChaosBatch((byte)0);
        locationModifyDTO2.setIsChaosPut((byte)0);
        locationModifyDTO2.setLocationCapacity(4444777);
        locationModifyDTO2.setSequence(333888);
        locationModifyDTOList.add(locationModifyDTO);
        locationModifyDTOList.add(locationModifyDTO2);
        // locationBL.modifyLocation(locationModifyDTOList);\
        System.out.println("=============\n================\n==============\n================\n"
            + "=============\n===============\n要生成的json字符串是:" + new Gson().toJson(locationModifyDTOList));
    }

    @Test
    public void listLocationWarehouseId() {
        LocationInfoQueryDTO locationQueryDTO = new LocationInfoQueryDTO();
        locationQueryDTO.setPageNum(1);
        locationQueryDTO.setPageSize(10000);
        locationQueryDTO.setWarehouseId(9981);
        PageList<LocationVisualDTO> los = locationServiceImpl.listLocationPage(locationQueryDTO);
        System.out.println("显示的可视货位数目：" + JSON.toJSONString(los));
    }

    @Test
    public void saveBatchLocation() {
        LoactionVisualUpdateDTO loactionVisualUpdateDTO = new LoactionVisualUpdateDTO();
        loactionVisualUpdateDTO.setWarehouseId(99810);
        loactionVisualUpdateDTO.setCityId(998);
        loactionVisualUpdateDTO.setLocationAreaId(169368317283391074L);
        loactionVisualUpdateDTO.setOperatorId(1111);

        List<LoactionDTO> saveLocationDTOs = new ArrayList<>();
        loactionVisualUpdateDTO.setSaveLocationDTOs(saveLocationDTOs);

        LoactionDTO loactionDTO = new LoactionDTO();
        // loactionDTO.setArea_Id(169368317283391074L);
        loactionDTO.setId(169368317283391421L);
        loactionDTO.setWidth(new BigDecimal(100));
        loactionDTO.setHeight(new BigDecimal(100));
        loactionDTO.setLayer(1);
        loactionDTO.setCoordinateX(new BigDecimal(100));
        loactionDTO.setCoordinateY(new BigDecimal(100));

        LoactionDTO loactionDTO2 = new LoactionDTO();
        // loactionDTO.setArea_Id(169368317283391074L);
        loactionDTO2.setId(169368317283391421L);
        loactionDTO2.setWidth(new BigDecimal(100));
        loactionDTO2.setHeight(new BigDecimal(100));
        loactionDTO2.setLayer(1);
        loactionDTO2.setCoordinateX(new BigDecimal(100));
        loactionDTO2.setCoordinateY(new BigDecimal(100));
        saveLocationDTOs.add(loactionDTO2);

        LoactionDTO loactionDTO1 = new LoactionDTO();
        // loactionDTO1.setArea_Id(169368317283391074L);
        // loactionDTO1.setId(169368317283391421L);
        loactionDTO1.setWidth(new BigDecimal(200));
        loactionDTO1.setHeight(new BigDecimal(200));
        loactionDTO1.setLayer(1);
        loactionDTO1.setRoadway("1");
        loactionDTO1.setCoordinateX(new BigDecimal(200));
        loactionDTO1.setCoordinateY(new BigDecimal(200));
        saveLocationDTOs.add(loactionDTO1);

        locationServiceImpl.saveBatch(loactionVisualUpdateDTO);
    }

    @Test
    public void listVisualLocation() {
        LocationQueryDTO locationQueryDTO = new LocationQueryDTO();
        List<LocationVisualDTO> los = locationServiceImpl.listLocation(locationQueryDTO);
        System.out.println("显示的可视货位数目：" + JSON.toJSONString(los));
    }
}
