package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productskulabel.ProductSkuLabelSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.ProductSkuLabelServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Map;

/**
 * 拣货通道测试类
 *
 * <AUTHOR>
 * @date 2018/8/8 17:48
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ProductSkuLabelServiceImplTest {

    @Autowired
    private ProductSkuLabelServiceImpl productSkuLabelService;

    @Test
    public void getProductSkuLabelByDeliveryMap() {
        ProductSkuLabelSO productSkuLabelSO = new ProductSkuLabelSO();
        productSkuLabelSO.setOrgId(999);
        productSkuLabelSO.setWarehouseId(9991);
        productSkuLabelSO.setProductSkuIds(Arrays.asList(Long.valueOf("************"), Long.valueOf("12344")));
        Map<Long, Boolean> map = productSkuLabelService.getProductByDirectDeliveryMap(productSkuLabelSO);
        System.out.println(JSON.toJSON(map));
    }

}
