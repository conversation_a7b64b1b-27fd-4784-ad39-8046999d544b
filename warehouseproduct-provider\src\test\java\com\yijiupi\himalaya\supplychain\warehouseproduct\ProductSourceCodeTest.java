package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSourceNewBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 溯源码测试
 *
 * <AUTHOR>
 * @Date 2021/8/2 14:46
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductSourceCodeTest {

    @Autowired
    private ProductSourceNewBL productSourceNewBL;

    @Test
    public void codeRecordSync() {
        ProductSourceCodeRecordSyncDTO codeRecordSyncDTO = new ProductSourceCodeRecordSyncDTO();

        List<Long> productSourceCodeIds = Arrays.asList(4899293445181531209L, 4898567397353994639L);

        codeRecordSyncDTO.setProductSourceCodeIds(productSourceCodeIds);
        codeRecordSyncDTO.setAfterState((byte)0);
        codeRecordSyncDTO.setBusinessType((byte)1);
        codeRecordSyncDTO.setBusinessId("123456");
        codeRecordSyncDTO.setBusinessNo("TEST123sss");
        codeRecordSyncDTO.setDescribe("描述123sss");
        codeRecordSyncDTO.setRemark("备注123sss");
        // codeRecordSyncDTO.setCreateUser("王贤psss");
        codeRecordSyncDTO.setCreateTime(new Date());
        productSourceNewBL.addProductSourceCodeRecordFromTms(codeRecordSyncDTO);
    }
}
