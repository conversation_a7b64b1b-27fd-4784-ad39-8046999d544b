package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.ProductSkuQueryBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/9/7 16:56
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class RelationPackageSkuTest {
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Test
    public void listDiffPackageSkuGroupTest() {
        String json = "{\n" + "    \"cityId\": 998,\n" + "    \"skuIdList\": [4966978233193974913]\n" + "}";
        ProductSkuQueryDTO queryDto = JSON.parseObject(json, ProductSkuQueryDTO.class);
        productSkuQueryBL.listRelationPackageSku(queryDto);
    }
}
