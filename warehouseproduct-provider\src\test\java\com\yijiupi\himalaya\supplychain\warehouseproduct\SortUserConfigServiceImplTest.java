package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.google.common.collect.Lists;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.LocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.SelectSortUser;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.SortPropertyByUserDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.sortuser.SortUserConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.SortUserConfigServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Array;
import java.util.Arrays;

/**
 * <AUTHOR> 2018/3/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class SortUserConfigServiceImplTest {
    @Autowired
    private SortUserConfigServiceImpl sortUserConfigService;

    /**
     * 通过仓库id查找分拣员
     *
     * @throws Exception
     */
    @Test
    public void findSortUserByWarehouseId() throws Exception {
        sortUserConfigService.findSortUserByWarehouseId(9991);
    }

    @Test
    public void selectSortUser() {
        SelectSortUser selectSortUser = new SelectSortUser();
        selectSortUser.setWarehouseId(9991);
        PageList<SortUserConfigDTO> dtoPageList = sortUserConfigService.selectSortUser(selectSortUser);
        System.out.println(1);
    }

    /**
     * 通过id查找分拣员
     *
     * @throws Exception
     */
    @Test
    public void findSortUserById() throws Exception {
        sortUserConfigService.findSortUserById(147);
    }

    @Test
    public void findSortPropertyByUser() {
        SortPropertyByUserDTO dto = new SortPropertyByUserDTO();
        dto.setType((byte)1);
        dto.setSelectType((byte)1);
        dto.setWarehouseId(9991);
        dto.setPropertyList(Arrays.asList("169675656907557211", "169675656907561813", "169675656907561813",
            "169675656907561813", "169675656907561813", "169675656907561813L"));
        sortUserConfigService.findSortPropertyByUser(dto);
    }

    /**
     * 添加分拣员策略
     */
    @Test
    public void addSortUserConfig() throws Exception {
        SortUserConfigDTO sortUserConfigDTO = new SortUserConfigDTO();
        sortUserConfigDTO.setCreateUser("sadsa");
        sortUserConfigDTO.setUserId(99);
        sortUserConfigDTO.setUserName("aa");
        sortUserConfigDTO.setWarehouseId(9991);
        sortUserConfigDTO.setType((byte)1);
        sortUserConfigDTO.setCategoryList(com.google.common.collect.Lists.newArrayList("白酒", "红酒", "洋酒"));
        LocationDTO locationDTO1 = new LocationDTO();
        locationDTO1.setId("asdasdas");
        locationDTO1.setName("aaa-bbb-ccc-ddd");
        LocationDTO locationDTO2 = new LocationDTO();
        locationDTO2.setId("dsa123as");
        locationDTO2.setName("r01-2sd-2s1-sss");
        sortUserConfigDTO.setLocationList(Lists.newArrayList(locationDTO1, locationDTO2));
        LocationAreaDTO locationAreaDTO = new LocationAreaDTO();
        locationAreaDTO.setId("sadasd123");
        locationAreaDTO.setArea("r01");
        sortUserConfigDTO.setLocationAreaList(Lists.newArrayList(locationAreaDTO));
        sortUserConfigService.addSortUserConfig(sortUserConfigDTO);
    }
}
