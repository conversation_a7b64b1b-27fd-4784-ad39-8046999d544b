package com.yijiupi.himalaya.supplychain.warehouseproduct;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AllotRegulationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseChargeQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WarehouseCopyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IWarehouseChargeService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.impl.StoreWareHouseServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/3/9 14:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class StoreHouseTest {
    @Autowired
    private StoreWareHouseServiceImpl storeWareHouseService;
    @Autowired
    private IWarehouseChargeService iWarehouseChargeService;

    @Test
    public void listAllWarehouseForSupplychain() throws IOException {
        System.out.println(storeWareHouseService.getWareHouseByCityId(104).getCityId());
    }

    @Test
    public void add() {
        WareHouseChargeDTO wareHouseChargeDTO = new Gson().fromJson(
            "{\"city\":\"拉萨市\",\"cityId\":1,\"companyName\":\"\",\"county\":\"城关区\",\"createUser\":6811,\"custodianCharge\":0,\"landingCharge\":0,\"loadingCharge\":0,\"name\":\"222\",\"province\":\"西藏自治区\",\"sortingCharge\":0,\"status\":1,\"street\":\"1\",\"transportCharge\":0,\"unloadingCharge\":0,\"warehouseType\":0}",
            WareHouseChargeDTO.class);
        List<AllotRegulationDTO> allotList = new ArrayList<>();
        AllotRegulationDTO dto = new AllotRegulationDTO();
        dto.setAllotCityId(998);
        dto.setAllotWarehouseId(9981);
        dto.setFromCityId(999);
        dto.setOptUserId(1);
        allotList.add(dto);
        wareHouseChargeDTO.setAllotList(allotList);
        iWarehouseChargeService.addWarehouse(wareHouseChargeDTO);
    }

    @Test
    public void update() {
        WareHouseChargeDTO wareHouseChargeDTO = new Gson().fromJson(
            "{\"allotList\":[],\"id\":null,\"warehouseId\":999177,\"unloadingCharge\":1,\"sortingCharge\":1,\"custodianCharge\":1,\"loadingCharge\":1,\"transportCharge\":1,\"landingCharge\":1,\"status\":1,\"createUser\":6811,\"createTime\":null,\"lastUpdateUser\":null,\"lastUpdateTime\":null,\"name\":\"测试拉萨仓库\",\"remark\":null,\"province\":\"西藏自治区\",\"city\":\"拉萨市\",\"county\":\"城关区\",\"street\":\"八廓街道\",\"detailAddress\":\"测试一路\",\"warehouseType\":0,\"warehouseClass\":null,\"shopId\":null,\"cityId\":999,\"longitude\":null,\"latitude\":null,\"companyName\":\"西藏自治区拉萨市\",\"stockNumber\":5}",
            WareHouseChargeDTO.class);
        iWarehouseChargeService.updateWarehouse(wareHouseChargeDTO);
    }

    @Test
    public void select() throws JsonParseException, JsonMappingException, IOException {
        WareHouseChargeQueryDTO wareHouseChargeQueryDTO = new ObjectMapper().readValue(
            "{\"pageNum\":1,\"pageSize\":20,\"city\":\"\",\"cityId\":\"\",\"name\":\"拉萨仓库\",\"parentCityId\":1}",
            WareHouseChargeQueryDTO.class);
        PageList<WareHouseChargeDTO> result =
            iWarehouseChargeService.listWarehouseChargeConfigSelective(wareHouseChargeQueryDTO);
        System.err.println(new Gson().toJson(result));
    }

    @Test
    public void copy() {
        WarehouseCopyDTO dto = new WarehouseCopyDTO();
        dto.setName("测试仓库");
        dto.setCityId(998);
        dto.setCity("测试城市");
        dto.setProvince("西藏自治区");
        dto.setCounty("测试区域");
        dto.setStreet("测试街道");
        dto.setWarehouseId(9981);
        dto.setStatus(1);
        iWarehouseChargeService.copyWarehouse(dto);
    }
}
