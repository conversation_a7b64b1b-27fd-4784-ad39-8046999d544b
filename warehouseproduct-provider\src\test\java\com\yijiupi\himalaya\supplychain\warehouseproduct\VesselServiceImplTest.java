package com.yijiupi.himalaya.supplychain.warehouseproduct;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.VesselBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;

/**
 * <AUTHOR> 2017/11/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class VesselServiceImplTest {

    @Autowired
    private VesselBL vesselBL;

    // @Autowired
    // private VesselServiceImpl vesselServiceImpl;
    //
    @Test
    public void getLocation() {
        VesselInfoQueryDTO vesselInfoQueryDTO = new VesselInfoQueryDTO();
        vesselInfoQueryDTO.setWarehouseId(9981);
        vesselInfoQueryDTO.setCityId(998);
        vesselInfoQueryDTO.setCategory((byte)2);
        vesselInfoQueryDTO.setFreezeState((byte)0);
        // vesselInfoQueryDTO.setVesselLocationNo("WL-11-01-1-2");
        vesselInfoQueryDTO.setVesselLocationNo("WL-11");
        vesselInfoQueryDTO.setUseVague(false);
        PageList<VesselDTO> pageList = vesselBL.pageListVessel(vesselInfoQueryDTO);
        System.err.println("pageList 结果:" + JSON.toJSONString(pageList));
    }

    //
    // @Test
    // public void addVesselTest() {
    // VesselDTO dto = new VesselDTO();
    // dto.setUserId(111);
    // dto.setCityId(998);
    // dto.setWarehouseId(9981);
    // dto.setCategory(CategoryEnum.CARGO_VESSEL.getValue().byteValue());
    // dto.setName("WL-99-999-99");
    // vesselServiceImpl.addVessel(dto);
    // }
    //
    @Test
    public void addVesselByLocationIdTest() {
        VesselDTO dto = new VesselDTO();
        dto.setUserId(111);
        dto.setName("测试");
        dto.setCityId(998);
        dto.setWarehouseId(9981);
        dto.setLocationIds(Arrays.asList(169368317354167931L));
        vesselBL.addVesselByLocationIds(dto);
    }

    @Test
    public void listSKUBesselDetailsTest() {
        Map<Long, VesselDetailsDTO> map = vesselBL.listSKUBesselDetails(Arrays.asList(1395362419968784416L), 9981);
        System.out.println("map结果：" + JSON.toJSONString(map));
    }

    @Test
    public void vesselRelationLocationTest() {
        LoactionDTO updateDTO = new LoactionDTO();
        updateDTO.setId(5118833376567619550L);
        updateDTO.setArea_Id(5033374983836607571L);
        updateDTO.setWarehouseId(9981);
        vesselBL.vesselRelationLocation(updateDTO);
    }

    @Test
    public void updateVesselInfoFreezeStateTest() {
        VesselInfoModifyDTO updateDTO = new VesselInfoModifyDTO();
        updateDTO.setIds(Arrays.asList(5118833376596207319L, 5118827292263327447L));
        // updateDTO.setIsFreeze(IsFreezeEnum.冻结.getType());
        vesselBL.updateVesselInfoFreezeState(updateDTO);
    }

    @Test
    public void updateVesselInfoCurrentLocationTest() {
        VesselInfoModifyDTO updateDTO = new VesselInfoModifyDTO();
        updateDTO.setLastUpdateUser("测试");
        updateDTO.setWarehouseId(9981);
        updateDTO.setLocationId(5117733663877492687L);
        updateDTO.setCurrentLocationId(169368317370561594L);
        updateDTO.setCurrentLocationName("1-01");
        List<VesselInfoModifyDTO> updateList = new ArrayList<>();
        updateList.add(updateDTO);
        vesselBL.updateVesselInfoCurrentLocation(updateList);
    }

    @Test
    public void addStoreTransferOrderTest() {
        List<VesselMoveDTO> updateDTOList = new ArrayList<>();
        VesselMoveDTO updateDTO = new VesselMoveDTO();
        updateDTO.setCityId(998);
        updateDTO.setWarehouseId(9981);
        updateDTO.setVesselId(5117734486292426708L);
        updateDTO.setVesselName("WL-221-01-2-2");
        updateDTO.setFromLocationId(1395869718329507221L);
        updateDTO.setFromLocationName("JH2123-231");
        updateDTO.setToLocationId(5033374983836607574L);
        updateDTO.setToLocationName("221-01-2-2");
        updateDTO.setUserId(111);
        updateDTO.setUserName("测试");
        updateDTOList.add(updateDTO);
        vesselBL.addStoreTransferOrder(updateDTOList);
    }

    @Test
    public void vesselDeleteAndTransfer() {
        VesselDTO dto = new VesselDTO();
        dto.setId(5256653044038951764L);
        dto.setCityId(998);
        dto.setWarehouseId(9981);
        dto.setUserId(1);
        dto.setUserName("测试");
        vesselBL.vesselDeleteAndTransfer(dto);
    }
}
