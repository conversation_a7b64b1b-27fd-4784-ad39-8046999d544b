package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaListDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationAreaReturnDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * Created by 余明 on 2018-05-07.
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class LocationAreaBLTest {

    @Autowired
    private LocationAreaBL locationAreaBL;

    @Test
    public void getLocationArea() {
        LocationAreaDTO dto = new LocationAreaDTO();
        dto.setArea("Z03");
        dto.setCityId(999);
        dto.setWarehouseId(9991);
        dto.setPageSize(100);
        dto.setPageNum(1);
        PageList<LocationAreaReturnDTO> locationArea = locationAreaBL.getLocationArea(dto);
    }

    @Test
    public void getLocationAreaNoPage() {
        LocationAreaListDTO dto = new LocationAreaListDTO();
        dto.setCityId(999);
        dto.setWarehouseId(9991);
        dto.setPageSize(100);
        dto.setPageNum(1);
        List<LocationAreaReturnDTO> locationAreaNoPage = locationAreaBL.getLocationAreaNoPage(dto);
    }
}