package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2024-05-07 13:55
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class LocationBLTest {

    @Autowired
    private LocationBL locationBL;

    @Test
    public void pageListLocation0Test() {
        LocationInfoQueryDTO query = new LocationInfoQueryDTO();
        query.setWarehouseId(1641);
        // 情况 1: 不设置分页参数
        PageResult<LoactionDTO> result = locationBL.pageListLocation0(query);
        Assert.assertFalse(result.isEmpty());
        // 情况 2: 设置较小的分页参数
        query.setPageNum(1);
        query.setPageSize(10);
        PageResult<LoactionDTO> result2 = locationBL.pageListLocation0(query);
        Assert.assertSame(10, result2.size());
        // 情况 3: 设置较大分页参数
        query.setPageSize(Integer.MAX_VALUE);
        PageResult<LoactionDTO> result3 = locationBL.pageListLocation0(query);
        Assert.assertFalse(result3.isEmpty());
    }

}