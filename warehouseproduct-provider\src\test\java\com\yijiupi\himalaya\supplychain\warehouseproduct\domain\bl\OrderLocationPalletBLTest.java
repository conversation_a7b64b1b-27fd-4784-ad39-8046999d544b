package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import java.util.*;
import java.util.stream.Collectors;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.orderlocationpallet.OrderLocationPalletBL;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.orderlocationpallet.OrderLocationPalletQueryDTO;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/8/15
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class OrderLocationPalletBLTest {
    @Autowired
    private OrderLocationPalletBL orderLocationPalletBL;

    @Test
    public void addPalletTest() {
        String json =
            "{\"batchTaskId\":\"2024061200053\",\"createUser\":\"1\",\"lastUpdateUser\":\"1\",\"locationId\":5251839754183746246,\"locationName\":\"CKW1-1\",\"orderId\":5328304470060516036,\"orderNo\":\"998415200001\",\"orgId\":998,\"palletNoList\":[\"1\",\"2\"],\"warehouseId\":9981}";
        OrderLocationPalletDTO addDTO = JSON.parseObject(json, OrderLocationPalletDTO.class);
        orderLocationPalletBL.addPallet(addDTO);
    }

    @Test
    public void findPalletByConditionTest() {
        OrderLocationPalletQueryDTO queryDTO = new OrderLocationPalletQueryDTO();
        queryDTO.setWarehouseId(9981);
        queryDTO.setBatchTaskId("2024061200053");
        queryDTO.setBatchTaskIdList(Arrays.asList("2024061200053"));
        queryDTO.setOrderId(5328304470060516036L);
        queryDTO.setOrderIdList(Arrays.asList(5328304470060516036L));
        List<OrderLocationPalletDTO> palletDTOS = orderLocationPalletBL.findPalletByCondition(queryDTO);
        Map<Long, String> palletMap = new HashMap<>();
        palletDTOS.stream().filter(p -> p.getOrderId() != null && !StringUtils.isEmpty(p.getPalletNo()))
            .collect(Collectors.groupingBy(OrderLocationPalletDTO::getOrderId)).forEach((orderId, palletList) -> {
                String palletNoStr =
                    palletList.stream().sorted(Comparator.comparing(OrderLocationPalletDTO::getPalletNo))
                        .map(OrderLocationPalletDTO::getPalletNo).collect(Collectors.joining("#"));
                palletMap.put(orderId, palletNoStr);
            });
        System.out.println("palletMap结果：" + JSON.toJSONString(palletMap));
    }
}
