package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.PassageItemSO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = WarehouseProductApp.class)
public class PassageBLTest {

    @Autowired
    private PassageBL passageBL;

    @Test
    public void listPassageByRelate() {
        PassageItemSO so = new PassageItemSO();
        so.setWarehouseId(9981);
        List<PassageDTO> passages = passageBL.listPassageByRelate(so);
        Assertions.assertThat(passages).isNotEmpty();
    }

}