package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by 余明 on 2018-05-07.
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ProductCategoryBLTest {

    @Autowired
    private ProductCategoryBL productCategoryBL;

    @Test
    public void initProductCategory() {
        // productCategoryBL.initProductCategoryBySkuId();
    }

}