package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.location.ProductLocationItemQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ProductLocationBLTest
 * @description:
 * @date 2023-06-19 09:42
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ProductLocationBLTest {

    @Autowired
    private ProductLocationBL productLocationBL;

    @Test
    public void findLocationItemByConTest() {
        ProductLocationItemQueryDTO queryDTO = new ProductLocationItemQueryDTO();
        queryDTO.setWarehouseId(9981);
        queryDTO.setProductSkuIds(Arrays.asList(99800000003948L, 5178723286962764435L, 5178723286962764435L));
        queryDTO.setSubcategorys(Arrays.asList(LocationAreaEnum.存储区.getType(), LocationAreaEnum.拣货区.getType(),
            LocationAreaEnum.零拣区.getType()));
        List<ProductLoactionItemDTO> productLoactionItemDTOList = productLocationBL.findLocationItemByCon(queryDTO);

        Assertions.assertThat(productLoactionItemDTOList).isNotNull();
    }

    @Test
    public void saveProductLocationTest() {
        ProductLocationDTO dto = new ProductLocationDTO();
        dto.setCityId(998);
        dto.setWarehouseId(9981);
        dto.setProductSkuId(4863512194543254020L);
        dto.setLocationId(169368317354167939L);
        dto.setUserId(125);

        productLocationBL.saveProductLocation(dto);
    }

    @Test
    public void findLocationListByCondition() {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setWarehouseId(9981);
        List<LocationReturnDTO> locations = productLocationBL.findLocationListByCondition(dto);
        Assertions.assertThat(locations).isNotEmpty();
    }
}
