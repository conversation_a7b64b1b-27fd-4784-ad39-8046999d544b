package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.supplychain.warehouseproduct.AbstractBaseTest;
import com.yijiupi.himalaya.supplychain.warehouseproduct.domain.dao.ProductSkuConfigPOMapper;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductWarehouseAllocationTypeVerifyQuery;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


public class ProductSkuConfigStateChangeBLTest extends AbstractBaseTest {
	@Autowired
	private ProductSkuConfigPOMapper productSkuConfigPOMapper;
	@Autowired
	private ProductSkuConfigStateChangeBL productSkuConfigStateChangeBL;
	@Autowired
	private ProductSkuServiceBL productSkuServiceBL;

	@Test
	public void skuOffShelfAndStockZeroThreeMonthsAgoTest() {
		Integer warehouseId = 9981;
		Long maxId = productSkuConfigPOMapper.getSkuOffShelfAndStockZeroThreeMonthsAgoMaxId(warehouseId);
		Long minId = maxId;

		while (true) {
			List<Long> skuIds = productSkuConfigPOMapper.findSkuOffShelfAndStockZeroThreeMonthsAgo(warehouseId, minId);
			if (CollectionUtils.isEmpty(skuIds)) {
				break;
			}
			// 取 skuIds 最小的 id
			Optional<Long> newMinId = skuIds.stream().min(Long::compare);
			if (newMinId.isPresent()) {
				minId = newMinId.get();
			} else {
				break;
			}
		}
		System.out.println("ok");
	}

	@Test
	public void disableProductSkuConfigStateTest() {
		productSkuConfigStateChangeBL.disableProductSkuConfigState(9981);
		System.out.println("ok");
	}

	@Test
	public void productWarehouseAllocationTypeVerifyTest() {
		ProductWarehouseAllocationTypeVerifyQuery query = new ProductWarehouseAllocationTypeVerifyQuery();
		query.setWarehouseId(9981);
		query.setUserId(17630); //  67795309
		query.setProductInfoIds(Arrays.asList(560343L, 450122L));

		//query.setProductSkuIds(Arrays.asList(5473964960826812523L, 5472225647880891338L, 5472225646199728226L));

		productSkuServiceBL.productWarehouseAllocationTypeVerify(query);
		System.out.println("ok");
	}
}
