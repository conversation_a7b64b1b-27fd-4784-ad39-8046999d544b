package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDeliverySO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @title: ProductSkuQueryBLTest
 * @description:
 * @date 2023-04-26 14:28
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ProductSkuQueryBLTest {

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Test
    public void getProductSkuDeliveryMapTest() {
        ProductSkuDeliverySO deliverySO = JSON.parseObject("{\"cityId\":999,\"companyCode\":\"YJP\",\"deliveryCityId\":999,\"deliveryWarehouseId\":999219,\"skuList\":[{\"identityKey\":\"4608-30000\",\"ownerId\":30000,\"saleModel\":6,\"secOwnerId\":5180897486823007570,\"skuId\":10000004608805,\"specId\":4608}]}", ProductSkuDeliverySO.class);
        Map<Long, ProductSkuDTO> skuDTOMap = productSkuQueryBL.getProductSkuDeliveryMap(deliverySO);

        Assertions.assertThat(skuDTOMap).isNotNull();
    }

}
