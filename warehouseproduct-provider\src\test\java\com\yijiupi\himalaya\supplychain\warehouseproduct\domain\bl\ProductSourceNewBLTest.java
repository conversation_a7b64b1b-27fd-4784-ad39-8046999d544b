package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl;

import com.yijiupi.himalaya.WarehouseProductApp;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductControlConfigDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @Date 2021/8/10 11:15
 */
@SpringBootTest(classes = WarehouseProductApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ProductSourceNewBLTest {
    @Autowired
    private ProductSourceNewBL productSourceNewBL;

    @Test
    public void getControlConfigBySourceCodeTest() {
        List<Long> codes = new ArrayList<>();
        codes.add(4899293445181531209L);
        codes.add(4898567397353994639L);
        Map<Long, ProductControlConfigDTO> controlConfigBySourceCode =
            productSourceNewBL.getControlConfigBySourceCodeId(codes);

        System.out.println(controlConfigBySourceCode);
    }
}
