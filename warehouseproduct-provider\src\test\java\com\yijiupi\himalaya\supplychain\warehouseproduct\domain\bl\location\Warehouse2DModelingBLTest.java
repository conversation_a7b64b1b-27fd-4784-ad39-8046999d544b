package com.yijiupi.himalaya.supplychain.warehouseproduct.domain.bl.location;

import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.AddLocationAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2025/4/1 15:13
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class Warehouse2DModelingBLTest {

    @Autowired
    private Warehouse2DModelingBL warehouse2DModelingBL;

    @Test
    public void createWarehouseLocationArea() {
        AddLocationAreaDTO addLocationAreaDTO  = new AddLocationAreaDTO();
        addLocationAreaDTO.setWarehouseId(7111);
        addLocationAreaDTO.setAreaType(LocationAreaEnum.存储区.getType().byteValue());
        addLocationAreaDTO.setAreaName("AKG");
        addLocationAreaDTO.setShelveGroupNumber(1);
        addLocationAreaDTO.setLayerNumber(2);
        addLocationAreaDTO.setShelveCount(2);
        addLocationAreaDTO.setShelveLocationCount(2);
        warehouse2DModelingBL.createWarehouseLocationArea(addLocationAreaDTO);
    }
}